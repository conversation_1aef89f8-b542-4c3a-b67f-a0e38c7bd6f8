{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取佣金记录列表\nexport function getCommissionList(params) {\n  return request({\n    url: '/commission/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 获取佣金统计数据\nexport function getCommissionStats() {\n  return request({\n    url: '/commission/stats',\n    method: 'get'\n  });\n}\n\n// 导出赠送记录\nexport function exportCommissionList(params) {\n  return request({\n    url: '/commission/export',\n    method: 'get',\n    params: params,\n    responseType: 'blob' // 设置响应类型为blob\n  });\n}", "map": {"version": 3, "names": ["request", "getCommissionList", "params", "url", "method", "getCommissionStats", "exportCommissionList", "responseType"], "sources": ["E:/最新的代码/adminweb/src/api/reward/commission.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取佣金记录列表\r\nexport function getCommissionList(params) {\r\n  return request({\r\n    url: '/commission/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取佣金统计数据\r\nexport function getCommissionStats() {\r\n  return request({\r\n    url: '/commission/stats',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 导出赠送记录\r\nexport function exportCommissionList(params) {\r\n  return request({\r\n    url: '/commission/export',\r\n    method: 'get',\r\n    params,\r\n    responseType: 'blob'  // 设置响应类型为blob\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAE;EACxC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,kBAAkBA,CAAA,EAAG;EACnC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,oBAAoBA,CAACJ,MAAM,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA,MAAM;IACNK,YAAY,EAAE,MAAM,CAAE;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}