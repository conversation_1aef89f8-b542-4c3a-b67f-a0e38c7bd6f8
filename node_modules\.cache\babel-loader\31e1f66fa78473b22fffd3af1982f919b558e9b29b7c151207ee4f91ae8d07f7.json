{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as echarts from '../../../core/echarts.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { SINGLE_REFERRING } from '../../../util/model.js';\nvar INNER_STACK_KEYWORD = '__ec_magicType_stack__';\nvar ICON_TYPES = ['line', 'bar', 'stack'];\n// stack and tiled appears in pair for the title\nvar TITLE_TYPES = ['line', 'bar', 'stack', 'tiled'];\nvar radioTypes = [['line', 'bar'], ['stack']];\nvar MagicType = /** @class */function (_super) {\n  __extends(MagicType, _super);\n  function MagicType() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MagicType.prototype.getIcons = function () {\n    var model = this.model;\n    var availableIcons = model.get('icon');\n    var icons = {};\n    zrUtil.each(model.get('type'), function (type) {\n      if (availableIcons[type]) {\n        icons[type] = availableIcons[type];\n      }\n    });\n    return icons;\n  };\n  MagicType.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      type: [],\n      // Icon group\n      icon: {\n        line: 'M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4',\n        bar: 'M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7',\n        // eslint-disable-next-line\n        stack: 'M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z' // jshint ignore:line\n      },\n      // `line`, `bar`, `stack`, `tiled`\n      title: ecModel.getLocaleModel().get(['toolbox', 'magicType', 'title']),\n      option: {},\n      seriesIndex: {}\n    };\n    return defaultOption;\n  };\n  MagicType.prototype.onclick = function (ecModel, api, type) {\n    var model = this.model;\n    var seriesIndex = model.get(['seriesIndex', type]);\n    // Not supported magicType\n    if (!seriesOptGenreator[type]) {\n      return;\n    }\n    var newOption = {\n      series: []\n    };\n    var generateNewSeriesTypes = function generateNewSeriesTypes(seriesModel) {\n      var seriesType = seriesModel.subType;\n      var seriesId = seriesModel.id;\n      var newSeriesOpt = seriesOptGenreator[type](seriesType, seriesId, seriesModel, model);\n      if (newSeriesOpt) {\n        // PENDING If merge original option?\n        zrUtil.defaults(newSeriesOpt, seriesModel.option);\n        newOption.series.push(newSeriesOpt);\n      }\n      // Modify boundaryGap\n      var coordSys = seriesModel.coordinateSystem;\n      if (coordSys && coordSys.type === 'cartesian2d' && (type === 'line' || type === 'bar')) {\n        var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n        if (categoryAxis) {\n          var axisDim = categoryAxis.dim;\n          var axisType = axisDim + 'Axis';\n          var axisModel = seriesModel.getReferringComponents(axisType, SINGLE_REFERRING).models[0];\n          var axisIndex = axisModel.componentIndex;\n          newOption[axisType] = newOption[axisType] || [];\n          for (var i = 0; i <= axisIndex; i++) {\n            newOption[axisType][axisIndex] = newOption[axisType][axisIndex] || {};\n          }\n          newOption[axisType][axisIndex].boundaryGap = type === 'bar';\n        }\n      }\n    };\n    zrUtil.each(radioTypes, function (radio) {\n      if (zrUtil.indexOf(radio, type) >= 0) {\n        zrUtil.each(radio, function (item) {\n          model.setIconStatus(item, 'normal');\n        });\n      }\n    });\n    model.setIconStatus(type, 'emphasis');\n    ecModel.eachComponent({\n      mainType: 'series',\n      query: seriesIndex == null ? null : {\n        seriesIndex: seriesIndex\n      }\n    }, generateNewSeriesTypes);\n    var newTitle;\n    var currentType = type;\n    // Change title of stack\n    if (type === 'stack') {\n      // use titles in model instead of ecModel\n      // as stack and tiled appears in pair, just flip them\n      // no need of checking stack state\n      newTitle = zrUtil.merge({\n        stack: model.option.title.tiled,\n        tiled: model.option.title.stack\n      }, model.option.title);\n      if (model.get(['iconStatus', type]) !== 'emphasis') {\n        currentType = 'tiled';\n      }\n    }\n    api.dispatchAction({\n      type: 'changeMagicType',\n      currentType: currentType,\n      newOption: newOption,\n      newTitle: newTitle,\n      featureName: 'magicType'\n    });\n  };\n  return MagicType;\n}(ToolboxFeature);\nvar seriesOptGenreator = {\n  'line': function line(seriesType, seriesId, seriesModel, model) {\n    if (seriesType === 'bar') {\n      return zrUtil.merge({\n        id: seriesId,\n        type: 'line',\n        // Preserve data related option\n        data: seriesModel.get('data'),\n        stack: seriesModel.get('stack'),\n        markPoint: seriesModel.get('markPoint'),\n        markLine: seriesModel.get('markLine')\n      }, model.get(['option', 'line']) || {}, true);\n    }\n  },\n  'bar': function bar(seriesType, seriesId, seriesModel, model) {\n    if (seriesType === 'line') {\n      return zrUtil.merge({\n        id: seriesId,\n        type: 'bar',\n        // Preserve data related option\n        data: seriesModel.get('data'),\n        stack: seriesModel.get('stack'),\n        markPoint: seriesModel.get('markPoint'),\n        markLine: seriesModel.get('markLine')\n      }, model.get(['option', 'bar']) || {}, true);\n    }\n  },\n  'stack': function stack(seriesType, seriesId, seriesModel, model) {\n    var isStack = seriesModel.get('stack') === INNER_STACK_KEYWORD;\n    if (seriesType === 'line' || seriesType === 'bar') {\n      model.setIconStatus('stack', isStack ? 'normal' : 'emphasis');\n      return zrUtil.merge({\n        id: seriesId,\n        stack: isStack ? '' : INNER_STACK_KEYWORD\n      }, model.get(['option', 'stack']) || {}, true);\n    }\n  }\n};\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'changeMagicType',\n  event: 'magicTypeChanged',\n  update: 'prepareAndUpdate'\n}, function (payload, ecModel) {\n  ecModel.mergeOption(payload.newOption);\n});\nexport default MagicType;", "map": {"version": 3, "names": ["__extends", "echarts", "zrUtil", "ToolboxFeature", "SINGLE_REFERRING", "INNER_STACK_KEYWORD", "ICON_TYPES", "TITLE_TYPES", "radioTypes", "MagicType", "_super", "apply", "arguments", "prototype", "getIcons", "model", "availableIcons", "get", "icons", "each", "type", "getDefaultOption", "ecModel", "defaultOption", "show", "icon", "line", "bar", "stack", "title", "getLocaleModel", "option", "seriesIndex", "onclick", "api", "seriesOptGenreator", "newOption", "series", "generateNewSeriesTypes", "seriesModel", "seriesType", "subType", "seriesId", "id", "newSeriesOpt", "defaults", "push", "coordSys", "coordinateSystem", "categoryAxis", "getAxesByScale", "axisDim", "dim", "axisType", "axisModel", "getReferringComponents", "models", "axisIndex", "componentIndex", "i", "boundaryGap", "radio", "indexOf", "item", "setIconStatus", "eachComponent", "mainType", "query", "newTitle", "currentType", "merge", "tiled", "dispatchAction", "featureName", "data", "markPoint", "markLine", "isStack", "registerAction", "event", "update", "payload", "mergeOption"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/echarts/lib/component/toolbox/feature/MagicType.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as echarts from '../../../core/echarts.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { SINGLE_REFERRING } from '../../../util/model.js';\nvar INNER_STACK_KEYWORD = '__ec_magicType_stack__';\nvar ICON_TYPES = ['line', 'bar', 'stack'];\n// stack and tiled appears in pair for the title\nvar TITLE_TYPES = ['line', 'bar', 'stack', 'tiled'];\nvar radioTypes = [['line', 'bar'], ['stack']];\nvar MagicType = /** @class */function (_super) {\n  __extends(MagicType, _super);\n  function MagicType() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MagicType.prototype.getIcons = function () {\n    var model = this.model;\n    var availableIcons = model.get('icon');\n    var icons = {};\n    zrUtil.each(model.get('type'), function (type) {\n      if (availableIcons[type]) {\n        icons[type] = availableIcons[type];\n      }\n    });\n    return icons;\n  };\n  MagicType.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      type: [],\n      // Icon group\n      icon: {\n        line: 'M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4',\n        bar: 'M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7',\n        // eslint-disable-next-line\n        stack: 'M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z' // jshint ignore:line\n      },\n\n      // `line`, `bar`, `stack`, `tiled`\n      title: ecModel.getLocaleModel().get(['toolbox', 'magicType', 'title']),\n      option: {},\n      seriesIndex: {}\n    };\n    return defaultOption;\n  };\n  MagicType.prototype.onclick = function (ecModel, api, type) {\n    var model = this.model;\n    var seriesIndex = model.get(['seriesIndex', type]);\n    // Not supported magicType\n    if (!seriesOptGenreator[type]) {\n      return;\n    }\n    var newOption = {\n      series: []\n    };\n    var generateNewSeriesTypes = function (seriesModel) {\n      var seriesType = seriesModel.subType;\n      var seriesId = seriesModel.id;\n      var newSeriesOpt = seriesOptGenreator[type](seriesType, seriesId, seriesModel, model);\n      if (newSeriesOpt) {\n        // PENDING If merge original option?\n        zrUtil.defaults(newSeriesOpt, seriesModel.option);\n        newOption.series.push(newSeriesOpt);\n      }\n      // Modify boundaryGap\n      var coordSys = seriesModel.coordinateSystem;\n      if (coordSys && coordSys.type === 'cartesian2d' && (type === 'line' || type === 'bar')) {\n        var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n        if (categoryAxis) {\n          var axisDim = categoryAxis.dim;\n          var axisType = axisDim + 'Axis';\n          var axisModel = seriesModel.getReferringComponents(axisType, SINGLE_REFERRING).models[0];\n          var axisIndex = axisModel.componentIndex;\n          newOption[axisType] = newOption[axisType] || [];\n          for (var i = 0; i <= axisIndex; i++) {\n            newOption[axisType][axisIndex] = newOption[axisType][axisIndex] || {};\n          }\n          newOption[axisType][axisIndex].boundaryGap = type === 'bar';\n        }\n      }\n    };\n    zrUtil.each(radioTypes, function (radio) {\n      if (zrUtil.indexOf(radio, type) >= 0) {\n        zrUtil.each(radio, function (item) {\n          model.setIconStatus(item, 'normal');\n        });\n      }\n    });\n    model.setIconStatus(type, 'emphasis');\n    ecModel.eachComponent({\n      mainType: 'series',\n      query: seriesIndex == null ? null : {\n        seriesIndex: seriesIndex\n      }\n    }, generateNewSeriesTypes);\n    var newTitle;\n    var currentType = type;\n    // Change title of stack\n    if (type === 'stack') {\n      // use titles in model instead of ecModel\n      // as stack and tiled appears in pair, just flip them\n      // no need of checking stack state\n      newTitle = zrUtil.merge({\n        stack: model.option.title.tiled,\n        tiled: model.option.title.stack\n      }, model.option.title);\n      if (model.get(['iconStatus', type]) !== 'emphasis') {\n        currentType = 'tiled';\n      }\n    }\n    api.dispatchAction({\n      type: 'changeMagicType',\n      currentType: currentType,\n      newOption: newOption,\n      newTitle: newTitle,\n      featureName: 'magicType'\n    });\n  };\n  return MagicType;\n}(ToolboxFeature);\nvar seriesOptGenreator = {\n  'line': function (seriesType, seriesId, seriesModel, model) {\n    if (seriesType === 'bar') {\n      return zrUtil.merge({\n        id: seriesId,\n        type: 'line',\n        // Preserve data related option\n        data: seriesModel.get('data'),\n        stack: seriesModel.get('stack'),\n        markPoint: seriesModel.get('markPoint'),\n        markLine: seriesModel.get('markLine')\n      }, model.get(['option', 'line']) || {}, true);\n    }\n  },\n  'bar': function (seriesType, seriesId, seriesModel, model) {\n    if (seriesType === 'line') {\n      return zrUtil.merge({\n        id: seriesId,\n        type: 'bar',\n        // Preserve data related option\n        data: seriesModel.get('data'),\n        stack: seriesModel.get('stack'),\n        markPoint: seriesModel.get('markPoint'),\n        markLine: seriesModel.get('markLine')\n      }, model.get(['option', 'bar']) || {}, true);\n    }\n  },\n  'stack': function (seriesType, seriesId, seriesModel, model) {\n    var isStack = seriesModel.get('stack') === INNER_STACK_KEYWORD;\n    if (seriesType === 'line' || seriesType === 'bar') {\n      model.setIconStatus('stack', isStack ? 'normal' : 'emphasis');\n      return zrUtil.merge({\n        id: seriesId,\n        stack: isStack ? '' : INNER_STACK_KEYWORD\n      }, model.get(['option', 'stack']) || {}, true);\n    }\n  }\n};\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'changeMagicType',\n  event: 'magicTypeChanged',\n  update: 'prepareAndUpdate'\n}, function (payload, ecModel) {\n  ecModel.mergeOption(payload.newOption);\n});\nexport default MagicType;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,OAAO,MAAM,0BAA0B;AACnD,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,IAAIC,mBAAmB,GAAG,wBAAwB;AAClD,IAAIC,UAAU,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;AACzC;AACA,IAAIC,WAAW,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;AACnD,IAAIC,UAAU,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AAC7C,IAAIC,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7CV,SAAS,CAACS,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAAA,EAAG;IACnB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,SAAS,CAACI,SAAS,CAACC,QAAQ,GAAG,YAAY;IACzC,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,cAAc,GAAGD,KAAK,CAACE,GAAG,CAAC,MAAM,CAAC;IACtC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACdhB,MAAM,CAACiB,IAAI,CAACJ,KAAK,CAACE,GAAG,CAAC,MAAM,CAAC,EAAE,UAAUG,IAAI,EAAE;MAC7C,IAAIJ,cAAc,CAACI,IAAI,CAAC,EAAE;QACxBF,KAAK,CAACE,IAAI,CAAC,GAAGJ,cAAc,CAACI,IAAI,CAAC;MACpC;IACF,CAAC,CAAC;IACF,OAAOF,KAAK;EACd,CAAC;EACDT,SAAS,CAACY,gBAAgB,GAAG,UAAUC,OAAO,EAAE;IAC9C,IAAIC,aAAa,GAAG;MAClBC,IAAI,EAAE,IAAI;MACVJ,IAAI,EAAE,EAAE;MACR;MACAK,IAAI,EAAE;QACJC,IAAI,EAAE,8DAA8D;QACpEC,GAAG,EAAE,iFAAiF;QACtF;QACAC,KAAK,EAAE,kYAAkY,CAAC;MAC5Y,CAAC;MAED;MACAC,KAAK,EAAEP,OAAO,CAACQ,cAAc,CAAC,CAAC,CAACb,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;MACtEc,MAAM,EAAE,CAAC,CAAC;MACVC,WAAW,EAAE,CAAC;IAChB,CAAC;IACD,OAAOT,aAAa;EACtB,CAAC;EACDd,SAAS,CAACI,SAAS,CAACoB,OAAO,GAAG,UAAUX,OAAO,EAAEY,GAAG,EAAEd,IAAI,EAAE;IAC1D,IAAIL,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIiB,WAAW,GAAGjB,KAAK,CAACE,GAAG,CAAC,CAAC,aAAa,EAAEG,IAAI,CAAC,CAAC;IAClD;IACA,IAAI,CAACe,kBAAkB,CAACf,IAAI,CAAC,EAAE;MAC7B;IACF;IACA,IAAIgB,SAAS,GAAG;MACdC,MAAM,EAAE;IACV,CAAC;IACD,IAAIC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAaC,WAAW,EAAE;MAClD,IAAIC,UAAU,GAAGD,WAAW,CAACE,OAAO;MACpC,IAAIC,QAAQ,GAAGH,WAAW,CAACI,EAAE;MAC7B,IAAIC,YAAY,GAAGT,kBAAkB,CAACf,IAAI,CAAC,CAACoB,UAAU,EAAEE,QAAQ,EAAEH,WAAW,EAAExB,KAAK,CAAC;MACrF,IAAI6B,YAAY,EAAE;QAChB;QACA1C,MAAM,CAAC2C,QAAQ,CAACD,YAAY,EAAEL,WAAW,CAACR,MAAM,CAAC;QACjDK,SAAS,CAACC,MAAM,CAACS,IAAI,CAACF,YAAY,CAAC;MACrC;MACA;MACA,IAAIG,QAAQ,GAAGR,WAAW,CAACS,gBAAgB;MAC3C,IAAID,QAAQ,IAAIA,QAAQ,CAAC3B,IAAI,KAAK,aAAa,KAAKA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;QACtF,IAAI6B,YAAY,GAAGF,QAAQ,CAACG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACxD,IAAID,YAAY,EAAE;UAChB,IAAIE,OAAO,GAAGF,YAAY,CAACG,GAAG;UAC9B,IAAIC,QAAQ,GAAGF,OAAO,GAAG,MAAM;UAC/B,IAAIG,SAAS,GAAGf,WAAW,CAACgB,sBAAsB,CAACF,QAAQ,EAAEjD,gBAAgB,CAAC,CAACoD,MAAM,CAAC,CAAC,CAAC;UACxF,IAAIC,SAAS,GAAGH,SAAS,CAACI,cAAc;UACxCtB,SAAS,CAACiB,QAAQ,CAAC,GAAGjB,SAAS,CAACiB,QAAQ,CAAC,IAAI,EAAE;UAC/C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;YACnCvB,SAAS,CAACiB,QAAQ,CAAC,CAACI,SAAS,CAAC,GAAGrB,SAAS,CAACiB,QAAQ,CAAC,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;UACvE;UACArB,SAAS,CAACiB,QAAQ,CAAC,CAACI,SAAS,CAAC,CAACG,WAAW,GAAGxC,IAAI,KAAK,KAAK;QAC7D;MACF;IACF,CAAC;IACDlB,MAAM,CAACiB,IAAI,CAACX,UAAU,EAAE,UAAUqD,KAAK,EAAE;MACvC,IAAI3D,MAAM,CAAC4D,OAAO,CAACD,KAAK,EAAEzC,IAAI,CAAC,IAAI,CAAC,EAAE;QACpClB,MAAM,CAACiB,IAAI,CAAC0C,KAAK,EAAE,UAAUE,IAAI,EAAE;UACjChD,KAAK,CAACiD,aAAa,CAACD,IAAI,EAAE,QAAQ,CAAC;QACrC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFhD,KAAK,CAACiD,aAAa,CAAC5C,IAAI,EAAE,UAAU,CAAC;IACrCE,OAAO,CAAC2C,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAEnC,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG;QAClCA,WAAW,EAAEA;MACf;IACF,CAAC,EAAEM,sBAAsB,CAAC;IAC1B,IAAI8B,QAAQ;IACZ,IAAIC,WAAW,GAAGjD,IAAI;IACtB;IACA,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB;MACA;MACA;MACAgD,QAAQ,GAAGlE,MAAM,CAACoE,KAAK,CAAC;QACtB1C,KAAK,EAAEb,KAAK,CAACgB,MAAM,CAACF,KAAK,CAAC0C,KAAK;QAC/BA,KAAK,EAAExD,KAAK,CAACgB,MAAM,CAACF,KAAK,CAACD;MAC5B,CAAC,EAAEb,KAAK,CAACgB,MAAM,CAACF,KAAK,CAAC;MACtB,IAAId,KAAK,CAACE,GAAG,CAAC,CAAC,YAAY,EAAEG,IAAI,CAAC,CAAC,KAAK,UAAU,EAAE;QAClDiD,WAAW,GAAG,OAAO;MACvB;IACF;IACAnC,GAAG,CAACsC,cAAc,CAAC;MACjBpD,IAAI,EAAE,iBAAiB;MACvBiD,WAAW,EAAEA,WAAW;MACxBjC,SAAS,EAAEA,SAAS;MACpBgC,QAAQ,EAAEA,QAAQ;MAClBK,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EACD,OAAOhE,SAAS;AAClB,CAAC,CAACN,cAAc,CAAC;AACjB,IAAIgC,kBAAkB,GAAG;EACvB,MAAM,EAAE,SAART,IAAMA,CAAYc,UAAU,EAAEE,QAAQ,EAAEH,WAAW,EAAExB,KAAK,EAAE;IAC1D,IAAIyB,UAAU,KAAK,KAAK,EAAE;MACxB,OAAOtC,MAAM,CAACoE,KAAK,CAAC;QAClB3B,EAAE,EAAED,QAAQ;QACZtB,IAAI,EAAE,MAAM;QACZ;QACAsD,IAAI,EAAEnC,WAAW,CAACtB,GAAG,CAAC,MAAM,CAAC;QAC7BW,KAAK,EAAEW,WAAW,CAACtB,GAAG,CAAC,OAAO,CAAC;QAC/B0D,SAAS,EAAEpC,WAAW,CAACtB,GAAG,CAAC,WAAW,CAAC;QACvC2D,QAAQ,EAAErC,WAAW,CAACtB,GAAG,CAAC,UAAU;MACtC,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;IAC/C;EACF,CAAC;EACD,KAAK,EAAE,SAAPU,GAAKA,CAAYa,UAAU,EAAEE,QAAQ,EAAEH,WAAW,EAAExB,KAAK,EAAE;IACzD,IAAIyB,UAAU,KAAK,MAAM,EAAE;MACzB,OAAOtC,MAAM,CAACoE,KAAK,CAAC;QAClB3B,EAAE,EAAED,QAAQ;QACZtB,IAAI,EAAE,KAAK;QACX;QACAsD,IAAI,EAAEnC,WAAW,CAACtB,GAAG,CAAC,MAAM,CAAC;QAC7BW,KAAK,EAAEW,WAAW,CAACtB,GAAG,CAAC,OAAO,CAAC;QAC/B0D,SAAS,EAAEpC,WAAW,CAACtB,GAAG,CAAC,WAAW,CAAC;QACvC2D,QAAQ,EAAErC,WAAW,CAACtB,GAAG,CAAC,UAAU;MACtC,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;IAC9C;EACF,CAAC;EACD,OAAO,EAAE,SAATW,KAAOA,CAAYY,UAAU,EAAEE,QAAQ,EAAEH,WAAW,EAAExB,KAAK,EAAE;IAC3D,IAAI8D,OAAO,GAAGtC,WAAW,CAACtB,GAAG,CAAC,OAAO,CAAC,KAAKZ,mBAAmB;IAC9D,IAAImC,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,KAAK,EAAE;MACjDzB,KAAK,CAACiD,aAAa,CAAC,OAAO,EAAEa,OAAO,GAAG,QAAQ,GAAG,UAAU,CAAC;MAC7D,OAAO3E,MAAM,CAACoE,KAAK,CAAC;QAClB3B,EAAE,EAAED,QAAQ;QACZd,KAAK,EAAEiD,OAAO,GAAG,EAAE,GAAGxE;MACxB,CAAC,EAAEU,KAAK,CAACE,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;IAChD;EACF;AACF,CAAC;AACD;AACAhB,OAAO,CAAC6E,cAAc,CAAC;EACrB1D,IAAI,EAAE,iBAAiB;EACvB2D,KAAK,EAAE,kBAAkB;EACzBC,MAAM,EAAE;AACV,CAAC,EAAE,UAAUC,OAAO,EAAE3D,OAAO,EAAE;EAC7BA,OAAO,CAAC4D,WAAW,CAACD,OAAO,CAAC7C,SAAS,CAAC;AACxC,CAAC,CAAC;AACF,eAAe3B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}