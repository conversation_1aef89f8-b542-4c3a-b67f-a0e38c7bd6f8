{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { layout, createProgressiveLayout } from '../../layout/barGrid.js';\nimport dataSample from '../../processor/dataSample.js';\nimport BarSeries from './BarSeries.js';\nimport BarView from './BarView.js';\nexport function install(registers) {\n  registers.registerChartView(BarView);\n  registers.registerSeriesModel(BarSeries);\n  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, zrUtil.curry(layout, 'bar'));\n  // Do layout after other overall layout, which can prepare some information.\n  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, createProgressiveLayout('bar'));\n  // Down sample after filter\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, dataSample('bar'));\n  /**\n   * @payload\n   * @property {string} [componentType=series]\n   * @property {number} [dx]\n   * @property {number} [dy]\n   * @property {number} [zoom]\n   * @property {number} [originX]\n   * @property {number} [originY]\n   */\n  registers.registerAction({\n    type: 'changeAxisOrder',\n    event: 'changeAxisOrder',\n    update: 'update'\n  }, function (payload, ecModel) {\n    var componentType = payload.componentType || 'series';\n    ecModel.eachComponent({\n      mainType: componentType,\n      query: payload\n    }, function (componentModel) {\n      if (payload.sortInfo) {\n        componentModel.axis.setCategorySortInfo(payload.sortInfo);\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "layout", "createProgressiveLayout", "dataSample", "BarSeries", "BarView", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "PRIORITY", "VISUAL", "LAYOUT", "curry", "PROGRESSIVE_LAYOUT", "registerProcessor", "PROCESSOR", "STATISTIC", "registerAction", "type", "event", "update", "payload", "ecModel", "componentType", "eachComponent", "mainType", "query", "componentModel", "sortInfo", "axis", "setCategorySortInfo"], "sources": ["G:/备份9/adminweb/node_modules/echarts/lib/chart/bar/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { layout, createProgressiveLayout } from '../../layout/barGrid.js';\nimport dataSample from '../../processor/dataSample.js';\nimport BarSeries from './BarSeries.js';\nimport BarView from './BarView.js';\nexport function install(registers) {\n  registers.registerChartView(BarView);\n  registers.registerSeriesModel(BarSeries);\n  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, zrUtil.curry(layout, 'bar'));\n  // Do layout after other overall layout, which can prepare some information.\n  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, createProgressiveLayout('bar'));\n  // Down sample after filter\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, dataSample('bar'));\n  /**\n   * @payload\n   * @property {string} [componentType=series]\n   * @property {number} [dx]\n   * @property {number} [dy]\n   * @property {number} [zoom]\n   * @property {number} [originX]\n   * @property {number} [originY]\n   */\n  registers.registerAction({\n    type: 'changeAxisOrder',\n    event: 'changeAxisOrder',\n    update: 'update'\n  }, function (payload, ecModel) {\n    var componentType = payload.componentType || 'series';\n    ecModel.eachComponent({\n      mainType: componentType,\n      query: payload\n    }, function (componentModel) {\n      if (payload.sortInfo) {\n        componentModel.axis.setCategorySortInfo(payload.sortInfo);\n      }\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,MAAM,EAAEC,uBAAuB,QAAQ,yBAAyB;AACzE,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACH,OAAO,CAAC;EACpCE,SAAS,CAACE,mBAAmB,CAACL,SAAS,CAAC;EACxCG,SAAS,CAACG,cAAc,CAACH,SAAS,CAACI,QAAQ,CAACC,MAAM,CAACC,MAAM,EAAEb,MAAM,CAACc,KAAK,CAACb,MAAM,EAAE,KAAK,CAAC,CAAC;EACvF;EACAM,SAAS,CAACG,cAAc,CAACH,SAAS,CAACI,QAAQ,CAACC,MAAM,CAACG,kBAAkB,EAAEb,uBAAuB,CAAC,KAAK,CAAC,CAAC;EACtG;EACAK,SAAS,CAACS,iBAAiB,CAACT,SAAS,CAACI,QAAQ,CAACM,SAAS,CAACC,SAAS,EAAEf,UAAU,CAAC,KAAK,CAAC,CAAC;EACtF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEI,SAAS,CAACY,cAAc,CAAC;IACvBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,iBAAiB;IACxBC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC7B,IAAIC,aAAa,GAAGF,OAAO,CAACE,aAAa,IAAI,QAAQ;IACrDD,OAAO,CAACE,aAAa,CAAC;MACpBC,QAAQ,EAAEF,aAAa;MACvBG,KAAK,EAAEL;IACT,CAAC,EAAE,UAAUM,cAAc,EAAE;MAC3B,IAAIN,OAAO,CAACO,QAAQ,EAAE;QACpBD,cAAc,CAACE,IAAI,CAACC,mBAAmB,CAACT,OAAO,CAACO,QAAQ,CAAC;MAC3D;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}