{"version": 3, "file": "get-outer-width.js", "sourceRoot": "", "sources": ["../src/get-outer-width.ts"], "names": [], "mappings": ";;AACA,yCAAmC;AACnC,yCAAmC;AAEnC,SAAwB,aAAa,CAAC,EAAe,EAAE,YAAkB;IACvE,IAAM,KAAK,GAAG,IAAA,mBAAQ,EAAC,EAAE,EAAE,YAAY,CAAC,CAAC;IACzC,IAAM,KAAK,GAAG,UAAU,CAAC,IAAA,mBAAQ,EAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAM,KAAK,GAAG,UAAU,CAAC,IAAA,mBAAQ,EAAC,EAAE,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3D,IAAM,MAAM,GAAG,UAAU,CAAC,IAAA,mBAAQ,EAAC,EAAE,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7D,IAAM,MAAM,GAAG,UAAU,CAAC,IAAA,mBAAQ,EAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC;IACjE,IAAM,MAAM,GAAG,UAAU,CAAC,IAAA,mBAAQ,EAAC,EAAE,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5D,IAAM,KAAK,GAAG,UAAU,CAAC,IAAA,mBAAQ,EAAC,EAAE,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;IAC1D,OAAO,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;AAClE,CAAC;AATD,gCASC"}