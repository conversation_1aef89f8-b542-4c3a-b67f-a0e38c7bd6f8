import type { Disposable, ResourceBase, ResourceType } from '../api';
import EventEmitter from 'eventemitter3';
import type { IDevice_WebGPU } from './interfaces';
export declare abstract class ResourceBase_WebGPU extends EventEmitter implements ResourceBase, Disposable {
    type: ResourceType;
    id: number;
    name: string;
    device: IDevice_WebGPU;
    constructor({ id, device }: {
        id: number;
        device: IDevice_WebGPU;
    });
    destroy(): void;
}
