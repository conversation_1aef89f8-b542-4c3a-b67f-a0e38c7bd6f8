import { ResourceType } from '../api';
import type { Sam<PERSON>, SamplerDescriptor } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
/**
 * In WebGL 1 texture image data and sampling information are both stored in texture objects
 * @see https://github.com/shrekshao/MoveWebGL1EngineToWebGL2/blob/master/Move-a-WebGL-1-Engine-To-WebGL-2-Blog-2.md#sampler-objects
 */
export declare class Sampler_GL extends ResourceBase_GL implements Sampler {
    type: ResourceType.Sampler;
    gl_sampler: WebGLSampler;
    descriptor: SamplerDescriptor;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: SamplerDescriptor;
    });
    setTextureParameters(gl_target: number, width: number, height: number): void;
    destroy(): void;
    isNPOT(width: number, height: number): boolean;
}
