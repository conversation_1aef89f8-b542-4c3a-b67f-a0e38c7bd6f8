{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"app-container\"\n  }, [_c(\"el-card\", {\n    staticClass: \"box-card\"\n  }, [_c(\"div\", {\n    staticClass: \"header\"\n  }, [_c(\"h3\", [_vm._v(\"首页通知设置\")]), _c(\"div\", {\n    staticClass: \"header-right\"\n  }, [_c(\"el-switch\", {\n    attrs: {\n      \"active-value\": 1,\n      \"inactive-value\": 0,\n      \"active-text\": \"开启\",\n      \"inactive-text\": \"关闭\"\n    },\n    on: {\n      change: _vm.handleStatusChange\n    },\n    model: {\n      value: _vm.notice.status,\n      callback: function callback($$v) {\n        _vm.$set(_vm.notice, \"status\", $$v);\n      },\n      expression: \"notice.status\"\n    }\n  }), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      loading: _vm.loading\n    },\n    on: {\n      click: _vm.handleSave\n    }\n  }, [_vm._v(\"保存修改\")])], 1)]), _c(\"div\", {\n    staticClass: \"content-container\"\n  }, [_c(\"el-input\", {\n    attrs: {\n      type: \"textarea\",\n      rows: 15,\n      placeholder: \"请输入首页通知内容\",\n      resize: \"none\"\n    },\n    model: {\n      value: _vm.notice.content,\n      callback: function callback($$v) {\n        _vm.$set(_vm.notice, \"content\", $$v);\n      },\n      expression: \"notice.content\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"tips\"\n  }, [_c(\"p\", [_vm._v(\"提示：\")]), _c(\"p\", [_vm._v(\"1. 支持换行\")]), _c(\"p\", [_vm._v(\"2. 建议控制文本长度，以确保良好的显示效果\")])])], 1)])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "on", "change", "handleStatusChange", "model", "value", "notice", "status", "callback", "$$v", "$set", "expression", "type", "loading", "click", "handleSave", "rows", "placeholder", "resize", "content", "staticRenderFns", "_withStripped"], "sources": ["F:/常规项目/华通云/adminweb/src/views/notice/home-notice/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"app-container\" },\n    [\n      _c(\"el-card\", { staticClass: \"box-card\" }, [\n        _c(\"div\", { staticClass: \"header\" }, [\n          _c(\"h3\", [_vm._v(\"首页通知设置\")]),\n          _c(\n            \"div\",\n            { staticClass: \"header-right\" },\n            [\n              _c(\"el-switch\", {\n                attrs: {\n                  \"active-value\": 1,\n                  \"inactive-value\": 0,\n                  \"active-text\": \"开启\",\n                  \"inactive-text\": \"关闭\",\n                },\n                on: { change: _vm.handleStatusChange },\n                model: {\n                  value: _vm.notice.status,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.notice, \"status\", $$v)\n                  },\n                  expression: \"notice.status\",\n                },\n              }),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\", loading: _vm.loading },\n                  on: { click: _vm.handleSave },\n                },\n                [_vm._v(\"保存修改\")]\n              ),\n            ],\n            1\n          ),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"content-container\" },\n          [\n            _c(\"el-input\", {\n              attrs: {\n                type: \"textarea\",\n                rows: 15,\n                placeholder: \"请输入首页通知内容\",\n                resize: \"none\",\n              },\n              model: {\n                value: _vm.notice.content,\n                callback: function ($$v) {\n                  _vm.$set(_vm.notice, \"content\", $$v)\n                },\n                expression: \"notice.content\",\n              },\n            }),\n            _c(\"div\", { staticClass: \"tips\" }, [\n              _c(\"p\", [_vm._v(\"提示：\")]),\n              _c(\"p\", [_vm._v(\"1. 支持换行\")]),\n              _c(\"p\", [_vm._v(\"2. 建议控制文本长度，以确保良好的显示效果\")]),\n            ]),\n          ],\n          1\n        ),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MACL,cAAc,EAAE,CAAC;MACjB,gBAAgB,EAAE,CAAC;MACnB,aAAa,EAAE,IAAI;MACnB,eAAe,EAAE;IACnB,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAEP,GAAG,CAACQ;IAAmB,CAAC;IACtCC,KAAK,EAAE;MACLC,KAAK,EAAEV,GAAG,CAACW,MAAM,CAACC,MAAM;MACxBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBd,GAAG,CAACe,IAAI,CAACf,GAAG,CAACW,MAAM,EAAE,QAAQ,EAAEG,GAAG,CAAC;MACrC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFf,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEY,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAElB,GAAG,CAACkB;IAAQ,CAAC;IAChDZ,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAACoB;IAAW;EAC9B,CAAC,EACD,CAACpB,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACpC,CACEF,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLY,IAAI,EAAE,UAAU;MAChBI,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,WAAW;MACxBC,MAAM,EAAE;IACV,CAAC;IACDd,KAAK,EAAE;MACLC,KAAK,EAAEV,GAAG,CAACW,MAAM,CAACa,OAAO;MACzBX,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBd,GAAG,CAACe,IAAI,CAACf,GAAG,CAACW,MAAM,EAAE,SAAS,EAAEG,GAAG,CAAC;MACtC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFf,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EACxBH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC5BH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAC5C,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIqB,eAAe,GAAG,EAAE;AACxB1B,MAAM,CAAC2B,aAAa,GAAG,IAAI;AAE3B,SAAS3B,MAAM,EAAE0B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}