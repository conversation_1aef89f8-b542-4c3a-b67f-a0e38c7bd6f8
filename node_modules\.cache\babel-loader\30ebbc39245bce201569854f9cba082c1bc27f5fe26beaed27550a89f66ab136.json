{"ast": null, "code": "import \"core-js/modules/es.reflect.construct.js\";\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "map": {"version": 3, "names": ["_isNativeReflectConstruct", "t", "Boolean", "prototype", "valueOf", "call", "Reflect", "construct", "default"], "sources": ["G:/备份9/adminweb/node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };"], "mappings": ";AAAA,SAASA,yBAAyBA,CAAA,EAAG;EACnC,IAAI;IACF,IAAIC,CAAC,GAAG,CAACC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,CAACL,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,OAAOD,CAAC,EAAE,CAAC;EACb,OAAO,CAACD,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IACvE,OAAO,CAAC,CAACC,CAAC;EACZ,CAAC,EAAE,CAAC;AACN;AACA,SAASD,yBAAyB,IAAIQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}