import { PlainObject, DistanceType, GraphData, KeyValueMap } from '../types';
/**
 * 获取数据中所有的属性及其对应的值
 * @param dataList 数据集
 * @param involvedKeys 参与计算的key集合
 * @param uninvolvedKeys 不参与计算的key集合
 */
export declare const getAllKeyValueMap: (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => KeyValueMap;
/**
 * one-hot编码：数据特征提取
 * @param dataList 数据集
 * @param involvedKeys 参与计算的的key集合
 * @param uninvolvedKeys 不参与计算的key集合
 */
export declare const oneHot: (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => any[];
/**
 * getDistance：获取两个元素之间的距离
 * @param item
 * @param otherItem
 * @param distanceType 距离类型
 * @param graphData 图数据
 */
export declare const getDistance: (item: any, otherItem: any, distanceType?: DistanceType, graphData?: GraphData) => number;
declare const _default: {
    getAllKeyValueMap: (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => KeyValueMap;
    oneHot: (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => any[];
    getDistance: (item: any, otherItem: any, distanceType?: DistanceType, graphData?: GraphData) => number;
};
export default _default;
