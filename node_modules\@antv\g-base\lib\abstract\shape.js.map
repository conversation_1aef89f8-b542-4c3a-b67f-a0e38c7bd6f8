{"version": 3, "file": "shape.js", "sourceRoot": "", "sources": ["../../src/abstract/shape.ts"], "names": [], "mappings": ";;;AAEA,qCAAgC;AAChC,yCAA8C;AAC9C;IAAqC,yCAAO;IAC1C,uBAAY,GAAa;eACvB,kBAAM,GAAG,CAAC;IACZ,CAAC;IAED,UAAU;IACV,iCAAS,GAAT,UAAU,IAAI,EAAE,IAAI;QAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;IAC1F,CAAC;IAED;;;;OAIG;IACH,wCAAgB,GAAhB,UAAiB,WAAuB;QACtC,iBAAM,gBAAgB,YAAC,WAAW,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IACD,wBAAwB;IACxB,+BAAO,GAAP;QACE,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACxB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,cAAc;IACd,qCAAa,GAAb;QACE,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACpC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAQD,mCAAW,GAAX,UAAY,MAAgB;QAC1B,iBAAM,WAAW,YAAC,MAAM,CAAC,CAAC;QAC1B,YAAY;QACZ,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,2CAAmB,GAAnB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACpC,IAAA,IAAI,GAAuB,IAAI,KAA3B,EAAE,IAAI,GAAiB,IAAI,KAArB,EAAE,IAAI,GAAW,IAAI,KAAf,EAAE,IAAI,GAAK,IAAI,KAAT,CAAU;QACtC,IAAI,WAAW,EAAE;YACf,IAAM,OAAO,GAAG,qBAAY,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClE,IAAM,QAAQ,GAAG,qBAAY,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACnE,IAAM,UAAU,GAAG,qBAAY,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACrE,IAAM,WAAW,GAAG,qBAAY,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACtE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;SACzE;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,yBAAyB;QACzB,IAAI,KAAK,CAAC,WAAW,EAAE;YACb,IAAA,KAAyD,KAAK,WAAhD,EAAd,UAAU,mBAAG,CAAC,KAAA,EAAE,KAAyC,KAAK,cAA7B,EAAjB,aAAa,mBAAG,CAAC,KAAA,EAAE,KAAsB,KAAK,cAAV,EAAjB,aAAa,mBAAG,CAAC,KAAA,CAAW;YACvE,IAAM,UAAU,GAAG,IAAI,GAAG,UAAU,GAAG,aAAa,CAAC;YACrD,IAAM,WAAW,GAAG,IAAI,GAAG,UAAU,GAAG,aAAa,CAAC;YACtD,IAAM,SAAS,GAAG,IAAI,GAAG,UAAU,GAAG,aAAa,CAAC;YACpD,IAAM,YAAY,GAAG,IAAI,GAAG,UAAU,GAAG,aAAa,CAAC;YACvD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAClC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACnC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACjC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;SACrC;QACD,OAAO;YACL,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,IAAI;YACP,IAAI,MAAA;YACJ,IAAI,MAAA;YACJ,IAAI,MAAA;YACJ,IAAI,MAAA;YACJ,KAAK,EAAE,IAAI,GAAG,IAAI;YAClB,MAAM,EAAE,IAAI,GAAG,IAAI;SACpB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,sCAAc,GAAd;QACE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO;IACP,mCAAW,GAAX;QACE,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;OAMG;IACH,iCAAS,GAAT,UAAU,IAAY,EAAE,IAAY;QAClC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACH,oCAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,iBAAiB;IACjB,6BAAK,GAAL,UAAM,CAAS,EAAE,CAAS;QACxB,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAChD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAA,IAAI,GAAU,GAAG,GAAb,EAAE,IAAI,GAAI,GAAG,GAAP,CAAQ;QACzB,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,4BAA4B;QAC5B,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACvB,OAAO,MAAM,CAAC;SACf;QACD,qBAAqB;QACrB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YACzC,YAAY;YACZ,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC9B,OAAO,IAAI,CAAC;aACb;YACD,cAAc;YACd,IAAI,eAAe,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACxD,OAAO,IAAI,CAAC;aACb;YACD,cAAc;YACd,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACpD,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACH,oBAAC;AAAD,CAAC,AA7JD,CAAqC,iBAAO,GA6J3C;AAED,kBAAe,aAAa,CAAC"}