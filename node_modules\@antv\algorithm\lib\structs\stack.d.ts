export default class Stack {
    private linkedList;
    private maxStep;
    constructor(maxStep?: number);
    get length(): number;
    /**
     * 判断栈是否为空，如果链表中没有头部元素，则栈为空
     */
    isEmpty(): boolean;
    /**
     * 是否到定义的栈的最大长度，如果达到最大长度后，不再允许入栈
     */
    isMaxStack(): boolean;
    /**
     * 访问顶端元素
     */
    peek(): any;
    push(value: any): void;
    pop(): any;
    toArray(): any[];
    clear(): void;
}
