"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "GADDIAsync", {
  enumerable: true,
  get: function get() {
    return _index.GADDIAsync;
  }
});
Object.defineProperty(exports, "connectedComponentAsync", {
  enumerable: true,
  get: function get() {
    return _index.connectedComponentAsync;
  }
});
exports.default = void 0;
Object.defineProperty(exports, "detectAllCyclesAsync", {
  enumerable: true,
  get: function get() {
    return _index.detectAllCyclesAsync;
  }
});
Object.defineProperty(exports, "detectAllDirectedCycleAsync", {
  enumerable: true,
  get: function get() {
    return _index.detectAllDirectedCycleAsync;
  }
});
Object.defineProperty(exports, "detectAllUndirectedCycleAsync", {
  enumerable: true,
  get: function get() {
    return _index.detectAllUndirectedCycleAsync;
  }
});
Object.defineProperty(exports, "detectCycleAsync", {
  enumerable: true,
  get: function get() {
    return _index.detectCycleAsync;
  }
});
exports.detectDirectedCycleAsync = void 0;
Object.defineProperty(exports, "dijkstraAsync", {
  enumerable: true,
  get: function get() {
    return _index.dijkstraAsync;
  }
});
Object.defineProperty(exports, "findAllPathAsync", {
  enumerable: true,
  get: function get() {
    return _index.findAllPathAsync;
  }
});
Object.defineProperty(exports, "findShortestPathAsync", {
  enumerable: true,
  get: function get() {
    return _index.findShortestPathAsync;
  }
});
Object.defineProperty(exports, "floydWarshallAsync", {
  enumerable: true,
  get: function get() {
    return _index.floydWarshallAsync;
  }
});
Object.defineProperty(exports, "getAdjMatrixAsync", {
  enumerable: true,
  get: function get() {
    return _index.getAdjMatrixAsync;
  }
});
Object.defineProperty(exports, "getDegreeAsync", {
  enumerable: true,
  get: function get() {
    return _index.getDegreeAsync;
  }
});
Object.defineProperty(exports, "getInDegreeAsync", {
  enumerable: true,
  get: function get() {
    return _index.getInDegreeAsync;
  }
});
Object.defineProperty(exports, "getNeighborsAsync", {
  enumerable: true,
  get: function get() {
    return _index.getNeighborsAsync;
  }
});
Object.defineProperty(exports, "getOutDegreeAsync", {
  enumerable: true,
  get: function get() {
    return _index.getOutDegreeAsync;
  }
});
Object.defineProperty(exports, "labelPropagationAsync", {
  enumerable: true,
  get: function get() {
    return _index.labelPropagationAsync;
  }
});
Object.defineProperty(exports, "louvainAsync", {
  enumerable: true,
  get: function get() {
    return _index.louvainAsync;
  }
});
Object.defineProperty(exports, "minimumSpanningTreeAsync", {
  enumerable: true,
  get: function get() {
    return _index.minimumSpanningTreeAsync;
  }
});
Object.defineProperty(exports, "pageRankAsync", {
  enumerable: true,
  get: function get() {
    return _index.pageRankAsync;
  }
});
var _index = require("./workers/index");
var detectDirectedCycleAsync = _index.detectCycleAsync;
exports.detectDirectedCycleAsync = detectDirectedCycleAsync;
var _default = {
  getAdjMatrixAsync: _index.getAdjMatrixAsync,
  connectedComponentAsync: _index.connectedComponentAsync,
  getDegreeAsync: _index.getDegreeAsync,
  getInDegreeAsync: _index.getInDegreeAsync,
  getOutDegreeAsync: _index.getOutDegreeAsync,
  detectCycleAsync: _index.detectCycleAsync,
  detectDirectedCycleAsync: detectDirectedCycleAsync,
  detectAllCyclesAsync: _index.detectAllCyclesAsync,
  detectAllDirectedCycleAsync: _index.detectAllDirectedCycleAsync,
  detectAllUndirectedCycleAsync: _index.detectAllUndirectedCycleAsync,
  dijkstraAsync: _index.dijkstraAsync,
  findAllPathAsync: _index.findAllPathAsync,
  findShortestPathAsync: _index.findShortestPathAsync,
  floydWarshallAsync: _index.floydWarshallAsync,
  labelPropagationAsync: _index.labelPropagationAsync,
  louvainAsync: _index.louvainAsync,
  minimumSpanningTreeAsync: _index.minimumSpanningTreeAsync,
  pageRankAsync: _index.pageRankAsync,
  getNeighborsAsync: _index.getNeighborsAsync,
  GADDIAsync: _index.GADDIAsync
};
exports.default = _default;