{"ast": null, "code": "import \"core-js/modules/es.array.fill.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport MapDraw from '../../component/helper/MapDraw.js';\nimport ChartView from '../../view/Chart.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { setStatesFlag, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nvar MapView = /** @class */function (_super) {\n  __extends(MapView, _super);\n  function MapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MapView.type;\n    return _this;\n  }\n  MapView.prototype.render = function (mapModel, ecModel, api, payload) {\n    // Not render if it is an toggleSelect action from self\n    if (payload && payload.type === 'mapToggleSelect' && payload.from === this.uid) {\n      return;\n    }\n    var group = this.group;\n    group.removeAll();\n    if (mapModel.getHostGeoModel()) {\n      return;\n    }\n    if (this._mapDraw && payload && payload.type === 'geoRoam') {\n      this._mapDraw.resetForLabelLayout();\n    }\n    // Not update map if it is an roam action from self\n    if (!(payload && payload.type === 'geoRoam' && payload.componentType === 'series' && payload.seriesId === mapModel.id)) {\n      if (mapModel.needsDrawMap) {\n        var mapDraw = this._mapDraw || new MapDraw(api);\n        group.add(mapDraw.group);\n        mapDraw.draw(mapModel, ecModel, api, this, payload);\n        this._mapDraw = mapDraw;\n      } else {\n        // Remove drawn map\n        this._mapDraw && this._mapDraw.remove();\n        this._mapDraw = null;\n      }\n    } else {\n      var mapDraw = this._mapDraw;\n      mapDraw && group.add(mapDraw.group);\n    }\n    mapModel.get('showLegendSymbol') && ecModel.getComponent('legend') && this._renderSymbols(mapModel, ecModel, api);\n  };\n  MapView.prototype.remove = function () {\n    this._mapDraw && this._mapDraw.remove();\n    this._mapDraw = null;\n    this.group.removeAll();\n  };\n  MapView.prototype.dispose = function () {\n    this._mapDraw && this._mapDraw.remove();\n    this._mapDraw = null;\n  };\n  MapView.prototype._renderSymbols = function (mapModel, ecModel, api) {\n    var originalData = mapModel.originalData;\n    var group = this.group;\n    originalData.each(originalData.mapDimension('value'), function (value, originalDataIndex) {\n      if (isNaN(value)) {\n        return;\n      }\n      var layout = originalData.getItemLayout(originalDataIndex);\n      if (!layout || !layout.point) {\n        // Not exists in map\n        return;\n      }\n      var point = layout.point;\n      var offset = layout.offset;\n      var circle = new graphic.Circle({\n        style: {\n          // Because the special of map draw.\n          // Which needs statistic of multiple series and draw on one map.\n          // And each series also need a symbol with legend color\n          //\n          // Layout and visual are put one the different data\n          // TODO\n          fill: mapModel.getData().getVisual('style').fill\n        },\n        shape: {\n          cx: point[0] + offset * 9,\n          cy: point[1],\n          r: 3\n        },\n        silent: true,\n        // Do not overlap the first series, on which labels are displayed.\n        z2: 8 + (!offset ? Z2_EMPHASIS_LIFT + 1 : 0)\n      });\n      // Only the series that has the first value on the same region is in charge of rendering the label.\n      // But consider the case:\n      // series: [\n      //     {id: 'X', type: 'map', map: 'm', {data: [{name: 'A', value: 11}, {name: 'B', {value: 22}]},\n      //     {id: 'Y', type: 'map', map: 'm', {data: [{name: 'A', value: 21}, {name: 'C', {value: 33}]}\n      // ]\n      // The offset `0` of item `A` is at series `X`, but of item `C` is at series `Y`.\n      // For backward compatibility, we follow the rule that render label `A` by the\n      // settings on series `X` but render label `C` by the settings on series `Y`.\n      if (!offset) {\n        var fullData = mapModel.mainSeries.getData();\n        var name_1 = originalData.getName(originalDataIndex);\n        var fullIndex_1 = fullData.indexOfName(name_1);\n        var itemModel = originalData.getItemModel(originalDataIndex);\n        var labelModel = itemModel.getModel('label');\n        var regionGroup = fullData.getItemGraphicEl(fullIndex_1);\n        // `getFormattedLabel` needs to use `getData` inside. Here\n        // `mapModel.getData()` is shallow cloned from `mainSeries.getData()`.\n        // FIXME\n        // If this is not the `mainSeries`, the item model (like label formatter)\n        // set on original data item will never get. But it has been working\n        // like that from the beginning, and this scenario is rarely encountered.\n        // So it won't be fixed until we have to.\n        setLabelStyle(circle, getLabelStatesModels(itemModel), {\n          labelFetcher: {\n            getFormattedLabel: function getFormattedLabel(idx, state) {\n              return mapModel.getFormattedLabel(fullIndex_1, state);\n            }\n          },\n          defaultText: name_1\n        });\n        circle.disableLabelAnimation = true;\n        if (!labelModel.get('position')) {\n          circle.setTextConfig({\n            position: 'bottom'\n          });\n        }\n        regionGroup.onHoverStateChange = function (toState) {\n          setStatesFlag(circle, toState);\n        };\n      }\n      group.add(circle);\n    });\n  };\n  MapView.type = 'map';\n  return MapView;\n}(ChartView);\nexport default MapView;", "map": {"version": 3, "names": ["__extends", "graphic", "MapDraw", "ChartView", "setLabelStyle", "getLabelStatesModels", "setStatesFlag", "Z2_EMPHASIS_LIFT", "MapView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "mapModel", "ecModel", "api", "payload", "from", "uid", "group", "removeAll", "getHostGeoModel", "_mapDraw", "resetForLabelLayout", "componentType", "seriesId", "id", "needsDrawMap", "mapDraw", "add", "draw", "remove", "get", "getComponent", "_renderSymbols", "dispose", "originalData", "each", "mapDimension", "value", "originalDataIndex", "isNaN", "layout", "getItemLayout", "point", "offset", "circle", "Circle", "style", "fill", "getData", "getVisual", "shape", "cx", "cy", "r", "silent", "z2", "fullData", "mainSeries", "name_1", "getName", "fullIndex_1", "indexOfName", "itemModel", "getItemModel", "labelModel", "getModel", "regionGroup", "getItemGraphicEl", "labelFetcher", "getFormattedLabel", "idx", "state", "defaultText", "disableLabelAnimation", "setTextConfig", "position", "onHoverStateChange", "toState"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/chart/map/MapView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport MapDraw from '../../component/helper/MapDraw.js';\nimport ChartView from '../../view/Chart.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { setStatesFlag, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nvar MapView = /** @class */function (_super) {\n  __extends(MapView, _super);\n  function MapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MapView.type;\n    return _this;\n  }\n  MapView.prototype.render = function (mapModel, ecModel, api, payload) {\n    // Not render if it is an toggleSelect action from self\n    if (payload && payload.type === 'mapToggleSelect' && payload.from === this.uid) {\n      return;\n    }\n    var group = this.group;\n    group.removeAll();\n    if (mapModel.getHostGeoModel()) {\n      return;\n    }\n    if (this._mapDraw && payload && payload.type === 'geoRoam') {\n      this._mapDraw.resetForLabelLayout();\n    }\n    // Not update map if it is an roam action from self\n    if (!(payload && payload.type === 'geoRoam' && payload.componentType === 'series' && payload.seriesId === mapModel.id)) {\n      if (mapModel.needsDrawMap) {\n        var mapDraw = this._mapDraw || new MapDraw(api);\n        group.add(mapDraw.group);\n        mapDraw.draw(mapModel, ecModel, api, this, payload);\n        this._mapDraw = mapDraw;\n      } else {\n        // Remove drawn map\n        this._mapDraw && this._mapDraw.remove();\n        this._mapDraw = null;\n      }\n    } else {\n      var mapDraw = this._mapDraw;\n      mapDraw && group.add(mapDraw.group);\n    }\n    mapModel.get('showLegendSymbol') && ecModel.getComponent('legend') && this._renderSymbols(mapModel, ecModel, api);\n  };\n  MapView.prototype.remove = function () {\n    this._mapDraw && this._mapDraw.remove();\n    this._mapDraw = null;\n    this.group.removeAll();\n  };\n  MapView.prototype.dispose = function () {\n    this._mapDraw && this._mapDraw.remove();\n    this._mapDraw = null;\n  };\n  MapView.prototype._renderSymbols = function (mapModel, ecModel, api) {\n    var originalData = mapModel.originalData;\n    var group = this.group;\n    originalData.each(originalData.mapDimension('value'), function (value, originalDataIndex) {\n      if (isNaN(value)) {\n        return;\n      }\n      var layout = originalData.getItemLayout(originalDataIndex);\n      if (!layout || !layout.point) {\n        // Not exists in map\n        return;\n      }\n      var point = layout.point;\n      var offset = layout.offset;\n      var circle = new graphic.Circle({\n        style: {\n          // Because the special of map draw.\n          // Which needs statistic of multiple series and draw on one map.\n          // And each series also need a symbol with legend color\n          //\n          // Layout and visual are put one the different data\n          // TODO\n          fill: mapModel.getData().getVisual('style').fill\n        },\n        shape: {\n          cx: point[0] + offset * 9,\n          cy: point[1],\n          r: 3\n        },\n        silent: true,\n        // Do not overlap the first series, on which labels are displayed.\n        z2: 8 + (!offset ? Z2_EMPHASIS_LIFT + 1 : 0)\n      });\n      // Only the series that has the first value on the same region is in charge of rendering the label.\n      // But consider the case:\n      // series: [\n      //     {id: 'X', type: 'map', map: 'm', {data: [{name: 'A', value: 11}, {name: 'B', {value: 22}]},\n      //     {id: 'Y', type: 'map', map: 'm', {data: [{name: 'A', value: 21}, {name: 'C', {value: 33}]}\n      // ]\n      // The offset `0` of item `A` is at series `X`, but of item `C` is at series `Y`.\n      // For backward compatibility, we follow the rule that render label `A` by the\n      // settings on series `X` but render label `C` by the settings on series `Y`.\n      if (!offset) {\n        var fullData = mapModel.mainSeries.getData();\n        var name_1 = originalData.getName(originalDataIndex);\n        var fullIndex_1 = fullData.indexOfName(name_1);\n        var itemModel = originalData.getItemModel(originalDataIndex);\n        var labelModel = itemModel.getModel('label');\n        var regionGroup = fullData.getItemGraphicEl(fullIndex_1);\n        // `getFormattedLabel` needs to use `getData` inside. Here\n        // `mapModel.getData()` is shallow cloned from `mainSeries.getData()`.\n        // FIXME\n        // If this is not the `mainSeries`, the item model (like label formatter)\n        // set on original data item will never get. But it has been working\n        // like that from the beginning, and this scenario is rarely encountered.\n        // So it won't be fixed until we have to.\n        setLabelStyle(circle, getLabelStatesModels(itemModel), {\n          labelFetcher: {\n            getFormattedLabel: function (idx, state) {\n              return mapModel.getFormattedLabel(fullIndex_1, state);\n            }\n          },\n          defaultText: name_1\n        });\n        circle.disableLabelAnimation = true;\n        if (!labelModel.get('position')) {\n          circle.setTextConfig({\n            position: 'bottom'\n          });\n        }\n        regionGroup.onHoverStateChange = function (toState) {\n          setStatesFlag(circle, toState);\n        };\n      }\n      group.add(circle);\n    });\n  };\n  MapView.type = 'map';\n  return MapView;\n}(ChartView);\nexport default MapView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,sBAAsB;AACtE,IAAIC,OAAO,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC3CT,SAAS,CAACQ,OAAO,EAAEC,MAAM,CAAC;EAC1B,SAASD,OAAOA,CAAA,EAAG;IACjB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,OAAO,CAACK,IAAI;IACzB,OAAOH,KAAK;EACd;EACAF,OAAO,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACpE;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACN,IAAI,KAAK,iBAAiB,IAAIM,OAAO,CAACC,IAAI,KAAK,IAAI,CAACC,GAAG,EAAE;MAC9E;IACF;IACA,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,CAACC,SAAS,CAAC,CAAC;IACjB,IAAIP,QAAQ,CAACQ,eAAe,CAAC,CAAC,EAAE;MAC9B;IACF;IACA,IAAI,IAAI,CAACC,QAAQ,IAAIN,OAAO,IAAIA,OAAO,CAACN,IAAI,KAAK,SAAS,EAAE;MAC1D,IAAI,CAACY,QAAQ,CAACC,mBAAmB,CAAC,CAAC;IACrC;IACA;IACA,IAAI,EAAEP,OAAO,IAAIA,OAAO,CAACN,IAAI,KAAK,SAAS,IAAIM,OAAO,CAACQ,aAAa,KAAK,QAAQ,IAAIR,OAAO,CAACS,QAAQ,KAAKZ,QAAQ,CAACa,EAAE,CAAC,EAAE;MACtH,IAAIb,QAAQ,CAACc,YAAY,EAAE;QACzB,IAAIC,OAAO,GAAG,IAAI,CAACN,QAAQ,IAAI,IAAIvB,OAAO,CAACgB,GAAG,CAAC;QAC/CI,KAAK,CAACU,GAAG,CAACD,OAAO,CAACT,KAAK,CAAC;QACxBS,OAAO,CAACE,IAAI,CAACjB,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAE,IAAI,EAAEC,OAAO,CAAC;QACnD,IAAI,CAACM,QAAQ,GAAGM,OAAO;MACzB,CAAC,MAAM;QACL;QACA,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACS,MAAM,CAAC,CAAC;QACvC,IAAI,CAACT,QAAQ,GAAG,IAAI;MACtB;IACF,CAAC,MAAM;MACL,IAAIM,OAAO,GAAG,IAAI,CAACN,QAAQ;MAC3BM,OAAO,IAAIT,KAAK,CAACU,GAAG,CAACD,OAAO,CAACT,KAAK,CAAC;IACrC;IACAN,QAAQ,CAACmB,GAAG,CAAC,kBAAkB,CAAC,IAAIlB,OAAO,CAACmB,YAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACC,cAAc,CAACrB,QAAQ,EAAEC,OAAO,EAAEC,GAAG,CAAC;EACnH,CAAC;EACDV,OAAO,CAACM,SAAS,CAACoB,MAAM,GAAG,YAAY;IACrC,IAAI,CAACT,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACS,MAAM,CAAC,CAAC;IACvC,IAAI,CAACT,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACH,KAAK,CAACC,SAAS,CAAC,CAAC;EACxB,CAAC;EACDf,OAAO,CAACM,SAAS,CAACwB,OAAO,GAAG,YAAY;IACtC,IAAI,CAACb,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACS,MAAM,CAAC,CAAC;IACvC,IAAI,CAACT,QAAQ,GAAG,IAAI;EACtB,CAAC;EACDjB,OAAO,CAACM,SAAS,CAACuB,cAAc,GAAG,UAAUrB,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACnE,IAAIqB,YAAY,GAAGvB,QAAQ,CAACuB,YAAY;IACxC,IAAIjB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBiB,YAAY,CAACC,IAAI,CAACD,YAAY,CAACE,YAAY,CAAC,OAAO,CAAC,EAAE,UAAUC,KAAK,EAAEC,iBAAiB,EAAE;MACxF,IAAIC,KAAK,CAACF,KAAK,CAAC,EAAE;QAChB;MACF;MACA,IAAIG,MAAM,GAAGN,YAAY,CAACO,aAAa,CAACH,iBAAiB,CAAC;MAC1D,IAAI,CAACE,MAAM,IAAI,CAACA,MAAM,CAACE,KAAK,EAAE;QAC5B;QACA;MACF;MACA,IAAIA,KAAK,GAAGF,MAAM,CAACE,KAAK;MACxB,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM;MAC1B,IAAIC,MAAM,GAAG,IAAIhD,OAAO,CAACiD,MAAM,CAAC;QAC9BC,KAAK,EAAE;UACL;UACA;UACA;UACA;UACA;UACA;UACAC,IAAI,EAAEpC,QAAQ,CAACqC,OAAO,CAAC,CAAC,CAACC,SAAS,CAAC,OAAO,CAAC,CAACF;QAC9C,CAAC;QACDG,KAAK,EAAE;UACLC,EAAE,EAAET,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,GAAG,CAAC;UACzBS,EAAE,EAAEV,KAAK,CAAC,CAAC,CAAC;UACZW,CAAC,EAAE;QACL,CAAC;QACDC,MAAM,EAAE,IAAI;QACZ;QACAC,EAAE,EAAE,CAAC,IAAI,CAACZ,MAAM,GAAGzC,gBAAgB,GAAG,CAAC,GAAG,CAAC;MAC7C,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACyC,MAAM,EAAE;QACX,IAAIa,QAAQ,GAAG7C,QAAQ,CAAC8C,UAAU,CAACT,OAAO,CAAC,CAAC;QAC5C,IAAIU,MAAM,GAAGxB,YAAY,CAACyB,OAAO,CAACrB,iBAAiB,CAAC;QACpD,IAAIsB,WAAW,GAAGJ,QAAQ,CAACK,WAAW,CAACH,MAAM,CAAC;QAC9C,IAAII,SAAS,GAAG5B,YAAY,CAAC6B,YAAY,CAACzB,iBAAiB,CAAC;QAC5D,IAAI0B,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAAC,OAAO,CAAC;QAC5C,IAAIC,WAAW,GAAGV,QAAQ,CAACW,gBAAgB,CAACP,WAAW,CAAC;QACxD;QACA;QACA;QACA;QACA;QACA;QACA;QACA7D,aAAa,CAAC6C,MAAM,EAAE5C,oBAAoB,CAAC8D,SAAS,CAAC,EAAE;UACrDM,YAAY,EAAE;YACZC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAYC,GAAG,EAAEC,KAAK,EAAE;cACvC,OAAO5D,QAAQ,CAAC0D,iBAAiB,CAACT,WAAW,EAAEW,KAAK,CAAC;YACvD;UACF,CAAC;UACDC,WAAW,EAAEd;QACf,CAAC,CAAC;QACFd,MAAM,CAAC6B,qBAAqB,GAAG,IAAI;QACnC,IAAI,CAACT,UAAU,CAAClC,GAAG,CAAC,UAAU,CAAC,EAAE;UAC/Bc,MAAM,CAAC8B,aAAa,CAAC;YACnBC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QACAT,WAAW,CAACU,kBAAkB,GAAG,UAAUC,OAAO,EAAE;UAClD5E,aAAa,CAAC2C,MAAM,EAAEiC,OAAO,CAAC;QAChC,CAAC;MACH;MACA5D,KAAK,CAACU,GAAG,CAACiB,MAAM,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC;EACDzC,OAAO,CAACK,IAAI,GAAG,KAAK;EACpB,OAAOL,OAAO;AAChB,CAAC,CAACL,SAAS,CAAC;AACZ,eAAeK,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}