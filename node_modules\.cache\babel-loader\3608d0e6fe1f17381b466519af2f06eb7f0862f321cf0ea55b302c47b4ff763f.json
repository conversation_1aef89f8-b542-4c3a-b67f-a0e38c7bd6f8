{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport SliderTimelineModel from './SliderTimelineModel.js';\nimport SliderTimelineView from './SliderTimelineView.js';\nimport { installTimelineAction } from './timelineAction.js';\nimport preprocessor from './preprocessor.js';\nexport function install(registers) {\n  registers.registerComponentModel(SliderTimelineModel);\n  registers.registerComponentView(SliderTimelineView);\n  registers.registerSubTypeDefaulter('timeline', function () {\n    // Only slider now.\n    return 'slider';\n  });\n  installTimelineAction(registers);\n  registers.registerPreprocessor(preprocessor);\n}", "map": {"version": 3, "names": ["SliderTimelineModel", "SliderTimelineView", "installTimelineAction", "preprocessor", "install", "registers", "registerComponentModel", "registerComponentView", "registerSubTypeDefaulter", "registerPreprocessor"], "sources": ["E:/新项目/整理6/adminweb/node_modules/echarts/lib/component/timeline/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport SliderTimelineModel from './SliderTimelineModel.js';\nimport SliderTimelineView from './SliderTimelineView.js';\nimport { installTimelineAction } from './timelineAction.js';\nimport preprocessor from './preprocessor.js';\nexport function install(registers) {\n  registers.registerComponentModel(SliderTimelineModel);\n  registers.registerComponentView(SliderTimelineView);\n  registers.registerSubTypeDefaulter('timeline', function () {\n    // Only slider now.\n    return 'slider';\n  });\n  installTimelineAction(registers);\n  registers.registerPreprocessor(preprocessor);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA,OAAOA,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,sBAAsB,CAACN,mBAAmB,CAAC;EACrDK,SAAS,CAACE,qBAAqB,CAACN,kBAAkB,CAAC;EACnDI,SAAS,CAACG,wBAAwB,CAAC,UAAU,EAAE,YAAY;IACzD;IACA,OAAO,QAAQ;EACjB,CAAC,CAAC;EACFN,qBAAqB,CAACG,SAAS,CAAC;EAChCA,SAAS,CAACI,oBAAoB,CAACN,YAAY,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}