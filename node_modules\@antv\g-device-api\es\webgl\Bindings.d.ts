import type { Bindings, BindingsDescriptor, BufferBinding, SamplerBinding } from '../api';
import { ResourceType } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export interface BindingLayoutTable_GL {
    firstUniformBuffer: number;
    numUniformBuffers: number;
    firstSampler: number;
    numSamplers: number;
}
export interface BindingLayouts_GL {
    numSamplers: number;
    numUniformBuffers: number;
    bindingLayoutTables: BindingLayoutTable_GL[];
}
export declare class Bindings_GL extends ResourceBase_GL implements Bindings {
    type: ResourceType.Bindings;
    uniformBufferBindings: BufferBinding[];
    samplerBindings: (SamplerBinding | null)[];
    bindingLayouts: BindingLayouts_GL;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: BindingsDescriptor;
    });
    private createBindingLayouts;
}
