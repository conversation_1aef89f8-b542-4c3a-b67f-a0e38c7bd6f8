import type { Disposable, ResourceBase } from '../api';
import EventEmitter from 'eventemitter3';
import type { Device_GL } from './Device';
export declare class ResourceBase_GL extends EventEmitter implements ResourceBase, Disposable {
    id: number;
    name: string;
    device: Device_GL;
    constructor({ id, device }: {
        id: number;
        device: Device_GL;
    });
    destroy(): void;
}
