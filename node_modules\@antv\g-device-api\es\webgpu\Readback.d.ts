import type { Buffer, Readback, Texture } from '../api';
import { Format, ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class Readback_WebGPU extends ResourceBase_WebGPU implements Readback {
    type: ResourceType.Readback;
    constructor({ id, device }: {
        id: number;
        device: IDevice_WebGPU;
    });
    readTexture(t: Texture, x: number, y: number, width: number, height: number, dst: ArrayBufferView, dstOffset?: number, length?: number): Promise<ArrayBufferView>;
    readTextureSync(t: Texture, x: number, y: number, width: number, height: number, dst: ArrayBufferView, dstOffset?: number, length?: number): ArrayBufferView;
    readBuffer(b: Buffer, srcByteOffset?: number, dstArrayBufferView?: ArrayBufferView, dstOffset?: number, _size?: number, type?: Format, noDataConversion?: boolean, destroy?: boolean, bytesPerRow?: number, bytesPerRowAligned?: number, height?: number): Promise<ArrayBufferView>;
    private getHalfFloatAsFloatRGBAArrayBuffer;
}
