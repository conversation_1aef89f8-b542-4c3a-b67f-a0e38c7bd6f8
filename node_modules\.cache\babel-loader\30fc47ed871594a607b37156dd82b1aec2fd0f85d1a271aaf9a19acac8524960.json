{"ast": null, "code": "require(\"core-js/modules/es.error.cause.js\");\nrequire(\"core-js/modules/es.error.to-string.js\");\nvar _typeof = require(\"./typeof.js\")[\"default\"];\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "map": {"version": 3, "names": ["_typeof", "require", "assertThisInitialized", "_possibleConstructorReturn", "t", "e", "TypeError", "module", "exports", "__esModule"], "sources": ["F:/常规项目/华通宝/adminweb/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": ";;AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC;AAC/C,IAAIC,qBAAqB,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AACjE,SAASE,0BAA0BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAIA,CAAC,KAAK,QAAQ,IAAIL,OAAO,CAACK,CAAC,CAAC,IAAI,UAAU,IAAI,OAAOA,CAAC,CAAC,EAAE,OAAOA,CAAC;EACrE,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,0DAA0D,CAAC;EACjG,OAAOJ,qBAAqB,CAACE,CAAC,CAAC;AACjC;AACAG,MAAM,CAACC,OAAO,GAAGL,0BAA0B,EAAEI,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}