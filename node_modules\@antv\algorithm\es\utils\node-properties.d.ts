import { NodeConfig } from '../types';
export declare const getAllSortProperties: (nodes?: NodeConfig[], n?: number) => string[];
export declare const getPropertyWeight: (nodes: NodeConfig[]) => any[];
export declare const getAllProperties: (nodes: any, key?: any) => any[];
declare const _default: {
    getAllSortProperties: (nodes?: NodeConfig[], n?: number) => string[];
    getPropertyWeight: (nodes: NodeConfig[]) => any[];
    getAllProperties: (nodes: any, key?: any) => any[];
};
export default _default;
