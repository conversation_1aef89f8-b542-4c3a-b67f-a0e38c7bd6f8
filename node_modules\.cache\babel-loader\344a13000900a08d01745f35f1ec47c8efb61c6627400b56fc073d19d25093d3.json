{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map([0, 1], function (dimIdx) {\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    var p1 = [];\n    var p2 = [];\n    p1[dimIdx] = val - halfSize;\n    p2[dimIdx] = val + halfSize;\n    p1[1 - dimIdx] = p2[1 - dimIdx] = dataItem[1 - dimIdx];\n    return Math.abs(this.dataToPoint(p1)[dimIdx] - this.dataToPoint(p2)[dimIdx]);\n  }, this);\n}\nexport default function geoPrepareCustom(coordSys) {\n  var rect = coordSys.getBoundingRect();\n  return {\n    coordSys: {\n      type: 'geo',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height,\n      zoom: coordSys.getZoom()\n    },\n    api: {\n      coord: function coord(data) {\n        // do not provide \"out\" and noRoam param,\n        // Compatible with this usage:\n        // echarts.util.map(item.points, api.coord)\n        return coordSys.dataToPoint(data);\n      },\n      size: zrUtil.bind(dataToCoordSize, coordSys)\n    }\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "dataToCoordSize", "dataSize", "dataItem", "map", "dimIdx", "val", "halfSize", "p1", "p2", "Math", "abs", "dataToPoint", "geoPrepareCustom", "coordSys", "rect", "getBoundingRect", "type", "x", "y", "width", "height", "zoom", "getZoom", "api", "coord", "data", "size", "bind"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/echarts/lib/coord/geo/prepareCustom.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map([0, 1], function (dimIdx) {\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    var p1 = [];\n    var p2 = [];\n    p1[dimIdx] = val - halfSize;\n    p2[dimIdx] = val + halfSize;\n    p1[1 - dimIdx] = p2[1 - dimIdx] = dataItem[1 - dimIdx];\n    return Math.abs(this.dataToPoint(p1)[dimIdx] - this.dataToPoint(p2)[dimIdx]);\n  }, this);\n}\nexport default function geoPrepareCustom(coordSys) {\n  var rect = coordSys.getBoundingRect();\n  return {\n    coordSys: {\n      type: 'geo',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height,\n      zoom: coordSys.getZoom()\n    },\n    api: {\n      coord: function (data) {\n        // do not provide \"out\" and noRoam param,\n        // Compatible with this usage:\n        // echarts.util.map(item.points, api.coord)\n        return coordSys.dataToPoint(data);\n      },\n      size: zrUtil.bind(dataToCoordSize, coordSys)\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,eAAeA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC3CA,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B,OAAOH,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUC,MAAM,EAAE;IAC1C,IAAIC,GAAG,GAAGH,QAAQ,CAACE,MAAM,CAAC;IAC1B,IAAIE,QAAQ,GAAGL,QAAQ,CAACG,MAAM,CAAC,GAAG,CAAC;IACnC,IAAIG,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACXD,EAAE,CAACH,MAAM,CAAC,GAAGC,GAAG,GAAGC,QAAQ;IAC3BE,EAAE,CAACJ,MAAM,CAAC,GAAGC,GAAG,GAAGC,QAAQ;IAC3BC,EAAE,CAAC,CAAC,GAAGH,MAAM,CAAC,GAAGI,EAAE,CAAC,CAAC,GAAGJ,MAAM,CAAC,GAAGF,QAAQ,CAAC,CAAC,GAAGE,MAAM,CAAC;IACtD,OAAOK,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAACJ,EAAE,CAAC,CAACH,MAAM,CAAC,GAAG,IAAI,CAACO,WAAW,CAACH,EAAE,CAAC,CAACJ,MAAM,CAAC,CAAC;EAC9E,CAAC,EAAE,IAAI,CAAC;AACV;AACA,eAAe,SAASQ,gBAAgBA,CAACC,QAAQ,EAAE;EACjD,IAAIC,IAAI,GAAGD,QAAQ,CAACE,eAAe,CAAC,CAAC;EACrC,OAAO;IACLF,QAAQ,EAAE;MACRG,IAAI,EAAE,KAAK;MACXC,CAAC,EAAEH,IAAI,CAACG,CAAC;MACTC,CAAC,EAAEJ,IAAI,CAACI,CAAC;MACTC,KAAK,EAAEL,IAAI,CAACK,KAAK;MACjBC,MAAM,EAAEN,IAAI,CAACM,MAAM;MACnBC,IAAI,EAAER,QAAQ,CAACS,OAAO,CAAC;IACzB,CAAC;IACDC,GAAG,EAAE;MACHC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,IAAI,EAAE;QACrB;QACA;QACA;QACA,OAAOZ,QAAQ,CAACF,WAAW,CAACc,IAAI,CAAC;MACnC,CAAC;MACDC,IAAI,EAAE3B,MAAM,CAAC4B,IAAI,CAAC3B,eAAe,EAAEa,QAAQ;IAC7C;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}