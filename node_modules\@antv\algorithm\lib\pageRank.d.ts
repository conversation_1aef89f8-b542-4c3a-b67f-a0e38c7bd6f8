import { GraphData } from "./types";
/**
 * PageRank https://en.wikipedia.org/wiki/PageRank
 * refer: https://github.com/anvaka/ngraph.pagerank
 * @param graph
 * @param epsilon 判断是否收敛的精度值，默认 0.000001
 * @param linkProb 阻尼系数（dumping factor），指任意时刻，用户访问到某节点后继续访问该节点链接的下一个节点的概率，经验值 0.85
 */
declare const pageRank: (graphData: GraphData, epsilon?: number, linkProb?: number) => {
    [key: string]: number;
};
export default pageRank;
