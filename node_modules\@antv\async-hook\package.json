{"name": "@antv/async-hook", "version": "2.2.9", "description": "the control flow for l7", "main": "lib/index.js", "module": "es/index.js", "types": "types/src/index.d.ts", "sideEffects": true, "files": ["lib", "es", "types", "README.md"], "scripts": {"tsc": "tsc -p ./tsconfig.type.json", "clean": "rimraf dist; rimraf es; rimraf lib;", "build": "rollup -c", "watch": "rollup -c --watch", "lint:ts": "run-p -c lint:ts-*", "test": "jest", "prepublish": "npm run clean && npm run build && npm run tsc"}, "author": "lzxue", "license": "ISC", "bugs": {"url": "https://github.com/antvis/L7/issues"}, "homepage": "https://github.com/antvis/L7#readme", "dependencies": {"async": "^3.1.1"}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/async": "^3.0.8", "rimraf": "^3.0.2", "rollup": "^2.52.6", "tslib": "^2.3.0", "typescript": "^4.0.0"}, "gitHead": "a5d354b66873f700730248d015c5e539c54b34b7", "publishConfig": {"access": "public"}}