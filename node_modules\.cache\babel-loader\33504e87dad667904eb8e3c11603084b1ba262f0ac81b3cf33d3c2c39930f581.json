{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// TODO: move labels out of viewport.\nimport { BoundingRect, updateProps, initProps, isElementRemoved } from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { parsePercent } from '../util/number.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport { updateLabelLinePoints, setLabelLineStyle, getLabelLineStatesModels } from './labelGuideHelper.js';\nimport { makeInner } from '../util/model.js';\nimport { retrieve2, each, keys, isFunction, filter, indexOf } from 'zrender/lib/core/util.js';\nimport { prepareLayoutList, hideOverlap, shiftLayoutOnX, shiftLayoutOnY } from './labelLayoutHelper.js';\nimport { labelInner, animateLabelValue } from './labelStyle.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nfunction cloneArr(points) {\n  if (points) {\n    var newPoints = [];\n    for (var i = 0; i < points.length; i++) {\n      newPoints.push(points[i].slice());\n    }\n    return newPoints;\n  }\n}\nfunction prepareLayoutCallbackParams(labelItem, hostEl) {\n  var label = labelItem.label;\n  var labelLine = hostEl && hostEl.getTextGuideLine();\n  return {\n    dataIndex: labelItem.dataIndex,\n    dataType: labelItem.dataType,\n    seriesIndex: labelItem.seriesModel.seriesIndex,\n    text: labelItem.label.style.text,\n    rect: labelItem.hostRect,\n    labelRect: labelItem.rect,\n    // x: labelAttr.x,\n    // y: labelAttr.y,\n    align: label.style.align,\n    verticalAlign: label.style.verticalAlign,\n    labelLinePoints: cloneArr(labelLine && labelLine.shape.points)\n  };\n}\nvar LABEL_OPTION_TO_STYLE_KEYS = ['align', 'verticalAlign', 'width', 'height', 'fontSize'];\nvar dummyTransformable = new Transformable();\nvar labelLayoutInnerStore = makeInner();\nvar labelLineAnimationStore = makeInner();\nfunction extendWithKeys(target, source, keys) {\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (source[key] != null) {\n      target[key] = source[key];\n    }\n  }\n}\nvar LABEL_LAYOUT_PROPS = ['x', 'y', 'rotation'];\nvar LabelManager = /** @class */function () {\n  function LabelManager() {\n    this._labelList = [];\n    this._chartViewList = [];\n  }\n  LabelManager.prototype.clearLabels = function () {\n    this._labelList = [];\n    this._chartViewList = [];\n  };\n  /**\n   * Add label to manager\n   */\n  LabelManager.prototype._addLabel = function (dataIndex, dataType, seriesModel, label, layoutOption) {\n    var labelStyle = label.style;\n    var hostEl = label.__hostTarget;\n    var textConfig = hostEl.textConfig || {};\n    // TODO: If label is in other state.\n    var labelTransform = label.getComputedTransform();\n    var labelRect = label.getBoundingRect().plain();\n    BoundingRect.applyTransform(labelRect, labelRect, labelTransform);\n    if (labelTransform) {\n      dummyTransformable.setLocalTransform(labelTransform);\n    } else {\n      // Identity transform.\n      dummyTransformable.x = dummyTransformable.y = dummyTransformable.rotation = dummyTransformable.originX = dummyTransformable.originY = 0;\n      dummyTransformable.scaleX = dummyTransformable.scaleY = 1;\n    }\n    dummyTransformable.rotation = normalizeRadian(dummyTransformable.rotation);\n    var host = label.__hostTarget;\n    var hostRect;\n    if (host) {\n      hostRect = host.getBoundingRect().plain();\n      var transform = host.getComputedTransform();\n      BoundingRect.applyTransform(hostRect, hostRect, transform);\n    }\n    var labelGuide = hostRect && host.getTextGuideLine();\n    this._labelList.push({\n      label: label,\n      labelLine: labelGuide,\n      seriesModel: seriesModel,\n      dataIndex: dataIndex,\n      dataType: dataType,\n      layoutOption: layoutOption,\n      computedLayoutOption: null,\n      rect: labelRect,\n      hostRect: hostRect,\n      // Label with lower priority will be hidden when overlapped\n      // Use rect size as default priority\n      priority: hostRect ? hostRect.width * hostRect.height : 0,\n      // Save default label attributes.\n      // For restore if developers want get back to default value in callback.\n      defaultAttr: {\n        ignore: label.ignore,\n        labelGuideIgnore: labelGuide && labelGuide.ignore,\n        x: dummyTransformable.x,\n        y: dummyTransformable.y,\n        scaleX: dummyTransformable.scaleX,\n        scaleY: dummyTransformable.scaleY,\n        rotation: dummyTransformable.rotation,\n        style: {\n          x: labelStyle.x,\n          y: labelStyle.y,\n          align: labelStyle.align,\n          verticalAlign: labelStyle.verticalAlign,\n          width: labelStyle.width,\n          height: labelStyle.height,\n          fontSize: labelStyle.fontSize\n        },\n        cursor: label.cursor,\n        attachedPos: textConfig.position,\n        attachedRot: textConfig.rotation\n      }\n    });\n  };\n  LabelManager.prototype.addLabelsOfSeries = function (chartView) {\n    var _this = this;\n    this._chartViewList.push(chartView);\n    var seriesModel = chartView.__model;\n    var layoutOption = seriesModel.get('labelLayout');\n    /**\n     * Ignore layouting if it's not specified anything.\n     */\n    if (!(isFunction(layoutOption) || keys(layoutOption).length)) {\n      return;\n    }\n    chartView.group.traverse(function (child) {\n      if (child.ignore) {\n        return true; // Stop traverse descendants.\n      }\n      // Only support label being hosted on graphic elements.\n      var textEl = child.getTextContent();\n      var ecData = getECData(child);\n      // Can only attach the text on the element with dataIndex\n      if (textEl && !textEl.disableLabelLayout) {\n        _this._addLabel(ecData.dataIndex, ecData.dataType, seriesModel, textEl, layoutOption);\n      }\n    });\n  };\n  LabelManager.prototype.updateLayoutConfig = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    function createDragHandler(el, labelLineModel) {\n      return function () {\n        updateLabelLinePoints(el, labelLineModel);\n      };\n    }\n    for (var i = 0; i < this._labelList.length; i++) {\n      var labelItem = this._labelList[i];\n      var label = labelItem.label;\n      var hostEl = label.__hostTarget;\n      var defaultLabelAttr = labelItem.defaultAttr;\n      var layoutOption = void 0;\n      // TODO A global layout option?\n      if (isFunction(labelItem.layoutOption)) {\n        layoutOption = labelItem.layoutOption(prepareLayoutCallbackParams(labelItem, hostEl));\n      } else {\n        layoutOption = labelItem.layoutOption;\n      }\n      layoutOption = layoutOption || {};\n      labelItem.computedLayoutOption = layoutOption;\n      var degreeToRadian = Math.PI / 180;\n      // TODO hostEl should always exists.\n      // Or label should not have parent because the x, y is all in global space.\n      if (hostEl) {\n        hostEl.setTextConfig({\n          // Force to set local false.\n          local: false,\n          // Ignore position and rotation config on the host el if x or y is changed.\n          position: layoutOption.x != null || layoutOption.y != null ? null : defaultLabelAttr.attachedPos,\n          // Ignore rotation config on the host el if rotation is changed.\n          rotation: layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.attachedRot,\n          offset: [layoutOption.dx || 0, layoutOption.dy || 0]\n        });\n      }\n      var needsUpdateLabelLine = false;\n      if (layoutOption.x != null) {\n        // TODO width of chart view.\n        label.x = parsePercent(layoutOption.x, width);\n        label.setStyle('x', 0); // Ignore movement in style. TODO: origin.\n        needsUpdateLabelLine = true;\n      } else {\n        label.x = defaultLabelAttr.x;\n        label.setStyle('x', defaultLabelAttr.style.x);\n      }\n      if (layoutOption.y != null) {\n        // TODO height of chart view.\n        label.y = parsePercent(layoutOption.y, height);\n        label.setStyle('y', 0); // Ignore movement in style.\n        needsUpdateLabelLine = true;\n      } else {\n        label.y = defaultLabelAttr.y;\n        label.setStyle('y', defaultLabelAttr.style.y);\n      }\n      if (layoutOption.labelLinePoints) {\n        var guideLine = hostEl.getTextGuideLine();\n        if (guideLine) {\n          guideLine.setShape({\n            points: layoutOption.labelLinePoints\n          });\n          // Not update\n          needsUpdateLabelLine = false;\n        }\n      }\n      var labelLayoutStore = labelLayoutInnerStore(label);\n      labelLayoutStore.needsUpdateLabelLine = needsUpdateLabelLine;\n      label.rotation = layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.rotation;\n      label.scaleX = defaultLabelAttr.scaleX;\n      label.scaleY = defaultLabelAttr.scaleY;\n      for (var k = 0; k < LABEL_OPTION_TO_STYLE_KEYS.length; k++) {\n        var key = LABEL_OPTION_TO_STYLE_KEYS[k];\n        label.setStyle(key, layoutOption[key] != null ? layoutOption[key] : defaultLabelAttr.style[key]);\n      }\n      if (layoutOption.draggable) {\n        label.draggable = true;\n        label.cursor = 'move';\n        if (hostEl) {\n          var hostModel = labelItem.seriesModel;\n          if (labelItem.dataIndex != null) {\n            var data = labelItem.seriesModel.getData(labelItem.dataType);\n            hostModel = data.getItemModel(labelItem.dataIndex);\n          }\n          label.on('drag', createDragHandler(hostEl, hostModel.getModel('labelLine')));\n        }\n      } else {\n        // TODO Other drag functions?\n        label.off('drag');\n        label.cursor = defaultLabelAttr.cursor;\n      }\n    }\n  };\n  LabelManager.prototype.layout = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    var labelList = prepareLayoutList(this._labelList);\n    var labelsNeedsAdjustOnX = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftX';\n    });\n    var labelsNeedsAdjustOnY = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftY';\n    });\n    shiftLayoutOnX(labelsNeedsAdjustOnX, 0, width);\n    shiftLayoutOnY(labelsNeedsAdjustOnY, 0, height);\n    var labelsNeedsHideOverlap = filter(labelList, function (item) {\n      return item.layoutOption.hideOverlap;\n    });\n    hideOverlap(labelsNeedsHideOverlap);\n  };\n  /**\n   * Process all labels. Not only labels with layoutOption.\n   */\n  LabelManager.prototype.processLabelsOverall = function () {\n    var _this = this;\n    each(this._chartViewList, function (chartView) {\n      var seriesModel = chartView.__model;\n      var ignoreLabelLineUpdate = chartView.ignoreLabelLineUpdate;\n      var animationEnabled = seriesModel.isAnimationEnabled();\n      chartView.group.traverse(function (child) {\n        if (child.ignore && !child.forceLabelAnimation) {\n          return true; // Stop traverse descendants.\n        }\n        var needsUpdateLabelLine = !ignoreLabelLineUpdate;\n        var label = child.getTextContent();\n        if (!needsUpdateLabelLine && label) {\n          needsUpdateLabelLine = labelLayoutInnerStore(label).needsUpdateLabelLine;\n        }\n        if (needsUpdateLabelLine) {\n          _this._updateLabelLine(child, seriesModel);\n        }\n        if (animationEnabled) {\n          _this._animateLabels(child, seriesModel);\n        }\n      });\n    });\n  };\n  LabelManager.prototype._updateLabelLine = function (el, seriesModel) {\n    // Only support label being hosted on graphic elements.\n    var textEl = el.getTextContent();\n    // Update label line style.\n    var ecData = getECData(el);\n    var dataIndex = ecData.dataIndex;\n    // Only support labelLine on the labels represent data.\n    if (textEl && dataIndex != null) {\n      var data = seriesModel.getData(ecData.dataType);\n      var itemModel = data.getItemModel(dataIndex);\n      var defaultStyle = {};\n      var visualStyle = data.getItemVisual(dataIndex, 'style');\n      if (visualStyle) {\n        var visualType = data.getVisual('drawType');\n        // Default to be same with main color\n        defaultStyle.stroke = visualStyle[visualType];\n      }\n      var labelLineModel = itemModel.getModel('labelLine');\n      setLabelLineStyle(el, getLabelLineStatesModels(itemModel), defaultStyle);\n      updateLabelLinePoints(el, labelLineModel);\n    }\n  };\n  LabelManager.prototype._animateLabels = function (el, seriesModel) {\n    var textEl = el.getTextContent();\n    var guideLine = el.getTextGuideLine();\n    // Animate\n    if (textEl\n    // `forceLabelAnimation` has the highest priority\n    && (el.forceLabelAnimation || !textEl.ignore && !textEl.invisible && !el.disableLabelAnimation && !isElementRemoved(el))) {\n      var layoutStore = labelLayoutInnerStore(textEl);\n      var oldLayout = layoutStore.oldLayout;\n      var ecData = getECData(el);\n      var dataIndex = ecData.dataIndex;\n      var newProps = {\n        x: textEl.x,\n        y: textEl.y,\n        rotation: textEl.rotation\n      };\n      var data = seriesModel.getData(ecData.dataType);\n      if (!oldLayout) {\n        textEl.attr(newProps);\n        // Disable fade in animation if value animation is enabled.\n        if (!labelInner(textEl).valueAnimation) {\n          var oldOpacity = retrieve2(textEl.style.opacity, 1);\n          // Fade in animation\n          textEl.style.opacity = 0;\n          initProps(textEl, {\n            style: {\n              opacity: oldOpacity\n            }\n          }, seriesModel, dataIndex);\n        }\n      } else {\n        textEl.attr(oldLayout);\n        // Make sure the animation from is in the right status.\n        var prevStates = el.prevStates;\n        if (prevStates) {\n          if (indexOf(prevStates, 'select') >= 0) {\n            textEl.attr(layoutStore.oldLayoutSelect);\n          }\n          if (indexOf(prevStates, 'emphasis') >= 0) {\n            textEl.attr(layoutStore.oldLayoutEmphasis);\n          }\n        }\n        updateProps(textEl, newProps, seriesModel, dataIndex);\n      }\n      layoutStore.oldLayout = newProps;\n      if (textEl.states.select) {\n        var layoutSelect = layoutStore.oldLayoutSelect = {};\n        extendWithKeys(layoutSelect, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutSelect, textEl.states.select, LABEL_LAYOUT_PROPS);\n      }\n      if (textEl.states.emphasis) {\n        var layoutEmphasis = layoutStore.oldLayoutEmphasis = {};\n        extendWithKeys(layoutEmphasis, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutEmphasis, textEl.states.emphasis, LABEL_LAYOUT_PROPS);\n      }\n      animateLabelValue(textEl, dataIndex, data, seriesModel, seriesModel);\n    }\n    if (guideLine && !guideLine.ignore && !guideLine.invisible) {\n      var layoutStore = labelLineAnimationStore(guideLine);\n      var oldLayout = layoutStore.oldLayout;\n      var newLayout = {\n        points: guideLine.shape.points\n      };\n      if (!oldLayout) {\n        guideLine.setShape(newLayout);\n        guideLine.style.strokePercent = 0;\n        initProps(guideLine, {\n          style: {\n            strokePercent: 1\n          }\n        }, seriesModel);\n      } else {\n        guideLine.attr({\n          shape: oldLayout\n        });\n        updateProps(guideLine, {\n          shape: newLayout\n        }, seriesModel);\n      }\n      layoutStore.oldLayout = newLayout;\n    }\n  };\n  return LabelManager;\n}();\nexport default LabelManager;", "map": {"version": 3, "names": ["BoundingRect", "updateProps", "initProps", "isElementRemoved", "getECData", "parsePercent", "Transformable", "updateLabelLinePoints", "setLabelLineStyle", "getLabelLineStatesModels", "makeInner", "retrieve2", "each", "keys", "isFunction", "filter", "indexOf", "prepareLayoutList", "hideOverlap", "shiftLayoutOnX", "shiftLayoutOnY", "labelInner", "animateLabelValue", "normalizeRadian", "cloneArr", "points", "newPoints", "i", "length", "push", "slice", "prepareLayoutCallbackParams", "labelItem", "hostEl", "label", "labelLine", "getTextGuideLine", "dataIndex", "dataType", "seriesIndex", "seriesModel", "text", "style", "rect", "hostRect", "labelRect", "align", "verticalAlign", "labelLinePoints", "shape", "LABEL_OPTION_TO_STYLE_KEYS", "dummyTransformable", "labelLayoutInnerStore", "labelLineAnimationStore", "extendWithKeys", "target", "source", "key", "LABEL_LAYOUT_PROPS", "LabelManager", "_labelList", "_chartViewList", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "_add<PERSON>abel", "layoutOption", "labelStyle", "__host<PERSON><PERSON>get", "textConfig", "labelTransform", "getComputedTransform", "getBoundingRect", "plain", "applyTransform", "setLocalTransform", "x", "y", "rotation", "originX", "originY", "scaleX", "scaleY", "host", "transform", "labelGuide", "computedLayoutOption", "priority", "width", "height", "defaultAttr", "ignore", "labelGuideIgnore", "fontSize", "cursor", "attachedPos", "position", "attachedRot", "addLabelsOfSeries", "chartView", "_this", "__model", "get", "group", "traverse", "child", "textEl", "getTextContent", "ecData", "disableLabelLayout", "updateLayoutConfig", "api", "getWidth", "getHeight", "createDragHandler", "el", "labelLineModel", "defaultLabelAttr", "degreeToRadian", "Math", "PI", "setTextConfig", "local", "rotate", "offset", "dx", "dy", "needsUpdateLabelLine", "setStyle", "guideLine", "setShape", "labelLayoutStore", "k", "draggable", "hostModel", "data", "getData", "getItemModel", "on", "getModel", "off", "layout", "labelList", "labelsNeedsAdjustOnX", "item", "moveOverlap", "labelsNeedsAdjustOnY", "labelsNeedsHideOverlap", "processLabelsOverall", "ignoreLabelLineUpdate", "animationEnabled", "isAnimationEnabled", "forceLabelAnimation", "_updateLabelLine", "_animate<PERSON><PERSON><PERSON>", "itemModel", "defaultStyle", "visualStyle", "getItemVisual", "visualType", "getVisual", "stroke", "invisible", "disableLabelAnimation", "layoutStore", "oldLayout", "newProps", "attr", "valueAnimation", "oldOpacity", "opacity", "prevStates", "oldLayoutSelect", "oldLayoutEmphasis", "states", "select", "layoutSelect", "emphasis", "layoutEmphasis", "newLayout", "strokePercent"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/label/LabelManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// TODO: move labels out of viewport.\nimport { BoundingRect, updateProps, initProps, isElementRemoved } from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { parsePercent } from '../util/number.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport { updateLabelLinePoints, setLabelLineStyle, getLabelLineStatesModels } from './labelGuideHelper.js';\nimport { makeInner } from '../util/model.js';\nimport { retrieve2, each, keys, isFunction, filter, indexOf } from 'zrender/lib/core/util.js';\nimport { prepareLayoutList, hideOverlap, shiftLayoutOnX, shiftLayoutOnY } from './labelLayoutHelper.js';\nimport { labelInner, animateLabelValue } from './labelStyle.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nfunction cloneArr(points) {\n  if (points) {\n    var newPoints = [];\n    for (var i = 0; i < points.length; i++) {\n      newPoints.push(points[i].slice());\n    }\n    return newPoints;\n  }\n}\nfunction prepareLayoutCallbackParams(labelItem, hostEl) {\n  var label = labelItem.label;\n  var labelLine = hostEl && hostEl.getTextGuideLine();\n  return {\n    dataIndex: labelItem.dataIndex,\n    dataType: labelItem.dataType,\n    seriesIndex: labelItem.seriesModel.seriesIndex,\n    text: labelItem.label.style.text,\n    rect: labelItem.hostRect,\n    labelRect: labelItem.rect,\n    // x: labelAttr.x,\n    // y: labelAttr.y,\n    align: label.style.align,\n    verticalAlign: label.style.verticalAlign,\n    labelLinePoints: cloneArr(labelLine && labelLine.shape.points)\n  };\n}\nvar LABEL_OPTION_TO_STYLE_KEYS = ['align', 'verticalAlign', 'width', 'height', 'fontSize'];\nvar dummyTransformable = new Transformable();\nvar labelLayoutInnerStore = makeInner();\nvar labelLineAnimationStore = makeInner();\nfunction extendWithKeys(target, source, keys) {\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (source[key] != null) {\n      target[key] = source[key];\n    }\n  }\n}\nvar LABEL_LAYOUT_PROPS = ['x', 'y', 'rotation'];\nvar LabelManager = /** @class */function () {\n  function LabelManager() {\n    this._labelList = [];\n    this._chartViewList = [];\n  }\n  LabelManager.prototype.clearLabels = function () {\n    this._labelList = [];\n    this._chartViewList = [];\n  };\n  /**\n   * Add label to manager\n   */\n  LabelManager.prototype._addLabel = function (dataIndex, dataType, seriesModel, label, layoutOption) {\n    var labelStyle = label.style;\n    var hostEl = label.__hostTarget;\n    var textConfig = hostEl.textConfig || {};\n    // TODO: If label is in other state.\n    var labelTransform = label.getComputedTransform();\n    var labelRect = label.getBoundingRect().plain();\n    BoundingRect.applyTransform(labelRect, labelRect, labelTransform);\n    if (labelTransform) {\n      dummyTransformable.setLocalTransform(labelTransform);\n    } else {\n      // Identity transform.\n      dummyTransformable.x = dummyTransformable.y = dummyTransformable.rotation = dummyTransformable.originX = dummyTransformable.originY = 0;\n      dummyTransformable.scaleX = dummyTransformable.scaleY = 1;\n    }\n    dummyTransformable.rotation = normalizeRadian(dummyTransformable.rotation);\n    var host = label.__hostTarget;\n    var hostRect;\n    if (host) {\n      hostRect = host.getBoundingRect().plain();\n      var transform = host.getComputedTransform();\n      BoundingRect.applyTransform(hostRect, hostRect, transform);\n    }\n    var labelGuide = hostRect && host.getTextGuideLine();\n    this._labelList.push({\n      label: label,\n      labelLine: labelGuide,\n      seriesModel: seriesModel,\n      dataIndex: dataIndex,\n      dataType: dataType,\n      layoutOption: layoutOption,\n      computedLayoutOption: null,\n      rect: labelRect,\n      hostRect: hostRect,\n      // Label with lower priority will be hidden when overlapped\n      // Use rect size as default priority\n      priority: hostRect ? hostRect.width * hostRect.height : 0,\n      // Save default label attributes.\n      // For restore if developers want get back to default value in callback.\n      defaultAttr: {\n        ignore: label.ignore,\n        labelGuideIgnore: labelGuide && labelGuide.ignore,\n        x: dummyTransformable.x,\n        y: dummyTransformable.y,\n        scaleX: dummyTransformable.scaleX,\n        scaleY: dummyTransformable.scaleY,\n        rotation: dummyTransformable.rotation,\n        style: {\n          x: labelStyle.x,\n          y: labelStyle.y,\n          align: labelStyle.align,\n          verticalAlign: labelStyle.verticalAlign,\n          width: labelStyle.width,\n          height: labelStyle.height,\n          fontSize: labelStyle.fontSize\n        },\n        cursor: label.cursor,\n        attachedPos: textConfig.position,\n        attachedRot: textConfig.rotation\n      }\n    });\n  };\n  LabelManager.prototype.addLabelsOfSeries = function (chartView) {\n    var _this = this;\n    this._chartViewList.push(chartView);\n    var seriesModel = chartView.__model;\n    var layoutOption = seriesModel.get('labelLayout');\n    /**\n     * Ignore layouting if it's not specified anything.\n     */\n    if (!(isFunction(layoutOption) || keys(layoutOption).length)) {\n      return;\n    }\n    chartView.group.traverse(function (child) {\n      if (child.ignore) {\n        return true; // Stop traverse descendants.\n      }\n      // Only support label being hosted on graphic elements.\n      var textEl = child.getTextContent();\n      var ecData = getECData(child);\n      // Can only attach the text on the element with dataIndex\n      if (textEl && !textEl.disableLabelLayout) {\n        _this._addLabel(ecData.dataIndex, ecData.dataType, seriesModel, textEl, layoutOption);\n      }\n    });\n  };\n  LabelManager.prototype.updateLayoutConfig = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    function createDragHandler(el, labelLineModel) {\n      return function () {\n        updateLabelLinePoints(el, labelLineModel);\n      };\n    }\n    for (var i = 0; i < this._labelList.length; i++) {\n      var labelItem = this._labelList[i];\n      var label = labelItem.label;\n      var hostEl = label.__hostTarget;\n      var defaultLabelAttr = labelItem.defaultAttr;\n      var layoutOption = void 0;\n      // TODO A global layout option?\n      if (isFunction(labelItem.layoutOption)) {\n        layoutOption = labelItem.layoutOption(prepareLayoutCallbackParams(labelItem, hostEl));\n      } else {\n        layoutOption = labelItem.layoutOption;\n      }\n      layoutOption = layoutOption || {};\n      labelItem.computedLayoutOption = layoutOption;\n      var degreeToRadian = Math.PI / 180;\n      // TODO hostEl should always exists.\n      // Or label should not have parent because the x, y is all in global space.\n      if (hostEl) {\n        hostEl.setTextConfig({\n          // Force to set local false.\n          local: false,\n          // Ignore position and rotation config on the host el if x or y is changed.\n          position: layoutOption.x != null || layoutOption.y != null ? null : defaultLabelAttr.attachedPos,\n          // Ignore rotation config on the host el if rotation is changed.\n          rotation: layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.attachedRot,\n          offset: [layoutOption.dx || 0, layoutOption.dy || 0]\n        });\n      }\n      var needsUpdateLabelLine = false;\n      if (layoutOption.x != null) {\n        // TODO width of chart view.\n        label.x = parsePercent(layoutOption.x, width);\n        label.setStyle('x', 0); // Ignore movement in style. TODO: origin.\n        needsUpdateLabelLine = true;\n      } else {\n        label.x = defaultLabelAttr.x;\n        label.setStyle('x', defaultLabelAttr.style.x);\n      }\n      if (layoutOption.y != null) {\n        // TODO height of chart view.\n        label.y = parsePercent(layoutOption.y, height);\n        label.setStyle('y', 0); // Ignore movement in style.\n        needsUpdateLabelLine = true;\n      } else {\n        label.y = defaultLabelAttr.y;\n        label.setStyle('y', defaultLabelAttr.style.y);\n      }\n      if (layoutOption.labelLinePoints) {\n        var guideLine = hostEl.getTextGuideLine();\n        if (guideLine) {\n          guideLine.setShape({\n            points: layoutOption.labelLinePoints\n          });\n          // Not update\n          needsUpdateLabelLine = false;\n        }\n      }\n      var labelLayoutStore = labelLayoutInnerStore(label);\n      labelLayoutStore.needsUpdateLabelLine = needsUpdateLabelLine;\n      label.rotation = layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.rotation;\n      label.scaleX = defaultLabelAttr.scaleX;\n      label.scaleY = defaultLabelAttr.scaleY;\n      for (var k = 0; k < LABEL_OPTION_TO_STYLE_KEYS.length; k++) {\n        var key = LABEL_OPTION_TO_STYLE_KEYS[k];\n        label.setStyle(key, layoutOption[key] != null ? layoutOption[key] : defaultLabelAttr.style[key]);\n      }\n      if (layoutOption.draggable) {\n        label.draggable = true;\n        label.cursor = 'move';\n        if (hostEl) {\n          var hostModel = labelItem.seriesModel;\n          if (labelItem.dataIndex != null) {\n            var data = labelItem.seriesModel.getData(labelItem.dataType);\n            hostModel = data.getItemModel(labelItem.dataIndex);\n          }\n          label.on('drag', createDragHandler(hostEl, hostModel.getModel('labelLine')));\n        }\n      } else {\n        // TODO Other drag functions?\n        label.off('drag');\n        label.cursor = defaultLabelAttr.cursor;\n      }\n    }\n  };\n  LabelManager.prototype.layout = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    var labelList = prepareLayoutList(this._labelList);\n    var labelsNeedsAdjustOnX = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftX';\n    });\n    var labelsNeedsAdjustOnY = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftY';\n    });\n    shiftLayoutOnX(labelsNeedsAdjustOnX, 0, width);\n    shiftLayoutOnY(labelsNeedsAdjustOnY, 0, height);\n    var labelsNeedsHideOverlap = filter(labelList, function (item) {\n      return item.layoutOption.hideOverlap;\n    });\n    hideOverlap(labelsNeedsHideOverlap);\n  };\n  /**\n   * Process all labels. Not only labels with layoutOption.\n   */\n  LabelManager.prototype.processLabelsOverall = function () {\n    var _this = this;\n    each(this._chartViewList, function (chartView) {\n      var seriesModel = chartView.__model;\n      var ignoreLabelLineUpdate = chartView.ignoreLabelLineUpdate;\n      var animationEnabled = seriesModel.isAnimationEnabled();\n      chartView.group.traverse(function (child) {\n        if (child.ignore && !child.forceLabelAnimation) {\n          return true; // Stop traverse descendants.\n        }\n\n        var needsUpdateLabelLine = !ignoreLabelLineUpdate;\n        var label = child.getTextContent();\n        if (!needsUpdateLabelLine && label) {\n          needsUpdateLabelLine = labelLayoutInnerStore(label).needsUpdateLabelLine;\n        }\n        if (needsUpdateLabelLine) {\n          _this._updateLabelLine(child, seriesModel);\n        }\n        if (animationEnabled) {\n          _this._animateLabels(child, seriesModel);\n        }\n      });\n    });\n  };\n  LabelManager.prototype._updateLabelLine = function (el, seriesModel) {\n    // Only support label being hosted on graphic elements.\n    var textEl = el.getTextContent();\n    // Update label line style.\n    var ecData = getECData(el);\n    var dataIndex = ecData.dataIndex;\n    // Only support labelLine on the labels represent data.\n    if (textEl && dataIndex != null) {\n      var data = seriesModel.getData(ecData.dataType);\n      var itemModel = data.getItemModel(dataIndex);\n      var defaultStyle = {};\n      var visualStyle = data.getItemVisual(dataIndex, 'style');\n      if (visualStyle) {\n        var visualType = data.getVisual('drawType');\n        // Default to be same with main color\n        defaultStyle.stroke = visualStyle[visualType];\n      }\n      var labelLineModel = itemModel.getModel('labelLine');\n      setLabelLineStyle(el, getLabelLineStatesModels(itemModel), defaultStyle);\n      updateLabelLinePoints(el, labelLineModel);\n    }\n  };\n  LabelManager.prototype._animateLabels = function (el, seriesModel) {\n    var textEl = el.getTextContent();\n    var guideLine = el.getTextGuideLine();\n    // Animate\n    if (textEl\n    // `forceLabelAnimation` has the highest priority\n    && (el.forceLabelAnimation || !textEl.ignore && !textEl.invisible && !el.disableLabelAnimation && !isElementRemoved(el))) {\n      var layoutStore = labelLayoutInnerStore(textEl);\n      var oldLayout = layoutStore.oldLayout;\n      var ecData = getECData(el);\n      var dataIndex = ecData.dataIndex;\n      var newProps = {\n        x: textEl.x,\n        y: textEl.y,\n        rotation: textEl.rotation\n      };\n      var data = seriesModel.getData(ecData.dataType);\n      if (!oldLayout) {\n        textEl.attr(newProps);\n        // Disable fade in animation if value animation is enabled.\n        if (!labelInner(textEl).valueAnimation) {\n          var oldOpacity = retrieve2(textEl.style.opacity, 1);\n          // Fade in animation\n          textEl.style.opacity = 0;\n          initProps(textEl, {\n            style: {\n              opacity: oldOpacity\n            }\n          }, seriesModel, dataIndex);\n        }\n      } else {\n        textEl.attr(oldLayout);\n        // Make sure the animation from is in the right status.\n        var prevStates = el.prevStates;\n        if (prevStates) {\n          if (indexOf(prevStates, 'select') >= 0) {\n            textEl.attr(layoutStore.oldLayoutSelect);\n          }\n          if (indexOf(prevStates, 'emphasis') >= 0) {\n            textEl.attr(layoutStore.oldLayoutEmphasis);\n          }\n        }\n        updateProps(textEl, newProps, seriesModel, dataIndex);\n      }\n      layoutStore.oldLayout = newProps;\n      if (textEl.states.select) {\n        var layoutSelect = layoutStore.oldLayoutSelect = {};\n        extendWithKeys(layoutSelect, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutSelect, textEl.states.select, LABEL_LAYOUT_PROPS);\n      }\n      if (textEl.states.emphasis) {\n        var layoutEmphasis = layoutStore.oldLayoutEmphasis = {};\n        extendWithKeys(layoutEmphasis, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutEmphasis, textEl.states.emphasis, LABEL_LAYOUT_PROPS);\n      }\n      animateLabelValue(textEl, dataIndex, data, seriesModel, seriesModel);\n    }\n    if (guideLine && !guideLine.ignore && !guideLine.invisible) {\n      var layoutStore = labelLineAnimationStore(guideLine);\n      var oldLayout = layoutStore.oldLayout;\n      var newLayout = {\n        points: guideLine.shape.points\n      };\n      if (!oldLayout) {\n        guideLine.setShape(newLayout);\n        guideLine.style.strokePercent = 0;\n        initProps(guideLine, {\n          style: {\n            strokePercent: 1\n          }\n        }, seriesModel);\n      } else {\n        guideLine.attr({\n          shape: oldLayout\n        });\n        updateProps(guideLine, {\n          shape: newLayout\n        }, seriesModel);\n      }\n      layoutStore.oldLayout = newLayout;\n    }\n  };\n  return LabelManager;\n}();\nexport default LabelManager;"], "mappings": ";;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC3F,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,qBAAqB,EAAEC,iBAAiB,EAAEC,wBAAwB,QAAQ,uBAAuB;AAC1G,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAEC,OAAO,QAAQ,0BAA0B;AAC7F,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,cAAc,EAAEC,cAAc,QAAQ,wBAAwB;AACvG,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,iBAAiB;AAC/D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxB,IAAIA,MAAM,EAAE;IACV,IAAIC,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCD,SAAS,CAACG,IAAI,CAACJ,MAAM,CAACE,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;IACnC;IACA,OAAOJ,SAAS;EAClB;AACF;AACA,SAASK,2BAA2BA,CAACC,SAAS,EAAEC,MAAM,EAAE;EACtD,IAAIC,KAAK,GAAGF,SAAS,CAACE,KAAK;EAC3B,IAAIC,SAAS,GAAGF,MAAM,IAAIA,MAAM,CAACG,gBAAgB,CAAC,CAAC;EACnD,OAAO;IACLC,SAAS,EAAEL,SAAS,CAACK,SAAS;IAC9BC,QAAQ,EAAEN,SAAS,CAACM,QAAQ;IAC5BC,WAAW,EAAEP,SAAS,CAACQ,WAAW,CAACD,WAAW;IAC9CE,IAAI,EAAET,SAAS,CAACE,KAAK,CAACQ,KAAK,CAACD,IAAI;IAChCE,IAAI,EAAEX,SAAS,CAACY,QAAQ;IACxBC,SAAS,EAAEb,SAAS,CAACW,IAAI;IACzB;IACA;IACAG,KAAK,EAAEZ,KAAK,CAACQ,KAAK,CAACI,KAAK;IACxBC,aAAa,EAAEb,KAAK,CAACQ,KAAK,CAACK,aAAa;IACxCC,eAAe,EAAExB,QAAQ,CAACW,SAAS,IAAIA,SAAS,CAACc,KAAK,CAACxB,MAAM;EAC/D,CAAC;AACH;AACA,IAAIyB,0BAA0B,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC;AAC1F,IAAIC,kBAAkB,GAAG,IAAI7C,aAAa,CAAC,CAAC;AAC5C,IAAI8C,qBAAqB,GAAG1C,SAAS,CAAC,CAAC;AACvC,IAAI2C,uBAAuB,GAAG3C,SAAS,CAAC,CAAC;AACzC,SAAS4C,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAE3C,IAAI,EAAE;EAC5C,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,IAAI,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAI8B,GAAG,GAAG5C,IAAI,CAACc,CAAC,CAAC;IACjB,IAAI6B,MAAM,CAACC,GAAG,CAAC,IAAI,IAAI,EAAE;MACvBF,MAAM,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAC3B;EACF;AACF;AACA,IAAIC,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC;AAC/C,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EACAF,YAAY,CAACG,SAAS,CAACC,WAAW,GAAG,YAAY;IAC/C,IAAI,CAACH,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B,CAAC;EACD;AACF;AACA;EACEF,YAAY,CAACG,SAAS,CAACE,SAAS,GAAG,UAAU3B,SAAS,EAAEC,QAAQ,EAAEE,WAAW,EAAEN,KAAK,EAAE+B,YAAY,EAAE;IAClG,IAAIC,UAAU,GAAGhC,KAAK,CAACQ,KAAK;IAC5B,IAAIT,MAAM,GAAGC,KAAK,CAACiC,YAAY;IAC/B,IAAIC,UAAU,GAAGnC,MAAM,CAACmC,UAAU,IAAI,CAAC,CAAC;IACxC;IACA,IAAIC,cAAc,GAAGnC,KAAK,CAACoC,oBAAoB,CAAC,CAAC;IACjD,IAAIzB,SAAS,GAAGX,KAAK,CAACqC,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IAC/CxE,YAAY,CAACyE,cAAc,CAAC5B,SAAS,EAAEA,SAAS,EAAEwB,cAAc,CAAC;IACjE,IAAIA,cAAc,EAAE;MAClBlB,kBAAkB,CAACuB,iBAAiB,CAACL,cAAc,CAAC;IACtD,CAAC,MAAM;MACL;MACAlB,kBAAkB,CAACwB,CAAC,GAAGxB,kBAAkB,CAACyB,CAAC,GAAGzB,kBAAkB,CAAC0B,QAAQ,GAAG1B,kBAAkB,CAAC2B,OAAO,GAAG3B,kBAAkB,CAAC4B,OAAO,GAAG,CAAC;MACvI5B,kBAAkB,CAAC6B,MAAM,GAAG7B,kBAAkB,CAAC8B,MAAM,GAAG,CAAC;IAC3D;IACA9B,kBAAkB,CAAC0B,QAAQ,GAAGtD,eAAe,CAAC4B,kBAAkB,CAAC0B,QAAQ,CAAC;IAC1E,IAAIK,IAAI,GAAGhD,KAAK,CAACiC,YAAY;IAC7B,IAAIvB,QAAQ;IACZ,IAAIsC,IAAI,EAAE;MACRtC,QAAQ,GAAGsC,IAAI,CAACX,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACzC,IAAIW,SAAS,GAAGD,IAAI,CAACZ,oBAAoB,CAAC,CAAC;MAC3CtE,YAAY,CAACyE,cAAc,CAAC7B,QAAQ,EAAEA,QAAQ,EAAEuC,SAAS,CAAC;IAC5D;IACA,IAAIC,UAAU,GAAGxC,QAAQ,IAAIsC,IAAI,CAAC9C,gBAAgB,CAAC,CAAC;IACpD,IAAI,CAACwB,UAAU,CAAC/B,IAAI,CAAC;MACnBK,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEiD,UAAU;MACrB5C,WAAW,EAAEA,WAAW;MACxBH,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAEA,QAAQ;MAClB2B,YAAY,EAAEA,YAAY;MAC1BoB,oBAAoB,EAAE,IAAI;MAC1B1C,IAAI,EAAEE,SAAS;MACfD,QAAQ,EAAEA,QAAQ;MAClB;MACA;MACA0C,QAAQ,EAAE1C,QAAQ,GAAGA,QAAQ,CAAC2C,KAAK,GAAG3C,QAAQ,CAAC4C,MAAM,GAAG,CAAC;MACzD;MACA;MACAC,WAAW,EAAE;QACXC,MAAM,EAAExD,KAAK,CAACwD,MAAM;QACpBC,gBAAgB,EAAEP,UAAU,IAAIA,UAAU,CAACM,MAAM;QACjDf,CAAC,EAAExB,kBAAkB,CAACwB,CAAC;QACvBC,CAAC,EAAEzB,kBAAkB,CAACyB,CAAC;QACvBI,MAAM,EAAE7B,kBAAkB,CAAC6B,MAAM;QACjCC,MAAM,EAAE9B,kBAAkB,CAAC8B,MAAM;QACjCJ,QAAQ,EAAE1B,kBAAkB,CAAC0B,QAAQ;QACrCnC,KAAK,EAAE;UACLiC,CAAC,EAAET,UAAU,CAACS,CAAC;UACfC,CAAC,EAAEV,UAAU,CAACU,CAAC;UACf9B,KAAK,EAAEoB,UAAU,CAACpB,KAAK;UACvBC,aAAa,EAAEmB,UAAU,CAACnB,aAAa;UACvCwC,KAAK,EAAErB,UAAU,CAACqB,KAAK;UACvBC,MAAM,EAAEtB,UAAU,CAACsB,MAAM;UACzBI,QAAQ,EAAE1B,UAAU,CAAC0B;QACvB,CAAC;QACDC,MAAM,EAAE3D,KAAK,CAAC2D,MAAM;QACpBC,WAAW,EAAE1B,UAAU,CAAC2B,QAAQ;QAChCC,WAAW,EAAE5B,UAAU,CAACS;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EACDlB,YAAY,CAACG,SAAS,CAACmC,iBAAiB,GAAG,UAAUC,SAAS,EAAE;IAC9D,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACtC,cAAc,CAAChC,IAAI,CAACqE,SAAS,CAAC;IACnC,IAAI1D,WAAW,GAAG0D,SAAS,CAACE,OAAO;IACnC,IAAInC,YAAY,GAAGzB,WAAW,CAAC6D,GAAG,CAAC,aAAa,CAAC;IACjD;AACJ;AACA;IACI,IAAI,EAAEvF,UAAU,CAACmD,YAAY,CAAC,IAAIpD,IAAI,CAACoD,YAAY,CAAC,CAACrC,MAAM,CAAC,EAAE;MAC5D;IACF;IACAsE,SAAS,CAACI,KAAK,CAACC,QAAQ,CAAC,UAAUC,KAAK,EAAE;MACxC,IAAIA,KAAK,CAACd,MAAM,EAAE;QAChB,OAAO,IAAI,CAAC,CAAC;MACf;MACA;MACA,IAAIe,MAAM,GAAGD,KAAK,CAACE,cAAc,CAAC,CAAC;MACnC,IAAIC,MAAM,GAAGvG,SAAS,CAACoG,KAAK,CAAC;MAC7B;MACA,IAAIC,MAAM,IAAI,CAACA,MAAM,CAACG,kBAAkB,EAAE;QACxCT,KAAK,CAACnC,SAAS,CAAC2C,MAAM,CAACtE,SAAS,EAAEsE,MAAM,CAACrE,QAAQ,EAAEE,WAAW,EAAEiE,MAAM,EAAExC,YAAY,CAAC;MACvF;IACF,CAAC,CAAC;EACJ,CAAC;EACDN,YAAY,CAACG,SAAS,CAAC+C,kBAAkB,GAAG,UAAUC,GAAG,EAAE;IACzD,IAAIvB,KAAK,GAAGuB,GAAG,CAACC,QAAQ,CAAC,CAAC;IAC1B,IAAIvB,MAAM,GAAGsB,GAAG,CAACE,SAAS,CAAC,CAAC;IAC5B,SAASC,iBAAiBA,CAACC,EAAE,EAAEC,cAAc,EAAE;MAC7C,OAAO,YAAY;QACjB5G,qBAAqB,CAAC2G,EAAE,EAAEC,cAAc,CAAC;MAC3C,CAAC;IACH;IACA,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACiC,UAAU,CAAChC,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,IAAIK,SAAS,GAAG,IAAI,CAAC4B,UAAU,CAACjC,CAAC,CAAC;MAClC,IAAIO,KAAK,GAAGF,SAAS,CAACE,KAAK;MAC3B,IAAID,MAAM,GAAGC,KAAK,CAACiC,YAAY;MAC/B,IAAIiD,gBAAgB,GAAGpF,SAAS,CAACyD,WAAW;MAC5C,IAAIxB,YAAY,GAAG,KAAK,CAAC;MACzB;MACA,IAAInD,UAAU,CAACkB,SAAS,CAACiC,YAAY,CAAC,EAAE;QACtCA,YAAY,GAAGjC,SAAS,CAACiC,YAAY,CAAClC,2BAA2B,CAACC,SAAS,EAAEC,MAAM,CAAC,CAAC;MACvF,CAAC,MAAM;QACLgC,YAAY,GAAGjC,SAAS,CAACiC,YAAY;MACvC;MACAA,YAAY,GAAGA,YAAY,IAAI,CAAC,CAAC;MACjCjC,SAAS,CAACqD,oBAAoB,GAAGpB,YAAY;MAC7C,IAAIoD,cAAc,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MAClC;MACA;MACA,IAAItF,MAAM,EAAE;QACVA,MAAM,CAACuF,aAAa,CAAC;UACnB;UACAC,KAAK,EAAE,KAAK;UACZ;UACA1B,QAAQ,EAAE9B,YAAY,CAACU,CAAC,IAAI,IAAI,IAAIV,YAAY,CAACW,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGwC,gBAAgB,CAACtB,WAAW;UAChG;UACAjB,QAAQ,EAAEZ,YAAY,CAACyD,MAAM,IAAI,IAAI,GAAGzD,YAAY,CAACyD,MAAM,GAAGL,cAAc,GAAGD,gBAAgB,CAACpB,WAAW;UAC3G2B,MAAM,EAAE,CAAC1D,YAAY,CAAC2D,EAAE,IAAI,CAAC,EAAE3D,YAAY,CAAC4D,EAAE,IAAI,CAAC;QACrD,CAAC,CAAC;MACJ;MACA,IAAIC,oBAAoB,GAAG,KAAK;MAChC,IAAI7D,YAAY,CAACU,CAAC,IAAI,IAAI,EAAE;QAC1B;QACAzC,KAAK,CAACyC,CAAC,GAAGtE,YAAY,CAAC4D,YAAY,CAACU,CAAC,EAAEY,KAAK,CAAC;QAC7CrD,KAAK,CAAC6F,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACxBD,oBAAoB,GAAG,IAAI;MAC7B,CAAC,MAAM;QACL5F,KAAK,CAACyC,CAAC,GAAGyC,gBAAgB,CAACzC,CAAC;QAC5BzC,KAAK,CAAC6F,QAAQ,CAAC,GAAG,EAAEX,gBAAgB,CAAC1E,KAAK,CAACiC,CAAC,CAAC;MAC/C;MACA,IAAIV,YAAY,CAACW,CAAC,IAAI,IAAI,EAAE;QAC1B;QACA1C,KAAK,CAAC0C,CAAC,GAAGvE,YAAY,CAAC4D,YAAY,CAACW,CAAC,EAAEY,MAAM,CAAC;QAC9CtD,KAAK,CAAC6F,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACxBD,oBAAoB,GAAG,IAAI;MAC7B,CAAC,MAAM;QACL5F,KAAK,CAAC0C,CAAC,GAAGwC,gBAAgB,CAACxC,CAAC;QAC5B1C,KAAK,CAAC6F,QAAQ,CAAC,GAAG,EAAEX,gBAAgB,CAAC1E,KAAK,CAACkC,CAAC,CAAC;MAC/C;MACA,IAAIX,YAAY,CAACjB,eAAe,EAAE;QAChC,IAAIgF,SAAS,GAAG/F,MAAM,CAACG,gBAAgB,CAAC,CAAC;QACzC,IAAI4F,SAAS,EAAE;UACbA,SAAS,CAACC,QAAQ,CAAC;YACjBxG,MAAM,EAAEwC,YAAY,CAACjB;UACvB,CAAC,CAAC;UACF;UACA8E,oBAAoB,GAAG,KAAK;QAC9B;MACF;MACA,IAAII,gBAAgB,GAAG9E,qBAAqB,CAAClB,KAAK,CAAC;MACnDgG,gBAAgB,CAACJ,oBAAoB,GAAGA,oBAAoB;MAC5D5F,KAAK,CAAC2C,QAAQ,GAAGZ,YAAY,CAACyD,MAAM,IAAI,IAAI,GAAGzD,YAAY,CAACyD,MAAM,GAAGL,cAAc,GAAGD,gBAAgB,CAACvC,QAAQ;MAC/G3C,KAAK,CAAC8C,MAAM,GAAGoC,gBAAgB,CAACpC,MAAM;MACtC9C,KAAK,CAAC+C,MAAM,GAAGmC,gBAAgB,CAACnC,MAAM;MACtC,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjF,0BAA0B,CAACtB,MAAM,EAAEuG,CAAC,EAAE,EAAE;QAC1D,IAAI1E,GAAG,GAAGP,0BAA0B,CAACiF,CAAC,CAAC;QACvCjG,KAAK,CAAC6F,QAAQ,CAACtE,GAAG,EAAEQ,YAAY,CAACR,GAAG,CAAC,IAAI,IAAI,GAAGQ,YAAY,CAACR,GAAG,CAAC,GAAG2D,gBAAgB,CAAC1E,KAAK,CAACe,GAAG,CAAC,CAAC;MAClG;MACA,IAAIQ,YAAY,CAACmE,SAAS,EAAE;QAC1BlG,KAAK,CAACkG,SAAS,GAAG,IAAI;QACtBlG,KAAK,CAAC2D,MAAM,GAAG,MAAM;QACrB,IAAI5D,MAAM,EAAE;UACV,IAAIoG,SAAS,GAAGrG,SAAS,CAACQ,WAAW;UACrC,IAAIR,SAAS,CAACK,SAAS,IAAI,IAAI,EAAE;YAC/B,IAAIiG,IAAI,GAAGtG,SAAS,CAACQ,WAAW,CAAC+F,OAAO,CAACvG,SAAS,CAACM,QAAQ,CAAC;YAC5D+F,SAAS,GAAGC,IAAI,CAACE,YAAY,CAACxG,SAAS,CAACK,SAAS,CAAC;UACpD;UACAH,KAAK,CAACuG,EAAE,CAAC,MAAM,EAAExB,iBAAiB,CAAChF,MAAM,EAAEoG,SAAS,CAACK,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9E;MACF,CAAC,MAAM;QACL;QACAxG,KAAK,CAACyG,GAAG,CAAC,MAAM,CAAC;QACjBzG,KAAK,CAAC2D,MAAM,GAAGuB,gBAAgB,CAACvB,MAAM;MACxC;IACF;EACF,CAAC;EACDlC,YAAY,CAACG,SAAS,CAAC8E,MAAM,GAAG,UAAU9B,GAAG,EAAE;IAC7C,IAAIvB,KAAK,GAAGuB,GAAG,CAACC,QAAQ,CAAC,CAAC;IAC1B,IAAIvB,MAAM,GAAGsB,GAAG,CAACE,SAAS,CAAC,CAAC;IAC5B,IAAI6B,SAAS,GAAG5H,iBAAiB,CAAC,IAAI,CAAC2C,UAAU,CAAC;IAClD,IAAIkF,oBAAoB,GAAG/H,MAAM,CAAC8H,SAAS,EAAE,UAAUE,IAAI,EAAE;MAC3D,OAAOA,IAAI,CAAC9E,YAAY,CAAC+E,WAAW,KAAK,QAAQ;IACnD,CAAC,CAAC;IACF,IAAIC,oBAAoB,GAAGlI,MAAM,CAAC8H,SAAS,EAAE,UAAUE,IAAI,EAAE;MAC3D,OAAOA,IAAI,CAAC9E,YAAY,CAAC+E,WAAW,KAAK,QAAQ;IACnD,CAAC,CAAC;IACF7H,cAAc,CAAC2H,oBAAoB,EAAE,CAAC,EAAEvD,KAAK,CAAC;IAC9CnE,cAAc,CAAC6H,oBAAoB,EAAE,CAAC,EAAEzD,MAAM,CAAC;IAC/C,IAAI0D,sBAAsB,GAAGnI,MAAM,CAAC8H,SAAS,EAAE,UAAUE,IAAI,EAAE;MAC7D,OAAOA,IAAI,CAAC9E,YAAY,CAAC/C,WAAW;IACtC,CAAC,CAAC;IACFA,WAAW,CAACgI,sBAAsB,CAAC;EACrC,CAAC;EACD;AACF;AACA;EACEvF,YAAY,CAACG,SAAS,CAACqF,oBAAoB,GAAG,YAAY;IACxD,IAAIhD,KAAK,GAAG,IAAI;IAChBvF,IAAI,CAAC,IAAI,CAACiD,cAAc,EAAE,UAAUqC,SAAS,EAAE;MAC7C,IAAI1D,WAAW,GAAG0D,SAAS,CAACE,OAAO;MACnC,IAAIgD,qBAAqB,GAAGlD,SAAS,CAACkD,qBAAqB;MAC3D,IAAIC,gBAAgB,GAAG7G,WAAW,CAAC8G,kBAAkB,CAAC,CAAC;MACvDpD,SAAS,CAACI,KAAK,CAACC,QAAQ,CAAC,UAAUC,KAAK,EAAE;QACxC,IAAIA,KAAK,CAACd,MAAM,IAAI,CAACc,KAAK,CAAC+C,mBAAmB,EAAE;UAC9C,OAAO,IAAI,CAAC,CAAC;QACf;QAEA,IAAIzB,oBAAoB,GAAG,CAACsB,qBAAqB;QACjD,IAAIlH,KAAK,GAAGsE,KAAK,CAACE,cAAc,CAAC,CAAC;QAClC,IAAI,CAACoB,oBAAoB,IAAI5F,KAAK,EAAE;UAClC4F,oBAAoB,GAAG1E,qBAAqB,CAAClB,KAAK,CAAC,CAAC4F,oBAAoB;QAC1E;QACA,IAAIA,oBAAoB,EAAE;UACxB3B,KAAK,CAACqD,gBAAgB,CAAChD,KAAK,EAAEhE,WAAW,CAAC;QAC5C;QACA,IAAI6G,gBAAgB,EAAE;UACpBlD,KAAK,CAACsD,cAAc,CAACjD,KAAK,EAAEhE,WAAW,CAAC;QAC1C;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDmB,YAAY,CAACG,SAAS,CAAC0F,gBAAgB,GAAG,UAAUtC,EAAE,EAAE1E,WAAW,EAAE;IACnE;IACA,IAAIiE,MAAM,GAAGS,EAAE,CAACR,cAAc,CAAC,CAAC;IAChC;IACA,IAAIC,MAAM,GAAGvG,SAAS,CAAC8G,EAAE,CAAC;IAC1B,IAAI7E,SAAS,GAAGsE,MAAM,CAACtE,SAAS;IAChC;IACA,IAAIoE,MAAM,IAAIpE,SAAS,IAAI,IAAI,EAAE;MAC/B,IAAIiG,IAAI,GAAG9F,WAAW,CAAC+F,OAAO,CAAC5B,MAAM,CAACrE,QAAQ,CAAC;MAC/C,IAAIoH,SAAS,GAAGpB,IAAI,CAACE,YAAY,CAACnG,SAAS,CAAC;MAC5C,IAAIsH,YAAY,GAAG,CAAC,CAAC;MACrB,IAAIC,WAAW,GAAGtB,IAAI,CAACuB,aAAa,CAACxH,SAAS,EAAE,OAAO,CAAC;MACxD,IAAIuH,WAAW,EAAE;QACf,IAAIE,UAAU,GAAGxB,IAAI,CAACyB,SAAS,CAAC,UAAU,CAAC;QAC3C;QACAJ,YAAY,CAACK,MAAM,GAAGJ,WAAW,CAACE,UAAU,CAAC;MAC/C;MACA,IAAI3C,cAAc,GAAGuC,SAAS,CAAChB,QAAQ,CAAC,WAAW,CAAC;MACpDlI,iBAAiB,CAAC0G,EAAE,EAAEzG,wBAAwB,CAACiJ,SAAS,CAAC,EAAEC,YAAY,CAAC;MACxEpJ,qBAAqB,CAAC2G,EAAE,EAAEC,cAAc,CAAC;IAC3C;EACF,CAAC;EACDxD,YAAY,CAACG,SAAS,CAAC2F,cAAc,GAAG,UAAUvC,EAAE,EAAE1E,WAAW,EAAE;IACjE,IAAIiE,MAAM,GAAGS,EAAE,CAACR,cAAc,CAAC,CAAC;IAChC,IAAIsB,SAAS,GAAGd,EAAE,CAAC9E,gBAAgB,CAAC,CAAC;IACrC;IACA,IAAIqE;IACJ;IAAA,IACIS,EAAE,CAACqC,mBAAmB,IAAI,CAAC9C,MAAM,CAACf,MAAM,IAAI,CAACe,MAAM,CAACwD,SAAS,IAAI,CAAC/C,EAAE,CAACgD,qBAAqB,IAAI,CAAC/J,gBAAgB,CAAC+G,EAAE,CAAC,CAAC,EAAE;MACxH,IAAIiD,WAAW,GAAG/G,qBAAqB,CAACqD,MAAM,CAAC;MAC/C,IAAI2D,SAAS,GAAGD,WAAW,CAACC,SAAS;MACrC,IAAIzD,MAAM,GAAGvG,SAAS,CAAC8G,EAAE,CAAC;MAC1B,IAAI7E,SAAS,GAAGsE,MAAM,CAACtE,SAAS;MAChC,IAAIgI,QAAQ,GAAG;QACb1F,CAAC,EAAE8B,MAAM,CAAC9B,CAAC;QACXC,CAAC,EAAE6B,MAAM,CAAC7B,CAAC;QACXC,QAAQ,EAAE4B,MAAM,CAAC5B;MACnB,CAAC;MACD,IAAIyD,IAAI,GAAG9F,WAAW,CAAC+F,OAAO,CAAC5B,MAAM,CAACrE,QAAQ,CAAC;MAC/C,IAAI,CAAC8H,SAAS,EAAE;QACd3D,MAAM,CAAC6D,IAAI,CAACD,QAAQ,CAAC;QACrB;QACA,IAAI,CAAChJ,UAAU,CAACoF,MAAM,CAAC,CAAC8D,cAAc,EAAE;UACtC,IAAIC,UAAU,GAAG7J,SAAS,CAAC8F,MAAM,CAAC/D,KAAK,CAAC+H,OAAO,EAAE,CAAC,CAAC;UACnD;UACAhE,MAAM,CAAC/D,KAAK,CAAC+H,OAAO,GAAG,CAAC;UACxBvK,SAAS,CAACuG,MAAM,EAAE;YAChB/D,KAAK,EAAE;cACL+H,OAAO,EAAED;YACX;UACF,CAAC,EAAEhI,WAAW,EAAEH,SAAS,CAAC;QAC5B;MACF,CAAC,MAAM;QACLoE,MAAM,CAAC6D,IAAI,CAACF,SAAS,CAAC;QACtB;QACA,IAAIM,UAAU,GAAGxD,EAAE,CAACwD,UAAU;QAC9B,IAAIA,UAAU,EAAE;UACd,IAAI1J,OAAO,CAAC0J,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE;YACtCjE,MAAM,CAAC6D,IAAI,CAACH,WAAW,CAACQ,eAAe,CAAC;UAC1C;UACA,IAAI3J,OAAO,CAAC0J,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE;YACxCjE,MAAM,CAAC6D,IAAI,CAACH,WAAW,CAACS,iBAAiB,CAAC;UAC5C;QACF;QACA3K,WAAW,CAACwG,MAAM,EAAE4D,QAAQ,EAAE7H,WAAW,EAAEH,SAAS,CAAC;MACvD;MACA8H,WAAW,CAACC,SAAS,GAAGC,QAAQ;MAChC,IAAI5D,MAAM,CAACoE,MAAM,CAACC,MAAM,EAAE;QACxB,IAAIC,YAAY,GAAGZ,WAAW,CAACQ,eAAe,GAAG,CAAC,CAAC;QACnDrH,cAAc,CAACyH,YAAY,EAAEV,QAAQ,EAAE3G,kBAAkB,CAAC;QAC1DJ,cAAc,CAACyH,YAAY,EAAEtE,MAAM,CAACoE,MAAM,CAACC,MAAM,EAAEpH,kBAAkB,CAAC;MACxE;MACA,IAAI+C,MAAM,CAACoE,MAAM,CAACG,QAAQ,EAAE;QAC1B,IAAIC,cAAc,GAAGd,WAAW,CAACS,iBAAiB,GAAG,CAAC,CAAC;QACvDtH,cAAc,CAAC2H,cAAc,EAAEZ,QAAQ,EAAE3G,kBAAkB,CAAC;QAC5DJ,cAAc,CAAC2H,cAAc,EAAExE,MAAM,CAACoE,MAAM,CAACG,QAAQ,EAAEtH,kBAAkB,CAAC;MAC5E;MACApC,iBAAiB,CAACmF,MAAM,EAAEpE,SAAS,EAAEiG,IAAI,EAAE9F,WAAW,EAAEA,WAAW,CAAC;IACtE;IACA,IAAIwF,SAAS,IAAI,CAACA,SAAS,CAACtC,MAAM,IAAI,CAACsC,SAAS,CAACiC,SAAS,EAAE;MAC1D,IAAIE,WAAW,GAAG9G,uBAAuB,CAAC2E,SAAS,CAAC;MACpD,IAAIoC,SAAS,GAAGD,WAAW,CAACC,SAAS;MACrC,IAAIc,SAAS,GAAG;QACdzJ,MAAM,EAAEuG,SAAS,CAAC/E,KAAK,CAACxB;MAC1B,CAAC;MACD,IAAI,CAAC2I,SAAS,EAAE;QACdpC,SAAS,CAACC,QAAQ,CAACiD,SAAS,CAAC;QAC7BlD,SAAS,CAACtF,KAAK,CAACyI,aAAa,GAAG,CAAC;QACjCjL,SAAS,CAAC8H,SAAS,EAAE;UACnBtF,KAAK,EAAE;YACLyI,aAAa,EAAE;UACjB;QACF,CAAC,EAAE3I,WAAW,CAAC;MACjB,CAAC,MAAM;QACLwF,SAAS,CAACsC,IAAI,CAAC;UACbrH,KAAK,EAAEmH;QACT,CAAC,CAAC;QACFnK,WAAW,CAAC+H,SAAS,EAAE;UACrB/E,KAAK,EAAEiI;QACT,CAAC,EAAE1I,WAAW,CAAC;MACjB;MACA2H,WAAW,CAACC,SAAS,GAAGc,SAAS;IACnC;EACF,CAAC;EACD,OAAOvH,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}