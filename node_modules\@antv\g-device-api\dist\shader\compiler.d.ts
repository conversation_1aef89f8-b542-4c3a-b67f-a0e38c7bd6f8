import type { VendorInfo } from '../api';
export declare function getDefines(shader: string): Record<string, number>;
export declare function getAttributeLocations(vert: string, defines: Record<string, number>): {
    location: number;
    name: string;
}[];
export declare function getUniforms(vert: string): string[];
export declare function preprocessShader_GLSL(vendorInfo: VendorInfo, type: 'vert' | 'frag', source: string, defines?: Record<string, string> | null, usePrecision?: boolean): string;
export interface ProgramDescriptorSimpleWithOrig {
    vert: string;
    frag: string;
    preprocessedVert: string;
    preprocessedFrag: string;
}
export declare function preprocessProgram_GLSL(vendorInfo: VendorInfo, vert: string, frag: string, defines?: Record<string, string> | null): ProgramDescriptorSimpleWithOrig;
