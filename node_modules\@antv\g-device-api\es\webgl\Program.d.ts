import type { Program, ProgramDescriptor } from '../api';
import { ResourceType } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export declare enum ProgramCompileState_GL {
    NeedsCompile = 0,
    Compiling = 1,
    NeedsBind = 2,
    ReadyToUse = 3
}
export declare class Program_GL extends ResourceBase_GL implements Program {
    private rawVertexGLSL;
    type: ResourceType.Program;
    gl_program: WebGLProgram;
    gl_shader_vert: WebGLShader | null;
    gl_shader_frag: WebGLShader | null;
    compileState: ProgramCompileState_GL;
    descriptor: ProgramDescriptor;
    uniformSetters: Record<string, any>;
    attributes: {
        name: string;
        location: number;
        type: number;
        size: number;
    }[];
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: ProgramDescriptor;
    }, rawVertexGLSL: string);
    destroy(): void;
    private tryCompileProgram;
    private readAttributesFromLinkedProgram;
    private readUniformLocationsFromLinkedProgram;
    private compileShader;
    setUniformsLegacy(uniforms?: Record<string, any>): this;
}
