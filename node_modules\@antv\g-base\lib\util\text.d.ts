import { ShapeAttrs } from '../types';
/**
 * 获取文本的高度
 * @param text 文本
 * @param fontSize 字体大小
 * @param lineHeight 行高，可以为空
 */
export declare function getTextHeight(text: string, fontSize: number, lineHeight?: number): number;
/**
 * 获取行间距如果文本多行，需要获取文本间距
 * @param fontSize 字体大小
 * @param lineHeight 行高
 */
export declare function getLineSpaceing(fontSize: number, lineHeight?: number): number;
/**
 * 字体宽度
 * @param text 文本
 * @param font 字体
 */
export declare function getTextWidth(text: string, font: string): number;
export declare function assembleFont(attrs: ShapeAttrs): string;
