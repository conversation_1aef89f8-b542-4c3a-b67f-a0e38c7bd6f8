/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export function __wbg_wgslcomposer_free(a: number): void;
export function wgslcomposer_new(): number;
export function wgslcomposer_load_composable(a: number, b: number, c: number): void;
export function wgslcomposer_wgsl_compile(a: number, b: number, c: number, d: number): void;
export function glsl_compile(a: number, b: number, c: number, d: number, e: number, f: number): void;
export function __wbindgen_malloc(a: number, b: number): number;
export function __wbindgen_realloc(a: number, b: number, c: number, d: number): number;
export function __wbindgen_add_to_stack_pointer(a: number): number;
export function __wbindgen_free(a: number, b: number, c: number): void;
