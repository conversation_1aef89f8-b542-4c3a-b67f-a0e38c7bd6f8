English | [简体中文](./README-zh_CN.md)

# g-canvas

[![](https://img.shields.io/travis/antvis/g.svg)](https://travis-ci.org/antvis/g)
![](https://img.shields.io/badge/language-javascript-red.svg)
![](https://img.shields.io/badge/license-MIT-000000.svg)

[![npm package](https://img.shields.io/npm/v/@antv/g-canvas.svg)](https://www.npmjs.com/package/@antv/g-canavs)
[![npm downloads](http://img.shields.io/npm/dm/@antv/g-canvas.svg)](https://npmjs.org/package/@antv/g-canvas)
[![Percentage of issues still open](http://isitmaintained.com/badge/open/antvis/g.svg)](http://isitmaintained.com/project/antvis/g 'Percentage of issues still open')

- A canvas library which providing 2d draw for AntV.

## ✨ Features

- Powerful and scalable rendering capability with built-in basic Graphics.
- Excellent rendering performance and supports visualization scenarios with large amounts of data.
- Complete simulation of browser DOM events, and no difference from native events.
- Smooth animation implementation and rich configuration interfaces.

## 📦 Install

```bash
$ npm install @antv/g-canvas --save
```

## 🔨 Usage

```html
<div id="c1"></div>
```

```js
import { Canvas } from '@antv/g-canvas';

const canvas = new Canvas({
  container: 'c1',
  width: 500,
  height: 500,
});

const group = canvas.addGroup();
group.addShape('circle', {
  attrs: {
    x: 100,
    y: 100,
    r: 50,
    fill: 'red',
    stroke: 'blue',
    lineWidth: 5,
  },
});
```
