{"ast": null, "code": "import \"core-js/modules/es.array.fill.js\";\nimport \"core-js/modules/es.array.index-of.js\";\nimport \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enterEmphasis, leaveEmphasis } from '../../util/states.js';\nimport Model from '../../model/Model.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport * as listComponentHelper from '../helper/listComponent.js';\nimport ComponentView from '../../view/Component.js';\nimport { ToolboxFeature, getFeature } from './featureManager.js';\nimport { getUID } from '../../util/component.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { getFont } from '../../label/labelStyle.js';\nvar ToolboxView = /** @class */function (_super) {\n  __extends(ToolboxView, _super);\n  function ToolboxView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  ToolboxView.prototype.render = function (toolboxModel, ecModel, api, payload) {\n    var group = this.group;\n    group.removeAll();\n    if (!toolboxModel.get('show')) {\n      return;\n    }\n    var itemSize = +toolboxModel.get('itemSize');\n    var isVertical = toolboxModel.get('orient') === 'vertical';\n    var featureOpts = toolboxModel.get('feature') || {};\n    var features = this._features || (this._features = {});\n    var featureNames = [];\n    zrUtil.each(featureOpts, function (opt, name) {\n      featureNames.push(name);\n    });\n    new DataDiffer(this._featureNames || [], featureNames).add(processFeature).update(processFeature).remove(zrUtil.curry(processFeature, null)).execute();\n    // Keep for diff.\n    this._featureNames = featureNames;\n    function processFeature(newIndex, oldIndex) {\n      var featureName = featureNames[newIndex];\n      var oldName = featureNames[oldIndex];\n      var featureOpt = featureOpts[featureName];\n      var featureModel = new Model(featureOpt, toolboxModel, toolboxModel.ecModel);\n      var feature;\n      // FIX#11236, merge feature title from MagicType newOption. TODO: consider seriesIndex ?\n      if (payload && payload.newTitle != null && payload.featureName === featureName) {\n        featureOpt.title = payload.newTitle;\n      }\n      if (featureName && !oldName) {\n        // Create\n        if (isUserFeatureName(featureName)) {\n          feature = {\n            onclick: featureModel.option.onclick,\n            featureName: featureName\n          };\n        } else {\n          var Feature = getFeature(featureName);\n          if (!Feature) {\n            return;\n          }\n          feature = new Feature();\n        }\n        features[featureName] = feature;\n      } else {\n        feature = features[oldName];\n        // If feature does not exist.\n        if (!feature) {\n          return;\n        }\n      }\n      feature.uid = getUID('toolbox-feature');\n      feature.model = featureModel;\n      feature.ecModel = ecModel;\n      feature.api = api;\n      var isToolboxFeature = feature instanceof ToolboxFeature;\n      if (!featureName && oldName) {\n        isToolboxFeature && feature.dispose && feature.dispose(ecModel, api);\n        return;\n      }\n      if (!featureModel.get('show') || isToolboxFeature && feature.unusable) {\n        isToolboxFeature && feature.remove && feature.remove(ecModel, api);\n        return;\n      }\n      createIconPaths(featureModel, feature, featureName);\n      featureModel.setIconStatus = function (iconName, status) {\n        var option = this.option;\n        var iconPaths = this.iconPaths;\n        option.iconStatus = option.iconStatus || {};\n        option.iconStatus[iconName] = status;\n        if (iconPaths[iconName]) {\n          (status === 'emphasis' ? enterEmphasis : leaveEmphasis)(iconPaths[iconName]);\n        }\n      };\n      if (feature instanceof ToolboxFeature) {\n        if (feature.render) {\n          feature.render(featureModel, ecModel, api, payload);\n        }\n      }\n    }\n    function createIconPaths(featureModel, feature, featureName) {\n      var iconStyleModel = featureModel.getModel('iconStyle');\n      var iconStyleEmphasisModel = featureModel.getModel(['emphasis', 'iconStyle']);\n      // If one feature has multiple icons, they are organized as\n      // {\n      //     icon: {\n      //         foo: '',\n      //         bar: ''\n      //     },\n      //     title: {\n      //         foo: '',\n      //         bar: ''\n      //     }\n      // }\n      var icons = feature instanceof ToolboxFeature && feature.getIcons ? feature.getIcons() : featureModel.get('icon');\n      var titles = featureModel.get('title') || {};\n      var iconsMap;\n      var titlesMap;\n      if (zrUtil.isString(icons)) {\n        iconsMap = {};\n        iconsMap[featureName] = icons;\n      } else {\n        iconsMap = icons;\n      }\n      if (zrUtil.isString(titles)) {\n        titlesMap = {};\n        titlesMap[featureName] = titles;\n      } else {\n        titlesMap = titles;\n      }\n      var iconPaths = featureModel.iconPaths = {};\n      zrUtil.each(iconsMap, function (iconStr, iconName) {\n        var path = graphic.createIcon(iconStr, {}, {\n          x: -itemSize / 2,\n          y: -itemSize / 2,\n          width: itemSize,\n          height: itemSize\n        }); // TODO handling image\n        path.setStyle(iconStyleModel.getItemStyle());\n        var pathEmphasisState = path.ensureState('emphasis');\n        pathEmphasisState.style = iconStyleEmphasisModel.getItemStyle();\n        // Text position calculation\n        // TODO: extract `textStyle` from `iconStyle` and use `createTextStyle`\n        var textContent = new ZRText({\n          style: {\n            text: titlesMap[iconName],\n            align: iconStyleEmphasisModel.get('textAlign'),\n            borderRadius: iconStyleEmphasisModel.get('textBorderRadius'),\n            padding: iconStyleEmphasisModel.get('textPadding'),\n            fill: null,\n            font: getFont({\n              fontStyle: iconStyleEmphasisModel.get('textFontStyle'),\n              fontFamily: iconStyleEmphasisModel.get('textFontFamily'),\n              fontSize: iconStyleEmphasisModel.get('textFontSize'),\n              fontWeight: iconStyleEmphasisModel.get('textFontWeight')\n            }, ecModel)\n          },\n          ignore: true\n        });\n        path.setTextContent(textContent);\n        graphic.setTooltipConfig({\n          el: path,\n          componentModel: toolboxModel,\n          itemName: iconName,\n          formatterParamsExtra: {\n            title: titlesMap[iconName]\n          }\n        });\n        path.__title = titlesMap[iconName];\n        path.on('mouseover', function () {\n          // Should not reuse above hoverStyle, which might be modified.\n          var hoverStyle = iconStyleEmphasisModel.getItemStyle();\n          var defaultTextPosition = isVertical ? toolboxModel.get('right') == null && toolboxModel.get('left') !== 'right' ? 'right' : 'left' : toolboxModel.get('bottom') == null && toolboxModel.get('top') !== 'bottom' ? 'bottom' : 'top';\n          textContent.setStyle({\n            fill: iconStyleEmphasisModel.get('textFill') || hoverStyle.fill || hoverStyle.stroke || '#000',\n            backgroundColor: iconStyleEmphasisModel.get('textBackgroundColor')\n          });\n          path.setTextConfig({\n            position: iconStyleEmphasisModel.get('textPosition') || defaultTextPosition\n          });\n          textContent.ignore = !toolboxModel.get('showTitle');\n          // Use enterEmphasis and leaveEmphasis provide by ec.\n          // There are flags managed by the echarts.\n          api.enterEmphasis(this);\n        }).on('mouseout', function () {\n          if (featureModel.get(['iconStatus', iconName]) !== 'emphasis') {\n            api.leaveEmphasis(this);\n          }\n          textContent.hide();\n        });\n        (featureModel.get(['iconStatus', iconName]) === 'emphasis' ? enterEmphasis : leaveEmphasis)(path);\n        group.add(path);\n        path.on('click', zrUtil.bind(feature.onclick, feature, ecModel, api, iconName));\n        iconPaths[iconName] = path;\n      });\n    }\n    listComponentHelper.layout(group, toolboxModel, api);\n    // Render background after group is layout\n    // FIXME\n    group.add(listComponentHelper.makeBackground(group.getBoundingRect(), toolboxModel));\n    // Adjust icon title positions to avoid them out of screen\n    isVertical || group.eachChild(function (icon) {\n      var titleText = icon.__title;\n      // const hoverStyle = icon.hoverStyle;\n      // TODO simplify code?\n      var emphasisState = icon.ensureState('emphasis');\n      var emphasisTextConfig = emphasisState.textConfig || (emphasisState.textConfig = {});\n      var textContent = icon.getTextContent();\n      var emphasisTextState = textContent && textContent.ensureState('emphasis');\n      // May be background element\n      if (emphasisTextState && !zrUtil.isFunction(emphasisTextState) && titleText) {\n        var emphasisTextStyle = emphasisTextState.style || (emphasisTextState.style = {});\n        var rect = textContain.getBoundingRect(titleText, ZRText.makeFont(emphasisTextStyle));\n        var offsetX = icon.x + group.x;\n        var offsetY = icon.y + group.y + itemSize;\n        var needPutOnTop = false;\n        if (offsetY + rect.height > api.getHeight()) {\n          emphasisTextConfig.position = 'top';\n          needPutOnTop = true;\n        }\n        var topOffset = needPutOnTop ? -5 - rect.height : itemSize + 10;\n        if (offsetX + rect.width / 2 > api.getWidth()) {\n          emphasisTextConfig.position = ['100%', topOffset];\n          emphasisTextStyle.align = 'right';\n        } else if (offsetX - rect.width / 2 < 0) {\n          emphasisTextConfig.position = [0, topOffset];\n          emphasisTextStyle.align = 'left';\n        }\n      }\n    });\n  };\n  ToolboxView.prototype.updateView = function (toolboxModel, ecModel, api, payload) {\n    zrUtil.each(this._features, function (feature) {\n      feature instanceof ToolboxFeature && feature.updateView && feature.updateView(feature.model, ecModel, api, payload);\n    });\n  };\n  // updateLayout(toolboxModel, ecModel, api, payload) {\n  //     zrUtil.each(this._features, function (feature) {\n  //         feature.updateLayout && feature.updateLayout(feature.model, ecModel, api, payload);\n  //     });\n  // },\n  ToolboxView.prototype.remove = function (ecModel, api) {\n    zrUtil.each(this._features, function (feature) {\n      feature instanceof ToolboxFeature && feature.remove && feature.remove(ecModel, api);\n    });\n    this.group.removeAll();\n  };\n  ToolboxView.prototype.dispose = function (ecModel, api) {\n    zrUtil.each(this._features, function (feature) {\n      feature instanceof ToolboxFeature && feature.dispose && feature.dispose(ecModel, api);\n    });\n  };\n  ToolboxView.type = 'toolbox';\n  return ToolboxView;\n}(ComponentView);\nfunction isUserFeatureName(featureName) {\n  return featureName.indexOf('my') === 0;\n}\nexport default ToolboxView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "textContain", "graphic", "enterEmphasis", "leaveEmphasis", "Model", "<PERSON><PERSON><PERSON><PERSON>", "listComponentHelper", "ComponentView", "ToolboxFeature", "getFeature", "getUID", "ZRText", "getFont", "ToolboxView", "_super", "apply", "arguments", "prototype", "render", "toolboxModel", "ecModel", "api", "payload", "group", "removeAll", "get", "itemSize", "isVertical", "featureOpts", "features", "_features", "featureNames", "each", "opt", "name", "push", "_featureNames", "add", "processFeature", "update", "remove", "curry", "execute", "newIndex", "oldIndex", "featureName", "old<PERSON>ame", "featureOpt", "featureModel", "feature", "newTitle", "title", "isUserFeatureName", "onclick", "option", "Feature", "uid", "model", "isToolboxFeature", "dispose", "unusable", "createIconPaths", "setIconStatus", "iconName", "status", "iconPaths", "iconStatus", "iconStyleModel", "getModel", "iconStyleEmphasisModel", "icons", "getIcons", "titles", "iconsMap", "titlesMap", "isString", "iconStr", "path", "createIcon", "x", "y", "width", "height", "setStyle", "getItemStyle", "pathEmphasisState", "ensureState", "style", "textContent", "text", "align", "borderRadius", "padding", "fill", "font", "fontStyle", "fontFamily", "fontSize", "fontWeight", "ignore", "setTextContent", "setTooltipConfig", "el", "componentModel", "itemName", "formatterParamsExtra", "__title", "on", "hoverStyle", "defaultTextPosition", "stroke", "backgroundColor", "setTextConfig", "position", "hide", "bind", "layout", "makeBackground", "getBoundingRect", "<PERSON><PERSON><PERSON><PERSON>", "icon", "titleText", "emphasisState", "emphasisTextConfig", "textConfig", "getTextContent", "emphasisTextState", "isFunction", "emphasisTextStyle", "rect", "makeFont", "offsetX", "offsetY", "needPutOnTop", "getHeight", "topOffset", "getWidth", "updateView", "type", "indexOf"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/component/toolbox/ToolboxView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enterEmphasis, leaveEmphasis } from '../../util/states.js';\nimport Model from '../../model/Model.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport * as listComponentHelper from '../helper/listComponent.js';\nimport ComponentView from '../../view/Component.js';\nimport { ToolboxFeature, getFeature } from './featureManager.js';\nimport { getUID } from '../../util/component.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { getFont } from '../../label/labelStyle.js';\nvar ToolboxView = /** @class */function (_super) {\n  __extends(ToolboxView, _super);\n  function ToolboxView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  ToolboxView.prototype.render = function (toolboxModel, ecModel, api, payload) {\n    var group = this.group;\n    group.removeAll();\n    if (!toolboxModel.get('show')) {\n      return;\n    }\n    var itemSize = +toolboxModel.get('itemSize');\n    var isVertical = toolboxModel.get('orient') === 'vertical';\n    var featureOpts = toolboxModel.get('feature') || {};\n    var features = this._features || (this._features = {});\n    var featureNames = [];\n    zrUtil.each(featureOpts, function (opt, name) {\n      featureNames.push(name);\n    });\n    new DataDiffer(this._featureNames || [], featureNames).add(processFeature).update(processFeature).remove(zrUtil.curry(processFeature, null)).execute();\n    // Keep for diff.\n    this._featureNames = featureNames;\n    function processFeature(newIndex, oldIndex) {\n      var featureName = featureNames[newIndex];\n      var oldName = featureNames[oldIndex];\n      var featureOpt = featureOpts[featureName];\n      var featureModel = new Model(featureOpt, toolboxModel, toolboxModel.ecModel);\n      var feature;\n      // FIX#11236, merge feature title from MagicType newOption. TODO: consider seriesIndex ?\n      if (payload && payload.newTitle != null && payload.featureName === featureName) {\n        featureOpt.title = payload.newTitle;\n      }\n      if (featureName && !oldName) {\n        // Create\n        if (isUserFeatureName(featureName)) {\n          feature = {\n            onclick: featureModel.option.onclick,\n            featureName: featureName\n          };\n        } else {\n          var Feature = getFeature(featureName);\n          if (!Feature) {\n            return;\n          }\n          feature = new Feature();\n        }\n        features[featureName] = feature;\n      } else {\n        feature = features[oldName];\n        // If feature does not exist.\n        if (!feature) {\n          return;\n        }\n      }\n      feature.uid = getUID('toolbox-feature');\n      feature.model = featureModel;\n      feature.ecModel = ecModel;\n      feature.api = api;\n      var isToolboxFeature = feature instanceof ToolboxFeature;\n      if (!featureName && oldName) {\n        isToolboxFeature && feature.dispose && feature.dispose(ecModel, api);\n        return;\n      }\n      if (!featureModel.get('show') || isToolboxFeature && feature.unusable) {\n        isToolboxFeature && feature.remove && feature.remove(ecModel, api);\n        return;\n      }\n      createIconPaths(featureModel, feature, featureName);\n      featureModel.setIconStatus = function (iconName, status) {\n        var option = this.option;\n        var iconPaths = this.iconPaths;\n        option.iconStatus = option.iconStatus || {};\n        option.iconStatus[iconName] = status;\n        if (iconPaths[iconName]) {\n          (status === 'emphasis' ? enterEmphasis : leaveEmphasis)(iconPaths[iconName]);\n        }\n      };\n      if (feature instanceof ToolboxFeature) {\n        if (feature.render) {\n          feature.render(featureModel, ecModel, api, payload);\n        }\n      }\n    }\n    function createIconPaths(featureModel, feature, featureName) {\n      var iconStyleModel = featureModel.getModel('iconStyle');\n      var iconStyleEmphasisModel = featureModel.getModel(['emphasis', 'iconStyle']);\n      // If one feature has multiple icons, they are organized as\n      // {\n      //     icon: {\n      //         foo: '',\n      //         bar: ''\n      //     },\n      //     title: {\n      //         foo: '',\n      //         bar: ''\n      //     }\n      // }\n      var icons = feature instanceof ToolboxFeature && feature.getIcons ? feature.getIcons() : featureModel.get('icon');\n      var titles = featureModel.get('title') || {};\n      var iconsMap;\n      var titlesMap;\n      if (zrUtil.isString(icons)) {\n        iconsMap = {};\n        iconsMap[featureName] = icons;\n      } else {\n        iconsMap = icons;\n      }\n      if (zrUtil.isString(titles)) {\n        titlesMap = {};\n        titlesMap[featureName] = titles;\n      } else {\n        titlesMap = titles;\n      }\n      var iconPaths = featureModel.iconPaths = {};\n      zrUtil.each(iconsMap, function (iconStr, iconName) {\n        var path = graphic.createIcon(iconStr, {}, {\n          x: -itemSize / 2,\n          y: -itemSize / 2,\n          width: itemSize,\n          height: itemSize\n        }); // TODO handling image\n        path.setStyle(iconStyleModel.getItemStyle());\n        var pathEmphasisState = path.ensureState('emphasis');\n        pathEmphasisState.style = iconStyleEmphasisModel.getItemStyle();\n        // Text position calculation\n        // TODO: extract `textStyle` from `iconStyle` and use `createTextStyle`\n        var textContent = new ZRText({\n          style: {\n            text: titlesMap[iconName],\n            align: iconStyleEmphasisModel.get('textAlign'),\n            borderRadius: iconStyleEmphasisModel.get('textBorderRadius'),\n            padding: iconStyleEmphasisModel.get('textPadding'),\n            fill: null,\n            font: getFont({\n              fontStyle: iconStyleEmphasisModel.get('textFontStyle'),\n              fontFamily: iconStyleEmphasisModel.get('textFontFamily'),\n              fontSize: iconStyleEmphasisModel.get('textFontSize'),\n              fontWeight: iconStyleEmphasisModel.get('textFontWeight')\n            }, ecModel)\n          },\n          ignore: true\n        });\n        path.setTextContent(textContent);\n        graphic.setTooltipConfig({\n          el: path,\n          componentModel: toolboxModel,\n          itemName: iconName,\n          formatterParamsExtra: {\n            title: titlesMap[iconName]\n          }\n        });\n        path.__title = titlesMap[iconName];\n        path.on('mouseover', function () {\n          // Should not reuse above hoverStyle, which might be modified.\n          var hoverStyle = iconStyleEmphasisModel.getItemStyle();\n          var defaultTextPosition = isVertical ? toolboxModel.get('right') == null && toolboxModel.get('left') !== 'right' ? 'right' : 'left' : toolboxModel.get('bottom') == null && toolboxModel.get('top') !== 'bottom' ? 'bottom' : 'top';\n          textContent.setStyle({\n            fill: iconStyleEmphasisModel.get('textFill') || hoverStyle.fill || hoverStyle.stroke || '#000',\n            backgroundColor: iconStyleEmphasisModel.get('textBackgroundColor')\n          });\n          path.setTextConfig({\n            position: iconStyleEmphasisModel.get('textPosition') || defaultTextPosition\n          });\n          textContent.ignore = !toolboxModel.get('showTitle');\n          // Use enterEmphasis and leaveEmphasis provide by ec.\n          // There are flags managed by the echarts.\n          api.enterEmphasis(this);\n        }).on('mouseout', function () {\n          if (featureModel.get(['iconStatus', iconName]) !== 'emphasis') {\n            api.leaveEmphasis(this);\n          }\n          textContent.hide();\n        });\n        (featureModel.get(['iconStatus', iconName]) === 'emphasis' ? enterEmphasis : leaveEmphasis)(path);\n        group.add(path);\n        path.on('click', zrUtil.bind(feature.onclick, feature, ecModel, api, iconName));\n        iconPaths[iconName] = path;\n      });\n    }\n    listComponentHelper.layout(group, toolboxModel, api);\n    // Render background after group is layout\n    // FIXME\n    group.add(listComponentHelper.makeBackground(group.getBoundingRect(), toolboxModel));\n    // Adjust icon title positions to avoid them out of screen\n    isVertical || group.eachChild(function (icon) {\n      var titleText = icon.__title;\n      // const hoverStyle = icon.hoverStyle;\n      // TODO simplify code?\n      var emphasisState = icon.ensureState('emphasis');\n      var emphasisTextConfig = emphasisState.textConfig || (emphasisState.textConfig = {});\n      var textContent = icon.getTextContent();\n      var emphasisTextState = textContent && textContent.ensureState('emphasis');\n      // May be background element\n      if (emphasisTextState && !zrUtil.isFunction(emphasisTextState) && titleText) {\n        var emphasisTextStyle = emphasisTextState.style || (emphasisTextState.style = {});\n        var rect = textContain.getBoundingRect(titleText, ZRText.makeFont(emphasisTextStyle));\n        var offsetX = icon.x + group.x;\n        var offsetY = icon.y + group.y + itemSize;\n        var needPutOnTop = false;\n        if (offsetY + rect.height > api.getHeight()) {\n          emphasisTextConfig.position = 'top';\n          needPutOnTop = true;\n        }\n        var topOffset = needPutOnTop ? -5 - rect.height : itemSize + 10;\n        if (offsetX + rect.width / 2 > api.getWidth()) {\n          emphasisTextConfig.position = ['100%', topOffset];\n          emphasisTextStyle.align = 'right';\n        } else if (offsetX - rect.width / 2 < 0) {\n          emphasisTextConfig.position = [0, topOffset];\n          emphasisTextStyle.align = 'left';\n        }\n      }\n    });\n  };\n  ToolboxView.prototype.updateView = function (toolboxModel, ecModel, api, payload) {\n    zrUtil.each(this._features, function (feature) {\n      feature instanceof ToolboxFeature && feature.updateView && feature.updateView(feature.model, ecModel, api, payload);\n    });\n  };\n  // updateLayout(toolboxModel, ecModel, api, payload) {\n  //     zrUtil.each(this._features, function (feature) {\n  //         feature.updateLayout && feature.updateLayout(feature.model, ecModel, api, payload);\n  //     });\n  // },\n  ToolboxView.prototype.remove = function (ecModel, api) {\n    zrUtil.each(this._features, function (feature) {\n      feature instanceof ToolboxFeature && feature.remove && feature.remove(ecModel, api);\n    });\n    this.group.removeAll();\n  };\n  ToolboxView.prototype.dispose = function (ecModel, api) {\n    zrUtil.each(this._features, function (feature) {\n      feature instanceof ToolboxFeature && feature.dispose && feature.dispose(ecModel, api);\n    });\n  };\n  ToolboxView.type = 'toolbox';\n  return ToolboxView;\n}(ComponentView);\nfunction isUserFeatureName(featureName) {\n  return featureName.indexOf('my') === 0;\n}\nexport default ToolboxView;"], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,WAAW,MAAM,6BAA6B;AAC1D,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AACnE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,mBAAmB,MAAM,4BAA4B;AACjE,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,cAAc,EAAEC,UAAU,QAAQ,qBAAqB;AAChE,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/ChB,SAAS,CAACe,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,WAAW,CAACI,SAAS,CAACC,MAAM,GAAG,UAAUC,YAAY,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC5E,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,CAACC,SAAS,CAAC,CAAC;IACjB,IAAI,CAACL,YAAY,CAACM,GAAG,CAAC,MAAM,CAAC,EAAE;MAC7B;IACF;IACA,IAAIC,QAAQ,GAAG,CAACP,YAAY,CAACM,GAAG,CAAC,UAAU,CAAC;IAC5C,IAAIE,UAAU,GAAGR,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,KAAK,UAAU;IAC1D,IAAIG,WAAW,GAAGT,YAAY,CAACM,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACnD,IAAII,QAAQ,GAAG,IAAI,CAACC,SAAS,KAAK,IAAI,CAACA,SAAS,GAAG,CAAC,CAAC,CAAC;IACtD,IAAIC,YAAY,GAAG,EAAE;IACrBhC,MAAM,CAACiC,IAAI,CAACJ,WAAW,EAAE,UAAUK,GAAG,EAAEC,IAAI,EAAE;MAC5CH,YAAY,CAACI,IAAI,CAACD,IAAI,CAAC;IACzB,CAAC,CAAC;IACF,IAAI7B,UAAU,CAAC,IAAI,CAAC+B,aAAa,IAAI,EAAE,EAAEL,YAAY,CAAC,CAACM,GAAG,CAACC,cAAc,CAAC,CAACC,MAAM,CAACD,cAAc,CAAC,CAACE,MAAM,CAACzC,MAAM,CAAC0C,KAAK,CAACH,cAAc,EAAE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC;IACtJ;IACA,IAAI,CAACN,aAAa,GAAGL,YAAY;IACjC,SAASO,cAAcA,CAACK,QAAQ,EAAEC,QAAQ,EAAE;MAC1C,IAAIC,WAAW,GAAGd,YAAY,CAACY,QAAQ,CAAC;MACxC,IAAIG,OAAO,GAAGf,YAAY,CAACa,QAAQ,CAAC;MACpC,IAAIG,UAAU,GAAGnB,WAAW,CAACiB,WAAW,CAAC;MACzC,IAAIG,YAAY,GAAG,IAAI5C,KAAK,CAAC2C,UAAU,EAAE5B,YAAY,EAAEA,YAAY,CAACC,OAAO,CAAC;MAC5E,IAAI6B,OAAO;MACX;MACA,IAAI3B,OAAO,IAAIA,OAAO,CAAC4B,QAAQ,IAAI,IAAI,IAAI5B,OAAO,CAACuB,WAAW,KAAKA,WAAW,EAAE;QAC9EE,UAAU,CAACI,KAAK,GAAG7B,OAAO,CAAC4B,QAAQ;MACrC;MACA,IAAIL,WAAW,IAAI,CAACC,OAAO,EAAE;QAC3B;QACA,IAAIM,iBAAiB,CAACP,WAAW,CAAC,EAAE;UAClCI,OAAO,GAAG;YACRI,OAAO,EAAEL,YAAY,CAACM,MAAM,CAACD,OAAO;YACpCR,WAAW,EAAEA;UACf,CAAC;QACH,CAAC,MAAM;UACL,IAAIU,OAAO,GAAG9C,UAAU,CAACoC,WAAW,CAAC;UACrC,IAAI,CAACU,OAAO,EAAE;YACZ;UACF;UACAN,OAAO,GAAG,IAAIM,OAAO,CAAC,CAAC;QACzB;QACA1B,QAAQ,CAACgB,WAAW,CAAC,GAAGI,OAAO;MACjC,CAAC,MAAM;QACLA,OAAO,GAAGpB,QAAQ,CAACiB,OAAO,CAAC;QAC3B;QACA,IAAI,CAACG,OAAO,EAAE;UACZ;QACF;MACF;MACAA,OAAO,CAACO,GAAG,GAAG9C,MAAM,CAAC,iBAAiB,CAAC;MACvCuC,OAAO,CAACQ,KAAK,GAAGT,YAAY;MAC5BC,OAAO,CAAC7B,OAAO,GAAGA,OAAO;MACzB6B,OAAO,CAAC5B,GAAG,GAAGA,GAAG;MACjB,IAAIqC,gBAAgB,GAAGT,OAAO,YAAYzC,cAAc;MACxD,IAAI,CAACqC,WAAW,IAAIC,OAAO,EAAE;QAC3BY,gBAAgB,IAAIT,OAAO,CAACU,OAAO,IAAIV,OAAO,CAACU,OAAO,CAACvC,OAAO,EAAEC,GAAG,CAAC;QACpE;MACF;MACA,IAAI,CAAC2B,YAAY,CAACvB,GAAG,CAAC,MAAM,CAAC,IAAIiC,gBAAgB,IAAIT,OAAO,CAACW,QAAQ,EAAE;QACrEF,gBAAgB,IAAIT,OAAO,CAACT,MAAM,IAAIS,OAAO,CAACT,MAAM,CAACpB,OAAO,EAAEC,GAAG,CAAC;QAClE;MACF;MACAwC,eAAe,CAACb,YAAY,EAAEC,OAAO,EAAEJ,WAAW,CAAC;MACnDG,YAAY,CAACc,aAAa,GAAG,UAAUC,QAAQ,EAAEC,MAAM,EAAE;QACvD,IAAIV,MAAM,GAAG,IAAI,CAACA,MAAM;QACxB,IAAIW,SAAS,GAAG,IAAI,CAACA,SAAS;QAC9BX,MAAM,CAACY,UAAU,GAAGZ,MAAM,CAACY,UAAU,IAAI,CAAC,CAAC;QAC3CZ,MAAM,CAACY,UAAU,CAACH,QAAQ,CAAC,GAAGC,MAAM;QACpC,IAAIC,SAAS,CAACF,QAAQ,CAAC,EAAE;UACvB,CAACC,MAAM,KAAK,UAAU,GAAG9D,aAAa,GAAGC,aAAa,EAAE8D,SAAS,CAACF,QAAQ,CAAC,CAAC;QAC9E;MACF,CAAC;MACD,IAAId,OAAO,YAAYzC,cAAc,EAAE;QACrC,IAAIyC,OAAO,CAAC/B,MAAM,EAAE;UAClB+B,OAAO,CAAC/B,MAAM,CAAC8B,YAAY,EAAE5B,OAAO,EAAEC,GAAG,EAAEC,OAAO,CAAC;QACrD;MACF;IACF;IACA,SAASuC,eAAeA,CAACb,YAAY,EAAEC,OAAO,EAAEJ,WAAW,EAAE;MAC3D,IAAIsB,cAAc,GAAGnB,YAAY,CAACoB,QAAQ,CAAC,WAAW,CAAC;MACvD,IAAIC,sBAAsB,GAAGrB,YAAY,CAACoB,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;MAC7E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIE,KAAK,GAAGrB,OAAO,YAAYzC,cAAc,IAAIyC,OAAO,CAACsB,QAAQ,GAAGtB,OAAO,CAACsB,QAAQ,CAAC,CAAC,GAAGvB,YAAY,CAACvB,GAAG,CAAC,MAAM,CAAC;MACjH,IAAI+C,MAAM,GAAGxB,YAAY,CAACvB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;MAC5C,IAAIgD,QAAQ;MACZ,IAAIC,SAAS;MACb,IAAI3E,MAAM,CAAC4E,QAAQ,CAACL,KAAK,CAAC,EAAE;QAC1BG,QAAQ,GAAG,CAAC,CAAC;QACbA,QAAQ,CAAC5B,WAAW,CAAC,GAAGyB,KAAK;MAC/B,CAAC,MAAM;QACLG,QAAQ,GAAGH,KAAK;MAClB;MACA,IAAIvE,MAAM,CAAC4E,QAAQ,CAACH,MAAM,CAAC,EAAE;QAC3BE,SAAS,GAAG,CAAC,CAAC;QACdA,SAAS,CAAC7B,WAAW,CAAC,GAAG2B,MAAM;MACjC,CAAC,MAAM;QACLE,SAAS,GAAGF,MAAM;MACpB;MACA,IAAIP,SAAS,GAAGjB,YAAY,CAACiB,SAAS,GAAG,CAAC,CAAC;MAC3ClE,MAAM,CAACiC,IAAI,CAACyC,QAAQ,EAAE,UAAUG,OAAO,EAAEb,QAAQ,EAAE;QACjD,IAAIc,IAAI,GAAG5E,OAAO,CAAC6E,UAAU,CAACF,OAAO,EAAE,CAAC,CAAC,EAAE;UACzCG,CAAC,EAAE,CAACrD,QAAQ,GAAG,CAAC;UAChBsD,CAAC,EAAE,CAACtD,QAAQ,GAAG,CAAC;UAChBuD,KAAK,EAAEvD,QAAQ;UACfwD,MAAM,EAAExD;QACV,CAAC,CAAC,CAAC,CAAC;QACJmD,IAAI,CAACM,QAAQ,CAAChB,cAAc,CAACiB,YAAY,CAAC,CAAC,CAAC;QAC5C,IAAIC,iBAAiB,GAAGR,IAAI,CAACS,WAAW,CAAC,UAAU,CAAC;QACpDD,iBAAiB,CAACE,KAAK,GAAGlB,sBAAsB,CAACe,YAAY,CAAC,CAAC;QAC/D;QACA;QACA,IAAII,WAAW,GAAG,IAAI7E,MAAM,CAAC;UAC3B4E,KAAK,EAAE;YACLE,IAAI,EAAEf,SAAS,CAACX,QAAQ,CAAC;YACzB2B,KAAK,EAAErB,sBAAsB,CAAC5C,GAAG,CAAC,WAAW,CAAC;YAC9CkE,YAAY,EAAEtB,sBAAsB,CAAC5C,GAAG,CAAC,kBAAkB,CAAC;YAC5DmE,OAAO,EAAEvB,sBAAsB,CAAC5C,GAAG,CAAC,aAAa,CAAC;YAClDoE,IAAI,EAAE,IAAI;YACVC,IAAI,EAAElF,OAAO,CAAC;cACZmF,SAAS,EAAE1B,sBAAsB,CAAC5C,GAAG,CAAC,eAAe,CAAC;cACtDuE,UAAU,EAAE3B,sBAAsB,CAAC5C,GAAG,CAAC,gBAAgB,CAAC;cACxDwE,QAAQ,EAAE5B,sBAAsB,CAAC5C,GAAG,CAAC,cAAc,CAAC;cACpDyE,UAAU,EAAE7B,sBAAsB,CAAC5C,GAAG,CAAC,gBAAgB;YACzD,CAAC,EAAEL,OAAO;UACZ,CAAC;UACD+E,MAAM,EAAE;QACV,CAAC,CAAC;QACFtB,IAAI,CAACuB,cAAc,CAACZ,WAAW,CAAC;QAChCvF,OAAO,CAACoG,gBAAgB,CAAC;UACvBC,EAAE,EAAEzB,IAAI;UACR0B,cAAc,EAAEpF,YAAY;UAC5BqF,QAAQ,EAAEzC,QAAQ;UAClB0C,oBAAoB,EAAE;YACpBtD,KAAK,EAAEuB,SAAS,CAACX,QAAQ;UAC3B;QACF,CAAC,CAAC;QACFc,IAAI,CAAC6B,OAAO,GAAGhC,SAAS,CAACX,QAAQ,CAAC;QAClCc,IAAI,CAAC8B,EAAE,CAAC,WAAW,EAAE,YAAY;UAC/B;UACA,IAAIC,UAAU,GAAGvC,sBAAsB,CAACe,YAAY,CAAC,CAAC;UACtD,IAAIyB,mBAAmB,GAAGlF,UAAU,GAAGR,YAAY,CAACM,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,IAAIN,YAAY,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM,GAAGN,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAIN,YAAY,CAACM,GAAG,CAAC,KAAK,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,KAAK;UACnO+D,WAAW,CAACL,QAAQ,CAAC;YACnBU,IAAI,EAAExB,sBAAsB,CAAC5C,GAAG,CAAC,UAAU,CAAC,IAAImF,UAAU,CAACf,IAAI,IAAIe,UAAU,CAACE,MAAM,IAAI,MAAM;YAC9FC,eAAe,EAAE1C,sBAAsB,CAAC5C,GAAG,CAAC,qBAAqB;UACnE,CAAC,CAAC;UACFoD,IAAI,CAACmC,aAAa,CAAC;YACjBC,QAAQ,EAAE5C,sBAAsB,CAAC5C,GAAG,CAAC,cAAc,CAAC,IAAIoF;UAC1D,CAAC,CAAC;UACFrB,WAAW,CAACW,MAAM,GAAG,CAAChF,YAAY,CAACM,GAAG,CAAC,WAAW,CAAC;UACnD;UACA;UACAJ,GAAG,CAACnB,aAAa,CAAC,IAAI,CAAC;QACzB,CAAC,CAAC,CAACyG,EAAE,CAAC,UAAU,EAAE,YAAY;UAC5B,IAAI3D,YAAY,CAACvB,GAAG,CAAC,CAAC,YAAY,EAAEsC,QAAQ,CAAC,CAAC,KAAK,UAAU,EAAE;YAC7D1C,GAAG,CAAClB,aAAa,CAAC,IAAI,CAAC;UACzB;UACAqF,WAAW,CAAC0B,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC;QACF,CAAClE,YAAY,CAACvB,GAAG,CAAC,CAAC,YAAY,EAAEsC,QAAQ,CAAC,CAAC,KAAK,UAAU,GAAG7D,aAAa,GAAGC,aAAa,EAAE0E,IAAI,CAAC;QACjGtD,KAAK,CAACc,GAAG,CAACwC,IAAI,CAAC;QACfA,IAAI,CAAC8B,EAAE,CAAC,OAAO,EAAE5G,MAAM,CAACoH,IAAI,CAAClE,OAAO,CAACI,OAAO,EAAEJ,OAAO,EAAE7B,OAAO,EAAEC,GAAG,EAAE0C,QAAQ,CAAC,CAAC;QAC/EE,SAAS,CAACF,QAAQ,CAAC,GAAGc,IAAI;MAC5B,CAAC,CAAC;IACJ;IACAvE,mBAAmB,CAAC8G,MAAM,CAAC7F,KAAK,EAAEJ,YAAY,EAAEE,GAAG,CAAC;IACpD;IACA;IACAE,KAAK,CAACc,GAAG,CAAC/B,mBAAmB,CAAC+G,cAAc,CAAC9F,KAAK,CAAC+F,eAAe,CAAC,CAAC,EAAEnG,YAAY,CAAC,CAAC;IACpF;IACAQ,UAAU,IAAIJ,KAAK,CAACgG,SAAS,CAAC,UAAUC,IAAI,EAAE;MAC5C,IAAIC,SAAS,GAAGD,IAAI,CAACd,OAAO;MAC5B;MACA;MACA,IAAIgB,aAAa,GAAGF,IAAI,CAAClC,WAAW,CAAC,UAAU,CAAC;MAChD,IAAIqC,kBAAkB,GAAGD,aAAa,CAACE,UAAU,KAAKF,aAAa,CAACE,UAAU,GAAG,CAAC,CAAC,CAAC;MACpF,IAAIpC,WAAW,GAAGgC,IAAI,CAACK,cAAc,CAAC,CAAC;MACvC,IAAIC,iBAAiB,GAAGtC,WAAW,IAAIA,WAAW,CAACF,WAAW,CAAC,UAAU,CAAC;MAC1E;MACA,IAAIwC,iBAAiB,IAAI,CAAC/H,MAAM,CAACgI,UAAU,CAACD,iBAAiB,CAAC,IAAIL,SAAS,EAAE;QAC3E,IAAIO,iBAAiB,GAAGF,iBAAiB,CAACvC,KAAK,KAAKuC,iBAAiB,CAACvC,KAAK,GAAG,CAAC,CAAC,CAAC;QACjF,IAAI0C,IAAI,GAAGjI,WAAW,CAACsH,eAAe,CAACG,SAAS,EAAE9G,MAAM,CAACuH,QAAQ,CAACF,iBAAiB,CAAC,CAAC;QACrF,IAAIG,OAAO,GAAGX,IAAI,CAACzC,CAAC,GAAGxD,KAAK,CAACwD,CAAC;QAC9B,IAAIqD,OAAO,GAAGZ,IAAI,CAACxC,CAAC,GAAGzD,KAAK,CAACyD,CAAC,GAAGtD,QAAQ;QACzC,IAAI2G,YAAY,GAAG,KAAK;QACxB,IAAID,OAAO,GAAGH,IAAI,CAAC/C,MAAM,GAAG7D,GAAG,CAACiH,SAAS,CAAC,CAAC,EAAE;UAC3CX,kBAAkB,CAACV,QAAQ,GAAG,KAAK;UACnCoB,YAAY,GAAG,IAAI;QACrB;QACA,IAAIE,SAAS,GAAGF,YAAY,GAAG,CAAC,CAAC,GAAGJ,IAAI,CAAC/C,MAAM,GAAGxD,QAAQ,GAAG,EAAE;QAC/D,IAAIyG,OAAO,GAAGF,IAAI,CAAChD,KAAK,GAAG,CAAC,GAAG5D,GAAG,CAACmH,QAAQ,CAAC,CAAC,EAAE;UAC7Cb,kBAAkB,CAACV,QAAQ,GAAG,CAAC,MAAM,EAAEsB,SAAS,CAAC;UACjDP,iBAAiB,CAACtC,KAAK,GAAG,OAAO;QACnC,CAAC,MAAM,IAAIyC,OAAO,GAAGF,IAAI,CAAChD,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE;UACvC0C,kBAAkB,CAACV,QAAQ,GAAG,CAAC,CAAC,EAAEsB,SAAS,CAAC;UAC5CP,iBAAiB,CAACtC,KAAK,GAAG,MAAM;QAClC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD7E,WAAW,CAACI,SAAS,CAACwH,UAAU,GAAG,UAAUtH,YAAY,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAChFvB,MAAM,CAACiC,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE,UAAUmB,OAAO,EAAE;MAC7CA,OAAO,YAAYzC,cAAc,IAAIyC,OAAO,CAACwF,UAAU,IAAIxF,OAAO,CAACwF,UAAU,CAACxF,OAAO,CAACQ,KAAK,EAAErC,OAAO,EAAEC,GAAG,EAAEC,OAAO,CAAC;IACrH,CAAC,CAAC;EACJ,CAAC;EACD;EACA;EACA;EACA;EACA;EACAT,WAAW,CAACI,SAAS,CAACuB,MAAM,GAAG,UAAUpB,OAAO,EAAEC,GAAG,EAAE;IACrDtB,MAAM,CAACiC,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE,UAAUmB,OAAO,EAAE;MAC7CA,OAAO,YAAYzC,cAAc,IAAIyC,OAAO,CAACT,MAAM,IAAIS,OAAO,CAACT,MAAM,CAACpB,OAAO,EAAEC,GAAG,CAAC;IACrF,CAAC,CAAC;IACF,IAAI,CAACE,KAAK,CAACC,SAAS,CAAC,CAAC;EACxB,CAAC;EACDX,WAAW,CAACI,SAAS,CAAC0C,OAAO,GAAG,UAAUvC,OAAO,EAAEC,GAAG,EAAE;IACtDtB,MAAM,CAACiC,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE,UAAUmB,OAAO,EAAE;MAC7CA,OAAO,YAAYzC,cAAc,IAAIyC,OAAO,CAACU,OAAO,IAAIV,OAAO,CAACU,OAAO,CAACvC,OAAO,EAAEC,GAAG,CAAC;IACvF,CAAC,CAAC;EACJ,CAAC;EACDR,WAAW,CAAC6H,IAAI,GAAG,SAAS;EAC5B,OAAO7H,WAAW;AACpB,CAAC,CAACN,aAAa,CAAC;AAChB,SAAS6C,iBAAiBA,CAACP,WAAW,EAAE;EACtC,OAAOA,WAAW,CAAC8F,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACxC;AACA,eAAe9H,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}