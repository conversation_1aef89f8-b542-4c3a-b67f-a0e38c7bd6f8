export default class MinBinaryHeap {
    list: any[];
    compareFn: (a: any, b: any) => number;
    constructor(compareFn?: (a: any, b: any) => number);
    getLeft(index: any): number;
    getRight(index: any): number;
    getParent(index: any): number;
    isEmpty(): boolean;
    top(): any;
    delMin(): any;
    insert(value: any): boolean;
    moveUp(index: any): void;
    moveDown(index: any): void;
}
