import type { Color } from '../interfaces';
export declare function colorEqual(c0: Readonly<Color>, c1: Readonly<Color>): boolean;
export declare function colorCopy(dst: Color, src: Readonly<Color>): void;
export declare function colorNewCopy(src: Readonly<Color>): Color;
export declare function colorNewFromRGBA(r: number, g: number, b: number, a?: number): Color;
export declare const TransparentBlack: Color;
export declare const OpaqueBlack: Color;
export declare const TransparentWhite: Color;
export declare const OpaqueWhite: Color;
