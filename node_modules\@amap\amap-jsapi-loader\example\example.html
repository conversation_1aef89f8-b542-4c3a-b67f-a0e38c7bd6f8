<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>AMap JSAPI Loader</title>
    <style>
        html, body, #container {
            height: 100%;
            width: 100%;
            margin: 0;
        }
    </style>
</head>
<body>
<div id="container" tabindex="0"></div>

<script src="https://webapi.amap.com/loader.js"></script>
<script>
    AMapLoader.load({ //首次调用 load
        key:'你申请的高德开放平台 Web 端 key',//首次load key为必填
        version:'2.0',
        plugins:['AMap.Scale','AMap.ToolBar']
    }).then((AMap)=>{
        map = new AMap.Map('container');
        map.addControl(new AMap.Scale())
        map.addControl(new AMap.ToolBar())
        map.add(new AMap.Marker({
            position:map.getCenter()
        }));
    }).catch((e)=>{
        console.error(e);
    });
   
    AMapLoader.load({ //可多次调用load
        plugins:['AMap.MapType']
    }).then((AMap)=>{
        map.addControl(new AMap.MapType())
    }).catch((e)=>{
        console.error(e);
    });
   
</script>
</body>
</html>