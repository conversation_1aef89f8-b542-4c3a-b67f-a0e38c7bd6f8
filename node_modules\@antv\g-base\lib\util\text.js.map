{"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../src/util/text.ts"], "names": [], "mappings": ";;;AAAA,+BAA+C;AAC/C,yCAAkD;AAGlD;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,IAAY,EAAE,QAAgB,EAAE,UAAmB;IAC/E,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,eAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;KACrC;IACD,IAAI,SAAS,GAAG,CAAC,EAAE;QACjB,IAAM,SAAS,GAAG,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACxD,OAAO,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;KAC3D;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAVD,sCAUC;AAED;;;;GAIG;AACH,SAAgB,eAAe,CAAC,QAAgB,EAAE,UAAmB;IACnE,OAAO,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9D,CAAC;AAFD,0CAEC;AAED;;;;GAIG;AACH,SAAgB,YAAY,CAAC,IAAY,EAAE,IAAY;IACrD,IAAM,OAAO,GAAG,+BAAmB,EAAE,CAAC,CAAC,iBAAiB;IACxD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,4BAA4B;IAC5B,IAAI,YAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,EAAE;QAC9B,OAAO,KAAK,CAAC;KACd;IACD,OAAO,CAAC,IAAI,EAAE,CAAC;IACf,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,IAAI,eAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACzC,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjC,WAAI,CAAC,OAAO,EAAE,UAAC,OAAO;YACpB,IAAM,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;YACxD,IAAI,KAAK,GAAG,YAAY,EAAE;gBACxB,KAAK,GAAG,YAAY,CAAC;aACtB;QACH,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;KACzC;IACD,OAAO,CAAC,OAAO,EAAE,CAAC;IAClB,OAAO,KAAK,CAAC;AACf,CAAC;AAtBD,oCAsBC;AAED,SAAgB,YAAY,CAAC,KAAiB;IACpC,IAAA,QAAQ,GAAqD,KAAK,SAA1D,EAAE,UAAU,GAAyC,KAAK,WAA9C,EAAE,UAAU,GAA6B,KAAK,WAAlC,EAAE,SAAS,GAAkB,KAAK,UAAvB,EAAE,WAAW,GAAK,KAAK,YAAV,CAAW;IAC3E,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAK,QAAQ,OAAI,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5F,CAAC;AAHD,oCAGC"}