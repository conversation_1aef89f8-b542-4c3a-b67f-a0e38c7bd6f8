/// <reference types="@webgpu/types" />
import type { Binding<PERSON>, BindingsDescriptor, <PERSON><PERSON><PERSON>, BufferD<PERSON>criptor, ComputePass, ComputePipeline, ComputePipelineDescriptor, Device, DeviceLimits, InputLayout, InputLayoutDescriptor, Program, ProgramDescriptor, QueryPool, QueryPoolType, Readback, RenderBundle, RenderPass, RenderPassDescriptor, RenderPipeline, RenderPipelineDescriptor, RenderTarget, RenderTargetDescriptor, Resource, Sampler, SamplerBinding, SamplerDescriptor, SwapChain, Texture, TextureDescriptor, VendorInfo } from '../api';
import { ClipSpaceNearZ, Format, ViewportOrigin } from '../api';
import type { glsl_compile as glsl_compile_, WGSLComposer } from '../../rust/pkg/glsl_wgsl_compiler';
import type { IDevice_WebGPU, TextureSharedDescriptor, TextureShared_WebGPU } from './interfaces';
export declare class Device_WebGPU implements <PERSON>wap<PERSON>hain, IDevice_WebGPU {
    private swapChainWidth;
    private swapChainHeight;
    private swapChainFormat;
    private swapChainTextureUsage;
    private _resourceUniqueId;
    private renderPassPool;
    private computePassPool;
    private frameCommandEncoderPool;
    private fallbackTexture2D;
    private fallbackTexture2DDepth;
    private fallbackTexture2DArray;
    private fallbackTexture3D;
    private fallbackTextureCube;
    private fallbackSamplerFiltering;
    private fallbackSamplerComparison;
    private featureTextureCompressionBC;
    readonly platformString: string;
    readonly glslVersion = "#version 440";
    readonly explicitBindingLocations = true;
    readonly separateSamplerTextures = true;
    readonly viewportOrigin = ViewportOrigin.UPPER_LEFT;
    readonly clipSpaceNearZ = ClipSpaceNearZ.ZERO;
    readonly supportsSyncPipelineCompilation: boolean;
    readonly supportMRT: boolean;
    device: GPUDevice;
    private canvas;
    private canvasContext;
    private glsl_compile;
    private WGSLComposer;
    constructor(adapter: GPUAdapter, device: GPUDevice, canvas: HTMLCanvasElement | OffscreenCanvas, canvasContext: GPUCanvasContext, glsl_compile: typeof glsl_compile_, wGSLComposer: WGSLComposer);
    destroy(): void;
    configureSwapChain(width: number, height: number): void;
    getOnscreenTexture(): Texture;
    getDevice(): Device;
    getCanvas(): HTMLCanvasElement | OffscreenCanvas;
    beginFrame(): void;
    endFrame(): void;
    private getNextUniqueId;
    createBuffer(descriptor: BufferDescriptor): Buffer;
    createTexture(descriptor: TextureDescriptor): Texture;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpudevice-createsampler
     * @see https://www.w3.org/TR/webgpu/#GPUSamplerDescriptor
     */
    createSampler(descriptor: SamplerDescriptor): Sampler;
    createRenderTarget(descriptor: RenderTargetDescriptor): RenderTarget;
    createRenderTargetFromTexture(texture: Texture): RenderTarget;
    createProgram(descriptor: ProgramDescriptor): Program;
    private createProgramSimple;
    createTextureShared(descriptor: TextureSharedDescriptor, texture: TextureShared_WebGPU, skipCreate: boolean): void;
    getFallbackSampler(samplerEntry: SamplerBinding): Sampler;
    getFallbackTexture(samplerEntry: SamplerBinding): Texture;
    private createFallbackTexture;
    createBindings(descriptor: BindingsDescriptor): Bindings;
    createInputLayout(descriptor: InputLayoutDescriptor): InputLayout;
    createComputePipeline(descriptor: ComputePipelineDescriptor): ComputePipeline;
    createRenderPipeline(descriptor: RenderPipelineDescriptor): RenderPipeline;
    createQueryPool(type: QueryPoolType, elemCount: number): QueryPool;
    private createRenderPipelineInternal;
    createReadback(): Readback;
    createRenderBundle(): RenderBundle;
    createRenderPass(renderPassDescriptor: RenderPassDescriptor): RenderPass;
    createComputePass(): ComputePass;
    submitPass(_pass: RenderPass | ComputePass): void;
    copySubTexture2D(dst_: Texture, dstX: number, dstY: number, src_: Texture, srcX: number, srcY: number, depthOrArrayLayers?: number): void;
    queryLimits(): DeviceLimits;
    queryTextureFormatSupported(format: Format, width: number, height: number): boolean;
    queryPlatformAvailable(): boolean;
    queryVendorInfo(): VendorInfo;
    queryRenderPass(o: RenderPass): Readonly<RenderPassDescriptor>;
    queryRenderTarget(o: RenderTarget): Readonly<RenderTargetDescriptor>;
    setResourceName(o: Resource, s: string): void;
    setResourceLeakCheck(o: Resource, v: boolean): void;
    checkForLeaks(): void;
    programPatched(o: Program): void;
    pipelineQueryReady(o: RenderPipeline): boolean;
    pipelineForceReady(o: RenderPipeline): void;
}
