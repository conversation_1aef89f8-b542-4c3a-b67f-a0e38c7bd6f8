{"ast": null, "code": "export var regionData = [{\n  name: '北京市',\n  children: [{\n    name: '北京市',\n    children: [{\n      name: '东城区'\n    }, {\n      name: '西城区'\n    }, {\n      name: '朝阳区'\n    }, {\n      name: '丰台区'\n    }, {\n      name: '石景山区'\n    }, {\n      name: '海淀区'\n    }, {\n      name: '门头沟区'\n    }, {\n      name: '房山区'\n    }, {\n      name: '通州区'\n    }, {\n      name: '顺义区'\n    }, {\n      name: '昌平区'\n    }, {\n      name: '大兴区'\n    }, {\n      name: '怀柔区'\n    }, {\n      name: '平谷区'\n    }, {\n      name: '密云区'\n    }, {\n      name: '延庆区'\n    }]\n  }]\n}, {\n  name: '天津市',\n  children: [{\n    name: '天津市',\n    children: [{\n      name: '和平区'\n    }, {\n      name: '河东区'\n    }, {\n      name: '河西区'\n    }, {\n      name: '南开区'\n    }, {\n      name: '河北区'\n    }, {\n      name: '红桥区'\n    }, {\n      name: '东丽区'\n    }, {\n      name: '西青区'\n    }, {\n      name: '津南区'\n    }, {\n      name: '北辰区'\n    }, {\n      name: '武清区'\n    }, {\n      name: '宝坻区'\n    }, {\n      name: '滨海新区'\n    }, {\n      name: '宁河区'\n    }, {\n      name: '静海区'\n    }, {\n      name: '蓟州区'\n    }]\n  }]\n}, {\n  name: '河北省',\n  children: [{\n    name: '石家庄市',\n    children: [{\n      name: '长安区'\n    }, {\n      name: '桥西区'\n    }, {\n      name: '新华区'\n    }, {\n      name: '井陉矿区'\n    }, {\n      name: '裕华区'\n    }, {\n      name: '藁城区'\n    }, {\n      name: '鹿泉区'\n    }, {\n      name: '栾城区'\n    }]\n  }, {\n    name: '唐山市',\n    children: [{\n      name: '路南区'\n    }, {\n      name: '路北区'\n    }, {\n      name: '古冶区'\n    }, {\n      name: '开平区'\n    }, {\n      name: '丰南区'\n    }, {\n      name: '丰润区'\n    }, {\n      name: '曹妃甸区'\n    }]\n  }, {\n    name: '秦皇岛市',\n    children: [{\n      name: '海港区'\n    }, {\n      name: '山海关区'\n    }, {\n      name: '北戴河区'\n    }, {\n      name: '抚宁区'\n    }]\n  }]\n}, {\n  name: '山西省',\n  children: [{\n    name: '太原市',\n    children: [{\n      name: '小店区'\n    }, {\n      name: '迎泽区'\n    }, {\n      name: '杏花岭区'\n    }, {\n      name: '尖草坪区'\n    }, {\n      name: '万柏林区'\n    }, {\n      name: '晋源区'\n    }]\n  }, {\n    name: '大同市',\n    children: [{\n      name: '平城区'\n    }, {\n      name: '云冈区'\n    }, {\n      name: '新荣区'\n    }, {\n      name: '云州区'\n    }]\n  }]\n}, {\n  name: '内蒙古自治区',\n  children: [{\n    name: '呼和浩特市',\n    children: [{\n      name: '新城区'\n    }, {\n      name: '回民区'\n    }, {\n      name: '玉泉区'\n    }, {\n      name: '赛罕区'\n    }, {\n      name: '土默特左旗'\n    }, {\n      name: '托克托县'\n    }]\n  }, {\n    name: '包头市',\n    children: [{\n      name: '东河区'\n    }, {\n      name: '昆都仑区'\n    }, {\n      name: '青山区'\n    }, {\n      name: '石拐区'\n    }, {\n      name: '白云鄂博矿区'\n    }, {\n      name: '九原区'\n    }]\n  }]\n}, {\n  name: '辽宁省',\n  children: [{\n    name: '沈阳市',\n    children: [{\n      name: '和平区'\n    }, {\n      name: '沈河区'\n    }, {\n      name: '大东区'\n    }, {\n      name: '皇姑区'\n    }, {\n      name: '铁西区'\n    }, {\n      name: '苏家屯区'\n    }, {\n      name: '浑南区'\n    }, {\n      name: '于洪区'\n    }]\n  }, {\n    name: '大连市',\n    children: [{\n      name: '中山区'\n    }, {\n      name: '西岗区'\n    }, {\n      name: '沙河口区'\n    }, {\n      name: '甘井子区'\n    }, {\n      name: '旅顺口区'\n    }, {\n      name: '金州区'\n    }, {\n      name: '普兰店区'\n    }]\n  }]\n}, {\n  name: '吉林省',\n  children: [{\n    name: '长春市',\n    children: [{\n      name: '南关区'\n    }, {\n      name: '宽城区'\n    }, {\n      name: '朝阳区'\n    }, {\n      name: '二道区'\n    }, {\n      name: '绿园区'\n    }, {\n      name: '双阳区'\n    }, {\n      name: '九台区'\n    }]\n  }, {\n    name: '吉林市',\n    children: [{\n      name: '昌邑区'\n    }, {\n      name: '龙潭区'\n    }, {\n      name: '船营区'\n    }, {\n      name: '丰满区'\n    }]\n  }]\n}, {\n  name: '黑龙江省',\n  children: [{\n    name: '哈尔滨市',\n    children: [{\n      name: '道里区'\n    }, {\n      name: '南岗区'\n    }, {\n      name: '道外区'\n    }, {\n      name: '平房区'\n    }, {\n      name: '松北区'\n    }, {\n      name: '香坊区'\n    }, {\n      name: '呼兰区'\n    }, {\n      name: '阿城区'\n    }]\n  }, {\n    name: '齐齐哈尔市',\n    children: [{\n      name: '龙沙区'\n    }, {\n      name: '建华区'\n    }, {\n      name: '铁锋区'\n    }, {\n      name: '昂昂溪区'\n    }, {\n      name: '富拉尔基区'\n    }]\n  }]\n}, {\n  name: '上海市',\n  children: [{\n    name: '上海市',\n    children: [{\n      name: '黄浦区'\n    }, {\n      name: '徐汇区'\n    }, {\n      name: '长宁区'\n    }, {\n      name: '静安区'\n    }, {\n      name: '普陀区'\n    }, {\n      name: '虹口区'\n    }, {\n      name: '杨浦区'\n    }, {\n      name: '闵行区'\n    }, {\n      name: '宝山区'\n    }, {\n      name: '嘉定区'\n    }, {\n      name: '浦东新区'\n    }, {\n      name: '金山区'\n    }, {\n      name: '松江区'\n    }, {\n      name: '青浦区'\n    }, {\n      name: '奉贤区'\n    }, {\n      name: '崇明区'\n    }]\n  }]\n}, {\n  name: '浙江省',\n  children: [{\n    name: '杭州市',\n    children: [{\n      name: '上城区'\n    }, {\n      name: '拱墅区'\n    }, {\n      name: '西湖区'\n    }, {\n      name: '滨江区'\n    }, {\n      name: '萧山区'\n    }, {\n      name: '余杭区'\n    }, {\n      name: '玄武区'\n    }, {\n      name: '秦淮区'\n    }, {\n      name: '建邺区'\n    }, {\n      name: '鼓楼区'\n    }, {\n      name: '浦口区'\n    }, {\n      name: '栖霞区'\n    }, {\n      name: '雨花台区'\n    }, {\n      name: '江宁区'\n    }, {\n      name: '六合区'\n    }]\n  }, {\n    name: '苏州市',\n    children: [{\n      name: '虎丘区'\n    }, {\n      name: '吴中区'\n    }, {\n      name: '相城区'\n    }, {\n      name: '姑苏区'\n    }, {\n      name: '吴江区'\n    }]\n  }, {\n    name: '无锡市',\n    children: [{\n      name: '锡山区'\n    }, {\n      name: '惠山区'\n    }, {\n      name: '滨湖区'\n    }, {\n      name: '梁溪区'\n    }, {\n      name: '新吴区'\n    }]\n  }]\n}, {\n  name: '浙江省',\n  children: [{\n    name: '杭州市',\n    children: [{\n      name: '上城区'\n    }, {\n      name: '下城区'\n    }, {\n      name: '江干区'\n    }, {\n      name: '拱墅区'\n    }, {\n      name: '西湖区'\n    }, {\n      name: '滨江区'\n    }, {\n      name: '萧山区'\n    }, {\n      name: '余杭区'\n    }, {\n      name: '富阳区'\n    }]\n  }, {\n    name: '宁波市',\n    children: [{\n      name: '海曙区'\n    }, {\n      name: '江北区'\n    }, {\n      name: '北仑区'\n    }, {\n      name: '镇海区'\n    }, {\n      name: '鄞州区'\n    }]\n  }, {\n    name: '温州市',\n    children: [{\n      name: '鹿城区'\n    }, {\n      name: '龙湾区'\n    }, {\n      name: '瓯海区'\n    }, {\n      name: '洞头区'\n    }]\n  }]\n}, {\n  name: '安徽省',\n  children: [{\n    name: '合肥市',\n    children: [{\n      name: '瑶海区'\n    }, {\n      name: '庐阳区'\n    }, {\n      name: '蜀山区'\n    }, {\n      name: '包河区'\n    }, {\n      name: '经开区'\n    }]\n  }, {\n    name: '芜湖市',\n    children: [{\n      name: '镜湖区'\n    }, {\n      name: '弋江区'\n    }, {\n      name: '鸠江区'\n    }, {\n      name: '湾沚区'\n    }]\n  }]\n}, {\n  name: '福建省',\n  children: [{\n    name: '福州市',\n    children: [{\n      name: '鼓楼区'\n    }, {\n      name: '台江区'\n    }, {\n      name: '仓山区'\n    }, {\n      name: '马尾区'\n    }, {\n      name: '晋安区'\n    }, {\n      name: '长乐区'\n    }]\n  }, {\n    name: '厦门市',\n    children: [{\n      name: '思明区'\n    }, {\n      name: '海沧区'\n    }, {\n      name: '湖里区'\n    }, {\n      name: '集美区'\n    }, {\n      name: '同安区'\n    }, {\n      name: '翔安区'\n    }]\n  }]\n}, {\n  name: '江西省',\n  children: [{\n    name: '南昌市',\n    children: [{\n      name: '东湖区'\n    }, {\n      name: '西湖区'\n    }, {\n      name: '青云谱区'\n    }, {\n      name: '湾里区'\n    }, {\n      name: '青山湖区'\n    }, {\n      name: '新建区'\n    }]\n  }, {\n    name: '赣州市',\n    children: [{\n      name: '章贡区'\n    }, {\n      name: '南康区'\n    }, {\n      name: '赣县区'\n    }]\n  }]\n}, {\n  name: '山东省',\n  children: [{\n    name: '济南市',\n    children: [{\n      name: '历下区'\n    }, {\n      name: '市中区'\n    }, {\n      name: '槐荫区'\n    }, {\n      name: '天桥区'\n    }, {\n      name: '历城区'\n    }, {\n      name: '长清区'\n    }, {\n      name: '章丘区'\n    }]\n  }, {\n    name: '青岛市',\n    children: [{\n      name: '市南区'\n    }, {\n      name: '市北区'\n    }, {\n      name: '黄岛区'\n    }, {\n      name: '崂山区'\n    }, {\n      name: '李沧区'\n    }, {\n      name: '城阳区'\n    }]\n  }]\n}, {\n  name: '河南省',\n  children: [{\n    name: '郑州市',\n    children: [{\n      name: '中原区'\n    }, {\n      name: '二七区'\n    }, {\n      name: '管城回族区'\n    }, {\n      name: '金水区'\n    }, {\n      name: '上街区'\n    }, {\n      name: '惠济区'\n    }, {\n      name: '中牟县'\n    }]\n  }, {\n    name: '洛阳市',\n    children: [{\n      name: '老城区'\n    }, {\n      name: '西工区'\n    }, {\n      name: '瀍河回族区'\n    }, {\n      name: '涧西区'\n    }, {\n      name: '吉利区'\n    }, {\n      name: '洛龙区'\n    }]\n  }]\n}, {\n  name: '湖北省',\n  children: [{\n    name: '武汉市',\n    children: [{\n      name: '江岸区'\n    }, {\n      name: '江汉区'\n    }, {\n      name: '硚口区'\n    }, {\n      name: '汉阳区'\n    }, {\n      name: '武昌区'\n    }, {\n      name: '青山区'\n    }, {\n      name: '洪山区'\n    }, {\n      name: '东西湖区'\n    }, {\n      name: '汉南区'\n    }, {\n      name: '蔡甸区'\n    }, {\n      name: '江夏区'\n    }, {\n      name: '黄陂区'\n    }, {\n      name: '新洲区'\n    }]\n  }, {\n    name: '宜昌市',\n    children: [{\n      name: '西陵区'\n    }, {\n      name: '伍家岗区'\n    }, {\n      name: '点军区'\n    }, {\n      name: '猇亭区'\n    }, {\n      name: '夷陵区'\n    }]\n  }]\n}, {\n  name: '湖南省',\n  children: [{\n    name: '长沙市',\n    children: [{\n      name: '芙蓉区'\n    }, {\n      name: '天心区'\n    }, {\n      name: '岳麓区'\n    }, {\n      name: '开福区'\n    }, {\n      name: '雨花区'\n    }, {\n      name: '望城区'\n    }, {\n      name: '长沙县'\n    }]\n  }, {\n    name: '株洲市',\n    children: [{\n      name: '荷塘区'\n    }, {\n      name: '芦淞区'\n    }, {\n      name: '石峰区'\n    }, {\n      name: '天元区'\n    }, {\n      name: '渌口区'\n    }]\n  }]\n}, {\n  name: '广东省',\n  children: [{\n    name: '广州市',\n    children: [{\n      name: '荔湾区'\n    }, {\n      name: '越秀区'\n    }, {\n      name: '海珠区'\n    }, {\n      name: '天河区'\n    }, {\n      name: '白云区'\n    }, {\n      name: '黄埔区'\n    }, {\n      name: '番禺区'\n    }, {\n      name: '花都区'\n    }, {\n      name: '南沙区'\n    }, {\n      name: '从���区'\n    }, {\n      name: '增城区'\n    }]\n  }, {\n    name: '深圳市',\n    children: [{\n      name: '福田区'\n    }, {\n      name: '罗湖区'\n    }, {\n      name: '南山区'\n    }, {\n      name: '宝安区'\n    }, {\n      name: '龙岗区'\n    }, {\n      name: '盐田区'\n    }, {\n      name: '龙华区'\n    }, {\n      name: '坪山区'\n    }, {\n      name: '光明区'\n    }]\n  }, {\n    name: '珠海市',\n    children: [{\n      name: '香洲区'\n    }, {\n      name: '斗门区'\n    }, {\n      name: '金湾区'\n    }]\n  }]\n}, {\n  name: '广西壮族自治区',\n  children: [{\n    name: '南宁市',\n    children: [{\n      name: '兴宁区'\n    }, {\n      name: '青秀区'\n    }, {\n      name: '江南区'\n    }, {\n      name: '西乡塘区'\n    }, {\n      name: '良庆区'\n    }, {\n      name: '邕宁区'\n    }, {\n      name: '武鸣区'\n    }]\n  }, {\n    name: '桂林市',\n    children: [{\n      name: '秀峰区'\n    }, {\n      name: '叠彩区'\n    }, {\n      name: '象山区'\n    }, {\n      name: '七星区'\n    }, {\n      name: '雁山区'\n    }]\n  }]\n}, {\n  name: '海南省',\n  children: [{\n    name: '海口市',\n    children: [{\n      name: '秀英区'\n    }, {\n      name: '龙华区'\n    }, {\n      name: '琼山区'\n    }, {\n      name: '美兰区'\n    }]\n  }, {\n    name: '三亚市',\n    children: [{\n      name: '海棠区'\n    }, {\n      name: '吉阳区'\n    }, {\n      name: '天涯区'\n    }, {\n      name: '崖州区'\n    }]\n  }]\n}, {\n  name: '重庆市',\n  children: [{\n    name: '重庆市',\n    children: [{\n      name: '渝中区'\n    }, {\n      name: '江北区'\n    }, {\n      name: '南岸区'\n    }, {\n      name: '沙坪坝区'\n    }, {\n      name: '九龙坡区'\n    }, {\n      name: '大渡口区'\n    }, {\n      name: '渝北区'\n    }, {\n      name: '巴南区'\n    }, {\n      name: '北碚区'\n    }, {\n      name: '綦江区'\n    }, {\n      name: '长寿区'\n    }, {\n      name: '江津区'\n    }, {\n      name: '合川区'\n    }, {\n      name: '永川区'\n    }, {\n      name: '南川区'\n    }]\n  }]\n}, {\n  name: '四川省',\n  children: [{\n    name: '成都市',\n    children: [{\n      name: '锦江区'\n    }, {\n      name: '青羊区'\n    }, {\n      name: '金牛区'\n    }, {\n      name: '武侯区'\n    }, {\n      name: '成华区'\n    }, {\n      name: '龙泉驿区'\n    }, {\n      name: '青白江区'\n    }, {\n      name: '新都区'\n    }, {\n      name: '温江区'\n    }, {\n      name: '双流区'\n    }, {\n      name: '郫都区'\n    }]\n  }, {\n    name: '绵阳市',\n    children: [{\n      name: '涪城区'\n    }, {\n      name: '游仙区'\n    }, {\n      name: '安州区'\n    }]\n  }, {\n    name: '乐山市',\n    children: [{\n      name: '市中区'\n    }, {\n      name: '沙湾区'\n    }, {\n      name: '五通桥区'\n    }, {\n      name: '金口河区'\n    }]\n  }]\n}, {\n  name: '贵州省',\n  children: [{\n    name: '贵阳市',\n    children: [{\n      name: '南明区'\n    }, {\n      name: '云岩区'\n    }, {\n      name: '花溪区'\n    }, {\n      name: '乌当区'\n    }, {\n      name: '白云区'\n    }, {\n      name: '观山湖区'\n    }]\n  }, {\n    name: '遵义市',\n    children: [{\n      name: '红花岗区'\n    }, {\n      name: '汇川区'\n    }, {\n      name: '播州区'\n    }]\n  }]\n}, {\n  name: '云南省',\n  children: [{\n    name: '昆明市',\n    children: [{\n      name: '五华区'\n    }, {\n      name: '盘龙区'\n    }, {\n      name: '官渡区'\n    }, {\n      name: '西山区'\n    }, {\n      name: '东川区'\n    }, {\n      name: '呈贡区'\n    }, {\n      name: '晋宁区'\n    }]\n  }, {\n    name: '大理白族自治州',\n    children: [{\n      name: '大理市'\n    }, {\n      name: '漾濞县'\n    }, {\n      name: '祥云县'\n    }, {\n      name: '宾川县'\n    }]\n  }]\n}, {\n  name: '西藏自治区',\n  children: [{\n    name: '拉萨市',\n    children: [{\n      name: '城关区'\n    }, {\n      name: '堆龙德庆区'\n    }, {\n      name: '达孜区'\n    }, {\n      name: '林周县'\n    }]\n  }, {\n    name: '日喀则市',\n    children: [{\n      name: '桑珠孜区'\n    }, {\n      name: '南木林县'\n    }, {\n      name: '江孜县'\n    }]\n  }]\n}, {\n  name: '陕西省',\n  children: [{\n    name: '西安市',\n    children: [{\n      name: '新城区'\n    }, {\n      name: '碑林区'\n    }, {\n      name: '莲湖区'\n    }, {\n      name: '灞桥区'\n    }, {\n      name: '未央区'\n    }, {\n      name: '雁塔区'\n    }, {\n      name: '阎良区'\n    }, {\n      name: '临潼区'\n    }, {\n      name: '长安区'\n    }, {\n      name: '高陵区'\n    }, {\n      name: '鄠邑区'\n    }, {\n      name: '蓝田县'\n    }, {\n      name: '周至县'\n    }]\n  }, {\n    name: '咸阳市',\n    children: [{\n      name: '秦都区'\n    }, {\n      name: '杨陵区'\n    }, {\n      name: '渭城区'\n    }, {\n      name: '三原县'\n    }, {\n      name: '泾阳县'\n    }, {\n      name: '乾县'\n    }, {\n      name: '礼泉县'\n    }, {\n      name: '永寿县'\n    }, {\n      name: '彬州市'\n    }, {\n      name: '长武县'\n    }, {\n      name: '旬邑县'\n    }, {\n      name: '淳化县'\n    }, {\n      name: '武功县'\n    }, {\n      name: '兴平市'\n    }]\n  }, {\n    name: '宝鸡市',\n    children: [{\n      name: '渭滨区'\n    }, {\n      name: '金台区'\n    }, {\n      name: '陈仓区'\n    }, {\n      name: '凤翔区'\n    }, {\n      name: '岐山县'\n    }, {\n      name: '扶风县'\n    }, {\n      name: '眉县'\n    }, {\n      name: '陇县'\n    }, {\n      name: '千阳县'\n    }, {\n      name: '麟游县'\n    }, {\n      name: '凤县'\n    }, {\n      name: '太白县'\n    }]\n  }, {\n    name: '渭南市',\n    children: [{\n      name: '临渭区'\n    }, {\n      name: '华州区'\n    }, {\n      name: '潼关县'\n    }, {\n      name: '大荔县'\n    }, {\n      name: '合阳县'\n    }, {\n      name: '澄城县'\n    }, {\n      name: '蒲城县'\n    }, {\n      name: '白水县'\n    }, {\n      name: '富平县'\n    }, {\n      name: '韩城市'\n    }, {\n      name: '华阴市'\n    }]\n  }, {\n    name: '延安市',\n    children: [{\n      name: '宝塔区'\n    }, {\n      name: '安塞区'\n    }, {\n      name: '延长县'\n    }, {\n      name: '延川县'\n    }, {\n      name: '子长市'\n    }, {\n      name: '志丹县'\n    }, {\n      name: '吴起县'\n    }, {\n      name: '甘泉县'\n    }, {\n      name: '富县'\n    }, {\n      name: '洛川县'\n    }, {\n      name: '宜川县'\n    }, {\n      name: '黄龙县'\n    }, {\n      name: '黄陵县'\n    }]\n  }, {\n    name: '汉中市',\n    children: [{\n      name: '汉台区'\n    }, {\n      name: '南郑区'\n    }, {\n      name: '城固县'\n    }, {\n      name: '洋县'\n    }, {\n      name: '西乡县'\n    }, {\n      name: '勉县'\n    }, {\n      name: '宁强县'\n    }, {\n      name: '略阳县'\n    }, {\n      name: '镇巴县'\n    }, {\n      name: '留坝县'\n    }, {\n      name: '佛坪县'\n    }]\n  }, {\n    name: '榆林市',\n    children: [{\n      name: '榆阳区'\n    }, {\n      name: '横山区'\n    }, {\n      name: '神木市'\n    }, {\n      name: '府谷县'\n    }, {\n      name: '靖边县'\n    }, {\n      name: '定边县'\n    }, {\n      name: '绥德县'\n    }, {\n      name: '米脂县'\n    }, {\n      name: '佳县'\n    }, {\n      name: '吴堡县'\n    }, {\n      name: '清涧县'\n    }, {\n      name: '子洲县'\n    }]\n  }, {\n    name: '安康市',\n    children: [{\n      name: '汉滨区'\n    }, {\n      name: '汉阴县'\n    }, {\n      name: '石泉县'\n    }, {\n      name: '宁陕县'\n    }, {\n      name: '紫阳县'\n    }, {\n      name: '岚皋县'\n    }, {\n      name: '平利县'\n    }, {\n      name: '镇坪县'\n    }, {\n      name: '旬阳市'\n    }, {\n      name: '白河县'\n    }]\n  }, {\n    name: '商洛市',\n    children: [{\n      name: '商州区'\n    }, {\n      name: '洛南县'\n    }, {\n      name: '丹凤县'\n    }, {\n      name: '商南县'\n    }, {\n      name: '山阳县'\n    }, {\n      name: '镇安县'\n    }, {\n      name: '柞水县'\n    }]\n  }]\n}, {\n  name: '甘肃省',\n  children: [{\n    name: '兰州市',\n    children: [{\n      name: '城关区'\n    }, {\n      name: '七里河区'\n    }, {\n      name: '西固区'\n    }, {\n      name: '安宁区'\n    }, {\n      name: '红古区'\n    }]\n  }, {\n    name: '天水市',\n    children: [{\n      name: '秦州区'\n    }, {\n      name: '麦积区'\n    }]\n  }]\n}, {\n  name: '青海省',\n  children: [{\n    name: '西宁市',\n    children: [{\n      name: '城东区'\n    }, {\n      name: '城中区'\n    }, {\n      name: '城西区'\n    }, {\n      name: '城北区'\n    }, {\n      name: '大通回族土族自治县'\n    }]\n  }, {\n    name: '海东市',\n    children: [{\n      name: '乐都区'\n    }, {\n      name: '平安区'\n    }]\n  }]\n}, {\n  name: '宁夏回族自治区',\n  children: [{\n    name: '银川市',\n    children: [{\n      name: '兴庆区'\n    }, {\n      name: '西夏区'\n    }, {\n      name: '金凤区'\n    }, {\n      name: '永宁县'\n    }, {\n      name: '贺兰县'\n    }]\n  }, {\n    name: '石嘴山市',\n    children: [{\n      name: '大武口区'\n    }, {\n      name: '惠农区'\n    }, {\n      name: '平罗县'\n    }]\n  }]\n}, {\n  name: '新疆维吾尔自治区',\n  children: [{\n    name: '乌鲁木齐市',\n    children: [{\n      name: '天山区'\n    }, {\n      name: '沙依巴克区'\n    }, {\n      name: '新市区'\n    }, {\n      name: '水磨沟区'\n    }, {\n      name: '头屯河区'\n    }, {\n      name: '达坂城区'\n    }, {\n      name: '米东区'\n    }]\n  }, {\n    name: '克拉玛依市',\n    children: [{\n      name: '独山子区'\n    }, {\n      name: '克拉玛依区'\n    }, {\n      name: '白碱滩区'\n    }, {\n      name: '乌尔禾区'\n    }]\n  }]\n}];", "map": {"version": 3, "names": ["regionData", "name", "children"], "sources": ["E:/新项目/整理6/adminweb/src/config/region-data.js"], "sourcesContent": ["export const regionData = [\r\n  {\r\n    name: '北京市',\r\n    children: [{\r\n      name: '北京市',\r\n      children: [\r\n        { name: '东城区' },\r\n        { name: '西城区' },\r\n        { name: '朝阳区' },\r\n        { name: '丰台区' },\r\n        { name: '石景山区' },\r\n        { name: '海淀区' },\r\n        { name: '门头沟区' },\r\n        { name: '房山区' },\r\n        { name: '通州区' },\r\n        { name: '顺义区' },\r\n        { name: '昌平区' },\r\n        { name: '大兴区' },\r\n        { name: '怀柔区' },\r\n        { name: '平谷区' },\r\n        { name: '密云区' },\r\n        { name: '延庆区' }\r\n      ]\r\n    }]\r\n  },\r\n  {\r\n    name: '天津市',\r\n    children: [{\r\n      name: '天津市',\r\n      children: [\r\n        { name: '和平区' },\r\n        { name: '河东区' },\r\n        { name: '河西区' },\r\n        { name: '南开区' },\r\n        { name: '河北区' },\r\n        { name: '红桥区' },\r\n        { name: '东丽区' },\r\n        { name: '西青区' },\r\n        { name: '津南区' },\r\n        { name: '北辰区' },\r\n        { name: '武清区' },\r\n        { name: '宝坻区' },\r\n        { name: '滨海新区' },\r\n        { name: '宁河区' },\r\n        { name: '静海区' },\r\n        { name: '蓟州区' }\r\n      ]\r\n    }]\r\n  },\r\n  {\r\n    name: '河北省',\r\n    children: [\r\n      {\r\n        name: '石家庄市',\r\n        children: [\r\n          { name: '长安区' },\r\n          { name: '桥西区' },\r\n          { name: '新华区' },\r\n          { name: '井陉矿区' },\r\n          { name: '裕华区' },\r\n          { name: '藁城区' },\r\n          { name: '鹿泉区' },\r\n          { name: '栾城区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '唐山市',\r\n        children: [\r\n          { name: '路南区' },\r\n          { name: '路北区' },\r\n          { name: '古冶区' },\r\n          { name: '开平区' },\r\n          { name: '丰南区' },\r\n          { name: '丰润区' },\r\n          { name: '曹妃甸区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '秦皇岛市',\r\n        children: [\r\n          { name: '海港区' },\r\n          { name: '山海关区' },\r\n          { name: '北戴河区' },\r\n          { name: '抚宁区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '山西省',\r\n    children: [\r\n      {\r\n        name: '太原市',\r\n        children: [\r\n          { name: '小店区' },\r\n          { name: '迎泽区' },\r\n          { name: '杏花岭区' },\r\n          { name: '尖草坪区' },\r\n          { name: '万柏林区' },\r\n          { name: '晋源区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '大同市',\r\n        children: [\r\n          { name: '平城区' },\r\n          { name: '云冈区' },\r\n          { name: '新荣区' },\r\n          { name: '云州区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '内蒙古自治区',\r\n    children: [\r\n      {\r\n        name: '呼和浩特市',\r\n        children: [\r\n          { name: '新城区' },\r\n          { name: '回民区' },\r\n          { name: '玉泉区' },\r\n          { name: '赛罕区' },\r\n          { name: '土默特左旗' },\r\n          { name: '托克托县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '包头市',\r\n        children: [\r\n          { name: '东河区' },\r\n          { name: '昆都仑区' },\r\n          { name: '青山区' },\r\n          { name: '石拐区' },\r\n          { name: '白云鄂博矿区' },\r\n          { name: '九原区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '辽宁省',\r\n    children: [\r\n      {\r\n        name: '沈阳市',\r\n        children: [\r\n          { name: '和平区' },\r\n          { name: '沈河区' },\r\n          { name: '大东区' },\r\n          { name: '皇姑区' },\r\n          { name: '铁西区' },\r\n          { name: '苏家屯区' },\r\n          { name: '浑南区' },\r\n          { name: '于洪区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '大连市',\r\n        children: [\r\n          { name: '中山区' },\r\n          { name: '西岗区' },\r\n          { name: '沙河口区' },\r\n          { name: '甘井子区' },\r\n          { name: '旅顺口区' },\r\n          { name: '金州区' },\r\n          { name: '普兰店区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '吉林省',\r\n    children: [\r\n      {\r\n        name: '长春市',\r\n        children: [\r\n          { name: '南关区' },\r\n          { name: '宽城区' },\r\n          { name: '朝阳区' },\r\n          { name: '二道区' },\r\n          { name: '绿园区' },\r\n          { name: '双阳区' },\r\n          { name: '九台区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '吉林市',\r\n        children: [\r\n          { name: '昌邑区' },\r\n          { name: '龙潭区' },\r\n          { name: '船营区' },\r\n          { name: '丰满区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '黑龙江省',\r\n    children: [\r\n      {\r\n        name: '哈尔滨市',\r\n        children: [\r\n          { name: '道里区' },\r\n          { name: '南岗区' },\r\n          { name: '道外区' },\r\n          { name: '平房区' },\r\n          { name: '松北区' },\r\n          { name: '香坊区' },\r\n          { name: '呼兰区' },\r\n          { name: '阿城区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '齐齐哈尔市',\r\n        children: [\r\n          { name: '龙沙区' },\r\n          { name: '建华区' },\r\n          { name: '铁锋区' },\r\n          { name: '昂昂溪区' },\r\n          { name: '富拉尔基区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '上海市',\r\n    children: [{\r\n      name: '上海市',\r\n      children: [\r\n        { name: '黄浦区' },\r\n        { name: '徐汇区' },\r\n        { name: '长宁区' },\r\n        { name: '静安区' },\r\n        { name: '普陀区' },\r\n        { name: '虹口区' },\r\n        { name: '杨浦区' },\r\n        { name: '闵行区' },\r\n        { name: '宝山区' },\r\n        { name: '嘉定区' },\r\n        { name: '浦东新区' },\r\n        { name: '金山区' },\r\n        { name: '松江区' },\r\n        { name: '青浦区' },\r\n        { name: '奉贤区' },\r\n        { name: '崇明区' }\r\n      ]\r\n    }]\r\n  },\r\n  {\r\n    name: '浙江省',\r\n    children: [\r\n      {\r\n        name: '杭州市',\r\n        children: [\r\n          { name: '上城区' },\r\n          { name: '拱墅区' },\r\n          { name: '西湖区' },\r\n          { name: '滨江区' },\r\n          { name: '萧山区' },\r\n          { name: '余杭区' },\r\n          { name: '玄武区' },\r\n          { name: '秦淮区' },\r\n          { name: '建邺区' },\r\n          { name: '鼓楼区' },\r\n          { name: '浦口区' },\r\n          { name: '栖霞区' },\r\n          { name: '雨花台区' },\r\n          { name: '江宁区' },\r\n          { name: '六合区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '苏州市',\r\n        children: [\r\n          { name: '虎丘区' },\r\n          { name: '吴中区' },\r\n          { name: '相城区' },\r\n          { name: '姑苏区' },\r\n          { name: '吴江区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '无锡市',\r\n        children: [\r\n          { name: '锡山区' },\r\n          { name: '惠山区' },\r\n          { name: '滨湖区' },\r\n          { name: '梁溪区' },\r\n          { name: '新吴区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '浙江省',\r\n    children: [\r\n      {\r\n        name: '杭州市',\r\n        children: [\r\n          { name: '上城区' },\r\n          { name: '下城区' },\r\n          { name: '江干区' },\r\n          { name: '拱墅区' },\r\n          { name: '西湖区' },\r\n          { name: '滨江区' },\r\n          { name: '萧山区' },\r\n          { name: '余杭区' },\r\n          { name: '富阳区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '宁波市',\r\n        children: [\r\n          { name: '海曙区' },\r\n          { name: '江北区' },\r\n          { name: '北仑区' },\r\n          { name: '镇海区' },\r\n          { name: '鄞州区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '温州市',\r\n        children: [\r\n          { name: '鹿城区' },\r\n          { name: '龙湾区' },\r\n          { name: '瓯海区' },\r\n          { name: '洞头区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '安徽省',\r\n    children: [\r\n      {\r\n        name: '合肥市',\r\n        children: [\r\n          { name: '瑶海区' },\r\n          { name: '庐阳区' },\r\n          { name: '蜀山区' },\r\n          { name: '包河区' },\r\n          { name: '经开区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '芜湖市',\r\n        children: [\r\n          { name: '镜湖区' },\r\n          { name: '弋江区' },\r\n          { name: '鸠江区' },\r\n          { name: '湾沚区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '福建省',\r\n    children: [\r\n      {\r\n        name: '福州市',\r\n        children: [\r\n          { name: '鼓楼区' },\r\n          { name: '台江区' },\r\n          { name: '仓山区' },\r\n          { name: '马尾区' },\r\n          { name: '晋安区' },\r\n          { name: '长乐区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '厦门市',\r\n        children: [\r\n          { name: '思明区' },\r\n          { name: '海沧区' },\r\n          { name: '湖里区' },\r\n          { name: '集美区' },\r\n          { name: '同安区' },\r\n          { name: '翔安区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '江西省',\r\n    children: [\r\n      {\r\n        name: '南昌市',\r\n        children: [\r\n          { name: '东湖区' },\r\n          { name: '西湖区' },\r\n          { name: '青云谱区' },\r\n          { name: '湾里区' },\r\n          { name: '青山湖区' },\r\n          { name: '新建区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '赣州市',\r\n        children: [\r\n          { name: '章贡区' },\r\n          { name: '南康区' },\r\n          { name: '赣县区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '山东省',\r\n    children: [\r\n      {\r\n        name: '济南市',\r\n        children: [\r\n          { name: '历下区' },\r\n          { name: '市中区' },\r\n          { name: '槐荫区' },\r\n          { name: '天桥区' },\r\n          { name: '历城区' },\r\n          { name: '长清区' },\r\n          { name: '章丘区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '青岛市',\r\n        children: [\r\n          { name: '市南区' },\r\n          { name: '市北区' },\r\n          { name: '黄岛区' },\r\n          { name: '崂山区' },\r\n          { name: '李沧区' },\r\n          { name: '城阳区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '河南省',\r\n    children: [\r\n      {\r\n        name: '郑州市',\r\n        children: [\r\n          { name: '中原区' },\r\n          { name: '二七区' },\r\n          { name: '管城回族区' },\r\n          { name: '金水区' },\r\n          { name: '上街区' },\r\n          { name: '惠济区' },\r\n          { name: '中牟县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '洛阳市',\r\n        children: [\r\n          { name: '老城区' },\r\n          { name: '西工区' },\r\n          { name: '瀍河回族区' },\r\n          { name: '涧西区' },\r\n          { name: '吉利区' },\r\n          { name: '洛龙区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '湖北省',\r\n    children: [\r\n      {\r\n        name: '武汉市',\r\n        children: [\r\n          { name: '江岸区' },\r\n          { name: '江汉区' },\r\n          { name: '硚口区' },\r\n          { name: '汉阳区' },\r\n          { name: '武昌区' },\r\n          { name: '青山区' },\r\n          { name: '洪山区' },\r\n          { name: '东西湖区' },\r\n          { name: '汉南区' },\r\n          { name: '蔡甸区' },\r\n          { name: '江夏区' },\r\n          { name: '黄陂区' },\r\n          { name: '新洲区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '宜昌市',\r\n        children: [\r\n          { name: '西陵区' },\r\n          { name: '伍家岗区' },\r\n          { name: '点军区' },\r\n          { name: '猇亭区' },\r\n          { name: '夷陵区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '湖南省',\r\n    children: [\r\n      {\r\n        name: '长沙市',\r\n        children: [\r\n          { name: '芙蓉区' },\r\n          { name: '天心区' },\r\n          { name: '岳麓区' },\r\n          { name: '开福区' },\r\n          { name: '雨花区' },\r\n          { name: '望城区' },\r\n          { name: '长沙县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '株洲市',\r\n        children: [\r\n          { name: '荷塘区' },\r\n          { name: '芦淞区' },\r\n          { name: '石峰区' },\r\n          { name: '天元区' },\r\n          { name: '渌口区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '广东省',\r\n    children: [\r\n      {\r\n        name: '广州市',\r\n        children: [\r\n          { name: '荔湾区' },\r\n          { name: '越秀区' },\r\n          { name: '海珠区' },\r\n          { name: '天河区' },\r\n          { name: '白云区' },\r\n          { name: '黄埔区' },\r\n          { name: '番禺区' },\r\n          { name: '花都区' },\r\n          { name: '南沙区' },\r\n          { name: '从���区' },\r\n          { name: '增城区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '深圳市',\r\n        children: [\r\n          { name: '福田区' },\r\n          { name: '罗湖区' },\r\n          { name: '南山区' },\r\n          { name: '宝安区' },\r\n          { name: '龙岗区' },\r\n          { name: '盐田区' },\r\n          { name: '龙华区' },\r\n          { name: '坪山区' },\r\n          { name: '光明区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '珠海市',\r\n        children: [\r\n          { name: '香洲区' },\r\n          { name: '斗门区' },\r\n          { name: '金湾区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '广西壮族自治区',\r\n    children: [\r\n      {\r\n        name: '南宁市',\r\n        children: [\r\n          { name: '兴宁区' },\r\n          { name: '青秀区' },\r\n          { name: '江南区' },\r\n          { name: '西乡塘区' },\r\n          { name: '良庆区' },\r\n          { name: '邕宁区' },\r\n          { name: '武鸣区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '桂林市',\r\n        children: [\r\n          { name: '秀峰区' },\r\n          { name: '叠彩区' },\r\n          { name: '象山区' },\r\n          { name: '七星区' },\r\n          { name: '雁山区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '海南省',\r\n    children: [\r\n      {\r\n        name: '海口市',\r\n        children: [\r\n          { name: '秀英区' },\r\n          { name: '龙华区' },\r\n          { name: '琼山区' },\r\n          { name: '美兰区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '三亚市',\r\n        children: [\r\n          { name: '海棠区' },\r\n          { name: '吉阳区' },\r\n          { name: '天涯区' },\r\n          { name: '崖州区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '重庆市',\r\n    children: [{\r\n      name: '重庆市',\r\n      children: [\r\n        { name: '渝中区' },\r\n        { name: '江北区' },\r\n        { name: '南岸区' },\r\n        { name: '沙坪坝区' },\r\n        { name: '九龙坡区' },\r\n        { name: '大渡口区' },\r\n        { name: '渝北区' },\r\n        { name: '巴南区' },\r\n        { name: '北碚区' },\r\n        { name: '綦江区' },\r\n        { name: '长寿区' },\r\n        { name: '江津区' },\r\n        { name: '合川区' },\r\n        { name: '永川区' },\r\n        { name: '南川区' }\r\n      ]\r\n    }]\r\n  },\r\n  {\r\n    name: '四川省',\r\n    children: [\r\n      {\r\n        name: '成都市',\r\n        children: [\r\n          { name: '锦江区' },\r\n          { name: '青羊区' },\r\n          { name: '金牛区' },\r\n          { name: '武侯区' },\r\n          { name: '成华区' },\r\n          { name: '龙泉驿区' },\r\n          { name: '青白江区' },\r\n          { name: '新都区' },\r\n          { name: '温江区' },\r\n          { name: '双流区' },\r\n          { name: '郫都区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '绵阳市',\r\n        children: [\r\n          { name: '涪城区' },\r\n          { name: '游仙区' },\r\n          { name: '安州区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '乐山市',\r\n        children: [\r\n          { name: '市中区' },\r\n          { name: '沙湾区' },\r\n          { name: '五通桥区' },\r\n          { name: '金口河区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '贵州省',\r\n    children: [\r\n      {\r\n        name: '贵阳市',\r\n        children: [\r\n          { name: '南明区' },\r\n          { name: '云岩区' },\r\n          { name: '花溪区' },\r\n          { name: '乌当区' },\r\n          { name: '白云区' },\r\n          { name: '观山湖区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '遵义市',\r\n        children: [\r\n          { name: '红花岗区' },\r\n          { name: '汇川区' },\r\n          { name: '播州区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '云南省',\r\n    children: [\r\n      {\r\n        name: '昆明市',\r\n        children: [\r\n          { name: '五华区' },\r\n          { name: '盘龙区' },\r\n          { name: '官渡区' },\r\n          { name: '西山区' },\r\n          { name: '东川区' },\r\n          { name: '呈贡区' },\r\n          { name: '晋宁区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '大理白族自治州',\r\n        children: [\r\n          { name: '大理市' },\r\n          { name: '漾濞县' },\r\n          { name: '祥云县' },\r\n          { name: '宾川县' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '西藏自治区',\r\n    children: [\r\n      {\r\n        name: '拉萨市',\r\n        children: [\r\n          { name: '城关区' },\r\n          { name: '堆龙德庆区' },\r\n          { name: '达孜区' },\r\n          { name: '林周县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '日喀则市',\r\n        children: [\r\n          { name: '桑珠孜区' },\r\n          { name: '南木林县' },\r\n          { name: '江孜县' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '陕西省',\r\n    children: [\r\n      {\r\n        name: '西安市',\r\n        children: [\r\n          { name: '新城区' },\r\n          { name: '碑林区' },\r\n          { name: '莲湖区' },\r\n          { name: '灞桥区' },\r\n          { name: '未央区' },\r\n          { name: '雁塔区' },\r\n          { name: '阎良区' },\r\n          { name: '临潼区' },\r\n          { name: '长安区' },\r\n          { name: '高陵区' },\r\n          { name: '鄠邑区' },\r\n          { name: '蓝田县' },\r\n          { name: '周至县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '咸阳市',\r\n        children: [\r\n          { name: '秦都区' },\r\n          { name: '杨陵区' },\r\n          { name: '渭城区' },\r\n          { name: '三原县' },\r\n          { name: '泾阳县' },\r\n          { name: '乾县' },\r\n          { name: '礼泉县' },\r\n          { name: '永寿县' },\r\n          { name: '彬州市' },\r\n          { name: '长武县' },\r\n          { name: '旬邑县' },\r\n          { name: '淳化县' },\r\n          { name: '武功县' },\r\n          { name: '兴平市' }\r\n        ]\r\n      },\r\n      {\r\n        name: '宝鸡市',\r\n        children: [\r\n          { name: '渭滨区' },\r\n          { name: '金台区' },\r\n          { name: '陈仓区' },\r\n          { name: '凤翔区' },\r\n          { name: '岐山县' },\r\n          { name: '扶风县' },\r\n          { name: '眉县' },\r\n          { name: '陇县' },\r\n          { name: '千阳县' },\r\n          { name: '麟游县' },\r\n          { name: '凤县' },\r\n          { name: '太白县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '渭南市',\r\n        children: [\r\n          { name: '临渭区' },\r\n          { name: '华州区' },\r\n          { name: '潼关县' },\r\n          { name: '大荔县' },\r\n          { name: '合阳县' },\r\n          { name: '澄城县' },\r\n          { name: '蒲城县' },\r\n          { name: '白水县' },\r\n          { name: '富平县' },\r\n          { name: '韩城市' },\r\n          { name: '华阴市' }\r\n        ]\r\n      },\r\n      {\r\n        name: '延安市',\r\n        children: [\r\n          { name: '宝塔区' },\r\n          { name: '安塞区' },\r\n          { name: '延长县' },\r\n          { name: '延川县' },\r\n          { name: '子长市' },\r\n          { name: '志丹县' },\r\n          { name: '吴起县' },\r\n          { name: '甘泉县' },\r\n          { name: '富县' },\r\n          { name: '洛川县' },\r\n          { name: '宜川县' },\r\n          { name: '黄龙县' },\r\n          { name: '黄陵县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '汉中市',\r\n        children: [\r\n          { name: '汉台区' },\r\n          { name: '南郑区' },\r\n          { name: '城固县' },\r\n          { name: '洋县' },\r\n          { name: '西乡县' },\r\n          { name: '勉县' },\r\n          { name: '宁强县' },\r\n          { name: '略阳县' },\r\n          { name: '镇巴县' },\r\n          { name: '留坝县' },\r\n          { name: '佛坪县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '榆林市',\r\n        children: [\r\n          { name: '榆阳区' },\r\n          { name: '横山区' },\r\n          { name: '神木市' },\r\n          { name: '府谷县' },\r\n          { name: '靖边县' },\r\n          { name: '定边县' },\r\n          { name: '绥德县' },\r\n          { name: '米脂县' },\r\n          { name: '佳县' },\r\n          { name: '吴堡县' },\r\n          { name: '清涧县' },\r\n          { name: '子洲县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '安康市',\r\n        children: [\r\n          { name: '汉滨区' },\r\n          { name: '汉阴县' },\r\n          { name: '石泉县' },\r\n          { name: '宁陕县' },\r\n          { name: '紫阳县' },\r\n          { name: '岚皋县' },\r\n          { name: '平利县' },\r\n          { name: '镇坪县' },\r\n          { name: '旬阳市' },\r\n          { name: '白河县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '商洛市',\r\n        children: [\r\n          { name: '商州区' },\r\n          { name: '洛南县' },\r\n          { name: '丹凤县' },\r\n          { name: '商南县' },\r\n          { name: '山阳县' },\r\n          { name: '镇安县' },\r\n          { name: '柞水县' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '甘肃省',\r\n    children: [\r\n      {\r\n        name: '兰州市',\r\n        children: [\r\n          { name: '城关区' },\r\n          { name: '七里河区' },\r\n          { name: '西固区' },\r\n          { name: '安宁区' },\r\n          { name: '红古区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '天水市',\r\n        children: [\r\n          { name: '秦州区' },\r\n          { name: '麦积区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '青海省',\r\n    children: [\r\n      {\r\n        name: '西宁市',\r\n        children: [\r\n          { name: '城东区' },\r\n          { name: '城中区' },\r\n          { name: '城西区' },\r\n          { name: '城北区' },\r\n          { name: '大通回族土族自治县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '海东市',\r\n        children: [\r\n          { name: '乐都区' },\r\n          { name: '平安区' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '宁夏回族自治区',\r\n    children: [\r\n      {\r\n        name: '银川市',\r\n        children: [\r\n          { name: '兴庆区' },\r\n          { name: '西夏区' },\r\n          { name: '金凤区' },\r\n          { name: '永宁县' },\r\n          { name: '贺兰县' }\r\n        ]\r\n      },\r\n      {\r\n        name: '石嘴山市',\r\n        children: [\r\n          { name: '大武口区' },\r\n          { name: '惠农区' },\r\n          { name: '平罗县' }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '新疆维吾尔自治区',\r\n    children: [\r\n      {\r\n        name: '乌鲁木齐市',\r\n        children: [\r\n          { name: '天山区' },\r\n          { name: '沙依巴克区' },\r\n          { name: '新市区' },\r\n          { name: '水磨沟区' },\r\n          { name: '头屯河区' },\r\n          { name: '达坂城区' },\r\n          { name: '米东区' }\r\n        ]\r\n      },\r\n      {\r\n        name: '克拉玛依市',\r\n        children: [\r\n          { name: '独山子区' },\r\n          { name: '克拉玛依区' },\r\n          { name: '白碱滩区' },\r\n          { name: '乌尔禾区' }\r\n        ]\r\n      }\r\n    ]\r\n  }\r\n]; "], "mappings": "AAAA,OAAO,IAAMA,UAAU,GAAG,CACxB;EACEC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC,EACD;IACEA,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAS,CAAC,EAClB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAQ,CAAC;EAErB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAY,CAAC;EAEzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC;AAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}