!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.G=n():t.G=n()}(window,(function(){return function(t){var n={};function e(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,e),i.l=!0,i.exports}return e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:r})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var i in t)e.d(r,i,function(n){return t[n]}.bind(null,i));return r},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s=55)}([function(t,n,e){"use strict";e.d(n,"g",(function(){return y})),e.d(n,"j",(function(){return l})),e.d(n,"k",(function(){return p})),e.d(n,"m",(function(){return m})),e.d(n,"o",(function(){return b})),e.d(n,"q",(function(){return P})),e.d(n,"b",(function(){return c})),e.d(n,"d",(function(){return o})),e.d(n,"e",(function(){return u})),e.d(n,"f",(function(){return g})),e.d(n,"h",(function(){return s})),e.d(n,"i",(function(){return v})),e.d(n,"a",(function(){return f})),e.d(n,"c",(function(){return C})),e.d(n,"l",(function(){return A})),e.d(n,"p",(function(){return j})),e.d(n,"n",(function(){return k}));var r=function(t){return null!==t&&"function"!=typeof t&&isFinite(t.length)},i={}.toString,a=function(t,n){return i.call(t)==="[object "+n+"]"},o=function(t){return a(t,"Function")},u=function(t){return null==t},c=function(t){return Array.isArray?Array.isArray(t):a(t,"Array")},s=function(t){var n=typeof t;return null!==t&&"object"===n||"function"===n};var f=function(t,n){if(t)if(c(t))for(var e=0,r=t.length;e<r&&!1!==n(t[e],e);e++);else if(s(t))for(var i in t)if(t.hasOwnProperty(i)&&!1===n(t[i],i))break};Object.keys;var h=function(t){return"object"==typeof t&&null!==t};var l=function(t){if(c(t))return t.reduce((function(t,n){return Math.max(t,n)}),t[0])},p=function(t){if(c(t))return t.reduce((function(t,n){return Math.min(t,n)}),t[0])},d=Array.prototype,v=(d.splice,d.indexOf,Array.prototype.splice,function(t){return a(t,"String")});Object.prototype.hasOwnProperty;var g=function(t){return a(t,"Number")};Number.isInteger&&Number.isInteger;function y(t,n,e){return void 0===e&&(e=1e-5),Math.abs(t-n)<e}var m=function(t,n){return(t%n+n)%n},x=(Math.PI,parseInt,Math.PI/180),b=function(t){return x*t},M=Object.values?function(t){return Object.values(t)}:function(t){var n=[];return f(t,(function(e,r){o(t)&&"prototype"===r||n.push(e)})),n},w=function(t){return u(t)?"":t.toString()};var P=function(t){var n=w(t);return n.charAt(0).toUpperCase()+n.substring(1)};Object.prototype;function _(t,n){for(var e in n)n.hasOwnProperty(e)&&"constructor"!==e&&void 0!==n[e]&&(t[e]=n[e])}function A(t,n,e,r){return n&&_(t,n),e&&_(t,e),r&&_(t,r),t}Object.prototype.hasOwnProperty;var O=function(t,n){if(t===n)return!0;if(!t||!n)return!1;if(v(t)||v(n))return!1;if(r(t)||r(n)){if(t.length!==n.length)return!1;for(var e=!0,i=0;i<t.length&&(e=O(t[i],n[i]));i++);return e}if(h(t)||h(n)){var a=Object.keys(t),o=Object.keys(n);if(a.length!==o.length)return!1;for(e=!0,i=0;i<a.length&&(e=O(t[a[i]],n[a[i]]));i++);return e}return!1},C=O,S=(Object.prototype.hasOwnProperty,{}),j=function(t){return S[t=t||"g"]?S[t]+=1:S[t]=1,t+S[t]},k=function(){};var T,E=e(9);(function(t,n){if(!o(t))throw new TypeError("Expected a function");var e=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var a=n?n.apply(this,r):r[0],o=e.cache;if(o.has(a))return o.get(a);var u=t.apply(this,r);return o.set(a,u),u};e.cache=new Map})((function(t,n){void 0===n&&(n={});var e=n.fontSize,r=n.fontFamily,i=n.fontWeight,a=n.fontStyle,o=n.fontVariant;return T||(T=document.createElement("canvas").getContext("2d")),T.font=[a,o,i,e+"px",r].join(" "),T.measureText(v(t)?t:"").width}),(function(t,n){return void 0===n&&(n={}),Object(E.b)([t],M(n)).join("")})),function(){function t(){this.map={}}t.prototype.has=function(t){return void 0!==this.map[t]},t.prototype.get=function(t,n){var e=this.map[t];return void 0===e?n:e},t.prototype.set=function(t,n){this.map[t]=n},t.prototype.clear=function(){this.map={}},t.prototype.delete=function(t){delete this.map[t]},t.prototype.size=function(){return Object.keys(this.map).length}}()},function(t,n,e){"use strict";e.d(n,"j",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"g",(function(){return o})),e.d(n,"b",(function(){return u}));var r=e(0);function i(t,n){var e=t.indexOf(n);-1!==e&&t.splice(e,1)}e.d(n,"e",(function(){return r.e})),e.d(n,"d",(function(){return r.d})),e.d(n,"h",(function(){return r.i})),e.d(n,"f",(function(){return r.h})),e.d(n,"i",(function(){return r.l})),e.d(n,"a",(function(){return r.a})),e.d(n,"k",(function(){return r.q}));var a="undefined"!=typeof window&&void 0!==window.document;function o(t,n){if(t.isCanvas())return!0;for(var e=n.getParent(),r=!1;e;){if(e===t){r=!0;break}e=e.getParent()}return r}function u(t){return t.cfg.visible&&t.cfg.capture}},function(t,n,e){"use strict";e.d(n,"b",(function(){return r})),e.d(n,"a",(function(){return i})),e.d(n,"c",(function(){return a}));var r=1e-6,i="undefined"!=typeof Float32Array?Float32Array:Array,a=Math.random;Math.PI;Math.hypot||(Math.hypot=function(){for(var t=0,n=arguments.length;n--;)t+=arguments[n]*arguments[n];return Math.sqrt(t)})},function(t,n,e){"use strict";e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return i}));var r={};e.r(r),e.d(r,"create",(function(){return u})),e.d(r,"fromMat4",(function(){return c})),e.d(r,"clone",(function(){return s})),e.d(r,"copy",(function(){return f})),e.d(r,"fromValues",(function(){return h})),e.d(r,"set",(function(){return l})),e.d(r,"identity",(function(){return p})),e.d(r,"transpose",(function(){return d})),e.d(r,"invert",(function(){return v})),e.d(r,"adjoint",(function(){return g})),e.d(r,"determinant",(function(){return y})),e.d(r,"multiply",(function(){return m})),e.d(r,"translate",(function(){return x})),e.d(r,"rotate",(function(){return b})),e.d(r,"scale",(function(){return M})),e.d(r,"fromTranslation",(function(){return w})),e.d(r,"fromRotation",(function(){return P})),e.d(r,"fromScaling",(function(){return _})),e.d(r,"fromMat2d",(function(){return A})),e.d(r,"fromQuat",(function(){return O})),e.d(r,"normalFromMat4",(function(){return C})),e.d(r,"projection",(function(){return S})),e.d(r,"str",(function(){return j})),e.d(r,"frob",(function(){return k})),e.d(r,"add",(function(){return T})),e.d(r,"subtract",(function(){return E})),e.d(r,"multiplyScalar",(function(){return B})),e.d(r,"multiplyScalarAndAdd",(function(){return I})),e.d(r,"exactEquals",(function(){return F})),e.d(r,"equals",(function(){return D})),e.d(r,"mul",(function(){return q})),e.d(r,"sub",(function(){return X}));var i={};e.r(i),e.d(i,"create",(function(){return N})),e.d(i,"clone",(function(){return R})),e.d(i,"fromValues",(function(){return L})),e.d(i,"copy",(function(){return Y})),e.d(i,"set",(function(){return z})),e.d(i,"add",(function(){return W})),e.d(i,"subtract",(function(){return $})),e.d(i,"multiply",(function(){return V})),e.d(i,"divide",(function(){return H})),e.d(i,"ceil",(function(){return Q})),e.d(i,"floor",(function(){return G})),e.d(i,"min",(function(){return Z})),e.d(i,"max",(function(){return U})),e.d(i,"round",(function(){return K})),e.d(i,"scale",(function(){return J})),e.d(i,"scaleAndAdd",(function(){return tt})),e.d(i,"distance",(function(){return nt})),e.d(i,"squaredDistance",(function(){return et})),e.d(i,"length",(function(){return rt})),e.d(i,"squaredLength",(function(){return it})),e.d(i,"negate",(function(){return at})),e.d(i,"inverse",(function(){return ot})),e.d(i,"normalize",(function(){return ut})),e.d(i,"dot",(function(){return ct})),e.d(i,"cross",(function(){return st})),e.d(i,"lerp",(function(){return ft})),e.d(i,"random",(function(){return ht})),e.d(i,"transformMat2",(function(){return lt})),e.d(i,"transformMat2d",(function(){return pt})),e.d(i,"transformMat3",(function(){return dt})),e.d(i,"transformMat4",(function(){return vt})),e.d(i,"rotate",(function(){return gt})),e.d(i,"angle",(function(){return yt})),e.d(i,"zero",(function(){return mt})),e.d(i,"str",(function(){return xt})),e.d(i,"exactEquals",(function(){return bt})),e.d(i,"equals",(function(){return Mt})),e.d(i,"len",(function(){return Pt})),e.d(i,"sub",(function(){return _t})),e.d(i,"mul",(function(){return At})),e.d(i,"div",(function(){return Ot})),e.d(i,"dist",(function(){return Ct})),e.d(i,"sqrDist",(function(){return St})),e.d(i,"sqrLen",(function(){return jt})),e.d(i,"forEach",(function(){return kt}));var a="undefined"!=typeof Float32Array?Float32Array:Array,o=Math.random;Math.PI;function u(){var t=new a(9);return a!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function c(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[4],t[4]=n[5],t[5]=n[6],t[6]=n[8],t[7]=n[9],t[8]=n[10],t}function s(t){var n=new a(9);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n}function f(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function h(t,n,e,r,i,o,u,c,s){var f=new a(9);return f[0]=t,f[1]=n,f[2]=e,f[3]=r,f[4]=i,f[5]=o,f[6]=u,f[7]=c,f[8]=s,f}function l(t,n,e,r,i,a,o,u,c,s){return t[0]=n,t[1]=e,t[2]=r,t[3]=i,t[4]=a,t[5]=o,t[6]=u,t[7]=c,t[8]=s,t}function p(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function d(t,n){if(t===n){var e=n[1],r=n[2],i=n[5];t[1]=n[3],t[2]=n[6],t[3]=e,t[5]=n[7],t[6]=r,t[7]=i}else t[0]=n[0],t[1]=n[3],t[2]=n[6],t[3]=n[1],t[4]=n[4],t[5]=n[7],t[6]=n[2],t[7]=n[5],t[8]=n[8];return t}function v(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=n[4],u=n[5],c=n[6],s=n[7],f=n[8],h=f*o-u*s,l=-f*a+u*c,p=s*a-o*c,d=e*h+r*l+i*p;return d?(d=1/d,t[0]=h*d,t[1]=(-f*r+i*s)*d,t[2]=(u*r-i*o)*d,t[3]=l*d,t[4]=(f*e-i*c)*d,t[5]=(-u*e+i*a)*d,t[6]=p*d,t[7]=(-s*e+r*c)*d,t[8]=(o*e-r*a)*d,t):null}function g(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=n[4],u=n[5],c=n[6],s=n[7],f=n[8];return t[0]=o*f-u*s,t[1]=i*s-r*f,t[2]=r*u-i*o,t[3]=u*c-a*f,t[4]=e*f-i*c,t[5]=i*a-e*u,t[6]=a*s-o*c,t[7]=r*c-e*s,t[8]=e*o-r*a,t}function y(t){var n=t[0],e=t[1],r=t[2],i=t[3],a=t[4],o=t[5],u=t[6],c=t[7],s=t[8];return n*(s*a-o*c)+e*(-s*i+o*u)+r*(c*i-a*u)}function m(t,n,e){var r=n[0],i=n[1],a=n[2],o=n[3],u=n[4],c=n[5],s=n[6],f=n[7],h=n[8],l=e[0],p=e[1],d=e[2],v=e[3],g=e[4],y=e[5],m=e[6],x=e[7],b=e[8];return t[0]=l*r+p*o+d*s,t[1]=l*i+p*u+d*f,t[2]=l*a+p*c+d*h,t[3]=v*r+g*o+y*s,t[4]=v*i+g*u+y*f,t[5]=v*a+g*c+y*h,t[6]=m*r+x*o+b*s,t[7]=m*i+x*u+b*f,t[8]=m*a+x*c+b*h,t}function x(t,n,e){var r=n[0],i=n[1],a=n[2],o=n[3],u=n[4],c=n[5],s=n[6],f=n[7],h=n[8],l=e[0],p=e[1];return t[0]=r,t[1]=i,t[2]=a,t[3]=o,t[4]=u,t[5]=c,t[6]=l*r+p*o+s,t[7]=l*i+p*u+f,t[8]=l*a+p*c+h,t}function b(t,n,e){var r=n[0],i=n[1],a=n[2],o=n[3],u=n[4],c=n[5],s=n[6],f=n[7],h=n[8],l=Math.sin(e),p=Math.cos(e);return t[0]=p*r+l*o,t[1]=p*i+l*u,t[2]=p*a+l*c,t[3]=p*o-l*r,t[4]=p*u-l*i,t[5]=p*c-l*a,t[6]=s,t[7]=f,t[8]=h,t}function M(t,n,e){var r=e[0],i=e[1];return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=i*n[3],t[4]=i*n[4],t[5]=i*n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function w(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=n[0],t[7]=n[1],t[8]=1,t}function P(t,n){var e=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=e,t[2]=0,t[3]=-e,t[4]=r,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function _(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=n[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function A(t,n){return t[0]=n[0],t[1]=n[1],t[2]=0,t[3]=n[2],t[4]=n[3],t[5]=0,t[6]=n[4],t[7]=n[5],t[8]=1,t}function O(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=e+e,u=r+r,c=i+i,s=e*o,f=r*o,h=r*u,l=i*o,p=i*u,d=i*c,v=a*o,g=a*u,y=a*c;return t[0]=1-h-d,t[3]=f-y,t[6]=l+g,t[1]=f+y,t[4]=1-s-d,t[7]=p-v,t[2]=l-g,t[5]=p+v,t[8]=1-s-h,t}function C(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=n[4],u=n[5],c=n[6],s=n[7],f=n[8],h=n[9],l=n[10],p=n[11],d=n[12],v=n[13],g=n[14],y=n[15],m=e*u-r*o,x=e*c-i*o,b=e*s-a*o,M=r*c-i*u,w=r*s-a*u,P=i*s-a*c,_=f*v-h*d,A=f*g-l*d,O=f*y-p*d,C=h*g-l*v,S=h*y-p*v,j=l*y-p*g,k=m*j-x*S+b*C+M*O-w*A+P*_;return k?(k=1/k,t[0]=(u*j-c*S+s*C)*k,t[1]=(c*O-o*j-s*A)*k,t[2]=(o*S-u*O+s*_)*k,t[3]=(i*S-r*j-a*C)*k,t[4]=(e*j-i*O+a*A)*k,t[5]=(r*O-e*S-a*_)*k,t[6]=(v*P-g*w+y*M)*k,t[7]=(g*b-d*P-y*x)*k,t[8]=(d*w-v*b+y*m)*k,t):null}function S(t,n,e){return t[0]=2/n,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/e,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t}function j(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"}function k(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8])}function T(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t[3]=n[3]+e[3],t[4]=n[4]+e[4],t[5]=n[5]+e[5],t[6]=n[6]+e[6],t[7]=n[7]+e[7],t[8]=n[8]+e[8],t}function E(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t}function B(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t[3]=n[3]*e,t[4]=n[4]*e,t[5]=n[5]*e,t[6]=n[6]*e,t[7]=n[7]*e,t[8]=n[8]*e,t}function I(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t[3]=n[3]+e[3]*r,t[4]=n[4]+e[4]*r,t[5]=n[5]+e[5]*r,t[6]=n[6]+e[6]*r,t[7]=n[7]+e[7]*r,t[8]=n[8]+e[8]*r,t}function F(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]}function D(t,n){var e=t[0],r=t[1],i=t[2],a=t[3],o=t[4],u=t[5],c=t[6],s=t[7],f=t[8],h=n[0],l=n[1],p=n[2],d=n[3],v=n[4],g=n[5],y=n[6],m=n[7],x=n[8];return Math.abs(e-h)<=1e-6*Math.max(1,Math.abs(e),Math.abs(h))&&Math.abs(r-l)<=1e-6*Math.max(1,Math.abs(r),Math.abs(l))&&Math.abs(i-p)<=1e-6*Math.max(1,Math.abs(i),Math.abs(p))&&Math.abs(a-d)<=1e-6*Math.max(1,Math.abs(a),Math.abs(d))&&Math.abs(o-v)<=1e-6*Math.max(1,Math.abs(o),Math.abs(v))&&Math.abs(u-g)<=1e-6*Math.max(1,Math.abs(u),Math.abs(g))&&Math.abs(c-y)<=1e-6*Math.max(1,Math.abs(c),Math.abs(y))&&Math.abs(s-m)<=1e-6*Math.max(1,Math.abs(s),Math.abs(m))&&Math.abs(f-x)<=1e-6*Math.max(1,Math.abs(f),Math.abs(x))}Math.hypot||(Math.hypot=function(){for(var t=0,n=arguments.length;n--;)t+=arguments[n]*arguments[n];return Math.sqrt(t)});var q=m,X=E;function N(){var t=new a(2);return a!=Float32Array&&(t[0]=0,t[1]=0),t}function R(t){var n=new a(2);return n[0]=t[0],n[1]=t[1],n}function L(t,n){var e=new a(2);return e[0]=t,e[1]=n,e}function Y(t,n){return t[0]=n[0],t[1]=n[1],t}function z(t,n,e){return t[0]=n,t[1]=e,t}function W(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t}function $(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t}function V(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t}function H(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t}function Q(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t}function G(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t}function Z(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t}function U(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t}function K(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t}function J(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t}function tt(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t}function nt(t,n){var e=n[0]-t[0],r=n[1]-t[1];return Math.hypot(e,r)}function et(t,n){var e=n[0]-t[0],r=n[1]-t[1];return e*e+r*r}function rt(t){var n=t[0],e=t[1];return Math.hypot(n,e)}function it(t){var n=t[0],e=t[1];return n*n+e*e}function at(t,n){return t[0]=-n[0],t[1]=-n[1],t}function ot(t,n){return t[0]=1/n[0],t[1]=1/n[1],t}function ut(t,n){var e=n[0],r=n[1],i=e*e+r*r;return i>0&&(i=1/Math.sqrt(i)),t[0]=n[0]*i,t[1]=n[1]*i,t}function ct(t,n){return t[0]*n[0]+t[1]*n[1]}function st(t,n,e){var r=n[0]*e[1]-n[1]*e[0];return t[0]=t[1]=0,t[2]=r,t}function ft(t,n,e,r){var i=n[0],a=n[1];return t[0]=i+r*(e[0]-i),t[1]=a+r*(e[1]-a),t}function ht(t,n){n=n||1;var e=2*o()*Math.PI;return t[0]=Math.cos(e)*n,t[1]=Math.sin(e)*n,t}function lt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[2]*i,t[1]=e[1]*r+e[3]*i,t}function pt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[2]*i+e[4],t[1]=e[1]*r+e[3]*i+e[5],t}function dt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[3]*i+e[6],t[1]=e[1]*r+e[4]*i+e[7],t}function vt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[4]*i+e[12],t[1]=e[1]*r+e[5]*i+e[13],t}function gt(t,n,e,r){var i=n[0]-e[0],a=n[1]-e[1],o=Math.sin(r),u=Math.cos(r);return t[0]=i*u-a*o+e[0],t[1]=i*o+a*u+e[1],t}function yt(t,n){var e=t[0],r=t[1],i=n[0],a=n[1],o=Math.sqrt(e*e+r*r)*Math.sqrt(i*i+a*a),u=o&&(e*i+r*a)/o;return Math.acos(Math.min(Math.max(u,-1),1))}function mt(t){return t[0]=0,t[1]=0,t}function xt(t){return"vec2("+t[0]+", "+t[1]+")"}function bt(t,n){return t[0]===n[0]&&t[1]===n[1]}function Mt(t,n){var e=t[0],r=t[1],i=n[0],a=n[1];return Math.abs(e-i)<=1e-6*Math.max(1,Math.abs(e),Math.abs(i))&&Math.abs(r-a)<=1e-6*Math.max(1,Math.abs(r),Math.abs(a))}var wt,Pt=rt,_t=$,At=V,Ot=H,Ct=nt,St=et,jt=it,kt=(wt=N(),function(t,n,e,r,i,a){var o,u;for(n||(n=2),e||(e=0),u=r?Math.min(r*n+e,t.length):t.length,o=e;o<u;o+=n)wt[0]=t[o],wt[1]=t[o+1],i(wt,wt,a),t[o]=wt[0],t[o+1]=wt[1];return t})},function(t,n,e){"use strict";e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return kt}));var r={};e.r(r),e.d(r,"create",(function(){return o})),e.d(r,"fromMat4",(function(){return u})),e.d(r,"clone",(function(){return c})),e.d(r,"copy",(function(){return s})),e.d(r,"fromValues",(function(){return f})),e.d(r,"set",(function(){return h})),e.d(r,"identity",(function(){return l})),e.d(r,"transpose",(function(){return p})),e.d(r,"invert",(function(){return d})),e.d(r,"adjoint",(function(){return v})),e.d(r,"determinant",(function(){return g})),e.d(r,"multiply",(function(){return y})),e.d(r,"translate",(function(){return m})),e.d(r,"rotate",(function(){return x})),e.d(r,"scale",(function(){return b})),e.d(r,"fromTranslation",(function(){return M})),e.d(r,"fromRotation",(function(){return w})),e.d(r,"fromScaling",(function(){return P})),e.d(r,"fromMat2d",(function(){return _})),e.d(r,"fromQuat",(function(){return A})),e.d(r,"normalFromMat4",(function(){return O})),e.d(r,"projection",(function(){return C})),e.d(r,"str",(function(){return S})),e.d(r,"frob",(function(){return j})),e.d(r,"add",(function(){return k})),e.d(r,"subtract",(function(){return T})),e.d(r,"multiplyScalar",(function(){return E})),e.d(r,"multiplyScalarAndAdd",(function(){return B})),e.d(r,"exactEquals",(function(){return I})),e.d(r,"equals",(function(){return F})),e.d(r,"mul",(function(){return D})),e.d(r,"sub",(function(){return q}));var i={};e.r(i),e.d(i,"create",(function(){return X})),e.d(i,"clone",(function(){return N})),e.d(i,"fromValues",(function(){return R})),e.d(i,"copy",(function(){return L})),e.d(i,"set",(function(){return Y})),e.d(i,"add",(function(){return z})),e.d(i,"subtract",(function(){return W})),e.d(i,"multiply",(function(){return $})),e.d(i,"divide",(function(){return V})),e.d(i,"ceil",(function(){return H})),e.d(i,"floor",(function(){return Q})),e.d(i,"min",(function(){return G})),e.d(i,"max",(function(){return Z})),e.d(i,"round",(function(){return U})),e.d(i,"scale",(function(){return K})),e.d(i,"scaleAndAdd",(function(){return J})),e.d(i,"distance",(function(){return tt})),e.d(i,"squaredDistance",(function(){return nt})),e.d(i,"length",(function(){return et})),e.d(i,"squaredLength",(function(){return rt})),e.d(i,"negate",(function(){return it})),e.d(i,"inverse",(function(){return at})),e.d(i,"normalize",(function(){return ot})),e.d(i,"dot",(function(){return ut})),e.d(i,"cross",(function(){return ct})),e.d(i,"lerp",(function(){return st})),e.d(i,"random",(function(){return ft})),e.d(i,"transformMat2",(function(){return ht})),e.d(i,"transformMat2d",(function(){return lt})),e.d(i,"transformMat3",(function(){return pt})),e.d(i,"transformMat4",(function(){return dt})),e.d(i,"rotate",(function(){return vt})),e.d(i,"angle",(function(){return gt})),e.d(i,"zero",(function(){return yt})),e.d(i,"str",(function(){return mt})),e.d(i,"exactEquals",(function(){return xt})),e.d(i,"equals",(function(){return bt})),e.d(i,"len",(function(){return wt})),e.d(i,"sub",(function(){return Pt})),e.d(i,"mul",(function(){return _t})),e.d(i,"div",(function(){return At})),e.d(i,"dist",(function(){return Ot})),e.d(i,"sqrDist",(function(){return Ct})),e.d(i,"sqrLen",(function(){return St})),e.d(i,"forEach",(function(){return jt}));var a=e(2);function o(){var t=new a.a(9);return a.a!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function u(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[4],t[4]=n[5],t[5]=n[6],t[6]=n[8],t[7]=n[9],t[8]=n[10],t}function c(t){var n=new a.a(9);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n}function s(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function f(t,n,e,r,i,o,u,c,s){var f=new a.a(9);return f[0]=t,f[1]=n,f[2]=e,f[3]=r,f[4]=i,f[5]=o,f[6]=u,f[7]=c,f[8]=s,f}function h(t,n,e,r,i,a,o,u,c,s){return t[0]=n,t[1]=e,t[2]=r,t[3]=i,t[4]=a,t[5]=o,t[6]=u,t[7]=c,t[8]=s,t}function l(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function p(t,n){if(t===n){var e=n[1],r=n[2],i=n[5];t[1]=n[3],t[2]=n[6],t[3]=e,t[5]=n[7],t[6]=r,t[7]=i}else t[0]=n[0],t[1]=n[3],t[2]=n[6],t[3]=n[1],t[4]=n[4],t[5]=n[7],t[6]=n[2],t[7]=n[5],t[8]=n[8];return t}function d(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=n[4],u=n[5],c=n[6],s=n[7],f=n[8],h=f*o-u*s,l=-f*a+u*c,p=s*a-o*c,d=e*h+r*l+i*p;return d?(d=1/d,t[0]=h*d,t[1]=(-f*r+i*s)*d,t[2]=(u*r-i*o)*d,t[3]=l*d,t[4]=(f*e-i*c)*d,t[5]=(-u*e+i*a)*d,t[6]=p*d,t[7]=(-s*e+r*c)*d,t[8]=(o*e-r*a)*d,t):null}function v(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=n[4],u=n[5],c=n[6],s=n[7],f=n[8];return t[0]=o*f-u*s,t[1]=i*s-r*f,t[2]=r*u-i*o,t[3]=u*c-a*f,t[4]=e*f-i*c,t[5]=i*a-e*u,t[6]=a*s-o*c,t[7]=r*c-e*s,t[8]=e*o-r*a,t}function g(t){var n=t[0],e=t[1],r=t[2],i=t[3],a=t[4],o=t[5],u=t[6],c=t[7],s=t[8];return n*(s*a-o*c)+e*(-s*i+o*u)+r*(c*i-a*u)}function y(t,n,e){var r=n[0],i=n[1],a=n[2],o=n[3],u=n[4],c=n[5],s=n[6],f=n[7],h=n[8],l=e[0],p=e[1],d=e[2],v=e[3],g=e[4],y=e[5],m=e[6],x=e[7],b=e[8];return t[0]=l*r+p*o+d*s,t[1]=l*i+p*u+d*f,t[2]=l*a+p*c+d*h,t[3]=v*r+g*o+y*s,t[4]=v*i+g*u+y*f,t[5]=v*a+g*c+y*h,t[6]=m*r+x*o+b*s,t[7]=m*i+x*u+b*f,t[8]=m*a+x*c+b*h,t}function m(t,n,e){var r=n[0],i=n[1],a=n[2],o=n[3],u=n[4],c=n[5],s=n[6],f=n[7],h=n[8],l=e[0],p=e[1];return t[0]=r,t[1]=i,t[2]=a,t[3]=o,t[4]=u,t[5]=c,t[6]=l*r+p*o+s,t[7]=l*i+p*u+f,t[8]=l*a+p*c+h,t}function x(t,n,e){var r=n[0],i=n[1],a=n[2],o=n[3],u=n[4],c=n[5],s=n[6],f=n[7],h=n[8],l=Math.sin(e),p=Math.cos(e);return t[0]=p*r+l*o,t[1]=p*i+l*u,t[2]=p*a+l*c,t[3]=p*o-l*r,t[4]=p*u-l*i,t[5]=p*c-l*a,t[6]=s,t[7]=f,t[8]=h,t}function b(t,n,e){var r=e[0],i=e[1];return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=i*n[3],t[4]=i*n[4],t[5]=i*n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function M(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=n[0],t[7]=n[1],t[8]=1,t}function w(t,n){var e=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=e,t[2]=0,t[3]=-e,t[4]=r,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function P(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=n[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function _(t,n){return t[0]=n[0],t[1]=n[1],t[2]=0,t[3]=n[2],t[4]=n[3],t[5]=0,t[6]=n[4],t[7]=n[5],t[8]=1,t}function A(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=e+e,u=r+r,c=i+i,s=e*o,f=r*o,h=r*u,l=i*o,p=i*u,d=i*c,v=a*o,g=a*u,y=a*c;return t[0]=1-h-d,t[3]=f-y,t[6]=l+g,t[1]=f+y,t[4]=1-s-d,t[7]=p-v,t[2]=l-g,t[5]=p+v,t[8]=1-s-h,t}function O(t,n){var e=n[0],r=n[1],i=n[2],a=n[3],o=n[4],u=n[5],c=n[6],s=n[7],f=n[8],h=n[9],l=n[10],p=n[11],d=n[12],v=n[13],g=n[14],y=n[15],m=e*u-r*o,x=e*c-i*o,b=e*s-a*o,M=r*c-i*u,w=r*s-a*u,P=i*s-a*c,_=f*v-h*d,A=f*g-l*d,O=f*y-p*d,C=h*g-l*v,S=h*y-p*v,j=l*y-p*g,k=m*j-x*S+b*C+M*O-w*A+P*_;return k?(k=1/k,t[0]=(u*j-c*S+s*C)*k,t[1]=(c*O-o*j-s*A)*k,t[2]=(o*S-u*O+s*_)*k,t[3]=(i*S-r*j-a*C)*k,t[4]=(e*j-i*O+a*A)*k,t[5]=(r*O-e*S-a*_)*k,t[6]=(v*P-g*w+y*M)*k,t[7]=(g*b-d*P-y*x)*k,t[8]=(d*w-v*b+y*m)*k,t):null}function C(t,n,e){return t[0]=2/n,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/e,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t}function S(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"}function j(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8])}function k(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t[3]=n[3]+e[3],t[4]=n[4]+e[4],t[5]=n[5]+e[5],t[6]=n[6]+e[6],t[7]=n[7]+e[7],t[8]=n[8]+e[8],t}function T(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t}function E(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t[3]=n[3]*e,t[4]=n[4]*e,t[5]=n[5]*e,t[6]=n[6]*e,t[7]=n[7]*e,t[8]=n[8]*e,t}function B(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t[3]=n[3]+e[3]*r,t[4]=n[4]+e[4]*r,t[5]=n[5]+e[5]*r,t[6]=n[6]+e[6]*r,t[7]=n[7]+e[7]*r,t[8]=n[8]+e[8]*r,t}function I(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]}function F(t,n){var e=t[0],r=t[1],i=t[2],o=t[3],u=t[4],c=t[5],s=t[6],f=t[7],h=t[8],l=n[0],p=n[1],d=n[2],v=n[3],g=n[4],y=n[5],m=n[6],x=n[7],b=n[8];return Math.abs(e-l)<=a.b*Math.max(1,Math.abs(e),Math.abs(l))&&Math.abs(r-p)<=a.b*Math.max(1,Math.abs(r),Math.abs(p))&&Math.abs(i-d)<=a.b*Math.max(1,Math.abs(i),Math.abs(d))&&Math.abs(o-v)<=a.b*Math.max(1,Math.abs(o),Math.abs(v))&&Math.abs(u-g)<=a.b*Math.max(1,Math.abs(u),Math.abs(g))&&Math.abs(c-y)<=a.b*Math.max(1,Math.abs(c),Math.abs(y))&&Math.abs(s-m)<=a.b*Math.max(1,Math.abs(s),Math.abs(m))&&Math.abs(f-x)<=a.b*Math.max(1,Math.abs(f),Math.abs(x))&&Math.abs(h-b)<=a.b*Math.max(1,Math.abs(h),Math.abs(b))}var D=y,q=T;function X(){var t=new a.a(2);return a.a!=Float32Array&&(t[0]=0,t[1]=0),t}function N(t){var n=new a.a(2);return n[0]=t[0],n[1]=t[1],n}function R(t,n){var e=new a.a(2);return e[0]=t,e[1]=n,e}function L(t,n){return t[0]=n[0],t[1]=n[1],t}function Y(t,n,e){return t[0]=n,t[1]=e,t}function z(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t}function W(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t}function $(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t}function V(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t}function H(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t}function Q(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t}function G(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t}function Z(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t}function U(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t}function K(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t}function J(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t}function tt(t,n){var e=n[0]-t[0],r=n[1]-t[1];return Math.hypot(e,r)}function nt(t,n){var e=n[0]-t[0],r=n[1]-t[1];return e*e+r*r}function et(t){var n=t[0],e=t[1];return Math.hypot(n,e)}function rt(t){var n=t[0],e=t[1];return n*n+e*e}function it(t,n){return t[0]=-n[0],t[1]=-n[1],t}function at(t,n){return t[0]=1/n[0],t[1]=1/n[1],t}function ot(t,n){var e=n[0],r=n[1],i=e*e+r*r;return i>0&&(i=1/Math.sqrt(i)),t[0]=n[0]*i,t[1]=n[1]*i,t}function ut(t,n){return t[0]*n[0]+t[1]*n[1]}function ct(t,n,e){var r=n[0]*e[1]-n[1]*e[0];return t[0]=t[1]=0,t[2]=r,t}function st(t,n,e,r){var i=n[0],a=n[1];return t[0]=i+r*(e[0]-i),t[1]=a+r*(e[1]-a),t}function ft(t,n){n=n||1;var e=2*a.c()*Math.PI;return t[0]=Math.cos(e)*n,t[1]=Math.sin(e)*n,t}function ht(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[2]*i,t[1]=e[1]*r+e[3]*i,t}function lt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[2]*i+e[4],t[1]=e[1]*r+e[3]*i+e[5],t}function pt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[3]*i+e[6],t[1]=e[1]*r+e[4]*i+e[7],t}function dt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[4]*i+e[12],t[1]=e[1]*r+e[5]*i+e[13],t}function vt(t,n,e,r){var i=n[0]-e[0],a=n[1]-e[1],o=Math.sin(r),u=Math.cos(r);return t[0]=i*u-a*o+e[0],t[1]=i*o+a*u+e[1],t}function gt(t,n){var e=t[0],r=t[1],i=n[0],a=n[1],o=Math.sqrt(e*e+r*r)*Math.sqrt(i*i+a*a),u=o&&(e*i+r*a)/o;return Math.acos(Math.min(Math.max(u,-1),1))}function yt(t){return t[0]=0,t[1]=0,t}function mt(t){return"vec2("+t[0]+", "+t[1]+")"}function xt(t,n){return t[0]===n[0]&&t[1]===n[1]}function bt(t,n){var e=t[0],r=t[1],i=n[0],o=n[1];return Math.abs(e-i)<=a.b*Math.max(1,Math.abs(e),Math.abs(i))&&Math.abs(r-o)<=a.b*Math.max(1,Math.abs(r),Math.abs(o))}var Mt,wt=et,Pt=W,_t=$,At=V,Ot=tt,Ct=nt,St=rt,jt=(Mt=X(),function(t,n,e,r,i,a){var o,u;for(n||(n=2),e||(e=0),u=r?Math.min(r*n+e,t.length):t.length,o=e;o<u;o+=n)Mt[0]=t[o],Mt[1]=t[o+1],i(Mt,Mt,a),t[o]=Mt[0],t[o+1]=Mt[1];return t}),kt=e(26)},function(t,n,e){"use strict";e.r(n),e.d(n,"contains",(function(){return i})),e.d(n,"includes",(function(){return i})),e.d(n,"difference",(function(){return o})),e.d(n,"find",(function(){return m})),e.d(n,"findIndex",(function(){return x})),e.d(n,"firstValue",(function(){return b})),e.d(n,"flatten",(function(){return M})),e.d(n,"flattenDeep",(function(){return P})),e.d(n,"getRange",(function(){return O})),e.d(n,"pull",(function(){return k})),e.d(n,"pullAt",(function(){return E})),e.d(n,"reduce",(function(){return B})),e.d(n,"remove",(function(){return I})),e.d(n,"sortBy",(function(){return D})),e.d(n,"union",(function(){return X})),e.d(n,"uniq",(function(){return q})),e.d(n,"valuesOfKey",(function(){return N})),e.d(n,"head",(function(){return R})),e.d(n,"last",(function(){return L})),e.d(n,"startsWith",(function(){return Y})),e.d(n,"endsWith",(function(){return z})),e.d(n,"filter",(function(){return a})),e.d(n,"every",(function(){return W})),e.d(n,"some",(function(){return $})),e.d(n,"group",(function(){return G})),e.d(n,"groupBy",(function(){return H})),e.d(n,"groupToMap",(function(){return Q})),e.d(n,"getWrapBehavior",(function(){return Z})),e.d(n,"wrapBehavior",(function(){return U})),e.d(n,"number2color",(function(){return J})),e.d(n,"parseRadius",(function(){return tt})),e.d(n,"clamp",(function(){return nt})),e.d(n,"fixedBase",(function(){return et})),e.d(n,"isDecimal",(function(){return it})),e.d(n,"isEven",(function(){return at})),e.d(n,"isInteger",(function(){return ot})),e.d(n,"isNegative",(function(){return ut})),e.d(n,"isNumberEqual",(function(){return ct})),e.d(n,"isOdd",(function(){return st})),e.d(n,"isPositive",(function(){return ft})),e.d(n,"max",(function(){return _})),e.d(n,"maxBy",(function(){return ht})),e.d(n,"min",(function(){return A})),e.d(n,"minBy",(function(){return lt})),e.d(n,"mod",(function(){return pt})),e.d(n,"toDegree",(function(){return vt})),e.d(n,"toInteger",(function(){return gt})),e.d(n,"toRadian",(function(){return mt})),e.d(n,"forIn",(function(){return xt})),e.d(n,"has",(function(){return bt})),e.d(n,"hasKey",(function(){return Mt})),e.d(n,"hasValue",(function(){return Pt})),e.d(n,"keys",(function(){return d})),e.d(n,"isMatch",(function(){return v})),e.d(n,"values",(function(){return wt})),e.d(n,"lowerCase",(function(){return At})),e.d(n,"lowerFirst",(function(){return Ot})),e.d(n,"substitute",(function(){return Ct})),e.d(n,"upperCase",(function(){return St})),e.d(n,"upperFirst",(function(){return jt})),e.d(n,"getType",(function(){return Tt})),e.d(n,"isArguments",(function(){return Et})),e.d(n,"isArray",(function(){return h})),e.d(n,"isArrayLike",(function(){return r})),e.d(n,"isBoolean",(function(){return Bt})),e.d(n,"isDate",(function(){return It})),e.d(n,"isError",(function(){return Ft})),e.d(n,"isFunction",(function(){return s})),e.d(n,"isFinite",(function(){return Dt})),e.d(n,"isNil",(function(){return f})),e.d(n,"isNull",(function(){return qt})),e.d(n,"isNumber",(function(){return rt})),e.d(n,"isObject",(function(){return l})),e.d(n,"isObjectLike",(function(){return g})),e.d(n,"isPlainObject",(function(){return y})),e.d(n,"isPrototype",(function(){return Nt})),e.d(n,"isRegExp",(function(){return Rt})),e.d(n,"isString",(function(){return F})),e.d(n,"isType",(function(){return c})),e.d(n,"isUndefined",(function(){return Lt})),e.d(n,"isElement",(function(){return Yt})),e.d(n,"requestAnimationFrame",(function(){return zt})),e.d(n,"clearAnimationFrame",(function(){return Wt})),e.d(n,"augment",(function(){return Ht})),e.d(n,"clone",(function(){return Gt})),e.d(n,"debounce",(function(){return Zt})),e.d(n,"memoize",(function(){return Ut})),e.d(n,"deepMix",(function(){return Jt})),e.d(n,"each",(function(){return p})),e.d(n,"extend",(function(){return tn})),e.d(n,"indexOf",(function(){return nn})),e.d(n,"isEmpty",(function(){return rn})),e.d(n,"isEqual",(function(){return on})),e.d(n,"isEqualWith",(function(){return un})),e.d(n,"map",(function(){return cn})),e.d(n,"mapValues",(function(){return fn})),e.d(n,"mix",(function(){return Vt})),e.d(n,"assign",(function(){return Vt})),e.d(n,"get",(function(){return hn})),e.d(n,"set",(function(){return ln})),e.d(n,"pick",(function(){return dn})),e.d(n,"omit",(function(){return vn})),e.d(n,"throttle",(function(){return gn})),e.d(n,"toArray",(function(){return yn})),e.d(n,"toString",(function(){return _t})),e.d(n,"uniqueId",(function(){return xn})),e.d(n,"noop",(function(){return bn})),e.d(n,"identity",(function(){return Mn})),e.d(n,"size",(function(){return wn})),e.d(n,"measureTextWidth",(function(){return An})),e.d(n,"getEllipsisText",(function(){return On})),e.d(n,"Cache",(function(){return Cn}));var r=function(t){return null!==t&&"function"!=typeof t&&isFinite(t.length)},i=function(t,n){return!!r(t)&&t.indexOf(n)>-1},a=function(t,n){if(!r(t))return t;for(var e=[],i=0;i<t.length;i++){var a=t[i];n(a,i)&&e.push(a)}return e},o=function(t,n){return void 0===n&&(n=[]),a(t,(function(t){return!i(n,t)}))},u={}.toString,c=function(t,n){return u.call(t)==="[object "+n+"]"},s=function(t){return c(t,"Function")},f=function(t){return null==t},h=function(t){return Array.isArray?Array.isArray(t):c(t,"Array")},l=function(t){var n=typeof t;return null!==t&&"object"===n||"function"===n};var p=function(t,n){if(t)if(h(t))for(var e=0,r=t.length;e<r&&!1!==n(t[e],e);e++);else if(l(t))for(var i in t)if(t.hasOwnProperty(i)&&!1===n(t[i],i))break},d=Object.keys?function(t){return Object.keys(t)}:function(t){var n=[];return p(t,(function(e,r){s(t)&&"prototype"===r||n.push(r)})),n};var v=function(t,n){var e=d(n),r=e.length;if(f(t))return!r;for(var i=0;i<r;i+=1){var a=e[i];if(n[a]!==t[a]||!(a in t))return!1}return!0},g=function(t){return"object"==typeof t&&null!==t},y=function(t){if(!g(t)||!c(t,"Object"))return!1;if(null===Object.getPrototypeOf(t))return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(t)===n};var m=function(t,n){if(!h(t))return null;var e;if(s(n)&&(e=n),y(n)&&(e=function(t){return v(t,n)}),e)for(var r=0;r<t.length;r+=1)if(e(t[r]))return t[r];return null};var x=function(t,n,e){void 0===e&&(e=0);for(var r=e;r<t.length;r++)if(n(t[r],r))return r;return-1},b=function(t,n){for(var e=null,r=0;r<t.length;r++){var i=t[r][n];if(!f(i)){e=h(i)?i[0]:i;break}}return e},M=function(t){if(!h(t))return[];for(var n=[],e=0;e<t.length;e++)n=n.concat(t[e]);return n},w=function(t,n){if(void 0===n&&(n=[]),h(t))for(var e=0;e<t.length;e+=1)w(t[e],n);else n.push(t);return n},P=w,_=function(t){if(h(t))return t.reduce((function(t,n){return Math.max(t,n)}),t[0])},A=function(t){if(h(t))return t.reduce((function(t,n){return Math.min(t,n)}),t[0])},O=function(t){var n=t.filter((function(t){return!isNaN(t)}));if(!n.length)return{min:0,max:0};if(h(t[0])){for(var e=[],r=0;r<t.length;r++)e=e.concat(t[r]);n=e}var i=_(n);return{min:A(n),max:i}},C=Array.prototype,S=C.splice,j=C.indexOf,k=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var r=0;r<n.length;r++)for(var i=n[r],a=-1;(a=j.call(t,i))>-1;)S.call(t,a,1);return t},T=Array.prototype.splice,E=function(t,n){if(!r(t))return[];for(var e=t?n.length:0,i=e-1;e--;){var a=void 0,o=n[e];e!==i&&o===a||(a=o,T.call(t,o,1))}return t},B=function(t,n,e){if(!h(t)&&!y(t))return t;var r=e;return p(t,(function(t,e){r=n(r,t,e)})),r},I=function(t,n){var e=[];if(!r(t))return e;for(var i=-1,a=[],o=t.length;++i<o;){var u=t[i];n(u,i,t)&&(e.push(u),a.push(i))}return E(t,a),e},F=function(t){return c(t,"String")};var D=function(t,n){var e;if(s(n))e=function(t,e){return n(t)-n(e)};else{var r=[];F(n)?r.push(n):h(n)&&(r=n),e=function(t,n){for(var e=0;e<r.length;e+=1){var i=r[e];if(t[i]>n[i])return 1;if(t[i]<n[i])return-1}return 0}}return t.sort(e),t};function q(t,n){void 0===n&&(n=new Map);var e=[];if(Array.isArray(t))for(var r=0,i=t.length;r<i;r++){var a=t[r];n.has(a)||(e.push(a),n.set(a,!0))}return e}var X=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return q([].concat.apply([],t))},N=function(t,n){for(var e=[],r={},i=0;i<t.length;i++){var a=t[i][n];if(!f(a)){h(a)||(a=[a]);for(var o=0;o<a.length;o++){var u=a[o];r[u]||(e.push(u),r[u]=!0)}}}return e};function R(t){if(r(t))return t[0]}function L(t){if(r(t)){return t[t.length-1]}}var Y=function(t,n){return!(!h(t)&&!F(t))&&t[0]===n};var z=function(t,n){return!(!h(t)&&!F(t))&&t[t.length-1]===n},W=function(t,n){for(var e=0;e<t.length;e++)if(!n(t[e],e))return!1;return!0},$=function(t,n){for(var e=0;e<t.length;e++)if(n(t[e],e))return!0;return!1},V=Object.prototype.hasOwnProperty;var H=function(t,n){if(!n||!h(t))return{};for(var e,r={},i=s(n)?n:function(t){return t[n]},a=0;a<t.length;a++){var o=t[a];e=i(o),V.call(r,e)?r[e].push(o):r[e]=[o]}return r};function Q(t,n){if(!n)return{0:t};if(!s(n)){var e=h(n)?n:n.replace(/\s+/g,"").split("*");n=function(t){for(var n="_",r=0,i=e.length;r<i;r++)n+=t[e[r]]&&t[e[r]].toString();return n}}return H(t,n)}var G=function(t,n){if(!n)return[t];var e=Q(t,n),r=[];for(var i in e)r.push(e[i]);return r};var Z=function(t,n){return t["_wrap_"+n]};var U=function(t,n){if(t["_wrap_"+n])return t["_wrap_"+n];var e=function(e){t[n](e)};return t["_wrap_"+n]=e,e},K={};var J=function(t){var n=K[t];if(!n){for(var e=t.toString(16),r=e.length;r<6;r++)e="0"+e;n="#"+e,K[t]=n}return n};var tt=function(t){var n=0,e=0,r=0,i=0;return h(t)?1===t.length?n=e=r=i=t[0]:2===t.length?(n=r=t[0],e=i=t[1]):3===t.length?(n=t[0],e=i=t[1],r=t[2]):(n=t[0],e=t[1],r=t[2],i=t[3]):n=e=r=i=t,{r1:n,r2:e,r3:r,r4:i}},nt=function(t,n,e){return t<n?n:t>e?e:t},et=function(t,n){var e=n.toString(),r=e.indexOf(".");if(-1===r)return Math.round(t);var i=e.substr(r+1).length;return i>20&&(i=20),parseFloat(t.toFixed(i))},rt=function(t){return c(t,"Number")},it=function(t){return rt(t)&&t%1!=0},at=function(t){return rt(t)&&t%2==0},ot=Number.isInteger?Number.isInteger:function(t){return rt(t)&&t%1==0},ut=function(t){return rt(t)&&t<0};function ct(t,n,e){return void 0===e&&(e=1e-5),Math.abs(t-n)<e}var st=function(t){return rt(t)&&t%2!=0},ft=function(t){return rt(t)&&t>0},ht=function(t,n){if(h(t)){for(var e,r=-1/0,i=0;i<t.length;i++){var a=t[i],o=s(n)?n(a):a[n];o>r&&(e=a,r=o)}return e}},lt=function(t,n){if(h(t)){for(var e,r=1/0,i=0;i<t.length;i++){var a=t[i],o=s(n)?n(a):a[n];o<r&&(e=a,r=o)}return e}},pt=function(t,n){return(t%n+n)%n},dt=180/Math.PI,vt=function(t){return dt*t},gt=parseInt,yt=Math.PI/180,mt=function(t){return yt*t},xt=p,bt=function(t,n){return t.hasOwnProperty(n)},Mt=bt,wt=Object.values?function(t){return Object.values(t)}:function(t){var n=[];return p(t,(function(e,r){s(t)&&"prototype"===r||n.push(e)})),n},Pt=function(t,n){return i(wt(t),n)},_t=function(t){return f(t)?"":t.toString()},At=function(t){return _t(t).toLowerCase()},Ot=function(t){var n=_t(t);return n.charAt(0).toLowerCase()+n.substring(1)};var Ct=function(t,n){return t&&n?t.replace(/\\?\{([^{}]+)\}/g,(function(t,e){return"\\"===t.charAt(0)?t.slice(1):void 0===n[e]?"":n[e]})):t},St=function(t){return _t(t).toUpperCase()},jt=function(t){var n=_t(t);return n.charAt(0).toUpperCase()+n.substring(1)},kt={}.toString,Tt=function(t){return kt.call(t).replace(/^\[object /,"").replace(/]$/,"")},Et=function(t){return c(t,"Arguments")},Bt=function(t){return c(t,"Boolean")},It=function(t){return c(t,"Date")},Ft=function(t){return c(t,"Error")},Dt=function(t){return rt(t)&&isFinite(t)},qt=function(t){return null===t},Xt=Object.prototype,Nt=function(t){var n=t&&t.constructor;return t===("function"==typeof n&&n.prototype||Xt)},Rt=function(t){return c(t,"RegExp")},Lt=function(t){return void 0===t},Yt=function(t){return t instanceof Element||t instanceof HTMLDocument};function zt(t){return(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return setTimeout(t,16)})(t)}function Wt(t){(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.msCancelAnimationFrame||clearTimeout)(t)}function $t(t,n){for(var e in n)n.hasOwnProperty(e)&&"constructor"!==e&&void 0!==n[e]&&(t[e]=n[e])}function Vt(t,n,e,r){return n&&$t(t,n),e&&$t(t,e),r&&$t(t,r),t}var Ht=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var e=t[0],r=1;r<t.length;r++){var i=t[r];s(i)&&(i=i.prototype),Vt(e.prototype,i)}},Qt=function(t){if("object"!=typeof t||null===t)return t;var n;if(h(t)){n=[];for(var e=0,r=t.length;e<r;e++)"object"==typeof t[e]&&null!=t[e]?n[e]=Qt(t[e]):n[e]=t[e]}else for(var i in n={},t)"object"==typeof t[i]&&null!=t[i]?n[i]=Qt(t[i]):n[i]=t[i];return n},Gt=Qt;var Zt=function(t,n,e){var r;return function(){var i=this,a=arguments,o=function(){r=null,e||t.apply(i,a)},u=e&&!r;clearTimeout(r),r=setTimeout(o,n),u&&t.apply(i,a)}},Ut=function(t,n){if(!s(t))throw new TypeError("Expected a function");var e=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var a=n?n.apply(this,r):r[0],o=e.cache;if(o.has(a))return o.get(a);var u=t.apply(this,r);return o.set(a,u),u};return e.cache=new Map,e};function Kt(t,n,e,r){for(var i in e=e||0,r=r||5,n)if(n.hasOwnProperty(i)){var a=n[i];null!==a&&y(a)?(y(t[i])||(t[i]={}),e<r?Kt(t[i],a,e+1,r):t[i]=n[i]):h(a)?(t[i]=[],t[i]=t[i].concat(a)):void 0!==a&&(t[i]=a)}}var Jt=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var r=0;r<n.length;r+=1)Kt(t,n[r]);return t},tn=function(t,n,e,r){s(n)||(e=n,n=t,t=function(){});var i=Object.create?function(t,n){return Object.create(t,{constructor:{value:n}})}:function(t,n){function e(){}e.prototype=t;var r=new e;return r.constructor=n,r},a=i(n.prototype,t);return t.prototype=Vt(a,t.prototype),t.superclass=i(n.prototype,n),Vt(a,e),Vt(t,r),t},nn=function(t,n){if(!r(t))return-1;var e=Array.prototype.indexOf;if(e)return e.call(t,n);for(var i=-1,a=0;a<t.length;a++)if(t[a]===n){i=a;break}return i},en=Object.prototype.hasOwnProperty;var rn=function(t){if(f(t))return!0;if(r(t))return!t.length;var n=Tt(t);if("Map"===n||"Set"===n)return!t.size;if(Nt(t))return!Object.keys(t).length;for(var e in t)if(en.call(t,e))return!1;return!0},an=function(t,n){if(t===n)return!0;if(!t||!n)return!1;if(F(t)||F(n))return!1;if(r(t)||r(n)){if(t.length!==n.length)return!1;for(var e=!0,i=0;i<t.length&&(e=an(t[i],n[i]));i++);return e}if(g(t)||g(n)){var a=Object.keys(t),o=Object.keys(n);if(a.length!==o.length)return!1;for(e=!0,i=0;i<a.length&&(e=an(t[a[i]],n[a[i]]));i++);return e}return!1},on=an,un=function(t,n,e){return s(e)?!!e(t,n):on(t,n)},cn=function(t,n){if(!r(t))return t;for(var e=[],i=0;i<t.length;i++){var a=t[i];e.push(n(a,i))}return e},sn=function(t){return t},fn=function(t,n){void 0===n&&(n=sn);var e={};return l(t)&&!f(t)&&Object.keys(t).forEach((function(r){e[r]=n(t[r],r)})),e},hn=function(t,n,e){for(var r=0,i=F(n)?n.split("."):n;t&&r<i.length;)t=t[i[r++]];return void 0===t||r<i.length?e:t},ln=function(t,n,e){var r=t,i=F(n)?n.split("."):n;return i.forEach((function(t,n){n<i.length-1?(l(r[t])||(r[t]=rt(i[n+1])?[]:{}),r=r[t]):r[t]=e})),t},pn=Object.prototype.hasOwnProperty,dn=function(t,n){if(null===t||!y(t))return{};var e={};return p(n,(function(n){pn.call(t,n)&&(e[n]=t[n])})),e},vn=function(t,n){return B(t,(function(t,e,r){return n.includes(r)||(t[r]=e),t}),{})},gn=function(t,n,e){var r,i,a,o,u=0;e||(e={});var c=function(){u=!1===e.leading?0:Date.now(),r=null,o=t.apply(i,a),r||(i=a=null)},s=function(){var s=Date.now();u||!1!==e.leading||(u=s);var f=n-(s-u);return i=this,a=arguments,f<=0||f>n?(r&&(clearTimeout(r),r=null),u=s,o=t.apply(i,a),r||(i=a=null)):r||!1===e.trailing||(r=setTimeout(c,f)),o};return s.cancel=function(){clearTimeout(r),u=0,r=i=a=null},s},yn=function(t){return r(t)?Array.prototype.slice.call(t):[]},mn={},xn=function(t){return mn[t=t||"g"]?mn[t]+=1:mn[t]=1,t+mn[t]},bn=function(){},Mn=function(t){return t};function wn(t){return f(t)?0:r(t)?t.length:Object.keys(t).length}var Pn,_n=e(6),An=Ut((function(t,n){void 0===n&&(n={});var e=n.fontSize,r=n.fontFamily,i=n.fontWeight,a=n.fontStyle,o=n.fontVariant;return Pn||(Pn=document.createElement("canvas").getContext("2d")),Pn.font=[a,o,i,e+"px",r].join(" "),Pn.measureText(F(t)?t:"").width}),(function(t,n){return void 0===n&&(n={}),Object(_n.__spreadArrays)([t],wt(n)).join("")})),On=function(t,n,e,r){void 0===r&&(r="...");var i,a,o=An(r,e),u=F(t)?t:_t(t),c=n,s=[];if(An(t,e)<=n)return t;for(;i=u.substr(0,16),!((a=An(i,e))+o>c&&a>c);)if(s.push(i),c-=a,!(u=u.substr(16)))return s.join("");for(;i=u.substr(0,1),!((a=An(i,e))+o>c);)if(s.push(i),c-=a,!(u=u.substr(1)))return s.join("");return""+s.join("")+r},Cn=function(){function t(){this.map={}}return t.prototype.has=function(t){return void 0!==this.map[t]},t.prototype.get=function(t,n){var e=this.map[t];return void 0===e?n:e},t.prototype.set=function(t,n){this.map[t]=n},t.prototype.clear=function(){this.map={}},t.prototype.delete=function(t){delete this.map[t]},t.prototype.size=function(){return Object.keys(this.map).length},t}()},function(t,n,e){"use strict";e.r(n),e.d(n,"__extends",(function(){return i})),e.d(n,"__assign",(function(){return a})),e.d(n,"__rest",(function(){return o})),e.d(n,"__decorate",(function(){return u})),e.d(n,"__param",(function(){return c})),e.d(n,"__esDecorate",(function(){return s})),e.d(n,"__runInitializers",(function(){return f})),e.d(n,"__propKey",(function(){return h})),e.d(n,"__setFunctionName",(function(){return l})),e.d(n,"__metadata",(function(){return p})),e.d(n,"__awaiter",(function(){return d})),e.d(n,"__generator",(function(){return v})),e.d(n,"__createBinding",(function(){return g})),e.d(n,"__exportStar",(function(){return y})),e.d(n,"__values",(function(){return m})),e.d(n,"__read",(function(){return x})),e.d(n,"__spread",(function(){return b})),e.d(n,"__spreadArrays",(function(){return M})),e.d(n,"__spreadArray",(function(){return w})),e.d(n,"__await",(function(){return P})),e.d(n,"__asyncGenerator",(function(){return _})),e.d(n,"__asyncDelegator",(function(){return A})),e.d(n,"__asyncValues",(function(){return O})),e.d(n,"__makeTemplateObject",(function(){return C})),e.d(n,"__importStar",(function(){return j})),e.d(n,"__importDefault",(function(){return k})),e.d(n,"__classPrivateFieldGet",(function(){return T})),e.d(n,"__classPrivateFieldSet",(function(){return E})),e.d(n,"__classPrivateFieldIn",(function(){return B}));var r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])})(t,n)};function i(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}var a=function(){return(a=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function o(t,n){var e={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(e[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)n.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(e[r[i]]=t[r[i]])}return e}function u(t,n,e,r){var i,a=arguments.length,o=a<3?n:null===r?r=Object.getOwnPropertyDescriptor(n,e):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,n,e,r);else for(var u=t.length-1;u>=0;u--)(i=t[u])&&(o=(a<3?i(o):a>3?i(n,e,o):i(n,e))||o);return a>3&&o&&Object.defineProperty(n,e,o),o}function c(t,n){return function(e,r){n(e,r,t)}}function s(t,n,e,r,i,a){function o(t){if(void 0!==t&&"function"!=typeof t)throw new TypeError("Function expected");return t}for(var u,c=r.kind,s="getter"===c?"get":"setter"===c?"set":"value",f=!n&&t?r.static?t:t.prototype:null,h=n||(f?Object.getOwnPropertyDescriptor(f,r.name):{}),l=!1,p=e.length-1;p>=0;p--){var d={};for(var v in r)d[v]="access"===v?{}:r[v];for(var v in r.access)d.access[v]=r.access[v];d.addInitializer=function(t){if(l)throw new TypeError("Cannot add initializers after decoration has completed");a.push(o(t||null))};var g=(0,e[p])("accessor"===c?{get:h.get,set:h.set}:h[s],d);if("accessor"===c){if(void 0===g)continue;if(null===g||"object"!=typeof g)throw new TypeError("Object expected");(u=o(g.get))&&(h.get=u),(u=o(g.set))&&(h.set=u),(u=o(g.init))&&i.push(u)}else(u=o(g))&&("field"===c?i.push(u):h[s]=u)}f&&Object.defineProperty(f,r.name,h),l=!0}function f(t,n,e){for(var r=arguments.length>2,i=0;i<n.length;i++)e=r?n[i].call(t,e):n[i].call(t);return r?e:void 0}function h(t){return"symbol"==typeof t?t:"".concat(t)}function l(t,n,e){return"symbol"==typeof n&&(n=n.description?"[".concat(n.description,"]"):""),Object.defineProperty(t,"name",{configurable:!0,value:e?"".concat(e," ",n):n})}function p(t,n){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,n)}function d(t,n,e,r){return new(e||(e=Promise))((function(i,a){function o(t){try{c(r.next(t))}catch(t){a(t)}}function u(t){try{c(r.throw(t))}catch(t){a(t)}}function c(t){var n;t.done?i(t.value):(n=t.value,n instanceof e?n:new e((function(t){t(n)}))).then(o,u)}c((r=r.apply(t,n||[])).next())}))}function v(t,n){var e,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(u){return function(c){return function(u){if(e)throw new TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(o=0)),o;)try{if(e=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return o.label++,{value:u[1],done:!1};case 5:o.label++,r=u[1],u=[0];continue;case 7:u=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==u[0]&&2!==u[0])){o=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){o.label=u[1];break}if(6===u[0]&&o.label<i[1]){o.label=i[1],i=u;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(u);break}i[2]&&o.ops.pop(),o.trys.pop();continue}u=n.call(t,o)}catch(t){u=[6,t],r=0}finally{e=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,c])}}}var g=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]};function y(t,n){for(var e in t)"default"===e||Object.prototype.hasOwnProperty.call(n,e)||g(n,t,e)}function m(t){var n="function"==typeof Symbol&&Symbol.iterator,e=n&&t[n],r=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function x(t,n){var e="function"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var r,i,a=e.call(t),o=[];try{for(;(void 0===n||n-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(e=a.return)&&e.call(a)}finally{if(i)throw i.error}}return o}function b(){for(var t=[],n=0;n<arguments.length;n++)t=t.concat(x(arguments[n]));return t}function M(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var r=Array(t),i=0;for(n=0;n<e;n++)for(var a=arguments[n],o=0,u=a.length;o<u;o++,i++)r[i]=a[o];return r}function w(t,n,e){if(e||2===arguments.length)for(var r,i=0,a=n.length;i<a;i++)!r&&i in n||(r||(r=Array.prototype.slice.call(n,0,i)),r[i]=n[i]);return t.concat(r||Array.prototype.slice.call(n))}function P(t){return this instanceof P?(this.v=t,this):new P(t)}function _(t,n,e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=e.apply(t,n||[]),a=[];return r={},o("next"),o("throw"),o("return"),r[Symbol.asyncIterator]=function(){return this},r;function o(t){i[t]&&(r[t]=function(n){return new Promise((function(e,r){a.push([t,n,e,r])>1||u(t,n)}))})}function u(t,n){try{(e=i[t](n)).value instanceof P?Promise.resolve(e.value.v).then(c,s):f(a[0][2],e)}catch(t){f(a[0][3],t)}var e}function c(t){u("next",t)}function s(t){u("throw",t)}function f(t,n){t(n),a.shift(),a.length&&u(a[0][0],a[0][1])}}function A(t){var n,e;return n={},r("next"),r("throw",(function(t){throw t})),r("return"),n[Symbol.iterator]=function(){return this},n;function r(r,i){n[r]=t[r]?function(n){return(e=!e)?{value:P(t[r](n)),done:!1}:i?i(n):n}:i}}function O(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,e=t[Symbol.asyncIterator];return e?e.call(t):(t=m(t),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(e){n[e]=t[e]&&function(n){return new Promise((function(r,i){(function(t,n,e,r){Promise.resolve(r).then((function(n){t({value:n,done:e})}),n)})(r,i,(n=t[e](n)).done,n.value)}))}}}function C(t,n){return Object.defineProperty?Object.defineProperty(t,"raw",{value:n}):t.raw=n,t}var S=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n};function j(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&g(n,t,e);return S(n,t),n}function k(t){return t&&t.__esModule?t:{default:t}}function T(t,n,e,r){if("a"===e&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof n?t!==n||!r:!n.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===e?r:"a"===e?r.call(t):r?r.value:n.get(t)}function E(t,n,e,r,i){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof n?t!==n||!i:!n.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(t,e):i?i.value=e:n.set(t,e),e}function B(t,n){if(null===n||"object"!=typeof n&&"function"!=typeof n)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof t?n===t:t.has(n)}},function(t,n,e){"use strict";e.r(n),e.d(n,"Quad",(function(){return T})),e.d(n,"Cubic",(function(){return q})),e.d(n,"Arc",(function(){return W})),e.d(n,"Line",(function(){return A})),e.d(n,"Polygon",(function(){return K})),e.d(n,"Polyline",(function(){return Z})),e.d(n,"Util",(function(){return r}));var r={};e.r(r),e.d(r,"distance",(function(){return y})),e.d(r,"isNumberEqual",(function(){return m})),e.d(r,"getBBoxByArray",(function(){return x})),e.d(r,"getBBoxRange",(function(){return b})),e.d(r,"piMod",(function(){return M}));var i={}.toString,a=function(t,n){return i.call(t)==="[object "+n+"]"},o=function(t){return a(t,"Function")},u=function(t){return Array.isArray?Array.isArray(t):a(t,"Array")},c=function(t){var n=typeof t;return null!==t&&"object"===n||"function"===n};var s=function(t,n){if(t)if(u(t))for(var e=0,r=t.length;e<r&&!1!==n(t[e],e);e++);else if(c(t))for(var i in t)if(t.hasOwnProperty(i)&&!1===n(t[i],i))break};Object.keys;var f=function(t){if(u(t))return t.reduce((function(t,n){return Math.max(t,n)}),t[0])},h=function(t){if(u(t))return t.reduce((function(t,n){return Math.min(t,n)}),t[0])},l=Array.prototype,p=(l.splice,l.indexOf,Array.prototype.splice,function(t){return a(t,"String")});Object.prototype.hasOwnProperty;var d=function(t){return a(t,"Number")};Number.isInteger&&Number.isInteger;Math.PI,parseInt,Math.PI;var v=Object.values?function(t){return Object.values(t)}:function(t){var n=[];return s(t,(function(e,r){o(t)&&"prototype"===r||n.push(e)})),n};Object.prototype;Object.prototype.hasOwnProperty;Object.prototype.hasOwnProperty;Object.create;var g;Object.create;(function(t,n){if(!o(t))throw new TypeError("Expected a function");var e=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var a=n?n.apply(this,r):r[0],o=e.cache;if(o.has(a))return o.get(a);var u=t.apply(this,r);return o.set(a,u),u};e.cache=new Map})((function(t,n){void 0===n&&(n={});var e=n.fontSize,r=n.fontFamily,i=n.fontWeight,a=n.fontStyle,o=n.fontVariant;return g||(g=document.createElement("canvas").getContext("2d")),g.font=[a,o,i,e+"px",r].join(" "),g.measureText(p(t)?t:"").width}),(function(t,n){return void 0===n&&(n={}),function(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var r=Array(t),i=0;for(n=0;n<e;n++)for(var a=arguments[n],o=0,u=a.length;o<u;o++,i++)r[i]=a[o];return r}([t],v(n)).join("")})),function(){function t(){this.map={}}t.prototype.has=function(t){return void 0!==this.map[t]},t.prototype.get=function(t,n){var e=this.map[t];return void 0===e?n:e},t.prototype.set=function(t,n){this.map[t]=n},t.prototype.clear=function(){this.map={}},t.prototype.delete=function(t){delete this.map[t]},t.prototype.size=function(){return Object.keys(this.map).length}}();function y(t,n,e,r){var i=t-e,a=n-r;return Math.sqrt(i*i+a*a)}function m(t,n){return Math.abs(t-n)<.001}function x(t,n){var e=h(t),r=h(n);return{x:e,y:r,width:f(t)-e,height:f(n)-r}}function b(t,n,e,r){return{minX:h([t,e]),maxX:f([t,e]),minY:h([n,r]),maxY:f([n,r])}}function M(t){return(t+2*Math.PI)%(2*Math.PI)}var w="undefined"!=typeof Float32Array?Float32Array:Array;Math.random;Math.PI;Math.hypot||(Math.hypot=function(){for(var t=0,n=arguments.length;n--;)t+=arguments[n]*arguments[n];return Math.sqrt(t)});P=new w(2),w!=Float32Array&&(P[0]=0,P[1]=0),_=P;var P,_,A={box:function(t,n,e,r){return x([t,e],[n,r])},length:function(t,n,e,r){return y(t,n,e,r)},pointAt:function(t,n,e,r,i){return{x:(1-i)*t+i*e,y:(1-i)*n+i*r}},pointDistance:function(t,n,e,r,i,a){var o=(e-t)*(i-t)+(r-n)*(a-n);return o<0?y(t,n,i,a):o>(e-t)*(e-t)+(r-n)*(r-n)?y(e,r,i,a):this.pointToLine(t,n,e,r,i,a)},pointToLine:function(t,n,e,r,i,a){var o=[e-t,r-n];if(function(t,n){return t[0]===n[0]&&t[1]===n[1]}(o,[0,0]))return Math.sqrt((i-t)*(i-t)+(a-n)*(a-n));var u=[-o[1],o[0]];!function(t,n){var e=n[0],r=n[1],i=e*e+r*r;i>0&&(i=1/Math.sqrt(i)),t[0]=n[0]*i,t[1]=n[1]*i}(u,u);var c=[i-t,a-n];return Math.abs(function(t,n){return t[0]*n[0]+t[1]*n[1]}(c,u))},tangentAngle:function(t,n,e,r){return Math.atan2(r-n,e-t)}};function O(t,n,e,r,i,a){var o,u=1/0,c=[e,r],s=20;a&&a>200&&(s=a/10);for(var f=1/s,h=f/10,l=0;l<=s;l++){var p=l*f,d=[i.apply(null,t.concat([p])),i.apply(null,n.concat([p]))];(x=y(c[0],c[1],d[0],d[1]))<u&&(o=p,u=x)}if(0===o)return{x:t[0],y:n[0]};if(1===o){var v=t.length;return{x:t[v-1],y:n[v-1]}}u=1/0;for(l=0;l<32&&!(h<1e-4);l++){var g=o-h,m=o+h,x=(d=[i.apply(null,t.concat([g])),i.apply(null,n.concat([g]))],y(c[0],c[1],d[0],d[1]));if(g>=0&&x<u)o=g,u=x;else{var b=[i.apply(null,t.concat([m])),i.apply(null,n.concat([m]))],M=y(c[0],c[1],b[0],b[1]);m<=1&&M<u?(o=m,u=M):h*=.5}}return{x:i.apply(null,t.concat([o])),y:i.apply(null,n.concat([o]))}}function C(t,n,e,r){var i=1-r;return i*i*t+2*r*i*n+r*r*e}function S(t,n,e){var r=t+e-2*n;if(m(r,0))return[.5];var i=(t-n)/r;return i<=1&&i>=0?[i]:[]}function j(t,n,e,r){return 2*(1-r)*(n-t)+2*r*(e-n)}function k(t,n,e,r,i,a,o){var u=C(t,e,i,o),c=C(n,r,a,o),s=A.pointAt(t,n,e,r,o),f=A.pointAt(e,r,i,a,o);return[[t,n,s.x,s.y,u,c],[u,c,f.x,f.y,i,a]]}var T={box:function(t,n,e,r,i,a){var o=S(t,e,i)[0],u=S(n,r,a)[0],c=[t,i],s=[n,a];return void 0!==o&&c.push(C(t,e,i,o)),void 0!==u&&s.push(C(n,r,a,u)),x(c,s)},length:function(t,n,e,r,i,a){return function t(n,e,r,i,a,o,u){if(0===u)return(y(n,e,r,i)+y(r,i,a,o)+y(n,e,a,o))/2;var c=k(n,e,r,i,a,o,.5),s=c[0],f=c[1];return s.push(u-1),f.push(u-1),t.apply(null,s)+t.apply(null,f)}(t,n,e,r,i,a,3)},nearestPoint:function(t,n,e,r,i,a,o,u){return O([t,e,i],[n,r,a],o,u,C)},pointDistance:function(t,n,e,r,i,a,o,u){var c=this.nearestPoint(t,n,e,r,i,a,o,u);return y(c.x,c.y,o,u)},interpolationAt:C,pointAt:function(t,n,e,r,i,a,o){return{x:C(t,e,i,o),y:C(n,r,a,o)}},divide:function(t,n,e,r,i,a,o){return k(t,n,e,r,i,a,o)},tangentAngle:function(t,n,e,r,i,a,o){var u=j(t,e,i,o),c=j(n,r,a,o);return M(Math.atan2(c,u))}};function E(t,n,e,r,i){var a=1-i;return a*a*a*t+3*n*i*a*a+3*e*i*i*a+r*i*i*i}function B(t,n,e,r,i){var a=1-i;return 3*(a*a*(n-t)+2*a*i*(e-n)+i*i*(r-e))}function I(t,n,e,r){var i,a,o,u=-3*t+9*n-9*e+3*r,c=6*t-12*n+6*e,s=3*n-3*t,f=[];if(m(u,0))m(c,0)||(i=-s/c)>=0&&i<=1&&f.push(i);else{var h=c*c-4*u*s;m(h,0)?f.push(-c/(2*u)):h>0&&(a=(-c-(o=Math.sqrt(h)))/(2*u),(i=(-c+o)/(2*u))>=0&&i<=1&&f.push(i),a>=0&&a<=1&&f.push(a))}return f}function F(t,n,e,r,i,a,o,u,c){var s=E(t,e,i,o,c),f=E(n,r,a,u,c),h=A.pointAt(t,n,e,r,c),l=A.pointAt(e,r,i,a,c),p=A.pointAt(i,a,o,u,c),d=A.pointAt(h.x,h.y,l.x,l.y,c),v=A.pointAt(l.x,l.y,p.x,p.y,c);return[[t,n,h.x,h.y,d.x,d.y,s,f],[s,f,v.x,v.y,p.x,p.y,o,u]]}function D(t,n,e,r,i,a,o,u,c){if(0===c)return function(t,n){for(var e=0,r=t.length,i=0;i<r;i++){e+=y(t[i],n[i],t[(i+1)%r],n[(i+1)%r])}return e/2}([t,e,i,o],[n,r,a,u]);var s=F(t,n,e,r,i,a,o,u,.5),f=s[0],h=s[1];return f.push(c-1),h.push(c-1),D.apply(null,f)+D.apply(null,h)}var q={extrema:I,box:function(t,n,e,r,i,a,o,u){for(var c=[t,o],s=[n,u],f=I(t,e,i,o),h=I(n,r,a,u),l=0;l<f.length;l++)c.push(E(t,e,i,o,f[l]));for(l=0;l<h.length;l++)s.push(E(n,r,a,u,h[l]));return x(c,s)},length:function(t,n,e,r,i,a,o,u){return D(t,n,e,r,i,a,o,u,3)},nearestPoint:function(t,n,e,r,i,a,o,u,c,s,f){return O([t,e,i,o],[n,r,a,u],c,s,E,f)},pointDistance:function(t,n,e,r,i,a,o,u,c,s,f){var h=this.nearestPoint(t,n,e,r,i,a,o,u,c,s,f);return y(h.x,h.y,c,s)},interpolationAt:E,pointAt:function(t,n,e,r,i,a,o,u,c){return{x:E(t,e,i,o,c),y:E(n,r,a,u,c)}},divide:function(t,n,e,r,i,a,o,u,c){return F(t,n,e,r,i,a,o,u,c)},tangentAngle:function(t,n,e,r,i,a,o,u,c){var s=B(t,e,i,o,c),f=B(n,r,a,u,c);return M(Math.atan2(f,s))}};function X(t,n){var e=Math.abs(t);return n>0?e:-1*e}var N=function(t,n,e,r,i,a){var o=e,u=r;if(0===o||0===u)return{x:t,y:n};for(var c,s,f=i-t,h=a-n,l=Math.abs(f),p=Math.abs(h),d=o*o,v=u*u,g=Math.PI/4,y=0;y<4;y++){c=o*Math.cos(g),s=u*Math.sin(g);var m=(d-v)*Math.pow(Math.cos(g),3)/o,x=(v-d)*Math.pow(Math.sin(g),3)/u,b=c-m,M=s-x,w=l-m,P=p-x,_=Math.hypot(M,b),A=Math.hypot(P,w);g+=_*Math.asin((b*P-M*w)/(_*A))/Math.sqrt(d+v-c*c-s*s),g=Math.min(Math.PI/2,Math.max(0,g))}return{x:t+X(c,f),y:n+X(s,h)}};function R(t,n,e,r,i,a){return e*Math.cos(i)*Math.cos(a)-r*Math.sin(i)*Math.sin(a)+t}function L(t,n,e,r,i,a){return e*Math.sin(i)*Math.cos(a)+r*Math.cos(i)*Math.sin(a)+n}function Y(t,n,e){return{x:t*Math.cos(e),y:n*Math.sin(e)}}function z(t,n,e){var r=Math.cos(e),i=Math.sin(e);return[t*r-n*i,t*i+n*r]}var W={box:function(t,n,e,r,i,a,o){for(var u=function(t,n,e){return Math.atan(-n/t*Math.tan(e))}(e,r,i),c=1/0,s=-1/0,f=[a,o],h=2*-Math.PI;h<=2*Math.PI;h+=Math.PI){var l=u+h;a<o?a<l&&l<o&&f.push(l):o<l&&l<a&&f.push(l)}for(h=0;h<f.length;h++){var p=R(t,0,e,r,i,f[h]);p<c&&(c=p),p>s&&(s=p)}var d=function(t,n,e){return Math.atan(n/(t*Math.tan(e)))}(e,r,i),v=1/0,g=-1/0,y=[a,o];for(h=2*-Math.PI;h<=2*Math.PI;h+=Math.PI){var m=d+h;a<o?a<m&&m<o&&y.push(m):o<m&&m<a&&y.push(m)}for(h=0;h<y.length;h++){var x=L(0,n,e,r,i,y[h]);x<v&&(v=x),x>g&&(g=x)}return{x:c,y:v,width:s-c,height:g-v}},length:function(t,n,e,r,i,a,o){},nearestPoint:function(t,n,e,r,i,a,o,u,c){var s=z(u-t,c-n,-i),f=s[0],h=s[1],l=N(0,0,e,r,f,h),p=function(t,n,e,r){return(Math.atan2(r*t,e*n)+2*Math.PI)%(2*Math.PI)}(e,r,l.x,l.y);p<a?l=Y(e,r,a):p>o&&(l=Y(e,r,o));var d=z(l.x,l.y,i);return{x:d[0]+t,y:d[1]+n}},pointDistance:function(t,n,e,r,i,a,o,u,c){var s=this.nearestPoint(t,n,e,r,u,c);return y(s.x,s.y,u,c)},pointAt:function(t,n,e,r,i,a,o,u){var c=(o-a)*u+a;return{x:R(t,0,e,r,i,c),y:L(0,n,e,r,i,c)}},tangentAngle:function(t,n,e,r,i,a,o,u){var c=(o-a)*u+a,s=function(t,n,e,r,i,a,o,u){return-1*e*Math.cos(i)*Math.sin(u)-r*Math.sin(i)*Math.cos(u)}(0,0,e,r,i,0,0,c),f=function(t,n,e,r,i,a,o,u){return-1*e*Math.sin(i)*Math.sin(u)+r*Math.cos(i)*Math.cos(u)}(0,0,e,r,i,0,0,c);return M(Math.atan2(f,s))}};function $(t){for(var n=0,e=[],r=0;r<t.length-1;r++){var i=t[r],a=t[r+1],o=y(i[0],i[1],a[0],a[1]),u={from:i,to:a,length:o};e.push(u),n+=o}return{segments:e,totalLength:n}}function V(t){if(t.length<2)return 0;for(var n=0,e=0;e<t.length-1;e++){var r=t[e],i=t[e+1];n+=y(r[0],r[1],i[0],i[1])}return n}function H(t,n){if(n>1||n<0||t.length<2)return null;var e=$(t),r=e.segments,i=e.totalLength;if(0===i)return{x:t[0][0],y:t[0][1]};for(var a=0,o=null,u=0;u<r.length;u++){var c=r[u],s=c.from,f=c.to,h=c.length/i;if(n>=a&&n<=a+h){var l=(n-a)/h;o=A.pointAt(s[0],s[1],f[0],f[1],l);break}a+=h}return o}function Q(t,n){if(n>1||n<0||t.length<2)return 0;for(var e=$(t),r=e.segments,i=e.totalLength,a=0,o=0,u=0;u<r.length;u++){var c=r[u],s=c.from,f=c.to,h=c.length/i;if(n>=a&&n<=a+h){o=Math.atan2(f[1]-s[1],f[0]-s[0]);break}a+=h}return o}function G(t,n,e){for(var r=1/0,i=0;i<t.length-1;i++){var a=t[i],o=t[i+1],u=A.pointDistance(a[0],a[1],o[0],o[1],n,e);u<r&&(r=u)}return r}var Z={box:function(t){for(var n=[],e=[],r=0;r<t.length;r++){var i=t[r];n.push(i[0]),e.push(i[1])}return x(n,e)},length:function(t){return V(t)},pointAt:function(t,n){return H(t,n)},pointDistance:function(t,n,e){return G(t,n,e)},tangentAngle:function(t,n){return Q(t,n)}};function U(t){var n=t.slice(0);return t.length&&n.push(t[0]),n}var K={box:function(t){return Z.box(t)},length:function(t){return V(U(t))},pointAt:function(t,n){return H(U(t),n)},pointDistance:function(t,n,e){return G(U(t),n,e)},tangentAngle:function(t,n){return Q(U(t),n)}}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.clearAnimationFrame=n.requestAnimationFrame=n.isNumberEqual=n.mod=n.toRadian=n.each=n.isArray=n.isFunction=n.isString=n.isNil=n.isSamePoint=n.mergeRegion=n.intersectRect=n.inBox=n.distance=n.getPixelRatio=void 0,n.getPixelRatio=function(){return window?window.devicePixelRatio:1},n.distance=function(t,n,e,r){var i=t-e,a=n-r;return Math.sqrt(i*i+a*a)},n.inBox=function(t,n,e,r,i,a){return i>=t&&i<=t+e&&a>=n&&a<=n+r},n.intersectRect=function(t,n){return!(n.minX>t.maxX||n.maxX<t.minX||n.minY>t.maxY||n.maxY<t.minY)},n.mergeRegion=function(t,n){return t&&n?{minX:Math.min(t.minX,n.minX),minY:Math.min(t.minY,n.minY),maxX:Math.max(t.maxX,n.maxX),maxY:Math.max(t.maxY,n.maxY)}:t||n},n.isSamePoint=function(t,n){return t[0]===n[0]&&t[1]===n[1]};var r=e(5);Object.defineProperty(n,"isNil",{enumerable:!0,get:function(){return r.isNil}}),Object.defineProperty(n,"isString",{enumerable:!0,get:function(){return r.isString}}),Object.defineProperty(n,"isFunction",{enumerable:!0,get:function(){return r.isFunction}}),Object.defineProperty(n,"isArray",{enumerable:!0,get:function(){return r.isArray}}),Object.defineProperty(n,"each",{enumerable:!0,get:function(){return r.each}}),Object.defineProperty(n,"toRadian",{enumerable:!0,get:function(){return r.toRadian}}),Object.defineProperty(n,"mod",{enumerable:!0,get:function(){return r.mod}}),Object.defineProperty(n,"isNumberEqual",{enumerable:!0,get:function(){return r.isNumberEqual}}),Object.defineProperty(n,"requestAnimationFrame",{enumerable:!0,get:function(){return r.requestAnimationFrame}}),Object.defineProperty(n,"clearAnimationFrame",{enumerable:!0,get:function(){return r.clearAnimationFrame}})},function(t,n,e){"use strict";e.d(n,"a",(function(){return i})),e.d(n,"b",(function(){return a}));var r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])})(t,n)};function i(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}Object.create;function a(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var r=Array(t),i=0;for(n=0;n<e;n++)for(var a=arguments[n],o=0,u=a.length;o<u;o++,i++)r[i]=a[o];return r}Object.create},function(t,n,e){"use strict";function r(t,n){var e=[],r=t[0],i=t[1],a=t[2],o=t[3],u=t[4],c=t[5],s=t[6],f=t[7],h=t[8],l=n[0],p=n[1],d=n[2],v=n[3],g=n[4],y=n[5],m=n[6],x=n[7],b=n[8];return e[0]=l*r+p*o+d*s,e[1]=l*i+p*u+d*f,e[2]=l*a+p*c+d*h,e[3]=v*r+g*o+y*s,e[4]=v*i+g*u+y*f,e[5]=v*a+g*c+y*h,e[6]=m*r+x*o+b*s,e[7]=m*i+x*u+b*f,e[8]=m*a+x*c+b*h,e}function i(t,n){var e=[],r=n[0],i=n[1];return e[0]=t[0]*r+t[3]*i+t[6],e[1]=t[1]*r+t[4]*i+t[7],e}function a(t){var n=[],e=t[0],r=t[1],i=t[2],a=t[3],o=t[4],u=t[5],c=t[6],s=t[7],f=t[8],h=f*o-u*s,l=-f*a+u*c,p=s*a-o*c,d=e*h+r*l+i*p;return d?(d=1/d,n[0]=h*d,n[1]=(-f*r+i*s)*d,n[2]=(u*r-i*o)*d,n[3]=l*d,n[4]=(f*e-i*c)*d,n[5]=(-u*e+i*a)*d,n[6]=p*d,n[7]=(-s*e+r*c)*d,n[8]=(o*e-r*a)*d,n):null}e.d(n,"b",(function(){return r})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){return a}))},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(12),a=e(8),o=e(16),u=e(12),c=e(15),s=e(25),f=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{lineWidth:1,lineAppendWidth:0,strokeOpacity:1,fillOpacity:1})},n.prototype.getShapeBase=function(){return c},n.prototype.getGroupBase=function(){return s.default},n.prototype.onCanvasChange=function(t){(0,o.refreshElement)(this,t)},n.prototype.calculateBBox=function(){var t=this.get("type"),n=this.getHitLineWidth(),e=(0,u.getBBoxMethod)(t)(this),r=n/2,i=e.x-r,a=e.y-r,o=e.x+e.width+r,c=e.y+e.height+r;return{x:i,minX:i,y:a,minY:a,width:e.width+n,height:e.height+n,maxX:o,maxY:c}},n.prototype.isFill=function(){return!!this.attrs.fill||this.isClipShape()},n.prototype.isStroke=function(){return!!this.attrs.stroke},n.prototype._applyClip=function(t,n){n&&(t.save(),(0,o.applyAttrsToContext)(t,n),n.createPath(t),t.restore(),t.clip(),n._afterDraw())},n.prototype.draw=function(t,n){var e=this.cfg.clipShape;if(n){if(!1===this.cfg.refresh)return void this.set("hasChanged",!1);var r=this.getCanvasBBox();if(!(0,a.intersectRect)(n,r))return this.set("hasChanged",!1),void(this.cfg.isInView&&this._afterDraw())}t.save(),(0,o.applyAttrsToContext)(t,this),this._applyClip(t,e),this.drawPath(t),t.restore(),this._afterDraw()},n.prototype.getCanvasViewBox=function(){var t=this.cfg.canvas;return t?t.getViewRange():null},n.prototype.cacheCanvasBBox=function(){var t=this.getCanvasViewBox();if(t){var n=this.getCanvasBBox(),e=(0,a.intersectRect)(n,t);this.set("isInView",e),e?this.set("cacheCanvasBBox",n):this.set("cacheCanvasBBox",null)}},n.prototype._afterDraw=function(){this.cacheCanvasBBox(),this.set("hasChanged",!1),this.set("refresh",null)},n.prototype.skipDraw=function(){this.set("cacheCanvasBBox",null),this.set("isInView",null),this.set("hasChanged",!1)},n.prototype.drawPath=function(t){this.createPath(t),this.strokeAndFill(t),this.afterDrawPath(t)},n.prototype.fill=function(t){t.fill()},n.prototype.stroke=function(t){t.stroke()},n.prototype.strokeAndFill=function(t){var n=this.attrs,e=n.lineWidth,r=n.opacity,i=n.strokeOpacity,o=n.fillOpacity;this.isFill()&&((0,a.isNil)(o)||1===o?this.fill(t):(t.globalAlpha=o,this.fill(t),t.globalAlpha=r)),this.isStroke()&&e>0&&((0,a.isNil)(i)||1===i||(t.globalAlpha=i),this.stroke(t)),this.afterDrawPath(t)},n.prototype.createPath=function(t){},n.prototype.afterDrawPath=function(t){},n.prototype.isInShape=function(t,n){var e=this.isStroke(),r=this.isFill(),i=this.getHitLineWidth();return this.isInStrokeOrPath(t,n,e,r,i)},n.prototype.isInStrokeOrPath=function(t,n,e,r,i){return!1},n.prototype.getHitLineWidth=function(){if(!this.isStroke())return 0;var t=this.attrs;return t.lineWidth+t.lineAppendWidth},n}(i.AbstractShape);n.default=f},function(t,n,e){"use strict";e.r(n),e.d(n,"version",(function(){return m}));var r=e(13);e.d(n,"PathUtil",(function(){return r}));var i=e(28);for(var a in i)["default","Event","Base","AbstractCanvas","AbstractGroup","AbstractShape","PathUtil","getBBoxMethod","registerBBox","getTextHeight","assembleFont","isAllowCapture","multiplyVec2","invert","getOffScreenContext","registerEasing","version"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(a);var o=e(29);for(var a in o)["default","Event","Base","AbstractCanvas","AbstractGroup","AbstractShape","PathUtil","getBBoxMethod","registerBBox","getTextHeight","assembleFont","isAllowCapture","multiplyVec2","invert","getOffScreenContext","registerEasing","version"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(a);var u=e(19);e.d(n,"Event",(function(){return u.a}));var c=e(22);e.d(n,"Base",(function(){return c.a}));var s=e(39);e.d(n,"AbstractCanvas",(function(){return s.a}));var f=e(30);e.d(n,"AbstractGroup",(function(){return f.a}));var h=e(31);e.d(n,"AbstractShape",(function(){return h.a}));var l=e(27);e.d(n,"getBBoxMethod",(function(){return l.a})),e.d(n,"registerBBox",(function(){return l.b}));var p=e(14);e.d(n,"getTextHeight",(function(){return p.b})),e.d(n,"assembleFont",(function(){return p.a}));var d=e(1);e.d(n,"isAllowCapture",(function(){return d.b}));var v=e(10);e.d(n,"multiplyVec2",(function(){return v.c})),e.d(n,"invert",(function(){return v.a}));var g=e(20);e.d(n,"getOffScreenContext",(function(){return g.a}));var y=e(21);e.d(n,"registerEasing",(function(){return y.b}));var m="0.5.11"},function(t,n,e){"use strict";e.r(n),e.d(n,"catmullRomToBezier",(function(){return c})),e.d(n,"fillPath",(function(){return k})),e.d(n,"fillPathByDiff",(function(){return B})),e.d(n,"formatPath",(function(){return D})),e.d(n,"intersection",(function(){return C})),e.d(n,"parsePathArray",(function(){return g})),e.d(n,"parsePathString",(function(){return u})),e.d(n,"pathToAbsolute",(function(){return f})),e.d(n,"pathToCurve",(function(){return d})),e.d(n,"rectPath",(function(){return w}));var r=e(0),i="\t\n\v\f\r   ᠎             　\u2028\u2029",a=new RegExp("([a-z])["+i+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+i+"]*,?["+i+"]*)+)","ig"),o=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+i+"]*,?["+i+"]*","ig"),u=function(t){if(!t)return null;if(Object(r.b)(t))return t;var n={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},e=[];return String(t).replace(a,(function(r,i,a){var u=[],c=i.toLowerCase();if(a.replace(o,(function(t,n){n&&u.push(+n)})),"m"===c&&u.length>2&&(e.push([i].concat(u.splice(0,2))),c="l",i="m"===i?"l":"L"),"o"===c&&1===u.length&&e.push([i,u[0]]),"r"===c)e.push([i].concat(u));else for(;u.length>=n[c]&&(e.push([i].concat(u.splice(0,n[c]))),n[c]););return t})),e},c=function(t,n){for(var e=[],r=0,i=t.length;i-2*!n>r;r+=2){var a=[{x:+t[r-2],y:+t[r-1]},{x:+t[r],y:+t[r+1]},{x:+t[r+2],y:+t[r+3]},{x:+t[r+4],y:+t[r+5]}];n?r?i-4===r?a[3]={x:+t[0],y:+t[1]}:i-2===r&&(a[2]={x:+t[0],y:+t[1]},a[3]={x:+t[2],y:+t[3]}):a[0]={x:+t[i-2],y:+t[i-1]}:i-4===r?a[3]=a[2]:r||(a[0]={x:+t[r],y:+t[r+1]}),e.push(["C",(-a[0].x+6*a[1].x+a[2].x)/6,(-a[0].y+6*a[1].y+a[2].y)/6,(a[1].x+6*a[2].x-a[3].x)/6,(a[1].y+6*a[2].y-a[3].y)/6,a[2].x,a[2].y])}return e},s=function(t,n,e,r,i){var a=[];if(null===i&&null===r&&(r=e),t=+t,n=+n,e=+e,r=+r,null!==i){var o=Math.PI/180,u=t+e*Math.cos(-r*o),c=t+e*Math.cos(-i*o);a=[["M",u,n+e*Math.sin(-r*o)],["A",e,e,0,+(i-r>180),0,c,n+e*Math.sin(-i*o)]]}else a=[["M",t,n],["m",0,-r],["a",e,r,0,1,1,0,2*r],["a",e,r,0,1,1,0,-2*r],["z"]];return a},f=function(t){if(!(t=u(t))||!t.length)return[["M",0,0]];var n,e,r=[],i=0,a=0,o=0,f=0,h=0;"M"===t[0][0]&&(o=i=+t[0][1],f=a=+t[0][2],h++,r[0]=["M",i,a]);for(var l=3===t.length&&"M"===t[0][0]&&"R"===t[1][0].toUpperCase()&&"Z"===t[2][0].toUpperCase(),p=void 0,d=void 0,v=h,g=t.length;v<g;v++){if(r.push(p=[]),(n=(d=t[v])[0])!==n.toUpperCase())switch(p[0]=n.toUpperCase(),p[0]){case"A":p[1]=d[1],p[2]=d[2],p[3]=d[3],p[4]=d[4],p[5]=d[5],p[6]=+d[6]+i,p[7]=+d[7]+a;break;case"V":p[1]=+d[1]+a;break;case"H":p[1]=+d[1]+i;break;case"R":for(var y=2,m=(e=[i,a].concat(d.slice(1))).length;y<m;y++)e[y]=+e[y]+i,e[++y]=+e[y]+a;r.pop(),r=r.concat(c(e,l));break;case"O":r.pop(),(e=s(i,a,d[1],d[2])).push(e[0]),r=r.concat(e);break;case"U":r.pop(),r=r.concat(s(i,a,d[1],d[2],d[3])),p=["U"].concat(r[r.length-1].slice(-2));break;case"M":o=+d[1]+i,f=+d[2]+a;break;default:for(y=1,m=d.length;y<m;y++)p[y]=+d[y]+(y%2?i:a)}else if("R"===n)e=[i,a].concat(d.slice(1)),r.pop(),r=r.concat(c(e,l)),p=["R"].concat(d.slice(-2));else if("O"===n)r.pop(),(e=s(i,a,d[1],d[2])).push(e[0]),r=r.concat(e);else if("U"===n)r.pop(),r=r.concat(s(i,a,d[1],d[2],d[3])),p=["U"].concat(r[r.length-1].slice(-2));else for(var x=0,b=d.length;x<b;x++)p[x]=d[x];if("O"!==(n=n.toUpperCase()))switch(p[0]){case"Z":i=+o,a=+f;break;case"H":i=p[1];break;case"V":a=p[1];break;case"M":o=p[p.length-2],f=p[p.length-1];break;default:i=p[p.length-2],a=p[p.length-1]}}return r},h=function(t,n,e,r){return[t,n,e,r,e,r]},l=function(t,n,e,r,i,a){return[1/3*t+2/3*e,1/3*n+2/3*r,1/3*i+2/3*e,1/3*a+2/3*r,i,a]},p=function(t,n,e,r,i,a,o,u,c,s){e===r&&(e+=1);var f,h,l,d,v,g=120*Math.PI/180,y=Math.PI/180*(+i||0),m=[],x=function(t,n,e){return{x:t*Math.cos(e)-n*Math.sin(e),y:t*Math.sin(e)+n*Math.cos(e)}};if(s)h=s[0],l=s[1],d=s[2],v=s[3];else{t=(f=x(t,n,-y)).x,n=f.y,u=(f=x(u,c,-y)).x,c=f.y,t===u&&n===c&&(u+=1,c+=1);var b=(t-u)/2,M=(n-c)/2,w=b*b/(e*e)+M*M/(r*r);w>1&&(e*=w=Math.sqrt(w),r*=w);var P=e*e,_=r*r,A=(a===o?-1:1)*Math.sqrt(Math.abs((P*_-P*M*M-_*b*b)/(P*M*M+_*b*b)));d=A*e*M/r+(t+u)/2,v=A*-r*b/e+(n+c)/2,h=Math.asin(((n-v)/r).toFixed(9)),l=Math.asin(((c-v)/r).toFixed(9)),h=t<d?Math.PI-h:h,l=u<d?Math.PI-l:l,h<0&&(h=2*Math.PI+h),l<0&&(l=2*Math.PI+l),o&&h>l&&(h-=2*Math.PI),!o&&l>h&&(l-=2*Math.PI)}var O=l-h;if(Math.abs(O)>g){var C=l,S=u,j=c;l=h+g*(o&&l>h?1:-1),u=d+e*Math.cos(l),c=v+r*Math.sin(l),m=p(u,c,e,r,i,0,o,S,j,[l,C,d,v])}O=l-h;var k=Math.cos(h),T=Math.sin(h),E=Math.cos(l),B=Math.sin(l),I=Math.tan(O/4),F=4/3*e*I,D=4/3*r*I,q=[t,n],X=[t+F*T,n-D*k],N=[u+F*B,c-D*E],R=[u,c];if(X[0]=2*q[0]-X[0],X[1]=2*q[1]-X[1],s)return[X,N,R].concat(m);for(var L=[],Y=0,z=(m=[X,N,R].concat(m).join().split(",")).length;Y<z;Y++)L[Y]=Y%2?x(m[Y-1],m[Y],y).y:x(m[Y],m[Y+1],y).x;return L},d=function(t,n){var e,r=f(t),i=n&&f(n),a={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},o={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},u=[],c=[],s="",d="",v=function(t,n,e){var r,i;if(!t)return["C",n.x,n.y,n.x,n.y,n.x,n.y];switch(!(t[0]in{T:1,Q:1})&&(n.qx=n.qy=null),t[0]){case"M":n.X=t[1],n.Y=t[2];break;case"A":t=["C"].concat(p.apply(0,[n.x,n.y].concat(t.slice(1))));break;case"S":"C"===e||"S"===e?(r=2*n.x-n.bx,i=2*n.y-n.by):(r=n.x,i=n.y),t=["C",r,i].concat(t.slice(1));break;case"T":"Q"===e||"T"===e?(n.qx=2*n.x-n.qx,n.qy=2*n.y-n.qy):(n.qx=n.x,n.qy=n.y),t=["C"].concat(l(n.x,n.y,n.qx,n.qy,t[1],t[2]));break;case"Q":n.qx=t[1],n.qy=t[2],t=["C"].concat(l(n.x,n.y,t[1],t[2],t[3],t[4]));break;case"L":t=["C"].concat(h(n.x,n.y,t[1],t[2]));break;case"H":t=["C"].concat(h(n.x,n.y,t[1],n.y));break;case"V":t=["C"].concat(h(n.x,n.y,n.x,t[1]));break;case"Z":t=["C"].concat(h(n.x,n.y,n.X,n.Y))}return t},g=function(t,n){if(t[n].length>7){t[n].shift();for(var a=t[n];a.length;)u[n]="A",i&&(c[n]="A"),t.splice(n++,0,["C"].concat(a.splice(0,6)));t.splice(n,1),e=Math.max(r.length,i&&i.length||0)}},y=function(t,n,a,o,u){t&&n&&"M"===t[u][0]&&"M"!==n[u][0]&&(n.splice(u,0,["M",o.x,o.y]),a.bx=0,a.by=0,a.x=t[u][1],a.y=t[u][2],e=Math.max(r.length,i&&i.length||0))};e=Math.max(r.length,i&&i.length||0);for(var m=0;m<e;m++){r[m]&&(s=r[m][0]),"C"!==s&&(u[m]=s,m&&(d=u[m-1])),r[m]=v(r[m],a,d),"A"!==u[m]&&"C"===s&&(u[m]="C"),g(r,m),i&&(i[m]&&(s=i[m][0]),"C"!==s&&(c[m]=s,m&&(d=c[m-1])),i[m]=v(i[m],o,d),"A"!==c[m]&&"C"===s&&(c[m]="C"),g(i,m)),y(r,i,a,o,m),y(i,r,o,a,m);var x=r[m],b=i&&i[m],M=x.length,w=i&&b.length;a.x=x[M-2],a.y=x[M-1],a.bx=parseFloat(x[M-4])||a.x,a.by=parseFloat(x[M-3])||a.y,o.bx=i&&(parseFloat(b[w-4])||o.x),o.by=i&&(parseFloat(b[w-3])||o.y),o.x=i&&b[w-2],o.y=i&&b[w-1]}return i?[r,i]:r},v=/,?([a-z]),?/gi,g=function(t){return t.join(",").replace(v,"$1")},y=function(t,n,e,r,i){return t*(t*(-3*n+9*e-9*r+3*i)+6*n-12*e+6*r)-3*n+3*e},m=function(t,n,e,r,i,a,o,u,c){null===c&&(c=1);for(var s=(c=c>1?1:c<0?0:c)/2,f=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],h=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],l=0,p=0;p<12;p++){var d=s*f[p]+s,v=y(d,t,e,i,o),g=y(d,n,r,a,u),m=v*v+g*g;l+=h[p]*Math.sqrt(m)}return s*l},x=function(t,n,e,r,i,a,o,u){for(var c,s,f,h,l=[],p=[[],[]],d=0;d<2;++d)if(0===d?(s=6*t-12*e+6*i,c=-3*t+9*e-9*i+3*o,f=3*e-3*t):(s=6*n-12*r+6*a,c=-3*n+9*r-9*a+3*u,f=3*r-3*n),Math.abs(c)<1e-12){if(Math.abs(s)<1e-12)continue;(h=-f/s)>0&&h<1&&l.push(h)}else{var v=s*s-4*f*c,g=Math.sqrt(v);if(!(v<0)){var y=(-s+g)/(2*c);y>0&&y<1&&l.push(y);var m=(-s-g)/(2*c);m>0&&m<1&&l.push(m)}}for(var x,b=l.length,M=b;b--;)x=1-(h=l[b]),p[0][b]=x*x*x*t+3*x*x*h*e+3*x*h*h*i+h*h*h*o,p[1][b]=x*x*x*n+3*x*x*h*r+3*x*h*h*a+h*h*h*u;return p[0][M]=t,p[1][M]=n,p[0][M+1]=o,p[1][M+1]=u,p[0].length=p[1].length=M+2,{min:{x:Math.min.apply(0,p[0]),y:Math.min.apply(0,p[1])},max:{x:Math.max.apply(0,p[0]),y:Math.max.apply(0,p[1])}}},b=function(t,n,e,r,i,a,o,u){if(!(Math.max(t,e)<Math.min(i,o)||Math.min(t,e)>Math.max(i,o)||Math.max(n,r)<Math.min(a,u)||Math.min(n,r)>Math.max(a,u))){var c=(t-e)*(a-u)-(n-r)*(i-o);if(c){var s=((t*r-n*e)*(i-o)-(t-e)*(i*u-a*o))/c,f=((t*r-n*e)*(a-u)-(n-r)*(i*u-a*o))/c,h=+s.toFixed(2),l=+f.toFixed(2);if(!(h<+Math.min(t,e).toFixed(2)||h>+Math.max(t,e).toFixed(2)||h<+Math.min(i,o).toFixed(2)||h>+Math.max(i,o).toFixed(2)||l<+Math.min(n,r).toFixed(2)||l>+Math.max(n,r).toFixed(2)||l<+Math.min(a,u).toFixed(2)||l>+Math.max(a,u).toFixed(2)))return{x:s,y:f}}}},M=function(t,n,e){return n>=t.x&&n<=t.x+t.width&&e>=t.y&&e<=t.y+t.height},w=function(t,n,e,r,i){if(i)return[["M",+t+ +i,n],["l",e-2*i,0],["a",i,i,0,0,1,i,i],["l",0,r-2*i],["a",i,i,0,0,1,-i,i],["l",2*i-e,0],["a",i,i,0,0,1,-i,-i],["l",0,2*i-r],["a",i,i,0,0,1,i,-i],["z"]];var a=[["M",t,n],["l",e,0],["l",0,r],["l",-e,0],["z"]];return a.parsePathArray=g,a},P=function(t,n,e,r){return null===t&&(t=n=e=r=0),null===n&&(n=t.y,e=t.width,r=t.height,t=t.x),{x:t,y:n,width:e,w:e,height:r,h:r,x2:t+e,y2:n+r,cx:t+e/2,cy:n+r/2,r1:Math.min(e,r)/2,r2:Math.max(e,r)/2,r0:Math.sqrt(e*e+r*r)/2,path:w(t,n,e,r),vb:[t,n,e,r].join(" ")}},_=function(t,n,e,i,a,o,u,c){Object(r.b)(t)||(t=[t,n,e,i,a,o,u,c]);var s=x.apply(null,t);return P(s.min.x,s.min.y,s.max.x-s.min.x,s.max.y-s.min.y)},A=function(t,n,e,r,i,a,o,u,c){var s=1-c,f=Math.pow(s,3),h=Math.pow(s,2),l=c*c,p=l*c,d=t+2*c*(e-t)+l*(i-2*e+t),v=n+2*c*(r-n)+l*(a-2*r+n),g=e+2*c*(i-e)+l*(o-2*i+e),y=r+2*c*(a-r)+l*(u-2*a+r);return{x:f*t+3*h*c*e+3*s*c*c*i+p*o,y:f*n+3*h*c*r+3*s*c*c*a+p*u,m:{x:d,y:v},n:{x:g,y:y},start:{x:s*t+c*e,y:s*n+c*r},end:{x:s*i+c*o,y:s*a+c*u},alpha:90-180*Math.atan2(d-g,v-y)/Math.PI}},O=function(t,n,e){if(!function(t,n){return t=P(t),n=P(n),M(n,t.x,t.y)||M(n,t.x2,t.y)||M(n,t.x,t.y2)||M(n,t.x2,t.y2)||M(t,n.x,n.y)||M(t,n.x2,n.y)||M(t,n.x,n.y2)||M(t,n.x2,n.y2)||(t.x<n.x2&&t.x>n.x||n.x<t.x2&&n.x>t.x)&&(t.y<n.y2&&t.y>n.y||n.y<t.y2&&n.y>t.y)}(_(t),_(n)))return e?0:[];for(var r=~~(m.apply(0,t)/8),i=~~(m.apply(0,n)/8),a=[],o=[],u={},c=e?0:[],s=0;s<r+1;s++){var f=A.apply(0,t.concat(s/r));a.push({x:f.x,y:f.y,t:s/r})}for(s=0;s<i+1;s++){f=A.apply(0,n.concat(s/i));o.push({x:f.x,y:f.y,t:s/i})}for(s=0;s<r;s++)for(var h=0;h<i;h++){var l=a[s],p=a[s+1],d=o[h],v=o[h+1],g=Math.abs(p.x-l.x)<.001?"y":"x",y=Math.abs(v.x-d.x)<.001?"y":"x",x=b(l.x,l.y,p.x,p.y,d.x,d.y,v.x,v.y);if(x){if(u[x.x.toFixed(4)]===x.y.toFixed(4))continue;u[x.x.toFixed(4)]=x.y.toFixed(4);var w=l.t+Math.abs((x[g]-l[g])/(p[g]-l[g]))*(p.t-l.t),O=d.t+Math.abs((x[y]-d[y])/(v[y]-d[y]))*(v.t-d.t);w>=0&&w<=1&&O>=0&&O<=1&&(e?c+=1:c.push({x:x.x,y:x.y,t1:w,t2:O}))}}return c},C=function(t,n){return function(t,n,e){var r,i,a,o,u,c,s,f,h,l;t=d(t),n=d(n);for(var p=e?0:[],v=0,g=t.length;v<g;v++){var y=t[v];if("M"===y[0])r=u=y[1],i=c=y[2];else{"C"===y[0]?(h=[r,i].concat(y.slice(1)),r=h[6],i=h[7]):(h=[r,i,r,i,u,c,u,c],r=u,i=c);for(var m=0,x=n.length;m<x;m++){var b=n[m];if("M"===b[0])a=s=b[1],o=f=b[2];else{"C"===b[0]?(l=[a,o].concat(b.slice(1)),a=l[6],o=l[7]):(l=[a,o,a,o,s,f,s,f],a=s,o=f);var M=O(h,l,e);if(e)p+=M;else{for(var w=0,P=M.length;w<P;w++)M[w].segment1=v,M[w].segment2=m,M[w].bez1=h,M[w].bez2=l;p=p.concat(M)}}}}}return p}(t,n)};function S(t,n){var e=[],r=[];return t.length&&function t(n,i){if(1===n.length)e.push(n[0]),r.push(n[0]);else{for(var a=[],o=0;o<n.length-1;o++)0===o&&e.push(n[0]),o===n.length-2&&r.push(n[o+1]),a[o]=[(1-i)*n[o][0]+i*n[o+1][0],(1-i)*n[o][1]+i*n[o+1][1]];t(a,i)}}(t,n),{left:e,right:r.reverse()}}var j=function(t,n,e){if(1===e)return[[].concat(t)];var r=[];if("L"===n[0]||"C"===n[0]||"Q"===n[0])r=r.concat(function(t,n,e){var r=[[t[1],t[2]]];e=e||2;var i=[];"A"===n[0]?(r.push(n[6]),r.push(n[7])):"C"===n[0]?(r.push([n[1],n[2]]),r.push([n[3],n[4]]),r.push([n[5],n[6]])):"S"===n[0]||"Q"===n[0]?(r.push([n[1],n[2]]),r.push([n[3],n[4]])):r.push([n[1],n[2]]);for(var a=r,o=1/e,u=0;u<e-1;u++){var c=S(a,o/(1-o*u));i.push(c.left),a=c.right}return i.push(a),i.map((function(t){var n=[];return 4===t.length&&(n.push("C"),n=n.concat(t[2])),t.length>=3&&(3===t.length&&n.push("Q"),n=n.concat(t[1])),2===t.length&&n.push("L"),n=n.concat(t[t.length-1])}))}(t,n,e));else{var i=[].concat(t);"M"===i[0]&&(i[0]="L");for(var a=0;a<=e-1;a++)r.push(i)}return r},k=function(t,n){if(1===t.length)return t;var e=t.length-1,r=n.length-1,i=e/r,a=[];if(1===t.length&&"M"===t[0][0]){for(var o=0;o<r-e;o++)t.push(t[0]);return t}for(o=0;o<r;o++){var u=Math.floor(i*o);a[u]=(a[u]||0)+1}var c=a.reduce((function(n,r,i){return i===e?n.concat(t[e]):n.concat(j(t[i],t[i+1],r))}),[]);return c.unshift(t[0]),"Z"!==n[r]&&"z"!==n[r]||c.push("Z"),c},T=function(t,n){if(t.length!==n.length)return!1;var e=!0;return Object(r.a)(t,(function(t,r){if(t!==n[r])return e=!1,!1})),e};function E(t,n,e){var r=null,i=e;return n<i&&(i=n,r="add"),t<i&&(i=t,r="del"),{type:r,min:i}}var B=function(t,n){var e=function(t,n){var e,r,i=t.length,a=n.length,o=0;if(0===i||0===a)return null;for(var u=[],c=0;c<=i;c++)u[c]=[],u[c][0]={min:c};for(var s=0;s<=a;s++)u[0][s]={min:s};for(c=1;c<=i;c++){e=t[c-1];for(s=1;s<=a;s++){r=n[s-1],o=T(e,r)?0:1;var f=u[c-1][s].min+1,h=u[c][s-1].min+1,l=u[c-1][s-1].min+o;u[c][s]=E(f,h,l)}}return u}(t,n),r=t.length,i=n.length,a=[],o=1,u=1;if(e[r][i].min!==r){for(var c=1;c<=r;c++){var s=e[c][c].min;u=c;for(var f=o;f<=i;f++)e[c][f].min<s&&(s=e[c][f].min,u=f);o=u,e[c][o].type&&a.push({index:c-1,type:e[c][o].type})}for(c=a.length-1;c>=0;c--)o=a[c].index,"add"===a[c].type?t.splice(o,0,[].concat(t[o])):t.splice(o,1)}var h=i-(r=t.length);if(r<i)for(c=0;c<h;c++)"z"===t[r-1][0]||"Z"===t[r-1][0]?t.splice(r-2,0,t[r-2]):t.push(t[r-1]),r+=1;return t};function I(t,n,e){for(var r,i=[].concat(t),a=1/(e+1),o=F(n)[0],u=1;u<=e;u++)a*=u,0===(r=Math.floor(t.length*a))?i.unshift([o[0]*a+t[r][0]*(1-a),o[1]*a+t[r][1]*(1-a)]):i.splice(r,0,[o[0]*a+t[r][0]*(1-a),o[1]*a+t[r][1]*(1-a)]);return i}function F(t){var n=[];switch(t[0]){case"M":case"L":n.push([t[1],t[2]]);break;case"A":n.push([t[6],t[7]]);break;case"Q":n.push([t[3],t[4]]),n.push([t[1],t[2]]);break;case"T":n.push([t[1],t[2]]);break;case"C":n.push([t[5],t[6]]),n.push([t[1],t[2]]),n.push([t[3],t[4]]);break;case"S":n.push([t[3],t[4]]),n.push([t[1],t[2]]);break;case"H":case"V":n.push([t[1],t[1]])}return n}var D=function(t,n){if(t.length<=1)return t;for(var e,r=0;r<n.length;r++)if(t[r][0]!==n[r][0])switch(e=F(t[r]),n[r][0]){case"M":t[r]=["M"].concat(e[0]);break;case"L":t[r]=["L"].concat(e[0]);break;case"A":t[r]=[].concat(n[r]),t[r][6]=e[0][0],t[r][7]=e[0][1];break;case"Q":if(e.length<2){if(!(r>0)){t[r]=n[r];break}e=I(e,t[r-1],1)}t[r]=["Q"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;case"T":t[r]=["T"].concat(e[0]);break;case"C":if(e.length<3){if(!(r>0)){t[r]=n[r];break}e=I(e,t[r-1],2)}t[r]=["C"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;case"S":if(e.length<2){if(!(r>0)){t[r]=n[r];break}e=I(e,t[r-1],1)}t[r]=["S"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;default:t[r]=n[r]}return t}},function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return u}));var r=e(1),i=e(20);function a(t,n,e){var i=1;return Object(r.h)(t)&&(i=t.split("\n").length),i>1?n*i+function(t,n){return n?n-t:.14*t}(n,e)*(i-1):n}function o(t,n){var e=Object(i.a)(),a=0;if(Object(r.e)(t)||""===t)return a;if(e.save(),e.font=n,Object(r.h)(t)&&t.includes("\n")){var o=t.split("\n");Object(r.a)(o,(function(t){var n=e.measureText(t).width;a<n&&(a=n)}))}else a=e.measureText(t).width;return e.restore(),a}function u(t){var n=t.fontSize,e=t.fontFamily,r=t.fontWeight;return[t.fontStyle,t.fontVariant,r,n+"px",e].join(" ").trim()}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Text=n.Rect=n.Polyline=n.Polygon=n.Path=n.Marker=n.Line=n.Image=n.Ellipse=n.Circle=n.Base=void 0;var r=e(11);Object.defineProperty(n,"Base",{enumerable:!0,get:function(){return r.default}});var i=e(57);Object.defineProperty(n,"Circle",{enumerable:!0,get:function(){return i.default}});var a=e(58);Object.defineProperty(n,"Ellipse",{enumerable:!0,get:function(){return a.default}});var o=e(59);Object.defineProperty(n,"Image",{enumerable:!0,get:function(){return o.default}});var u=e(60);Object.defineProperty(n,"Line",{enumerable:!0,get:function(){return u.default}});var c=e(61);Object.defineProperty(n,"Marker",{enumerable:!0,get:function(){return c.default}});var s=e(62);Object.defineProperty(n,"Path",{enumerable:!0,get:function(){return s.default}});var f=e(64);Object.defineProperty(n,"Polygon",{enumerable:!0,get:function(){return f.default}});var h=e(65);Object.defineProperty(n,"Polyline",{enumerable:!0,get:function(){return h.default}});var l=e(66);Object.defineProperty(n,"Rect",{enumerable:!0,get:function(){return l.default}});var p=e(69);Object.defineProperty(n,"Text",{enumerable:!0,get:function(){return p.default}})},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.mergeView=n.getMergedRegion=n.getRefreshRegion=n.refreshElement=n.drawPath=n.clearChanged=n.checkChildrenRefresh=n.checkRefresh=n.drawChildren=n.applyAttrsToContext=void 0;var r=e(5),i=e(32),a=e(33),o=e(8),u=e(17),c={fill:"fillStyle",stroke:"strokeStyle",opacity:"globalAlpha"};function s(t,n){for(var e=0;e<t.length;e++){var r=t[e];if(r.cfg.visible)if(r.cfg.hasChanged)r.cfg.refresh=!0,r.isGroup()&&f(r.cfg.children,n);else if(r.cfg.refresh)r.isGroup()&&s(r.cfg.children,n);else{var i=h(r,n);r.cfg.refresh=i,i&&r.isGroup()&&s(r.cfg.children,n)}}}function f(t,n){for(var e=0;e<t.length;e++){var r=t[e];r.cfg.visible&&(r.cfg.refresh=!0,r.isGroup()&&f(r.get("children"),n))}}function h(t,n){var e=t.cfg.cacheCanvasBBox;return t.cfg.isInView&&e&&(0,o.intersectRect)(e,n)}function l(t){var n;if(t.destroyed)n=t._cacheCanvasBBox;else{var e=t.get("cacheCanvasBBox"),r=e&&!(!e.width||!e.height),i=t.getCanvasBBox(),a=i&&!(!i.width||!i.height);r&&a?n=(0,o.mergeRegion)(e,i):r?n=e:a&&(n=i)}return n}n.applyAttrsToContext=function(t,n){var e=n.attr();for(var a in e){var o=e[a],u=c[a]?c[a]:a;"matrix"===u&&o?t.transform(o[0],o[1],o[3],o[4],o[6],o[7]):"lineDash"===u&&t.setLineDash?(0,r.isArray)(o)&&t.setLineDash(o):("strokeStyle"===u||"fillStyle"===u?o=(0,i.parseStyle)(t,n,o):"globalAlpha"===u&&(o*=t.globalAlpha),t[u]=o)}},n.drawChildren=function(t,n,e){for(var r=0;r<n.length;r++){var i=n[r];i.cfg.visible?i.draw(t,e):i.skipDraw()}},n.checkRefresh=function(t,n,e){var i=t.get("refreshElements");(0,r.each)(i,(function(n){if(n!==t)for(var e=n.cfg.parent;e&&e!==t&&!e.cfg.refresh;)e.cfg.refresh=!0,e=e.cfg.parent})),i[0]===t?f(n,e):s(n,e)},n.checkChildrenRefresh=s,n.clearChanged=function t(n){for(var e=0;e<n.length;e++){var r=n[e];r.cfg.hasChanged=!1,r.isGroup()&&!r.destroyed&&t(r.cfg.children)}},n.drawPath=function(t,n,e,r){var i=e.path,o=e.startArrow,c=e.endArrow;if(i){var s=[0,0],f=[0,0],h={dx:0,dy:0};n.beginPath();for(var l=0;l<i.length;l++){var p=i[l],d=p[0];if(0===l&&o&&o.d){var v=t.getStartTangent();h=u.getShortenOffset(v[0][0],v[0][1],v[1][0],v[1][1],o.d)}else if(l===i.length-2&&"Z"===i[l+1][0]&&c&&c.d){if("Z"===i[l+1][0]){v=t.getEndTangent();h=u.getShortenOffset(v[0][0],v[0][1],v[1][0],v[1][1],c.d)}}else if(l===i.length-1&&c&&c.d&&"Z"!==i[0]){v=t.getEndTangent();h=u.getShortenOffset(v[0][0],v[0][1],v[1][0],v[1][1],c.d)}var g=h.dx,y=h.dy;switch(d){case"M":n.moveTo(p[1]-g,p[2]-y),f=[p[1],p[2]];break;case"L":n.lineTo(p[1]-g,p[2]-y);break;case"Q":n.quadraticCurveTo(p[1],p[2],p[3]-g,p[4]-y);break;case"C":n.bezierCurveTo(p[1],p[2],p[3],p[4],p[5]-g,p[6]-y);break;case"A":var m=void 0;r?(m=r[l])||(m=(0,a.default)(s,p),r[l]=m):m=(0,a.default)(s,p);var x=m.cx,b=m.cy,M=m.rx,w=m.ry,P=m.startAngle,_=m.endAngle,A=m.xRotation,O=m.sweepFlag;if(n.ellipse)n.ellipse(x,b,M,w,A,P,_,1-O);else{var C=M>w?M:w,S=M>w?1:M/w,j=M>w?w/M:1;n.translate(x,b),n.rotate(A),n.scale(S,j),n.arc(0,0,C,P,_,1-O),n.scale(1/S,1/j),n.rotate(-A),n.translate(-x,-b)}break;case"Z":n.closePath()}if("Z"===d)s=f;else{var k=p.length;s=[p[k-2],p[k-1]]}}}},n.refreshElement=function(t,n){var e=t.get("canvas");e&&("remove"===n&&(t._cacheCanvasBBox=t.get("cacheCanvasBBox")),t.get("hasChanged")||(t.set("hasChanged",!0),t.cfg.parent&&t.cfg.parent.get("hasChanged")||(e.refreshElement(t,n,e),e.get("autoDraw")&&e.draw())))},n.getRefreshRegion=l,n.getMergedRegion=function(t){if(!t.length)return null;var n=[],e=[],i=[],a=[];return(0,r.each)(t,(function(t){var r=l(t);r&&(n.push(r.minX),e.push(r.minY),i.push(r.maxX),a.push(r.maxY))})),{minX:(0,r.min)(n),minY:(0,r.min)(e),maxX:(0,r.max)(i),maxY:(0,r.max)(a)}},n.mergeView=function(t,n){return t&&n&&(0,o.intersectRect)(t,n)?{minX:Math.max(t.minX,n.minX),minY:Math.max(t.minY,n.minY),maxX:Math.min(t.maxX,n.maxX),maxY:Math.min(t.maxY,n.maxY)}:null}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.addEndArrow=n.addStartArrow=n.getShortenOffset=void 0;var r=e(6),i=e(15),a=Math.sin,o=Math.cos,u=Math.atan2,c=Math.PI;function s(t,n,e,r,s,f,h){var l=n.stroke,p=n.lineWidth,d=u(r-f,e-s),v=new i.Path({type:"path",canvas:t.get("canvas"),isArrowShape:!0,attrs:{path:"M".concat(10*o(c/6),",").concat(10*a(c/6)," L0,0 L").concat(10*o(c/6),",-").concat(10*a(c/6)),stroke:l,lineWidth:p}});v.translate(s,f),v.rotateAtPoint(s,f,d),t.set(h?"startArrowShape":"endArrowShape",v)}function f(t,n,e,c,s,f,h){var l=n.startArrow,p=n.endArrow,d=n.stroke,v=n.lineWidth,g=h?l:p,y=g.d,m=g.fill,x=g.stroke,b=g.lineWidth,M=r.__rest(g,["d","fill","stroke","lineWidth"]),w=u(c-f,e-s);y&&(s-=o(w)*y,f-=a(w)*y);var P=new i.Path({type:"path",canvas:t.get("canvas"),isArrowShape:!0,attrs:r.__assign(r.__assign({},M),{stroke:x||d,lineWidth:b||v,fill:m})});P.translate(s,f),P.rotateAtPoint(s,f,w),t.set(h?"startArrowShape":"endArrowShape",P)}n.getShortenOffset=function(t,n,e,r,i){var c=u(r-n,e-t);return{dx:o(c)*i,dy:a(c)*i}},n.addStartArrow=function(t,n,e,r,i,a){"object"==typeof n.startArrow?f(t,n,e,r,i,a,!0):n.startArrow?s(t,n,e,r,i,a,!0):t.set("startArrowShape",null)},n.addEndArrow=function(t,n,e,r,i,a){"object"==typeof n.endArrow?f(t,n,e,r,i,a,!1):n.endArrow?s(t,n,e,r,i,a,!1):t.set("startArrowShape",null)}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(7);n.default=function(t,n,e,i,a,o,u){var c=Math.min(t,e),s=Math.max(t,e),f=Math.min(n,i),h=Math.max(n,i),l=a/2;return o>=c-l&&o<=s+l&&u>=f-l&&u<=h+l&&r.Line.pointToLine(t,n,e,i,o,u)<=a/2}},function(t,n,e){"use strict";var r=function(){function t(t,n){this.bubbles=!0,this.target=null,this.currentTarget=null,this.delegateTarget=null,this.delegateObject=null,this.defaultPrevented=!1,this.propagationStopped=!1,this.shape=null,this.fromShape=null,this.toShape=null,this.propagationPath=[],this.type=t,this.name=t,this.originalEvent=n,this.timeStamp=n.timeStamp}return t.prototype.preventDefault=function(){this.defaultPrevented=!0,this.originalEvent.preventDefault&&this.originalEvent.preventDefault()},t.prototype.stopPropagation=function(){this.propagationStopped=!0},t.prototype.toString=function(){return"[Event (type="+this.type+")]"},t.prototype.save=function(){},t.prototype.restore=function(){},t}();n.a=r},function(t,n,e){"use strict";e.d(n,"a",(function(){return i}));var r=null;function i(){if(!r){var t=document.createElement("canvas");t.width=1,t.height=1,r=t.getContext("2d")}return r}},function(t,n,e){"use strict";e.d(n,"a",(function(){return X})),e.d(n,"b",(function(){return N}));var r={};function i(t){return+t}function a(t){return t*t}function o(t){return t*(2-t)}function u(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function c(t){return t*t*t}function s(t){return--t*t*t+1}function f(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}e.r(r),e.d(r,"easeLinear",(function(){return i})),e.d(r,"easeQuad",(function(){return u})),e.d(r,"easeQuadIn",(function(){return a})),e.d(r,"easeQuadOut",(function(){return o})),e.d(r,"easeQuadInOut",(function(){return u})),e.d(r,"easeCubic",(function(){return f})),e.d(r,"easeCubicIn",(function(){return c})),e.d(r,"easeCubicOut",(function(){return s})),e.d(r,"easeCubicInOut",(function(){return f})),e.d(r,"easePoly",(function(){return p})),e.d(r,"easePolyIn",(function(){return h})),e.d(r,"easePolyOut",(function(){return l})),e.d(r,"easePolyInOut",(function(){return p})),e.d(r,"easeSin",(function(){return m})),e.d(r,"easeSinIn",(function(){return g})),e.d(r,"easeSinOut",(function(){return y})),e.d(r,"easeSinInOut",(function(){return m})),e.d(r,"easeExp",(function(){return w})),e.d(r,"easeExpIn",(function(){return b})),e.d(r,"easeExpOut",(function(){return M})),e.d(r,"easeExpInOut",(function(){return w})),e.d(r,"easeCircle",(function(){return A})),e.d(r,"easeCircleIn",(function(){return P})),e.d(r,"easeCircleOut",(function(){return _})),e.d(r,"easeCircleInOut",(function(){return A})),e.d(r,"easeBounce",(function(){return S})),e.d(r,"easeBounceIn",(function(){return C})),e.d(r,"easeBounceOut",(function(){return S})),e.d(r,"easeBounceInOut",(function(){return j})),e.d(r,"easeBack",(function(){return E})),e.d(r,"easeBackIn",(function(){return k})),e.d(r,"easeBackOut",(function(){return T})),e.d(r,"easeBackInOut",(function(){return E})),e.d(r,"easeElastic",(function(){return F})),e.d(r,"easeElasticIn",(function(){return I})),e.d(r,"easeElasticOut",(function(){return F})),e.d(r,"easeElasticInOut",(function(){return D}));var h=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),l=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),p=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),d=Math.PI,v=d/2;function g(t){return 1==+t?1:1-Math.cos(t*v)}function y(t){return Math.sin(t*v)}function m(t){return(1-Math.cos(d*t))/2}function x(t){return 1.0009775171065494*(Math.pow(2,-10*t)-.0009765625)}function b(t){return x(1-+t)}function M(t){return 1-x(t)}function w(t){return((t*=2)<=1?x(1-t):2-x(t-1))/2}function P(t){return 1-Math.sqrt(1-t*t)}function _(t){return Math.sqrt(1- --t*t)}function A(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var O=7.5625;function C(t){return 1-S(1-t)}function S(t){return(t=+t)<4/11?O*t*t:t<8/11?O*(t-=6/11)*t+3/4:t<10/11?O*(t-=9/11)*t+15/16:O*(t-=21/22)*t+63/64}function j(t){return((t*=2)<=1?1-S(1-t):S(t-1)+1)/2}var k=function t(n){function e(t){return(t=+t)*t*(n*(t-1)+t)}return n=+n,e.overshoot=t,e}(1.70158),T=function t(n){function e(t){return--t*t*((t+1)*n+t)+1}return n=+n,e.overshoot=t,e}(1.70158),E=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),B=2*Math.PI,I=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=B);function i(t){return n*x(- --t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*B)},i.period=function(e){return t(n,e)},i}(1,.3),F=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=B);function i(t){return 1-n*x(t=+t)*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*B)},i.period=function(e){return t(n,e)},i}(1,.3),D=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=B);function i(t){return((t=2*t-1)<0?n*x(-t)*Math.sin((r-t)/e):2-n*x(t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*B)},i.period=function(e){return t(n,e)},i}(1,.3),q={};function X(t){return q[t.toLowerCase()]||r[t]}function N(t,n){q[t.toLowerCase()]=n}},function(t,n,e){"use strict";var r=e(9),i=function(){function t(){this._events={}}return t.prototype.on=function(t,n,e){return this._events[t]||(this._events[t]=[]),this._events[t].push({callback:n,once:!!e}),this},t.prototype.once=function(t,n){return this.on(t,n,!0)},t.prototype.emit=function(t){for(var n=this,e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var i=this._events[t]||[],a=this._events["*"]||[],o=function(r){for(var i=r.length,a=0;a<i;a++)if(r[a]){var o=r[a],u=o.callback;o.once&&(r.splice(a,1),0===r.length&&delete n._events[t],i--,a--),u.apply(n,e)}};o(i),o(a)},t.prototype.off=function(t,n){if(t)if(n){for(var e=this._events[t]||[],r=e.length,i=0;i<r;i++)e[i].callback===n&&(e.splice(i,1),r--,i--);0===e.length&&delete this._events[t]}else delete this._events[t];else this._events={};return this},t.prototype.getEvents=function(){return this._events},t}(),a=e(1),o=function(t){function n(n){var e=t.call(this)||this;e.destroyed=!1;var r=e.getDefaultCfg();return e.cfg=Object(a.i)(r,n),e}return Object(r.a)(n,t),n.prototype.getDefaultCfg=function(){return{}},n.prototype.get=function(t){return this.cfg[t]},n.prototype.set=function(t,n){this.cfg[t]=n},n.prototype.destroy=function(){this.cfg={destroyed:!0},this.off(),this.destroyed=!0},n}(i);n.a=o},function(t,n,e){"use strict";var r=e(9),i=e(24),a=e(1),o={};var u=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return Object(r.a)(n,t),n.prototype.isCanvas=function(){return!1},n.prototype.getBBox=function(){var t=1/0,n=-1/0,e=1/0,r=-1/0,i=this.getChildren().filter((function(t){return t.get("visible")&&(!t.isGroup()||t.isGroup()&&t.getChildren().length>0)}));return i.length>0?Object(a.a)(i,(function(i){var a=i.getBBox(),o=a.minX,u=a.maxX,c=a.minY,s=a.maxY;o<t&&(t=o),u>n&&(n=u),c<e&&(e=c),s>r&&(r=s)})):(t=0,n=0,e=0,r=0),{x:t,y:e,minX:t,minY:e,maxX:n,maxY:r,width:n-t,height:r-e}},n.prototype.getCanvasBBox=function(){var t=1/0,n=-1/0,e=1/0,r=-1/0,i=this.getChildren().filter((function(t){return t.get("visible")&&(!t.isGroup()||t.isGroup()&&t.getChildren().length>0)}));return i.length>0?Object(a.a)(i,(function(i){var a=i.getCanvasBBox(),o=a.minX,u=a.maxX,c=a.minY,s=a.maxY;o<t&&(t=o),u>n&&(n=u),c<e&&(e=c),s>r&&(r=s)})):(t=0,n=0,e=0,r=0),{x:t,y:e,minX:t,minY:e,maxX:n,maxY:r,width:n-t,height:r-e}},n.prototype.getDefaultCfg=function(){var n=t.prototype.getDefaultCfg.call(this);return n.children=[],n},n.prototype.onAttrChange=function(n,e,r){if(t.prototype.onAttrChange.call(this,n,e,r),"matrix"===n){var i=this.getTotalMatrix();this._applyChildrenMarix(i)}},n.prototype.applyMatrix=function(n){var e=this.getTotalMatrix();t.prototype.applyMatrix.call(this,n);var r=this.getTotalMatrix();r!==e&&this._applyChildrenMarix(r)},n.prototype._applyChildrenMarix=function(t){var n=this.getChildren();Object(a.a)(n,(function(n){n.applyMatrix(t)}))},n.prototype.addShape=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=t[0],r=t[1];Object(a.f)(e)?r=e:r.type=e;var i=o[r.type];i||(i=Object(a.k)(r.type),o[r.type]=i);var u=this.getShapeBase(),c=new u[i](r);return this.add(c),c},n.prototype.addGroup=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e,r=t[0],i=t[1];if(Object(a.d)(r))e=new r(i||{parent:this});else{var o=r||{},u=this.getGroupBase();e=new u(o)}return this.add(e),e},n.prototype.getCanvas=function(){return this.isCanvas()?this:this.get("canvas")},n.prototype.getShape=function(t,n,e){if(!Object(a.b)(this))return null;var r,i=this.getChildren();if(this.isCanvas())r=this._findShape(i,t,n,e);else{var o=[t,n,1];o=this.invertFromMatrix(o),this.isClipped(o[0],o[1])||(r=this._findShape(i,o[0],o[1],e))}return r},n.prototype._findShape=function(t,n,e,r){for(var i=null,o=t.length-1;o>=0;o--){var u=t[o];if(Object(a.b)(u)&&(u.isGroup()?i=u.getShape(n,e,r):u.isHit(n,e)&&(i=u)),i)break}return i},n.prototype.add=function(t){var n=this.getCanvas(),e=this.getChildren(),r=this.get("timeline"),i=t.getParent();i&&function(t,n,e){void 0===e&&(e=!0),e?n.destroy():(n.set("parent",null),n.set("canvas",null)),Object(a.j)(t.getChildren(),n)}(i,t,!1),t.set("parent",this),n&&function t(n,e){if(n.set("canvas",e),n.isGroup()){var r=n.get("children");r.length&&r.forEach((function(n){t(n,e)}))}}(t,n),r&&function t(n,e){if(n.set("timeline",e),n.isGroup()){var r=n.get("children");r.length&&r.forEach((function(n){t(n,e)}))}}(t,r),e.push(t),t.onCanvasChange("add"),this._applyElementMatrix(t)},n.prototype._applyElementMatrix=function(t){var n=this.getTotalMatrix();n&&t.applyMatrix(n)},n.prototype.getChildren=function(){return this.get("children")||[]},n.prototype.sort=function(){var t,n=this.getChildren();Object(a.a)(n,(function(t,n){return t._INDEX=n,t})),n.sort((t=function(t,n){return t.get("zIndex")-n.get("zIndex")},function(n,e){var r=t(n,e);return 0===r?n._INDEX-e._INDEX:r})),this.onCanvasChange("sort")},n.prototype.clear=function(){if(this.set("clearing",!0),!this.destroyed){for(var t=this.getChildren(),n=t.length-1;n>=0;n--)t[n].destroy();this.set("children",[]),this.onCanvasChange("clear"),this.set("clearing",!1)}},n.prototype.destroy=function(){this.get("destroyed")||(this.clear(),t.prototype.destroy.call(this))},n.prototype.getFirst=function(){return this.getChildByIndex(0)},n.prototype.getLast=function(){var t=this.getChildren();return this.getChildByIndex(t.length-1)},n.prototype.getChildByIndex=function(t){return this.getChildren()[t]},n.prototype.getCount=function(){return this.getChildren().length},n.prototype.contain=function(t){return this.getChildren().indexOf(t)>-1},n.prototype.removeChild=function(t,n){void 0===n&&(n=!0),this.contain(t)&&t.remove(n)},n.prototype.findAll=function(t){var n=[],e=this.getChildren();return Object(a.a)(e,(function(e){t(e)&&n.push(e),e.isGroup()&&(n=n.concat(e.findAll(t)))})),n},n.prototype.find=function(t){var n=null,e=this.getChildren();return Object(a.a)(e,(function(e){if(t(e)?n=e:e.isGroup()&&(n=e.find(t)),n)return!1})),n},n.prototype.findById=function(t){return this.find((function(n){return n.get("id")===t}))},n.prototype.findByClassName=function(t){return this.find((function(n){return n.get("className")===t}))},n.prototype.findAllByName=function(t){return this.findAll((function(n){return n.get("name")===t}))},n}(i.a);n.a=u},function(t,n,e){"use strict";var r={};e.r(r),e.d(r,"leftTranslate",(function(){return u})),e.d(r,"leftRotate",(function(){return c})),e.d(r,"leftScale",(function(){return s})),e.d(r,"transform",(function(){return f})),e.d(r,"direction",(function(){return h})),e.d(r,"angleTo",(function(){return l})),e.d(r,"vertical",(function(){return p}));var i=e(9),a=e(0),o=e(3);function u(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return o.a.fromTranslation(r,e),o.a.multiply(t,r,n)}function c(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return o.a.fromRotation(r,e),o.a.multiply(t,r,n)}function s(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return o.a.fromScaling(r,e),o.a.multiply(t,r,n)}function f(t,n){for(var e,r,i,a=t?[].concat(t):[1,0,0,0,1,0,0,0,1],f=0,h=n.length;f<h;f++){var l=n[f];switch(l[0]){case"t":u(a,a,[l[1],l[2]]);break;case"s":s(a,a,[l[1],l[2]]);break;case"r":c(a,a,l[1]);break;case"m":e=a,r=a,i=l[1],o.a.multiply(e,i,r)}}return a}function h(t,n){return t[0]*n[1]-n[0]*t[1]}function l(t,n,e){var r=o.b.angle(t,n),i=h(t,n)>=0;return e?i?2*Math.PI-r:r:i?r:2*Math.PI-r}function p(t,n,e){return e?(t[0]=n[1],t[1]=-1*n[0]):(t[0]=-1*n[1],t[1]=n[0]),t}var d=e(1),v=e(10),g=e(22),y=r.transform,m=["zIndex","capture","visible","type"],x=["repeat"];function b(t,n){var e={},r=n.attrs;for(var i in t)e[i]=r[i];return e}function M(t,n){var e={},r=n.attr();return Object(a.a)(t,(function(t,n){-1!==x.indexOf(n)||Object(a.c)(r[n],t)||(e[n]=t)})),e}function w(t,n){if(n.onFrame)return t;var e=n.startTime,r=n.delay,i=n.duration,o=Object.prototype.hasOwnProperty;return Object(a.a)(t,(function(t){e+r<t.startTime+t.delay+t.duration&&i>t.delay&&Object(a.a)(n.toAttrs,(function(n,e){o.call(t.toAttrs,e)&&(delete t.toAttrs[e],delete t.fromAttrs[e])}))})),t}var P=function(t){function n(n){var e=t.call(this,n)||this;e.attrs={};var r=e.getDefaultAttrs();return Object(a.l)(r,n.attrs),e.attrs=r,e.initAttrs(r),e.initAnimate(),e}return Object(i.a)(n,t),n.prototype.getDefaultCfg=function(){return{visible:!0,capture:!0,zIndex:0}},n.prototype.getDefaultAttrs=function(){return{matrix:this.getDefaultMatrix(),opacity:1}},n.prototype.onCanvasChange=function(t){},n.prototype.initAttrs=function(t){},n.prototype.initAnimate=function(){this.set("animable",!0),this.set("animating",!1)},n.prototype.isGroup=function(){return!1},n.prototype.getParent=function(){return this.get("parent")},n.prototype.getCanvas=function(){return this.get("canvas")},n.prototype.attr=function(){for(var t,n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var r=n[0],i=n[1];if(!r)return this.attrs;if(Object(a.h)(r)){for(var o in r)this.setAttr(o,r[o]);return this.afterAttrsChange(r),this}return 2===n.length?(this.setAttr(r,i),this.afterAttrsChange(((t={})[r]=i,t)),this):this.attrs[r]},n.prototype.isClipped=function(t,n){var e=this.getClip();return e&&!e.isHit(t,n)},n.prototype.setAttr=function(t,n){var e=this.attrs[t];e!==n&&(this.attrs[t]=n,this.onAttrChange(t,n,e))},n.prototype.onAttrChange=function(t,n,e){"matrix"===t&&this.set("totalMatrix",null)},n.prototype.afterAttrsChange=function(t){if(this.cfg.isClipShape){var n=this.cfg.applyTo;n&&n.onCanvasChange("clip")}else this.onCanvasChange("attr")},n.prototype.show=function(){return this.set("visible",!0),this.onCanvasChange("show"),this},n.prototype.hide=function(){return this.set("visible",!1),this.onCanvasChange("hide"),this},n.prototype.setZIndex=function(t){this.set("zIndex",t);var n=this.getParent();return n&&n.sort(),this},n.prototype.toFront=function(){var t=this.getParent();if(t){var n=t.getChildren(),e=(this.get("el"),n.indexOf(this));n.splice(e,1),n.push(this),this.onCanvasChange("zIndex")}},n.prototype.toBack=function(){var t=this.getParent();if(t){var n=t.getChildren(),e=(this.get("el"),n.indexOf(this));n.splice(e,1),n.unshift(this),this.onCanvasChange("zIndex")}},n.prototype.remove=function(t){void 0===t&&(t=!0);var n=this.getParent();n?(Object(d.j)(n.getChildren(),this),n.get("clearing")||this.onCanvasChange("remove")):this.onCanvasChange("remove"),t&&this.destroy()},n.prototype.resetMatrix=function(){this.attr("matrix",this.getDefaultMatrix()),this.onCanvasChange("matrix")},n.prototype.getMatrix=function(){return this.attr("matrix")},n.prototype.setMatrix=function(t){this.attr("matrix",t),this.onCanvasChange("matrix")},n.prototype.getTotalMatrix=function(){var t=this.cfg.totalMatrix;if(!t){var n=this.attr("matrix"),e=this.cfg.parentMatrix;t=e&&n?Object(v.b)(e,n):n||e,this.set("totalMatrix",t)}return t},n.prototype.applyMatrix=function(t){var n=this.attr("matrix"),e=null;e=t&&n?Object(v.b)(t,n):n||t,this.set("totalMatrix",e),this.set("parentMatrix",t)},n.prototype.getDefaultMatrix=function(){return null},n.prototype.applyToMatrix=function(t){var n=this.attr("matrix");return n?Object(v.c)(n,t):t},n.prototype.invertFromMatrix=function(t){var n=this.attr("matrix");if(n){var e=Object(v.a)(n);if(e)return Object(v.c)(e,t)}return t},n.prototype.setClip=function(t){var n=this.getCanvas(),e=null;if(t){var r=this.getShapeBase()[Object(a.q)(t.type)];r&&(e=new r({type:t.type,isClipShape:!0,applyTo:this,attrs:t.attrs,canvas:n}))}return this.set("clipShape",e),this.onCanvasChange("clip"),e},n.prototype.getClip=function(){var t=this.cfg.clipShape;return t||null},n.prototype.clone=function(){var t=this,n=this.attrs,e={};Object(a.a)(n,(function(t,r){Object(a.b)(n[r])?e[r]=function(t){for(var n=[],e=0;e<t.length;e++)Object(a.b)(t[e])?n.push([].concat(t[e])):n.push(t[e]);return n}(n[r]):e[r]=n[r]}));var r=new(0,this.constructor)({attrs:e});return Object(a.a)(m,(function(n){r.set(n,t.get(n))})),r},n.prototype.destroy=function(){this.destroyed||(this.attrs={},t.prototype.destroy.call(this))},n.prototype.isAnimatePaused=function(){return this.get("_pause").isPaused},n.prototype.animate=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(this.get("timeline")||this.get("canvas")){this.set("animating",!0);var e=this.get("timeline");e||(e=this.get("canvas").get("timeline"),this.set("timeline",e));var r=this.get("animations")||[];e.timer||e.initTimer();var i,o,u,c,s,f=t[0],h=t[1],l=t[2],p=void 0===l?"easeLinear":l,d=t[3],v=void 0===d?a.n:d,g=t[4],y=void 0===g?0:g;Object(a.d)(f)?(i=f,f={}):Object(a.h)(f)&&f.onFrame&&(i=f.onFrame,o=f.repeat),Object(a.h)(h)?(h=(s=h).duration,p=s.easing||"easeLinear",y=s.delay||0,o=s.repeat||o||!1,v=s.callback||a.n,u=s.pauseCallback||a.n,c=s.resumeCallback||a.n):(Object(a.f)(v)&&(y=v,v=null),Object(a.d)(p)?(v=p,p="easeLinear"):p=p||"easeLinear");var m=M(f,this),x={fromAttrs:b(m,this),toAttrs:m,duration:h,easing:p,repeat:o,callback:v,pauseCallback:u,resumeCallback:c,delay:y,startTime:e.getTime(),id:Object(a.p)(),onFrame:i,pathFormatted:!1};r.length>0?r=w(r,x):e.addAnimator(this),r.push(x),this.set("animations",r),this.set("_pause",{isPaused:!1})}},n.prototype.stopAnimate=function(t){var n=this;void 0===t&&(t=!0);var e=this.get("animations");Object(a.a)(e,(function(e){t&&(e.onFrame?n.attr(e.onFrame(1)):n.attr(e.toAttrs)),e.callback&&e.callback()})),this.set("animating",!1),this.set("animations",[])},n.prototype.pauseAnimate=function(){var t=this.get("timeline"),n=this.get("animations"),e=t.getTime();return Object(a.a)(n,(function(t){t._paused=!0,t._pauseTime=e,t.pauseCallback&&t.pauseCallback()})),this.set("_pause",{isPaused:!0,pauseTime:e}),this},n.prototype.resumeAnimate=function(){var t=this.get("timeline").getTime(),n=this.get("animations"),e=this.get("_pause").pauseTime;return Object(a.a)(n,(function(n){n.startTime=n.startTime+(t-e),n._paused=!1,n._pauseTime=null,n.resumeCallback&&n.resumeCallback()})),this.set("_pause",{isPaused:!1}),this.set("animations",n),this},n.prototype.emitDelegation=function(t,n){var e,r=this,i=n.propagationPath;this.getEvents();"mouseenter"===t?e=n.fromShape:"mouseleave"===t&&(e=n.toShape);for(var o=function(t){var o=i[t],c=o.get("name");if(c){if((o.isGroup()||o.isCanvas&&o.isCanvas())&&e&&Object(d.g)(o,e))return"break";Object(a.b)(c)?Object(a.a)(c,(function(t){r.emitDelegateEvent(o,t,n)})):u.emitDelegateEvent(o,c,n)}},u=this,c=0;c<i.length;c++){if("break"===o(c))break}},n.prototype.emitDelegateEvent=function(t,n,e){var r=this.getEvents(),i=n+":"+e.type;(r[i]||r["*"])&&(e.name=i,e.currentTarget=t,e.delegateTarget=this,e.delegateObject=t.get("delegateObject"),this.emit(i,e))},n.prototype.translate=function(t,n){void 0===t&&(t=0),void 0===n&&(n=0);var e=this.getMatrix(),r=y(e,[["t",t,n]]);return this.setMatrix(r),this},n.prototype.move=function(t,n){var e=this.attr("x")||0,r=this.attr("y")||0;return this.translate(t-e,n-r),this},n.prototype.moveTo=function(t,n){return this.move(t,n)},n.prototype.scale=function(t,n){var e=this.getMatrix(),r=y(e,[["s",t,n||t]]);return this.setMatrix(r),this},n.prototype.rotate=function(t){var n=this.getMatrix(),e=y(n,[["r",t]]);return this.setMatrix(e),this},n.prototype.rotateAtStart=function(t){var n=this.attr(),e=n.x,r=n.y,i=this.getMatrix(),a=y(i,[["t",-e,-r],["r",t],["t",e,r]]);return this.setMatrix(a),this},n.prototype.rotateAtPoint=function(t,n,e){var r=this.getMatrix(),i=y(r,[["t",-t,-n],["r",e],["t",t,n]]);return this.setMatrix(i),this},n}(g.a);n.a=P},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(12),a=e(15),o=e(16),u=e(5),c=e(8),s=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.onCanvasChange=function(t){(0,o.refreshElement)(this,t)},n.prototype.getShapeBase=function(){return a},n.prototype.getGroupBase=function(){return n},n.prototype._applyClip=function(t,n){n&&(t.save(),(0,o.applyAttrsToContext)(t,n),n.createPath(t),t.restore(),t.clip(),n._afterDraw())},n.prototype.cacheCanvasBBox=function(){var t=this.cfg.children,n=[],e=[];(0,u.each)(t,(function(t){var r=t.cfg.cacheCanvasBBox;r&&t.cfg.isInView&&(n.push(r.minX,r.maxX),e.push(r.minY,r.maxY))}));var r=null;if(n.length){var i=(0,u.min)(n),a=(0,u.max)(n),o=(0,u.min)(e),s=(0,u.max)(e);r={minX:i,minY:o,x:i,y:o,maxX:a,maxY:s,width:a-i,height:s-o};var f=this.cfg.canvas;if(f){var h=f.getViewRange();this.set("isInView",(0,c.intersectRect)(r,h))}}else this.set("isInView",!1);this.set("cacheCanvasBBox",r)},n.prototype.draw=function(t,n){var e=this.cfg.children,r=!n||this.cfg.refresh;e.length&&r&&(t.save(),(0,o.applyAttrsToContext)(t,this),this._applyClip(t,this.getClip()),(0,o.drawChildren)(t,e,n),t.restore(),this.cacheCanvasBBox()),this.cfg.refresh=null,this.set("hasChanged",!1)},n.prototype.skipDraw=function(){this.set("cacheCanvasBBox",null),this.set("hasChanged",!1)},n}(i.AbstractGroup);n.default=s},function(t,n,e){"use strict";e.r(n),e.d(n,"create",(function(){return i})),e.d(n,"clone",(function(){return a})),e.d(n,"length",(function(){return o})),e.d(n,"fromValues",(function(){return u})),e.d(n,"copy",(function(){return c})),e.d(n,"set",(function(){return s})),e.d(n,"add",(function(){return f})),e.d(n,"subtract",(function(){return h})),e.d(n,"multiply",(function(){return l})),e.d(n,"divide",(function(){return p})),e.d(n,"ceil",(function(){return d})),e.d(n,"floor",(function(){return v})),e.d(n,"min",(function(){return g})),e.d(n,"max",(function(){return y})),e.d(n,"round",(function(){return m})),e.d(n,"scale",(function(){return x})),e.d(n,"scaleAndAdd",(function(){return b})),e.d(n,"distance",(function(){return M})),e.d(n,"squaredDistance",(function(){return w})),e.d(n,"squaredLength",(function(){return P})),e.d(n,"negate",(function(){return _})),e.d(n,"inverse",(function(){return A})),e.d(n,"normalize",(function(){return O})),e.d(n,"dot",(function(){return C})),e.d(n,"cross",(function(){return S})),e.d(n,"lerp",(function(){return j})),e.d(n,"hermite",(function(){return k})),e.d(n,"bezier",(function(){return T})),e.d(n,"random",(function(){return E})),e.d(n,"transformMat4",(function(){return B})),e.d(n,"transformMat3",(function(){return I})),e.d(n,"transformQuat",(function(){return F})),e.d(n,"rotateX",(function(){return D})),e.d(n,"rotateY",(function(){return q})),e.d(n,"rotateZ",(function(){return X})),e.d(n,"angle",(function(){return N})),e.d(n,"zero",(function(){return R})),e.d(n,"str",(function(){return L})),e.d(n,"exactEquals",(function(){return Y})),e.d(n,"equals",(function(){return z})),e.d(n,"sub",(function(){return $})),e.d(n,"mul",(function(){return V})),e.d(n,"div",(function(){return H})),e.d(n,"dist",(function(){return Q})),e.d(n,"sqrDist",(function(){return G})),e.d(n,"len",(function(){return Z})),e.d(n,"sqrLen",(function(){return U})),e.d(n,"forEach",(function(){return K}));var r=e(2);function i(){var t=new r.a(3);return r.a!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function a(t){var n=new r.a(3);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n}function o(t){var n=t[0],e=t[1],r=t[2];return Math.hypot(n,e,r)}function u(t,n,e){var i=new r.a(3);return i[0]=t,i[1]=n,i[2]=e,i}function c(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t}function s(t,n,e,r){return t[0]=n,t[1]=e,t[2]=r,t}function f(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t}function h(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t}function l(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t[2]=n[2]*e[2],t}function p(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t[2]=n[2]/e[2],t}function d(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t[2]=Math.ceil(n[2]),t}function v(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t[2]=Math.floor(n[2]),t}function g(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t[2]=Math.min(n[2],e[2]),t}function y(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t[2]=Math.max(n[2],e[2]),t}function m(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t[2]=Math.round(n[2]),t}function x(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t}function b(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t}function M(t,n){var e=n[0]-t[0],r=n[1]-t[1],i=n[2]-t[2];return Math.hypot(e,r,i)}function w(t,n){var e=n[0]-t[0],r=n[1]-t[1],i=n[2]-t[2];return e*e+r*r+i*i}function P(t){var n=t[0],e=t[1],r=t[2];return n*n+e*e+r*r}function _(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t}function A(t,n){return t[0]=1/n[0],t[1]=1/n[1],t[2]=1/n[2],t}function O(t,n){var e=n[0],r=n[1],i=n[2],a=e*e+r*r+i*i;return a>0&&(a=1/Math.sqrt(a)),t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t}function C(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function S(t,n,e){var r=n[0],i=n[1],a=n[2],o=e[0],u=e[1],c=e[2];return t[0]=i*c-a*u,t[1]=a*o-r*c,t[2]=r*u-i*o,t}function j(t,n,e,r){var i=n[0],a=n[1],o=n[2];return t[0]=i+r*(e[0]-i),t[1]=a+r*(e[1]-a),t[2]=o+r*(e[2]-o),t}function k(t,n,e,r,i,a){var o=a*a,u=o*(2*a-3)+1,c=o*(a-2)+a,s=o*(a-1),f=o*(3-2*a);return t[0]=n[0]*u+e[0]*c+r[0]*s+i[0]*f,t[1]=n[1]*u+e[1]*c+r[1]*s+i[1]*f,t[2]=n[2]*u+e[2]*c+r[2]*s+i[2]*f,t}function T(t,n,e,r,i,a){var o=1-a,u=o*o,c=a*a,s=u*o,f=3*a*u,h=3*c*o,l=c*a;return t[0]=n[0]*s+e[0]*f+r[0]*h+i[0]*l,t[1]=n[1]*s+e[1]*f+r[1]*h+i[1]*l,t[2]=n[2]*s+e[2]*f+r[2]*h+i[2]*l,t}function E(t,n){n=n||1;var e=2*r.c()*Math.PI,i=2*r.c()-1,a=Math.sqrt(1-i*i)*n;return t[0]=Math.cos(e)*a,t[1]=Math.sin(e)*a,t[2]=i*n,t}function B(t,n,e){var r=n[0],i=n[1],a=n[2],o=e[3]*r+e[7]*i+e[11]*a+e[15];return o=o||1,t[0]=(e[0]*r+e[4]*i+e[8]*a+e[12])/o,t[1]=(e[1]*r+e[5]*i+e[9]*a+e[13])/o,t[2]=(e[2]*r+e[6]*i+e[10]*a+e[14])/o,t}function I(t,n,e){var r=n[0],i=n[1],a=n[2];return t[0]=r*e[0]+i*e[3]+a*e[6],t[1]=r*e[1]+i*e[4]+a*e[7],t[2]=r*e[2]+i*e[5]+a*e[8],t}function F(t,n,e){var r=e[0],i=e[1],a=e[2],o=e[3],u=n[0],c=n[1],s=n[2],f=i*s-a*c,h=a*u-r*s,l=r*c-i*u,p=i*l-a*h,d=a*f-r*l,v=r*h-i*f,g=2*o;return f*=g,h*=g,l*=g,p*=2,d*=2,v*=2,t[0]=u+f+p,t[1]=c+h+d,t[2]=s+l+v,t}function D(t,n,e,r){var i=[],a=[];return i[0]=n[0]-e[0],i[1]=n[1]-e[1],i[2]=n[2]-e[2],a[0]=i[0],a[1]=i[1]*Math.cos(r)-i[2]*Math.sin(r),a[2]=i[1]*Math.sin(r)+i[2]*Math.cos(r),t[0]=a[0]+e[0],t[1]=a[1]+e[1],t[2]=a[2]+e[2],t}function q(t,n,e,r){var i=[],a=[];return i[0]=n[0]-e[0],i[1]=n[1]-e[1],i[2]=n[2]-e[2],a[0]=i[2]*Math.sin(r)+i[0]*Math.cos(r),a[1]=i[1],a[2]=i[2]*Math.cos(r)-i[0]*Math.sin(r),t[0]=a[0]+e[0],t[1]=a[1]+e[1],t[2]=a[2]+e[2],t}function X(t,n,e,r){var i=[],a=[];return i[0]=n[0]-e[0],i[1]=n[1]-e[1],i[2]=n[2]-e[2],a[0]=i[0]*Math.cos(r)-i[1]*Math.sin(r),a[1]=i[0]*Math.sin(r)+i[1]*Math.cos(r),a[2]=i[2],t[0]=a[0]+e[0],t[1]=a[1]+e[1],t[2]=a[2]+e[2],t}function N(t,n){var e=t[0],r=t[1],i=t[2],a=n[0],o=n[1],u=n[2],c=Math.sqrt(e*e+r*r+i*i)*Math.sqrt(a*a+o*o+u*u),s=c&&C(t,n)/c;return Math.acos(Math.min(Math.max(s,-1),1))}function R(t){return t[0]=0,t[1]=0,t[2]=0,t}function L(t){return"vec3("+t[0]+", "+t[1]+", "+t[2]+")"}function Y(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]}function z(t,n){var e=t[0],i=t[1],a=t[2],o=n[0],u=n[1],c=n[2];return Math.abs(e-o)<=r.b*Math.max(1,Math.abs(e),Math.abs(o))&&Math.abs(i-u)<=r.b*Math.max(1,Math.abs(i),Math.abs(u))&&Math.abs(a-c)<=r.b*Math.max(1,Math.abs(a),Math.abs(c))}var W,$=h,V=l,H=p,Q=M,G=w,Z=o,U=P,K=(W=i(),function(t,n,e,r,i,a){var o,u;for(n||(n=3),e||(e=0),u=r?Math.min(r*n+e,t.length):t.length,o=e;o<u;o+=n)W[0]=t[o],W[1]=t[o+1],W[2]=t[o+2],i(W,W,a),t[o]=W[0],t[o+1]=W[1],t[o+2]=W[2];return t})},function(t,n,e){"use strict";e.d(n,"a",(function(){return a})),e.d(n,"b",(function(){return i}));var r=new Map;function i(t,n){r.set(t,n)}function a(t){return r.get(t)}var o=function(t){var n=t.attr();return{x:n.x,y:n.y,width:n.width,height:n.height}},u=function(t){var n=t.attr(),e=n.x,r=n.y,i=n.r;return{x:e-i,y:r-i,width:2*i,height:2*i}},c=e(7);function s(t,n){return t&&n?{minX:Math.min(t.minX,n.minX),minY:Math.min(t.minY,n.minY),maxX:Math.max(t.maxX,n.maxX),maxY:Math.max(t.maxY,n.maxY)}:t||n}function f(t,n){var e=t.get("startArrowShape"),r=t.get("endArrowShape");return e&&(n=s(n,e.getCanvasBBox())),r&&(n=s(n,r.getCanvasBBox())),n}var h=e(14),l=e(0),p=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/gi,d=/[^\s\,]+/gi;var v=function(t){var n=t||[];return Object(l.b)(n)?n:Object(l.i)(n)?(n=n.match(p),Object(l.a)(n,(function(t,e){if((t=t.match(d))[0].length>1){var r=t[0].charAt(0);t.splice(1,0,t[0].substr(1)),t[0]=r}Object(l.a)(t,(function(n,e){isNaN(n)||(t[e]=+n)})),n[e]=t})),n):void 0};e(3);var g="\t\n\v\f\r   ᠎             　\u2028\u2029";new RegExp("([a-z])["+g+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+g+"]*,?["+g+"]*)+)","ig"),new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+g+"]*,?["+g+"]*","ig");Math.PI;function y(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function m(t,n){return y(t)*y(n)?(t[0]*n[0]+t[1]*n[1])/(y(t)*y(n)):1}function x(t,n){return(t[0]*n[1]<t[1]*n[0]?-1:1)*Math.acos(m(t,n))}function b(t,n){return t[0]===n[0]&&t[1]===n[1]}function M(t,n){var e=n[1],r=n[2],i=Object(l.m)(Object(l.o)(n[3]),2*Math.PI),a=n[4],o=n[5],u=t[0],c=t[1],s=n[6],f=n[7],h=Math.cos(i)*(u-s)/2+Math.sin(i)*(c-f)/2,p=-1*Math.sin(i)*(u-s)/2+Math.cos(i)*(c-f)/2,d=h*h/(e*e)+p*p/(r*r);d>1&&(e*=Math.sqrt(d),r*=Math.sqrt(d));var v=e*e*(p*p)+r*r*(h*h),g=v?Math.sqrt((e*e*(r*r)-v)/v):1;a===o&&(g*=-1),isNaN(g)&&(g=0);var y=r?g*e*p/r:0,M=e?g*-r*h/e:0,w=(u+s)/2+Math.cos(i)*y-Math.sin(i)*M,P=(c+f)/2+Math.sin(i)*y+Math.cos(i)*M,_=[(h-y)/e,(p-M)/r],A=[(-1*h-y)/e,(-1*p-M)/r],O=x([1,0],_),C=x(_,A);return m(_,A)<=-1&&(C=Math.PI),m(_,A)>=1&&(C=0),0===o&&C>0&&(C-=2*Math.PI),1===o&&C<0&&(C+=2*Math.PI),{cx:w,cy:P,rx:b(t,[s,f])?0:e,ry:b(t,[s,f])?0:r,startAngle:O,endAngle:O+C,xRotation:i,arcFlag:a,sweepFlag:o}}function w(t,n){return[n[0]+(n[0]-t[0]),n[1]+(n[1]-t[1])]}function P(t,n){var e=t.prePoint,r=t.currentPoint,i=t.nextPoint,a=Math.pow(r[0]-e[0],2)+Math.pow(r[1]-e[1],2),o=Math.pow(r[0]-i[0],2)+Math.pow(r[1]-i[1],2),u=Math.pow(e[0]-i[0],2)+Math.pow(e[1]-i[1],2),c=Math.acos((a+o-u)/(2*Math.sqrt(a)*Math.sqrt(o)));if(!c||0===Math.sin(c)||Object(l.g)(c,0))return{xExtra:0,yExtra:0};var s=Math.abs(Math.atan2(i[1]-r[1],i[0]-r[0])),f=Math.abs(Math.atan2(i[0]-r[0],i[1]-r[1]));return s=s>Math.PI/2?Math.PI-s:s,f=f>Math.PI/2?Math.PI-f:f,{xExtra:Math.cos(c/2-s)*(n/2*(1/Math.sin(c/2)))-n/2||0,yExtra:Math.cos(f-c/2)*(n/2*(1/Math.sin(c/2)))-n/2||0}}i("rect",o),i("image",o),i("circle",u),i("marker",u),i("polyline",(function(t){for(var n=t.attr().points,e=[],r=[],i=0;i<n.length;i++){var a=n[i];e.push(a[0]),r.push(a[1])}var o=c.Util.getBBoxByArray(e,r),u=o.x,s=o.y,h={minX:u,minY:s,maxX:u+o.width,maxY:s+o.height};return{x:(h=f(t,h)).minX,y:h.minY,width:h.maxX-h.minX,height:h.maxY-h.minY}})),i("polygon",(function(t){for(var n=t.attr().points,e=[],r=[],i=0;i<n.length;i++){var a=n[i];e.push(a[0]),r.push(a[1])}return c.Util.getBBoxByArray(e,r)})),i("text",(function(t){var n=t.attr(),e=n.x,r=n.y,i=n.text,a=n.fontSize,o=n.lineHeight,u=n.font;u||(u=Object(h.a)(n));var c,s=Object(h.c)(i,u);if(s){var f=n.textAlign,l=n.textBaseline,p=Object(h.b)(i,a,o),d={x:e,y:r-p};f&&("end"===f||"right"===f?d.x-=s:"center"===f&&(d.x-=s/2)),l&&("top"===l?d.y+=p:"middle"===l&&(d.y+=p/2)),c={x:d.x,y:d.y,width:s,height:p}}else c={x:e,y:r,width:0,height:0};return c})),i("path",(function(t){var n=t.attr(),e=n.path,r=n.stroke?n.lineWidth:0,i=function(t,n){for(var e=[],r=[],i=[],a=0;a<t.length;a++){var o=(y=t[a]).currentPoint,u=y.params,s=y.prePoint,f=void 0;switch(y.command){case"Q":f=c.Quad.box(s[0],s[1],u[1],u[2],u[3],u[4]);break;case"C":f=c.Cubic.box(s[0],s[1],u[1],u[2],u[3],u[4],u[5],u[6]);break;case"A":var h=y.arcParams;f=c.Arc.box(h.cx,h.cy,h.rx,h.ry,h.xRotation,h.startAngle,h.endAngle);break;default:e.push(o[0]),r.push(o[1])}f&&(y.box=f,e.push(f.x,f.x+f.width),r.push(f.y,f.y+f.height)),n&&("L"===y.command||"M"===y.command)&&y.prePoint&&y.nextPoint&&i.push(y)}e=e.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0})),r=r.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0}));var p=Object(l.k)(e),d=Object(l.k)(r),v=Object(l.j)(e),g=Object(l.j)(r);if(0===i.length)return{x:p,y:d,width:v-p,height:g-d};for(a=0;a<i.length;a++){var y;(o=(y=i[a]).currentPoint)[0]===p?p-=P(y,n).xExtra:o[0]===v&&(v+=P(y,n).xExtra),o[1]===d?d-=P(y,n).yExtra:o[1]===g&&(g+=P(y,n).yExtra)}return{x:p,y:d,width:v-p,height:g-d}}(t.get("segments")||function(t){for(var n=[],e=null,r=null,i=null,a=0,o=(t=v(t)).length,u=0;u<o;u++){var c=t[u];r=t[u+1];var s=c[0],f={command:s,prePoint:e,params:c,startTangent:null,endTangent:null};switch(s){case"M":i=[c[1],c[2]],a=u;break;case"A":var h=M(e,c);f.arcParams=h}if("Z"===s)e=i,r=t[a+1];else{var l=c.length;e=[c[l-2],c[l-1]]}r&&"Z"===r[0]&&(r=t[a],n[a]&&(n[a].prePoint=e)),f.currentPoint=e,n[a]&&b(e,n[a].currentPoint)&&(n[a].prePoint=f.prePoint);var p=r?[r[r.length-2],r[r.length-1]]:null;f.nextPoint=p;var d=f.prePoint;if(["L","H","V"].includes(s))f.startTangent=[d[0]-e[0],d[1]-e[1]],f.endTangent=[e[0]-d[0],e[1]-d[1]];else if("Q"===s){var g=[c[1],c[2]];f.startTangent=[d[0]-g[0],d[1]-g[1]],f.endTangent=[e[0]-g[0],e[1]-g[1]]}else if("T"===s){g=w((x=n[u-1]).currentPoint,d);"Q"===x.command?(f.command="Q",f.startTangent=[d[0]-g[0],d[1]-g[1]],f.endTangent=[e[0]-g[0],e[1]-g[1]]):(f.command="TL",f.startTangent=[d[0]-e[0],d[1]-e[1]],f.endTangent=[e[0]-d[0],e[1]-d[1]])}else if("C"===s){var y=[c[1],c[2]],m=[c[3],c[4]];f.startTangent=[d[0]-y[0],d[1]-y[1]],f.endTangent=[e[0]-m[0],e[1]-m[1]],0===f.startTangent[0]&&0===f.startTangent[1]&&(f.startTangent=[y[0]-m[0],y[1]-m[1]]),0===f.endTangent[0]&&0===f.endTangent[1]&&(f.endTangent=[m[0]-y[0],m[1]-y[1]])}else if("S"===s){var x;y=w((x=n[u-1]).currentPoint,d),m=[c[1],c[2]];"C"===x.command?(f.command="C",f.startTangent=[d[0]-y[0],d[1]-y[1]],f.endTangent=[e[0]-m[0],e[1]-m[1]]):(f.command="SQ",f.startTangent=[d[0]-m[0],d[1]-m[1]],f.endTangent=[e[0]-m[0],e[1]-m[1]])}else if("A"===s){var P=.001,_=f.arcParams||{},A=_.cx,O=void 0===A?0:A,C=_.cy,S=void 0===C?0:C,j=_.rx,k=void 0===j?0:j,T=_.ry,E=void 0===T?0:T,B=_.sweepFlag,I=void 0===B?0:B,F=_.startAngle,D=void 0===F?0:F,q=_.endAngle,X=void 0===q?0:q;0===I&&(P*=-1);var N=k*Math.cos(D-P)+O,R=E*Math.sin(D-P)+S;f.startTangent=[N-i[0],R-i[1]];var L=k*Math.cos(D+X+P)+O,Y=E*Math.sin(D+X-P)+S;f.endTangent=[d[0]-L,d[1]-Y]}n.push(f)}return n}(e),r),a=i.x,o=i.y,u={minX:a,minY:o,maxX:a+i.width,maxY:o+i.height};return{x:(u=f(t,u)).minX,y:u.minY,width:u.maxX-u.minX,height:u.maxY-u.minY}})),i("line",(function(t){var n=t.attr(),e=n.x1,r=n.y1,i=n.x2,a=n.y2,o={minX:Math.min(e,i),maxX:Math.max(e,i),minY:Math.min(r,a),maxY:Math.max(r,a)};return{x:(o=f(t,o)).minX,y:o.minY,width:o.maxX-o.minX,height:o.maxY-o.minY}})),i("ellipse",(function(t){var n=t.attr(),e=n.x,r=n.y,i=n.rx,a=n.ry;return{x:e-i,y:r-a,width:2*i,height:2*a}}))},function(t,n){},function(t,n){},function(t,n,e){"use strict";var r=e(9),i=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return Object(r.a)(n,t),n.prototype.isGroup=function(){return!0},n.prototype.isEntityGroup=function(){return!1},n.prototype.clone=function(){for(var n=t.prototype.clone.call(this),e=this.getChildren(),r=0;r<e.length;r++){var i=e[r];n.add(i.clone())}return n},n}(e(23).a);n.a=i},function(t,n,e){"use strict";var r=e(9),i=e(24),a=e(10),o=function(t){function n(n){return t.call(this,n)||this}return Object(r.a)(n,t),n.prototype._isInBBox=function(t,n){var e=this.getBBox();return e.minX<=t&&e.maxX>=t&&e.minY<=n&&e.maxY>=n},n.prototype.afterAttrsChange=function(n){t.prototype.afterAttrsChange.call(this,n),this.clearCacheBBox()},n.prototype.getBBox=function(){var t=this.cfg.bbox;return t||(t=this.calculateBBox(),this.set("bbox",t)),t},n.prototype.getCanvasBBox=function(){var t=this.cfg.canvasBBox;return t||(t=this.calculateCanvasBBox(),this.set("canvasBBox",t)),t},n.prototype.applyMatrix=function(n){t.prototype.applyMatrix.call(this,n),this.set("canvasBBox",null)},n.prototype.calculateCanvasBBox=function(){var t=this.getBBox(),n=this.getTotalMatrix(),e=t.minX,r=t.minY,i=t.maxX,o=t.maxY;if(n){var u=Object(a.c)(n,[t.minX,t.minY]),c=Object(a.c)(n,[t.maxX,t.minY]),s=Object(a.c)(n,[t.minX,t.maxY]),f=Object(a.c)(n,[t.maxX,t.maxY]);e=Math.min(u[0],c[0],s[0],f[0]),i=Math.max(u[0],c[0],s[0],f[0]),r=Math.min(u[1],c[1],s[1],f[1]),o=Math.max(u[1],c[1],s[1],f[1])}var h=this.attrs;if(h.shadowColor){var l=h.shadowBlur,p=void 0===l?0:l,d=h.shadowOffsetX,v=void 0===d?0:d,g=h.shadowOffsetY,y=void 0===g?0:g,m=e-p+v,x=i+p+v,b=r-p+y,M=o+p+y;e=Math.min(e,m),i=Math.max(i,x),r=Math.min(r,b),o=Math.max(o,M)}return{x:e,y:r,minX:e,minY:r,maxX:i,maxY:o,width:i-e,height:o-r}},n.prototype.clearCacheBBox=function(){this.set("bbox",null),this.set("canvasBBox",null)},n.prototype.isClipShape=function(){return this.get("isClipShape")},n.prototype.isInShape=function(t,n){return!1},n.prototype.isOnlyHitBox=function(){return!1},n.prototype.isHit=function(t,n){var e=this.get("startArrowShape"),r=this.get("endArrowShape"),i=[t,n,1],a=(i=this.invertFromMatrix(i))[0],o=i[1],u=this._isInBBox(a,o);if(this.isOnlyHitBox())return u;if(u&&!this.isClipped(a,o)){if(this.isInShape(a,o))return!0;if(e&&e.isHit(a,o))return!0;if(r&&r.isHit(a,o))return!0}return!1},n}(i.a);n.a=o},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.parseRadius=n.parseStyle=n.parsePattern=n.parseRadialGradient=n.parseLineGradient=void 0;var r=e(8),i=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,a=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,o=/^p\s*\(\s*([axyn])\s*\)\s*(.*)/i,u=/[\d.]+:(#[^\s]+|[^\)]+\))/gi;function c(t,n){var e=t.match(u);(0,r.each)(e,(function(t){var e=t.split(":");n.addColorStop(e[0],e[1])}))}function s(t,n,e){var r,a,o=i.exec(e),u=parseFloat(o[1])%360*(Math.PI/180),s=o[2],f=n.getBBox();u>=0&&u<.5*Math.PI?(r={x:f.minX,y:f.minY},a={x:f.maxX,y:f.maxY}):.5*Math.PI<=u&&u<Math.PI?(r={x:f.maxX,y:f.minY},a={x:f.minX,y:f.maxY}):Math.PI<=u&&u<1.5*Math.PI?(r={x:f.maxX,y:f.maxY},a={x:f.minX,y:f.minY}):(r={x:f.minX,y:f.maxY},a={x:f.maxX,y:f.minY});var h=Math.tan(u),l=h*h,p=(a.x-r.x+h*(a.y-r.y))/(l+1)+r.x,d=h*(a.x-r.x+h*(a.y-r.y))/(l+1)+r.y,v=t.createLinearGradient(r.x,r.y,p,d);return c(s,v),v}function f(t,n,e){var r=a.exec(e),i=parseFloat(r[1]),o=parseFloat(r[2]),s=parseFloat(r[3]),f=r[4];if(0===s){var h=f.match(u);return h[h.length-1].split(":")[1]}var l=n.getBBox(),p=l.maxX-l.minX,d=l.maxY-l.minY,v=Math.sqrt(p*p+d*d)/2,g=t.createRadialGradient(l.minX+p*i,l.minY+d*o,0,l.minX+p/2,l.minY+d/2,s*v);return c(f,g),g}function h(t,n,e){if(n.get("patternSource")&&n.get("patternSource")===e)return n.get("pattern");var r,i,a=o.exec(e),u=a[1],c=a[2];function s(){r=t.createPattern(i,u),n.set("pattern",r),n.set("patternSource",e)}switch(u){case"a":u="repeat";break;case"x":u="repeat-x";break;case"y":u="repeat-y";break;case"n":u="no-repeat";break;default:u="no-repeat"}return i=new Image,c.match(/^data:/i)||(i.crossOrigin="Anonymous"),i.src=c,i.complete?s():(i.onload=s,i.src=i.src),r}n.parseLineGradient=s,n.parseRadialGradient=f,n.parsePattern=h,n.parseStyle=function(t,n,e){var i=n.getBBox();if(isNaN(i.x)||isNaN(i.y)||isNaN(i.width)||isNaN(i.height))return e;if((0,r.isString)(e)){if("("===e[1]||"("===e[2]){if("l"===e[0])return s(t,n,e);if("r"===e[0])return f(t,n,e);if("p"===e[0])return h(t,n,e)}return e}return e instanceof CanvasPattern?e:void 0},n.parseRadius=function(t){var n=0,e=0,i=0,a=0;return(0,r.isArray)(t)?1===t.length?n=e=i=a=t[0]:2===t.length?(n=i=t[0],e=a=t[1]):3===t.length?(n=t[0],e=a=t[1],i=t[2]):(n=t[0],e=t[1],i=t[2],a=t[3]):n=e=i=a=t,[n,e,i,a]}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(8);function i(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function a(t,n){return i(t)*i(n)?(t[0]*n[0]+t[1]*n[1])/(i(t)*i(n)):1}function o(t,n){return(t[0]*n[1]<t[1]*n[0]?-1:1)*Math.acos(a(t,n))}n.default=function(t,n){var e=n[1],i=n[2],u=(0,r.mod)((0,r.toRadian)(n[3]),2*Math.PI),c=n[4],s=n[5],f=t[0],h=t[1],l=n[6],p=n[7],d=Math.cos(u)*(f-l)/2+Math.sin(u)*(h-p)/2,v=-1*Math.sin(u)*(f-l)/2+Math.cos(u)*(h-p)/2,g=d*d/(e*e)+v*v/(i*i);g>1&&(e*=Math.sqrt(g),i*=Math.sqrt(g));var y=e*e*(v*v)+i*i*(d*d),m=y?Math.sqrt((e*e*(i*i)-y)/y):1;c===s&&(m*=-1),isNaN(m)&&(m=0);var x=i?m*e*v/i:0,b=e?m*-i*d/e:0,M=(f+l)/2+Math.cos(u)*x-Math.sin(u)*b,w=(h+p)/2+Math.sin(u)*x+Math.cos(u)*b,P=[(d-x)/e,(v-b)/i],_=[(-1*d-x)/e,(-1*v-b)/i],A=o([1,0],P),O=o(P,_);return a(P,_)<=-1&&(O=Math.PI),a(P,_)>=1&&(O=0),0===s&&O>0&&(O-=2*Math.PI),1===s&&O<0&&(O+=2*Math.PI),{cx:M,cy:w,rx:(0,r.isSamePoint)(t,[l,p])?0:e,ry:(0,r.isSamePoint)(t,[l,p])?0:i,startAngle:A,endAngle:A+O,xRotation:u,arcFlag:c,sweepFlag:s}}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(12);n.default=function(t,n,e){var i=(0,r.getOffScreenContext)();return t.createPath(i),i.isPointInPath(n,e)}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});function r(t){return Math.abs(t)<1e-6?0:t<0?-1:1}function i(t,n,e){return(e[0]-t[0])*(n[1]-t[1])==(n[0]-t[0])*(e[1]-t[1])&&Math.min(t[0],n[0])<=e[0]&&e[0]<=Math.max(t[0],n[0])&&Math.min(t[1],n[1])<=e[1]&&e[1]<=Math.max(t[1],n[1])}n.default=function(t,n,e){var a=!1,o=t.length;if(o<=2)return!1;for(var u=0;u<o;u++){var c=t[u],s=t[(u+1)%o];if(i(c,s,[n,e]))return!0;r(c[1]-e)>0!=r(s[1]-e)>0&&r(n-(e-c[1])*(c[0]-s[0])/(c[1]-s[1])-c[0])<0&&(a=!a)}return a}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(8);n.default=function(t,n,e,i,a,o,u,c){var s=(Math.atan2(c-n,u-t)+2*Math.PI)%(2*Math.PI);if(s<i||s>a)return!1;var f={x:t+e*Math.cos(s),y:n+e*Math.sin(s)};return(0,r.distance)(f.x,f.y,u,c)<=o/2}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(18);n.default=function(t,n,e,i,a){var o=t.length;if(o<2)return!1;for(var u=0;u<o-1;u++){var c=t[u][0],s=t[u][1],f=t[u+1][0],h=t[u+1][1];if((0,r.default)(c,s,f,h,n,e,i))return!0}if(a){var l=t[0],p=t[o-1];if((0,r.default)(l[0],l[1],p[0],p[1],n,e,i))return!0}return!1}},function(t,n,e){"use strict";e.r(n),e.d(n,"parsePath",(function(){return o})),e.d(n,"catmullRom2Bezier",(function(){return c})),e.d(n,"fillPath",(function(){return h})),e.d(n,"fillPathByDiff",(function(){return p})),e.d(n,"formatPath",(function(){return g})),e.d(n,"pathIntersection",(function(){return $})),e.d(n,"parsePathArray",(function(){return H})),e.d(n,"parsePathString",(function(){return M})),e.d(n,"path2Curve",(function(){return I})),e.d(n,"path2Absolute",(function(){return _})),e.d(n,"reactPath",(function(){return y})),e.d(n,"getArcParams",(function(){return K})),e.d(n,"path2Segments",(function(){return tt})),e.d(n,"getLineIntersect",(function(){return et})),e.d(n,"isPolygonsIntersect",(function(){return ct})),e.d(n,"isPointInPolygon",(function(){return at}));var r=e(5),i=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/gi,a=/[^\s\,]+/gi;var o=function(t){var n=t||[];return Object(r.isArray)(n)?n:Object(r.isString)(n)?(n=n.match(i),Object(r.each)(n,(function(t,e){if((t=t.match(a))[0].length>1){var i=t[0].charAt(0);t.splice(1,0,t[0].substr(1)),t[0]=i}Object(r.each)(t,(function(n,e){isNaN(n)||(t[e]=+n)})),n[e]=t})),n):void 0},u=e(4);var c=function(t,n,e){void 0===n&&(n=!1),void 0===e&&(e=[[0,0],[1,1]]);for(var r=!!n,i=[],a=0,o=t.length;a<o;a+=2)i.push([t[a],t[a+1]]);var c,s,f,h=function(t,n,e,r){var i,a,o,c,s,f,h,l=[],p=!!r;if(p){o=r[0],c=r[1];for(var d=0,v=t.length;d<v;d+=1){var g=t[d];o=u.b.min([0,0],o,g),c=u.b.max([0,0],c,g)}}d=0;for(var y=t.length;d<y;d+=1){g=t[d];if(0!==d||e)if(d!==y-1||e){i=t[[d?d-1:y-1,d-1][e?0:1]],a=t[e?(d+1)%y:d+1];var m=[0,0];m=u.b.sub(m,a,i),m=u.b.scale(m,m,n);var x=u.b.distance(g,i),b=u.b.distance(g,a),M=x+b;0!==M&&(x/=M,b/=M);var w=u.b.scale([0,0],m,-x),P=u.b.scale([0,0],m,b);f=u.b.add([0,0],g,w),s=u.b.add([0,0],g,P),s=u.b.min([0,0],s,u.b.max([0,0],a,g)),s=u.b.max([0,0],s,u.b.min([0,0],a,g)),w=u.b.sub([0,0],s,g),w=u.b.scale([0,0],w,-x/b),f=u.b.add([0,0],g,w),f=u.b.min([0,0],f,u.b.max([0,0],i,g)),f=u.b.max([0,0],f,u.b.min([0,0],i,g)),P=u.b.sub([0,0],g,f),P=u.b.scale([0,0],P,b/x),s=u.b.add([0,0],g,P),p&&(f=u.b.max([0,0],f,o),f=u.b.min([0,0],f,c),s=u.b.max([0,0],s,o),s=u.b.min([0,0],s,c)),l.push(h),l.push(f),h=s}else f=g,l.push(h),l.push(f);else h=g}return e&&l.push(l.shift()),l}(i,.4,r,e),l=i.length,p=[];for(a=0;a<l-1;a+=1)c=h[2*a],s=h[2*a+1],f=i[a+1],p.push(["C",c[0],c[1],s[0],s[1],f[0],f[1]]);return r&&(c=h[l],s=h[l+1],f=i[0],p.push(["C",c[0],c[1],s[0],s[1],f[0],f[1]])),p};function s(t,n){var e=[],r=[];return t.length&&function t(n,i){if(1===n.length)e.push(n[0]),r.push(n[0]);else{for(var a=[],o=0;o<n.length-1;o++)0===o&&e.push(n[0]),o===n.length-2&&r.push(n[o+1]),a[o]=[(1-i)*n[o][0]+i*n[o+1][0],(1-i)*n[o][1]+i*n[o+1][1]];t(a,i)}}(t,n),{left:e,right:r.reverse()}}function f(t,n,e){if(1===e)return[[].concat(t)];var r=[];if("L"===n[0]||"C"===n[0]||"Q"===n[0])r=r.concat(function(t,n,e){var r=[[t[1],t[2]]];e=e||2;var i=[];"A"===n[0]?(r.push(n[6]),r.push(n[7])):"C"===n[0]?(r.push([n[1],n[2]]),r.push([n[3],n[4]]),r.push([n[5],n[6]])):"S"===n[0]||"Q"===n[0]?(r.push([n[1],n[2]]),r.push([n[3],n[4]])):r.push([n[1],n[2]]);for(var a=r,o=1/e,u=0;u<e-1;u++){var c=s(a,o/(1-o*u));i.push(c.left),a=c.right}return i.push(a),i.map((function(t){var n=[];return 4===t.length&&(n.push("C"),n=n.concat(t[2])),t.length>=3&&(3===t.length&&n.push("Q"),n=n.concat(t[1])),2===t.length&&n.push("L"),n=n.concat(t[t.length-1])}))}(t,n,e));else{var i=[].concat(t);"M"===i[0]&&(i[0]="L");for(var a=0;a<=e-1;a++)r.push(i)}return r}function h(t,n){if(1===t.length)return t;var e=t.length-1,r=n.length-1,i=e/r,a=[];if(1===t.length&&"M"===t[0][0]){for(var o=0;o<r-e;o++)t.push(t[0]);return t}for(o=0;o<r;o++){var u=Math.floor(i*o);a[u]=(a[u]||0)+1}var c=a.reduce((function(n,r,i){return i===e?n.concat(t[e]):n.concat(f(t[i],t[i+1],r))}),[]);return c.unshift(t[0]),"Z"!==n[r]&&"z"!==n[r]||c.push("Z"),c}function l(t,n,e){var r=null,i=e;return n<i&&(i=n,r="add"),t<i&&(i=t,r="del"),{type:r,min:i}}function p(t,n){var e=function(t,n){var e,i,a=t.length,o=n.length,u=0;if(0===a||0===o)return null;for(var c=[],s=0;s<=a;s++)c[s]=[],c[s][0]={min:s};for(var f=0;f<=o;f++)c[0][f]={min:f};for(s=1;s<=a;s++){e=t[s-1];for(f=1;f<=o;f++){i=n[f-1],u=Object(r.isEqual)(e,i)?0:1;var h=c[s-1][f].min+1,p=c[s][f-1].min+1,d=c[s-1][f-1].min+u;c[s][f]=l(h,p,d)}}return c}(t,n),i=t.length,a=n.length,o=[],u=1,c=1;if(e[i][a]!==i){for(var s=1;s<=i;s++){var f=e[s][s].min;c=s;for(var h=u;h<=a;h++)e[s][h].min<f&&(f=e[s][h].min,c=h);u=c,e[s][u].type&&o.push({index:s-1,type:e[s][u].type})}for(s=o.length-1;s>=0;s--)u=o[s].index,"add"===o[s].type?t.splice(u,0,[].concat(t[u])):t.splice(u,1)}if((i=t.length)<a)for(s=0;s<a-i;s++)"z"===t[i-1][0]||"Z"===t[i-1][0]?t.splice(i-2,0,t[i-2]):t.push(t[i-1]);return t}function d(t){var n=[];switch(t[0]){case"M":case"L":n.push([t[1],t[2]]);break;case"A":n.push([t[6],t[7]]);break;case"Q":n.push([t[3],t[4]]),n.push([t[1],t[2]]);break;case"T":n.push([t[1],t[2]]);break;case"C":n.push([t[5],t[6]]),n.push([t[1],t[2]]),n.push([t[3],t[4]]);break;case"S":n.push([t[3],t[4]]),n.push([t[1],t[2]]);break;case"H":case"V":n.push([t[1],t[1]])}return n}function v(t,n,e){for(var r,i=[].concat(t),a=1/(e+1),o=d(n)[0],u=1;u<=e;u++)a*=u,0===(r=Math.floor(t.length*a))?i.unshift([o[0]*a+t[r][0]*(1-a),o[1]*a+t[r][1]*(1-a)]):i.splice(r,0,[o[0]*a+t[r][0]*(1-a),o[1]*a+t[r][1]*(1-a)]);return i}function g(t,n){if(t.length<=1)return t;for(var e,r=0;r<n.length;r++)if(t[r][0]!==n[r][0])switch(e=d(t[r]),n[r][0]){case"M":t[r]=["M"].concat(e[0]);break;case"L":t[r]=["L"].concat(e[0]);break;case"A":t[r]=[].concat(n[r]),t[r][6]=e[0][0],t[r][7]=e[0][1];break;case"Q":if(e.length<2){if(!(r>0)){t[r]=n[r];break}e=v(e,t[r-1],1)}t[r]=["Q"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;case"T":t[r]=["T"].concat(e[0]);break;case"C":if(e.length<3){if(!(r>0)){t[r]=n[r];break}e=v(e,t[r-1],2)}t[r]=["C"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;case"S":if(e.length<2){if(!(r>0)){t[r]=n[r];break}e=v(e,t[r-1],1)}t[r]=["S"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;default:t[r]=n[r]}return t}function y(t,n,e,r,i){return i?[["M",+t+ +i,n],["l",e-2*i,0],["a",i,i,0,0,1,i,i],["l",0,r-2*i],["a",i,i,0,0,1,-i,i],["l",2*i-e,0],["a",i,i,0,0,1,-i,-i],["l",0,2*i-r],["a",i,i,0,0,1,i,-i],["z"]]:[["M",t,n],["l",e,0],["l",0,r],["l",-e,0],["z"]]}var m="\t\n\v\f\r   ᠎             　\u2028\u2029",x=new RegExp("([a-z])["+m+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+m+"]*,?["+m+"]*)+)","ig"),b=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+m+"]*,?["+m+"]*","ig");function M(t){if(!t)return null;if(Object(r.isArray)(t))return t;var n={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},e=[];return String(t).replace(x,(function(t,r,i){var a=[],o=r.toLowerCase();if(i.replace(b,(function(t,n){n&&a.push(+n)})),"m"===o&&a.length>2&&(e.push([r].concat(a.splice(0,2))),o="l",r="m"===r?"l":"L"),"o"===o&&1===a.length&&e.push([r,a[0]]),"r"===o)e.push([r].concat(a));else for(;a.length>=n[o]&&(e.push([r].concat(a.splice(0,n[o]))),n[o]););return""})),e}var w=/[a-z]/;function P(t,n){return[n[0]+(n[0]-t[0]),n[1]+(n[1]-t[1])]}function _(t){var n=M(t);if(!n||!n.length)return[["M",0,0]];for(var e=!1,r=0;r<n.length;r++){var i=n[r][0];if(w.test(i)||["V","H","T","S"].indexOf(i)>=0){e=!0;break}}if(!e)return n;var a=[],o=0,u=0,c=0,s=0,f=0,h=n[0];"M"!==h[0]&&"m"!==h[0]||(c=o=+h[1],s=u=+h[2],f++,a[0]=["M",o,u]);r=f;for(var l=n.length;r<l;r++){var p=n[r],d=a[r-1],v=[],g=(i=p[0]).toUpperCase();if(i!==g)switch(v[0]=g,g){case"A":v[1]=p[1],v[2]=p[2],v[3]=p[3],v[4]=p[4],v[5]=p[5],v[6]=+p[6]+o,v[7]=+p[7]+u;break;case"V":v[1]=+p[1]+u;break;case"H":v[1]=+p[1]+o;break;case"M":c=+p[1]+o,s=+p[2]+u,v[1]=c,v[2]=s;break;default:for(var y=1,m=p.length;y<m;y++)v[y]=+p[y]+(y%2?o:u)}else v=n[r];switch(g){case"Z":o=+c,u=+s;break;case"H":v=["L",o=v[1],u];break;case"V":v=["L",o,u=v[1]];break;case"T":o=v[1],u=v[2];var x=P([d[1],d[2]],[d[3],d[4]]);v=["Q",x[0],x[1],o,u];break;case"S":o=v[v.length-2],u=v[v.length-1];var b=d.length,_=P([d[b-4],d[b-3]],[d[b-2],d[b-1]]);v=["C",_[0],_[1],v[1],v[2],o,u];break;case"M":c=v[v.length-2],s=v[v.length-1];break;default:o=v[v.length-2],u=v[v.length-1]}a.push(v)}return a}var A=2*Math.PI,O=function(t,n,e,r,i,a,o){var u=t.x,c=t.y;return{x:r*(u*=n)-i*(c*=e)+a,y:i*u+r*c+o}},C=function(t,n){var e=1.5707963267948966===n?.551915024494:-1.5707963267948966===n?-.551915024494:4/3*Math.tan(n/4),r=Math.cos(t),i=Math.sin(t),a=Math.cos(t+n),o=Math.sin(t+n);return[{x:r-i*e,y:i+r*e},{x:a+o*e,y:o-a*e},{x:a,y:o}]},S=function(t,n,e,r){var i=t*e+n*r;return i>1&&(i=1),i<-1&&(i=-1),(t*r-n*e<0?-1:1)*Math.acos(i)},j=function(t){var n=t.px,e=t.py,r=t.cx,i=t.cy,a=t.rx,o=t.ry,u=t.xAxisRotation,c=void 0===u?0:u,s=t.largeArcFlag,f=void 0===s?0:s,h=t.sweepFlag,l=void 0===h?0:h,p=[];if(0===a||0===o)return[{x1:0,y1:0,x2:0,y2:0,x:r,y:i}];var d=Math.sin(c*A/360),v=Math.cos(c*A/360),g=v*(n-r)/2+d*(e-i)/2,y=-d*(n-r)/2+v*(e-i)/2;if(0===g&&0===y)return[{x1:0,y1:0,x2:0,y2:0,x:r,y:i}];a=Math.abs(a),o=Math.abs(o);var m=Math.pow(g,2)/Math.pow(a,2)+Math.pow(y,2)/Math.pow(o,2);m>1&&(a*=Math.sqrt(m),o*=Math.sqrt(m));var x=function(t,n,e,r,i,a,o,u,c,s,f,h){var l=Math.pow(i,2),p=Math.pow(a,2),d=Math.pow(f,2),v=Math.pow(h,2),g=l*p-l*v-p*d;g<0&&(g=0),g/=l*v+p*d;var y=(g=Math.sqrt(g)*(o===u?-1:1))*i/a*h,m=g*-a/i*f,x=s*y-c*m+(t+e)/2,b=c*y+s*m+(n+r)/2,M=(f-y)/i,w=(h-m)/a,P=(-f-y)/i,_=(-h-m)/a,O=S(1,0,M,w),C=S(M,w,P,_);return 0===u&&C>0&&(C-=A),1===u&&C<0&&(C+=A),[x,b,O,C]}(n,e,r,i,a,o,f,l,d,v,g,y),b=x[0],M=x[1],w=x[2],P=x[3],_=Math.abs(P)/(A/4);Math.abs(1-_)<1e-7&&(_=1);var j=Math.max(Math.ceil(_),1);P/=j;for(var k=0;k<j;k++)p.push(C(w,P)),w+=P;return p.map((function(t){var n=O(t[0],a,o,v,d,b,M),e=n.x,r=n.y,i=O(t[1],a,o,v,d,b,M),u=i.x,c=i.y,s=O(t[2],a,o,v,d,b,M);return{x1:e,y1:r,x2:u,y2:c,x:s.x,y:s.y}}))};function k(t,n,e,r,i,a,o,u,c){return j({px:t,py:n,cx:u,cy:c,rx:e,ry:r,xAxisRotation:i,largeArcFlag:a,sweepFlag:o}).reduce((function(t,n){var e=n.x1,r=n.y1,i=n.x2,a=n.y2,o=n.x,u=n.y;return t.push(e,r,i,a,o,u),t}),[])}function T(t,n,e,r,i,a){return[1/3*t+2/3*e,1/3*n+2/3*r,1/3*i+2/3*e,1/3*a+2/3*r,i,a]}function E(t,n,e,r){return[t,n,e,r,e,r]}function B(t,n){"TQ".indexOf(t[0])<0&&(n.qx=null,n.qy=null);var e=t.slice(1),r=e[0],i=e[1];switch(t[0]){case"M":return n.x=r,n.y=i,t;case"A":return["C"].concat(k.apply(0,[n.x1,n.y1].concat(t.slice(1))));case"Q":return n.qx=r,n.qy=i,["C"].concat(T.apply(0,[n.x1,n.y1].concat(t.slice(1))));case"L":return["C"].concat(E(n.x1,n.y1,t[1],t[2]));case"H":return["C"].concat(E(n.x1,n.y1,t[1],n.y1));case"V":return["C"].concat(E(n.x1,n.y1,n.x1,t[1]));case"Z":return["C"].concat(E(n.x1,n.y1,n.x,n.y))}return t}function I(t,n){void 0===n&&(n=!1);for(var e,r,i=_(t),a={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null},o=[],u="",c=i.length,s=[],f=0;f<c;f+=1)i[f]&&(u=i[f][0]),o[f]=u,i[f]=B(i[f],a),F(i,o,f),c=i.length,"Z"===u&&s.push(f),r=(e=i[f]).length,a.x1=+e[r-2],a.y1=+e[r-1],a.x2=+e[r-4]||a.x1,a.y2=+e[r-3]||a.y1;return n?[i,s]:i}function F(t,n,e){if(t[e].length>7){t[e].shift();for(var r=t[e],i=e;r.length;)n[e]="A",t.splice(i+=1,0,["C"].concat(r.splice(0,6)));t.splice(e,1)}}var D=function(t,n,e,r,i){return t*(t*(-3*n+9*e-9*r+3*i)+6*n-12*e+6*r)-3*n+3*e},q=function(t,n,e,r,i,a,o,u,c){null===c&&(c=1);for(var s=(c=c>1?1:c<0?0:c)/2,f=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],h=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],l=0,p=0;p<12;p++){var d=s*f[p]+s,v=D(d,t,e,i,o),g=D(d,n,r,a,u),y=v*v+g*g;l+=h[p]*Math.sqrt(y)}return s*l},X=function(t,n,e,r,i,a,o,u){for(var c,s,f,h,l=[],p=[[],[]],d=0;d<2;++d)if(0===d?(s=6*t-12*e+6*i,c=-3*t+9*e-9*i+3*o,f=3*e-3*t):(s=6*n-12*r+6*a,c=-3*n+9*r-9*a+3*u,f=3*r-3*n),Math.abs(c)<1e-12){if(Math.abs(s)<1e-12)continue;(h=-f/s)>0&&h<1&&l.push(h)}else{var v=s*s-4*f*c,g=Math.sqrt(v);if(!(v<0)){var y=(-s+g)/(2*c);y>0&&y<1&&l.push(y);var m=(-s-g)/(2*c);m>0&&m<1&&l.push(m)}}for(var x,b=l.length,M=b;b--;)x=1-(h=l[b]),p[0][b]=x*x*x*t+3*x*x*h*e+3*x*h*h*i+h*h*h*o,p[1][b]=x*x*x*n+3*x*x*h*r+3*x*h*h*a+h*h*h*u;return p[0][M]=t,p[1][M]=n,p[0][M+1]=o,p[1][M+1]=u,p[0].length=p[1].length=M+2,{min:{x:Math.min.apply(0,p[0]),y:Math.min.apply(0,p[1])},max:{x:Math.max.apply(0,p[0]),y:Math.max.apply(0,p[1])}}},N=function(t,n,e,r,i,a,o,u){if(!(Math.max(t,e)<Math.min(i,o)||Math.min(t,e)>Math.max(i,o)||Math.max(n,r)<Math.min(a,u)||Math.min(n,r)>Math.max(a,u))){var c=(t-e)*(a-u)-(n-r)*(i-o);if(c){var s=((t*r-n*e)*(i-o)-(t-e)*(i*u-a*o))/c,f=((t*r-n*e)*(a-u)-(n-r)*(i*u-a*o))/c,h=+s.toFixed(2),l=+f.toFixed(2);if(!(h<+Math.min(t,e).toFixed(2)||h>+Math.max(t,e).toFixed(2)||h<+Math.min(i,o).toFixed(2)||h>+Math.max(i,o).toFixed(2)||l<+Math.min(n,r).toFixed(2)||l>+Math.max(n,r).toFixed(2)||l<+Math.min(a,u).toFixed(2)||l>+Math.max(a,u).toFixed(2)))return{x:s,y:f}}}},R=function(t,n,e){return n>=t.x&&n<=t.x+t.width&&e>=t.y&&e<=t.y+t.height},L=function(t,n,e,r){return null===t&&(t=n=e=r=0),null===n&&(n=t.y,e=t.width,r=t.height,t=t.x),{x:t,y:n,width:e,w:e,height:r,h:r,x2:t+e,y2:n+r,cx:t+e/2,cy:n+r/2,r1:Math.min(e,r)/2,r2:Math.max(e,r)/2,r0:Math.sqrt(e*e+r*r)/2,path:y(t,n,e,r),vb:[t,n,e,r].join(" ")}},Y=function(t,n,e,i,a,o,u,c){Object(r.isArray)(t)||(t=[t,n,e,i,a,o,u,c]);var s=X.apply(null,t);return L(s.min.x,s.min.y,s.max.x-s.min.x,s.max.y-s.min.y)},z=function(t,n,e,r,i,a,o,u,c){var s=1-c,f=Math.pow(s,3),h=Math.pow(s,2),l=c*c,p=l*c,d=t+2*c*(e-t)+l*(i-2*e+t),v=n+2*c*(r-n)+l*(a-2*r+n),g=e+2*c*(i-e)+l*(o-2*i+e),y=r+2*c*(a-r)+l*(u-2*a+r);return{x:f*t+3*h*c*e+3*s*c*c*i+p*o,y:f*n+3*h*c*r+3*s*c*c*a+p*u,m:{x:d,y:v},n:{x:g,y:y},start:{x:s*t+c*e,y:s*n+c*r},end:{x:s*i+c*o,y:s*a+c*u},alpha:90-180*Math.atan2(d-g,v-y)/Math.PI}},W=function(t,n,e){if(!function(t,n){return t=L(t),n=L(n),R(n,t.x,t.y)||R(n,t.x2,t.y)||R(n,t.x,t.y2)||R(n,t.x2,t.y2)||R(t,n.x,n.y)||R(t,n.x2,n.y)||R(t,n.x,n.y2)||R(t,n.x2,n.y2)||(t.x<n.x2&&t.x>n.x||n.x<t.x2&&n.x>t.x)&&(t.y<n.y2&&t.y>n.y||n.y<t.y2&&n.y>t.y)}(Y(t),Y(n)))return e?0:[];for(var r=~~(q.apply(0,t)/8),i=~~(q.apply(0,n)/8),a=[],o=[],u={},c=e?0:[],s=0;s<r+1;s++){var f=z.apply(0,t.concat(s/r));a.push({x:f.x,y:f.y,t:s/r})}for(s=0;s<i+1;s++){f=z.apply(0,n.concat(s/i));o.push({x:f.x,y:f.y,t:s/i})}for(s=0;s<r;s++)for(var h=0;h<i;h++){var l=a[s],p=a[s+1],d=o[h],v=o[h+1],g=Math.abs(p.x-l.x)<.001?"y":"x",y=Math.abs(v.x-d.x)<.001?"y":"x",m=N(l.x,l.y,p.x,p.y,d.x,d.y,v.x,v.y);if(m){if(u[m.x.toFixed(4)]===m.y.toFixed(4))continue;u[m.x.toFixed(4)]=m.y.toFixed(4);var x=l.t+Math.abs((m[g]-l[g])/(p[g]-l[g]))*(p.t-l.t),b=d.t+Math.abs((m[y]-d[y])/(v[y]-d[y]))*(v.t-d.t);x>=0&&x<=1&&b>=0&&b<=1&&(e?c++:c.push({x:m.x,y:m.y,t1:x,t2:b}))}}return c};function $(t,n){return function(t,n,e){var r,i,a,o,u,c,s,f,h,l;t=I(t),n=I(n);for(var p=e?0:[],d=0,v=t.length;d<v;d++){var g=t[d];if("M"===g[0])r=u=g[1],i=c=g[2];else{"C"===g[0]?(h=[r,i].concat(g.slice(1)),r=h[6],i=h[7]):(h=[r,i,r,i,u,c,u,c],r=u,i=c);for(var y=0,m=n.length;y<m;y++){var x=n[y];if("M"===x[0])a=s=x[1],o=f=x[2];else{"C"===x[0]?(l=[a,o].concat(x.slice(1)),a=l[6],o=l[7]):(l=[a,o,a,o,s,f,s,f],a=s,o=f);var b=W(h,l,e);if(e)p+=b;else{for(var M=0,w=b.length;M<w;M++)b[M].segment1=d,b[M].segment2=y,b[M].bez1=h,b[M].bez2=l;p=p.concat(b)}}}}}return p}(t,n)}var V=/,?([a-z]),?/gi;function H(t){return t.join(",").replace(V,"$1")}function Q(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function G(t,n){return Q(t)*Q(n)?(t[0]*n[0]+t[1]*n[1])/(Q(t)*Q(n)):1}function Z(t,n){return(t[0]*n[1]<t[1]*n[0]?-1:1)*Math.acos(G(t,n))}function U(t,n){return t[0]===n[0]&&t[1]===n[1]}function K(t,n){var e=n[1],i=n[2],a=Object(r.mod)(Object(r.toRadian)(n[3]),2*Math.PI),o=n[4],u=n[5],c=t[0],s=t[1],f=n[6],h=n[7],l=Math.cos(a)*(c-f)/2+Math.sin(a)*(s-h)/2,p=-1*Math.sin(a)*(c-f)/2+Math.cos(a)*(s-h)/2,d=l*l/(e*e)+p*p/(i*i);d>1&&(e*=Math.sqrt(d),i*=Math.sqrt(d));var v=e*e*(p*p)+i*i*(l*l),g=v?Math.sqrt((e*e*(i*i)-v)/v):1;o===u&&(g*=-1),isNaN(g)&&(g=0);var y=i?g*e*p/i:0,m=e?g*-i*l/e:0,x=(c+f)/2+Math.cos(a)*y-Math.sin(a)*m,b=(s+h)/2+Math.sin(a)*y+Math.cos(a)*m,M=[(l-y)/e,(p-m)/i],w=[(-1*l-y)/e,(-1*p-m)/i],P=Z([1,0],M),_=Z(M,w);return G(M,w)<=-1&&(_=Math.PI),G(M,w)>=1&&(_=0),0===u&&_>0&&(_-=2*Math.PI),1===u&&_<0&&(_+=2*Math.PI),{cx:x,cy:b,rx:U(t,[f,h])?0:e,ry:U(t,[f,h])?0:i,startAngle:P,endAngle:P+_,xRotation:a,arcFlag:o,sweepFlag:u}}function J(t,n){return[n[0]+(n[0]-t[0]),n[1]+(n[1]-t[1])]}function tt(t){for(var n=[],e=null,r=null,i=null,a=0,u=(t=o(t)).length,c=0;c<u;c++){var s=t[c];r=t[c+1];var f=s[0],h={command:f,prePoint:e,params:s,startTangent:null,endTangent:null};switch(f){case"M":i=[s[1],s[2]],a=c;break;case"A":var l=K(e,s);h.arcParams=l}if("Z"===f)e=i,r=t[a+1];else{var p=s.length;e=[s[p-2],s[p-1]]}r&&"Z"===r[0]&&(r=t[a],n[a]&&(n[a].prePoint=e)),h.currentPoint=e,n[a]&&U(e,n[a].currentPoint)&&(n[a].prePoint=h.prePoint);var d=r?[r[r.length-2],r[r.length-1]]:null;h.nextPoint=d;var v=h.prePoint;if(["L","H","V"].includes(f))h.startTangent=[v[0]-e[0],v[1]-e[1]],h.endTangent=[e[0]-v[0],e[1]-v[1]];else if("Q"===f){var g=[s[1],s[2]];h.startTangent=[v[0]-g[0],v[1]-g[1]],h.endTangent=[e[0]-g[0],e[1]-g[1]]}else if("T"===f){g=J((x=n[c-1]).currentPoint,v);"Q"===x.command?(h.command="Q",h.startTangent=[v[0]-g[0],v[1]-g[1]],h.endTangent=[e[0]-g[0],e[1]-g[1]]):(h.command="TL",h.startTangent=[v[0]-e[0],v[1]-e[1]],h.endTangent=[e[0]-v[0],e[1]-v[1]])}else if("C"===f){var y=[s[1],s[2]],m=[s[3],s[4]];h.startTangent=[v[0]-y[0],v[1]-y[1]],h.endTangent=[e[0]-m[0],e[1]-m[1]],0===h.startTangent[0]&&0===h.startTangent[1]&&(h.startTangent=[y[0]-m[0],y[1]-m[1]]),0===h.endTangent[0]&&0===h.endTangent[1]&&(h.endTangent=[m[0]-y[0],m[1]-y[1]])}else if("S"===f){var x;y=J((x=n[c-1]).currentPoint,v),m=[s[1],s[2]];"C"===x.command?(h.command="C",h.startTangent=[v[0]-y[0],v[1]-y[1]],h.endTangent=[e[0]-m[0],e[1]-m[1]]):(h.command="SQ",h.startTangent=[v[0]-m[0],v[1]-m[1]],h.endTangent=[e[0]-m[0],e[1]-m[1]])}else if("A"===f){var b=.001,M=h.arcParams||{},w=M.cx,P=void 0===w?0:w,_=M.cy,A=void 0===_?0:_,O=M.rx,C=void 0===O?0:O,S=M.ry,j=void 0===S?0:S,k=M.sweepFlag,T=void 0===k?0:k,E=M.startAngle,B=void 0===E?0:E,I=M.endAngle,F=void 0===I?0:I;0===T&&(b*=-1);var D=C*Math.cos(B-b)+P,q=j*Math.sin(B-b)+A;h.startTangent=[D-i[0],q-i[1]];var X=C*Math.cos(B+F+b)+P,N=j*Math.sin(B+F-b)+A;h.endTangent=[v[0]-X,v[1]-N]}n.push(h)}return n}var nt=function(t,n,e){return t>=n&&t<=e};function et(t,n,e,r){var i=e.x-t.x,a=e.y-t.y,o=n.x-t.x,u=n.y-t.y,c=r.x-e.x,s=r.y-e.y,f=o*s-u*c,h=null;if(f*f>.001*(o*o+u*u)*(c*c+s*s)){var l=(i*s-a*c)/f,p=(i*u-a*o)/f;nt(l,0,1)&&nt(p,0,1)&&(h={x:t.x+l*o,y:t.y+l*u})}return h}function rt(t){return Math.abs(t)<1e-6?0:t<0?-1:1}function it(t,n,e){return(e[0]-t[0])*(n[1]-t[1])==(n[0]-t[0])*(e[1]-t[1])&&Math.min(t[0],n[0])<=e[0]&&e[0]<=Math.max(t[0],n[0])&&Math.min(t[1],n[1])<=e[1]&&e[1]<=Math.max(t[1],n[1])}function at(t,n,e){var r=!1,i=t.length;if(i<=2)return!1;for(var a=0;a<i;a++){var o=t[a],u=t[(a+1)%i];if(it(o,u,[n,e]))return!0;rt(o[1]-e)>0!=rt(u[1]-e)>0&&rt(n-(e-o[1])*(o[0]-u[0])/(o[1]-u[1])-o[0])<0&&(r=!r)}return r}function ot(t){for(var n=[],e=t.length,r=0;r<e-1;r++){var i=t[r],a=t[r+1];n.push({from:{x:i[0],y:i[1]},to:{x:a[0],y:a[1]}})}if(n.length>1){var o=t[0],u=t[e-1];n.push({from:{x:u[0],y:u[1]},to:{x:o[0],y:o[1]}})}return n}function ut(t){var n=t.map((function(t){return t[0]})),e=t.map((function(t){return t[1]}));return{minX:Math.min.apply(null,n),maxX:Math.max.apply(null,n),minY:Math.min.apply(null,e),maxY:Math.max.apply(null,e)}}function ct(t,n){if(t.length<2||n.length<2)return!1;var e,i,a=ut(t),o=ut(n);if(e=a,(i=o).minX>e.maxX||i.maxX<e.minX||i.minY>e.maxY||i.maxY<e.minY)return!1;var u=!1;if(Object(r.each)(n,(function(n){if(at(t,n[0],n[1]))return u=!0,!1})),u)return!0;if(Object(r.each)(t,(function(t){if(at(n,t[0],t[1]))return u=!0,!1})),u)return!0;var c=ot(t),s=ot(n),f=!1;return Object(r.each)(s,(function(t){if(function(t,n){var e=!1;return Object(r.each)(t,(function(t){if(et(t.from,t.to,n.from,n.to))return e=!0,!1})),e}(c,t))return f=!0,!1})),f}},function(t,n,e){"use strict";var r,i,a=e(9),o=e(40),u=e(23),c=e(1),s=e(0),f=0,h=0,l=0,p=0,d=0,v=0,g="object"==typeof performance&&performance.now?performance:Date,y="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function m(){return d||(y(x),d=g.now()+v)}function x(){d=0}function b(){this._call=this._time=this._next=null}function M(t,n,e){var r=new b;return r.restart(t,n,e),r}function w(){d=(p=g.now())+v,f=h=0;try{!function(){m(),++f;for(var t,n=r;n;)(t=d-n._time)>=0&&n._call.call(null,t),n=n._next;--f}()}finally{f=0,function(){var t,n,e=r,a=1/0;for(;e;)e._call?(a>e._time&&(a=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:r=n);i=t,_(a)}(),d=0}}function P(){var t=g.now(),n=t-p;n>1e3&&(v-=n,p=t)}function _(t){f||(h&&(h=clearTimeout(h)),t-d>24?(t<1/0&&(h=setTimeout(w,t-g.now()-v)),l&&(l=clearInterval(l))):(l||(p=g.now(),l=setInterval(P,1e3)),f=1,y(w)))}b.prototype=M.prototype={constructor:b,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?m():+e)+(null==n?0:+n),this._next||i===this||(i?i._next=this:r=this,i=this),this._call=t,this._time=e,_()},stop:function(){this._call&&(this._call=null,this._time=1/0,_())}};var A=function(t,n,e){t.prototype=n.prototype=e,e.constructor=t};function O(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function C(){}var S="\\s*([+-]?\\d+)\\s*",j="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",k="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",T=/^#([0-9a-f]{3,8})$/,E=new RegExp(`^rgb\\(${S},${S},${S}\\)$`),B=new RegExp(`^rgb\\(${k},${k},${k}\\)$`),I=new RegExp(`^rgba\\(${S},${S},${S},${j}\\)$`),F=new RegExp(`^rgba\\(${k},${k},${k},${j}\\)$`),D=new RegExp(`^hsl\\(${j},${k},${k}\\)$`),q=new RegExp(`^hsla\\(${j},${k},${k},${j}\\)$`),X={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function N(){return this.rgb().formatHex()}function R(){return this.rgb().formatRgb()}function L(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=T.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?Y(n):3===e?new V(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?z(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?z(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=E.exec(t))?new V(n[1],n[2],n[3],1):(n=B.exec(t))?new V(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=I.exec(t))?z(n[1],n[2],n[3],n[4]):(n=F.exec(t))?z(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=D.exec(t))?K(n[1],n[2]/100,n[3]/100,1):(n=q.exec(t))?K(n[1],n[2]/100,n[3]/100,n[4]):X.hasOwnProperty(t)?Y(X[t]):"transparent"===t?new V(NaN,NaN,NaN,0):null}function Y(t){return new V(t>>16&255,t>>8&255,255&t,1)}function z(t,n,e,r){return r<=0&&(t=n=e=NaN),new V(t,n,e,r)}function W(t){return t instanceof C||(t=L(t)),t?new V((t=t.rgb()).r,t.g,t.b,t.opacity):new V}function $(t,n,e,r){return 1===arguments.length?W(t):new V(t,n,e,null==r?1:r)}function V(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function H(){return`#${U(this.r)}${U(this.g)}${U(this.b)}`}function Q(){const t=G(this.opacity);return`${1===t?"rgb(":"rgba("}${Z(this.r)}, ${Z(this.g)}, ${Z(this.b)}${1===t?")":`, ${t})`}`}function G(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Z(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function U(t){return((t=Z(t))<16?"0":"")+t.toString(16)}function K(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new tt(t,n,e,r)}function J(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof C||(t=L(t)),!t)return new tt;if(t instanceof tt)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),a=Math.max(n,e,r),o=NaN,u=a-i,c=(a+i)/2;return u?(o=n===a?(e-r)/u+6*(e<r):e===a?(r-n)/u+2:(n-e)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new tt(o,u,c,t.opacity)}function tt(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function nt(t){return(t=(t||0)%360)<0?t+360:t}function et(t){return Math.max(0,Math.min(1,t||0))}function rt(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}function it(t,n,e,r,i){var a=t*t,o=a*t;return((1-3*t+3*a-o)*n+(4-6*a+3*o)*e+(1+3*t+3*a-3*o)*r+o*i)/6}A(C,L,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:N,formatHex:N,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return J(this).formatHsl()},formatRgb:R,toString:R}),A(V,$,O(C,{brighter(t){return t=null==t?1/.7:Math.pow(1/.7,t),new V(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new V(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new V(Z(this.r),Z(this.g),Z(this.b),G(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:H,formatHex:H,formatHex8:function(){return`#${U(this.r)}${U(this.g)}${U(this.b)}${U(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Q,toString:Q})),A(tt,(function(t,n,e,r){return 1===arguments.length?J(t):new tt(t,n,e,null==r?1:r)}),O(C,{brighter(t){return t=null==t?1/.7:Math.pow(1/.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new V(rt(t>=240?t-240:t+120,i,r),rt(t,i,r),rt(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new tt(nt(this.h),et(this.s),et(this.l),G(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=G(this.opacity);return`${1===t?"hsl(":"hsla("}${nt(this.h)}, ${100*et(this.s)}%, ${100*et(this.l)}%${1===t?")":`, ${t})`}`}}));var at=t=>()=>t;function ot(t,n){return function(e){return t+e*n}}function ut(t){return 1==(t=+t)?ct:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):at(isNaN(n)?e:n)}}function ct(t,n){var e=n-t;return e?ot(t,e):at(isNaN(t)?n:t)}var st=function t(n){var e=ut(n);function r(t,n){var r=e((t=$(t)).r,(n=$(n)).r),i=e(t.g,n.g),a=e(t.b,n.b),o=ct(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=a(n),t.opacity=o(n),t+""}}return r.gamma=t,r}(1);function ft(t){return function(n){var e,r,i=n.length,a=new Array(i),o=new Array(i),u=new Array(i);for(e=0;e<i;++e)r=$(n[e]),a[e]=r.r||0,o[e]=r.g||0,u[e]=r.b||0;return a=t(a),o=t(o),u=t(u),r.opacity=1,function(t){return r.r=a(t),r.g=o(t),r.b=u(t),r+""}}}ft((function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],a=t[r+1],o=r>0?t[r-1]:2*i-a,u=r<n-1?t[r+2]:2*a-i;return it((e-r/n)*n,o,i,a,u)}})),ft((function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],a=t[r%n],o=t[(r+1)%n],u=t[(r+2)%n];return it((e-r/n)*n,i,a,o,u)}}));var ht=function(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(a){for(e=0;e<r;++e)i[e]=t[e]*(1-a)+n[e]*a;return i}};function lt(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function pt(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,a=new Array(i),o=new Array(r);for(e=0;e<i;++e)a[e]=bt(t[e],n[e]);for(;e<r;++e)o[e]=n[e];return function(t){for(e=0;e<i;++e)o[e]=a[e](t);return o}}var dt=function(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}},vt=function(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}},gt=function(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=bt(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}},yt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,mt=new RegExp(yt.source,"g");var xt=function(t,n){var e,r,i,a=yt.lastIndex=mt.lastIndex=0,o=-1,u=[],c=[];for(t+="",n+="";(e=yt.exec(t))&&(r=mt.exec(n));)(i=r.index)>a&&(i=n.slice(a,i),u[o]?u[o]+=i:u[++o]=i),(e=e[0])===(r=r[0])?u[o]?u[o]+=r:u[++o]=r:(u[++o]=null,c.push({i:o,x:vt(e,r)})),a=mt.lastIndex;return a<n.length&&(i=n.slice(a),u[o]?u[o]+=i:u[++o]=i),u.length<2?c[0]?function(t){return function(n){return t(n)+""}}(c[0].x):function(t){return function(){return t}}(n):(n=c.length,function(t){for(var e,r=0;r<n;++r)u[(e=c[r]).i]=e.x(t);return u.join("")})},bt=function(t,n){var e,r=typeof n;return null==n||"boolean"===r?at(n):("number"===r?vt:"string"===r?(e=L(n))?(n=e,st):xt:n instanceof L?st:n instanceof Date?dt:lt(n)?ht:Array.isArray(n)?pt:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?gt:vt)(t,n)},Mt=e(21),wt=e(13),Pt=[1,0,0,0,1,0,0,0,1];function _t(t,n,e){var r,i=n.startTime;if(e<i+n.delay||n._paused)return!1;var a=n.duration,o=n.easing,u=Object(Mt.a)(o);if(e=e-i-n.delay,n.repeat)r=u(r=e%a/a);else{if(!((r=e/a)<1))return n.onFrame?t.attr(n.onFrame(1)):t.attr(n.toAttrs),!0;r=u(r)}if(n.onFrame){var c=n.onFrame(r);t.attr(c)}else!function(t,n,e){var r={},i=n.fromAttrs,a=n.toAttrs;if(!t.destroyed){var o,u,c,f;for(var h in a)if(!Object(s.c)(i[h],a[h]))if("path"===h){var l=a[h],p=i[h];l.length>p.length?(l=wt.parsePathString(a[h]),p=wt.parsePathString(i[h]),p=wt.fillPathByDiff(p,l),p=wt.formatPath(p,l),n.fromAttrs.path=p,n.toAttrs.path=l):n.pathFormatted||(l=wt.parsePathString(a[h]),p=wt.parsePathString(i[h]),p=wt.formatPath(p,l),n.fromAttrs.path=p,n.toAttrs.path=l,n.pathFormatted=!0),r[h]=[];for(var d=0;d<l.length;d++){for(var v=l[d],g=p[d],y=[],m=0;m<v.length;m++)Object(s.f)(v[m])&&g&&Object(s.f)(g[m])?(o=bt(g[m],v[m]),y.push(o(e))):y.push(v[m]);r[h].push(y)}}else if("matrix"===h){var x=(c=i[h]||Pt,(lt(f=a[h]||Pt)?ht:pt)(c,f))(e);r[h]=x}else["fill","stroke","fillStyle","strokeStyle"].includes(h)&&(u=a[h],/^[r,R,L,l]{1}[\s]*\(/.test(u))?r[h]=a[h]:Object(s.d)(a[h])||(o=bt(i[h],a[h]),r[h]=o(e));t.attr(r)}}(t,n,r);return!1}var At=function(){function t(t){this.animators=[],this.current=0,this.timer=null,this.canvas=t}return t.prototype.initTimer=function(){var t,n,e,r=this;this.timer=M((function(i){if(r.current=i,r.animators.length>0){for(var a=r.animators.length-1;a>=0;a--)if((t=r.animators[a]).destroyed)r.removeAnimator(a);else{if(!t.isAnimatePaused())for(var o=(n=t.get("animations")).length-1;o>=0;o--)e=n[o],_t(t,e,i)&&(n.splice(o,1),!1,e.callback&&e.callback());0===n.length&&r.removeAnimator(a)}r.canvas.get("autoDraw")||r.canvas.draw()}}))},t.prototype.addAnimator=function(t){this.animators.push(t)},t.prototype.removeAnimator=function(t){this.animators.splice(t,1)},t.prototype.isAnimating=function(){return!!this.animators.length},t.prototype.stop=function(){this.timer&&this.timer.stop()},t.prototype.stopAllAnimations=function(t){void 0===t&&(t=!0),this.animators.forEach((function(n){n.stopAnimate(t)})),this.animators=[],this.canvas.draw()},t.prototype.getTime=function(){return this.current},t}(),Ot=e(19),Ct=["mousedown","mouseup","dblclick","mouseout","mouseover","mousemove","mouseleave","mouseenter","touchstart","touchmove","touchend","dragenter","dragover","dragleave","drop","contextmenu","mousewheel"];function St(t,n,e){e.name=n,e.target=t,e.currentTarget=t,e.delegateTarget=t,t.emit(n,e)}function jt(t,n,e){if(e.bubbles){var r=void 0,i=!1;if("mouseenter"===n?(r=e.fromShape,i=!0):"mouseleave"===n&&(i=!0,r=e.toShape),t.isCanvas()&&i)return;if(r&&Object(c.g)(t,r))return void(e.bubbles=!1);e.name=n,e.currentTarget=t,e.delegateTarget=t,t.emit(n,e)}}var kt=function(){function t(t){var n=this;this.draggingShape=null,this.dragging=!1,this.currentShape=null,this.mousedownShape=null,this.mousedownPoint=null,this._eventCallback=function(t){var e=t.type;n._triggerEvent(e,t)},this._onDocumentMove=function(t){if(n.canvas.get("el")!==t.target&&(n.dragging||n.currentShape)){var e=n._getPointInfo(t);n.dragging&&n._emitEvent("drag",t,e,n.draggingShape)}},this._onDocumentMouseUp=function(t){if(n.canvas.get("el")!==t.target&&n.dragging){var e=n._getPointInfo(t);n.draggingShape&&n._emitEvent("drop",t,e,null),n._emitEvent("dragend",t,e,n.draggingShape),n._afterDrag(n.draggingShape,e,t)}},this.canvas=t.canvas}return t.prototype.init=function(){this._bindEvents()},t.prototype._bindEvents=function(){var t=this,n=this.canvas.get("el");Object(c.a)(Ct,(function(e){n.addEventListener(e,t._eventCallback)})),document&&(document.addEventListener("mousemove",this._onDocumentMove),document.addEventListener("mouseup",this._onDocumentMouseUp))},t.prototype._clearEvents=function(){var t=this,n=this.canvas.get("el");Object(c.a)(Ct,(function(e){n.removeEventListener(e,t._eventCallback)})),document&&(document.removeEventListener("mousemove",this._onDocumentMove),document.removeEventListener("mouseup",this._onDocumentMouseUp))},t.prototype._getEventObj=function(t,n,e,r,i,a){var o=new Ot.a(t,n);return o.fromShape=i,o.toShape=a,o.x=e.x,o.y=e.y,o.clientX=e.clientX,o.clientY=e.clientY,o.propagationPath.push(r),o},t.prototype._getShape=function(t,n){return this.canvas.getShape(t.x,t.y,n)},t.prototype._getPointInfo=function(t){var n=this.canvas,e=n.getClientByEvent(t),r=n.getPointByEvent(t);return{x:r.x,y:r.y,clientX:e.x,clientY:e.y}},t.prototype._triggerEvent=function(t,n){var e=this._getPointInfo(n),r=this._getShape(e,n),i=this["_on"+t],a=!1;if(i)i.call(this,e,r,n);else{var o=this.currentShape;"mouseenter"===t||"dragenter"===t||"mouseover"===t?(this._emitEvent(t,n,e,null,null,r),r&&this._emitEvent(t,n,e,r,null,r),"mouseenter"===t&&this.draggingShape&&this._emitEvent("dragenter",n,e,null)):"mouseleave"===t||"dragleave"===t||"mouseout"===t?(a=!0,o&&this._emitEvent(t,n,e,o,o,null),this._emitEvent(t,n,e,null,o,null),"mouseleave"===t&&this.draggingShape&&this._emitEvent("dragleave",n,e,null)):this._emitEvent(t,n,e,r,null,null)}if(a||(this.currentShape=r),r&&!r.get("destroyed")){var u=this.canvas;u.get("el").style.cursor=r.attr("cursor")||u.get("cursor")}},t.prototype._onmousedown=function(t,n,e){0===e.button&&(this.mousedownShape=n,this.mousedownPoint=t,this.mousedownTimeStamp=e.timeStamp),this._emitEvent("mousedown",e,t,n,null,null)},t.prototype._emitMouseoverEvents=function(t,n,e,r){var i=this.canvas.get("el");e!==r&&(e&&(this._emitEvent("mouseout",t,n,e,e,r),this._emitEvent("mouseleave",t,n,e,e,r),r&&!r.get("destroyed")||(i.style.cursor=this.canvas.get("cursor"))),r&&(this._emitEvent("mouseover",t,n,r,e,r),this._emitEvent("mouseenter",t,n,r,e,r)))},t.prototype._emitDragoverEvents=function(t,n,e,r,i){r?(r!==e&&(e&&this._emitEvent("dragleave",t,n,e,e,r),this._emitEvent("dragenter",t,n,r,e,r)),i||this._emitEvent("dragover",t,n,r)):e&&this._emitEvent("dragleave",t,n,e,e,r),i&&this._emitEvent("dragover",t,n,r)},t.prototype._afterDrag=function(t,n,e){t&&(t.set("capture",!0),this.draggingShape=null),this.dragging=!1;var r=this._getShape(n,e);r!==t&&this._emitMouseoverEvents(e,n,t,r),this.currentShape=r},t.prototype._onmouseup=function(t,n,e){if(0===e.button){var r=this.draggingShape;this.dragging?(r&&this._emitEvent("drop",e,t,n),this._emitEvent("dragend",e,t,r),this._afterDrag(r,t,e)):(this._emitEvent("mouseup",e,t,n),n===this.mousedownShape&&this._emitEvent("click",e,t,n),this.mousedownShape=null,this.mousedownPoint=null)}},t.prototype._ondragover=function(t,n,e){e.preventDefault();var r=this.currentShape;this._emitDragoverEvents(e,t,r,n,!0)},t.prototype._onmousemove=function(t,n,e){var r=this.canvas,i=this.currentShape,a=this.draggingShape;if(this.dragging)a&&this._emitDragoverEvents(e,t,i,n,!1),this._emitEvent("drag",e,t,a);else{var o=this.mousedownPoint;if(o){var u=this.mousedownShape,c=e.timeStamp-this.mousedownTimeStamp,s=o.clientX-t.clientX,f=o.clientY-t.clientY;c>120||s*s+f*f>40?u&&u.get("draggable")?((a=this.mousedownShape).set("capture",!1),this.draggingShape=a,this.dragging=!0,this._emitEvent("dragstart",e,t,a),this.mousedownShape=null,this.mousedownPoint=null):!u&&r.get("draggable")?(this.dragging=!0,this._emitEvent("dragstart",e,t,null),this.mousedownShape=null,this.mousedownPoint=null):(this._emitMouseoverEvents(e,t,i,n),this._emitEvent("mousemove",e,t,n)):(this._emitMouseoverEvents(e,t,i,n),this._emitEvent("mousemove",e,t,n))}else this._emitMouseoverEvents(e,t,i,n),this._emitEvent("mousemove",e,t,n)}},t.prototype._emitEvent=function(t,n,e,r,i,a){var o=this._getEventObj(t,n,e,r,i,a);if(r){o.shape=r,St(r,t,o);for(var u=r.getParent();u;)u.emitDelegation(t,o),o.propagationStopped||jt(u,t,o),o.propagationPath.push(u),u=u.getParent()}else{St(this.canvas,t,o)}},t.prototype.destroy=function(){this._clearEvents(),this.canvas=null,this.currentShape=null,this.draggingShape=null,this.mousedownPoint=null,this.mousedownShape=null,this.mousedownTimeStamp=null},t}(),Tt=Object(o.a)(),Et=Tt&&"firefox"===Tt.name,Bt=function(t){function n(n){var e=t.call(this,n)||this;return e.initContainer(),e.initDom(),e.initEvents(),e.initTimeline(),e}return Object(a.a)(n,t),n.prototype.getDefaultCfg=function(){var n=t.prototype.getDefaultCfg.call(this);return n.cursor="default",n.supportCSSTransform=!1,n},n.prototype.initContainer=function(){var t=this.get("container");Object(c.h)(t)&&(t=document.getElementById(t),this.set("container",t))},n.prototype.initDom=function(){var t=this.createDom();this.set("el",t),this.get("container").appendChild(t),this.setDOMSize(this.get("width"),this.get("height"))},n.prototype.initEvents=function(){var t=new kt({canvas:this});t.init(),this.set("eventController",t)},n.prototype.initTimeline=function(){var t=new At(this);this.set("timeline",t)},n.prototype.setDOMSize=function(t,n){var e=this.get("el");c.c&&(e.style.width=t+"px",e.style.height=n+"px")},n.prototype.changeSize=function(t,n){this.setDOMSize(t,n),this.set("width",t),this.set("height",n),this.onCanvasChange("changeSize")},n.prototype.getRenderer=function(){return this.get("renderer")},n.prototype.getCursor=function(){return this.get("cursor")},n.prototype.setCursor=function(t){this.set("cursor",t);var n=this.get("el");c.c&&n&&(n.style.cursor=t)},n.prototype.getPointByEvent=function(t){if(this.get("supportCSSTransform")){if(Et&&!Object(c.e)(t.layerX)&&t.layerX!==t.offsetX)return{x:t.layerX,y:t.layerY};if(!Object(c.e)(t.offsetX))return{x:t.offsetX,y:t.offsetY}}var n=this.getClientByEvent(t),e=n.x,r=n.y;return this.getPointByClient(e,r)},n.prototype.getClientByEvent=function(t){var n=t;return t.touches&&(n="touchend"===t.type?t.changedTouches[0]:t.touches[0]),{x:n.clientX,y:n.clientY}},n.prototype.getPointByClient=function(t,n){var e=this.get("el").getBoundingClientRect();return{x:t-e.left,y:n-e.top}},n.prototype.getClientByPoint=function(t,n){var e=this.get("el").getBoundingClientRect();return{x:t+e.left,y:n+e.top}},n.prototype.draw=function(){},n.prototype.removeDom=function(){var t=this.get("el");t.parentNode.removeChild(t)},n.prototype.clearEvents=function(){this.get("eventController").destroy()},n.prototype.isCanvas=function(){return!0},n.prototype.getParent=function(){return null},n.prototype.destroy=function(){var n=this.get("timeline");this.get("destroyed")||(this.clear(),n&&n.stop(),this.clearEvents(),this.removeDom(),t.prototype.destroy.call(this))},n}(u.a);n.a=Bt},function(t,n,e){"use strict";(function(t){e.d(n,"a",(function(){return l}));var r=function(t,n,e){if(e||2===arguments.length)for(var r,i=0,a=n.length;i<a;i++)!r&&i in n||(r||(r=Array.prototype.slice.call(n,0,i)),r[i]=n[i]);return t.concat(r||Array.prototype.slice.call(n))},i=function(t,n,e){this.name=t,this.version=n,this.os=e,this.type="browser"},a=function(n){this.version=n,this.type="node",this.name="node",this.os=t.platform},o=function(t,n,e,r){this.name=t,this.version=n,this.os=e,this.bot=r,this.type="bot-device"},u=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},c=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},s=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,f=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],h=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function l(n){return n?d(n):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new c:"undefined"!=typeof navigator?d(navigator.userAgent):void 0!==t&&t.version?new a(t.version.slice(1)):null}function p(t){return""!==t&&f.reduce((function(n,e){var r=e[0],i=e[1];if(n)return n;var a=i.exec(t);return!!a&&[r,a]}),!1)}function d(t){var n=p(t);if(!n)return null;var e=n[0],a=n[1];if("searchbot"===e)return new u;var c=a[1]&&a[1].split(".").join("_").split("_").slice(0,3);c?c.length<3&&(c=r(r([],c,!0),function(t){for(var n=[],e=0;e<t;e++)n.push("0");return n}(3-c.length),!0)):c=[];var f=c.join("."),l=function(t){for(var n=0,e=h.length;n<e;n++){var r=h[n],i=r[0];if(r[1].exec(t))return i}return null}(t),d=s.exec(t);return d&&d[1]?new o(e,f,l,d[1]):new i(e,f,l)}}).call(this,e(56))},,,,,,,,,,,,,,,function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.version=n.getArcParams=n.Shape=n.Group=n.Canvas=n.Region=n.IElement=void 0;var r=e(6),i=e(15);n.Shape=i,r.__exportStar(e(12),n);var a=e(70);Object.defineProperty(n,"IElement",{enumerable:!0,get:function(){return a.IElement}});var o=e(71);Object.defineProperty(n,"Region",{enumerable:!0,get:function(){return o.Region}});var u=e(72);Object.defineProperty(n,"Canvas",{enumerable:!0,get:function(){return u.default}});var c=e(25);Object.defineProperty(n,"Group",{enumerable:!0,get:function(){return c.default}});var s=e(33);Object.defineProperty(n,"getArcParams",{enumerable:!0,get:function(){return s.default}}),n.version="0.5.12"},function(t,n){var e,r,i=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function u(t){if(e===setTimeout)return setTimeout(t,0);if((e===a||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(n){try{return e.call(null,t,0)}catch(n){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:a}catch(t){e=a}try{r="function"==typeof clearTimeout?clearTimeout:o}catch(t){r=o}}();var c,s=[],f=!1,h=-1;function l(){f&&c&&(f=!1,c.length?s=c.concat(s):h=-1,s.length&&p())}function p(){if(!f){var t=u(l);f=!0;for(var n=s.length;n;){for(c=s,s=[];++h<n;)c&&c[h].run();h=-1,n=s.length}c=null,f=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(n){try{return r.call(null,t)}catch(n){return r.call(this,t)}}}(t)}}function d(t,n){this.fun=t,this.array=n}function v(){}i.nextTick=function(t){var n=new Array(arguments.length-1);if(arguments.length>1)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];s.push(new d(t,n)),1!==s.length||f||u(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(11),a=e(8),o=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,r:0})},n.prototype.isInStrokeOrPath=function(t,n,e,r,i){var o=this.attr(),u=o.x,c=o.y,s=o.r,f=i/2,h=(0,a.distance)(u,c,t,n);return r&&e?h<=s+f:r?h<=s:!!e&&(h>=s-f&&h<=s+f)},n.prototype.createPath=function(t){var n=this.attr(),e=n.x,r=n.y,i=n.r;t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()},n}(i.default);n.default=o},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6);function i(t,n,e,r){return t/(e*e)+n/(r*r)}var a=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,rx:0,ry:0})},n.prototype.isInStrokeOrPath=function(t,n,e,r,a){var o=this.attr(),u=a/2,c=o.x,s=o.y,f=o.rx,h=o.ry,l=(t-c)*(t-c),p=(n-s)*(n-s);return r&&e?i(l,p,f+u,h+u)<=1:r?i(l,p,f,h)<=1:!!e&&(i(l,p,f-u,h-u)>=1&&i(l,p,f+u,h+u)<=1)},n.prototype.createPath=function(t){var n=this.attr(),e=n.x,r=n.y,i=n.rx,a=n.ry;if(t.beginPath(),t.ellipse)t.ellipse(e,r,i,a,0,0,2*Math.PI,!1);else{var o=i>a?i:a,u=i>a?1:i/a,c=i>a?a/i:1;t.save(),t.translate(e,r),t.scale(u,c),t.arc(0,0,o,0,2*Math.PI),t.restore(),t.closePath()}},n}(e(11).default);n.default=a},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(11),a=e(8);function o(t){return t instanceof HTMLElement&&(0,a.isString)(t.nodeName)&&"CANVAS"===t.nodeName.toUpperCase()}var u=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,width:0,height:0})},n.prototype.initAttrs=function(t){this._setImage(t.img)},n.prototype.isStroke=function(){return!1},n.prototype.isOnlyHitBox=function(){return!0},n.prototype._afterLoading=function(){if(!0===this.get("toDraw")){var t=this.get("canvas");t?t.draw():this.createPath(this.get("context"))}},n.prototype._setImage=function(t){var n=this,e=this.attrs;if((0,a.isString)(t)){var r=new Image;r.onload=function(){if(n.destroyed)return!1;n.attr("img",r),n.set("loading",!1),n._afterLoading();var t=n.get("callback");t&&t.call(n)},r.crossOrigin="Anonymous",r.src=t,this.set("loading",!0)}else t instanceof Image?(e.width||(e.width=t.width),e.height||(e.height=t.height)):o(t)&&(e.width||(e.width=Number(t.getAttribute("width"))),e.height||(e.height,Number(t.getAttribute("height"))))},n.prototype.onAttrChange=function(n,e,r){t.prototype.onAttrChange.call(this,n,e,r),"img"===n&&this._setImage(e)},n.prototype.createPath=function(t){if(this.get("loading"))return this.set("toDraw",!0),void this.set("context",t);var n=this.attr(),e=n.x,r=n.y,i=n.width,u=n.height,c=n.sx,s=n.sy,f=n.swidth,h=n.sheight,l=n.img;(l instanceof Image||o(l))&&((0,a.isNil)(c)||(0,a.isNil)(s)||(0,a.isNil)(f)||(0,a.isNil)(h)?t.drawImage(l,e,r,i,u):t.drawImage(l,c,s,f,h,e,r,i,u))},n}(i.default);n.default=u},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(7),a=e(11),o=e(18),u=e(17),c=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x1:0,y1:0,x2:0,y2:0,startArrow:!1,endArrow:!1})},n.prototype.initAttrs=function(t){this.setArrow()},n.prototype.onAttrChange=function(n,e,r){t.prototype.onAttrChange.call(this,n,e,r),this.setArrow()},n.prototype.setArrow=function(){var t=this.attr(),n=t.x1,e=t.y1,r=t.x2,i=t.y2,a=t.startArrow,o=t.endArrow;a&&u.addStartArrow(this,t,r,i,n,e),o&&u.addEndArrow(this,t,n,e,r,i)},n.prototype.isInStrokeOrPath=function(t,n,e,r,i){if(!e||!i)return!1;var a=this.attr(),u=a.x1,c=a.y1,s=a.x2,f=a.y2;return(0,o.default)(u,c,s,f,i,t,n)},n.prototype.createPath=function(t){var n=this.attr(),e=n.x1,r=n.y1,i=n.x2,a=n.y2,o=n.startArrow,c=n.endArrow,s={dx:0,dy:0},f={dx:0,dy:0};o&&o.d&&(s=u.getShortenOffset(e,r,i,a,n.startArrow.d)),c&&c.d&&(f=u.getShortenOffset(e,r,i,a,n.endArrow.d)),t.beginPath(),t.moveTo(e+s.dx,r+s.dy),t.lineTo(i-f.dx,a-f.dy)},n.prototype.afterDrawPath=function(t){var n=this.get("startArrowShape"),e=this.get("endArrowShape");n&&n.draw(t),e&&e.draw(t)},n.prototype.getTotalLength=function(){var t=this.attr(),n=t.x1,e=t.y1,r=t.x2,a=t.y2;return i.Line.length(n,e,r,a)},n.prototype.getPoint=function(t){var n=this.attr(),e=n.x1,r=n.y1,a=n.x2,o=n.y2;return i.Line.pointAt(e,r,a,o,t)},n}(a.default);n.default=c},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(5),a=e(38),o=e(11),u=e(8),c=e(16),s={circle:function(t,n,e){return[["M",t-e,n],["A",e,e,0,1,0,t+e,n],["A",e,e,0,1,0,t-e,n]]},square:function(t,n,e){return[["M",t-e,n-e],["L",t+e,n-e],["L",t+e,n+e],["L",t-e,n+e],["Z"]]},diamond:function(t,n,e){return[["M",t-e,n],["L",t,n-e],["L",t+e,n],["L",t,n+e],["Z"]]},triangle:function(t,n,e){var r=e*Math.sin(1/3*Math.PI);return[["M",t-e,n+r],["L",t,n-r],["L",t+e,n+r],["Z"]]},"triangle-down":function(t,n,e){var r=e*Math.sin(1/3*Math.PI);return[["M",t-e,n-r],["L",t+e,n-r],["L",t,n+r],["Z"]]}},f=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.initAttrs=function(t){this._resetParamsCache()},n.prototype._resetParamsCache=function(){this.set("paramsCache",{})},n.prototype.onAttrChange=function(n,e,r){t.prototype.onAttrChange.call(this,n,e,r),-1!==["symbol","x","y","r","radius"].indexOf(n)&&this._resetParamsCache()},n.prototype.isOnlyHitBox=function(){return!0},n.prototype._getR=function(t){return(0,i.isNil)(t.r)?t.radius:t.r},n.prototype._getPath=function(){var t,e,r=this.attr(),i=r.x,o=r.y,c=r.symbol||"circle",s=this._getR(r);if((0,u.isFunction)(c))e=(t=c)(i,o,s),e=(0,a.path2Absolute)(e);else{if(!(t=n.Symbols[c]))return console.warn("".concat(c," marker is not supported.")),null;e=t(i,o,s)}return e},n.prototype.createPath=function(t){var n=this._getPath(),e=this.get("paramsCache");(0,c.drawPath)(this,t,{path:n},e)},n.Symbols=s,n}(o.default);n.default=f},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(7),a=e(5),o=e(11),u=e(38),c=e(16),s=e(34),f=e(35),h=e(63),l=e(17);function p(t,n,e){for(var r=!1,i=0;i<t.length;i++){var a=t[i];if(r=(0,f.default)(a,n,e))break}return r}var d=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{startArrow:!1,endArrow:!1})},n.prototype.initAttrs=function(t){this._setPathArr(t.path),this.setArrow()},n.prototype.onAttrChange=function(n,e,r){t.prototype.onAttrChange.call(this,n,e,r),"path"===n&&this._setPathArr(e),this.setArrow()},n.prototype._setPathArr=function(t){this.attrs.path=(0,u.path2Absolute)(t);var n=h.default.hasArc(t);this.set("hasArc",n),this.set("paramsCache",{}),this.set("segments",null),this.set("curve",null),this.set("tCache",null),this.set("totalLength",null)},n.prototype.getSegments=function(){var t=this.get("segements");return t||(t=(0,u.path2Segments)(this.attr("path")),this.set("segments",t)),t},n.prototype.setArrow=function(){var t=this.attr(),n=t.startArrow,e=t.endArrow;if(n){var r=this.getStartTangent();l.addStartArrow(this,t,r[0][0],r[0][1],r[1][0],r[1][1])}if(e){r=this.getEndTangent();l.addEndArrow(this,t,r[0][0],r[0][1],r[1][0],r[1][1])}},n.prototype.isInStrokeOrPath=function(t,n,e,r,i){var a=this.getSegments(),o=this.get("hasArc"),u=!1;if(e){var c=this.getTotalLength();u=h.default.isPointInStroke(a,i,t,n,c)}if(!u&&r)if(o)u=(0,s.default)(this,t,n);else{var f=this.attr("path"),l=h.default.extractPolygons(f);u=p(l.polygons,t,n)||p(l.polylines,t,n)}return u},n.prototype.createPath=function(t){var n=this.attr(),e=this.get("paramsCache");(0,c.drawPath)(this,t,n,e)},n.prototype.afterDrawPath=function(t){var n=this.get("startArrowShape"),e=this.get("endArrowShape");n&&n.draw(t),e&&e.draw(t)},n.prototype.getTotalLength=function(){var t=this.get("totalLength");return(0,a.isNil)(t)?(this._calculateCurve(),this._setTcache(),this.get("totalLength")):t},n.prototype.getPoint=function(t){var n,e,r=this.get("tCache");r||(this._calculateCurve(),this._setTcache(),r=this.get("tCache"));var o=this.get("curve");if(!r||0===r.length)return o?{x:o[0][1],y:o[0][2]}:null;(0,a.each)(r,(function(r,i){t>=r[0]&&t<=r[1]&&(n=(t-r[0])/(r[1]-r[0]),e=i)}));var u=o[e];if((0,a.isNil)(u)||(0,a.isNil)(e))return null;var c=u.length,s=o[e+1];return i.Cubic.pointAt(u[c-2],u[c-1],s[1],s[2],s[3],s[4],s[5],s[6],n)},n.prototype._calculateCurve=function(){var t=this.attr().path;this.set("curve",h.default.pathToCurve(t))},n.prototype._setTcache=function(){var t,n,e,r,o=0,u=0,c=[],s=this.get("curve");s&&((0,a.each)(s,(function(t,n){e=s[n+1],r=t.length,e&&(o+=i.Cubic.length(t[r-2],t[r-1],e[1],e[2],e[3],e[4],e[5],e[6])||0)})),this.set("totalLength",o),0!==o?((0,a.each)(s,(function(a,f){e=s[f+1],r=a.length,e&&((t=[])[0]=u/o,n=i.Cubic.length(a[r-2],a[r-1],e[1],e[2],e[3],e[4],e[5],e[6]),u+=n||0,t[1]=u/o,c.push(t))})),this.set("tCache",c)):this.set("tCache",[]))},n.prototype.getStartTangent=function(){var t,n=this.getSegments();if(n.length>1){var e=n[0].currentPoint,r=n[1].currentPoint,i=n[1].startTangent;t=[],i?(t.push([e[0]-i[0],e[1]-i[1]]),t.push([e[0],e[1]])):(t.push([r[0],r[1]]),t.push([e[0],e[1]]))}return t},n.prototype.getEndTangent=function(){var t,n=this.getSegments(),e=n.length;if(e>1){var r=n[e-2].currentPoint,i=n[e-1].currentPoint,a=n[e-1].endTangent;t=[],a?(t.push([i[0]-a[0],i[1]-a[1]]),t.push([i[0],i[1]])):(t.push([r[0],r[1]]),t.push([i[0],i[1]]))}return t},n}(o.default);n.default=d},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(12),a=e(7),o=e(7),u=e(74),c=e(26),s=e(8),f=e(18),h=e(36),l=u.ext.transform;n.default=r.__assign({hasArc:function(t){for(var n=!1,e=t.length,r=0;r<e;r++){var i=t[r][0];if("C"===i||"A"===i||"Q"===i){n=!0;break}}return n},extractPolygons:function(t){for(var n=t.length,e=[],r=[],i=[],a=0;a<n;a++){var o=t[a],u=o[0];"M"===u?(i.length&&(r.push(i),i=[]),i.push([o[1],o[2]])):"Z"===u?i.length&&(e.push(i),i=[]):i.push([o[1],o[2]])}return i.length>0&&r.push(i),{polygons:e,polylines:r}},isPointInStroke:function(t,n,e,r,i){for(var u=!1,p=n/2,d=0;d<t.length;d++){var v=t[d],g=v.currentPoint,y=v.params,m=v.prePoint,x=v.box;if(!x||(0,s.inBox)(x.x-p,x.y-p,x.width+n,x.height+n,e,r)){switch(v.command){case"L":case"Z":u=(0,f.default)(m[0],m[1],g[0],g[1],n,e,r);break;case"Q":u=a.Quad.pointDistance(m[0],m[1],y[1],y[2],y[3],y[4],e,r)<=n/2;break;case"C":u=o.Cubic.pointDistance(m[0],m[1],y[1],y[2],y[3],y[4],y[5],y[6],e,r,i)<=n/2;break;case"A":var b=v.arcParams,M=b.cx,w=b.cy,P=b.rx,_=b.ry,A=b.startAngle,O=b.endAngle,C=b.xRotation,S=[e,r,1],j=P>_?P:_,k=l(null,[["t",-M,-w],["r",-C],["s",1/(P>_?1:P/_),1/(P>_?_/P:1)]]);c.transformMat3(S,S,k),u=(0,h.default)(0,0,j,A,O,n,S[0],S[1])}if(u)break}}return u}},i.PathUtil)},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(11),a=e(37),o=e(35),u=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.isInStrokeOrPath=function(t,n,e,r,i){var u=this.attr().points,c=!1;return e&&(c=(0,a.default)(u,i,t,n,!0)),!c&&r&&(c=(0,o.default)(u,t,n)),c},n.prototype.createPath=function(t){var n=this.attr().points;if(!(n.length<2)){t.beginPath();for(var e=0;e<n.length;e++){var r=n[e];0===e?t.moveTo(r[0],r[1]):t.lineTo(r[0],r[1])}t.closePath()}},n}(i.default);n.default=u},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(7),a=e(7),o=e(5),u=e(11),c=e(37),s=e(17),f=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{startArrow:!1,endArrow:!1})},n.prototype.initAttrs=function(t){this.setArrow()},n.prototype.onAttrChange=function(n,e,r){t.prototype.onAttrChange.call(this,n,e,r),this.setArrow(),-1!==["points"].indexOf(n)&&this._resetCache()},n.prototype._resetCache=function(){this.set("totalLength",null),this.set("tCache",null)},n.prototype.setArrow=function(){var t=this.attr(),n=this.attrs,e=n.points,r=n.startArrow,i=n.endArrow,a=e.length,o=e[0][0],u=e[0][1],c=e[a-1][0],f=e[a-1][1];r&&s.addStartArrow(this,t,e[1][0],e[1][1],o,u),i&&s.addEndArrow(this,t,e[a-2][0],e[a-2][1],c,f)},n.prototype.isFill=function(){return!1},n.prototype.isInStrokeOrPath=function(t,n,e,r,i){if(!e||!i)return!1;var a=this.attr().points;return(0,c.default)(a,i,t,n,!1)},n.prototype.isStroke=function(){return!0},n.prototype.createPath=function(t){var n=this.attr(),e=n.points,r=n.startArrow,i=n.endArrow,a=e.length;if(!(e.length<2)){var o,u=e[0][0],c=e[0][1],f=e[a-1][0],h=e[a-1][1];if(r&&r.d)u+=(o=s.getShortenOffset(u,c,e[1][0],e[1][1],r.d)).dx,c+=o.dy;if(i&&i.d)f-=(o=s.getShortenOffset(e[a-2][0],e[a-2][1],f,h,i.d)).dx,h-=o.dy;t.beginPath(),t.moveTo(u,c);for(var l=0;l<a-1;l++){var p=e[l];t.lineTo(p[0],p[1])}t.lineTo(f,h)}},n.prototype.afterDrawPath=function(t){var n=this.get("startArrowShape"),e=this.get("endArrowShape");n&&n.draw(t),e&&e.draw(t)},n.prototype.getTotalLength=function(){var t=this.attr().points,n=this.get("totalLength");return(0,o.isNil)(n)?(this.set("totalLength",a.Polyline.length(t)),this.get("totalLength")):n},n.prototype.getPoint=function(t){var n,e,r=this.attr().points,a=this.get("tCache");return a||(this._setTcache(),a=this.get("tCache")),(0,o.each)(a,(function(r,i){t>=r[0]&&t<=r[1]&&(n=(t-r[0])/(r[1]-r[0]),e=i)})),i.Line.pointAt(r[e][0],r[e][1],r[e+1][0],r[e+1][1],n)},n.prototype._setTcache=function(){var t=this.attr().points;if(t&&0!==t.length){var n=this.getTotalLength();if(!(n<=0)){var e,r,a=0,u=[];(0,o.each)(t,(function(o,c){t[c+1]&&((e=[])[0]=a/n,r=i.Line.length(o[0],o[1],t[c+1][0],t[c+1][1]),a+=r,e[1]=a/n,u.push(e))})),this.set("tCache",u)}}},n.prototype.getStartTangent=function(){var t=this.attr().points,n=[];return n.push([t[1][0],t[1][1]]),n.push([t[0][0],t[0][1]]),n},n.prototype.getEndTangent=function(){var t=this.attr().points,n=t.length-1,e=[];return e.push([t[n-1][0],t[n-1][1]]),e.push([t[n][0],t[n][1]]),e},n}(u.default);n.default=f},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(11),a=e(32),o=e(8),u=e(67),c=e(68),s=e(34),f=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,width:0,height:0,radius:0})},n.prototype.isInStrokeOrPath=function(t,n,e,r,i){var a=this.attr(),f=a.x,h=a.y,l=a.width,p=a.height,d=a.radius;if(d){var v=!1;return e&&(v=(0,c.default)(f,h,l,p,d,i,t,n)),!v&&r&&(v=(0,s.default)(this,t,n)),v}var g=i/2;return r&&e?(0,o.inBox)(f-g,h-g,l+g,p+g,t,n):r?(0,o.inBox)(f,h,l,p,t,n):e?(0,u.default)(f,h,l,p,i,t,n):void 0},n.prototype.createPath=function(t){var n=this.attr(),e=n.x,r=n.y,i=n.width,o=n.height,u=n.radius;if(t.beginPath(),0===u)t.rect(e,r,i,o);else{var c=(0,a.parseRadius)(u),s=c[0],f=c[1],h=c[2],l=c[3];t.moveTo(e+s,r),t.lineTo(e+i-f,r),0!==f&&t.arc(e+i-f,r+f,f,-Math.PI/2,0),t.lineTo(e+i,r+o-h),0!==h&&t.arc(e+i-h,r+o-h,h,0,Math.PI/2),t.lineTo(e+l,r+o),0!==l&&t.arc(e+l,r+o-l,l,Math.PI/2,Math.PI),t.lineTo(e,r+s),0!==s&&t.arc(e+s,r+s,s,Math.PI,1.5*Math.PI),t.closePath()}},n}(i.default);n.default=f},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(8);n.default=function(t,n,e,i,a,o,u){var c=a/2;return(0,r.inBox)(t-c,n-c,e,a,o,u)||(0,r.inBox)(t+e-c,n-c,a,i,o,u)||(0,r.inBox)(t+c,n+i-c,e,a,o,u)||(0,r.inBox)(t-c,n+c,a,i,o,u)}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(18),i=e(36);n.default=function(t,n,e,a,o,u,c,s){return(0,r.default)(t+o,n,t+e-o,n,u,c,s)||(0,r.default)(t+e,n+o,t+e,n+a-o,u,c,s)||(0,r.default)(t+e-o,n+a,t+o,n+a,u,c,s)||(0,r.default)(t,n+a-o,t,n+o,u,c,s)||(0,i.default)(t+e-o,n+o,o,1.5*Math.PI,2*Math.PI,u,c,s)||(0,i.default)(t+e-o,n+a-o,o,0,.5*Math.PI,u,c,s)||(0,i.default)(t+o,n+a-o,o,.5*Math.PI,Math.PI,u,c,s)||(0,i.default)(t+o,n+o,o,Math.PI,1.5*Math.PI,u,c,s)}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(11),a=e(8),o=e(12),u=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,text:null,fontSize:12,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",textAlign:"start",textBaseline:"bottom"})},n.prototype.isOnlyHitBox=function(){return!0},n.prototype.initAttrs=function(t){this._assembleFont(),t.text&&this._setText(t.text)},n.prototype._assembleFont=function(){var t=this.attrs;t.font=(0,o.assembleFont)(t)},n.prototype._setText=function(t){var n=null;(0,a.isString)(t)&&-1!==t.indexOf("\n")&&(n=t.split("\n")),this.set("textArr",n)},n.prototype.onAttrChange=function(n,e,r){t.prototype.onAttrChange.call(this,n,e,r),n.startsWith("font")&&this._assembleFont(),"text"===n&&this._setText(e)},n.prototype._getSpaceingY=function(){var t=this.attrs,n=t.lineHeight,e=1*t.fontSize;return n?n-e:.14*e},n.prototype._drawTextArr=function(t,n,e){var r,i=this.attrs,u=i.textBaseline,c=i.x,s=i.y,f=1*i.fontSize,h=this._getSpaceingY(),l=(0,o.getTextHeight)(i.text,i.fontSize,i.lineHeight);(0,a.each)(n,(function(n,i){r=s+i*(h+f)-l+f,"middle"===u&&(r+=l-f-(l-f)/2),"top"===u&&(r+=l-f),(0,a.isNil)(n)||(e?t.fillText(n,c,r):t.strokeText(n,c,r))}))},n.prototype._drawText=function(t,n){var e=this.attr(),r=e.x,i=e.y,o=this.get("textArr");if(o)this._drawTextArr(t,o,n);else{var u=e.text;(0,a.isNil)(u)||(n?t.fillText(u,r,i):t.strokeText(u,r,i))}},n.prototype.strokeAndFill=function(t){var n=this.attrs,e=n.lineWidth,r=n.opacity,i=n.strokeOpacity,o=n.fillOpacity;this.isStroke()&&e>0&&((0,a.isNil)(i)||1===i||(t.globalAlpha=r),this.stroke(t)),this.isFill()&&((0,a.isNil)(o)||1===o?this.fill(t):(t.globalAlpha=o,this.fill(t),t.globalAlpha=r)),this.afterDrawPath(t)},n.prototype.fill=function(t){this._drawText(t,!0)},n.prototype.stroke=function(t){this._drawText(t,!1)},n}(i.default);n.default=u},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),e(6).__exportStar(e(12),n)},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),e(6).__exportStar(e(12),n)},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(6),i=e(12),a=e(73),o=e(15),u=e(25),c=e(8),s=e(16),f=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.getDefaultCfg=function(){var n=t.prototype.getDefaultCfg.call(this);return n.renderer="canvas",n.autoDraw=!0,n.localRefresh=!0,n.refreshElements=[],n.clipView=!0,n.quickHit=!1,n},n.prototype.onCanvasChange=function(t){"attr"!==t&&"sort"!==t&&"changeSize"!==t||(this.set("refreshElements",[this]),this.draw())},n.prototype.getShapeBase=function(){return o},n.prototype.getGroupBase=function(){return u.default},n.prototype.getPixelRatio=function(){var t=this.get("pixelRatio")||(0,c.getPixelRatio)();return t>=1?Math.ceil(t):1},n.prototype.getViewRange=function(){return{minX:0,minY:0,maxX:this.cfg.width,maxY:this.cfg.height}},n.prototype.createDom=function(){var t=document.createElement("canvas"),n=t.getContext("2d");return this.set("context",n),t},n.prototype.setDOMSize=function(n,e){t.prototype.setDOMSize.call(this,n,e);var r=this.get("context"),i=this.get("el"),a=this.getPixelRatio();i.width=a*n,i.height=a*e,a>1&&r.scale(a,a)},n.prototype.clear=function(){t.prototype.clear.call(this),this._clearFrame();var n=this.get("context"),e=this.get("el");n.clearRect(0,0,e.width,e.height)},n.prototype.getShape=function(n,e){return this.get("quickHit")?(0,a.getShape)(this,n,e):t.prototype.getShape.call(this,n,e,null)},n.prototype._getRefreshRegion=function(){var t,n=this.get("refreshElements"),e=this.getViewRange();n.length&&n[0]===this?t=e:(t=(0,s.getMergedRegion)(n))&&(t.minX=Math.floor(t.minX),t.minY=Math.floor(t.minY),t.maxX=Math.ceil(t.maxX),t.maxY=Math.ceil(t.maxY),t.maxY+=1,this.get("clipView")&&(t=(0,s.mergeView)(t,e)));return t},n.prototype.refreshElement=function(t){this.get("refreshElements").push(t)},n.prototype._clearFrame=function(){var t=this.get("drawFrame");t&&((0,c.clearAnimationFrame)(t),this.set("drawFrame",null),this.set("refreshElements",[]))},n.prototype.draw=function(){var t=this.get("drawFrame");this.get("autoDraw")&&t||this._startDraw()},n.prototype._drawAll=function(){var t=this.get("context"),n=this.get("el"),e=this.getChildren();t.clearRect(0,0,n.width,n.height),(0,s.applyAttrsToContext)(t,this),(0,s.drawChildren)(t,e),this.set("refreshElements",[])},n.prototype._drawRegion=function(){var t=this.get("context"),n=this.get("refreshElements"),e=this.getChildren(),r=this._getRefreshRegion();r?(t.clearRect(r.minX,r.minY,r.maxX-r.minX,r.maxY-r.minY),t.save(),t.beginPath(),t.rect(r.minX,r.minY,r.maxX-r.minX,r.maxY-r.minY),t.clip(),(0,s.applyAttrsToContext)(t,this),(0,s.checkRefresh)(this,e,r),(0,s.drawChildren)(t,e,r),t.restore()):n.length&&(0,s.clearChanged)(n),(0,c.each)(n,(function(t){t.get("hasChanged")&&t.set("hasChanged",!1)})),this.set("refreshElements",[])},n.prototype._startDraw=function(){var t=this,n=this.get("drawFrame"),e=this.get("drawFrameCallback");n||(n=(0,c.requestAnimationFrame)((function(){t.get("localRefresh")?t._drawRegion():t._drawAll(),t.set("drawFrame",null),e&&e()})),this.set("drawFrame",n))},n.prototype.skipDraw=function(){},n.prototype.removeDom=function(){var t=this.get("el");t.width=0,t.height=0,t.parentNode.removeChild(t)},n}(i.AbstractCanvas);n.default=f},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getShape=void 0;var r=e(12);function i(t,n,e){var i=t.getTotalMatrix();if(i){var a=function(t,n){if(n){var e=(0,r.invert)(n);return(0,r.multiplyVec2)(e,t)}return t}([n,e,1],i);return[a[0],a[1]]}return[n,e]}function a(t,n,e){if(t.isCanvas&&t.isCanvas())return!0;if(!(0,r.isAllowCapture)(t)||!1===t.cfg.isInView)return!1;if(t.cfg.clipShape){var a=i(t,n,e),o=a[0],u=a[1];if(t.isClipped(o,u))return!1}var c=t.cfg.cacheCanvasBBox||t.getCanvasBBox();return n>=c.minX&&n<=c.maxX&&e>=c.minY&&e<=c.maxY}n.getShape=function t(n,e,r){if(!a(n,e,r))return null;for(var o=null,u=n.getChildren(),c=u.length-1;c>=0;c--){var s=u[c];if(s.isGroup())o=t(s,e,r);else if(a(s,e,r)){var f=s,h=i(s,e,r),l=h[0],p=h[1];f.isInShape(l,p)&&(o=s)}if(o)break}return o}},function(t,n,e){"use strict";e.r(n),e.d(n,"mat3",(function(){return i.a})),e.d(n,"vec2",(function(){return i.b})),e.d(n,"vec3",(function(){return i.c})),e.d(n,"ext",(function(){return r}));var r={};e.r(r),e.d(r,"leftTranslate",(function(){return a})),e.d(r,"leftRotate",(function(){return o})),e.d(r,"leftScale",(function(){return u})),e.d(r,"transform",(function(){return c})),e.d(r,"direction",(function(){return s})),e.d(r,"angleTo",(function(){return f})),e.d(r,"vertical",(function(){return h}));var i=e(4);function a(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return i.a.fromTranslation(r,e),i.a.multiply(t,r,n)}function o(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return i.a.fromRotation(r,e),i.a.multiply(t,r,n)}function u(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return i.a.fromScaling(r,e),i.a.multiply(t,r,n)}function c(t,n){for(var e,r,c,s=t?[].concat(t):[1,0,0,0,1,0,0,0,1],f=0,h=n.length;f<h;f++){var l=n[f];switch(l[0]){case"t":a(s,s,[l[1],l[2]]);break;case"s":u(s,s,[l[1],l[2]]);break;case"r":o(s,s,l[1]);break;case"m":e=s,r=s,c=l[1],i.a.multiply(e,c,r)}}return s}function s(t,n){return t[0]*n[1]-n[0]*t[1]}function f(t,n,e){var r=i.b.angle(t,n),a=s(t,n)>=0;return e?a?2*Math.PI-r:r:a?r:2*Math.PI-r}function h(t,n,e){return e?(t[0]=n[1],t[1]=-1*n[0]):(t[0]=-1*n[1],t[1]=n[0]),t}}])}));
//# sourceMappingURL=g.min.js.map