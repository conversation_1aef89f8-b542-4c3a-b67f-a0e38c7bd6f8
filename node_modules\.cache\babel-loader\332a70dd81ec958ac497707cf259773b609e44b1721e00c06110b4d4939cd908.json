{"ast": null, "code": "import \"core-js/modules/es.array.is-array.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport * as util from '../util';\n\n/**\n *  Rule for validating minimum and maximum allowed values.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction range(rule, value, source, errors, options) {\n  var len = typeof rule.len === 'number';\n  var min = typeof rule.min === 'number';\n  var max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  var val = value;\n  var key = null;\n  var num = typeof value === 'number';\n  var str = typeof value === 'string';\n  var arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(util.format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(util.format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(util.format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(util.format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n  }\n}\nexport default range;", "map": {"version": 3, "names": ["util", "range", "rule", "value", "source", "errors", "options", "len", "min", "max", "spRegexp", "val", "key", "num", "str", "arr", "Array", "isArray", "length", "replace", "push", "format", "messages", "fullField"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/async-validator/es/rule/range.js"], "sourcesContent": ["import * as util from '../util';\n\n/**\n *  Rule for validating minimum and maximum allowed values.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction range(rule, value, source, errors, options) {\n  var len = typeof rule.len === 'number';\n  var min = typeof rule.min === 'number';\n  var max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  var val = value;\n  var key = null;\n  var num = typeof value === 'number';\n  var str = typeof value === 'string';\n  var arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(util.format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(util.format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(util.format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(util.format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n  }\n}\n\nexport default range;"], "mappings": ";;;;AAAA,OAAO,KAAKA,IAAI,MAAM,SAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACnD,IAAIC,GAAG,GAAG,OAAOL,IAAI,CAACK,GAAG,KAAK,QAAQ;EACtC,IAAIC,GAAG,GAAG,OAAON,IAAI,CAACM,GAAG,KAAK,QAAQ;EACtC,IAAIC,GAAG,GAAG,OAAOP,IAAI,CAACO,GAAG,KAAK,QAAQ;EACtC;EACA,IAAIC,QAAQ,GAAG,iCAAiC;EAChD,IAAIC,GAAG,GAAGR,KAAK;EACf,IAAIS,GAAG,GAAG,IAAI;EACd,IAAIC,GAAG,GAAG,OAAOV,KAAK,KAAK,QAAQ;EACnC,IAAIW,GAAG,GAAG,OAAOX,KAAK,KAAK,QAAQ;EACnC,IAAIY,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACd,KAAK,CAAC;EAC9B,IAAIU,GAAG,EAAE;IACPD,GAAG,GAAG,QAAQ;EAChB,CAAC,MAAM,IAAIE,GAAG,EAAE;IACdF,GAAG,GAAG,QAAQ;EAChB,CAAC,MAAM,IAAIG,GAAG,EAAE;IACdH,GAAG,GAAG,OAAO;EACf;EACA;EACA;EACA;EACA,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,KAAK;EACd;EACA,IAAIG,GAAG,EAAE;IACPJ,GAAG,GAAGR,KAAK,CAACe,MAAM;EACpB;EACA,IAAIJ,GAAG,EAAE;IACP;IACAH,GAAG,GAAGR,KAAK,CAACgB,OAAO,CAACT,QAAQ,EAAE,GAAG,CAAC,CAACQ,MAAM;EAC3C;EACA,IAAIX,GAAG,EAAE;IACP,IAAII,GAAG,KAAKT,IAAI,CAACK,GAAG,EAAE;MACpBF,MAAM,CAACe,IAAI,CAACpB,IAAI,CAACqB,MAAM,CAACf,OAAO,CAACgB,QAAQ,CAACV,GAAG,CAAC,CAACL,GAAG,EAAEL,IAAI,CAACqB,SAAS,EAAErB,IAAI,CAACK,GAAG,CAAC,CAAC;IAC/E;EACF,CAAC,MAAM,IAAIC,GAAG,IAAI,CAACC,GAAG,IAAIE,GAAG,GAAGT,IAAI,CAACM,GAAG,EAAE;IACxCH,MAAM,CAACe,IAAI,CAACpB,IAAI,CAACqB,MAAM,CAACf,OAAO,CAACgB,QAAQ,CAACV,GAAG,CAAC,CAACJ,GAAG,EAAEN,IAAI,CAACqB,SAAS,EAAErB,IAAI,CAACM,GAAG,CAAC,CAAC;EAC/E,CAAC,MAAM,IAAIC,GAAG,IAAI,CAACD,GAAG,IAAIG,GAAG,GAAGT,IAAI,CAACO,GAAG,EAAE;IACxCJ,MAAM,CAACe,IAAI,CAACpB,IAAI,CAACqB,MAAM,CAACf,OAAO,CAACgB,QAAQ,CAACV,GAAG,CAAC,CAACH,GAAG,EAAEP,IAAI,CAACqB,SAAS,EAAErB,IAAI,CAACO,GAAG,CAAC,CAAC;EAC/E,CAAC,MAAM,IAAID,GAAG,IAAIC,GAAG,KAAKE,GAAG,GAAGT,IAAI,CAACM,GAAG,IAAIG,GAAG,GAAGT,IAAI,CAACO,GAAG,CAAC,EAAE;IAC3DJ,MAAM,CAACe,IAAI,CAACpB,IAAI,CAACqB,MAAM,CAACf,OAAO,CAACgB,QAAQ,CAACV,GAAG,CAAC,CAACX,KAAK,EAAEC,IAAI,CAACqB,SAAS,EAAErB,IAAI,CAACM,GAAG,EAAEN,IAAI,CAACO,GAAG,CAAC,CAAC;EAC3F;AACF;AAEA,eAAeR,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}