/// <reference types="@webgpu/types" />
import type { InputLayout, InputLayoutDescriptor } from '../api';
import { ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class InputLayout_WebGPU extends ResourceBase_WebGPU implements InputLayout {
    type: ResourceType.InputLayout;
    buffers: GPUVertexBufferLayout[];
    indexFormat: GPUIndexFormat | undefined;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: InputLayoutDescriptor;
    });
}
