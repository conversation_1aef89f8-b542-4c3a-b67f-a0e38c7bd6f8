{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../../src/util/path.ts"], "names": [], "mappings": ";;;AAAA;;;GAGG;AACH,uCAAwC;AACxC,uCAAgD;AAChD,uCAAkD;AAClD,iDAAwC;AACxC,qCAAuC;AACvC,+BAA+B;AAC/B,yCAAsC;AACtC,uCAAoC;AAE5B,IAAA,SAAS,GAAK,iBAAG,UAAR,CAAS;AAE1B,SAAS,MAAM,CAAC,IAAI;IAClB,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,EAAE;YAC7C,MAAM,GAAG,IAAI,CAAC;YACd,MAAM;SACP;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;IACxD,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,IAAM,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpB,IAAA,YAAY,GAA4B,OAAO,aAAnC,EAAE,MAAM,GAAoB,OAAO,OAA3B,EAAE,QAAQ,GAAU,OAAO,SAAjB,EAAE,GAAG,GAAK,OAAO,IAAZ,CAAa;QACxD,0BAA0B;QAC1B,IAAI,GAAG,IAAI,CAAC,YAAK,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,GAAG,CAAC,KAAK,GAAG,SAAS,EAAE,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5G,SAAS;SACV;QACD,QAAQ,OAAO,CAAC,OAAO,EAAE;YACvB,sBAAsB;YACtB,KAAK,GAAG,CAAC;YACT,KAAK,GAAG;gBACN,KAAK,GAAG,cAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5F,MAAM;YACR,KAAK,GAAG;gBACN,IAAM,SAAS,GAAG,aAAQ,CAAC,aAAa,CACtC,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,CAAC,EACD,CAAC,CACF,CAAC;gBACF,KAAK,GAAG,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,GAAG;gBACN,IAAM,SAAS,GAAG,cAAS,CAAC,aAAa,CACvC,QAAQ,CAAC,CAAC,CAAC,EAAE,oBAAoB;gBACjC,QAAQ,CAAC,CAAC,CAAC,EACX,MAAM,CAAC,CAAC,CAAC,EAAE,2CAA2C;gBACtD,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,CAAC,EACD,CAAC,EACD,MAAM,CACP,CAAC;gBACF,KAAK,GAAG,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,GAAG;gBACN,sCAAsC;gBACtC,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;gBAC5B,IAAA,EAAE,GAAkD,SAAS,GAA3D,EAAE,EAAE,GAA8C,SAAS,GAAvD,EAAE,EAAE,GAA0C,SAAS,GAAnD,EAAE,EAAE,GAAsC,SAAS,GAA/C,EAAE,UAAU,GAA0B,SAAS,WAAnC,EAAE,QAAQ,GAAgB,SAAS,SAAzB,EAAE,SAAS,GAAK,SAAS,UAAd,CAAe;gBACtE,IAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB,IAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5B,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;gBACrC,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,IAAM,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE;oBACxB,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;oBACf,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC;oBACjB,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC;iBAC9B,CAAC,CAAC;gBACH,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5B,KAAK,GAAG,aAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,MAAM;YACR;gBACE,MAAM;SACT;QACD,IAAI,KAAK,EAAE;YACT,MAAM;SACP;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,IAAI;IAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAM,SAAS,GAAG,EAAE,CAAC;IACrB,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,gBAAgB;IACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,GAAG,KAAK,GAAG,EAAE;YACf,0BAA0B;YAC1B,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,+BAA+B;gBAC/B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,MAAM,GAAG,EAAE,CAAC,CAAC,QAAQ;aACtB;YACD,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;aAAM,IAAI,GAAG,KAAK,GAAG,EAAE;YACtB,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,MAAM;gBACN,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtB,MAAM,GAAG,EAAE,CAAC,CAAC,UAAU;aACxB;YACD,2BAA2B;SAC5B;aAAM;YACL,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;KACF;IACD,qCAAqC;IACrC,yBAAyB;IACzB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACrB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACxB;IACD,OAAO;QACL,QAAQ,UAAA;QACR,SAAS,WAAA;KACV,CAAC;AACJ,CAAC;AAED,qCACE,MAAM,QAAA;IACN,eAAe,iBAAA;IACf,eAAe,iBAAA,IACZ,iBAAQ,EACX"}