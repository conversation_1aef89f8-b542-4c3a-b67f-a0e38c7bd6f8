{"ast": null, "code": "import \"core-js/modules/es.array.for-each.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.sort.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// Universal transitions that can animate between any shapes(series) and any properties in any amounts.\nimport { SERIES_UNIVERSAL_TRANSITION_PROP } from '../model/Series.js';\nimport { createHashMap, each, map, filter, isArray, extend } from 'zrender/lib/core/util.js';\nimport { applyMorphAnimation, getPathList } from './morphTransitionHelper.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { initProps } from '../util/graphic.js';\nimport DataDiffer from '../data/DataDiffer.js';\nimport { makeInner, normalizeToArray } from '../util/model.js';\nimport { warn } from '../util/log.js';\nimport { getAnimationConfig, getOldStyle } from './basicTransition.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nvar DATA_COUNT_THRESHOLD = 1e4;\nvar TRANSITION_NONE = 0;\nvar TRANSITION_P2C = 1;\nvar TRANSITION_C2P = 2;\n;\nvar getUniversalTransitionGlobalStore = makeInner();\nfunction getDimension(data, visualDimension) {\n  var dimensions = data.dimensions;\n  for (var i = 0; i < dimensions.length; i++) {\n    var dimInfo = data.getDimensionInfo(dimensions[i]);\n    if (dimInfo && dimInfo.otherDims[visualDimension] === 0) {\n      return dimensions[i];\n    }\n  }\n}\n// get value by dimension. (only get value of itemGroupId or childGroupId, so convert it to string)\nfunction getValueByDimension(data, dataIndex, dimension) {\n  var dimInfo = data.getDimensionInfo(dimension);\n  var dimOrdinalMeta = dimInfo && dimInfo.ordinalMeta;\n  if (dimInfo) {\n    var value = data.get(dimInfo.name, dataIndex);\n    if (dimOrdinalMeta) {\n      return dimOrdinalMeta.categories[value] || value + '';\n    }\n    return value + '';\n  }\n}\nfunction getGroupId(data, dataIndex, dataGroupId, isChild) {\n  // try to get groupId from encode\n  var visualDimension = isChild ? 'itemChildGroupId' : 'itemGroupId';\n  var groupIdDim = getDimension(data, visualDimension);\n  if (groupIdDim) {\n    var groupId = getValueByDimension(data, dataIndex, groupIdDim);\n    return groupId;\n  }\n  // try to get groupId from raw data item\n  var rawDataItem = data.getRawDataItem(dataIndex);\n  var property = isChild ? 'childGroupId' : 'groupId';\n  if (rawDataItem && rawDataItem[property]) {\n    return rawDataItem[property] + '';\n  }\n  // fallback\n  if (isChild) {\n    return;\n  }\n  // try to use series.dataGroupId as groupId, otherwise use dataItem's id as groupId\n  return dataGroupId || data.getId(dataIndex);\n}\n// flatten all data items from different serieses into one arrary\nfunction flattenDataDiffItems(list) {\n  var items = [];\n  each(list, function (seriesInfo) {\n    var data = seriesInfo.data;\n    var dataGroupId = seriesInfo.dataGroupId;\n    if (data.count() > DATA_COUNT_THRESHOLD) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Universal transition is disabled on large data > 10k.');\n      }\n      return;\n    }\n    var indices = data.getIndices();\n    for (var dataIndex = 0; dataIndex < indices.length; dataIndex++) {\n      items.push({\n        data: data,\n        groupId: getGroupId(data, dataIndex, dataGroupId, false),\n        childGroupId: getGroupId(data, dataIndex, dataGroupId, true),\n        divide: seriesInfo.divide,\n        dataIndex: dataIndex\n      });\n    }\n  });\n  return items;\n}\nfunction fadeInElement(newEl, newSeries, newIndex) {\n  newEl.traverse(function (el) {\n    if (el instanceof Path) {\n      // TODO use fade in animation for target element.\n      initProps(el, {\n        style: {\n          opacity: 0\n        }\n      }, newSeries, {\n        dataIndex: newIndex,\n        isFrom: true\n      });\n    }\n  });\n}\nfunction removeEl(el) {\n  if (el.parent) {\n    // Bake parent transform to element.\n    // So it can still have proper transform to transition after it's removed.\n    var computedTransform = el.getComputedTransform();\n    el.setLocalTransform(computedTransform);\n    el.parent.remove(el);\n  }\n}\nfunction stopAnimation(el) {\n  el.stopAnimation();\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.stopAnimation();\n    });\n  }\n}\nfunction animateElementStyles(el, dataIndex, seriesModel) {\n  var animationConfig = getAnimationConfig('update', seriesModel, dataIndex);\n  animationConfig && el.traverse(function (child) {\n    if (child instanceof Displayable) {\n      var oldStyle = getOldStyle(child);\n      if (oldStyle) {\n        child.animateFrom({\n          style: oldStyle\n        }, animationConfig);\n      }\n    }\n  });\n}\nfunction isAllIdSame(oldDiffItems, newDiffItems) {\n  var len = oldDiffItems.length;\n  if (len !== newDiffItems.length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    var oldItem = oldDiffItems[i];\n    var newItem = newDiffItems[i];\n    if (oldItem.data.getId(oldItem.dataIndex) !== newItem.data.getId(newItem.dataIndex)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction transitionBetween(oldList, newList, api) {\n  var oldDiffItems = flattenDataDiffItems(oldList);\n  var newDiffItems = flattenDataDiffItems(newList);\n  function updateMorphingPathProps(from, to, rawFrom, rawTo, animationCfg) {\n    if (rawFrom || from) {\n      to.animateFrom({\n        style: rawFrom && rawFrom !== from\n        // dividingMethod like clone may override the style(opacity)\n        // So extend it to raw style.\n        ? extend(extend({}, rawFrom.style), from.style) : from.style\n      }, animationCfg);\n    }\n  }\n  var hasMorphAnimation = false;\n  /**\n   * With groupId and childGroupId, we can build parent-child relationships between dataItems.\n   * However, we should mind the parent-child \"direction\" between old and new options.\n   *\n   * For example, suppose we have two dataItems from two series.data:\n   *\n   * dataA: [                          dataB: [\n   *   {                                 {\n   *     value: 5,                         value: 3,\n   *     groupId: 'creatures',             groupId: 'animals',\n   *     childGroupId: 'animals'           childGroupId: 'dogs'\n   *   },                                },\n   *   ...                               ...\n   * ]                                 ]\n   *\n   * where dataA is belong to optionA and dataB is belong to optionB.\n   *\n   * When we `setOption(optionB)` from optionA, we choose childGroupId of dataItemA and groupId of\n   * dataItemB as keys so the two keys are matched (both are 'animals'), then universalTransition\n   * will work. This derection is \"parent -> child\".\n   *\n   * If we `setOption(optionA)` from optionB, we also choose groupId of dataItemB and childGroupId\n   * of dataItemA as keys and universalTransition will work. This derection is \"child -> parent\".\n   *\n   * If there is no childGroupId specified, which means no multiLevelDrillDown/Up is needed and no\n   * parent-child relationship exists. This direction is \"none\".\n   *\n   * So we need to know whether to use groupId or childGroupId as the key when we call the keyGetter\n   * functions. Thus, we need to decide the direction first.\n   *\n   * The rule is:\n   *\n   * if (all childGroupIds in oldDiffItems and all groupIds in newDiffItems have common value) {\n   *   direction = 'parent -> child';\n   * } else if (all groupIds in oldDiffItems and all childGroupIds in newDiffItems have common value) {\n   *   direction = 'child -> parent';\n   * } else {\n   *   direction = 'none';\n   * }\n   */\n  var direction = TRANSITION_NONE;\n  // find all groupIds and childGroupIds from oldDiffItems\n  var oldGroupIds = createHashMap();\n  var oldChildGroupIds = createHashMap();\n  oldDiffItems.forEach(function (item) {\n    item.groupId && oldGroupIds.set(item.groupId, true);\n    item.childGroupId && oldChildGroupIds.set(item.childGroupId, true);\n  });\n  // traverse newDiffItems and decide the direction according to the rule\n  for (var i = 0; i < newDiffItems.length; i++) {\n    var newGroupId = newDiffItems[i].groupId;\n    if (oldChildGroupIds.get(newGroupId)) {\n      direction = TRANSITION_P2C;\n      break;\n    }\n    var newChildGroupId = newDiffItems[i].childGroupId;\n    if (newChildGroupId && oldGroupIds.get(newChildGroupId)) {\n      direction = TRANSITION_C2P;\n      break;\n    }\n  }\n  function createKeyGetter(isOld, onlyGetId) {\n    return function (diffItem) {\n      var data = diffItem.data;\n      var dataIndex = diffItem.dataIndex;\n      // TODO if specified dim\n      if (onlyGetId) {\n        return data.getId(dataIndex);\n      }\n      if (isOld) {\n        return direction === TRANSITION_P2C ? diffItem.childGroupId : diffItem.groupId;\n      } else {\n        return direction === TRANSITION_C2P ? diffItem.childGroupId : diffItem.groupId;\n      }\n    };\n  }\n  // Use id if it's very likely to be an one to one animation\n  // It's more robust than groupId\n  // TODO Check if key dimension is specified.\n  var useId = isAllIdSame(oldDiffItems, newDiffItems);\n  var isElementStillInChart = {};\n  if (!useId) {\n    // We may have different diff strategy with basicTransition if we use other dimension as key.\n    // If so, we can't simply check if oldEl is same with newEl. We need a map to check if oldEl is still being used in the new chart.\n    // We can't use the elements that already being morphed. Let it keep it's original basic transition.\n    for (var i = 0; i < newDiffItems.length; i++) {\n      var newItem = newDiffItems[i];\n      var el = newItem.data.getItemGraphicEl(newItem.dataIndex);\n      if (el) {\n        isElementStillInChart[el.id] = true;\n      }\n    }\n  }\n  function updateOneToOne(newIndex, oldIndex) {\n    var oldItem = oldDiffItems[oldIndex];\n    var newItem = newDiffItems[newIndex];\n    var newSeries = newItem.data.hostModel;\n    // TODO Mark this elements is morphed and don't morph them anymore\n    var oldEl = oldItem.data.getItemGraphicEl(oldItem.dataIndex);\n    var newEl = newItem.data.getItemGraphicEl(newItem.dataIndex);\n    // Can't handle same elements.\n    if (oldEl === newEl) {\n      newEl && animateElementStyles(newEl, newItem.dataIndex, newSeries);\n      return;\n    }\n    if (\n    // We can't use the elements that already being morphed\n    oldEl && isElementStillInChart[oldEl.id]) {\n      return;\n    }\n    if (newEl) {\n      // TODO: If keep animating the group in case\n      // some of the elements don't want to be morphed.\n      // TODO Label?\n      stopAnimation(newEl);\n      if (oldEl) {\n        stopAnimation(oldEl);\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        removeEl(oldEl);\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldEl), getPathList(newEl), newItem.divide, newSeries, newIndex, updateMorphingPathProps);\n      } else {\n        fadeInElement(newEl, newSeries, newIndex);\n      }\n    }\n    // else keep oldEl leaving animation.\n  }\n  new DataDiffer(oldDiffItems, newDiffItems, createKeyGetter(true, useId), createKeyGetter(false, useId), null, 'multiple').update(updateOneToOne).updateManyToOne(function (newIndex, oldIndices) {\n    var newItem = newDiffItems[newIndex];\n    var newData = newItem.data;\n    var newSeries = newData.hostModel;\n    var newEl = newData.getItemGraphicEl(newItem.dataIndex);\n    var oldElsList = filter(map(oldIndices, function (idx) {\n      return oldDiffItems[idx].data.getItemGraphicEl(oldDiffItems[idx].dataIndex);\n    }), function (oldEl) {\n      return oldEl && oldEl !== newEl && !isElementStillInChart[oldEl.id];\n    });\n    if (newEl) {\n      stopAnimation(newEl);\n      if (oldElsList.length) {\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        each(oldElsList, function (oldEl) {\n          stopAnimation(oldEl);\n          removeEl(oldEl);\n        });\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldElsList), getPathList(newEl), newItem.divide, newSeries, newIndex, updateMorphingPathProps);\n      } else {\n        fadeInElement(newEl, newSeries, newItem.dataIndex);\n      }\n    }\n    // else keep oldEl leaving animation.\n  }).updateOneToMany(function (newIndices, oldIndex) {\n    var oldItem = oldDiffItems[oldIndex];\n    var oldEl = oldItem.data.getItemGraphicEl(oldItem.dataIndex);\n    // We can't use the elements that already being morphed\n    if (oldEl && isElementStillInChart[oldEl.id]) {\n      return;\n    }\n    var newElsList = filter(map(newIndices, function (idx) {\n      return newDiffItems[idx].data.getItemGraphicEl(newDiffItems[idx].dataIndex);\n    }), function (el) {\n      return el && el !== oldEl;\n    });\n    var newSeris = newDiffItems[newIndices[0]].data.hostModel;\n    if (newElsList.length) {\n      each(newElsList, function (newEl) {\n        return stopAnimation(newEl);\n      });\n      if (oldEl) {\n        stopAnimation(oldEl);\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        removeEl(oldEl);\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldEl), getPathList(newElsList), oldItem.divide,\n        // Use divide on old.\n        newSeris, newIndices[0], updateMorphingPathProps);\n      } else {\n        each(newElsList, function (newEl) {\n          return fadeInElement(newEl, newSeris, newIndices[0]);\n        });\n      }\n    }\n    // else keep oldEl leaving animation.\n  }).updateManyToMany(function (newIndices, oldIndices) {\n    // If two data are same and both have groupId.\n    // Normally they should be diff by id.\n    new DataDiffer(oldIndices, newIndices, function (rawIdx) {\n      return oldDiffItems[rawIdx].data.getId(oldDiffItems[rawIdx].dataIndex);\n    }, function (rawIdx) {\n      return newDiffItems[rawIdx].data.getId(newDiffItems[rawIdx].dataIndex);\n    }).update(function (newIndex, oldIndex) {\n      // Use the original index\n      updateOneToOne(newIndices[newIndex], oldIndices[oldIndex]);\n    }).execute();\n  }).execute();\n  if (hasMorphAnimation) {\n    each(newList, function (_a) {\n      var data = _a.data;\n      var seriesModel = data.hostModel;\n      var view = seriesModel && api.getViewOfSeriesModel(seriesModel);\n      var animationCfg = getAnimationConfig('update', seriesModel, 0); // use 0 index.\n      if (view && seriesModel.isAnimationEnabled() && animationCfg && animationCfg.duration > 0) {\n        view.group.traverse(function (el) {\n          if (el instanceof Path && !el.animators.length) {\n            // We can't accept there still exists element that has no animation\n            // if universalTransition is enabled\n            el.animateFrom({\n              style: {\n                opacity: 0\n              }\n            }, animationCfg);\n          }\n        });\n      }\n    });\n  }\n}\nfunction getSeriesTransitionKey(series) {\n  var seriesKey = series.getModel('universalTransition').get('seriesKey');\n  if (!seriesKey) {\n    // Use series id by default.\n    return series.id;\n  }\n  return seriesKey;\n}\nfunction convertArraySeriesKeyToString(seriesKey) {\n  if (isArray(seriesKey)) {\n    // Order independent.\n    return seriesKey.sort().join(',');\n  }\n  return seriesKey;\n}\nfunction getDivideShapeFromData(data) {\n  if (data.hostModel) {\n    return data.hostModel.getModel('universalTransition').get('divideShape');\n  }\n}\nfunction findTransitionSeriesBatches(globalStore, params) {\n  var updateBatches = createHashMap();\n  var oldDataMap = createHashMap();\n  // Map that only store key in array seriesKey.\n  // Which is used to query the old data when transition from one to multiple series.\n  var oldDataMapForSplit = createHashMap();\n  each(globalStore.oldSeries, function (series, idx) {\n    var oldDataGroupId = globalStore.oldDataGroupIds[idx];\n    var oldData = globalStore.oldData[idx];\n    var transitionKey = getSeriesTransitionKey(series);\n    var transitionKeyStr = convertArraySeriesKeyToString(transitionKey);\n    oldDataMap.set(transitionKeyStr, {\n      dataGroupId: oldDataGroupId,\n      data: oldData\n    });\n    if (isArray(transitionKey)) {\n      // Same key can't in different array seriesKey.\n      each(transitionKey, function (key) {\n        oldDataMapForSplit.set(key, {\n          key: transitionKeyStr,\n          dataGroupId: oldDataGroupId,\n          data: oldData\n        });\n      });\n    }\n  });\n  function checkTransitionSeriesKeyDuplicated(transitionKeyStr) {\n    if (updateBatches.get(transitionKeyStr)) {\n      warn(\"Duplicated seriesKey in universalTransition \" + transitionKeyStr);\n    }\n  }\n  each(params.updatedSeries, function (series) {\n    if (series.isUniversalTransitionEnabled() && series.isAnimationEnabled()) {\n      var newDataGroupId = series.get('dataGroupId');\n      var newData = series.getData();\n      var transitionKey = getSeriesTransitionKey(series);\n      var transitionKeyStr = convertArraySeriesKeyToString(transitionKey);\n      // Only transition between series with same id.\n      var oldData = oldDataMap.get(transitionKeyStr);\n      // string transition key is the best match.\n      if (oldData) {\n        if (process.env.NODE_ENV !== 'production') {\n          checkTransitionSeriesKeyDuplicated(transitionKeyStr);\n        }\n        // TODO check if data is same?\n        updateBatches.set(transitionKeyStr, {\n          oldSeries: [{\n            dataGroupId: oldData.dataGroupId,\n            divide: getDivideShapeFromData(oldData.data),\n            data: oldData.data\n          }],\n          newSeries: [{\n            dataGroupId: newDataGroupId,\n            divide: getDivideShapeFromData(newData),\n            data: newData\n          }]\n        });\n      } else {\n        // Transition from multiple series.\n        // e.g. 'female', 'male' -> ['female', 'male']\n        if (isArray(transitionKey)) {\n          if (process.env.NODE_ENV !== 'production') {\n            checkTransitionSeriesKeyDuplicated(transitionKeyStr);\n          }\n          var oldSeries_1 = [];\n          each(transitionKey, function (key) {\n            var oldData = oldDataMap.get(key);\n            if (oldData.data) {\n              oldSeries_1.push({\n                dataGroupId: oldData.dataGroupId,\n                divide: getDivideShapeFromData(oldData.data),\n                data: oldData.data\n              });\n            }\n          });\n          if (oldSeries_1.length) {\n            updateBatches.set(transitionKeyStr, {\n              oldSeries: oldSeries_1,\n              newSeries: [{\n                dataGroupId: newDataGroupId,\n                data: newData,\n                divide: getDivideShapeFromData(newData)\n              }]\n            });\n          }\n        } else {\n          // Try transition to multiple series.\n          // e.g. ['female', 'male'] -> 'female', 'male'\n          var oldData_1 = oldDataMapForSplit.get(transitionKey);\n          if (oldData_1) {\n            var batch = updateBatches.get(oldData_1.key);\n            if (!batch) {\n              batch = {\n                oldSeries: [{\n                  dataGroupId: oldData_1.dataGroupId,\n                  data: oldData_1.data,\n                  divide: getDivideShapeFromData(oldData_1.data)\n                }],\n                newSeries: []\n              };\n              updateBatches.set(oldData_1.key, batch);\n            }\n            batch.newSeries.push({\n              dataGroupId: newDataGroupId,\n              data: newData,\n              divide: getDivideShapeFromData(newData)\n            });\n          }\n        }\n      }\n    }\n  });\n  return updateBatches;\n}\nfunction querySeries(series, finder) {\n  for (var i = 0; i < series.length; i++) {\n    var found = finder.seriesIndex != null && finder.seriesIndex === series[i].seriesIndex || finder.seriesId != null && finder.seriesId === series[i].id;\n    if (found) {\n      return i;\n    }\n  }\n}\nfunction transitionSeriesFromOpt(transitionOpt, globalStore, params, api) {\n  var from = [];\n  var to = [];\n  each(normalizeToArray(transitionOpt.from), function (finder) {\n    var idx = querySeries(globalStore.oldSeries, finder);\n    if (idx >= 0) {\n      from.push({\n        dataGroupId: globalStore.oldDataGroupIds[idx],\n        data: globalStore.oldData[idx],\n        // TODO can specify divideShape in transition.\n        divide: getDivideShapeFromData(globalStore.oldData[idx]),\n        groupIdDim: finder.dimension\n      });\n    }\n  });\n  each(normalizeToArray(transitionOpt.to), function (finder) {\n    var idx = querySeries(params.updatedSeries, finder);\n    if (idx >= 0) {\n      var data = params.updatedSeries[idx].getData();\n      to.push({\n        dataGroupId: globalStore.oldDataGroupIds[idx],\n        data: data,\n        divide: getDivideShapeFromData(data),\n        groupIdDim: finder.dimension\n      });\n    }\n  });\n  if (from.length > 0 && to.length > 0) {\n    transitionBetween(from, to, api);\n  }\n}\nexport function installUniversalTransition(registers) {\n  registers.registerUpdateLifecycle('series:beforeupdate', function (ecMOdel, api, params) {\n    each(normalizeToArray(params.seriesTransition), function (transOpt) {\n      each(normalizeToArray(transOpt.to), function (finder) {\n        var series = params.updatedSeries;\n        for (var i = 0; i < series.length; i++) {\n          if (finder.seriesIndex != null && finder.seriesIndex === series[i].seriesIndex || finder.seriesId != null && finder.seriesId === series[i].id) {\n            series[i][SERIES_UNIVERSAL_TRANSITION_PROP] = true;\n          }\n        }\n      });\n    });\n  });\n  registers.registerUpdateLifecycle('series:transition', function (ecModel, api, params) {\n    // TODO api provide an namespace that can save stuff per instance\n    var globalStore = getUniversalTransitionGlobalStore(api);\n    // TODO multiple to multiple series.\n    if (globalStore.oldSeries && params.updatedSeries && params.optionChanged) {\n      // TODO transitionOpt was used in an old implementation and can be removed now\n      // Use give transition config if its' give;\n      var transitionOpt = params.seriesTransition;\n      if (transitionOpt) {\n        each(normalizeToArray(transitionOpt), function (opt) {\n          transitionSeriesFromOpt(opt, globalStore, params, api);\n        });\n      } else {\n        // Else guess from series based on transition series key.\n        var updateBatches_1 = findTransitionSeriesBatches(globalStore, params);\n        each(updateBatches_1.keys(), function (key) {\n          var batch = updateBatches_1.get(key);\n          transitionBetween(batch.oldSeries, batch.newSeries, api);\n        });\n      }\n      // Reset\n      each(params.updatedSeries, function (series) {\n        // Reset;\n        if (series[SERIES_UNIVERSAL_TRANSITION_PROP]) {\n          series[SERIES_UNIVERSAL_TRANSITION_PROP] = false;\n        }\n      });\n    }\n    // Save all series of current update. Not only the updated one.\n    var allSeries = ecModel.getSeries();\n    var savedSeries = globalStore.oldSeries = [];\n    var savedDataGroupIds = globalStore.oldDataGroupIds = [];\n    var savedData = globalStore.oldData = [];\n    for (var i = 0; i < allSeries.length; i++) {\n      var data = allSeries[i].getData();\n      // Only save the data that can have transition.\n      // Avoid large data costing too much extra memory\n      if (data.count() < DATA_COUNT_THRESHOLD) {\n        savedSeries.push(allSeries[i]);\n        savedDataGroupIds.push(allSeries[i].get('dataGroupId'));\n        savedData.push(data);\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["SERIES_UNIVERSAL_TRANSITION_PROP", "createHashMap", "each", "map", "filter", "isArray", "extend", "applyMorphAnimation", "getPathList", "Path", "initProps", "<PERSON><PERSON><PERSON><PERSON>", "makeInner", "normalizeToArray", "warn", "getAnimationConfig", "getOldStyle", "Displayable", "DATA_COUNT_THRESHOLD", "TRANSITION_NONE", "TRANSITION_P2C", "TRANSITION_C2P", "getUniversalTransitionGlobalStore", "getDimension", "data", "visualDimension", "dimensions", "i", "length", "dimInfo", "getDimensionInfo", "otherDims", "getValueByDimension", "dataIndex", "dimension", "dimOrdinalMeta", "ordinalMeta", "value", "get", "name", "categories", "getGroupId", "dataGroupId", "<PERSON><PERSON><PERSON><PERSON>", "groupIdDim", "groupId", "rawDataItem", "getRawDataItem", "property", "getId", "flattenDataDiffItems", "list", "items", "seriesInfo", "count", "process", "env", "NODE_ENV", "indices", "getIndices", "push", "childGroupId", "divide", "fadeInElement", "newEl", "newSeries", "newIndex", "traverse", "el", "style", "opacity", "isFrom", "removeEl", "parent", "computedTransform", "getComputedTransform", "setLocalTransform", "remove", "stopAnimation", "isGroup", "child", "animateElementStyles", "seriesModel", "animationConfig", "oldStyle", "animateFrom", "isAllIdSame", "oldDiffItems", "newDiffItems", "len", "oldItem", "newItem", "transitionBetween", "oldList", "newList", "api", "updateMorphingPathProps", "from", "to", "rawFrom", "rawTo", "animationCfg", "hasMorphAnimation", "direction", "oldGroupIds", "oldChildGroupIds", "for<PERSON>ach", "item", "set", "newGroupId", "newChildGroupId", "createKeyGetter", "isOld", "onlyGetId", "diffItem", "useId", "isElementStillInChart", "getItemGraphicEl", "id", "updateOneToOne", "oldIndex", "hostModel", "oldEl", "update", "updateManyToOne", "oldIndices", "newData", "oldElsList", "idx", "updateOneToMany", "newIndices", "newElsList", "newSeris", "updateManyToMany", "rawIdx", "execute", "_a", "view", "getViewOfSeriesModel", "isAnimationEnabled", "duration", "group", "animators", "getSeriesTransitionKey", "series", "series<PERSON>ey", "getModel", "convertArraySeriesKeyToString", "sort", "join", "getDivideShapeFromData", "findTransitionSeriesBatches", "globalStore", "params", "updateBatches", "oldDataMap", "oldDataMapForSplit", "oldSeries", "oldDataGroupId", "oldDataGroupIds", "oldData", "<PERSON><PERSON><PERSON>", "transitionKeyStr", "key", "checkTransitionSeriesKeyDuplicated", "updatedSeries", "isUniversalTransitionEnabled", "newDataGroupId", "getData", "oldSeries_1", "oldData_1", "batch", "querySeries", "finder", "found", "seriesIndex", "seriesId", "transitionSeriesFromOpt", "transitionOpt", "installUniversalTransition", "registers", "registerUpdateLifecycle", "ecMOdel", "seriesTransition", "transOpt", "ecModel", "optionChanged", "opt", "updateBatches_1", "keys", "allSeries", "getSeries", "savedSeries", "savedDataGroupIds", "savedData"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/animation/universalTransition.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// Universal transitions that can animate between any shapes(series) and any properties in any amounts.\nimport { SERIES_UNIVERSAL_TRANSITION_PROP } from '../model/Series.js';\nimport { createHashMap, each, map, filter, isArray, extend } from 'zrender/lib/core/util.js';\nimport { applyMorphAnimation, getPathList } from './morphTransitionHelper.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { initProps } from '../util/graphic.js';\nimport DataDiffer from '../data/DataDiffer.js';\nimport { makeInner, normalizeToArray } from '../util/model.js';\nimport { warn } from '../util/log.js';\nimport { getAnimationConfig, getOldStyle } from './basicTransition.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nvar DATA_COUNT_THRESHOLD = 1e4;\nvar TRANSITION_NONE = 0;\nvar TRANSITION_P2C = 1;\nvar TRANSITION_C2P = 2;\n;\nvar getUniversalTransitionGlobalStore = makeInner();\nfunction getDimension(data, visualDimension) {\n  var dimensions = data.dimensions;\n  for (var i = 0; i < dimensions.length; i++) {\n    var dimInfo = data.getDimensionInfo(dimensions[i]);\n    if (dimInfo && dimInfo.otherDims[visualDimension] === 0) {\n      return dimensions[i];\n    }\n  }\n}\n// get value by dimension. (only get value of itemGroupId or childGroupId, so convert it to string)\nfunction getValueByDimension(data, dataIndex, dimension) {\n  var dimInfo = data.getDimensionInfo(dimension);\n  var dimOrdinalMeta = dimInfo && dimInfo.ordinalMeta;\n  if (dimInfo) {\n    var value = data.get(dimInfo.name, dataIndex);\n    if (dimOrdinalMeta) {\n      return dimOrdinalMeta.categories[value] || value + '';\n    }\n    return value + '';\n  }\n}\nfunction getGroupId(data, dataIndex, dataGroupId, isChild) {\n  // try to get groupId from encode\n  var visualDimension = isChild ? 'itemChildGroupId' : 'itemGroupId';\n  var groupIdDim = getDimension(data, visualDimension);\n  if (groupIdDim) {\n    var groupId = getValueByDimension(data, dataIndex, groupIdDim);\n    return groupId;\n  }\n  // try to get groupId from raw data item\n  var rawDataItem = data.getRawDataItem(dataIndex);\n  var property = isChild ? 'childGroupId' : 'groupId';\n  if (rawDataItem && rawDataItem[property]) {\n    return rawDataItem[property] + '';\n  }\n  // fallback\n  if (isChild) {\n    return;\n  }\n  // try to use series.dataGroupId as groupId, otherwise use dataItem's id as groupId\n  return dataGroupId || data.getId(dataIndex);\n}\n// flatten all data items from different serieses into one arrary\nfunction flattenDataDiffItems(list) {\n  var items = [];\n  each(list, function (seriesInfo) {\n    var data = seriesInfo.data;\n    var dataGroupId = seriesInfo.dataGroupId;\n    if (data.count() > DATA_COUNT_THRESHOLD) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Universal transition is disabled on large data > 10k.');\n      }\n      return;\n    }\n    var indices = data.getIndices();\n    for (var dataIndex = 0; dataIndex < indices.length; dataIndex++) {\n      items.push({\n        data: data,\n        groupId: getGroupId(data, dataIndex, dataGroupId, false),\n        childGroupId: getGroupId(data, dataIndex, dataGroupId, true),\n        divide: seriesInfo.divide,\n        dataIndex: dataIndex\n      });\n    }\n  });\n  return items;\n}\nfunction fadeInElement(newEl, newSeries, newIndex) {\n  newEl.traverse(function (el) {\n    if (el instanceof Path) {\n      // TODO use fade in animation for target element.\n      initProps(el, {\n        style: {\n          opacity: 0\n        }\n      }, newSeries, {\n        dataIndex: newIndex,\n        isFrom: true\n      });\n    }\n  });\n}\nfunction removeEl(el) {\n  if (el.parent) {\n    // Bake parent transform to element.\n    // So it can still have proper transform to transition after it's removed.\n    var computedTransform = el.getComputedTransform();\n    el.setLocalTransform(computedTransform);\n    el.parent.remove(el);\n  }\n}\nfunction stopAnimation(el) {\n  el.stopAnimation();\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.stopAnimation();\n    });\n  }\n}\nfunction animateElementStyles(el, dataIndex, seriesModel) {\n  var animationConfig = getAnimationConfig('update', seriesModel, dataIndex);\n  animationConfig && el.traverse(function (child) {\n    if (child instanceof Displayable) {\n      var oldStyle = getOldStyle(child);\n      if (oldStyle) {\n        child.animateFrom({\n          style: oldStyle\n        }, animationConfig);\n      }\n    }\n  });\n}\nfunction isAllIdSame(oldDiffItems, newDiffItems) {\n  var len = oldDiffItems.length;\n  if (len !== newDiffItems.length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    var oldItem = oldDiffItems[i];\n    var newItem = newDiffItems[i];\n    if (oldItem.data.getId(oldItem.dataIndex) !== newItem.data.getId(newItem.dataIndex)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction transitionBetween(oldList, newList, api) {\n  var oldDiffItems = flattenDataDiffItems(oldList);\n  var newDiffItems = flattenDataDiffItems(newList);\n  function updateMorphingPathProps(from, to, rawFrom, rawTo, animationCfg) {\n    if (rawFrom || from) {\n      to.animateFrom({\n        style: rawFrom && rawFrom !== from\n        // dividingMethod like clone may override the style(opacity)\n        // So extend it to raw style.\n        ? extend(extend({}, rawFrom.style), from.style) : from.style\n      }, animationCfg);\n    }\n  }\n  var hasMorphAnimation = false;\n  /**\n   * With groupId and childGroupId, we can build parent-child relationships between dataItems.\n   * However, we should mind the parent-child \"direction\" between old and new options.\n   *\n   * For example, suppose we have two dataItems from two series.data:\n   *\n   * dataA: [                          dataB: [\n   *   {                                 {\n   *     value: 5,                         value: 3,\n   *     groupId: 'creatures',             groupId: 'animals',\n   *     childGroupId: 'animals'           childGroupId: 'dogs'\n   *   },                                },\n   *   ...                               ...\n   * ]                                 ]\n   *\n   * where dataA is belong to optionA and dataB is belong to optionB.\n   *\n   * When we `setOption(optionB)` from optionA, we choose childGroupId of dataItemA and groupId of\n   * dataItemB as keys so the two keys are matched (both are 'animals'), then universalTransition\n   * will work. This derection is \"parent -> child\".\n   *\n   * If we `setOption(optionA)` from optionB, we also choose groupId of dataItemB and childGroupId\n   * of dataItemA as keys and universalTransition will work. This derection is \"child -> parent\".\n   *\n   * If there is no childGroupId specified, which means no multiLevelDrillDown/Up is needed and no\n   * parent-child relationship exists. This direction is \"none\".\n   *\n   * So we need to know whether to use groupId or childGroupId as the key when we call the keyGetter\n   * functions. Thus, we need to decide the direction first.\n   *\n   * The rule is:\n   *\n   * if (all childGroupIds in oldDiffItems and all groupIds in newDiffItems have common value) {\n   *   direction = 'parent -> child';\n   * } else if (all groupIds in oldDiffItems and all childGroupIds in newDiffItems have common value) {\n   *   direction = 'child -> parent';\n   * } else {\n   *   direction = 'none';\n   * }\n   */\n  var direction = TRANSITION_NONE;\n  // find all groupIds and childGroupIds from oldDiffItems\n  var oldGroupIds = createHashMap();\n  var oldChildGroupIds = createHashMap();\n  oldDiffItems.forEach(function (item) {\n    item.groupId && oldGroupIds.set(item.groupId, true);\n    item.childGroupId && oldChildGroupIds.set(item.childGroupId, true);\n  });\n  // traverse newDiffItems and decide the direction according to the rule\n  for (var i = 0; i < newDiffItems.length; i++) {\n    var newGroupId = newDiffItems[i].groupId;\n    if (oldChildGroupIds.get(newGroupId)) {\n      direction = TRANSITION_P2C;\n      break;\n    }\n    var newChildGroupId = newDiffItems[i].childGroupId;\n    if (newChildGroupId && oldGroupIds.get(newChildGroupId)) {\n      direction = TRANSITION_C2P;\n      break;\n    }\n  }\n  function createKeyGetter(isOld, onlyGetId) {\n    return function (diffItem) {\n      var data = diffItem.data;\n      var dataIndex = diffItem.dataIndex;\n      // TODO if specified dim\n      if (onlyGetId) {\n        return data.getId(dataIndex);\n      }\n      if (isOld) {\n        return direction === TRANSITION_P2C ? diffItem.childGroupId : diffItem.groupId;\n      } else {\n        return direction === TRANSITION_C2P ? diffItem.childGroupId : diffItem.groupId;\n      }\n    };\n  }\n  // Use id if it's very likely to be an one to one animation\n  // It's more robust than groupId\n  // TODO Check if key dimension is specified.\n  var useId = isAllIdSame(oldDiffItems, newDiffItems);\n  var isElementStillInChart = {};\n  if (!useId) {\n    // We may have different diff strategy with basicTransition if we use other dimension as key.\n    // If so, we can't simply check if oldEl is same with newEl. We need a map to check if oldEl is still being used in the new chart.\n    // We can't use the elements that already being morphed. Let it keep it's original basic transition.\n    for (var i = 0; i < newDiffItems.length; i++) {\n      var newItem = newDiffItems[i];\n      var el = newItem.data.getItemGraphicEl(newItem.dataIndex);\n      if (el) {\n        isElementStillInChart[el.id] = true;\n      }\n    }\n  }\n  function updateOneToOne(newIndex, oldIndex) {\n    var oldItem = oldDiffItems[oldIndex];\n    var newItem = newDiffItems[newIndex];\n    var newSeries = newItem.data.hostModel;\n    // TODO Mark this elements is morphed and don't morph them anymore\n    var oldEl = oldItem.data.getItemGraphicEl(oldItem.dataIndex);\n    var newEl = newItem.data.getItemGraphicEl(newItem.dataIndex);\n    // Can't handle same elements.\n    if (oldEl === newEl) {\n      newEl && animateElementStyles(newEl, newItem.dataIndex, newSeries);\n      return;\n    }\n    if (\n    // We can't use the elements that already being morphed\n    oldEl && isElementStillInChart[oldEl.id]) {\n      return;\n    }\n    if (newEl) {\n      // TODO: If keep animating the group in case\n      // some of the elements don't want to be morphed.\n      // TODO Label?\n      stopAnimation(newEl);\n      if (oldEl) {\n        stopAnimation(oldEl);\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        removeEl(oldEl);\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldEl), getPathList(newEl), newItem.divide, newSeries, newIndex, updateMorphingPathProps);\n      } else {\n        fadeInElement(newEl, newSeries, newIndex);\n      }\n    }\n    // else keep oldEl leaving animation.\n  }\n\n  new DataDiffer(oldDiffItems, newDiffItems, createKeyGetter(true, useId), createKeyGetter(false, useId), null, 'multiple').update(updateOneToOne).updateManyToOne(function (newIndex, oldIndices) {\n    var newItem = newDiffItems[newIndex];\n    var newData = newItem.data;\n    var newSeries = newData.hostModel;\n    var newEl = newData.getItemGraphicEl(newItem.dataIndex);\n    var oldElsList = filter(map(oldIndices, function (idx) {\n      return oldDiffItems[idx].data.getItemGraphicEl(oldDiffItems[idx].dataIndex);\n    }), function (oldEl) {\n      return oldEl && oldEl !== newEl && !isElementStillInChart[oldEl.id];\n    });\n    if (newEl) {\n      stopAnimation(newEl);\n      if (oldElsList.length) {\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        each(oldElsList, function (oldEl) {\n          stopAnimation(oldEl);\n          removeEl(oldEl);\n        });\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldElsList), getPathList(newEl), newItem.divide, newSeries, newIndex, updateMorphingPathProps);\n      } else {\n        fadeInElement(newEl, newSeries, newItem.dataIndex);\n      }\n    }\n    // else keep oldEl leaving animation.\n  }).updateOneToMany(function (newIndices, oldIndex) {\n    var oldItem = oldDiffItems[oldIndex];\n    var oldEl = oldItem.data.getItemGraphicEl(oldItem.dataIndex);\n    // We can't use the elements that already being morphed\n    if (oldEl && isElementStillInChart[oldEl.id]) {\n      return;\n    }\n    var newElsList = filter(map(newIndices, function (idx) {\n      return newDiffItems[idx].data.getItemGraphicEl(newDiffItems[idx].dataIndex);\n    }), function (el) {\n      return el && el !== oldEl;\n    });\n    var newSeris = newDiffItems[newIndices[0]].data.hostModel;\n    if (newElsList.length) {\n      each(newElsList, function (newEl) {\n        return stopAnimation(newEl);\n      });\n      if (oldEl) {\n        stopAnimation(oldEl);\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        removeEl(oldEl);\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldEl), getPathList(newElsList), oldItem.divide,\n        // Use divide on old.\n        newSeris, newIndices[0], updateMorphingPathProps);\n      } else {\n        each(newElsList, function (newEl) {\n          return fadeInElement(newEl, newSeris, newIndices[0]);\n        });\n      }\n    }\n    // else keep oldEl leaving animation.\n  }).updateManyToMany(function (newIndices, oldIndices) {\n    // If two data are same and both have groupId.\n    // Normally they should be diff by id.\n    new DataDiffer(oldIndices, newIndices, function (rawIdx) {\n      return oldDiffItems[rawIdx].data.getId(oldDiffItems[rawIdx].dataIndex);\n    }, function (rawIdx) {\n      return newDiffItems[rawIdx].data.getId(newDiffItems[rawIdx].dataIndex);\n    }).update(function (newIndex, oldIndex) {\n      // Use the original index\n      updateOneToOne(newIndices[newIndex], oldIndices[oldIndex]);\n    }).execute();\n  }).execute();\n  if (hasMorphAnimation) {\n    each(newList, function (_a) {\n      var data = _a.data;\n      var seriesModel = data.hostModel;\n      var view = seriesModel && api.getViewOfSeriesModel(seriesModel);\n      var animationCfg = getAnimationConfig('update', seriesModel, 0); // use 0 index.\n      if (view && seriesModel.isAnimationEnabled() && animationCfg && animationCfg.duration > 0) {\n        view.group.traverse(function (el) {\n          if (el instanceof Path && !el.animators.length) {\n            // We can't accept there still exists element that has no animation\n            // if universalTransition is enabled\n            el.animateFrom({\n              style: {\n                opacity: 0\n              }\n            }, animationCfg);\n          }\n        });\n      }\n    });\n  }\n}\nfunction getSeriesTransitionKey(series) {\n  var seriesKey = series.getModel('universalTransition').get('seriesKey');\n  if (!seriesKey) {\n    // Use series id by default.\n    return series.id;\n  }\n  return seriesKey;\n}\nfunction convertArraySeriesKeyToString(seriesKey) {\n  if (isArray(seriesKey)) {\n    // Order independent.\n    return seriesKey.sort().join(',');\n  }\n  return seriesKey;\n}\nfunction getDivideShapeFromData(data) {\n  if (data.hostModel) {\n    return data.hostModel.getModel('universalTransition').get('divideShape');\n  }\n}\nfunction findTransitionSeriesBatches(globalStore, params) {\n  var updateBatches = createHashMap();\n  var oldDataMap = createHashMap();\n  // Map that only store key in array seriesKey.\n  // Which is used to query the old data when transition from one to multiple series.\n  var oldDataMapForSplit = createHashMap();\n  each(globalStore.oldSeries, function (series, idx) {\n    var oldDataGroupId = globalStore.oldDataGroupIds[idx];\n    var oldData = globalStore.oldData[idx];\n    var transitionKey = getSeriesTransitionKey(series);\n    var transitionKeyStr = convertArraySeriesKeyToString(transitionKey);\n    oldDataMap.set(transitionKeyStr, {\n      dataGroupId: oldDataGroupId,\n      data: oldData\n    });\n    if (isArray(transitionKey)) {\n      // Same key can't in different array seriesKey.\n      each(transitionKey, function (key) {\n        oldDataMapForSplit.set(key, {\n          key: transitionKeyStr,\n          dataGroupId: oldDataGroupId,\n          data: oldData\n        });\n      });\n    }\n  });\n  function checkTransitionSeriesKeyDuplicated(transitionKeyStr) {\n    if (updateBatches.get(transitionKeyStr)) {\n      warn(\"Duplicated seriesKey in universalTransition \" + transitionKeyStr);\n    }\n  }\n  each(params.updatedSeries, function (series) {\n    if (series.isUniversalTransitionEnabled() && series.isAnimationEnabled()) {\n      var newDataGroupId = series.get('dataGroupId');\n      var newData = series.getData();\n      var transitionKey = getSeriesTransitionKey(series);\n      var transitionKeyStr = convertArraySeriesKeyToString(transitionKey);\n      // Only transition between series with same id.\n      var oldData = oldDataMap.get(transitionKeyStr);\n      // string transition key is the best match.\n      if (oldData) {\n        if (process.env.NODE_ENV !== 'production') {\n          checkTransitionSeriesKeyDuplicated(transitionKeyStr);\n        }\n        // TODO check if data is same?\n        updateBatches.set(transitionKeyStr, {\n          oldSeries: [{\n            dataGroupId: oldData.dataGroupId,\n            divide: getDivideShapeFromData(oldData.data),\n            data: oldData.data\n          }],\n          newSeries: [{\n            dataGroupId: newDataGroupId,\n            divide: getDivideShapeFromData(newData),\n            data: newData\n          }]\n        });\n      } else {\n        // Transition from multiple series.\n        // e.g. 'female', 'male' -> ['female', 'male']\n        if (isArray(transitionKey)) {\n          if (process.env.NODE_ENV !== 'production') {\n            checkTransitionSeriesKeyDuplicated(transitionKeyStr);\n          }\n          var oldSeries_1 = [];\n          each(transitionKey, function (key) {\n            var oldData = oldDataMap.get(key);\n            if (oldData.data) {\n              oldSeries_1.push({\n                dataGroupId: oldData.dataGroupId,\n                divide: getDivideShapeFromData(oldData.data),\n                data: oldData.data\n              });\n            }\n          });\n          if (oldSeries_1.length) {\n            updateBatches.set(transitionKeyStr, {\n              oldSeries: oldSeries_1,\n              newSeries: [{\n                dataGroupId: newDataGroupId,\n                data: newData,\n                divide: getDivideShapeFromData(newData)\n              }]\n            });\n          }\n        } else {\n          // Try transition to multiple series.\n          // e.g. ['female', 'male'] -> 'female', 'male'\n          var oldData_1 = oldDataMapForSplit.get(transitionKey);\n          if (oldData_1) {\n            var batch = updateBatches.get(oldData_1.key);\n            if (!batch) {\n              batch = {\n                oldSeries: [{\n                  dataGroupId: oldData_1.dataGroupId,\n                  data: oldData_1.data,\n                  divide: getDivideShapeFromData(oldData_1.data)\n                }],\n                newSeries: []\n              };\n              updateBatches.set(oldData_1.key, batch);\n            }\n            batch.newSeries.push({\n              dataGroupId: newDataGroupId,\n              data: newData,\n              divide: getDivideShapeFromData(newData)\n            });\n          }\n        }\n      }\n    }\n  });\n  return updateBatches;\n}\nfunction querySeries(series, finder) {\n  for (var i = 0; i < series.length; i++) {\n    var found = finder.seriesIndex != null && finder.seriesIndex === series[i].seriesIndex || finder.seriesId != null && finder.seriesId === series[i].id;\n    if (found) {\n      return i;\n    }\n  }\n}\nfunction transitionSeriesFromOpt(transitionOpt, globalStore, params, api) {\n  var from = [];\n  var to = [];\n  each(normalizeToArray(transitionOpt.from), function (finder) {\n    var idx = querySeries(globalStore.oldSeries, finder);\n    if (idx >= 0) {\n      from.push({\n        dataGroupId: globalStore.oldDataGroupIds[idx],\n        data: globalStore.oldData[idx],\n        // TODO can specify divideShape in transition.\n        divide: getDivideShapeFromData(globalStore.oldData[idx]),\n        groupIdDim: finder.dimension\n      });\n    }\n  });\n  each(normalizeToArray(transitionOpt.to), function (finder) {\n    var idx = querySeries(params.updatedSeries, finder);\n    if (idx >= 0) {\n      var data = params.updatedSeries[idx].getData();\n      to.push({\n        dataGroupId: globalStore.oldDataGroupIds[idx],\n        data: data,\n        divide: getDivideShapeFromData(data),\n        groupIdDim: finder.dimension\n      });\n    }\n  });\n  if (from.length > 0 && to.length > 0) {\n    transitionBetween(from, to, api);\n  }\n}\nexport function installUniversalTransition(registers) {\n  registers.registerUpdateLifecycle('series:beforeupdate', function (ecMOdel, api, params) {\n    each(normalizeToArray(params.seriesTransition), function (transOpt) {\n      each(normalizeToArray(transOpt.to), function (finder) {\n        var series = params.updatedSeries;\n        for (var i = 0; i < series.length; i++) {\n          if (finder.seriesIndex != null && finder.seriesIndex === series[i].seriesIndex || finder.seriesId != null && finder.seriesId === series[i].id) {\n            series[i][SERIES_UNIVERSAL_TRANSITION_PROP] = true;\n          }\n        }\n      });\n    });\n  });\n  registers.registerUpdateLifecycle('series:transition', function (ecModel, api, params) {\n    // TODO api provide an namespace that can save stuff per instance\n    var globalStore = getUniversalTransitionGlobalStore(api);\n    // TODO multiple to multiple series.\n    if (globalStore.oldSeries && params.updatedSeries && params.optionChanged) {\n      // TODO transitionOpt was used in an old implementation and can be removed now\n      // Use give transition config if its' give;\n      var transitionOpt = params.seriesTransition;\n      if (transitionOpt) {\n        each(normalizeToArray(transitionOpt), function (opt) {\n          transitionSeriesFromOpt(opt, globalStore, params, api);\n        });\n      } else {\n        // Else guess from series based on transition series key.\n        var updateBatches_1 = findTransitionSeriesBatches(globalStore, params);\n        each(updateBatches_1.keys(), function (key) {\n          var batch = updateBatches_1.get(key);\n          transitionBetween(batch.oldSeries, batch.newSeries, api);\n        });\n      }\n      // Reset\n      each(params.updatedSeries, function (series) {\n        // Reset;\n        if (series[SERIES_UNIVERSAL_TRANSITION_PROP]) {\n          series[SERIES_UNIVERSAL_TRANSITION_PROP] = false;\n        }\n      });\n    }\n    // Save all series of current update. Not only the updated one.\n    var allSeries = ecModel.getSeries();\n    var savedSeries = globalStore.oldSeries = [];\n    var savedDataGroupIds = globalStore.oldDataGroupIds = [];\n    var savedData = globalStore.oldData = [];\n    for (var i = 0; i < allSeries.length; i++) {\n      var data = allSeries[i].getData();\n      // Only save the data that can have transition.\n      // Avoid large data costing too much extra memory\n      if (data.count() < DATA_COUNT_THRESHOLD) {\n        savedSeries.push(allSeries[i]);\n        savedDataGroupIds.push(allSeries[i].get('dataGroupId'));\n        savedData.push(data);\n      }\n    }\n  });\n}"], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gCAAgC,QAAQ,oBAAoB;AACrE,SAASC,aAAa,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,0BAA0B;AAC5F,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,4BAA4B;AAC7E,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,SAAS,EAAEC,gBAAgB,QAAQ,kBAAkB;AAC9D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,sBAAsB;AACtE,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,IAAIC,oBAAoB,GAAG,GAAG;AAC9B,IAAIC,eAAe,GAAG,CAAC;AACvB,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,cAAc,GAAG,CAAC;AACtB;AACA,IAAIC,iCAAiC,GAAGV,SAAS,CAAC,CAAC;AACnD,SAASW,YAAYA,CAACC,IAAI,EAAEC,eAAe,EAAE;EAC3C,IAAIC,UAAU,GAAGF,IAAI,CAACE,UAAU;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAIE,OAAO,GAAGL,IAAI,CAACM,gBAAgB,CAACJ,UAAU,CAACC,CAAC,CAAC,CAAC;IAClD,IAAIE,OAAO,IAAIA,OAAO,CAACE,SAAS,CAACN,eAAe,CAAC,KAAK,CAAC,EAAE;MACvD,OAAOC,UAAU,CAACC,CAAC,CAAC;IACtB;EACF;AACF;AACA;AACA,SAASK,mBAAmBA,CAACR,IAAI,EAAES,SAAS,EAAEC,SAAS,EAAE;EACvD,IAAIL,OAAO,GAAGL,IAAI,CAACM,gBAAgB,CAACI,SAAS,CAAC;EAC9C,IAAIC,cAAc,GAAGN,OAAO,IAAIA,OAAO,CAACO,WAAW;EACnD,IAAIP,OAAO,EAAE;IACX,IAAIQ,KAAK,GAAGb,IAAI,CAACc,GAAG,CAACT,OAAO,CAACU,IAAI,EAAEN,SAAS,CAAC;IAC7C,IAAIE,cAAc,EAAE;MAClB,OAAOA,cAAc,CAACK,UAAU,CAACH,KAAK,CAAC,IAAIA,KAAK,GAAG,EAAE;IACvD;IACA,OAAOA,KAAK,GAAG,EAAE;EACnB;AACF;AACA,SAASI,UAAUA,CAACjB,IAAI,EAAES,SAAS,EAAES,WAAW,EAAEC,OAAO,EAAE;EACzD;EACA,IAAIlB,eAAe,GAAGkB,OAAO,GAAG,kBAAkB,GAAG,aAAa;EAClE,IAAIC,UAAU,GAAGrB,YAAY,CAACC,IAAI,EAAEC,eAAe,CAAC;EACpD,IAAImB,UAAU,EAAE;IACd,IAAIC,OAAO,GAAGb,mBAAmB,CAACR,IAAI,EAAES,SAAS,EAAEW,UAAU,CAAC;IAC9D,OAAOC,OAAO;EAChB;EACA;EACA,IAAIC,WAAW,GAAGtB,IAAI,CAACuB,cAAc,CAACd,SAAS,CAAC;EAChD,IAAIe,QAAQ,GAAGL,OAAO,GAAG,cAAc,GAAG,SAAS;EACnD,IAAIG,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAAC,EAAE;IACxC,OAAOF,WAAW,CAACE,QAAQ,CAAC,GAAG,EAAE;EACnC;EACA;EACA,IAAIL,OAAO,EAAE;IACX;EACF;EACA;EACA,OAAOD,WAAW,IAAIlB,IAAI,CAACyB,KAAK,CAAChB,SAAS,CAAC;AAC7C;AACA;AACA,SAASiB,oBAAoBA,CAACC,IAAI,EAAE;EAClC,IAAIC,KAAK,GAAG,EAAE;EACdlD,IAAI,CAACiD,IAAI,EAAE,UAAUE,UAAU,EAAE;IAC/B,IAAI7B,IAAI,GAAG6B,UAAU,CAAC7B,IAAI;IAC1B,IAAIkB,WAAW,GAAGW,UAAU,CAACX,WAAW;IACxC,IAAIlB,IAAI,CAAC8B,KAAK,CAAC,CAAC,GAAGpC,oBAAoB,EAAE;MACvC,IAAIqC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC3C,IAAI,CAAC,uDAAuD,CAAC;MAC/D;MACA;IACF;IACA,IAAI4C,OAAO,GAAGlC,IAAI,CAACmC,UAAU,CAAC,CAAC;IAC/B,KAAK,IAAI1B,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGyB,OAAO,CAAC9B,MAAM,EAAEK,SAAS,EAAE,EAAE;MAC/DmB,KAAK,CAACQ,IAAI,CAAC;QACTpC,IAAI,EAAEA,IAAI;QACVqB,OAAO,EAAEJ,UAAU,CAACjB,IAAI,EAAES,SAAS,EAAES,WAAW,EAAE,KAAK,CAAC;QACxDmB,YAAY,EAAEpB,UAAU,CAACjB,IAAI,EAAES,SAAS,EAAES,WAAW,EAAE,IAAI,CAAC;QAC5DoB,MAAM,EAAET,UAAU,CAACS,MAAM;QACzB7B,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOmB,KAAK;AACd;AACA,SAASW,aAAaA,CAACC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EACjDF,KAAK,CAACG,QAAQ,CAAC,UAAUC,EAAE,EAAE;IAC3B,IAAIA,EAAE,YAAY3D,IAAI,EAAE;MACtB;MACAC,SAAS,CAAC0D,EAAE,EAAE;QACZC,KAAK,EAAE;UACLC,OAAO,EAAE;QACX;MACF,CAAC,EAAEL,SAAS,EAAE;QACZhC,SAAS,EAAEiC,QAAQ;QACnBK,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;AACA,SAASC,QAAQA,CAACJ,EAAE,EAAE;EACpB,IAAIA,EAAE,CAACK,MAAM,EAAE;IACb;IACA;IACA,IAAIC,iBAAiB,GAAGN,EAAE,CAACO,oBAAoB,CAAC,CAAC;IACjDP,EAAE,CAACQ,iBAAiB,CAACF,iBAAiB,CAAC;IACvCN,EAAE,CAACK,MAAM,CAACI,MAAM,CAACT,EAAE,CAAC;EACtB;AACF;AACA,SAASU,aAAaA,CAACV,EAAE,EAAE;EACzBA,EAAE,CAACU,aAAa,CAAC,CAAC;EAClB,IAAIV,EAAE,CAACW,OAAO,EAAE;IACdX,EAAE,CAACD,QAAQ,CAAC,UAAUa,KAAK,EAAE;MAC3BA,KAAK,CAACF,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ;AACF;AACA,SAASG,oBAAoBA,CAACb,EAAE,EAAEnC,SAAS,EAAEiD,WAAW,EAAE;EACxD,IAAIC,eAAe,GAAGpE,kBAAkB,CAAC,QAAQ,EAAEmE,WAAW,EAAEjD,SAAS,CAAC;EAC1EkD,eAAe,IAAIf,EAAE,CAACD,QAAQ,CAAC,UAAUa,KAAK,EAAE;IAC9C,IAAIA,KAAK,YAAY/D,WAAW,EAAE;MAChC,IAAImE,QAAQ,GAAGpE,WAAW,CAACgE,KAAK,CAAC;MACjC,IAAII,QAAQ,EAAE;QACZJ,KAAK,CAACK,WAAW,CAAC;UAChBhB,KAAK,EAAEe;QACT,CAAC,EAAED,eAAe,CAAC;MACrB;IACF;EACF,CAAC,CAAC;AACJ;AACA,SAASG,WAAWA,CAACC,YAAY,EAAEC,YAAY,EAAE;EAC/C,IAAIC,GAAG,GAAGF,YAAY,CAAC3D,MAAM;EAC7B,IAAI6D,GAAG,KAAKD,YAAY,CAAC5D,MAAM,EAAE;IAC/B,OAAO,KAAK;EACd;EACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,GAAG,EAAE9D,CAAC,EAAE,EAAE;IAC5B,IAAI+D,OAAO,GAAGH,YAAY,CAAC5D,CAAC,CAAC;IAC7B,IAAIgE,OAAO,GAAGH,YAAY,CAAC7D,CAAC,CAAC;IAC7B,IAAI+D,OAAO,CAAClE,IAAI,CAACyB,KAAK,CAACyC,OAAO,CAACzD,SAAS,CAAC,KAAK0D,OAAO,CAACnE,IAAI,CAACyB,KAAK,CAAC0C,OAAO,CAAC1D,SAAS,CAAC,EAAE;MACnF,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AACA,SAAS2D,iBAAiBA,CAACC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAChD,IAAIR,YAAY,GAAGrC,oBAAoB,CAAC2C,OAAO,CAAC;EAChD,IAAIL,YAAY,GAAGtC,oBAAoB,CAAC4C,OAAO,CAAC;EAChD,SAASE,uBAAuBA,CAACC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAE;IACvE,IAAIF,OAAO,IAAIF,IAAI,EAAE;MACnBC,EAAE,CAACb,WAAW,CAAC;QACbhB,KAAK,EAAE8B,OAAO,IAAIA,OAAO,KAAKF;QAC9B;QACA;QAAA,EACE3F,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE6F,OAAO,CAAC9B,KAAK,CAAC,EAAE4B,IAAI,CAAC5B,KAAK,CAAC,GAAG4B,IAAI,CAAC5B;MACzD,CAAC,EAAEgC,YAAY,CAAC;IAClB;EACF;EACA,IAAIC,iBAAiB,GAAG,KAAK;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,SAAS,GAAGpF,eAAe;EAC/B;EACA,IAAIqF,WAAW,GAAGvG,aAAa,CAAC,CAAC;EACjC,IAAIwG,gBAAgB,GAAGxG,aAAa,CAAC,CAAC;EACtCsF,YAAY,CAACmB,OAAO,CAAC,UAAUC,IAAI,EAAE;IACnCA,IAAI,CAAC9D,OAAO,IAAI2D,WAAW,CAACI,GAAG,CAACD,IAAI,CAAC9D,OAAO,EAAE,IAAI,CAAC;IACnD8D,IAAI,CAAC9C,YAAY,IAAI4C,gBAAgB,CAACG,GAAG,CAACD,IAAI,CAAC9C,YAAY,EAAE,IAAI,CAAC;EACpE,CAAC,CAAC;EACF;EACA,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,YAAY,CAAC5D,MAAM,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAIkF,UAAU,GAAGrB,YAAY,CAAC7D,CAAC,CAAC,CAACkB,OAAO;IACxC,IAAI4D,gBAAgB,CAACnE,GAAG,CAACuE,UAAU,CAAC,EAAE;MACpCN,SAAS,GAAGnF,cAAc;MAC1B;IACF;IACA,IAAI0F,eAAe,GAAGtB,YAAY,CAAC7D,CAAC,CAAC,CAACkC,YAAY;IAClD,IAAIiD,eAAe,IAAIN,WAAW,CAAClE,GAAG,CAACwE,eAAe,CAAC,EAAE;MACvDP,SAAS,GAAGlF,cAAc;MAC1B;IACF;EACF;EACA,SAAS0F,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAE;IACzC,OAAO,UAAUC,QAAQ,EAAE;MACzB,IAAI1F,IAAI,GAAG0F,QAAQ,CAAC1F,IAAI;MACxB,IAAIS,SAAS,GAAGiF,QAAQ,CAACjF,SAAS;MAClC;MACA,IAAIgF,SAAS,EAAE;QACb,OAAOzF,IAAI,CAACyB,KAAK,CAAChB,SAAS,CAAC;MAC9B;MACA,IAAI+E,KAAK,EAAE;QACT,OAAOT,SAAS,KAAKnF,cAAc,GAAG8F,QAAQ,CAACrD,YAAY,GAAGqD,QAAQ,CAACrE,OAAO;MAChF,CAAC,MAAM;QACL,OAAO0D,SAAS,KAAKlF,cAAc,GAAG6F,QAAQ,CAACrD,YAAY,GAAGqD,QAAQ,CAACrE,OAAO;MAChF;IACF,CAAC;EACH;EACA;EACA;EACA;EACA,IAAIsE,KAAK,GAAG7B,WAAW,CAACC,YAAY,EAAEC,YAAY,CAAC;EACnD,IAAI4B,qBAAqB,GAAG,CAAC,CAAC;EAC9B,IAAI,CAACD,KAAK,EAAE;IACV;IACA;IACA;IACA,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,YAAY,CAAC5D,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAIgE,OAAO,GAAGH,YAAY,CAAC7D,CAAC,CAAC;MAC7B,IAAIyC,EAAE,GAAGuB,OAAO,CAACnE,IAAI,CAAC6F,gBAAgB,CAAC1B,OAAO,CAAC1D,SAAS,CAAC;MACzD,IAAImC,EAAE,EAAE;QACNgD,qBAAqB,CAAChD,EAAE,CAACkD,EAAE,CAAC,GAAG,IAAI;MACrC;IACF;EACF;EACA,SAASC,cAAcA,CAACrD,QAAQ,EAAEsD,QAAQ,EAAE;IAC1C,IAAI9B,OAAO,GAAGH,YAAY,CAACiC,QAAQ,CAAC;IACpC,IAAI7B,OAAO,GAAGH,YAAY,CAACtB,QAAQ,CAAC;IACpC,IAAID,SAAS,GAAG0B,OAAO,CAACnE,IAAI,CAACiG,SAAS;IACtC;IACA,IAAIC,KAAK,GAAGhC,OAAO,CAAClE,IAAI,CAAC6F,gBAAgB,CAAC3B,OAAO,CAACzD,SAAS,CAAC;IAC5D,IAAI+B,KAAK,GAAG2B,OAAO,CAACnE,IAAI,CAAC6F,gBAAgB,CAAC1B,OAAO,CAAC1D,SAAS,CAAC;IAC5D;IACA,IAAIyF,KAAK,KAAK1D,KAAK,EAAE;MACnBA,KAAK,IAAIiB,oBAAoB,CAACjB,KAAK,EAAE2B,OAAO,CAAC1D,SAAS,EAAEgC,SAAS,CAAC;MAClE;IACF;IACA;IACA;IACAyD,KAAK,IAAIN,qBAAqB,CAACM,KAAK,CAACJ,EAAE,CAAC,EAAE;MACxC;IACF;IACA,IAAItD,KAAK,EAAE;MACT;MACA;MACA;MACAc,aAAa,CAACd,KAAK,CAAC;MACpB,IAAI0D,KAAK,EAAE;QACT5C,aAAa,CAAC4C,KAAK,CAAC;QACpB;QACAlD,QAAQ,CAACkD,KAAK,CAAC;QACfpB,iBAAiB,GAAG,IAAI;QACxB/F,mBAAmB,CAACC,WAAW,CAACkH,KAAK,CAAC,EAAElH,WAAW,CAACwD,KAAK,CAAC,EAAE2B,OAAO,CAAC7B,MAAM,EAAEG,SAAS,EAAEC,QAAQ,EAAE8B,uBAAuB,CAAC;MAC3H,CAAC,MAAM;QACLjC,aAAa,CAACC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,CAAC;MAC3C;IACF;IACA;EACF;EAEA,IAAIvD,UAAU,CAAC4E,YAAY,EAAEC,YAAY,EAAEuB,eAAe,CAAC,IAAI,EAAEI,KAAK,CAAC,EAAEJ,eAAe,CAAC,KAAK,EAAEI,KAAK,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAACQ,MAAM,CAACJ,cAAc,CAAC,CAACK,eAAe,CAAC,UAAU1D,QAAQ,EAAE2D,UAAU,EAAE;IAC/L,IAAIlC,OAAO,GAAGH,YAAY,CAACtB,QAAQ,CAAC;IACpC,IAAI4D,OAAO,GAAGnC,OAAO,CAACnE,IAAI;IAC1B,IAAIyC,SAAS,GAAG6D,OAAO,CAACL,SAAS;IACjC,IAAIzD,KAAK,GAAG8D,OAAO,CAACT,gBAAgB,CAAC1B,OAAO,CAAC1D,SAAS,CAAC;IACvD,IAAI8F,UAAU,GAAG3H,MAAM,CAACD,GAAG,CAAC0H,UAAU,EAAE,UAAUG,GAAG,EAAE;MACrD,OAAOzC,YAAY,CAACyC,GAAG,CAAC,CAACxG,IAAI,CAAC6F,gBAAgB,CAAC9B,YAAY,CAACyC,GAAG,CAAC,CAAC/F,SAAS,CAAC;IAC7E,CAAC,CAAC,EAAE,UAAUyF,KAAK,EAAE;MACnB,OAAOA,KAAK,IAAIA,KAAK,KAAK1D,KAAK,IAAI,CAACoD,qBAAqB,CAACM,KAAK,CAACJ,EAAE,CAAC;IACrE,CAAC,CAAC;IACF,IAAItD,KAAK,EAAE;MACTc,aAAa,CAACd,KAAK,CAAC;MACpB,IAAI+D,UAAU,CAACnG,MAAM,EAAE;QACrB;QACA1B,IAAI,CAAC6H,UAAU,EAAE,UAAUL,KAAK,EAAE;UAChC5C,aAAa,CAAC4C,KAAK,CAAC;UACpBlD,QAAQ,CAACkD,KAAK,CAAC;QACjB,CAAC,CAAC;QACFpB,iBAAiB,GAAG,IAAI;QACxB/F,mBAAmB,CAACC,WAAW,CAACuH,UAAU,CAAC,EAAEvH,WAAW,CAACwD,KAAK,CAAC,EAAE2B,OAAO,CAAC7B,MAAM,EAAEG,SAAS,EAAEC,QAAQ,EAAE8B,uBAAuB,CAAC;MAChI,CAAC,MAAM;QACLjC,aAAa,CAACC,KAAK,EAAEC,SAAS,EAAE0B,OAAO,CAAC1D,SAAS,CAAC;MACpD;IACF;IACA;EACF,CAAC,CAAC,CAACgG,eAAe,CAAC,UAAUC,UAAU,EAAEV,QAAQ,EAAE;IACjD,IAAI9B,OAAO,GAAGH,YAAY,CAACiC,QAAQ,CAAC;IACpC,IAAIE,KAAK,GAAGhC,OAAO,CAAClE,IAAI,CAAC6F,gBAAgB,CAAC3B,OAAO,CAACzD,SAAS,CAAC;IAC5D;IACA,IAAIyF,KAAK,IAAIN,qBAAqB,CAACM,KAAK,CAACJ,EAAE,CAAC,EAAE;MAC5C;IACF;IACA,IAAIa,UAAU,GAAG/H,MAAM,CAACD,GAAG,CAAC+H,UAAU,EAAE,UAAUF,GAAG,EAAE;MACrD,OAAOxC,YAAY,CAACwC,GAAG,CAAC,CAACxG,IAAI,CAAC6F,gBAAgB,CAAC7B,YAAY,CAACwC,GAAG,CAAC,CAAC/F,SAAS,CAAC;IAC7E,CAAC,CAAC,EAAE,UAAUmC,EAAE,EAAE;MAChB,OAAOA,EAAE,IAAIA,EAAE,KAAKsD,KAAK;IAC3B,CAAC,CAAC;IACF,IAAIU,QAAQ,GAAG5C,YAAY,CAAC0C,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC1G,IAAI,CAACiG,SAAS;IACzD,IAAIU,UAAU,CAACvG,MAAM,EAAE;MACrB1B,IAAI,CAACiI,UAAU,EAAE,UAAUnE,KAAK,EAAE;QAChC,OAAOc,aAAa,CAACd,KAAK,CAAC;MAC7B,CAAC,CAAC;MACF,IAAI0D,KAAK,EAAE;QACT5C,aAAa,CAAC4C,KAAK,CAAC;QACpB;QACAlD,QAAQ,CAACkD,KAAK,CAAC;QACfpB,iBAAiB,GAAG,IAAI;QACxB/F,mBAAmB,CAACC,WAAW,CAACkH,KAAK,CAAC,EAAElH,WAAW,CAAC2H,UAAU,CAAC,EAAEzC,OAAO,CAAC5B,MAAM;QAC/E;QACAsE,QAAQ,EAAEF,UAAU,CAAC,CAAC,CAAC,EAAElC,uBAAuB,CAAC;MACnD,CAAC,MAAM;QACL9F,IAAI,CAACiI,UAAU,EAAE,UAAUnE,KAAK,EAAE;UAChC,OAAOD,aAAa,CAACC,KAAK,EAAEoE,QAAQ,EAAEF,UAAU,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC;MACJ;IACF;IACA;EACF,CAAC,CAAC,CAACG,gBAAgB,CAAC,UAAUH,UAAU,EAAEL,UAAU,EAAE;IACpD;IACA;IACA,IAAIlH,UAAU,CAACkH,UAAU,EAAEK,UAAU,EAAE,UAAUI,MAAM,EAAE;MACvD,OAAO/C,YAAY,CAAC+C,MAAM,CAAC,CAAC9G,IAAI,CAACyB,KAAK,CAACsC,YAAY,CAAC+C,MAAM,CAAC,CAACrG,SAAS,CAAC;IACxE,CAAC,EAAE,UAAUqG,MAAM,EAAE;MACnB,OAAO9C,YAAY,CAAC8C,MAAM,CAAC,CAAC9G,IAAI,CAACyB,KAAK,CAACuC,YAAY,CAAC8C,MAAM,CAAC,CAACrG,SAAS,CAAC;IACxE,CAAC,CAAC,CAAC0F,MAAM,CAAC,UAAUzD,QAAQ,EAAEsD,QAAQ,EAAE;MACtC;MACAD,cAAc,CAACW,UAAU,CAAChE,QAAQ,CAAC,EAAE2D,UAAU,CAACL,QAAQ,CAAC,CAAC;IAC5D,CAAC,CAAC,CAACe,OAAO,CAAC,CAAC;EACd,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC;EACZ,IAAIjC,iBAAiB,EAAE;IACrBpG,IAAI,CAAC4F,OAAO,EAAE,UAAU0C,EAAE,EAAE;MAC1B,IAAIhH,IAAI,GAAGgH,EAAE,CAAChH,IAAI;MAClB,IAAI0D,WAAW,GAAG1D,IAAI,CAACiG,SAAS;MAChC,IAAIgB,IAAI,GAAGvD,WAAW,IAAIa,GAAG,CAAC2C,oBAAoB,CAACxD,WAAW,CAAC;MAC/D,IAAImB,YAAY,GAAGtF,kBAAkB,CAAC,QAAQ,EAAEmE,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;MACjE,IAAIuD,IAAI,IAAIvD,WAAW,CAACyD,kBAAkB,CAAC,CAAC,IAAItC,YAAY,IAAIA,YAAY,CAACuC,QAAQ,GAAG,CAAC,EAAE;QACzFH,IAAI,CAACI,KAAK,CAAC1E,QAAQ,CAAC,UAAUC,EAAE,EAAE;UAChC,IAAIA,EAAE,YAAY3D,IAAI,IAAI,CAAC2D,EAAE,CAAC0E,SAAS,CAAClH,MAAM,EAAE;YAC9C;YACA;YACAwC,EAAE,CAACiB,WAAW,CAAC;cACbhB,KAAK,EAAE;gBACLC,OAAO,EAAE;cACX;YACF,CAAC,EAAE+B,YAAY,CAAC;UAClB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;AACF;AACA,SAAS0C,sBAAsBA,CAACC,MAAM,EAAE;EACtC,IAAIC,SAAS,GAAGD,MAAM,CAACE,QAAQ,CAAC,qBAAqB,CAAC,CAAC5G,GAAG,CAAC,WAAW,CAAC;EACvE,IAAI,CAAC2G,SAAS,EAAE;IACd;IACA,OAAOD,MAAM,CAAC1B,EAAE;EAClB;EACA,OAAO2B,SAAS;AAClB;AACA,SAASE,6BAA6BA,CAACF,SAAS,EAAE;EAChD,IAAI5I,OAAO,CAAC4I,SAAS,CAAC,EAAE;IACtB;IACA,OAAOA,SAAS,CAACG,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACnC;EACA,OAAOJ,SAAS;AAClB;AACA,SAASK,sBAAsBA,CAAC9H,IAAI,EAAE;EACpC,IAAIA,IAAI,CAACiG,SAAS,EAAE;IAClB,OAAOjG,IAAI,CAACiG,SAAS,CAACyB,QAAQ,CAAC,qBAAqB,CAAC,CAAC5G,GAAG,CAAC,aAAa,CAAC;EAC1E;AACF;AACA,SAASiH,2BAA2BA,CAACC,WAAW,EAAEC,MAAM,EAAE;EACxD,IAAIC,aAAa,GAAGzJ,aAAa,CAAC,CAAC;EACnC,IAAI0J,UAAU,GAAG1J,aAAa,CAAC,CAAC;EAChC;EACA;EACA,IAAI2J,kBAAkB,GAAG3J,aAAa,CAAC,CAAC;EACxCC,IAAI,CAACsJ,WAAW,CAACK,SAAS,EAAE,UAAUb,MAAM,EAAEhB,GAAG,EAAE;IACjD,IAAI8B,cAAc,GAAGN,WAAW,CAACO,eAAe,CAAC/B,GAAG,CAAC;IACrD,IAAIgC,OAAO,GAAGR,WAAW,CAACQ,OAAO,CAAChC,GAAG,CAAC;IACtC,IAAIiC,aAAa,GAAGlB,sBAAsB,CAACC,MAAM,CAAC;IAClD,IAAIkB,gBAAgB,GAAGf,6BAA6B,CAACc,aAAa,CAAC;IACnEN,UAAU,CAAC/C,GAAG,CAACsD,gBAAgB,EAAE;MAC/BxH,WAAW,EAAEoH,cAAc;MAC3BtI,IAAI,EAAEwI;IACR,CAAC,CAAC;IACF,IAAI3J,OAAO,CAAC4J,aAAa,CAAC,EAAE;MAC1B;MACA/J,IAAI,CAAC+J,aAAa,EAAE,UAAUE,GAAG,EAAE;QACjCP,kBAAkB,CAAChD,GAAG,CAACuD,GAAG,EAAE;UAC1BA,GAAG,EAAED,gBAAgB;UACrBxH,WAAW,EAAEoH,cAAc;UAC3BtI,IAAI,EAAEwI;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,SAASI,kCAAkCA,CAACF,gBAAgB,EAAE;IAC5D,IAAIR,aAAa,CAACpH,GAAG,CAAC4H,gBAAgB,CAAC,EAAE;MACvCpJ,IAAI,CAAC,8CAA8C,GAAGoJ,gBAAgB,CAAC;IACzE;EACF;EACAhK,IAAI,CAACuJ,MAAM,CAACY,aAAa,EAAE,UAAUrB,MAAM,EAAE;IAC3C,IAAIA,MAAM,CAACsB,4BAA4B,CAAC,CAAC,IAAItB,MAAM,CAACL,kBAAkB,CAAC,CAAC,EAAE;MACxE,IAAI4B,cAAc,GAAGvB,MAAM,CAAC1G,GAAG,CAAC,aAAa,CAAC;MAC9C,IAAIwF,OAAO,GAAGkB,MAAM,CAACwB,OAAO,CAAC,CAAC;MAC9B,IAAIP,aAAa,GAAGlB,sBAAsB,CAACC,MAAM,CAAC;MAClD,IAAIkB,gBAAgB,GAAGf,6BAA6B,CAACc,aAAa,CAAC;MACnE;MACA,IAAID,OAAO,GAAGL,UAAU,CAACrH,GAAG,CAAC4H,gBAAgB,CAAC;MAC9C;MACA,IAAIF,OAAO,EAAE;QACX,IAAIzG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC2G,kCAAkC,CAACF,gBAAgB,CAAC;QACtD;QACA;QACAR,aAAa,CAAC9C,GAAG,CAACsD,gBAAgB,EAAE;UAClCL,SAAS,EAAE,CAAC;YACVnH,WAAW,EAAEsH,OAAO,CAACtH,WAAW;YAChCoB,MAAM,EAAEwF,sBAAsB,CAACU,OAAO,CAACxI,IAAI,CAAC;YAC5CA,IAAI,EAAEwI,OAAO,CAACxI;UAChB,CAAC,CAAC;UACFyC,SAAS,EAAE,CAAC;YACVvB,WAAW,EAAE6H,cAAc;YAC3BzG,MAAM,EAAEwF,sBAAsB,CAACxB,OAAO,CAAC;YACvCtG,IAAI,EAAEsG;UACR,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;QACA,IAAIzH,OAAO,CAAC4J,aAAa,CAAC,EAAE;UAC1B,IAAI1G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC2G,kCAAkC,CAACF,gBAAgB,CAAC;UACtD;UACA,IAAIO,WAAW,GAAG,EAAE;UACpBvK,IAAI,CAAC+J,aAAa,EAAE,UAAUE,GAAG,EAAE;YACjC,IAAIH,OAAO,GAAGL,UAAU,CAACrH,GAAG,CAAC6H,GAAG,CAAC;YACjC,IAAIH,OAAO,CAACxI,IAAI,EAAE;cAChBiJ,WAAW,CAAC7G,IAAI,CAAC;gBACflB,WAAW,EAAEsH,OAAO,CAACtH,WAAW;gBAChCoB,MAAM,EAAEwF,sBAAsB,CAACU,OAAO,CAACxI,IAAI,CAAC;gBAC5CA,IAAI,EAAEwI,OAAO,CAACxI;cAChB,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;UACF,IAAIiJ,WAAW,CAAC7I,MAAM,EAAE;YACtB8H,aAAa,CAAC9C,GAAG,CAACsD,gBAAgB,EAAE;cAClCL,SAAS,EAAEY,WAAW;cACtBxG,SAAS,EAAE,CAAC;gBACVvB,WAAW,EAAE6H,cAAc;gBAC3B/I,IAAI,EAAEsG,OAAO;gBACbhE,MAAM,EAAEwF,sBAAsB,CAACxB,OAAO;cACxC,CAAC;YACH,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA;UACA,IAAI4C,SAAS,GAAGd,kBAAkB,CAACtH,GAAG,CAAC2H,aAAa,CAAC;UACrD,IAAIS,SAAS,EAAE;YACb,IAAIC,KAAK,GAAGjB,aAAa,CAACpH,GAAG,CAACoI,SAAS,CAACP,GAAG,CAAC;YAC5C,IAAI,CAACQ,KAAK,EAAE;cACVA,KAAK,GAAG;gBACNd,SAAS,EAAE,CAAC;kBACVnH,WAAW,EAAEgI,SAAS,CAAChI,WAAW;kBAClClB,IAAI,EAAEkJ,SAAS,CAAClJ,IAAI;kBACpBsC,MAAM,EAAEwF,sBAAsB,CAACoB,SAAS,CAAClJ,IAAI;gBAC/C,CAAC,CAAC;gBACFyC,SAAS,EAAE;cACb,CAAC;cACDyF,aAAa,CAAC9C,GAAG,CAAC8D,SAAS,CAACP,GAAG,EAAEQ,KAAK,CAAC;YACzC;YACAA,KAAK,CAAC1G,SAAS,CAACL,IAAI,CAAC;cACnBlB,WAAW,EAAE6H,cAAc;cAC3B/I,IAAI,EAAEsG,OAAO;cACbhE,MAAM,EAAEwF,sBAAsB,CAACxB,OAAO;YACxC,CAAC,CAAC;UACJ;QACF;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAO4B,aAAa;AACtB;AACA,SAASkB,WAAWA,CAAC5B,MAAM,EAAE6B,MAAM,EAAE;EACnC,KAAK,IAAIlJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqH,MAAM,CAACpH,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAImJ,KAAK,GAAGD,MAAM,CAACE,WAAW,IAAI,IAAI,IAAIF,MAAM,CAACE,WAAW,KAAK/B,MAAM,CAACrH,CAAC,CAAC,CAACoJ,WAAW,IAAIF,MAAM,CAACG,QAAQ,IAAI,IAAI,IAAIH,MAAM,CAACG,QAAQ,KAAKhC,MAAM,CAACrH,CAAC,CAAC,CAAC2F,EAAE;IACrJ,IAAIwD,KAAK,EAAE;MACT,OAAOnJ,CAAC;IACV;EACF;AACF;AACA,SAASsJ,uBAAuBA,CAACC,aAAa,EAAE1B,WAAW,EAAEC,MAAM,EAAE1D,GAAG,EAAE;EACxE,IAAIE,IAAI,GAAG,EAAE;EACb,IAAIC,EAAE,GAAG,EAAE;EACXhG,IAAI,CAACW,gBAAgB,CAACqK,aAAa,CAACjF,IAAI,CAAC,EAAE,UAAU4E,MAAM,EAAE;IAC3D,IAAI7C,GAAG,GAAG4C,WAAW,CAACpB,WAAW,CAACK,SAAS,EAAEgB,MAAM,CAAC;IACpD,IAAI7C,GAAG,IAAI,CAAC,EAAE;MACZ/B,IAAI,CAACrC,IAAI,CAAC;QACRlB,WAAW,EAAE8G,WAAW,CAACO,eAAe,CAAC/B,GAAG,CAAC;QAC7CxG,IAAI,EAAEgI,WAAW,CAACQ,OAAO,CAAChC,GAAG,CAAC;QAC9B;QACAlE,MAAM,EAAEwF,sBAAsB,CAACE,WAAW,CAACQ,OAAO,CAAChC,GAAG,CAAC,CAAC;QACxDpF,UAAU,EAAEiI,MAAM,CAAC3I;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFhC,IAAI,CAACW,gBAAgB,CAACqK,aAAa,CAAChF,EAAE,CAAC,EAAE,UAAU2E,MAAM,EAAE;IACzD,IAAI7C,GAAG,GAAG4C,WAAW,CAACnB,MAAM,CAACY,aAAa,EAAEQ,MAAM,CAAC;IACnD,IAAI7C,GAAG,IAAI,CAAC,EAAE;MACZ,IAAIxG,IAAI,GAAGiI,MAAM,CAACY,aAAa,CAACrC,GAAG,CAAC,CAACwC,OAAO,CAAC,CAAC;MAC9CtE,EAAE,CAACtC,IAAI,CAAC;QACNlB,WAAW,EAAE8G,WAAW,CAACO,eAAe,CAAC/B,GAAG,CAAC;QAC7CxG,IAAI,EAAEA,IAAI;QACVsC,MAAM,EAAEwF,sBAAsB,CAAC9H,IAAI,CAAC;QACpCoB,UAAU,EAAEiI,MAAM,CAAC3I;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,IAAI+D,IAAI,CAACrE,MAAM,GAAG,CAAC,IAAIsE,EAAE,CAACtE,MAAM,GAAG,CAAC,EAAE;IACpCgE,iBAAiB,CAACK,IAAI,EAAEC,EAAE,EAAEH,GAAG,CAAC;EAClC;AACF;AACA,OAAO,SAASoF,0BAA0BA,CAACC,SAAS,EAAE;EACpDA,SAAS,CAACC,uBAAuB,CAAC,qBAAqB,EAAE,UAAUC,OAAO,EAAEvF,GAAG,EAAE0D,MAAM,EAAE;IACvFvJ,IAAI,CAACW,gBAAgB,CAAC4I,MAAM,CAAC8B,gBAAgB,CAAC,EAAE,UAAUC,QAAQ,EAAE;MAClEtL,IAAI,CAACW,gBAAgB,CAAC2K,QAAQ,CAACtF,EAAE,CAAC,EAAE,UAAU2E,MAAM,EAAE;QACpD,IAAI7B,MAAM,GAAGS,MAAM,CAACY,aAAa;QACjC,KAAK,IAAI1I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqH,MAAM,CAACpH,MAAM,EAAED,CAAC,EAAE,EAAE;UACtC,IAAIkJ,MAAM,CAACE,WAAW,IAAI,IAAI,IAAIF,MAAM,CAACE,WAAW,KAAK/B,MAAM,CAACrH,CAAC,CAAC,CAACoJ,WAAW,IAAIF,MAAM,CAACG,QAAQ,IAAI,IAAI,IAAIH,MAAM,CAACG,QAAQ,KAAKhC,MAAM,CAACrH,CAAC,CAAC,CAAC2F,EAAE,EAAE;YAC7I0B,MAAM,CAACrH,CAAC,CAAC,CAAC3B,gCAAgC,CAAC,GAAG,IAAI;UACpD;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACFoL,SAAS,CAACC,uBAAuB,CAAC,mBAAmB,EAAE,UAAUI,OAAO,EAAE1F,GAAG,EAAE0D,MAAM,EAAE;IACrF;IACA,IAAID,WAAW,GAAGlI,iCAAiC,CAACyE,GAAG,CAAC;IACxD;IACA,IAAIyD,WAAW,CAACK,SAAS,IAAIJ,MAAM,CAACY,aAAa,IAAIZ,MAAM,CAACiC,aAAa,EAAE;MACzE;MACA;MACA,IAAIR,aAAa,GAAGzB,MAAM,CAAC8B,gBAAgB;MAC3C,IAAIL,aAAa,EAAE;QACjBhL,IAAI,CAACW,gBAAgB,CAACqK,aAAa,CAAC,EAAE,UAAUS,GAAG,EAAE;UACnDV,uBAAuB,CAACU,GAAG,EAAEnC,WAAW,EAAEC,MAAM,EAAE1D,GAAG,CAAC;QACxD,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAI6F,eAAe,GAAGrC,2BAA2B,CAACC,WAAW,EAAEC,MAAM,CAAC;QACtEvJ,IAAI,CAAC0L,eAAe,CAACC,IAAI,CAAC,CAAC,EAAE,UAAU1B,GAAG,EAAE;UAC1C,IAAIQ,KAAK,GAAGiB,eAAe,CAACtJ,GAAG,CAAC6H,GAAG,CAAC;UACpCvE,iBAAiB,CAAC+E,KAAK,CAACd,SAAS,EAAEc,KAAK,CAAC1G,SAAS,EAAE8B,GAAG,CAAC;QAC1D,CAAC,CAAC;MACJ;MACA;MACA7F,IAAI,CAACuJ,MAAM,CAACY,aAAa,EAAE,UAAUrB,MAAM,EAAE;QAC3C;QACA,IAAIA,MAAM,CAAChJ,gCAAgC,CAAC,EAAE;UAC5CgJ,MAAM,CAAChJ,gCAAgC,CAAC,GAAG,KAAK;QAClD;MACF,CAAC,CAAC;IACJ;IACA;IACA,IAAI8L,SAAS,GAAGL,OAAO,CAACM,SAAS,CAAC,CAAC;IACnC,IAAIC,WAAW,GAAGxC,WAAW,CAACK,SAAS,GAAG,EAAE;IAC5C,IAAIoC,iBAAiB,GAAGzC,WAAW,CAACO,eAAe,GAAG,EAAE;IACxD,IAAImC,SAAS,GAAG1C,WAAW,CAACQ,OAAO,GAAG,EAAE;IACxC,KAAK,IAAIrI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmK,SAAS,CAAClK,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAIH,IAAI,GAAGsK,SAAS,CAACnK,CAAC,CAAC,CAAC6I,OAAO,CAAC,CAAC;MACjC;MACA;MACA,IAAIhJ,IAAI,CAAC8B,KAAK,CAAC,CAAC,GAAGpC,oBAAoB,EAAE;QACvC8K,WAAW,CAACpI,IAAI,CAACkI,SAAS,CAACnK,CAAC,CAAC,CAAC;QAC9BsK,iBAAiB,CAACrI,IAAI,CAACkI,SAAS,CAACnK,CAAC,CAAC,CAACW,GAAG,CAAC,aAAa,CAAC,CAAC;QACvD4J,SAAS,CAACtI,IAAI,CAACpC,IAAI,CAAC;MACtB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}