{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取登录日志列表\nexport function getLoginLogList(params) {\n  return request({\n    url: '/loginlog/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 清空登录日志\nexport function cleanLoginLog() {\n  return request({\n    url: '/loginlog/clean',\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["request", "getLoginLogList", "params", "url", "method", "cleanLoginLog"], "sources": ["G:/备份9/adminweb/src/api/log/login.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取登录日志列表\r\nexport function getLoginLogList(params) {\r\n  return request({\r\n    url: '/loginlog/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 清空登录日志\r\nexport function cleanLoginLog() {\r\n  return request({\r\n    url: '/loginlog/clean',\r\n    method: 'delete'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAE;EACtC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,aAAaA,CAAA,EAAG;EAC9B,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}