{"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../src/shape/text.ts"], "names": [], "mappings": "AAAA;;;GAGG;;AAEH,OAAO,SAAS,MAAM,QAAQ,CAAC;AAC/B,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC3D;IAAmB,wBAAS;IAA5B;;IAqJA,CAAC;IApJC,SAAS;IACT,8BAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6BACK,KAAK,KACR,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,IAAI,EAAE,IAAI,EACV,QAAQ,EAAE,EAAE,EACZ,UAAU,EAAE,YAAY,EACxB,SAAS,EAAE,QAAQ,EACnB,UAAU,EAAE,QAAQ,EACpB,WAAW,EAAE,QAAQ,EACrB,SAAS,EAAE,OAAO,EAClB,YAAY,EAAE,QAAQ,IACtB;IACJ,CAAC;IAED,iBAAiB;IACjB,2BAAY,GAAZ;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6BAA6B;IAC7B,wBAAS,GAAT,UAAU,KAAK;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,KAAK,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC3B;IACH,CAAC;IACD,OAAO;IACP,4BAAa,GAAb;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,eAAe;IACf,uBAAQ,GAAR,UAAS,IAAI;QACX,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/C,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,0BAA0B;IAC1B,2BAAY,GAAZ,UAAa,IAAY,EAAE,KAAU,EAAE,WAAgB;QACrD,iBAAM,YAAY,YAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YAC3B,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QACD,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACtB;IACH,CAAC;IAED,sCAAsC;IACtC,wBAAwB;IAExB,IAAI;IAEJ,kBAAkB;IAClB,4BAAa,GAAb;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACpC,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;QACpC,OAAO,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC9D,CAAC;IAED,eAAe;IACf,2BAAY,GAAZ,UAAa,OAAO,EAAE,OAAO,EAAE,MAAM;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACxC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAClB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAClB,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;QACpC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACvC,IAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC3E,IAAI,IAAI,CAAC;QACT,IAAI,CAAC,OAAO,EAAE,UAAC,OAAO,EAAE,KAAa;YACnC,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC,UAAU;YACzE,IAAI,YAAY,KAAK,QAAQ;gBAAE,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnF,IAAI,YAAY,KAAK,KAAK;gBAAE,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBACnB,IAAI,MAAM,EAAE;oBACV,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;iBACpC;qBAAM;oBACL,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;iBACtC;aACF;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB;IACnB,wBAAS,GAAT,UAAU,OAAO,EAAE,MAAM;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAClB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAClB,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAC7C;aAAM;YACL,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAChB,IAAI,MAAM,EAAE;oBACV,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC9B;qBAAM;oBACL,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBAChC;aACF;SACF;IACH,CAAC;IAED,gCAAgC;IAChC,4BAAa,GAAb,UAAc,OAAO;QACb,IAAA,KAAqD,IAAI,CAAC,KAAK,EAA7D,SAAS,eAAA,EAAE,OAAO,aAAA,EAAE,aAAa,mBAAA,EAAE,WAAW,iBAAe,CAAC;QAEtE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACnB,IAAI,SAAS,GAAG,CAAC,EAAE;gBACjB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE;oBAChD,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;iBAC/B;gBACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aACtB;SACF;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,WAAW,KAAK,CAAC,EAAE;gBAC5C,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;gBAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;aAC/B;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACpB;SACF;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,SAAS;IACT,mBAAI,GAAJ,UAAK,OAAO;QACV,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,YAAY;IACZ,qBAAM,GAAN,UAAO,OAAO;QACZ,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IACH,WAAC;AAAD,CAAC,AArJD,CAAmB,SAAS,GAqJ3B;AAED,eAAe,IAAI,CAAC"}