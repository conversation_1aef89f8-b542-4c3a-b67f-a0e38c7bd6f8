/// <reference types="@webgpu/types" />
import { <PERSON><PERSON><PERSON>, BufferDescriptor } from '../api';
import { BufferUsage, ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class Buffer_WebGPU extends ResourceBase_WebGPU implements Buffer {
    type: ResourceType.Buffer;
    /**
     * @see https://www.w3.org/TR/webgpu/#gpubuffer
     */
    gpuBuffer: GPUBuffer;
    /**
     * size in bytes
     */
    size: number;
    view: ArrayBufferView | null;
    usage: BufferUsage;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: BufferDescriptor;
    });
    setSubData(dstByteOffset: number, src: Uint8Array, srcByteOffset?: number, byteLength?: number): void;
    destroy(): void;
}
