{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取操作日志列表\nexport function getOperLogList(params) {\n  return request({\n    url: '/operlog/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 清空操作日志\nexport function cleanOperLog() {\n  return request({\n    url: '/operlog/clean',\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["request", "getOperLogList", "params", "url", "method", "cleanOperLog"], "sources": ["E:/新项目/整理6/adminweb/src/api/log/operation.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取操作日志列表\r\nexport function getOperLogList(params) {\r\n  return request({\r\n    url: '/operlog/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 清空操作日志\r\nexport function cleanOperLog() {\r\n  return request({\r\n    url: '/operlog/clean',\r\n    method: 'delete'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAE;EACrC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,YAAYA,CAAA,EAAG;EAC7B,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}