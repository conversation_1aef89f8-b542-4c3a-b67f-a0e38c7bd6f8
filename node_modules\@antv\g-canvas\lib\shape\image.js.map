{"version": 3, "file": "image.js", "sourceRoot": "", "sources": ["../../src/shape/image.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,+BAA+B;AAC/B,qCAA+C;AAC/C,SAAS,QAAQ,CAAC,GAAG;IACnB,OAAO,GAAG,YAAY,WAAW,IAAI,eAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC;AACzG,CAAC;AAED;IAAyB,sCAAS;IAAlC;;IAmHA,CAAC;IAlHC,oCAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6CACK,KAAK,KACR,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,KAAK,EAAE,CAAC,EACR,MAAM,EAAE,CAAC,IACT;IACJ,CAAC;IAED,8BAAS,GAAT,UAAU,KAAK;QACb,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED,mBAAmB;IACnB,6BAAQ,GAAR;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,iBAAiB;IACjB,+BAA+B;IAC/B,iCAAY,GAAZ;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kCAAa,GAAb;QACE,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;YAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,MAAM,EAAE;gBACV,aAAa;gBACb,MAAM,CAAC,IAAI,EAAE,CAAC;aACf;iBAAM;gBACL,kBAAkB;gBAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;aACtC;SACF;IACH,CAAC;IAED,8BAAS,GAAT,UAAU,GAAG;QAAb,iBA6CC;QA5CC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,eAAQ,CAAC,GAAG,CAAC,EAAE;YACjB,IAAM,OAAK,GAAG,IAAI,KAAK,EAAE,CAAC;YAC1B,OAAK,CAAC,MAAM,GAAG;gBACb,gBAAgB;gBAChB,IAAI,KAAI,CAAC,SAAS,EAAE;oBAClB,OAAO,KAAK,CAAC;iBACd;gBACD,wBAAwB;gBACxB,iCAAiC;gBACjC,2BAA2B;gBAC3B,iCAAiC;gBACjC,KAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAK,CAAC,CAAC;gBACxB,KAAI,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBAC3B,KAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAM,QAAQ,GAAG,KAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACtC,IAAI,QAAQ,EAAE;oBACZ,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;iBACrB;YACH,CAAC,CAAC;YACF,OAAO;YACP,OAAK,CAAC,WAAW,GAAG,WAAW,CAAC;YAEhC,OAAK,CAAC,GAAG,GAAG,GAAG,CAAC;YAChB,iBAAiB;YACjB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SAC3B;aAAM,IAAI,GAAG,YAAY,KAAK,EAAE;YAC/B,uBAAuB;YACvB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBAChB,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;aACzB;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACjB,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;aAC3B;SACF;aAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,kBAAkB;YAClB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBAChB,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;aACjD;YAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACjB,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;aAClD;SACF;IACH,CAAC;IAED,iCAAY,GAAZ,UAAa,IAAY,EAAE,KAAU,EAAE,WAAgB;QACrD,iBAAM,YAAY,YAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAC7C,qBAAqB;QACrB,IAAI,IAAI,KAAK,KAAK,EAAE;YAClB,wCAAwC;YACxC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACvB;IACH,CAAC;IAED,+BAAU,GAAV,UAAW,OAAiC;QAC1C,WAAW;QACX,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACvB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,UAAU;YACpC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC7B,OAAO;SACR;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAA,CAAC,GAAgD,KAAK,EAArD,EAAE,CAAC,GAA6C,KAAK,EAAlD,EAAE,KAAK,GAAsC,KAAK,MAA3C,EAAE,MAAM,GAA8B,KAAK,OAAnC,EAAE,EAAE,GAA0B,KAAK,GAA/B,EAAE,EAAE,GAAsB,KAAK,GAA3B,EAAE,MAAM,GAAc,KAAK,OAAnB,EAAE,OAAO,GAAK,KAAK,QAAV,CAAW;QAE/D,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QACtB,IAAI,GAAG,YAAY,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACzC,IAAI,CAAC,YAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAK,CAAC,OAAO,CAAC,EAAE;gBACjE,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aACtE;iBAAM;gBACL,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aAC7C;SACF;IACH,CAAC;IACH,iBAAC;AAAD,CAAC,AAnHD,CAAyB,cAAS,GAmHjC;AAED,kBAAe,UAAU,CAAC"}