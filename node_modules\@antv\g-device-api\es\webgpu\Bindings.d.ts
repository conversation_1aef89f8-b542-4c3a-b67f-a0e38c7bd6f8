/// <reference types="@webgpu/types" />
import type { Bindings, BindingsDescriptor } from '../api';
import { ResourceType } from '../api';
import type { BindGroupLayout, IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class Bindings_WebGPU extends ResourceBase_WebGPU implements Bindings {
    type: ResourceType.Bindings;
    gpuBindGroup: GPUBindGroup[];
    bindGroupLayout: BindGroupLayout;
    numUniformBuffers: number;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: BindingsDescriptor;
    });
}
