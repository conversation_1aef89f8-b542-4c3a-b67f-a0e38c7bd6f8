{"version": 3, "file": "draw.js", "sourceRoot": "", "sources": ["../../src/util/draw.ts"], "names": [], "mappings": ";;;AAAA,mCAAqD;AAGrD,iCAAqC;AACrC,2CAAwC;AACxC,+BAAoD;AACpD,yCAA2C;AAE3C,IAAM,eAAe,GAAG;IACtB,IAAI,EAAE,WAAW;IACjB,MAAM,EAAE,aAAa;IACrB,OAAO,EAAE,aAAa;CACvB,CAAC;AAEF,SAAgB,mBAAmB,CAAC,OAAiC,EAAE,OAAiB;IACtF,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAC7B,KAAK,IAAM,CAAC,IAAI,KAAK,EAAE;QACrB,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACjB,uBAAuB;QACvB,IAAM,MAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,MAAI,KAAK,QAAQ,IAAI,CAAC,EAAE;YAC1B,OAAO;YACP,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACvD;aAAM,IAAI,MAAI,KAAK,UAAU,IAAI,OAAO,CAAC,WAAW,EAAE;YACrD,2BAA2B;YAC3B,cAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,IAAI,MAAI,KAAK,aAAa,IAAI,MAAI,KAAK,WAAW,EAAE;gBAClD,yBAAyB;gBACzB,gCAAgC;gBAChC,CAAC,GAAG,kBAAU,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,MAAI,KAAK,aAAa,EAAE;gBACjC,gDAAgD;gBAChD,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC;aAC7B;YACD,OAAO,CAAC,MAAI,CAAC,GAAG,CAAC,CAAC;SACnB;KACF;AACH,CAAC;AAxBD,kDAwBC;AAED,SAAgB,YAAY,CAAC,OAAiC,EAAE,QAAoB,EAAE,MAAe;IACnG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAa,CAAC;QACtC,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;YACrB,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAC7B;aAAM;YACL,KAAK,CAAC,QAAQ,EAAE,CAAC;SAClB;KACF;AACH,CAAC;AATD,oCASC;AAED,qEAAqE;AACrE,SAAgB,YAAY,CAAC,MAAM,EAAE,QAAoB,EAAE,MAAc;IACvE,IAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACtD,kCAAkC;IAClC,WAAI,CAAC,eAAe,EAAE,UAAC,EAAE;QACvB,IAAI,EAAE,KAAK,MAAM,EAAE;YACjB,IAAI,QAAM,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,OAAO,QAAM,IAAI,QAAM,KAAK,MAAM,IAAI,CAAC,QAAM,CAAC,GAAG,CAAC,OAAO,EAAE;gBACzD,QAAM,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;gBAC1B,QAAM,GAAG,QAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC5B;SACF;IACH,CAAC,CAAC,CAAC;IACH,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;QACjC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;KACtC;SAAM;QACL,gBAAgB;QAChB,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;KACxC;AACH,CAAC;AAlBD,oCAkBC;AACD,iBAAiB;AACjB,SAAgB,oBAAoB,CAAC,QAAoB,EAAE,MAAc;IACvE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAa,CAAC;QACtC,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;YACrB,uCAAuC;YACvC,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE;gBACxB,qCAAqC;gBACrC,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;gBACzB,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;oBACnB,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;iBAChD;aACF;iBAAM,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;gBAC5B,4CAA4C;gBAC5C,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;oBACnB,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;iBAClD;aACF;iBAAM;gBACL,kDAAkD;gBAClD,IAAM,OAAO,GAAG,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACnD,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;gBAC5B,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;oBAC9B,2BAA2B;oBAC3B,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;iBAClD;aACF;SACF;KACF;AACH,CAAC;AA3BD,oDA2BC;AAED,sDAAsD;AACtD,mCAAmC;AACnC,SAAgB,YAAY,CAAC,QAAoB;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC;QAC1B,OAAO;QACP,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACjC,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC/B;KACF;AACH,CAAC;AATD,oCASC;AAED,iCAAiC;AACjC,SAAS,kBAAkB,CAAC,QAAoB,EAAE,MAAc;IAC9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAa,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;YACtB,SAAS;SACV;QACD,sBAAsB;QACtB,0CAA0C;QAC1C,qCAAqC;QACrC,cAAc;QACd,aAAa;QACb,2CAA2C;QAC3C,IAAI;QACJ,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;QACzB,8BAA8B;QAC9B,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;YACnB,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;SACnD;KACF;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAe,EAAE,MAAc;IAC1D,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC;IACvC,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,IAAI,oBAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC1E,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,UAAU;AACV,SAAgB,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc;IACpD,IAAA,IAAI,GAA2B,KAAK,KAAhC,EAAE,UAAU,GAAe,KAAK,WAApB,EAAE,QAAQ,GAAK,KAAK,SAAV,CAAW;IAC7C,IAAI,CAAC,IAAI,EAAE;QACT,OAAO;KACR;IACD,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO;IAClC,IAAI,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB;IAC9C,IAAI,QAAQ,GAAG;QACb,EAAE,EAAE,CAAC;QACL,EAAE,EAAE,CAAC;KACN,CAAC;IACF,OAAO,CAAC,SAAS,EAAE,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,IAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,EAAE;YACzC,IAAM,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YACxC,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;SACjH;aAAM,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,EAAE;YACpF,iDAAiD;YACjD,IAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7B,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACvB,IAAM,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtC,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC/G;SACF;aAAM,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,EAAE;YAC1D,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACnB,IAAM,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtC,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC/G;SACF;QAEO,IAAA,EAAE,GAAS,QAAQ,GAAjB,EAAE,EAAE,GAAK,QAAQ,GAAb,CAAc;QAC5B,uBAAuB;QACvB,QAAQ,OAAO,EAAE;YACf,KAAK,GAAG;gBACN,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/C,cAAc,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,GAAG;gBACN,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,GAAG;gBACN,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/E,MAAM;YACR,KAAK,GAAG;gBACN,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBAClG,MAAM;YACR,KAAK,GAAG,CAAC,CAAC;gBACR,IAAI,SAAS,SAAA,CAAC;gBACd,4BAA4B;gBAC5B,IAAI,cAAc,EAAE;oBAClB,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;oBAC9B,IAAI,CAAC,SAAS,EAAE;wBACd,SAAS,GAAG,oBAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;wBAC/C,cAAc,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;qBAC/B;iBACF;qBAAM;oBACL,SAAS,GAAG,oBAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;iBAChD;gBACO,IAAA,EAAE,GAA6D,SAAS,GAAtE,EAAE,EAAE,GAAyD,SAAS,GAAlE,EAAE,EAAE,GAAqD,SAAS,GAA9D,EAAE,EAAE,GAAiD,SAAS,GAA1D,EAAE,UAAU,GAAqC,SAAS,WAA9C,EAAE,QAAQ,GAA2B,SAAS,SAApC,EAAE,SAAS,GAAgB,SAAS,UAAzB,EAAE,SAAS,GAAK,SAAS,UAAd,CAAe;gBACjF,cAAc;gBACd,IAAI,OAAO,CAAC,OAAO,EAAE;oBACnB,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;iBACjF;qBAAM;oBACL,IAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC5B,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBACrC,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC1B,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAC1B,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAC9B,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;oBAC1D,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;oBACtC,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;oBAC3B,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;iBAC7B;gBACD,MAAM;aACP;YACD,KAAK,GAAG;gBACN,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,MAAM;YACR;gBACE,MAAM;SACT;QAED,wBAAwB;QACxB,IAAI,OAAO,KAAK,GAAG,EAAE;YACnB,YAAY,GAAG,cAAc,CAAC;SAC/B;aAAM;YACL,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;YAC1B,YAAY,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SACnD;KACF;AACH,CAAC;AA7FD,4BA6FC;AAED,yBAAyB;AACzB,SAAgB,cAAc,CAAC,OAAO,EAAE,UAAU;IAChD,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrC,oBAAoB;IACpB,IAAI,MAAM,EAAE;QACV,IAAI,UAAU,KAAK,QAAQ,EAAE;YAC3B,gCAAgC;YAChC,6BAA6B;YAC7B,gBAAgB;YAChB,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;SAC3D;QACD,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YAC9B,iCAAiC;YACjC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAEhC,2BAA2B;YAC3B,oCAAoC;YACpC,wDAAwD;YACxD,IAAI;YACJ,sEAAsE;YACtE,gDAAgD;YAChD,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE;gBACjE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;gBACnD,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;oBAC1B,MAAM,CAAC,IAAI,EAAE,CAAC;iBACf;aACF;SACF;KACF;AACH,CAAC;AA7BD,wCA6BC;AAED,SAAgB,gBAAgB,CAAC,OAAO;IACtC,IAAI,MAAM,CAAC;IACX,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACtB,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAChD,IAAM,UAAU,GAAG,QAAQ,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrE,IAAM,IAAI,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QACrC,IAAM,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;QACxD,uCAAuC;QACvC,IAAI,UAAU,IAAI,SAAS,EAAE;YAC3B,MAAM,GAAG,kBAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACtC;aAAM,IAAI,UAAU,EAAE;YACrB,MAAM,GAAG,QAAQ,CAAC;SACnB;aAAM,IAAI,SAAS,EAAE;YACpB,MAAM,GAAG,IAAI,CAAC;SACf;KACF;SAAM;QACL,wBAAwB;QACxB,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;KACtC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AApBD,4CAoBC;AAED,SAAgB,eAAe,CAAC,QAAQ;IACtC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACpB,OAAO,IAAI,CAAC;KACb;IACD,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,WAAI,CAAC,QAAQ,EAAE,UAAC,EAAY;QAC1B,IAAM,MAAM,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;QACpC,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC3B;IACH,CAAC,CAAC,CAAC;IACH,OAAO;QACL,IAAI,EAAE,UAAG,CAAC,OAAO,CAAC;QAClB,IAAI,EAAE,UAAG,CAAC,OAAO,CAAC;QAClB,IAAI,EAAE,UAAG,CAAC,OAAO,CAAC;QAClB,IAAI,EAAE,UAAG,CAAC,OAAO,CAAC;KACnB,CAAC;AACJ,CAAC;AAvBD,0CAuBC;AAED,SAAgB,SAAS,CAAC,MAAM,EAAE,UAAU;IAC1C,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE;QAC1B,OAAO,IAAI,CAAC;KACb;IACD,iBAAiB;IACjB,IAAI,CAAC,oBAAa,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE;QACtC,OAAO,IAAI,CAAC;KACb;IACD,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC;QAC5C,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC;QAC5C,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC;QAC5C,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC;KAC7C,CAAC;AACJ,CAAC;AAdD,8BAcC"}