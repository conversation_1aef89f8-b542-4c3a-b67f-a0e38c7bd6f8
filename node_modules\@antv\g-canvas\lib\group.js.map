{"version": 3, "file": "group.js", "sourceRoot": "", "sources": ["../src/group.ts"], "names": [], "mappings": ";;;AAAA,uCAA6C;AAK7C,+BAAiC;AACjC,oCAAgF;AAChF,mCAA4C;AAC5C,oCAA4C;AAE5C;IAAoB,iCAAa;IAAjC;;IAwGA,CAAC;IAvGC;;;OAGG;IACH,8BAAc,GAAd,UAAe,UAAsB;QACnC,qBAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAED,4BAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,4BAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,kBAAkB;IAClB,0BAAU,GAAV,UAAW,OAAO,EAAE,IAAe;QACjC,IAAI,IAAI,EAAE;YACR,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,0BAA0B;YAC1B,0BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACnC,aAAa;YACb,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzB,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK;YACL,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;IACH,CAAC;IAED,8DAA8D;IAC9D,qEAAqE;IAC7D,+BAAe,GAAvB;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;QACnC,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,WAAI,CAAC,QAAQ,EAAE,UAAC,KAAK;YACnB,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC;YACvC,qCAAqC;YACrC,kCAAkC;YAClC,IAAI,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aACjC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAM,IAAI,GAAG,UAAG,CAAC,IAAI,CAAC,CAAC;YACvB,IAAM,IAAI,GAAG,UAAG,CAAC,IAAI,CAAC,CAAC;YACvB,IAAM,IAAI,GAAG,UAAG,CAAC,IAAI,CAAC,CAAC;YACvB,IAAM,IAAI,GAAG,UAAG,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,GAAG;gBACL,IAAI,MAAA;gBACJ,IAAI,MAAA;gBACJ,CAAC,EAAE,IAAI;gBACP,CAAC,EAAE,IAAI;gBACP,IAAI,MAAA;gBACJ,IAAI,MAAA;gBACJ,KAAK,EAAE,IAAI,GAAG,IAAI;gBAClB,MAAM,EAAE,IAAI,GAAG,IAAI;aACpB,CAAC;YACF,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC/B,IAAI,MAAM,EAAE;gBACV,IAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxC,yDAAyD;gBACzD,4CAA4C;gBAC5C,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,oBAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;aACtD;SACF;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAC7B;QAED,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,oBAAI,GAAJ,UAAK,OAAiC,EAAE,MAAe;QACrD,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAsB,CAAC;QACjD,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW;QAC/D,sDAAsD;QACtD,4CAA4C;QAC5C,2CAA2C;QAC3C,IAAI,QAAQ,CAAC,MAAM,IAAI,SAAS,EAAE;YAChC,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,yBAAyB;YACzB,wDAAwD;YACxD,0BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAe,CAAC,CAAC;YACtD,mBAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxC,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;QACD,oBAAoB;QACpB,qDAAqD;QACrD,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,aAAa;QACb,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IACD,oBAAoB;IACpB,wBAAQ,GAAR;QACE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IACH,YAAC;AAAD,CAAC,AAxGD,CAAoB,sBAAa,GAwGhC;AAED,kBAAe,KAAK,CAAC"}