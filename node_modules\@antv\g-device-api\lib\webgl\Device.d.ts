import { Bindings, BindingsD<PERSON><PERSON>or, <PERSON><PERSON><PERSON>, BufferDescriptor, ComputePass, ComputePipeline, ComputePipelineDescriptor, <PERSON><PERSON>, <PERSON>ceLimits, IndexBufferDescriptor, InputLayout, InputLayoutDescriptor, PlatformFramebuffer, Program, ProgramDescriptor, QueryPool, QueryPoolType, Readback, RenderBundle, RenderPass, RenderPassDescriptor, RenderPipeline, RenderPipelineDescriptor, RenderTarget, RenderTargetDescriptor, Resource, Sampler, SamplerDescriptor, SwapChain, Texture, TextureDescriptor, VendorInfo, VertexBufferDescriptor } from '../api';
import { ClipSpaceNearZ, Format, ViewportOrigin } from '../api';
import type { EXT_texture_compression_rgtc, EXT_texture_norm16, KHR_parallel_shader_compile, OES_draw_buffers_indexed } from './interfaces';
import { Program_GL } from './Program';
import { RenderBundle_GL } from './RenderBundle';
/**
 * Extract uniform blocks from shader source.
 *
 * @example
 * ```glsl
 * layout(std140) uniform CommonUniforms {
    float u_blur;
  };
 * ```
 */
export declare const UNIFROM_BLOCK_REGEXP: RegExp;
export declare class Device_GL implements SwapChain, Device {
    private shaderDebug;
    private contextAttributes;
    OES_vertex_array_object: OES_vertex_array_object | null;
    ANGLE_instanced_arrays: ANGLE_instanced_arrays | null;
    OES_texture_float: OES_texture_float | null;
    OES_draw_buffers_indexed: OES_draw_buffers_indexed | null;
    WEBGL_draw_buffers: WEBGL_draw_buffers | null;
    WEBGL_depth_texture: WEBGL_depth_texture | null;
    WEBGL_color_buffer_float: WEBGL_color_buffer_float | null;
    EXT_color_buffer_half_float: EXT_color_buffer_half_float | null;
    WEBGL_compressed_texture_s3tc: WEBGL_compressed_texture_s3tc | null;
    WEBGL_compressed_texture_s3tc_srgb: WEBGL_compressed_texture_s3tc_srgb | null;
    EXT_texture_compression_rgtc: EXT_texture_compression_rgtc | null;
    EXT_texture_filter_anisotropic: EXT_texture_filter_anisotropic | null;
    KHR_parallel_shader_compile: KHR_parallel_shader_compile | null;
    EXT_texture_norm16: EXT_texture_norm16 | null;
    EXT_color_buffer_float: EXT_color_buffer_float | null;
    OES_texture_float_linear: OES_texture_float_linear | null;
    OES_texture_half_float_linear: OES_texture_half_float_linear | null;
    private scTexture;
    private scPlatformFramebuffer;
    private currentActiveTexture;
    private currentBoundVAO;
    private currentProgram;
    private resourceCreationTracker;
    private resourceUniqueId;
    private currentColorAttachments;
    private currentColorAttachmentLevels;
    private currentColorResolveTos;
    private currentColorResolveToLevels;
    private currentDepthStencilAttachment;
    private currentDepthStencilResolveTo;
    private currentSampleCount;
    private currentPipeline;
    private currentIndexBufferByteOffset;
    private currentMegaState;
    private currentSamplers;
    private currentTextures;
    private currentUniformBuffers;
    private currentUniformBufferByteOffsets;
    private currentUniformBufferByteSizes;
    private currentScissorEnabled;
    private currentStencilRef;
    private currentRenderPassDescriptor;
    private currentRenderPassDescriptorStack;
    private debugGroupStack;
    private resolveColorAttachmentsChanged;
    private resolveColorReadFramebuffer;
    private resolveColorDrawFramebuffer;
    private resolveDepthStencilAttachmentsChanged;
    private resolveDepthStencilReadFramebuffer;
    private resolveDepthStencilDrawFramebuffer;
    /**
     * use DRAW_FRAMEBUFFER in WebGL2
     */
    private renderPassDrawFramebuffer;
    private readbackFramebuffer;
    private fallbackTexture2D;
    private fallbackTexture2DDepth;
    private fallbackTexture2DArray;
    private fallbackTexture3D;
    private fallbackTextureCube;
    private fallbackVertexBuffer;
    readonly platformString: string;
    readonly glslVersion: string;
    readonly explicitBindingLocations = false;
    readonly separateSamplerTextures = false;
    readonly viewportOrigin = ViewportOrigin.LOWER_LEFT;
    readonly clipSpaceNearZ = ClipSpaceNearZ.NEGATIVE_ONE;
    readonly supportMRT: boolean;
    private inBlitRenderPass;
    private blitRenderPipeline;
    private blitInputLayout;
    private blitVertexBuffer;
    private blitBindings;
    private blitProgram;
    private renderBundle;
    /**
     * @see https://github.com/shrekshao/MoveWebGL1EngineToWebGL2/blob/master/Move-a-WebGL-1-Engine-To-WebGL-2-Blog-2.md#uniform-buffer
     */
    uniformBufferMaxPageByteSize: number;
    uniformBufferWordAlignment: number;
    uniformBufferMaxPageWordSize: number;
    supportedSampleCounts: number[];
    maxVertexAttribs: number;
    occlusionQueriesRecommended: boolean;
    computeShadersSupported: boolean;
    gl: WebGLRenderingContext | WebGL2RenderingContext;
    constructor(gl: WebGLRenderingContext | WebGL2RenderingContext, configuration?: Partial<{
        shaderDebug: boolean;
        trackResources: boolean;
    }>);
    destroy(): void;
    private createFallbackTexture;
    private getNextUniqueId;
    private checkLimits;
    configureSwapChain(width: number, height: number, platformFramebuffer?: PlatformFramebuffer): void;
    getDevice(): Device;
    getCanvas(): HTMLCanvasElement | OffscreenCanvas;
    getOnscreenTexture(): Texture;
    beginFrame(): void;
    endFrame(): void;
    translateTextureInternalFormat(fmt: Format, isRenderbufferStorage?: boolean): GLenum;
    translateTextureType(fmt: Format): GLenum;
    /**
     * Only works in WebGL2
     * @see https://webgl2fundamentals.org/webgl/lessons/webgl-data-textures.html
     */
    translateInternalTextureFormat(fmt: Format): GLenum;
    translateTextureFormat(fmt: Format): GLenum;
    setActiveTexture(texture: GLenum): void;
    private bindVAO;
    private programCompiled;
    private useProgram;
    ensureResourceExists<T>(resource: T | null): T;
    createBuffer(descriptor: BufferDescriptor): Buffer;
    createTexture(descriptor: TextureDescriptor): Texture;
    createSampler(descriptor: SamplerDescriptor): Sampler;
    createRenderTarget(descriptor: RenderTargetDescriptor): RenderTarget;
    createRenderTargetFromTexture(texture: Texture): RenderTarget;
    createProgram(descriptor: ProgramDescriptor): Program_GL;
    private createProgramSimple;
    createBindings(descriptor: BindingsDescriptor): Bindings;
    createInputLayout(descriptor: InputLayoutDescriptor): InputLayout;
    createRenderPipeline(descriptor: RenderPipelineDescriptor): RenderPipeline;
    createComputePass(): ComputePass;
    createComputePipeline(descriptor: ComputePipelineDescriptor): ComputePipeline;
    createReadback(): Readback;
    createQueryPool(type: QueryPoolType, elemCount: number): QueryPool;
    private formatRenderPassDescriptor;
    createRenderBundle(): RenderBundle_GL;
    beginBundle(bundle: RenderBundle): void;
    endBundle(): void;
    executeBundles(renderBundles: RenderBundle[]): void;
    createRenderPass(descriptor: RenderPassDescriptor): RenderPass;
    submitPass(pass: RenderPass | ComputePass): void;
    copySubTexture2D(dst_: Texture, dstX: number, dstY: number, src_: Texture, srcX: number, srcY: number): void;
    queryLimits(): DeviceLimits;
    queryTextureFormatSupported(format: Format, width: number, height: number): boolean;
    private queryProgramReady;
    queryPlatformAvailable(): boolean;
    queryVendorInfo(): VendorInfo;
    queryRenderPass(o: RenderPass): Readonly<RenderPassDescriptor>;
    queryRenderTarget(o: RenderTarget): Readonly<RenderTargetDescriptor>;
    setResourceName(o: Resource, name: string): void;
    setResourceLeakCheck(o: Resource, v: boolean): void;
    checkForLeaks(): void;
    pushDebugGroup(name: string): void;
    popDebugGroup(): void;
    insertDebugMarker(markerLabel: string): void;
    programPatched(o: Program, descriptor: ProgramDescriptor): void;
    getBufferData(buffer: Buffer, dstBuffer: ArrayBufferView, wordOffset?: number): void;
    private debugGroupStatisticsDrawCall;
    private debugGroupStatisticsBufferUpload;
    private debugGroupStatisticsTextureBind;
    private debugGroupStatisticsTriangles;
    private reportShaderError;
    private checkProgramCompilationForErrors;
    private bindFramebufferAttachment;
    private bindFramebufferDepthStencilAttachment;
    private validateCurrentAttachments;
    private setRenderPassParametersBegin;
    private setRenderPassParametersColor;
    private setRenderPassParametersDepthStencil;
    private setRenderPassParametersClearColor;
    private setRenderPassParametersClearDepthStencil;
    setBindings(bindings_: Bindings): void;
    setViewport(x: number, y: number, w: number, h: number): void;
    setScissorRect(x: number, y: number, w: number, h: number): void;
    private applyAttachmentStateIndexed;
    private applyAttachmentState;
    private setMegaState;
    private validatePipelineFormats;
    setPipeline(o: RenderPipeline): void;
    setVertexInput(inputLayout_: InputLayout | null, vertexBuffers: (VertexBufferDescriptor | null)[] | null, indexBuffer: IndexBufferDescriptor | null): void;
    setStencilReference(value: number): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpurendercommandsmixin-draw
     */
    draw(vertexCount: number, instanceCount?: number, firstVertex?: number, firstInstance?: number): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpurendercommandsmixin-drawindexed
     */
    drawIndexed(indexCount: number, instanceCount?: number, firstIndex?: number, baseVertex?: number, firstInstance?: number): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpurendercommandsmixin-drawindirect
     */
    drawIndirect(indirectBuffer: Buffer, indirectOffset: number): void;
    drawIndexedIndirect(indirectBuffer: Buffer, indirectOffset: number): void;
    beginOcclusionQuery(queryIndex: number): void;
    endOcclusionQuery(): void;
    pipelineQueryReady(o: RenderPipeline): boolean;
    pipelineForceReady(o: RenderPipeline): void;
    private endPass;
    private setScissorRectEnabled;
    private applyStencil;
    private getFallbackTexture;
    private submitBlitRenderPass;
}
