{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport request from '@/utils/request';\nimport { Message } from 'element-ui';\n\n// 获取验证码\nexport function getCaptcha() {\n  return request({\n    url: '/auth/captcha',\n    method: 'get'\n  })[\"catch\"](function (error) {\n    Message.error(error.msg || '获取验证码失败');\n    return Promise.reject(error);\n  });\n}\n\n// 登录\nexport function login(data) {\n  return request({\n    url: '/auth/login',\n    method: 'post',\n    data: data\n  })[\"catch\"](function (error) {\n    Message.error(error.msg || '登录失败');\n    return Promise.reject(error);\n  });\n}\n\n// 发送重置密码验证码\nexport function sendResetCode(email) {\n  return request({\n    url: '/auth/password/reset/code',\n    method: 'post',\n    params: {\n      email: email\n    }\n  });\n}\n\n// 重置密码\nexport function resetPassword(data) {\n  return request({\n    url: '/auth/password/reset',\n    method: 'post',\n    data: data\n  });\n}\n\n// 获取用户信息\nexport function getUserInfo() {\n  return request({\n    url: '/admin/info',\n    method: 'get'\n  });\n}\n\n// 更新用户信息\nexport function updateUserInfo(data) {\n  return request({\n    url: '/admin/info/update',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改密码\nexport function updatePassword(data) {\n  return request({\n    url: '/admin/password',\n    method: 'post',\n    data: data\n  });\n}", "map": {"version": 3, "names": ["request", "Message", "getCaptcha", "url", "method", "error", "msg", "Promise", "reject", "login", "data", "sendResetCode", "email", "params", "resetPassword", "getUserInfo", "updateUserInfo", "updatePassword"], "sources": ["F:/常规项目/华通云/adminweb/src/api/auth.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport { Message } from 'element-ui'\r\n\r\n// 获取验证码\r\nexport function getCaptcha() {\r\n  return request({\r\n    url: '/auth/captcha',\r\n    method: 'get'\r\n  }).catch(error => {\r\n    Message.error(error.msg || '获取验证码失败')\r\n    return Promise.reject(error)\r\n  })\r\n}\r\n\r\n// 登录\r\nexport function login(data) {\r\n  return request({\r\n    url: '/auth/login',\r\n    method: 'post',\r\n    data\r\n  }).catch(error => {\r\n    Message.error(error.msg || '登录失败')\r\n    return Promise.reject(error)\r\n  })\r\n}\r\n\r\n// 发送重置密码验证码\r\nexport function sendResetCode(email) {\r\n  return request({\r\n    url: '/auth/password/reset/code',\r\n    method: 'post',\r\n    params: { email }\r\n  })\r\n}\r\n\r\n// 重置密码\r\nexport function resetPassword(data) {\r\n  return request({\r\n    url: '/auth/password/reset',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取用户信息\r\nexport function getUserInfo() {\r\n  return request({\r\n    url: '/admin/info',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新用户信息\r\nexport function updateUserInfo(data) {\r\n  return request({\r\n    url: '/admin/info/update',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 修改密码\r\nexport function updatePassword(data) {\r\n  return request({\r\n    url: '/admin/password',\r\n    method: 'post',\r\n    data\r\n  })\r\n}"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,SAASC,OAAO,QAAQ,YAAY;;AAEpC;AACA,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC,SAAM,CAAC,UAAAC,KAAK,EAAI;IAChBJ,OAAO,CAACI,KAAK,CAACA,KAAK,CAACC,GAAG,IAAI,SAAS,CAAC;IACrC,OAAOC,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;EAC9B,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,KAAKA,CAACC,IAAI,EAAE;EAC1B,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAJA;EACF,CAAC,CAAC,SAAM,CAAC,UAAAL,KAAK,EAAI;IAChBJ,OAAO,CAACI,KAAK,CAACA,KAAK,CAACC,GAAG,IAAI,MAAM,CAAC;IAClC,OAAOC,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;EAC9B,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdS,MAAM,EAAE;MAAED,KAAK,EAALA;IAAM;EAClB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,aAAaA,CAACJ,IAAI,EAAE;EAClC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,WAAWA,CAAA,EAAG;EAC5B,OAAOf,OAAO,CAAC;IACbG,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,cAAcA,CAACN,IAAI,EAAE;EACnC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,cAAcA,CAACP,IAAI,EAAE;EACnC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}