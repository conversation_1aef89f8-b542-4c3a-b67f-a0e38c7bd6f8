!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Algorithm=t():e.Algorithm=t()}(this,(()=>(()=>{"use strict";var e={d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},t={};e.d(t,{default:()=>pe});const r=function(e,t){var r=e.nodes,n=e.edges,o=[],i={};if(!r)throw new Error("invalid nodes data!");return r&&r.forEach((function(e,t){i[e.id]=t,o.push([])})),n&&n.forEach((function(e){var r=e.source,n=e.target,a=i[r],d=i[n];!a&&0!==a||!d&&0!==d||(o[a][d]=1,t||(o[d][a]=1))})),o};var n=function(e,t){return e===t},o=function(){function e(e,t){void 0===t&&(t=null),this.value=e,this.next=t}return e.prototype.toString=function(e){return e?e(this.value):"".concat(this.value)},e}();const i=function(){function e(e){void 0===e&&(e=n),this.head=null,this.tail=null,this.compare=e}return e.prototype.prepend=function(e){var t=new o(e,this.head);return this.head=t,this.tail||(this.tail=t),this},e.prototype.append=function(e){var t=new o(e);return this.head?(this.tail.next=t,this.tail=t,this):(this.head=t,this.tail=t,this)},e.prototype.delete=function(e){if(!this.head)return null;for(var t=null;this.head&&this.compare(this.head.value,e);)t=this.head,this.head=this.head.next;var r=this.head;if(null!==r)for(;r.next;)this.compare(r.next.value,e)?(t=r.next,r.next=r.next.next):r=r.next;return this.compare(this.tail.value,e)&&(this.tail=r),t},e.prototype.find=function(e){var t=e.value,r=void 0===t?void 0:t,n=e.callback,o=void 0===n?void 0:n;if(!this.head)return null;for(var i=this.head;i;){if(o&&o(i.value))return i;if(void 0!==r&&this.compare(i.value,r))return i;i=i.next}return null},e.prototype.deleteTail=function(){var e=this.tail;if(this.head===this.tail)return this.head=null,this.tail=null,e;for(var t=this.head;t.next;)t.next.next?t=t.next:t.next=null;return this.tail=t,e},e.prototype.deleteHead=function(){if(!this.head)return null;var e=this.head;return this.head.next?this.head=this.head.next:(this.head=null,this.tail=null),e},e.prototype.fromArray=function(e){var t=this;return e.forEach((function(e){return t.append(e)})),this},e.prototype.toArray=function(){for(var e=[],t=this.head;t;)e.push(t),t=t.next;return e},e.prototype.reverse=function(){for(var e=this.head,t=null,r=null;e;)r=e.next,e.next=t,t=e,e=r;this.tail=this.head,this.head=t},e.prototype.toString=function(e){return void 0===e&&(e=void 0),this.toArray().map((function(t){return t.toString(e)})).toString()},e}(),a=function(){function e(){this.linkedList=new i}return e.prototype.isEmpty=function(){return!this.linkedList.head},e.prototype.peek=function(){return this.linkedList.head?this.linkedList.head.value:null},e.prototype.enqueue=function(e){this.linkedList.append(e)},e.prototype.dequeue=function(){var e=this.linkedList.deleteHead();return e?e.value:null},e.prototype.toString=function(e){return this.linkedList.toString(e)},e}();var d=function(e,t,r){void 0===t&&(t=[]);var n=t.filter((function(t){return t.source===e||t.target===e}));return"target"===r?n.filter((function(t){return t.source===e})).map((function(e){return e.target})):"source"===r?n.filter((function(t){return t.target===e})).map((function(e){return e.source})):n.map((function(t){return t.source===e?t.target:t.source}))},u=function(e,t){return t.filter((function(t){return t.source===e||t.target===e}))},s=function(e){void 0===e&&(e=0);var t="".concat(Math.random()).split(".")[1].substr(0,5),r="".concat(Math.random()).split(".")[1].substr(0,5);return"".concat(e,"-").concat(t).concat(r)};var c=function(e){for(var t=e.nodes,r=void 0===t?[]:t,n=e.edges,o=void 0===n?[]:n,i=[],a={},u={},s={},c=[],f=0,l=function(e){u[e.id]=f,s[e.id]=f,f+=1,i.push(e),a[e.id]=!0;for(var t=d(e.id,o,"target").filter((function(e){return r.map((function(e){return e.id})).indexOf(e)>-1})),n=function(n){var o=t[n];if(u[o]||0===u[o])a[o]&&(s[e.id]=Math.min(s[e.id],u[o]));else{var i=r.filter((function(e){return e.id===o}));i.length>0&&l(i[0]),s[e.id]=Math.min(s[e.id],s[o])}},h=0;h<t.length;h++)n(h);if(s[e.id]===u[e.id]){for(var v=[];i.length>0;){var p=i.pop();if(a[p.id]=!1,v.push(p),p===e)break}v.length>0&&c.push(v)}},h=0,v=r;h<v.length;h++){var p=v[h];u[p.id]||0===u[p.id]||l(p)}return c};function f(e,t){return t?c(e):function(e){for(var t=e.nodes,r=void 0===t?[]:t,n=e.edges,o=void 0===n?[]:n,i=[],a={},u=[],s=function(e){u.push(e),a[e.id]=!0;for(var t=d(e.id,o),n=function(e){var n=t[e];if(!a[n]){var o=r.filter((function(e){return e.id===n}));o.length>0&&s(o[0])}},i=0;i<t.length;++i)n(i)},c=0;c<r.length;c++){var f=r[c];if(!a[f.id]){s(f);for(var l=[];u.length>0;)l.push(u.pop());i.push(l)}}return i}(e)}var l=function(e){var t={},r=e.nodes,n=void 0===r?[]:r,o=e.edges,i=void 0===o?[]:o;return n.forEach((function(e){t[e.id]={degree:0,inDegree:0,outDegree:0}})),i.forEach((function(e){t[e.source].degree++,t[e.source].outDegree++,t[e.target].degree++,t[e.target].inDegree++})),t};const h=l;function v(e,t,r,n,o){void 0===o&&(o=!0),n.enter({current:t,previous:r});var i=e.edges;d(t,void 0===i?[]:i,o?"target":void 0).forEach((function(i){n.allowTraversal({previous:r,current:t,next:i})&&v(e,i,t,n,o)})),n.leave({current:t,previous:r})}function p(e,t,r,n){void 0===n&&(n=!0),v(e,t,"",function(e){void 0===e&&(e={});var t,r=e,n=function(){},o=(t={},function(e){var r=e.next;return!t[r]&&(t[r]=!0,!0)});return r.allowTraversal=e.allowTraversal||o,r.enter=e.enter||n,r.leave=e.leave||n,r}(r),n)}var g=function(e,t,r){var n,o;void 0===r&&(r=!0);for(var i=[],a=0,u=f(e,!1);a<u.length;a++){var s=u[a];if(s.length)for(var c=s[0],l=c.id,h=[c],v=((n={})[l]=c,n),p=((o={})[l]=new Set,o);h.length>0;)for(var g=h.pop(),b=g.id,E=d(b,e.edges),y=function(n){var o,a=E[n],d=e.nodes.find((function(e){return e.id===a}));if(a===b)i.push(((o={})[a]=g,o));else if(a in p){if(!p[b].has(d)){for(var u=!0,s=[d,g],c=v[b];p[a].size&&!p[a].has(c)&&(s.push(c),c!==v[c.id]);)c=v[c.id];if(s.push(c),t&&r?(u=!1,s.findIndex((function(e){return t.indexOf(e.id)>-1}))>-1&&(u=!0)):t&&!r&&s.findIndex((function(e){return t.indexOf(e.id)>-1}))>-1&&(u=!1),u){for(var f={},l=1;l<s.length;l+=1)f[s[l-1].id]=s[l];s.length&&(f[s[s.length-1].id]=s[0]),i.push(f)}p[a].add(g)}}else v[a]=g,h.push(d),p[a]=new Set([g])},m=0;m<E.length;m+=1)y(m)}return i},b=function(e,t,r){void 0===r&&(r=!0);for(var n=[],o=new Set,i=[],a=[],u={},s={},f=function(e,d,s){var c=!1;if(t&&!1===r&&t.indexOf(e.id)>-1)return c;n.push(e),o.add(e);for(var l=s[e.id],h=0;h<l.length;h+=1)if((g=u[l[h]])===d){for(var v={},p=1;p<n.length;p+=1)v[n[p-1].id]=n[p];n.length&&(v[n[n.length-1].id]=n[0]),a.push(v),c=!0}else o.has(g)||f(g,d,s)&&(c=!0);if(c)!function(e){for(var t=[e];t.length>0;){var r=t.pop();o.has(r)&&(o.delete(r),i[r.id].forEach((function(e){t.push(e)})),i[r.id].clear())}}(e);else for(h=0;h<l.length;h+=1){var g=u[l[h]];i[g.id].has(e)||i[g.id].add(e)}return n.pop(),c},l=e.nodes,h=void 0===l?[]:l,v=0;v<h.length;v+=1){var p=h[v],g=p.id;s[g]=v,u[v]=p}if(t&&r){var b=function(e){var r=t[e];s[h[e].id]=s[r],s[r]=0,u[0]=h.find((function(e){return e.id===r})),u[s[h[e].id]]=h[e]};for(v=0;v<t.length;v++)b(v)}for(var E=function(n){for(var o,i,u=1/0,c=0;c<n.length;c+=1)for(var f=n[c],l=0;l<f.length;l++){var h=s[f[l].id];h<u&&(u=h,i=c)}var v=n[i],p=[];for(c=0;c<v.length;c+=1){var g=v[c];p[g.id]=[];for(var b=0,E=d(g.id,e.edges,"target").filter((function(e){return v.map((function(e){return e.id})).indexOf(e)>-1}));b<E.length;b++){var y=E[b];y!==g.id||!1===r&&t.indexOf(g.id)>-1?p[g.id].push(s[y]):a.push(((o={})[g.id]=g,o))}}return{component:v,adjList:p,minIdx:u}},y=0;y<h.length;){var m=h.filter((function(e){return s[e.id]>=y})),L=c({nodes:m,edges:e.edges}).filter((function(e){return e.length>1}));if(0===L.length)break;var N=E(L),w=N.minIdx,k=N.adjList,I=N.component;if(!(I.length>1))break;I.forEach((function(e){i[e.id]=new Set}));var x=u[w];if(t&&r&&-1===t.indexOf(x.id))return a;f(x,x,k),y=w+1}return a};const E=function(e){var t=null,r=e.nodes,n={},o={},i={},a={};(void 0===r?[]:r).forEach((function(e){o[e.id]=e}));for(var d={enter:function(e){var r=e.current,a=e.previous;if(i[r]){t={};for(var d=r,u=a;u!==r;)t[d]=u,d=u,u=n[u];t[d]=u}else i[r]=r,delete o[r],n[r]=a},leave:function(e){var t=e.current;a[t]=t,delete i[t]},allowTraversal:function(e){var r=e.next;return!t&&!a[r]}};Object.keys(o).length;)p(e,Object.keys(o)[0],d);return t};function y(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var m={}.toString;const L=function(e,t){return m.call(e)==="[object "+t+"]"},N=function(e){return L(e,"Function")},w=function(e){return Array.isArray?Array.isArray(e):L(e,"Array")};Object.keys;var k=Array.prototype;k.splice,k.indexOf,Array.prototype.splice;const I=function(e){return L(e,"String")};function x(e,t){void 0===t&&(t=new Map);var r=[];if(Array.isArray(e))for(var n=0,o=e.length;n<o;n++){var i=e[n];t.has(i)||(r.push(i),t.set(i,!0))}return r}Object.prototype.hasOwnProperty;Number.isInteger&&Number.isInteger,Math.PI,Math.PI;Object.values;Object.prototype;const M=function e(t){if("object"!=typeof t||null===t)return t;var r;if(w(t)){r=[];for(var n=0,o=t.length;n<o;n++)"object"==typeof t[n]&&null!=t[n]?r[n]=e(t[n]):r[n]=t[n]}else for(var i in r={},t)"object"==typeof t[i]&&null!=t[i]?r[i]=e(t[i]):r[i]=t[i];return r};Object.prototype.hasOwnProperty;const j=function(e){return"object"==typeof e&&null!==e},O=function(e){return null!==e&&"function"!=typeof e&&isFinite(e.length)},D=function e(t,r){if(t===r)return!0;if(!t||!r)return!1;if(I(t)||I(r))return!1;if(O(t)||O(r)){if(t.length!==r.length)return!1;for(var n=!0,o=0;o<t.length&&(n=e(t[o],r[o]));o++);return n}if(j(t)||j(r)){var i=Object.keys(t),a=Object.keys(r);if(i.length!==a.length)return!1;for(n=!0,o=0;o<i.length&&(n=e(t[i[o]],r[i[o]]));o++);return n}return!1};var S;Object.prototype.hasOwnProperty,function(e,t){if(!N(e))throw new TypeError("Expected a function");new Map}((function(e,t){void 0===t&&(t={});var r=t.fontSize,n=t.fontFamily,o=t.fontWeight,i=t.fontStyle,a=t.fontVariant;return S||(S=document.createElement("canvas").getContext("2d")),S.font=[i,a,o,r+"px",n].join(" "),S.measureText(I(e)?e:"").width})),function(){function e(){this.map={}}e.prototype.has=function(e){return void 0!==this.map[e]},e.prototype.get=function(e,t){var r=this.map[e];return void 0===r?t:r},e.prototype.set=function(e,t){this.map[e]=t},e.prototype.clear=function(){this.map={}},e.prototype.delete=function(e){delete this.map[e]},e.prototype.size=function(){return Object.keys(this.map).length}}();const C=function(e,t,r,n){var o=e.nodes,i=void 0===o?[]:o,a=e.edges,d=void 0===a?[]:a,s=[],c={},f={},l={};i.forEach((function(e,r){var n=e.id;s.push(n),f[n]=1/0,n===t&&(f[n]=0)}));for(var h=i.length,v=function(e){var t=function(e,t,r){for(var n,o=1/0,i=0;i<t.length;i++){var a=t[i].id;!r[a]&&e[a]<=o&&(o=e[a],n=t[i])}return n}(f,i,c),o=t.id;if(c[o]=!0,f[o]===1/0)return"continue";var a=[];a=r?function(e,t){return t.filter((function(t){return t.source===e}))}(o,d):u(o,d),a.forEach((function(e){var r=e.target,i=e.source,a=r===o?i:r,d=n&&e[n]?e[n]:1;f[a]>f[t.id]+d?(f[a]=f[t.id]+d,l[a]=[t.id]):f[a]===f[t.id]+d&&l[a].push(t.id)}))},p=0;p<h;p++)v();l[t]=[t];var g={};for(var b in f)f[b]!==1/0&&P(t,b,l,g);var E={};for(var b in g)E[b]=g[b][0];return{length:f,path:E,allPath:g}};function P(e,t,r,n){if(e===t)return[e];if(n[t])return n[t];for(var o=[],i=0,a=r[t];i<a.length;i++){var d=P(e,a[i],r,n);if(!d)return;for(var u=0,s=d;u<s.length;u++){var c=s[u];w(c)?o.push(y(y([],c,!0),[t],!1)):o.push([c,t])}}return n[t]=o,n[t]}const A=function(e,t){for(var n=r(e,t),o=[],i=n.length,a=0;a<i;a+=1){o[a]=[];for(var d=0;d<i;d+=1)a===d?o[a][d]=0:0!==n[a][d]&&n[a][d]?o[a][d]=n[a][d]:o[a][d]=1/0}for(var u=0;u<i;u+=1)for(a=0;a<i;a+=1)for(d=0;d<i;d+=1)o[a][d]>o[a][u]+o[u][d]&&(o[a][d]=o[a][u]+o[u][d]);return o};const T=function(){function e(e){this.arr=e}return e.prototype.getArr=function(){return this.arr||[]},e.prototype.add=function(t){var r,n=t.arr;if(!(null===(r=this.arr)||void 0===r?void 0:r.length))return new e(n);if(!(null==n?void 0:n.length))return new e(this.arr);if(this.arr.length===n.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]+n[i];return new e(o)}},e.prototype.subtract=function(t){var r,n=t.arr;if(!(null===(r=this.arr)||void 0===r?void 0:r.length))return new e(n);if(!(null==n?void 0:n.length))return new e(this.arr);if(this.arr.length===n.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]-n[i];return new e(o)}},e.prototype.avg=function(t){var r=[];if(0!==t)for(var n in this.arr)r[n]=this.arr[n]/t;return new e(r)},e.prototype.negate=function(){var t=[];for(var r in this.arr)t[r]=-this.arr[r];return new e(t)},e.prototype.squareEuclideanDistance=function(e){var t,r=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null==r?void 0:r.length))return 0;if(this.arr.length===r.length){var n=0;for(var o in this.arr)n+=Math.pow(this.arr[o]-e.arr[o],2);return n}},e.prototype.euclideanDistance=function(e){var t,r=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null==r?void 0:r.length))return 0;if(this.arr.length===r.length){var n=0;for(var o in this.arr)n+=Math.pow(this.arr[o]-e.arr[o],2);return Math.sqrt(n)}console.error("The two vectors are unequal in length.")},e.prototype.normalize=function(){var t=[],r=M(this.arr);r.sort((function(e,t){return e-t}));var n=r[r.length-1],o=r[0];for(var i in this.arr)t[i]=(this.arr[i]-o)/(n-o);return new e(t)},e.prototype.norm2=function(){var e;if(!(null===(e=this.arr)||void 0===e?void 0:e.length))return 0;var t=0;for(var r in this.arr)t+=Math.pow(this.arr[r],2);return Math.sqrt(t)},e.prototype.dot=function(e){var t,r=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null==r?void 0:r.length))return 0;if(this.arr.length===r.length){var n=0;for(var o in this.arr)n+=this.arr[o]*e.arr[o];return n}console.error("The two vectors are unequal in length.")},e.prototype.equal=function(e){var t,r=e.arr;if((null===(t=this.arr)||void 0===t?void 0:t.length)!==(null==r?void 0:r.length))return!1;for(var n in this.arr)if(this.arr[n]!==r[n])return!1;return!0},e}();var q,F=function(e,t){void 0===t&&(t=void 0);var r=[];return e.forEach((function(e){void 0===t&&r.push(e),void 0!==e[t]&&r.push(e[t])})),r};!function(e){e.EuclideanDistance="euclideanDistance"}(q||(q={}));var R=function(e,t,r){var n=function(e,t,r){var n=[];(null==t?void 0:t.length)?n=t:(e.forEach((function(e){n=n.concat(Object.keys(e))})),n=x(n));var o={};return n.forEach((function(t){var n=[];e.forEach((function(e){void 0!==e[t]&&""!==e[t]&&n.push(e[t])})),n.length&&!(null==r?void 0:r.includes(t))&&(o[t]=x(n))})),o}(e,t,r),o=[];if(!Object.keys(n).length)return o;var i=Object.values(n).every((function(e){return e.every((function(e){return"number"==typeof e}))}));return e.forEach((function(e,t){var r=[];Object.keys(n).forEach((function(t){var o=e[t],a=n[t],d=a.findIndex((function(e){return o===e})),u=[];if(i)u.push(o);else for(var s=0;s<a.length;s++)s===d?u.push(1):u.push(0);r=r.concat(u)})),o[t]=r})),o},G=function(e,t,r,n){void 0===r&&(r=q.EuclideanDistance);var o=0;return r===q.EuclideanDistance&&(o=new T(e).euclideanDistance(new T(t))),o},U=function(e,t,r,n){for(var o=t.length,i=2*n,a=0,d=0;d<o;d++)for(var u=e[d].clusterId,s=0;s<o;s++)u===e[s].clusterId&&(a+=(t[d][s]||0)-(r[d]||0)*(r[s]||0)/i);return a*(1/i)},z=function(e,t){void 0===e&&(e=[]);for(var r=e.length,n=new T([]),o=0;o<r;o++)n=n.add(new T(t[o]));var i=n.avg(r);i.normalize();var a=0;for(o=0;o<r;o++)a+=(u=new T(t[o])).squareEuclideanDistance(i);var d=[];for(e.forEach((function(){d.push([])})),o=0;o<r;o++){var u=new T(t[o]);e[o].clusterInertial=0;for(var s=0;s<r;s++)if(o!==s){var c=new T(t[s]);d[o][s]=u.squareEuclideanDistance(c),e[o].clusterInertial+=d[o][s]}else d[o][s]=0}var f=0,l=2*r*a;for(o=0;o<r;o++){var h=e[o].clusterId;for(s=0;s<r;s++){var v=e[s].clusterId;o!==s&&h===v&&(f+=e[o].clusterInertial*e[s].clusterInertial/Math.pow(l,2)-d[o][s]/l)}}return Number(f.toFixed(4))};const B=function(e,t,n,o,i,a,d,u,s){void 0===t&&(t=!1),void 0===n&&(n="weight"),void 0===o&&(o=1e-4),void 0===i&&(i=!1),void 0===a&&(a=void 0),void 0===d&&(d=[]),void 0===u&&(u=["id"]),void 0===s&&(s=1);var c=e.nodes,f=void 0===c?[]:c,l=e.edges,h=void 0===l?[]:l,v=[];if(i){f.forEach((function(e,t){e.properties=e.properties||{},e.originIndex=t}));var p=[];f.every((function(e){return e.hasOwnProperty("nodeType")}))&&(p=Array.from(new Set(f.map((function(e){return e.nodeType})))),f.forEach((function(e){e.properties.nodeType=p.findIndex((function(t){return t===e.nodeType}))})));var g=F(f,a);v=R(g,d,u)}var b=1,E={},y={};f.forEach((function(e,t){var r=String(b++);e.clusterId=r,E[r]={id:r,nodes:[e]},y[e.id]={node:e,idx:t}}));var m=r(e,t),L=[],N={},w=0;m.forEach((function(e,t){var r=0,n=f[t].id;N[n]={},e.forEach((function(e,t){if(e){r+=e;var o=f[t].id;N[n][o]=e,w+=e}})),L.push(r)})),w/=2;for(var k=1/0,I=1/0,x=0,j=[],O={};;){k=i&&f.every((function(e){return e.hasOwnProperty("properties")}))?U(f,m,L,w)+z(f,v)*s:U(f,m,L,w),0===x&&(I=k,j=f,O=E);var D=k>0&&k>I&&k-I<o;if(k>I&&(j=f.map((function(e){return{node:e,clusterId:e.clusterId}})),O=M(E),I=k),D||x>100)break;x++,Object.keys(E).forEach((function(e){var t=0;h.forEach((function(r){var o=r.source,i=r.target,a=y[o].node.clusterId,d=y[i].node.clusterId;(a===e&&d!==e||d===e&&a!==e)&&(t+=r[n]||1)})),E[e].sumTot=t})),f.forEach((function(e,t){var r,o=E[e.clusterId],a=0,d=L[t]/(2*w),u=0,c=o.nodes;c.forEach((function(e){var r=y[e.id].idx;u+=m[t][r]||0}));var f=u-o.sumTot*d,l=c.filter((function(t){return t.id!==e.id})),p=[];l.forEach((function(e,t){p[t]=v[e.originIndex]}));var g=z(l,v)*s,b=N[e.id];if(Object.keys(b).forEach((function(n){var o=y[n].node.clusterId;if(o!==e.clusterId){var u=E[o],c=u.nodes;if(c&&c.length){var l=0;c.forEach((function(e){var r=y[e.id].idx;l+=m[t][r]||0}));var h=l-u.sumTot*d,p=c.concat([e]),b=[];p.forEach((function(e,t){b[t]=v[e.originIndex]}));var L=z(p,v)*s,N=h-f;i&&(N=h+L-(f+g)),N>a&&(a=N,r=u)}}})),a>0){r.nodes.push(e);var k=e.clusterId;e.clusterId=r.id;var I=o.nodes.indexOf(e);o.nodes.splice(I,1);var x=0,M=0;h.forEach((function(e){var t=e.source,o=e.target,i=y[t].node.clusterId,a=y[o].node.clusterId;(i===r.id&&a!==r.id||a===r.id&&i!==r.id)&&(x+=e[n]||1),(i===k&&a!==k||a===k&&i!==k)&&(M+=e[n]||1)})),r.sumTot=x,o.sumTot=M}}))}var S={},C=0;Object.keys(O).forEach((function(e){var t=O[e];if(t.nodes&&t.nodes.length){var r=String(C+1);r!==e&&(t.id=r,t.nodes=t.nodes.map((function(e){return{id:e.id,clusterId:r}})),O[r]=t,S[e]=r,delete O[e],C++)}else delete O[e]})),j.forEach((function(e){var t=e.node,r=e.clusterId;t&&(t.clusterId=r,t.clusterId&&S[t.clusterId]&&(t.clusterId=S[t.clusterId]))}));var P=[],A={};h.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=y[t].node.clusterId,a=y[r].node.clusterId;if(i&&a){var d="".concat(i,"---").concat(a);if(A[d])A[d].weight+=o,A[d].count++;else{var u={source:i,target:a,weight:o,count:1};A[d]=u,P.push(u)}}}));var T=[];return Object.keys(O).forEach((function(e){T.push(O[e])})),{clusters:T,clusterEdges:P}};var H=function(e,t,r){return e===q.EuclideanDistance?t[r]:[]};const W=function(e,t){var r=new T(t),n=r.norm2(),o=new T(e),i=o.norm2(),a=r.dot(o),d=n*i;return d?a/d:0},K=function(){function e(e){this.count=e.length,this.parent={};for(var t=0,r=e;t<r.length;t++){var n=r[t];this.parent[n]=n}}return e.prototype.find=function(e){for(;this.parent[e]!==e;)e=this.parent[e];return e},e.prototype.union=function(e,t){var r=this.find(e),n=this.find(t);r!==n&&(r<n?(this.parent[t]!==t&&this.union(this.parent[t],e),this.parent[t]=this.parent[e]):(this.parent[e]!==e&&this.union(this.parent[e],t),this.parent[e]=this.parent[t]))},e.prototype.connected=function(e,t){return this.find(e)===this.find(t)},e}();var V=function(e,t){return e-t};const J=function(){function e(e){void 0===e&&(e=V),this.compareFn=e,this.list=[]}return e.prototype.getLeft=function(e){return 2*e+1},e.prototype.getRight=function(e){return 2*e+2},e.prototype.getParent=function(e){return 0===e?null:Math.floor((e-1)/2)},e.prototype.isEmpty=function(){return this.list.length<=0},e.prototype.top=function(){return this.isEmpty()?void 0:this.list[0]},e.prototype.delMin=function(){var e=this.top(),t=this.list.pop();return this.list.length>0&&(this.list[0]=t,this.moveDown(0)),e},e.prototype.insert=function(e){if(null!==e){this.list.push(e);var t=this.list.length-1;return this.moveUp(t),!0}return!1},e.prototype.moveUp=function(e){for(var t=this.getParent(e);e&&e>0&&this.compareFn(this.list[t],this.list[e])>0;){var r=this.list[t];this.list[t]=this.list[e],this.list[e]=r,e=t,t=this.getParent(e)}},e.prototype.moveDown=function(e){var t,r=e,n=this.getLeft(e),o=this.getRight(e),i=this.list.length;null!==n&&n<i&&this.compareFn(this.list[r],this.list[n])>0?r=n:null!==o&&o<i&&this.compareFn(this.list[r],this.list[o])>0&&(r=o),e!==r&&(t=[this.list[r],this.list[e]],this.list[e]=t[0],this.list[r]=t[1],this.moveDown(r))},e}();var Q=function(e,t){var r=[],n=e.nodes,o=void 0===n?[]:n,i=e.edges,a=void 0===i?[]:i;if(0===o.length)return r;var d=o[0],s=new Set;s.add(d);var c=new J((function(e,r){return t?e.weight-r.weight:0}));for(u(d.id,a).forEach((function(e){c.insert(e)}));!c.isEmpty();){var f=c.delMin(),l=f.source,h=f.target;s.has(l)&&s.has(h)||(r.push(f),s.has(l)||(s.add(l),u(l,a).forEach((function(e){c.insert(e)}))),s.has(h)||(s.add(h),u(h,a).forEach((function(e){c.insert(e)}))))}return r},X=function(e,t){var r=[],n=e.nodes,o=void 0===n?[]:n,i=e.edges,a=void 0===i?[]:i;if(0===o.length)return r;var d=a.map((function(e){return e}));t&&d.sort((function(e,t){return e.weight-t.weight}));for(var u=new K(o.map((function(e){return e.id})));d.length>0;){var s=d.shift(),c=s.source,f=s.target;u.connected(c,f)||(r.push(s),u.union(c,f))}return r};var Y="-1",Z=function(e,t,r,n){void 0===e&&(e=-1),void 0===t&&(t=-1),void 0===r&&(r=-1),void 0===n&&(n="-1"),this.id=e,this.from=t,this.to=r,this.label=n},$=function(){function e(e,t){void 0===e&&(e=-1),void 0===t&&(t=Y),this.id=e,this.label=t,this.edges=[],this.edgeMap={}}return e.prototype.addEdge=function(e){this.edges.push(e),this.edgeMap[e.id]=e},e}(),_=function(){function e(e,t,r){void 0===e&&(e=-1),void 0===t&&(t=!0),void 0===r&&(r=!1),this.id=e,this.edgeIdAutoIncrease=t,this.edges=[],this.nodes=[],this.nodeMap={},this.edgeMap={},this.nodeLabelMap={},this.edgeLabelMap={},this.counter=0,this.directed=r}return e.prototype.getNodeNum=function(){return this.nodes.length},e.prototype.addNode=function(e,t){if(!this.nodeMap[e]){var r=new $(e,t);this.nodes.push(r),this.nodeMap[e]=r,this.nodeLabelMap[t]||(this.nodeLabelMap[t]=[]),this.nodeLabelMap[t].push(e)}},e.prototype.addEdge=function(e,t,r,n){if((this.edgeIdAutoIncrease||void 0===e)&&(e=this.counter++),!(this.nodeMap[t]&&this.nodeMap[r]&&this.nodeMap[r].edgeMap[e])){var o=new Z(e,t,r,n);if(this.edges.push(o),this.edgeMap[e]=o,this.nodeMap[t].addEdge(o),this.edgeLabelMap[n]||(this.edgeLabelMap[n]=[]),this.edgeLabelMap[n].push(o),!this.directed){var i=new Z(e,r,t,n);this.nodeMap[r].addEdge(i),this.edgeLabelMap[n].push(i)}}},e}(),ee=function(){function e(e,t,r,n,o){this.fromNode=e,this.toNode=t,this.nodeEdgeNodeLabel={nodeLabel1:r||Y,edgeLabel:n||"-1",nodeLabel2:o||Y}}return e.prototype.equalTo=function(e){return this.fromNode===e.formNode&&this.toNode===e.toNode&&this.nodeEdgeNodeLabel===e.nodeEdgeNodeLabel},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e}(),te=function(){function e(){this.rmpath=[],this.dfsEdgeList=[]}return e.prototype.equalTo=function(e){var t=this.dfsEdgeList.length;if(t!==e.length)return!1;for(var r=0;r<t;r++)if(this.dfsEdgeList[r]!==e[r])return!1;return!0},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e.prototype.pushBack=function(e,t,r,n,o){return this.dfsEdgeList.push(new ee(e,t,r,n,o)),this.dfsEdgeList},e.prototype.toGraph=function(e,t){void 0===e&&(e=-1),void 0===t&&(t=!1);var r=new _(e,!0,t);return this.dfsEdgeList.forEach((function(e){var t=e.fromNode,n=e.toNode,o=e.nodeEdgeNodeLabel,i=o.nodeLabel1,a=o.edgeLabel,d=o.nodeLabel2;i!==Y&&r.addNode(t,i),d!==Y&&r.addNode(n,d),i!==Y&&d!==i&&r.addEdge(void 0,t,n,a)})),r},e.prototype.buildRmpath=function(){this.rmpath=[];for(var e=void 0,t=this.dfsEdgeList.length-1;t>=0;t--){var r=this.dfsEdgeList[t],n=r.fromNode,o=r.toNode;n<o&&(void 0===e||o===e)&&(this.rmpath.push(t),e=n)}return this.rmpath},e.prototype.getNodeNum=function(){var e={};return this.dfsEdgeList.forEach((function(t){e[t.fromNode]||(e[t.fromNode]=!0),e[t.toNode]||(e[t.toNode]=!0)})),Object.keys(e).length},e}(),re=function(){function e(e){if(this.his={},this.nodesUsed={},this.edgesUsed={},this.edges=[],e){for(;e;){var t=e.edge;this.edges.push(t),this.nodesUsed[t.from]=1,this.nodesUsed[t.to]=1,this.edgesUsed[t.id]=1,e=e.preNode}this.edges=this.edges.reverse()}}return e.prototype.hasNode=function(e){return 1===this.nodesUsed[e.id]},e.prototype.hasEdge=function(e){return 1===this.edgesUsed[e.id]},e}(),ne=function(){function e(e){var t=e.graphs,r=e.minSupport,n=void 0===r?2:r,o=e.minNodeNum,i=void 0===o?1:o,a=e.maxNodeNum,d=void 0===a?4:a,u=e.top,s=void 0===u?10:u,c=e.directed,f=void 0!==c&&c,l=e.verbose,h=void 0!==l&&l;this.graphs=t,this.dfsCode=new te,this.support=0,this.frequentSize1Subgraphs=[],this.frequentSubgraphs=[],this.minSupport=n,this.top=s,this.directed=f,this.counter=0,this.maxNodeNum=d,this.minNodeNum=i,this.verbose=h,this.maxNodeNum<this.minNodeNum&&(this.maxNodeNum=this.minNodeNum),this.reportDF=[]}return e.prototype.findForwardRootEdges=function(e,t){var r=this,n=[],o=e.nodeMap;return t.edges.forEach((function(e){(r.directed||t.label<=o[e.to].label)&&n.push(e)})),n},e.prototype.findBackwardEdge=function(e,t,r,n){if(!this.directed&&t===r)return null;for(var o=e.nodeMap,i=o[r.to].edges,a=i.length,d=0;d<a;d++){var u=i[d];if(!n.hasEdge(u)&&u.to===t.from)if(this.directed){if(o[t.from].label<o[r.to].label||o[t.from].label===o[r.to].label&&t.label<=u.label)return u}else if(t.label<u.label||t.label===u.label&&o[t.to].label<=o[r.to].label)return u}return null},e.prototype.findForwardPureEdges=function(e,t,r,n){for(var o=[],i=t.to,a=e.nodeMap[i].edges,d=a.length,u=0;u<d;u++){var s=a[u],c=e.nodeMap[s.to];r<=c.label&&!n.hasNode(c)&&o.push(s)}return o},e.prototype.findForwardRmpathEdges=function(e,t,r,n){for(var o=[],i=e.nodeMap,a=i[t.to].label,d=i[t.from].edges,u=d.length,s=0;s<u;s++){var c=d[s],f=i[c.to].label;t.to===c.to||r>f||n.hasNode(i[c.to])||(t.label<c.label||t.label===c.label&&a<=f)&&o.push(c)}return o},e.prototype.getSupport=function(e){var t={};return e.forEach((function(e){t[e.graphId]||(t[e.graphId]=!0)})),Object.keys(t).length},e.prototype.findMinLabel=function(e){var t=void 0;return Object.keys(e).forEach((function(r){var n=e[r],o=n.nodeLabel1,i=n.edgeLabel,a=n.nodeLabel2;t?(o<t.nodeLabel1||o===t.nodeLabel1&&i<t.edgeLabel||o===t.nodeLabel1&&i===t.edgeLabel&&a<t.nodeLabel2)&&(t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}):t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}})),t},e.prototype.isMin=function(){var e=this,t=this.dfsCode;if(this.verbose&&console.log("isMin checking",t),1===t.dfsEdgeList.length)return!0;var r=this.directed,n=t.toGraph(-1,r),o=n.nodeMap,i=new te,a={};n.nodes.forEach((function(t){e.findForwardRootEdges(n,t).forEach((function(e){var r=o[e.to],i="".concat(t.label,"-").concat(e.label,"-").concat(r.label);a[i]||(a[i]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:r.label});var d={graphId:n.id,edge:e,preNode:null};a[i].projected.push(d)}))}));var d=this.findMinLabel(a);if(d){i.dfsEdgeList.push(new ee(0,1,d.nodeLabel1,d.edgeLabel,d.nodeLabel2));var u=function(a){for(var d=i.buildRmpath(),s=i.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,c=i.dfsEdgeList[d[0]].toNode,f={},l=!1,h=0,v=r?-1:0,p=function(t){if(l)return"break";a.forEach((function(r){var o=new re(r),a=e.findBackwardEdge(n,o.edges[d[t]],o.edges[d[0]],o);a&&(f[a.label]||(f[a.label]={projected:[],edgeLabel:a.label}),f[a.label].projected.push({graphId:n.id,edge:f,preNode:r}),h=i.dfsEdgeList[d[t]].fromNode,l=!0)}))},g=d.length-1;g>v&&"break"!==p(g);g--);if(l){var b=e.findMinLabel(f);i.dfsEdgeList.push(new ee(c,h,Y,b.edgeLabel,Y));var E=i.dfsEdgeList.length-1;return e.dfsCode.dfsEdgeList[E]===i.dfsEdgeList[E]&&u(f[b.edgeLabel].projected)}var y={};l=!1;var m=0;a.forEach((function(t){var r=new re(t),i=e.findForwardPureEdges(n,r.edges[d[0]],s,r);i.length>0&&(l=!0,m=c,i.forEach((function(e){var r="".concat(e.label,"-").concat(o[e.to].label);y[r]||(y[r]={projected:[],edgeLabel:e.label,nodeLabel2:o[e.to].label}),y[r].projected.push({graphId:n.id,edge:e,preNode:t})})))}));var L=d.length,N=function(t){if(l)return"break";var r=d[t];a.forEach((function(t){var a=new re(t),d=e.findForwardRmpathEdges(n,a.edges[r],s,a);d.length>0&&(l=!0,m=i.dfsEdgeList[r].fromNode,d.forEach((function(e){var r="".concat(e.label,"-").concat(o[e.to].label);y[r]||(y[r]={projected:[],edgeLabel:e.label,nodeLabel2:o[e.to].label}),y[r].projected.push({graphId:n.id,edge:e,preNode:t})})))}))};for(g=0;g<L&&"break"!==N(g);g++);if(!l)return!0;var w=e.findMinLabel(y);i.dfsEdgeList.push(new ee(m,c+1,Y,w.edgeLabel,w.nodeLabel2));var k=i.dfsEdgeList.length-1;return t.dfsEdgeList[k]===i.dfsEdgeList[k]&&u(y["".concat(w.edgeLabel,"-").concat(w.nodeLabel2)].projected)},s="".concat(d.nodeLabel1,"-").concat(d.edgeLabel,"-").concat(d.nodeLabel2);return u(a[s].projected)}},e.prototype.report=function(){if(!(this.dfsCode.getNodeNum()<this.minNodeNum)){this.counter++;var e=this.dfsCode.toGraph(this.counter,this.directed);this.frequentSubgraphs.push(M(e))}},e.prototype.subGraphMining=function(e){var t=this;if(!(this.getSupport(e)<this.minSupport)&&this.isMin()){this.report();var r=this.dfsCode.getNodeNum(),n=this.dfsCode.buildRmpath(),o=this.dfsCode.dfsEdgeList[n[0]].toNode,i=this.dfsCode.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,a={},d={};e.forEach((function(e){for(var u=t.graphs[e.graphId],s=u.nodeMap,c=new re(e),f=n.length-1;f>=0;f--){var l=t.findBackwardEdge(u,c.edges[n[f]],c.edges[n[0]],c);if(l){var h="".concat(t.dfsCode.dfsEdgeList[n[f]].fromNode,"-").concat(l.label);d[h]||(d[h]={projected:[],toNodeId:t.dfsCode.dfsEdgeList[n[f]].fromNode,edgeLabel:l.label}),d[h].projected.push({graphId:e.graphId,edge:l,preNode:e})}}if(!(r>=t.maxNodeNum)){t.findForwardPureEdges(u,c.edges[n[0]],i,c).forEach((function(t){var r="".concat(o,"-").concat(t.label,"-").concat(s[t.to].label);a[r]||(a[r]={projected:[],fromNodeId:o,edgeLabel:t.label,nodeLabel2:s[t.to].label}),a[r].projected.push({graphId:e.graphId,edge:t,preNode:e})}));var v=function(r){t.findForwardRmpathEdges(u,c.edges[n[r]],i,c).forEach((function(o){var i="".concat(t.dfsCode.dfsEdgeList[n[r]].fromNode,"-").concat(o.label,"-").concat(s[o.to].label);a[i]||(a[i]={projected:[],fromNodeId:t.dfsCode.dfsEdgeList[n[r]].fromNode,edgeLabel:o.label,nodeLabel2:s[o.to].label}),a[i].projected.push({graphId:e.graphId,edge:o,preNode:e})}))};for(f=0;f<n.length;f++)v(f)}})),Object.keys(d).forEach((function(e){var r=d[e],n=r.toNodeId,i=r.edgeLabel;t.dfsCode.dfsEdgeList.push(new ee(o,n,"-1",i,"-1")),t.subGraphMining(d[e].projected),t.dfsCode.dfsEdgeList.pop()})),Object.keys(a).forEach((function(e){var r=a[e],n=r.fromNodeId,i=r.edgeLabel,d=r.nodeLabel2;t.dfsCode.dfsEdgeList.push(new ee(n,o+1,Y,i,d)),t.subGraphMining(a[e].projected),t.dfsCode.dfsEdgeList.pop()}))}},e.prototype.generate1EdgeFrequentSubGraphs=function(){var e=this.graphs,t=this.directed,r=this.minSupport,n=this.frequentSize1Subgraphs,o={},i={},a={},d={};return Object.keys(e).forEach((function(r){var n=e[r],u=n.nodeMap;n.nodes.forEach((function(e,n){var s=e.label,c="".concat(r,"-").concat(s);if(!a[c]){var f=o[s]||0;f++,o[s]=f}a[c]={graphKey:r,label:s},e.edges.forEach((function(e){var n=s,o=u[e.to].label;if(!t&&n>o){var a=o;o=n,n=a}var c=e.label,f="".concat(r,"-").concat(n,"-").concat(c,"-").concat(o),l="".concat(n,"-").concat(c,"-").concat(o);if(!i[l]){var h=i[l]||0;h++,i[l]=h}d[f]={graphId:r,nodeLabel1:n,edgeLabel:c,nodeLabel2:o}}))}))})),Object.keys(o).forEach((function(e){if(!(o[e]<r)){var t={nodes:[],edges:[]};t.nodes.push({id:"0",label:e}),n.push(t)}})),n},e.prototype.run=function(){var e=this;if(this.frequentSize1Subgraphs=this.generate1EdgeFrequentSubGraphs(),!(this.maxNodeNum<2)){var t=this.graphs,r=(this.directed,{});Object.keys(t).forEach((function(n){var o=t[n],i=o.nodeMap;o.nodes.forEach((function(t){e.findForwardRootEdges(o,t).forEach((function(e){var o=i[e.to],a="".concat(t.label,"-").concat(e.label,"-").concat(o.label);r[a]||(r[a]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:o.label});var d={graphId:n,edge:e,preNode:null};r[a].projected.push(d)}))}))})),Object.keys(r).forEach((function(t){var n=r[t],o=n.projected,i=n.nodeLabel1,a=n.edgeLabel,d=n.nodeLabel2;e.dfsCode.dfsEdgeList.push(new ee(0,1,i,a,d)),e.subGraphMining(o),e.dfsCode.dfsEdgeList.pop()}))}},e}(),oe="cluster";var ie=function(e,t,r,n){void 0===r&&(r="cluster"),void 0===n&&(n=2);var o=[],i=e.nodes;return t.forEach((function(e,t){o.push(ae(i,e,t,r,n))})),o},ae=function(e,t,r,n,o){var i=[r],a=[],d={};return t.forEach((function(t,u){if(t<=o&&r!==u){i.push(u),a.push(e[u]);var s=e[u][n];d[s]?(d[s].count++,d[s].dists.push(t)):d[s]={count:1,dists:[t]}}})),Object.keys(d).forEach((function(e){d[e].dists=d[e].dists.sort((function(e,t){return e-t}))})),{nodeIdx:r,nodeId:e[r].id,nodeIdxs:i,neighbors:a,neighborNum:i.length-1,nodeLabelCountMap:d}},de=function(e,t,r,n){var o=r.nodes;return n||(n={}),Object.keys(e).forEach((function(i){var a,d;if(!n||!n[i]){n[i]={nodes:[],edges:[]};var u=e[i],s=null===(a=t[u.start])||void 0===a?void 0:a.nodeIdxs,c=null===(d=t[u.end])||void 0===d?void 0:d.nodeIdxs;if(s&&c){var f=new Set(c),l=s.filter((function(e){return f.has(e)}));if(l&&l.length){for(var h={},v=l.length,p=0;p<v;p++){var g=o[l[p]];n[i].nodes.push(g),h[g.id]=!0}r.edges.forEach((function(e){h[e.source]&&h[e.target]&&n[i].edges.push(e)}))}}}})),n},ue=function(e,t,r,n){var o,i,a={};e.nodes.forEach((function(e){a[e.id]=e}));var d=0;return!(null===(o=null==t?void 0:t.edges)||void 0===o?void 0:o.length)||(null===(i=null==t?void 0:t.nodes)||void 0===i?void 0:i.length)<2?0:(e.edges.forEach((function(e){var o=a[e.source][r],i=a[e.target][r],u=null==t?void 0:t.nodes[0][r],s=null==t?void 0:t.nodes[1][r],c=null==t?void 0:t.edges[0][n];e[n]===c&&(o===u&&i===s||o===s&&i===u)&&d++})),d)},se=function(e,t){var r={},n={};return e.forEach((function(e,o){r[e.id]={idx:o,node:e,degree:0,inDegree:0,outDegree:0};var i=e[t];n[i]||(n[i]=[]),n[i].push(e)})),{nodeMap:r,nodeLabelMap:n}},ce=function(e,t,r){var n={},o={};return e.forEach((function(e,i){n["".concat(s)]={idx:i,edge:e};var a=e[t];o[a]||(o[a]=[]),o[a].push(e);var d=r[e.source];d&&(d.degree++,d.outDegree++);var u=r[e.target];u&&(u.degree++,u.inDegree++)})),{edgeMap:n,edgeLabelMap:o}},fe=function(e,t,r){var n=t.length,o={};return t.forEach((function(t,i){for(var a=r?0:i+1,d=e[i].id,u=a;u<n;u++)if(i!==u){var s=e[u].id,c=t[u];o["".concat(d,"-").concat(s)]=c,r||(o["".concat(s,"-").concat(d)]=c)}})),o},le=function(e,t,r,n,o,i,a,d,u,s,c){var f,l="".concat(t.id,"-").concat(r.id);if(s&&s[l])return s[l];var h=c?c[l]:void 0;if(!h){var v=((f={})[l]={start:n[t.id].idx,end:n[r.id].idx,distance:o},f);h=(c=de(v,i,e,c))[l]}return ue(h,a,d,u)},he=function(e,t,r,n){var o,i,a,d=null===(o=e[t])||void 0===o?void 0:o.degree,u=null===(i=e[t])||void 0===i?void 0:i.inDegree,s=null===(a=e[t])||void 0===a?void 0:a.outDegree;return void 0===e[t]&&(d=1/0,u=1/0,s=1/0,n[t].forEach((function(e){var t=r[e.id].degree;d>t&&(d=t);var n=r[e.id].inDegree;u>n&&(u=n);var o=r[e.id].outDegree;s>o&&(s=o)})),e[t]={degree:d,inDegree:u,outDegree:s}),{minPatternNodeLabelDegree:d,minPatternNodeLabelInDegree:u,minPatternNodeLabelOutDegree:s}};var ve=function(){function e(e){void 0===e&&(e=10),this.linkedList=new i,this.maxStep=e}return Object.defineProperty(e.prototype,"length",{get:function(){return this.linkedList.toArray().length},enumerable:!1,configurable:!0}),e.prototype.isEmpty=function(){return!this.linkedList.head},e.prototype.isMaxStack=function(){return this.toArray().length>=this.maxStep},e.prototype.peek=function(){return this.isEmpty()?null:this.linkedList.head.value},e.prototype.push=function(e){this.linkedList.prepend(e),this.length>this.maxStep&&this.linkedList.deleteTail()},e.prototype.pop=function(){var e=this.linkedList.deleteHead();return e?e.value:null},e.prototype.toArray=function(){return this.linkedList.toArray().map((function(e){return e.value}))},e.prototype.clear=function(){for(;!this.isEmpty();)this.pop()},e}();const pe={getAdjMatrix:r,breadthFirstSearch:function(e,t,r,n){void 0===n&&(n=!0);var o=function(e){void 0===e&&(e={});var t,r=e,n=function(){},o=(t={},function(e){var r=e.next;return!t[r]&&(t[r]=!0,!0)});return r.allowTraversal=e.allowTraversal||o,r.enter=e.enter||n,r.leave=e.leave||n,r}(r),i=new a,u=e.edges,s=void 0===u?[]:u;i.enqueue(t);for(var c="",f=function(){var e=i.dequeue();o.enter({current:e,previous:c}),d(e,s,n?"target":void 0).forEach((function(t){o.allowTraversal({previous:c,current:e,next:t})&&i.enqueue(t)})),o.leave({current:e,previous:c}),c=e};!i.isEmpty();)f()},connectedComponent:f,getDegree:h,getInDegree:function(e,t){return l(e)[t]?l(e)[t].inDegree:0},getOutDegree:function(e,t){return l(e)[t]?l(e)[t].outDegree:0},detectCycle:E,detectDirectedCycle:E,detectAllCycles:function(e,t,r,n){return void 0===n&&(n=!0),t?b(e,r,n):g(e,r,n)},detectAllDirectedCycle:b,detectAllUndirectedCycle:g,depthFirstSearch:p,dijkstra:C,findAllPath:function(e,t,r,n){var o;if(t===r)return[[t]];var i=e.edges,a=void 0===i?[]:i,u=[t],s=((o={})[t]=!0,o),c=[],f=[],l=n?d(t,a,"target"):d(t,a);for(c.push(l);u.length>0&&c.length>0;){var h=c[c.length-1];if(h.length){var v=h.shift();if(v&&(u.push(v),s[v]=!0,l=n?d(v,a,"target"):d(v,a),c.push(l.filter((function(e){return!s[e]})))),u[u.length-1]===r){var p=u.map((function(e){return e}));f.push(p),g=u.pop(),s[g]=!1,c.pop()}}else{var g=u.pop();s[g]=!1,c.pop()}}return f},findShortestPath:function(e,t,r,n,o){var i=C(e,t,n,o),a=i.length,d=i.path,u=i.allPath;return{length:a[r],path:d[r],allPath:u[r]}},floydWarshall:A,labelPropagation:function(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n="weight"),void 0===o&&(o=1e3);var i=e.nodes,a=void 0===i?[]:i,d=e.edges,u=void 0===d?[]:d,c={},f={};a.forEach((function(e,t){var r=s();e.clusterId=r,c[r]={id:r,nodes:[e]},f[e.id]={node:e,idx:t}}));var l=r(e,t),h=[],v={};l.forEach((function(e,t){var r=0,n=a[t].id;v[n]={},e.forEach((function(e,t){if(e){r+=e;var o=a[t].id;v[n][o]=e}})),h.push(r)}));for(var p=0,g=function(){var e=!1;if(a.forEach((function(t){var r={};Object.keys(v[t.id]).forEach((function(e){var n=v[t.id][e],o=f[e].node.clusterId;r[o]||(r[o]=0),r[o]+=n}));var n=-1/0,o=[];if(Object.keys(r).forEach((function(e){n<r[e]?(n=r[e],o=[e]):n===r[e]&&o.push(e)})),1!==o.length||o[0]!==t.clusterId){var i=o.indexOf(t.clusterId);if(i>=0&&o.splice(i,1),o&&o.length){e=!0;var a=c[t.clusterId],d=a.nodes.indexOf(t);a.nodes.splice(d,1);var u=Math.floor(Math.random()*o.length),s=c[o[u]];s.nodes.push(t),t.clusterId=s.id}}})),!e)return"break";p++};p<o&&"break"!==g(););Object.keys(c).forEach((function(e){var t=c[e];t.nodes&&t.nodes.length||delete c[e]}));var b=[],E={};u.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=f[t].node.clusterId,a=f[r].node.clusterId,d="".concat(i,"---").concat(a);if(E[d])E[d].weight+=o,E[d].count++;else{var u={source:i,target:a,weight:o,count:1};E[d]=u,b.push(u)}}));var y=[];return Object.keys(c).forEach((function(e){y.push(c[e])})),{clusters:y,clusterEdges:b}},louvain:B,iLouvain:function(e,t,r,n,o,i,a,d){return void 0===t&&(t=!1),void 0===r&&(r="weight"),void 0===n&&(n=1e-4),void 0===o&&(o=void 0),void 0===i&&(i=[]),void 0===a&&(a=["id"]),void 0===d&&(d=1),B(e,t,r,n,!0,o,i,a,d)},kCore:function(e,t){var r;void 0===t&&(t=1);for(var n=M(e),o=n.nodes,i=void 0===o?[]:o,a=n.edges,d=void 0===a?[]:a,u=function(){var e=h({nodes:i,edges:d}),n=Object.keys(e);n.sort((function(t,r){var n,o;return(null===(n=e[t])||void 0===n?void 0:n.degree)-(null===(o=e[r])||void 0===o?void 0:o.degree)}));var o=n[0];if(!i.length||(null===(r=e[o])||void 0===r?void 0:r.degree)>=t)return"break";var a=i.findIndex((function(e){return e.id===o}));i.splice(a,1),d=d.filter((function(e){return!(e.source===o||e.target===o)}))};"break"!==u(););return{nodes:i,edges:d}},kMeans:function(e,t,r,n,o,i){void 0===t&&(t=3),void 0===r&&(r=void 0),void 0===n&&(n=[]),void 0===o&&(o=["id"]),void 0===i&&(i=q.EuclideanDistance);var a=e.nodes,d=void 0===a?[]:a,u=e.edges,s=void 0===u?[]:u,c={clusters:[{id:"0",nodes:d}],clusterEdges:[]};if(i===q.EuclideanDistance&&!d.every((function(e){return e.hasOwnProperty(r)})))return c;var f=[],l=[];if(i===q.EuclideanDistance&&(f=F(d,r),l=R(f,n,o)),!l.length)return c;for(var h=x(l.map((function(e){return e.join("")}))),v=Math.min(t,d.length,h.length),p=0;p<d.length;p++)d[p].originIndex=p;var g=[],b=[],E=[];for(p=0;p<v;p++)if(0===p){var y=Math.floor(Math.random()*d.length);i===q.EuclideanDistance?g[p]=l[y]:g[p]=[],b.push(y),E[p]=[d[y]],d[y].clusterId=String(p)}else{for(var m=-1/0,L=0,N=function(e){if(!b.includes(e)){for(var t=0,r=0;r<g.length;r++){var n=0;i===q.EuclideanDistance&&(n=G(l[d[e].originIndex],g[r],i)),t+=n}var o=t/g.length;o>m&&!g.find((function(t){return D(t,H(i,l,d[e].originIndex))}))&&(m=o,L=e)}},w=0;w<d.length;w++)N(w);g[p]=H(i,l,L),b.push(L),E[p]=[d[L]],d[L].clusterId=String(p)}for(var k=0;;){for(p=0;p<d.length;p++){var I=0,M=1/0;if(0!==k||!b.includes(p)){for(var j=0;j<g.length;j++){var O=0;i===q.EuclideanDistance&&(O=G(l[p],g[j],i)),O<M&&(M=O,I=j)}if(void 0!==d[p].clusterId)for(var S=E[Number(d[p].clusterId)].length-1;S>=0;S--)E[Number(d[p].clusterId)][S].id===d[p].id&&E[Number(d[p].clusterId)].splice(S,1);d[p].clusterId=String(I),E[I].push(d[p])}}var C=!1;for(p=0;p<E.length;p++){var P=E[p],A=new T([]);for(j=0;j<P.length;j++)A=A.add(new T(l[P[j].originIndex]));var U=A.avg(P.length);U.equal(new T(g[p]))||(C=!0,g[p]=U.getArr())}if(k++,d.every((function(e){return void 0!==e.clusterId}))&&C||k>=1e3)break}var z=[],B={};return s.forEach((function(e){var t,r,n=e.source,o=e.target,i=null===(t=d.find((function(e){return e.id===n})))||void 0===t?void 0:t.clusterId,a=null===(r=d.find((function(e){return e.id===o})))||void 0===r?void 0:r.clusterId,u="".concat(i,"---").concat(a);if(B[u])B[u].count++;else{var s={source:i,target:a,count:1};B[u]=s,z.push(s)}})),{clusters:E,clusterEdges:z}},cosineSimilarity:W,nodesCosineSimilarity:function(e,t,r,n,o){void 0===e&&(e=[]),void 0===r&&(r=void 0),void 0===n&&(n=[]),void 0===o&&(o=[]);var i=M(e.filter((function(e){return e.id!==t.id}))),a=e.findIndex((function(e){return e.id===t.id})),d=F(e,r),u=R(d,n,o),s=u[a],c=[];return i.forEach((function(e,r){if(e.id!==t.id){var n=u[r],o=W(n,s);c.push(o),e.cosineSimilarity=o}})),i.sort((function(e,t){return t.cosineSimilarity-e.cosineSimilarity})),{allCosineSimilarity:c,similarNodes:i}},minimumSpanningTree:function(e,t,r){return r?{prim:Q,kruskal:X}[r](e,t):X(e,t)},pageRank:function(e,t,r){"number"!=typeof t&&(t=1e-6),"number"!=typeof r&&(r=.85);for(var n,o=1,i=0,a=1e3,u=e.nodes,s=void 0===u?[]:u,c=e.edges,f=void 0===c?[]:c,l=s.length,v={},p={},g=0;g<l;++g)v[E=(w=s[g]).id]=1/l,p[E]=1/l;for(var b=h(e);a>0&&o>t;){for(i=0,g=0;g<l;++g){var E=(w=s[g]).id;if(n=0,0===b[w.id].inDegree)v[E]=0;else{for(var y=d(E,f,"source"),m=0;m<y.length;++m){var L=y[m],N=b[L].outDegree;N>0&&(n+=p[L]/N)}v[E]=r*n,i+=v[E]}}for(i=(1-i)/l,o=0,g=0;g<l;++g){var w;n=v[E=(w=s[g]).id]+i,o+=Math.abs(n-p[E]),p[E]=n}a-=1}return p},getNeighbors:d,Stack:ve,GADDI:function(e,t,r,n,o,i,a){var d;if(void 0===r&&(r=!1),void 0===i&&(i="cluster"),void 0===a&&(a="cluster"),e&&e.nodes){var u=e.nodes.length;if(u){var s=A(e,r),c=A(t,r),f=fe(e.nodes,s,r),l=fe(t.nodes,c,r),h=se(e.nodes,i),v=h.nodeMap,p=h.nodeLabelMap,g=se(t.nodes,i),b=g.nodeMap,E=g.nodeLabelMap;ce(e.edges,a,v);var m=ce(t.edges,a,b).edgeLabelMap,L=[];null==c||c.forEach((function(e){L=L.concat(e)})),o||(o=Math.max.apply(Math,y(y([],L,!1),[2],!1))),n||(n=o);var N=ie(e,s,i,n),w=ie(t,c,i,n),k=function(e,t,r,n,o){var i=Math.ceil(r/t),a={},d=0;return n.forEach((function(e,n){for(var u=0,s=0,c=e.nodeIdxs,f=e.neighborNum-1;u<i;){for(var l=c[1+Math.floor(Math.random()*f)],h=0;(a["".concat(n,"-").concat(l)]||a["".concat(l,"-").concat(n)])&&(l=Math.floor(Math.random()*t),!(++h>2*t)););if(h<2*t&&(a["".concat(n,"-").concat(l)]={start:n,end:l,distance:o[n][l]},u++,++d>=r))return a;if(++s>2*t)break}u<i&&(i=(i+(i-u))/(t-n-1))})),a}(0,u,Math.min(100,u*(u-1)/2),N,s),I=de(k,N,e),x=function(e){var t=e.graphs,r=e.directed,n=void 0!==r&&r,o=e.nodeLabelProp,i=void 0===o?oe:o,a=e.edgeLabelProp,d=void 0===a?oe:a,u=function(e,t,r,n){var o={};return Object.keys(e).forEach((function(i,a){var d=e[i],u=new _(a,!0,t),s={};d.nodes.forEach((function(e,t){u.addNode(t,e[r]),s[e.id]=t})),d.edges.forEach((function(e,t){var r=s[e.source],o=s[e.target];u.addEdge(-1,r,o,e[n])})),u&&u.getNodeNum()&&(o[u.id]=u)})),o}(t,n,i,d),s=e.minSupport,c=e.maxNodeNum,f=e.minNodeNum,l=e.verbose,h=e.top,v=new ne({graphs:u,minSupport:s,maxNodeNum:c,minNodeNum:f,top:h,verbose:l,directed:n});v.run();var p=function(e,t,r){var n=[];return e.forEach((function(e){var o={nodes:[],edges:[]};e.nodes.forEach((function(e){var r;o.nodes.push(((r={id:"".concat(e.id)})[t]=e.label,r))})),e.edges.forEach((function(e){var t;o.edges.push(((t={source:"".concat(e.from),target:"".concat(e.to)})[r]=e.label,t))})),n.push(o)})),n}(v.frequentSubgraphs,i,d);return p}({graphs:I,nodeLabelProp:i,edgeLabelProp:a,minSupport:1,minNodeNum:1,maxNodeNum:4,directed:r}).slice(0,10),M=x.length,j=[];x.forEach((function(e,t){j[t]={},Object.keys(I).forEach((function(r){var n=I[r],o=ue(n,e,i,a);j[t][r]=o}))}));var O=function(e,t,r){for(var n=1/0,o=0,i=function(t){var r=e[t],i=Object.keys(r).sort((function(e,t){return r[e]-r[t]})),a=[];i.forEach((function(e,t){a[t%10]||(a[t%10]={graphs:[],totalCount:0,aveCount:0}),a[t%10].graphs.push(e),a[t%10].totalCount+=r[e]}));var d=0,u=[];a.forEach((function(e){var t=e.totalCount/e.graphs.length;e.aveCount=t,u.push(t);var n=0,o=e.length;e.graphs.forEach((function(t,o){var i=r[t];e.graphs.forEach((function(e,t){o!==t&&(n+=Math.abs(i-r[e]))}))})),d+=n/=o*(o-1)/2})),d/=a.length;var s=0;u.forEach((function(e,t){u.forEach((function(r,n){t!==n&&(s+=Math.abs(e-r))})),s/=u.length*(u.length-1)/2}));var c=s-d;n<c&&(n=c,o=t)},a=0;a<t;a++)i(a);return{structure:r[o],structureCountMap:e[o]}}(j,M,x),D=O.structure,S=O.structureCountMap,P=t.nodes[0],T=[],q=null===(d=t.nodes[0])||void 0===d?void 0:d[i],F=-1/0;t.nodes.forEach((function(e){var t=e[i],r=p[t];(null==r?void 0:r.length)>F&&(F=r.length,T=r,q=t,P=e)}));var R={},G={},U={},z={},B={},H={};Object.keys(E).forEach((function(n,o){B[n]=[],r&&(H[n]=[]);var d=-1/0,u=E[n],s={};u.forEach((function(e){var t=l["".concat(P.id,"-").concat(e.id)];if(t&&B[n].push(t),d<t&&(d=t),s["".concat(P.id,"-").concat(e.id)]={start:0,end:b[e.id].idx,distance:t},r){var o=l["".concat(e.id,"-").concat(P.id)];o&&H[n].push(o)}})),B[n]=B[n].sort((function(e,t){return e-t})),r&&(H[n]=H[n].sort((function(e,t){return e-t}))),G=de(s,w,t,G);var c=[];if(Object.keys(s).forEach((function(e){if(U[e])c.push(U[e]);else{var t=G[e];U[e]=ue(t,D,i,a),c.push(U[e])}})),c=c.sort((function(e,t){return t-e})),z["".concat(P.id,"-").concat(n)]=c,n!==q)for(var h=function(t){var r=T[t],o=N[v[r.id].idx],d=o.nodeLabelCountMap[n],u=E[n].length;if(!d||d.count<u)return T.splice(t,1),"continue";for(var s=!1,l=0;l<u;l++)if(d.dists[l]>B[n][l]){s=!0;break}if(s)return T.splice(t,1),"continue";var h={};o.neighbors.forEach((function(e){var t=f["".concat(r.id,"-").concat(e.id)];h["".concat(r.id,"-").concat(e.id)]={start:v[r.id].idx,end:v[e.id].idx,distance:t}})),I=de(h,N,e,I);var p=[];Object.keys(h).forEach((function(e){if(S[e])p.push(S[e]);else{var t=I[e];S[e]=ue(t,D,i,a),p.push(S[e])}})),p=p.sort((function(e,t){return t-e}));var g=!1;for(l=0;l<u;l++)if(p[l]<c[l]){g=!0;break}return g?(T.splice(t,1),"continue"):void 0},p=((null==T?void 0:T.length)||0)-1;p>=0;p--)h(p)}));var W=[];null==T||T.forEach((function(n){for(var d=v[n.id].idx,u=ae(e.nodes,s[d],d,i,o).neighbors,c=!1,l=u.length-1;l>=0;l--){if(u.length+1<t.nodes.length)return void(c=!0);var h=u[l],p=h[i];if(E[p]&&E[p].length)if(B[p]&&B[p].length){var g="".concat(n.id,"-").concat(h.id),y=f[g],m=B[p].length-1;if(y>B[p][m])u.splice(l,1);else{if(r){var L="".concat(h.id,"-").concat(n.id),w=f[L];if(m=H[p].length-1,w>H[p][m]){u.splice(l,1);continue}}var k=S[g]?S[g]:le(e,n,h,v,y,N,D,i,a,S,I),x="".concat(P.id,"-").concat(p);if(k<z[x][z[x].length-1])u.splice(l,1);else{var M=he(R,p,b,E),j=M.minPatternNodeLabelDegree;M.minPatternNodeLabelInDegree,M.minPatternNodeLabelOutDegree,v[h.id].degree<j&&u.splice(l,1)}}}else u.splice(l,1);else u.splice(l,1)}c||W.push({nodes:[n].concat(u)})}));var K=C(t,P.id,!1).length,V={};r?(Object.keys(K).forEach((function(e){var t=b[e].node[i];V[t]?V[t].push(K[e]):V[t]=[K[e]]})),Object.keys(V).forEach((function(e){V[e].sort((function(e,t){return e-t}))}))):V=B;for(var J=function(n){var o=W[n],d=o.nodes[0],u={},s={};o.nodes.forEach((function(e,t){s[e.id]={idx:t,node:e,degree:0,inDegree:0,outDegree:0};var r=e[i];u[r]?u[r]++:u[r]=1}));var c=[],f={};e.edges.forEach((function(e){s[e.source]&&s[e.target]&&(c.push(e),f[e[a]]?f[e[a]]++:f[e[a]]=1,s[e.source].degree++,s[e.target].degree++,s[e.source].outDegree++,s[e.target].inDegree++)}));for(var l=Object.keys(m).length,h=!1,p=0;p<l;p++){var g=Object.keys(m)[p];if(!f[g]||f[g]<m[g].length){h=!0;break}}if(h)return W.splice(n,1),"continue";var y=c.length;if(y<t.edges.length)return W.splice(n,1),"break";var L=!1,N=function(e){var t=c[e],n=t[a],o=m[n];if(!o||!o.length)return f[n]--,o&&f[n]<o.length?(L=!0,"break"):(c.splice(e,1),s[t.source].degree--,s[t.target].degree--,s[t.source].outDegree--,s[t.target].inDegree--,"continue");var d=s[t.source].node[i],u=s[t.target].node[i],l=!1;return o.forEach((function(e){var t=b[e.source].node,n=b[e.target].node;t[i]===d&&n[i]===u&&(l=!0),r||t[i]!==u||n[i]!==d||(l=!0)})),l?void 0:(f[n]--,o&&f[n]<o.length?(L=!0,"break"):(c.splice(e,1),s[t.source].degree--,s[t.target].degree--,s[t.source].outDegree--,s[t.target].inDegree--,"continue"))};for(p=y-1;p>=0&&"break"!==N(p);p--);if(L)return W.splice(n,1),"continue";o.edges=c;var w=C(o,o.nodes[0].id,!1).length;if(Object.keys(w).reverse().forEach((function(e){if(e!==o.nodes[0].id&&!L){if(w[e]===1/0){var t=s[e].node[i];if(u[t]--,u[t]<E[t].length)return void(L=!0);var r=o.nodes.indexOf(s[e].node);return o.nodes.splice(r,1),void(s[e]=void 0)}var n=v[e].node[i];if(!V[n]||!V[n].length||w[e]>V[n][V[n].length-1]){if(t=s[e].node[i],u[t]--,u[t]<E[t].length)return void(L=!0);r=o.nodes.indexOf(s[e].node),o.nodes.splice(r,1),s[e]=void 0}}})),L)return W.splice(n,1),"continue";for(var k=!0,I=0;k&&!L;){if(k=!1,r?s[d.id].degree<b[P.id].degree||s[d.id].inDegree<b[P.id].inDegree||s[d.id].outDegree<b[P.id].outDegree:s[d.id].degree<b[P.id].degree){L=!0;break}if(u[d[i]]<E[d[i]].length){L=!0;break}for(var x=o.nodes.length-1;x>=0;x--){var M=o.nodes[x],j=s[M.id].degree,O=s[M.id].inDegree,D=s[M.id].outDegree,S=M[i],A=he(R,S,b,E),T=A.minPatternNodeLabelDegree,q=A.minPatternNodeLabelInDegree,F=A.minPatternNodeLabelOutDegree;if(r?j<T||O<q||D<F:j<T){if(u[M[i]]--,u[M[i]]<E[M[i]].length){L=!0;break}o.nodes.splice(x,1),s[M.id]=void 0,k=!0}}if(L||!k&&0!==I)break;for(var G=(y=c.length)-1;G>=0;G--){var U=c[G];if(!s[U.source]||!s[U.target]){c.splice(G,1);var z=U[a];if(f[z]--,s[U.source]&&(s[U.source].degree--,s[U.source].outDegree--),s[U.target]&&(s[U.target].degree--,s[U.target].inDegree--),m[z]&&f[z]<m[z].length){L=!0;break}k=!0}}I++}return L||L||o.nodes.length<t.nodes.length||c.length<t.edges.length?(W.splice(n,1),"continue"):void 0},Q=W.length-1;Q>=0&&"break"!==J(Q);Q--);var X=W.length,Y=function(e){var t=W[e],r={};t.edges.forEach((function(e){var t="".concat(e.source,"-").concat(e.target,"-").concat(e.label);r[t]?r[t]++:r[t]=1}));for(var n=function(e){var t=W[e],n={};t.edges.forEach((function(e){var t="".concat(e.source,"-").concat(e.target,"-").concat(e.label);n[t]?n[t]++:n[t]=1}));var o=!0;Object.keys(n).length!==Object.keys(r).length?o=!1:Object.keys(r).forEach((function(e){n[e]!==r[e]&&(o=!1)})),o&&W.splice(e,1)},o=X-1;o>e;o--)n(o);X=W.length};for(Q=0;Q<=X-1;Q++)Y(Q);return W}}}};return t.default})()));
//# sourceMappingURL=index.min.js.map