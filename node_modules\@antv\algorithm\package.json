{"name": "@antv/algorithm", "version": "0.1.26", "description": "graph algorithm", "keywords": ["graph", "algorithm", "antv", "G6"], "files": ["package.json", "es", "lib", "dist", "LICENSE", "README.md"], "main": "lib/index.js", "module": "es/index.js", "types": "lib/index.d.ts", "unpkg": "dist/index.min.js", "scripts": {"build": "npm run clean && father build && npm run build:umd", "build:umd": "webpack --config webpack.config.js --mode production", "dev:umd": "webpack --config webpack.dev.config.js --mode development", "ci": "npm run build && npm run coverage", "clean": "rimraf es lib dist", "coverage": "jest --coverage", "lint": "eslint --ext .js,.jsx,.ts,.tsx --format=pretty \"./\"", "lint:src": "eslint --ext .ts --format=pretty \"./src\"", "prettier": "prettier -c --write \"**/*\"", "test": "npm run build:umd && jest", "test-live": "npm run build:umd && DEBUG_MODE=1 jest --watch ./tests/unit/louvain-spec.ts", "test-live:async": "npm run build:umd && DEBUG_MODE=1 jest --watch ./tests/unit/louvain-async-spec.ts", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx", "cdn": "antv-bin upload -n @antv/algorithm"}, "homepage": "https://g6.antv.vision", "bugs": {"url": "https://github.com/antvis/algorithm/issues"}, "repository": {"type": "git", "url": "https://github.com/antvis/algorithm"}, "license": "MIT", "author": "https://github.com/orgs/antvis/people", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/preset-env": "^7.12.7", "@babel/preset-typescript": "^7.12.7", "@types/jest": "^26.0.18", "@umijs/fabric": "^2.5.6", "babel-loader": "^8.2.2", "father": "^2.30.0", "jest": "^26.6.3", "jest-electron": "^0.1.11", "rimraf": "^3.0.2", "ts-jest": "^26.4.4", "ts-loader": "^8.0.14", "tslint": "^6.1.3", "typescript": "^4.1.3", "webpack": "^5.17.0", "webpack-cli": "^4.9.1", "worker-loader": "^3.0.7"}, "dependencies": {"@antv/util": "^2.0.13", "tslib": "^2.0.0"}}