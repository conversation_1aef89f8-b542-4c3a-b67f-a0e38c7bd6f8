<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>JSAPI Loader</title>
    <style>
        html,
        body,
        #container {
            height: 100%;
            width: 100%;
            margin: 0;
        }
    </style>
</head>

<body>
    <div id="container" tabindex="0"></div>

    <script src="../dist/index.js"></script>
    <script>
        AMapLoader.load({
            key: '',//首次load必填
            version: '2.0Beta',
            plugins: ['AMap.Scale'],
            AMapUI: {
                plugins: ['overlay/SimpleMarker'],
            }
        }).then((AMap) => {
            map = new AMap.Map('container');
            map.addControl(new AMap.Scale());
            new AMapUI.SimpleMarker({

                //前景文字
                iconLabel: 'A',

                //图标主题
                iconTheme: 'default',

                //背景图标样式
                iconStyle: 'red',

                //...其他Marker选项...，不包括content
                map: map,
                position: map.getCenter()
            });
        }).catch((e) => {
            console.error(e);
        });
        setTimeout(function () {
            AMapLoader.load({
                plugins: ['AMap.MapType']
            }).then((AMap) => {
                map.addControl(new AMap.MapType())
            }).catch((e) => {
                console.error(e);
            });
        }, 5000);

    </script>


    </script>
</body>

</html>