{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nfunction dataIndexMapValueLength(valNumOrArrLengthMoreThan2) {\n  return valNumOrArrLengthMoreThan2 == null ? 0 : valNumOrArrLengthMoreThan2.length || 1;\n}\nfunction defaultKeyGetter(item) {\n  return item;\n}\nvar DataDiffer = /** @class */function () {\n  /**\n   * @param context Can be visited by this.context in callback.\n   */\n  function DataDiffer(oldArr, newArr, oldKeyGetter, newKeyGetter, context,\n  // By default: 'oneToOne'.\n  diffMode) {\n    this._old = oldArr;\n    this._new = newArr;\n    this._oldKeyGetter = oldKeyGetter || defaultKeyGetter;\n    this._newKeyGetter = newKeyGetter || defaultKeyGetter;\n    // Visible in callback via `this.context`;\n    this.context = context;\n    this._diffModeMultiple = diffMode === 'multiple';\n  }\n  /**\n   * Callback function when add a data\n   */\n  DataDiffer.prototype.add = function (func) {\n    this._add = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data\n   */\n  DataDiffer.prototype.update = function (func) {\n    this._update = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\n   */\n  DataDiffer.prototype.updateManyToOne = function (func) {\n    this._updateManyToOne = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\n   */\n  DataDiffer.prototype.updateOneToMany = function (func) {\n    this._updateOneToMany = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\n   */\n  DataDiffer.prototype.updateManyToMany = function (func) {\n    this._updateManyToMany = func;\n    return this;\n  };\n  /**\n   * Callback function when remove a data\n   */\n  DataDiffer.prototype.remove = function (func) {\n    this._remove = func;\n    return this;\n  };\n  DataDiffer.prototype.execute = function () {\n    this[this._diffModeMultiple ? '_executeMultiple' : '_executeOneToOne']();\n  };\n  DataDiffer.prototype._executeOneToOne = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var newDataIndexMap = {};\n    var oldDataKeyArr = new Array(oldArr.length);\n    var newDataKeyArr = new Array(newArr.length);\n    this._initIndexMap(oldArr, null, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      // idx can never be empty array here. see 'set null' logic below.\n      if (newIdxMapValLen > 1) {\n        // Consider there is duplicate key (for example, use dataItem.name as key).\n        // We should make sure every item in newArr and oldArr can be visited.\n        var newIdx = newIdxMapVal.shift();\n        if (newIdxMapVal.length === 1) {\n          newDataIndexMap[oldKey] = newIdxMapVal[0];\n        }\n        this._update && this._update(newIdx, i);\n      } else if (newIdxMapValLen === 1) {\n        newDataIndexMap[oldKey] = null;\n        this._update && this._update(newIdxMapVal, i);\n      } else {\n        this._remove && this._remove(i);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  /**\n   * For example, consider the case:\n   * oldData: [o0, o1, o2, o3, o4, o5, o6, o7],\n   * newData: [n0, n1, n2, n3, n4, n5, n6, n7, n8],\n   * Where:\n   *     o0, o1, n0 has key 'a' (many to one)\n   *     o5, n4, n5, n6 has key 'b' (one to many)\n   *     o2, n1 has key 'c' (one to one)\n   *     n2, n3 has key 'd' (add)\n   *     o3, o4 has key 'e' (remove)\n   *     o6, o7, n7, n8 has key 'f' (many to many, treated as add and remove)\n   * Then:\n   *     (The order of the following directives are not ensured.)\n   *     this._updateManyToOne(n0, [o0, o1]);\n   *     this._updateOneToMany([n4, n5, n6], o5);\n   *     this._update(n1, o2);\n   *     this._remove(o3);\n   *     this._remove(o4);\n   *     this._remove(o6);\n   *     this._remove(o7);\n   *     this._add(n2);\n   *     this._add(n3);\n   *     this._add(n7);\n   *     this._add(n8);\n   */\n  DataDiffer.prototype._executeMultiple = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var oldDataIndexMap = {};\n    var newDataIndexMap = {};\n    var oldDataKeyArr = [];\n    var newDataKeyArr = [];\n    this._initIndexMap(oldArr, oldDataIndexMap, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldDataKeyArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var oldIdxMapVal = oldDataIndexMap[oldKey];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var oldIdxMapValLen = dataIndexMapValueLength(oldIdxMapVal);\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (oldIdxMapValLen > 1 && newIdxMapValLen === 1) {\n        this._updateManyToOne && this._updateManyToOne(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen > 1) {\n        this._updateOneToMany && this._updateOneToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen === 1) {\n        this._update && this._update(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1 && newIdxMapValLen > 1) {\n        this._updateManyToMany && this._updateManyToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1) {\n        for (var i_1 = 0; i_1 < oldIdxMapValLen; i_1++) {\n          this._remove && this._remove(oldIdxMapVal[i_1]);\n        }\n      } else {\n        this._remove && this._remove(oldIdxMapVal);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  DataDiffer.prototype._performRestAdd = function (newDataKeyArr, newDataIndexMap) {\n    for (var i = 0; i < newDataKeyArr.length; i++) {\n      var newKey = newDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[newKey];\n      var idxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (idxMapValLen > 1) {\n        for (var j = 0; j < idxMapValLen; j++) {\n          this._add && this._add(newIdxMapVal[j]);\n        }\n      } else if (idxMapValLen === 1) {\n        this._add && this._add(newIdxMapVal);\n      }\n      // Support both `newDataKeyArr` are duplication removed or not removed.\n      newDataIndexMap[newKey] = null;\n    }\n  };\n  DataDiffer.prototype._initIndexMap = function (arr,\n  // Can be null.\n  map,\n  // In 'byKey', the output `keyArr` is duplication removed.\n  // In 'byIndex', the output `keyArr` is not duplication removed and\n  //     its indices are accurately corresponding to `arr`.\n  keyArr, keyGetterName) {\n    var cbModeMultiple = this._diffModeMultiple;\n    for (var i = 0; i < arr.length; i++) {\n      // Add prefix to avoid conflict with Object.prototype.\n      var key = '_ec_' + this[keyGetterName](arr[i], i);\n      if (!cbModeMultiple) {\n        keyArr[i] = key;\n      }\n      if (!map) {\n        continue;\n      }\n      var idxMapVal = map[key];\n      var idxMapValLen = dataIndexMapValueLength(idxMapVal);\n      if (idxMapValLen === 0) {\n        // Simple optimize: in most cases, one index has one key,\n        // do not need array.\n        map[key] = i;\n        if (cbModeMultiple) {\n          keyArr.push(key);\n        }\n      } else if (idxMapValLen === 1) {\n        map[key] = [idxMapVal, i];\n      } else {\n        idxMapVal.push(i);\n      }\n    }\n  };\n  return DataDiffer;\n}();\nexport default DataDiffer;", "map": {"version": 3, "names": ["dataIndexMapValueLength", "valNumOrArrLengthMoreThan2", "length", "defaultKeyGetter", "item", "<PERSON><PERSON><PERSON><PERSON>", "oldArr", "newArr", "oldKeyGetter", "newKeyGetter", "context", "diffMode", "_old", "_new", "_old<PERSON>eyG<PERSON>r", "_newKeyGetter", "_diffModeMultiple", "prototype", "add", "func", "_add", "update", "_update", "updateManyToOne", "_updateManyToOne", "updateOneToMany", "_updateOneToMany", "updateManyToMany", "_updateManyToMany", "remove", "_remove", "execute", "_executeOneToOne", "newDataIndexMap", "oldDataKeyArr", "Array", "newDataKeyArr", "_initIndexMap", "i", "<PERSON><PERSON><PERSON>", "newIdxMapVal", "newIdxMapValLen", "newIdx", "shift", "_performRestAdd", "_executeMultiple", "oldDataIndexMap", "oldIdxMapVal", "oldIdxMapValLen", "i_1", "new<PERSON>ey", "idxMapValLen", "j", "arr", "map", "keyArr", "keyGetterName", "cbModeMultiple", "key", "idxMapVal", "push"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/echarts/lib/data/DataDiffer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nfunction dataIndexMapValueLength(valNumOrArrLengthMoreThan2) {\n  return valNumOrArrLengthMoreThan2 == null ? 0 : valNumOrArrLengthMoreThan2.length || 1;\n}\nfunction defaultKeyGetter(item) {\n  return item;\n}\nvar DataDiffer = /** @class */function () {\n  /**\n   * @param context Can be visited by this.context in callback.\n   */\n  function DataDiffer(oldArr, newArr, oldKeyGetter, newKeyGetter, context,\n  // By default: 'oneToOne'.\n  diffMode) {\n    this._old = oldArr;\n    this._new = newArr;\n    this._oldKeyGetter = oldKeyGetter || defaultKeyGetter;\n    this._newKeyGetter = newKeyGetter || defaultKeyGetter;\n    // Visible in callback via `this.context`;\n    this.context = context;\n    this._diffModeMultiple = diffMode === 'multiple';\n  }\n  /**\n   * Callback function when add a data\n   */\n  DataDiffer.prototype.add = function (func) {\n    this._add = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data\n   */\n  DataDiffer.prototype.update = function (func) {\n    this._update = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\n   */\n  DataDiffer.prototype.updateManyToOne = function (func) {\n    this._updateManyToOne = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\n   */\n  DataDiffer.prototype.updateOneToMany = function (func) {\n    this._updateOneToMany = func;\n    return this;\n  };\n  /**\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\n   */\n  DataDiffer.prototype.updateManyToMany = function (func) {\n    this._updateManyToMany = func;\n    return this;\n  };\n  /**\n   * Callback function when remove a data\n   */\n  DataDiffer.prototype.remove = function (func) {\n    this._remove = func;\n    return this;\n  };\n  DataDiffer.prototype.execute = function () {\n    this[this._diffModeMultiple ? '_executeMultiple' : '_executeOneToOne']();\n  };\n  DataDiffer.prototype._executeOneToOne = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var newDataIndexMap = {};\n    var oldDataKeyArr = new Array(oldArr.length);\n    var newDataKeyArr = new Array(newArr.length);\n    this._initIndexMap(oldArr, null, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      // idx can never be empty array here. see 'set null' logic below.\n      if (newIdxMapValLen > 1) {\n        // Consider there is duplicate key (for example, use dataItem.name as key).\n        // We should make sure every item in newArr and oldArr can be visited.\n        var newIdx = newIdxMapVal.shift();\n        if (newIdxMapVal.length === 1) {\n          newDataIndexMap[oldKey] = newIdxMapVal[0];\n        }\n        this._update && this._update(newIdx, i);\n      } else if (newIdxMapValLen === 1) {\n        newDataIndexMap[oldKey] = null;\n        this._update && this._update(newIdxMapVal, i);\n      } else {\n        this._remove && this._remove(i);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  /**\n   * For example, consider the case:\n   * oldData: [o0, o1, o2, o3, o4, o5, o6, o7],\n   * newData: [n0, n1, n2, n3, n4, n5, n6, n7, n8],\n   * Where:\n   *     o0, o1, n0 has key 'a' (many to one)\n   *     o5, n4, n5, n6 has key 'b' (one to many)\n   *     o2, n1 has key 'c' (one to one)\n   *     n2, n3 has key 'd' (add)\n   *     o3, o4 has key 'e' (remove)\n   *     o6, o7, n7, n8 has key 'f' (many to many, treated as add and remove)\n   * Then:\n   *     (The order of the following directives are not ensured.)\n   *     this._updateManyToOne(n0, [o0, o1]);\n   *     this._updateOneToMany([n4, n5, n6], o5);\n   *     this._update(n1, o2);\n   *     this._remove(o3);\n   *     this._remove(o4);\n   *     this._remove(o6);\n   *     this._remove(o7);\n   *     this._add(n2);\n   *     this._add(n3);\n   *     this._add(n7);\n   *     this._add(n8);\n   */\n  DataDiffer.prototype._executeMultiple = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var oldDataIndexMap = {};\n    var newDataIndexMap = {};\n    var oldDataKeyArr = [];\n    var newDataKeyArr = [];\n    this._initIndexMap(oldArr, oldDataIndexMap, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldDataKeyArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var oldIdxMapVal = oldDataIndexMap[oldKey];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var oldIdxMapValLen = dataIndexMapValueLength(oldIdxMapVal);\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (oldIdxMapValLen > 1 && newIdxMapValLen === 1) {\n        this._updateManyToOne && this._updateManyToOne(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen > 1) {\n        this._updateOneToMany && this._updateOneToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen === 1) {\n        this._update && this._update(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1 && newIdxMapValLen > 1) {\n        this._updateManyToMany && this._updateManyToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1) {\n        for (var i_1 = 0; i_1 < oldIdxMapValLen; i_1++) {\n          this._remove && this._remove(oldIdxMapVal[i_1]);\n        }\n      } else {\n        this._remove && this._remove(oldIdxMapVal);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  DataDiffer.prototype._performRestAdd = function (newDataKeyArr, newDataIndexMap) {\n    for (var i = 0; i < newDataKeyArr.length; i++) {\n      var newKey = newDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[newKey];\n      var idxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (idxMapValLen > 1) {\n        for (var j = 0; j < idxMapValLen; j++) {\n          this._add && this._add(newIdxMapVal[j]);\n        }\n      } else if (idxMapValLen === 1) {\n        this._add && this._add(newIdxMapVal);\n      }\n      // Support both `newDataKeyArr` are duplication removed or not removed.\n      newDataIndexMap[newKey] = null;\n    }\n  };\n  DataDiffer.prototype._initIndexMap = function (arr,\n  // Can be null.\n  map,\n  // In 'byKey', the output `keyArr` is duplication removed.\n  // In 'byIndex', the output `keyArr` is not duplication removed and\n  //     its indices are accurately corresponding to `arr`.\n  keyArr, keyGetterName) {\n    var cbModeMultiple = this._diffModeMultiple;\n    for (var i = 0; i < arr.length; i++) {\n      // Add prefix to avoid conflict with Object.prototype.\n      var key = '_ec_' + this[keyGetterName](arr[i], i);\n      if (!cbModeMultiple) {\n        keyArr[i] = key;\n      }\n      if (!map) {\n        continue;\n      }\n      var idxMapVal = map[key];\n      var idxMapValLen = dataIndexMapValueLength(idxMapVal);\n      if (idxMapValLen === 0) {\n        // Simple optimize: in most cases, one index has one key,\n        // do not need array.\n        map[key] = i;\n        if (cbModeMultiple) {\n          keyArr.push(key);\n        }\n      } else if (idxMapValLen === 1) {\n        map[key] = [idxMapVal, i];\n      } else {\n        idxMapVal.push(i);\n      }\n    }\n  };\n  return DataDiffer;\n}();\nexport default DataDiffer;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAACC,0BAA0B,EAAE;EAC3D,OAAOA,0BAA0B,IAAI,IAAI,GAAG,CAAC,GAAGA,0BAA0B,CAACC,MAAM,IAAI,CAAC;AACxF;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,OAAOA,IAAI;AACb;AACA,IAAIC,UAAU,GAAG,aAAa,YAAY;EACxC;AACF;AACA;EACE,SAASA,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAEC,YAAY,EAAEC,OAAO;EACvE;EACAC,QAAQ,EAAE;IACR,IAAI,CAACC,IAAI,GAAGN,MAAM;IAClB,IAAI,CAACO,IAAI,GAAGN,MAAM;IAClB,IAAI,CAACO,aAAa,GAAGN,YAAY,IAAIL,gBAAgB;IACrD,IAAI,CAACY,aAAa,GAAGN,YAAY,IAAIN,gBAAgB;IACrD;IACA,IAAI,CAACO,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,iBAAiB,GAAGL,QAAQ,KAAK,UAAU;EAClD;EACA;AACF;AACA;EACEN,UAAU,CAACY,SAAS,CAACC,GAAG,GAAG,UAAUC,IAAI,EAAE;IACzC,IAAI,CAACC,IAAI,GAAGD,IAAI;IAChB,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACEd,UAAU,CAACY,SAAS,CAACI,MAAM,GAAG,UAAUF,IAAI,EAAE;IAC5C,IAAI,CAACG,OAAO,GAAGH,IAAI;IACnB,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACEd,UAAU,CAACY,SAAS,CAACM,eAAe,GAAG,UAAUJ,IAAI,EAAE;IACrD,IAAI,CAACK,gBAAgB,GAAGL,IAAI;IAC5B,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACEd,UAAU,CAACY,SAAS,CAACQ,eAAe,GAAG,UAAUN,IAAI,EAAE;IACrD,IAAI,CAACO,gBAAgB,GAAGP,IAAI;IAC5B,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACEd,UAAU,CAACY,SAAS,CAACU,gBAAgB,GAAG,UAAUR,IAAI,EAAE;IACtD,IAAI,CAACS,iBAAiB,GAAGT,IAAI;IAC7B,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACEd,UAAU,CAACY,SAAS,CAACY,MAAM,GAAG,UAAUV,IAAI,EAAE;IAC5C,IAAI,CAACW,OAAO,GAAGX,IAAI;IACnB,OAAO,IAAI;EACb,CAAC;EACDd,UAAU,CAACY,SAAS,CAACc,OAAO,GAAG,YAAY;IACzC,IAAI,CAAC,IAAI,CAACf,iBAAiB,GAAG,kBAAkB,GAAG,kBAAkB,CAAC,CAAC,CAAC;EAC1E,CAAC;EACDX,UAAU,CAACY,SAAS,CAACe,gBAAgB,GAAG,YAAY;IAClD,IAAI1B,MAAM,GAAG,IAAI,CAACM,IAAI;IACtB,IAAIL,MAAM,GAAG,IAAI,CAACM,IAAI;IACtB,IAAIoB,eAAe,GAAG,CAAC,CAAC;IACxB,IAAIC,aAAa,GAAG,IAAIC,KAAK,CAAC7B,MAAM,CAACJ,MAAM,CAAC;IAC5C,IAAIkC,aAAa,GAAG,IAAID,KAAK,CAAC5B,MAAM,CAACL,MAAM,CAAC;IAC5C,IAAI,CAACmC,aAAa,CAAC/B,MAAM,EAAE,IAAI,EAAE4B,aAAa,EAAE,eAAe,CAAC;IAChE,IAAI,CAACG,aAAa,CAAC9B,MAAM,EAAE0B,eAAe,EAAEG,aAAa,EAAE,eAAe,CAAC;IAC3E,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,MAAM,CAACJ,MAAM,EAAEoC,CAAC,EAAE,EAAE;MACtC,IAAIC,MAAM,GAAGL,aAAa,CAACI,CAAC,CAAC;MAC7B,IAAIE,YAAY,GAAGP,eAAe,CAACM,MAAM,CAAC;MAC1C,IAAIE,eAAe,GAAGzC,uBAAuB,CAACwC,YAAY,CAAC;MAC3D;MACA,IAAIC,eAAe,GAAG,CAAC,EAAE;QACvB;QACA;QACA,IAAIC,MAAM,GAAGF,YAAY,CAACG,KAAK,CAAC,CAAC;QACjC,IAAIH,YAAY,CAACtC,MAAM,KAAK,CAAC,EAAE;UAC7B+B,eAAe,CAACM,MAAM,CAAC,GAAGC,YAAY,CAAC,CAAC,CAAC;QAC3C;QACA,IAAI,CAAClB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACoB,MAAM,EAAEJ,CAAC,CAAC;MACzC,CAAC,MAAM,IAAIG,eAAe,KAAK,CAAC,EAAE;QAChCR,eAAe,CAACM,MAAM,CAAC,GAAG,IAAI;QAC9B,IAAI,CAACjB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACkB,YAAY,EAAEF,CAAC,CAAC;MAC/C,CAAC,MAAM;QACL,IAAI,CAACR,OAAO,IAAI,IAAI,CAACA,OAAO,CAACQ,CAAC,CAAC;MACjC;IACF;IACA,IAAI,CAACM,eAAe,CAACR,aAAa,EAAEH,eAAe,CAAC;EACtD,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE5B,UAAU,CAACY,SAAS,CAAC4B,gBAAgB,GAAG,YAAY;IAClD,IAAIvC,MAAM,GAAG,IAAI,CAACM,IAAI;IACtB,IAAIL,MAAM,GAAG,IAAI,CAACM,IAAI;IACtB,IAAIiC,eAAe,GAAG,CAAC,CAAC;IACxB,IAAIb,eAAe,GAAG,CAAC,CAAC;IACxB,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIE,aAAa,GAAG,EAAE;IACtB,IAAI,CAACC,aAAa,CAAC/B,MAAM,EAAEwC,eAAe,EAAEZ,aAAa,EAAE,eAAe,CAAC;IAC3E,IAAI,CAACG,aAAa,CAAC9B,MAAM,EAAE0B,eAAe,EAAEG,aAAa,EAAE,eAAe,CAAC;IAC3E,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,aAAa,CAAChC,MAAM,EAAEoC,CAAC,EAAE,EAAE;MAC7C,IAAIC,MAAM,GAAGL,aAAa,CAACI,CAAC,CAAC;MAC7B,IAAIS,YAAY,GAAGD,eAAe,CAACP,MAAM,CAAC;MAC1C,IAAIC,YAAY,GAAGP,eAAe,CAACM,MAAM,CAAC;MAC1C,IAAIS,eAAe,GAAGhD,uBAAuB,CAAC+C,YAAY,CAAC;MAC3D,IAAIN,eAAe,GAAGzC,uBAAuB,CAACwC,YAAY,CAAC;MAC3D,IAAIQ,eAAe,GAAG,CAAC,IAAIP,eAAe,KAAK,CAAC,EAAE;QAChD,IAAI,CAACjB,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACgB,YAAY,EAAEO,YAAY,CAAC;QAC1Ed,eAAe,CAACM,MAAM,CAAC,GAAG,IAAI;MAChC,CAAC,MAAM,IAAIS,eAAe,KAAK,CAAC,IAAIP,eAAe,GAAG,CAAC,EAAE;QACvD,IAAI,CAACf,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACc,YAAY,EAAEO,YAAY,CAAC;QAC1Ed,eAAe,CAACM,MAAM,CAAC,GAAG,IAAI;MAChC,CAAC,MAAM,IAAIS,eAAe,KAAK,CAAC,IAAIP,eAAe,KAAK,CAAC,EAAE;QACzD,IAAI,CAACnB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACkB,YAAY,EAAEO,YAAY,CAAC;QACxDd,eAAe,CAACM,MAAM,CAAC,GAAG,IAAI;MAChC,CAAC,MAAM,IAAIS,eAAe,GAAG,CAAC,IAAIP,eAAe,GAAG,CAAC,EAAE;QACrD,IAAI,CAACb,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACY,YAAY,EAAEO,YAAY,CAAC;QAC5Ed,eAAe,CAACM,MAAM,CAAC,GAAG,IAAI;MAChC,CAAC,MAAM,IAAIS,eAAe,GAAG,CAAC,EAAE;QAC9B,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,eAAe,EAAEC,GAAG,EAAE,EAAE;UAC9C,IAAI,CAACnB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACiB,YAAY,CAACE,GAAG,CAAC,CAAC;QACjD;MACF,CAAC,MAAM;QACL,IAAI,CAACnB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACiB,YAAY,CAAC;MAC5C;IACF;IACA,IAAI,CAACH,eAAe,CAACR,aAAa,EAAEH,eAAe,CAAC;EACtD,CAAC;EACD5B,UAAU,CAACY,SAAS,CAAC2B,eAAe,GAAG,UAAUR,aAAa,EAAEH,eAAe,EAAE;IAC/E,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAAClC,MAAM,EAAEoC,CAAC,EAAE,EAAE;MAC7C,IAAIY,MAAM,GAAGd,aAAa,CAACE,CAAC,CAAC;MAC7B,IAAIE,YAAY,GAAGP,eAAe,CAACiB,MAAM,CAAC;MAC1C,IAAIC,YAAY,GAAGnD,uBAAuB,CAACwC,YAAY,CAAC;MACxD,IAAIW,YAAY,GAAG,CAAC,EAAE;QACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,EAAEC,CAAC,EAAE,EAAE;UACrC,IAAI,CAAChC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACoB,YAAY,CAACY,CAAC,CAAC,CAAC;QACzC;MACF,CAAC,MAAM,IAAID,YAAY,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAC/B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACoB,YAAY,CAAC;MACtC;MACA;MACAP,eAAe,CAACiB,MAAM,CAAC,GAAG,IAAI;IAChC;EACF,CAAC;EACD7C,UAAU,CAACY,SAAS,CAACoB,aAAa,GAAG,UAAUgB,GAAG;EAClD;EACAC,GAAG;EACH;EACA;EACA;EACAC,MAAM,EAAEC,aAAa,EAAE;IACrB,IAAIC,cAAc,GAAG,IAAI,CAACzC,iBAAiB;IAC3C,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,GAAG,CAACnD,MAAM,EAAEoC,CAAC,EAAE,EAAE;MACnC;MACA,IAAIoB,GAAG,GAAG,MAAM,GAAG,IAAI,CAACF,aAAa,CAAC,CAACH,GAAG,CAACf,CAAC,CAAC,EAAEA,CAAC,CAAC;MACjD,IAAI,CAACmB,cAAc,EAAE;QACnBF,MAAM,CAACjB,CAAC,CAAC,GAAGoB,GAAG;MACjB;MACA,IAAI,CAACJ,GAAG,EAAE;QACR;MACF;MACA,IAAIK,SAAS,GAAGL,GAAG,CAACI,GAAG,CAAC;MACxB,IAAIP,YAAY,GAAGnD,uBAAuB,CAAC2D,SAAS,CAAC;MACrD,IAAIR,YAAY,KAAK,CAAC,EAAE;QACtB;QACA;QACAG,GAAG,CAACI,GAAG,CAAC,GAAGpB,CAAC;QACZ,IAAImB,cAAc,EAAE;UAClBF,MAAM,CAACK,IAAI,CAACF,GAAG,CAAC;QAClB;MACF,CAAC,MAAM,IAAIP,YAAY,KAAK,CAAC,EAAE;QAC7BG,GAAG,CAACI,GAAG,CAAC,GAAG,CAACC,SAAS,EAAErB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLqB,SAAS,CAACC,IAAI,CAACtB,CAAC,CAAC;MACnB;IACF;EACF,CAAC;EACD,OAAOjC,UAAU;AACnB,CAAC,CAAC,CAAC;AACH,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}