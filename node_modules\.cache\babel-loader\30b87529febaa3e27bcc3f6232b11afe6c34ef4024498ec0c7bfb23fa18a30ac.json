{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"E:/\\u65B0\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"E:/\\u65B0\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport \"core-js/modules/es.array.for-each.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport utils from './../utils.js';\nvar InterceptorManager = /*#__PURE__*/function () {\n  function InterceptorManager() {\n    _classCallCheck(this, InterceptorManager);\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  return _createClass(InterceptorManager, [{\n    key: \"use\",\n    value: function use(fulfilled, rejected, options) {\n      this.handlers.push({\n        fulfilled: fulfilled,\n        rejected: rejected,\n        synchronous: options ? options.synchronous : false,\n        runWhen: options ? options.runWhen : null\n      });\n      return this.handlers.length - 1;\n    }\n\n    /**\n     * Remove an interceptor from the stack\n     *\n     * @param {Number} id The ID that was returned by `use`\n     *\n     * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n     */\n  }, {\n    key: \"eject\",\n    value: function eject(id) {\n      if (this.handlers[id]) {\n        this.handlers[id] = null;\n      }\n    }\n\n    /**\n     * Clear all interceptors from the stack\n     *\n     * @returns {void}\n     */\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      if (this.handlers) {\n        this.handlers = [];\n      }\n    }\n\n    /**\n     * Iterate over all the registered interceptors\n     *\n     * This method is particularly useful for skipping over any\n     * interceptors that may have become `null` calling `eject`.\n     *\n     * @param {Function} fn The function to call for each interceptor\n     *\n     * @returns {void}\n     */\n  }, {\n    key: \"forEach\",\n    value: function forEach(fn) {\n      utils.forEach(this.handlers, function forEachHandler(h) {\n        if (h !== null) {\n          fn(h);\n        }\n      });\n    }\n  }]);\n}();\nexport default InterceptorManager;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "utils", "InterceptorManager", "handlers", "key", "value", "use", "fulfilled", "rejected", "options", "push", "synchronous", "runWhen", "length", "eject", "id", "clear", "for<PERSON>ach", "fn", "forEachHandler", "h"], "sources": ["E:/新项目/adminweb/node_modules/axios/lib/core/InterceptorManager.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\nclass InterceptorManager {\n  constructor() {\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  use(fulfilled, rejected, options) {\n    this.handlers.push({\n      fulfilled,\n      rejected,\n      synchronous: options ? options.synchronous : false,\n      runWhen: options ? options.runWhen : null\n    });\n    return this.handlers.length - 1;\n  }\n\n  /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */\n  eject(id) {\n    if (this.handlers[id]) {\n      this.handlers[id] = null;\n    }\n  }\n\n  /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */\n  clear() {\n    if (this.handlers) {\n      this.handlers = [];\n    }\n  }\n\n  /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */\n  forEach(fn) {\n    utils.forEach(this.handlers, function forEachHandler(h) {\n      if (h !== null) {\n        fn(h);\n      }\n    });\n  }\n}\n\nexport default InterceptorManager;\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEb,OAAOC,KAAK,MAAM,eAAe;AAAC,IAE5BC,kBAAkB;EACtB,SAAAA,mBAAA,EAAc;IAAAH,eAAA,OAAAG,kBAAA;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE,OAAAH,YAAA,CAAAE,kBAAA;IAAAE,GAAA;IAAAC,KAAA,EAQA,SAAAC,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MAChC,IAAI,CAACN,QAAQ,CAACO,IAAI,CAAC;QACjBH,SAAS,EAATA,SAAS;QACTC,QAAQ,EAARA,QAAQ;QACRG,WAAW,EAAEF,OAAO,GAAGA,OAAO,CAACE,WAAW,GAAG,KAAK;QAClDC,OAAO,EAAEH,OAAO,GAAGA,OAAO,CAACG,OAAO,GAAG;MACvC,CAAC,CAAC;MACF,OAAO,IAAI,CAACT,QAAQ,CAACU,MAAM,GAAG,CAAC;IACjC;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAAT,GAAA;IAAAC,KAAA,EAOA,SAAAS,KAAKA,CAACC,EAAE,EAAE;MACR,IAAI,IAAI,CAACZ,QAAQ,CAACY,EAAE,CAAC,EAAE;QACrB,IAAI,CAACZ,QAAQ,CAACY,EAAE,CAAC,GAAG,IAAI;MAC1B;IACF;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAX,GAAA;IAAAC,KAAA,EAKA,SAAAW,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAACb,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,GAAG,EAAE;MACpB;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE;IAAAC,GAAA;IAAAC,KAAA,EAUA,SAAAY,OAAOA,CAACC,EAAE,EAAE;MACVjB,KAAK,CAACgB,OAAO,CAAC,IAAI,CAACd,QAAQ,EAAE,SAASgB,cAAcA,CAACC,CAAC,EAAE;QACtD,IAAIA,CAAC,KAAK,IAAI,EAAE;UACdF,EAAE,CAACE,CAAC,CAAC;QACP;MACF,CAAC,CAAC;IACJ;EAAC;AAAA;AAGH,eAAelB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}