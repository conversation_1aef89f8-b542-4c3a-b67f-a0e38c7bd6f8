import { GraphData } from './types';
/**
 * GADDI 模式匹配
 * @param graphData 原图数据
 * @param pattern 搜索图（需要在原图上搜索的模式）数据
 * @param directed 是否计算有向图，默认 false
 * @param k 参数 k，表示 k-近邻
 * @param length 参数 length
 * @param nodeLabelProp 节点数据中代表节点标签（分类信息）的属性名。默认为 cluster
 * @param edgeLabelProp 边数据中代表边标签（分类信息）的属性名。默认为 cluster
 */
declare const GADDI: (graphData: GraphData, pattern: GraphData, directed: boolean, k: number, length: number, nodeLabelProp?: string, edgeLabelProp?: string) => GraphData[];
export default GADDI;
