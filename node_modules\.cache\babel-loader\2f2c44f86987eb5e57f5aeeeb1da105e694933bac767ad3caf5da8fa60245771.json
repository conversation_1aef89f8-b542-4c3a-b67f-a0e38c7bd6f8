{"ast": null, "code": "import request from '@/utils/request';\nexport function getNavMenus() {\n  return request({\n    url: '/menu/nav',\n    method: 'get'\n  });\n}", "map": {"version": 3, "names": ["request", "getNavMenus", "url", "method"], "sources": ["E:/最新的代码/adminweb/src/api/menu.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\nexport function getNavMenus() {\r\n  return request({\r\n    url: '/menu/nav',\r\n    method: 'get'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5B,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}