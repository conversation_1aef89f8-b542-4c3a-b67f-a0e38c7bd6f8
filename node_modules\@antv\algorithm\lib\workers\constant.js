"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MESSAGE = exports.ALGORITHM = void 0;
var ALGORITHM = {
  pageRank: 'pageRank',
  breadthFirstSearch: 'breadthFirstSearch',
  connectedComponent: 'connectedComponent',
  depthFirstSearch: 'depthFirstSearch',
  detectCycle: 'detectCycle',
  detectDirectedCycle: 'detectDirectedCycle',
  detectAllCycles: 'detectAllCycles',
  detectAllDirectedCycle: 'detectAllDirectedCycle',
  detectAllUndirectedCycle: 'detectAllUndirectedCycle',
  dijkstra: 'dijkstra',
  findAllPath: 'findAllPath',
  findShortestPath: 'findShortestPath',
  floydWarshall: 'floydWarshall',
  getAdjMatrix: 'getAdjMatrix',
  getDegree: 'getDegree',
  getInDegree: 'getInDegree',
  getNeighbors: 'getNeighbors',
  getOutDegree: 'getOutDegree',
  labelPropagation: 'labelPropagation',
  louvain: 'louvain',
  GADDI: 'GADDI',
  minimumSpanningTree: 'minimumSpanningTree',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE'
};
exports.ALGORITHM = ALGORITHM;
var MESSAGE = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE'
};
exports.MESSAGE = MESSAGE;