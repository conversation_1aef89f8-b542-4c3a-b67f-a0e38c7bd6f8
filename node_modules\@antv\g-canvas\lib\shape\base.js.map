{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/shape/base.ts"], "names": [], "mappings": ";;;AAAA,uCAA6C;AAE7C,qCAAoD;AACpD,qCAAmE;AACnE,uCAA6C;AAE7C,+BAAiC;AACjC,kCAA6B;AAE7B;IAAwB,qCAAa;IAArC;;IA8OA,CAAC;IA7OC,mCAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,QAAQ;QACR,6CACK,KAAK,KACR,SAAS,EAAE,CAAC,EACZ,eAAe,EAAE,CAAC,EAClB,aAAa,EAAE,CAAC,EAChB,WAAW,EAAE,CAAC,IACd;IACJ,CAAC;IAED,gCAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gCAAY,GAAZ;QACE,OAAO,eAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,kCAAc,GAAd,UAAe,UAAsB;QACnC,qBAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAED,iCAAa,GAAb;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACzC,6BAA6B;QAC7B,IAAM,UAAU,GAAG,sBAAa,CAAC,IAAI,CAAC,CAAC;QACvC,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAM,aAAa,GAAG,SAAS,GAAG,CAAC,CAAC;QACpC,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;QACnC,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;QACnC,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC;QAC/C,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC;QAChD,OAAO;YACL,CAAC,EAAE,IAAI;YACP,IAAI,MAAA;YACJ,CAAC,EAAE,IAAI;YACP,IAAI,MAAA;YACJ,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,SAAS;YAC5B,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,SAAS;YAC9B,IAAI,MAAA;YACJ,IAAI,MAAA;SACL,CAAC;IACJ,CAAC;IAED,0BAAM,GAAN;QACE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;IACpD,CAAC;IAED,4BAAQ,GAAR;QACE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED,kBAAkB;IAClB,8BAAU,GAAV,UAAW,OAAO,EAAE,IAAe;QACjC,IAAI,IAAI,EAAE;YACR,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,0BAA0B;YAC1B,0BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACnC,aAAa;YACb,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzB,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK;YACL,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;IACH,CAAC;IAED,sBAAsB;IACtB,wBAAI,GAAJ,UAAK,OAAiC,EAAE,MAAe;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;QAChC,6BAA6B;QAC7B,IAAI,MAAM,EAAE;YACV,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE;gBAC9B,qBAAqB;gBACrB,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBAC9B,OAAO;aACR;YACD,qBAAqB;YACrB,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAClC,IAAI,CAAC,oBAAa,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;gBAChC,0BAA0B;gBAC1B,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBAC9B,4CAA4C;gBAC5C,wBAAwB;gBACxB,8EAA8E;gBAC9E,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACrB,IAAI,CAAC,UAAU,EAAE,CAAC;iBACnB;gBACD,OAAO;aACR;SACF;QACD,OAAO,CAAC,IAAI,EAAE,CAAC;QACf,wDAAwD;QACxD,0BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAiB,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACvB,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,oCAAgB,GAAxB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE;YACV,aAAa;YACb,OAAO,MAAM,CAAC,YAAY,EAAE,CAAC;SAC9B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mCAAe,GAAf;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,aAAa;QACb,IAAI,UAAU,EAAE;YACd,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAClC,IAAM,QAAQ,GAAG,oBAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YACjD,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC/B,6CAA6C;YAC7C,8BAA8B;YAC9B,gCAAgC;YAChC,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;aACnC;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;aACnC;SACF;IACH,CAAC;IAED,8BAAU,GAAV;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,4BAAQ,GAAR;QACE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,4BAAQ,GAAR,UAAS,OAAiC;QACxC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,wBAAI,GAAJ,UAAK,OAAiC;QACpC,OAAO,CAAC,IAAI,EAAE,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACH,0BAAM,GAAN,UAAO,OAAiC;QACtC,OAAO,CAAC,MAAM,EAAE,CAAC;IACnB,CAAC;IAED,SAAS;IACT,iCAAa,GAAb,UAAc,OAAO;QACb,IAAA,KAAqD,IAAI,CAAC,KAAK,EAA7D,SAAS,eAAA,EAAE,OAAO,aAAA,EAAE,aAAa,mBAAA,EAAE,WAAW,iBAAe,CAAC;QAEtE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,IAAI,CAAC,YAAK,CAAC,WAAW,CAAC,IAAI,WAAW,KAAK,CAAC,EAAE;gBAC5C,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;gBAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;aAC/B;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACpB;SACF;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACnB,IAAI,SAAS,GAAG,CAAC,EAAE;gBACjB,IAAI,CAAC,YAAK,CAAC,aAAa,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE;oBAChD,OAAO,CAAC,WAAW,GAAG,aAAa,CAAC;iBACrC;gBACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aACtB;SACF;QACD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,8BAAU,GAAV,UAAW,OAAiC,IAAG,CAAC;IAEhD;;;OAGG;IACH,iCAAa,GAAb,UAAc,OAAiC,IAAG,CAAC;IAEnD,6BAAS,GAAT,UAAU,IAAY,EAAE,IAAY;QAClC,+CAA+C;QAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,6CAA6C;IAC7C,oCAAgB,GAAhB,UAAiB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,mCAAe,GAAf;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,OAAO,CAAC,CAAC;SACV;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,OAAO,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACvD,CAAC;IACH,gBAAC;AAAD,CAAC,AA9OD,CAAwB,sBAAa,GA8OpC;AAED,kBAAe,SAAS,CAAC"}