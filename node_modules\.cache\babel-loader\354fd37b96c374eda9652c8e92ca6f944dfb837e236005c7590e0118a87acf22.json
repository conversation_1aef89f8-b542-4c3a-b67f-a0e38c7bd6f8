{"ast": null, "code": "import _regeneratorRuntime from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.find.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.find.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport * as echarts from 'echarts';\nimport { getLoginLogList } from '@/api/log/login';\nimport { getOperLogList } from '@/api/log/operation';\nimport { getDashboardStats, getProvinceDeviceStats } from '@/api/dashboard';\nvar provinceMap = {\n  'guangdong': '440000',\n  'beijing': '110000',\n  'shanghai': '310000',\n  'tianjin': '120000',\n  'chongqing': '500000',\n  'hebei': '130000',\n  'shanxi': '140000',\n  'liaoning': '210000',\n  'jilin': '220000',\n  'heilongjiang': '230000',\n  'jiangsu': '320000',\n  'zhejiang': '330000',\n  'anhui': '340000',\n  'fujian': '350000',\n  'jiangxi': '360000',\n  'shandong': '370000',\n  'henan': '410000',\n  'hubei': '420000',\n  'hunan': '430000',\n  'guangxi': '450000',\n  'hainan': '460000',\n  'sichuan': '510000',\n  'guizhou': '520000',\n  'yunnan': '530000',\n  'xizang': '540000',\n  'shaanxi': '610000',\n  'gansu': '620000',\n  'qinghai': '630000',\n  'ningxia': '640000',\n  'xinjiang': '650000'\n};\nvar cityMap = {\n  '440000': {\n    'guangzhou': '440100',\n    'shenzhen': '440300',\n    'zhuhai': '440400',\n    'shantou': '440500',\n    'foshan': '440600',\n    'shaoguan': '440200',\n    'zhanjiang': '440800',\n    'zhaoqing': '441200',\n    'jiangmen': '440700',\n    'maoming': '440900',\n    'huizhou': '441300',\n    'meizhou': '441400',\n    'shanwei': '441500',\n    'heyuan': '441600',\n    'yangjiang': '441700',\n    'qingyuan': '441800',\n    'dongguan': '441900',\n    'zhongshan': '442000',\n    'chaozhou': '445100',\n    'jieyang': '445200',\n    'yunfu': '445300'\n  },\n  '110000': {\n    'dongcheng': '110101',\n    'xicheng': '110102',\n    'chaoyang': '110105',\n    'haidian': '110108',\n    'fengtai': '110106',\n    'shijingshan': '110107'\n  },\n  '310000': {\n    'huangpu': '310101',\n    'xuhui': '310104',\n    'changning': '310105',\n    'jingan': '310106',\n    'putuo': '310107',\n    'hongkou': '310109'\n  },\n  '320000': {\n    'nanjing': '320100',\n    'suzhou': '320500',\n    'wuxi': '320200',\n    'changzhou': '320400',\n    'nantong': '320600',\n    'yangzhou': '321000'\n  },\n  '330000': {\n    'hangzhou': '330100',\n    'ningbo': '330200',\n    'wenzhou': '330300',\n    'jiaxing': '330400',\n    'huzhou': '330500',\n    'shaoxing': '330600'\n  }\n};\nexport default {\n  name: 'Dashboard',\n  data: function data() {\n    return {\n      // 登录日志\n      loginLogs: [],\n      // 操作日志\n      operationLogs: [],\n      // 添加新的地图关数据\n      mapChart: null,\n      currentArea: [],\n      // 当前区域层级 [{name: '广东省', code: '440000'}, {name: '深圳市', code: '440300'}]\n      mapData: {\n        china: [],\n        // 改为空数组，从后端获取数据\n        beijing: [{\n          name: '东城区',\n          value: 25,\n          hasDevice: true\n        }, {\n          name: '西城区',\n          value: 30,\n          hasDevice: true\n        }, {\n          name: '朝阳区',\n          value: 45,\n          hasDevice: true\n        }, {\n          name: '海淀区',\n          value: 50,\n          hasDevice: true\n        }, {\n          name: '丰台区',\n          value: 28,\n          hasDevice: true\n        }, {\n          name: '石景山区',\n          value: 20,\n          hasDevice: true\n        }],\n        shanghai: [{\n          name: '黄浦区',\n          value: 28,\n          hasDevice: true\n        }, {\n          name: '徐汇区',\n          value: 35,\n          hasDevice: true\n        }, {\n          name: '长宁区',\n          value: 30,\n          hasDevice: true\n        }, {\n          name: '静安区',\n          value: 40,\n          hasDevice: true\n        }, {\n          name: '普陀区',\n          value: 25,\n          hasDevice: true\n        }, {\n          name: '虹口区',\n          value: 22,\n          hasDevice: true\n        }],\n        jiangsu: [{\n          name: '南京',\n          value: 55,\n          children: true\n        }, {\n          name: '苏州',\n          value: 60,\n          children: true\n        }, {\n          name: '无锡',\n          value: 45,\n          children: true\n        }, {\n          name: '常州',\n          value: 35,\n          children: true\n        }, {\n          name: '南通',\n          value: 30,\n          children: true\n        }, {\n          name: '扬州',\n          value: 25,\n          children: true\n        }],\n        zhejiang: [{\n          name: '杭州',\n          value: 65,\n          children: true\n        }, {\n          name: '宁波',\n          value: 50,\n          children: true\n        }, {\n          name: '温州',\n          value: 40,\n          children: true\n        }, {\n          name: '嘉兴',\n          value: 30,\n          children: true\n        }, {\n          name: '湖州',\n          value: 25,\n          children: true\n        }, {\n          name: '绍兴',\n          value: 35,\n          children: true\n        }],\n        guangdong: [{\n          name: '广州',\n          value: 45,\n          children: true\n        }, {\n          name: '深圳',\n          value: 50,\n          children: true\n        }, {\n          name: '珠海',\n          value: 25,\n          children: true\n        }, {\n          name: '汕头',\n          value: 20,\n          children: true\n        }, {\n          name: '佛山',\n          value: 35,\n          children: true\n        }, {\n          name: '韶关',\n          value: 15,\n          children: true\n        }, {\n          name: '湛江',\n          value: 18,\n          children: true\n        }, {\n          name: '肇庆',\n          value: 22,\n          children: true\n        }, {\n          name: '江门',\n          value: 28,\n          children: true\n        }, {\n          name: '茂名',\n          value: 20,\n          children: true\n        }, {\n          name: '惠州',\n          value: 30,\n          children: true\n        }, {\n          name: '梅州',\n          value: 15,\n          children: true\n        }, {\n          name: '汕尾',\n          value: 12,\n          children: true\n        }, {\n          name: '河源',\n          value: 18,\n          children: true\n        }, {\n          name: '阳江',\n          value: 16,\n          children: true\n        }, {\n          name: '清远',\n          value: 25,\n          children: true\n        }, {\n          name: '东莞',\n          value: 40,\n          children: true\n        }, {\n          name: '中山',\n          value: 32,\n          children: true\n        }, {\n          name: '潮州',\n          value: 15,\n          children: true\n        }, {\n          name: '阳',\n          value: 18,\n          children: true\n        }, {\n          name: '云浮',\n          value: 12,\n          children: true\n        }],\n        guangzhou: [{\n          name: '越秀区',\n          value: 15,\n          hasDevice: true\n        }, {\n          name: '海珠区',\n          value: 12,\n          hasDevice: true\n        }, {\n          name: '荔湾区',\n          value: 10,\n          hasDevice: true\n        }, {\n          name: '天河区',\n          value: 20,\n          hasDevice: true\n        }, {\n          name: '白云区',\n          value: 18,\n          hasDevice: true\n        }, {\n          name: '黄埔区',\n          value: 15,\n          hasDevice: true\n        }],\n        shenzhen: [{\n          name: '福田区',\n          value: 35,\n          hasDevice: true\n        }, {\n          name: '罗湖区',\n          value: 28,\n          hasDevice: true\n        }, {\n          name: '南山区',\n          value: 42,\n          hasDevice: true\n        }, {\n          name: '宝安区',\n          value: 38,\n          hasDevice: true\n        }, {\n          name: '龙岗区',\n          value: 32,\n          hasDevice: true\n        }, {\n          name: '盐田区',\n          value: 15,\n          hasDevice: true\n        }, {\n          name: '龙华区',\n          value: 30,\n          hasDevice: true\n        }, {\n          name: '坪山区',\n          value: 18,\n          hasDevice: true\n        }, {\n          name: '光明区',\n          value: 20,\n          hasDevice: true\n        }, {\n          name: '大鹏新区',\n          value: 12,\n          hasDevice: true\n        }]\n      },\n      // 修改查询参数\n      logQuery: {\n        pageNum: 1,\n        pageSize: 3,\n        // 只获取3条记录\n        orderByColumn: 'createTime',\n        // 按创建时间排序\n        isAsc: 'desc' // 降序排序，最新的在前\n      },\n      // 添加统计数据\n      stats: {\n        todayUsers: 0,\n        todayUsersRate: 0,\n        todayOrders: 0,\n        todayOrdersRate: 0,\n        todayWithdraw: 0,\n        todayWithdrawRate: 0,\n        todayDevices: 0,\n        todayDevicesRate: 0\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.initEchartsMap();\n    this.getLoginLogs();\n    this.getOperationLogs();\n    this.getStats(); // 添加获取统计数据的调用\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.mapChart) {\n      this.mapChart.dispose();\n    }\n  },\n  methods: {\n    initEchartsMap: function initEchartsMap() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var chartDom, response, chinaMap, res, option;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              chartDom = document.getElementById('deviceMap');\n              _this.mapChart = echarts.init(chartDom);\n\n              // 先加载中国地图\n              _context2.next = 5;\n              return fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json');\n            case 5:\n              response = _context2.sent;\n              _context2.next = 8;\n              return response.json();\n            case 8:\n              chinaMap = _context2.sent;\n              echarts.registerMap('china', chinaMap);\n\n              // 获取省份设备数量数据\n              _context2.next = 12;\n              return getProvinceDeviceStats();\n            case 12:\n              res = _context2.sent;\n              if (res.code === 200) {\n                // 处理数据，添加 children 属性\n                _this.mapData.china = res.data.map(function (item) {\n                  return {\n                    name: item.province.replace('省', '').replace('市', '').replace('自治区', ''),\n                    // 移除后缀以匹配地图数据\n                    value: item.value,\n                    children: true\n                  };\n                });\n              }\n\n              // 设置地图配置\n              option = {\n                tooltip: {\n                  trigger: 'item',\n                  formatter: '{b}<br/>设备数量：{c}'\n                },\n                visualMap: {\n                  min: 0,\n                  max: 200,\n                  text: ['高', '低'],\n                  realtime: false,\n                  calculable: true,\n                  inRange: {\n                    color: ['#0a2b5a', '#4ecdc4']\n                  },\n                  textStyle: {\n                    color: '#fff' // 设置文字颜色为白色\n                  }\n                },\n                series: [{\n                  name: '设备分布',\n                  type: 'map',\n                  map: 'china',\n                  roam: true,\n                  label: {\n                    show: true,\n                    color: '#fff',\n                    fontSize: 12\n                  },\n                  itemStyle: {\n                    areaColor: '#0c2c5a',\n                    borderColor: '#00fcff',\n                    borderWidth: 1\n                  },\n                  emphasis: {\n                    label: {\n                      show: true,\n                      color: '#fff'\n                    },\n                    itemStyle: {\n                      areaColor: '#4ecdc4'\n                    }\n                  },\n                  data: _this.mapData.china\n                }]\n              };\n              _this.mapChart.setOption(option);\n\n              // 添加点击事件处理\n              _this.mapChart.off('click').on('click', /*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(params) {\n                  var data, areaName, nextLevel, code;\n                  return _regeneratorRuntime().wrap(function _callee$(_context) {\n                    while (1) switch (_context.prev = _context.next) {\n                      case 0:\n                        console.log('Clicked area:', params.name);\n                        data = _this.mapData[params.name.toLowerCase()];\n                        if (!(data && data.children)) {\n                          _context.next = 11;\n                          break;\n                        }\n                        // 处理地区名称，移除各种后缀\n                        areaName = params.name.replace(/(省|市|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '');\n                        nextLevel = areaName.toLowerCase();\n                        console.log('Next level:', nextLevel);\n\n                        // 获取下一级区域的编码\n                        _context.next = 8;\n                        return _this.getNextLevelCode(areaName, _this.currentArea.length);\n                      case 8:\n                        code = _context.sent;\n                        console.log('Next level code:', code);\n                        if (code) {\n                          _this.currentArea.push({\n                            name: params.name,\n                            code: code\n                          });\n                          _this.loadMap(nextLevel);\n                        }\n                      case 11:\n                      case \"end\":\n                        return _context.stop();\n                    }\n                  }, _callee);\n                }));\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }());\n\n              // 添加窗口大小改变事件监听\n              window.addEventListener('resize', function () {\n                _this.mapChart.resize();\n              });\n              _context2.next = 23;\n              break;\n            case 20:\n              _context2.prev = 20;\n              _context2.t0 = _context2[\"catch\"](0);\n              console.error('初始化地图失败:', _context2.t0);\n            case 23:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 20]]);\n      }))();\n    },\n    loadMap: function loadMap(mapName) {\n      var _arguments = arguments,\n        _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var data, code, response, mapJson, mapData, mapOption;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              data = _arguments.length > 1 && _arguments[1] !== undefined ? _arguments[1] : null;\n              _context4.prev = 1;\n              if (!(mapName !== 'china')) {\n                _context4.next = 12;\n                break;\n              }\n              code = _this2.getAreaCode(mapName);\n              console.log('Loading map for:', mapName, 'with code:', code);\n              _context4.next = 7;\n              return fetch(\"https://geo.datav.aliyun.com/areas_v3/bound/\".concat(code, \"_full.json\"));\n            case 7:\n              response = _context4.sent;\n              _context4.next = 10;\n              return response.json();\n            case 10:\n              mapJson = _context4.sent;\n              echarts.registerMap(mapName, mapJson);\n            case 12:\n              mapData = data || _this2.mapData[mapName.toLowerCase()] || [];\n              console.log('Map data for:', mapName, mapData);\n\n              // 抽取地图配置\n              mapOption = {\n                tooltip: {\n                  trigger: 'item',\n                  formatter: function formatter(params) {\n                    var value = params.value || 0;\n                    return \"\".concat(params.name, \"\\n\").concat(value);\n                  }\n                },\n                visualMap: {\n                  min: 0,\n                  max: 200,\n                  text: ['高', '低'],\n                  realtime: false,\n                  calculable: true,\n                  inRange: {\n                    color: ['#ffe4e1', '#ff4500']\n                  }\n                },\n                series: [{\n                  name: '设备分布',\n                  type: 'map',\n                  map: mapName,\n                  roam: true,\n                  zoom: 1.5,\n                  center: [104.297, 35.861],\n                  scaleLimit: {\n                    min: 1,\n                    max: 5\n                  },\n                  label: {\n                    show: true,\n                    formatter: function formatter(params) {\n                      var value = params.value || 0;\n                      return \"\".concat(params.name, \"\\n\").concat(value);\n                    },\n                    fontSize: 14,\n                    color: '#fff',\n                    backgroundColor: 'rgba(0, 150, 255, 0.5)',\n                    padding: [4, 8],\n                    borderRadius: 4,\n                    textBorderWidth: 2,\n                    textBorderColor: 'rgba(0, 0, 0, 0.8)'\n                  },\n                  itemStyle: {\n                    areaColor: '#0c2c5a',\n                    borderColor: '#00fcff',\n                    borderWidth: 1.5,\n                    shadowColor: 'rgba(0, 252, 255, 0.3)',\n                    shadowBlur: 10\n                  },\n                  emphasis: {\n                    label: {\n                      show: true,\n                      color: '#fff',\n                      fontSize: 16,\n                      backgroundColor: 'rgba(0, 150, 255, 0.8)'\n                    },\n                    itemStyle: {\n                      areaColor: '#0052d9',\n                      borderColor: '#00fcff',\n                      borderWidth: 2,\n                      shadowColor: 'rgba(0, 252, 255, 0.5)',\n                      shadowBlur: 15\n                    }\n                  },\n                  data: mapData.map(function (item) {\n                    return {\n                      name: item.name,\n                      value: item.value || 0,\n                      children: item.children,\n                      hasDevice: item.hasDevice,\n                      itemStyle: item.hasDevice ? {\n                        areaColor: '#ff4500',\n                        shadowColor: 'rgba(255, 69, 0, 0.5)',\n                        shadowBlur: 10\n                      } : null\n                    };\n                  })\n                }]\n              };\n              _this2.mapChart.setOption(mapOption);\n\n              // 修改点击事件处理\n              _this2.mapChart.off('click').on('click', /*#__PURE__*/function () {\n                var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {\n                  var data, areaName, nextLevel, _code;\n                  return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n                    while (1) switch (_context3.prev = _context3.next) {\n                      case 0:\n                        console.log('Clicked area:', params.name);\n                        data = mapData.find(function (item) {\n                          return item.name === params.name;\n                        });\n                        if (!(data && data.children)) {\n                          _context3.next = 11;\n                          break;\n                        }\n                        // 处理地区名称，移除各种后缀\n                        areaName = params.name.replace(/(省|市|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '');\n                        nextLevel = areaName.toLowerCase();\n                        console.log('Next level:', nextLevel);\n\n                        // 获取下一级区域的编码\n                        _context3.next = 8;\n                        return _this2.getNextLevelCode(areaName, _this2.currentArea.length);\n                      case 8:\n                        _code = _context3.sent;\n                        console.log('Next level code:', _code);\n                        if (_code) {\n                          _this2.currentArea.push({\n                            name: params.name,\n                            code: _code\n                          });\n                          _this2.loadMap(nextLevel);\n                        }\n                      case 11:\n                      case \"end\":\n                        return _context3.stop();\n                    }\n                  }, _callee3);\n                }));\n                return function (_x2) {\n                  return _ref2.apply(this, arguments);\n                };\n              }());\n              _context4.next = 22;\n              break;\n            case 19:\n              _context4.prev = 19;\n              _context4.t0 = _context4[\"catch\"](1);\n              console.error('加载地图数据失败:', _context4.t0);\n            case 22:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[1, 19]]);\n      }))();\n    },\n    handleAreaClick: function handleAreaClick(index) {\n      if (index === 0) {\n        this.currentArea = [];\n        this.loadMap('china');\n      } else {\n        this.currentArea = this.currentArea.slice(0, index + 1);\n        var areaName = this.currentArea[index].name.replace(/(省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '');\n        this.loadMap(areaName.toLowerCase());\n      }\n    },\n    getAreaCode: function getAreaCode(name) {\n      var currentLevel = this.currentArea.length;\n      var areaName = name.toLowerCase();\n      console.log('Getting area code for:', name, 'level:', currentLevel);\n      if (currentLevel === 0) {\n        return '100000';\n      } else if (currentLevel === 1) {\n        return provinceMap[areaName] || '100000';\n      } else if (currentLevel === 2) {\n        var _cityMap$parentCode;\n        var parentCode = this.currentArea[0].code;\n        var cityCode = (_cityMap$parentCode = cityMap[parentCode]) === null || _cityMap$parentCode === void 0 ? void 0 : _cityMap$parentCode[areaName];\n        console.log('City code:', cityCode, 'for parent:', parentCode);\n        return cityCode || parentCode;\n      }\n      return '100000';\n    },\n    getNextLevelCode: function getNextLevelCode(name, currentLevel) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var areaName, _cityMap$parentCode2, parentCode, _parentCode, response, json, district;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              areaName = name.toLowerCase();\n              if (!(currentLevel === 0)) {\n                _context5.next = 6;\n                break;\n              }\n              return _context5.abrupt(\"return\", provinceMap[areaName]);\n            case 6:\n              if (!(currentLevel === 1)) {\n                _context5.next = 11;\n                break;\n              }\n              // 获取市级编码\n              parentCode = _this3.currentArea[0].code;\n              return _context5.abrupt(\"return\", (_cityMap$parentCode2 = cityMap[parentCode]) === null || _cityMap$parentCode2 === void 0 ? void 0 : _cityMap$parentCode2[areaName]);\n            case 11:\n              if (!(currentLevel === 2)) {\n                _context5.next = 21;\n                break;\n              }\n              // 获取区级编码\n              _parentCode = _this3.currentArea[1].code;\n              _context5.next = 15;\n              return fetch(\"https://geo.datav.aliyun.com/areas_v3/bound/\".concat(_parentCode, \".json\"));\n            case 15:\n              response = _context5.sent;\n              _context5.next = 18;\n              return response.json();\n            case 18:\n              json = _context5.sent;\n              district = json.features.find(function (f) {\n                return f.properties.name.includes(name) || name.includes(f.properties.name);\n              });\n              return _context5.abrupt(\"return\", district === null || district === void 0 ? void 0 : district.properties.adcode);\n            case 21:\n              _context5.next = 27;\n              break;\n            case 23:\n              _context5.prev = 23;\n              _context5.t0 = _context5[\"catch\"](0);\n              console.error('获取区域编码失败:', _context5.t0);\n              return _context5.abrupt(\"return\", null);\n            case 27:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 23]]);\n      }))();\n    },\n    // 获取登录日志\n    getLoginLogs: function getLoginLogs() {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.prev = 0;\n              _context6.next = 3;\n              return getLoginLogList(_this4.logQuery);\n            case 3:\n              res = _context6.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this4.loginLogs = res.data.map(function (item) {\n                  return {\n                    loginTime: _this4.formatDateTime(item.loginTime),\n                    ip: item.ipaddr,\n                    location: \"\".concat(item.loginLocation),\n                    status: item.status\n                  };\n                });\n              } else {\n                _this4.$message.error(res.msg || '获取登录日志失败');\n              }\n              _context6.next = 11;\n              break;\n            case 7:\n              _context6.prev = 7;\n              _context6.t0 = _context6[\"catch\"](0);\n              console.error('获取登录日志失败:', _context6.t0);\n              _this4.$message.error('获取登录日志失败');\n            case 11:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6, null, [[0, 7]]);\n      }))();\n    },\n    // 获取操作日志\n    getOperationLogs: function getOperationLogs() {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.prev = 0;\n              console.log('数据');\n              _context7.next = 4;\n              return getOperLogList(_this5.logQuery);\n            case 4:\n              res = _context7.sent;\n              console.log(res.data);\n              if (res.code === 0 || res.code === 200) {\n                _this5.operationLogs = res.data.map(function (item) {\n                  return {\n                    operateTime: _this5.formatDateTime(item.operTime),\n                    module: item.title,\n                    // 使用 title 作为模块名\n                    operation: item.operType,\n                    // 使用 operName 作为操作内容\n                    status: item.status\n                  };\n                });\n              } else {\n                _this5.$message.error(res.msg || '获取操作日志失败');\n              }\n              _context7.next = 13;\n              break;\n            case 9:\n              _context7.prev = 9;\n              _context7.t0 = _context7[\"catch\"](0);\n              console.error('获取操作日志失败:', _context7.t0);\n              _this5.$message.error('获取操作日志失败');\n            case 13:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7, null, [[0, 9]]);\n      }))();\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    // 获取统计数据\n    getStats: function getStats() {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              _context8.prev = 0;\n              _context8.next = 3;\n              return getDashboardStats();\n            case 3:\n              res = _context8.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this6.stats = {\n                  todayUsers: res.data.todayUsers || 0,\n                  todayUsersRate: res.data.todayUsersRate || 0,\n                  todayOrders: res.data.todayOrders || 0,\n                  todayOrdersRate: res.data.todayOrdersRate || 0,\n                  todayWithdraw: res.data.todayWithdraw || 0,\n                  todayWithdrawRate: res.data.todayWithdrawRate || 0,\n                  todayDevices: res.data.todayDevices || 0,\n                  todayDevicesRate: res.data.todayDevicesRate || 0\n                };\n              }\n              _context8.next = 10;\n              break;\n            case 7:\n              _context8.prev = 7;\n              _context8.t0 = _context8[\"catch\"](0);\n              console.error('获取统计数据失败:', _context8.t0);\n            case 10:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8, null, [[0, 7]]);\n      }))();\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getLoginLogList", "getOperLogList", "getDashboardStats", "getProvinceDeviceStats", "provinceMap", "cityMap", "name", "data", "loginLogs", "operationLogs", "mapChart", "currentArea", "mapData", "china", "beijing", "value", "hasDevice", "shanghai", "<PERSON><PERSON><PERSON>", "children", "zhejiang", "guangdong", "guangzhou", "<PERSON><PERSON><PERSON>", "log<PERSON><PERSON>y", "pageNum", "pageSize", "orderByColumn", "isAsc", "stats", "todayUsers", "todayUsersRate", "todayOrders", "todayOrdersRate", "todayWithdraw", "todayWithdrawRate", "todayDevices", "todayDevicesRate", "mounted", "initEchartsMap", "getLoginLogs", "getOperationLogs", "getStats", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee2", "chartDom", "response", "chinaMap", "res", "option", "wrap", "_callee2$", "_context2", "prev", "next", "document", "getElementById", "init", "fetch", "sent", "json", "registerMap", "code", "map", "item", "province", "replace", "tooltip", "trigger", "formatter", "visualMap", "min", "max", "text", "realtime", "calculable", "inRange", "color", "textStyle", "series", "type", "roam", "label", "show", "fontSize", "itemStyle", "areaColor", "borderColor", "borderWidth", "emphasis", "setOption", "off", "on", "_ref", "_callee", "params", "areaName", "nextLevel", "_callee$", "_context", "console", "log", "toLowerCase", "getNextLevelCode", "length", "push", "loadMap", "stop", "_x", "apply", "arguments", "window", "addEventListener", "resize", "t0", "error", "mapName", "_arguments", "_this2", "_callee4", "mapJson", "mapOption", "_callee4$", "_context4", "undefined", "getAreaCode", "concat", "zoom", "center", "scaleLimit", "backgroundColor", "padding", "borderRadius", "textBorder<PERSON>idth", "textBorderColor", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "_ref2", "_callee3", "_code", "_callee3$", "_context3", "find", "_x2", "handleAreaClick", "index", "slice", "currentLevel", "_cityMap$parentCode", "parentCode", "cityCode", "_this3", "_callee5", "_cityMap$parentCode2", "_parentCode", "district", "_callee5$", "_context5", "abrupt", "features", "f", "properties", "includes", "adcode", "_this4", "_callee6", "_callee6$", "_context6", "loginTime", "formatDateTime", "ip", "ipaddr", "location", "loginLocation", "status", "$message", "msg", "_this5", "_callee7", "_callee7$", "_context7", "operateTime", "operTime", "module", "title", "operation", "operType", "time", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "_this6", "_callee8", "_callee8$", "_context8"], "sources": ["src/views/dashboard/home/<USER>"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日新增用户</div>\r\n            <div class=\"value\">{{ stats.todayUsers }}</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayUsersRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayUsersRate >= 0 ? '+' : '' }}{{ stats.todayUsersRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-user\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日订单金额</div>\r\n            <div class=\"value\">{{ stats.todayOrders }}</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayOrdersRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayOrdersRate >= 0 ? '+' : '' }}{{ stats.todayOrdersRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-s-order\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日提现额</div>\r\n            <div class=\"value\">￥{{ stats.todayWithdraw }}</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayWithdrawRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayWithdrawRate >= 0 ? '+' : '' }}{{ stats.todayWithdrawRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-money\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日新增广告设备</div>\r\n            <div class=\"value\">{{ stats.todayDevices }}</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayDevicesRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayDevicesRate >= 0 ? '+' : '' }}{{ stats.todayDevicesRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-monitor\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 日志明细 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n      <el-col :span=\"24\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>广告设备分布</span>\r\n            <el-breadcrumb v-if=\"currentArea.length > 0\" separator=\"/\" style=\"display: inline-block; margin-left: 15px;\">\r\n              <el-breadcrumb-item \r\n                v-for=\"(area, index) in currentArea\" \r\n                :key=\"index\"\r\n                @click.native=\"handleAreaClick(index)\">\r\n                {{ area.name }}\r\n              </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n          </div>\r\n          <div id=\"deviceMap\" style=\"height: 700px; margin-top: -10px; background: linear-gradient(to bottom, #020b1c, #0a2b5a);\"></div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n      <el-col :span=\"12\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>登录日志</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$router.push('/dashboard/log/login')\">\r\n              查看更多\r\n            </el-button>\r\n          </div>\r\n          <el-table :data=\"loginLogs\" stripe style=\"width: 100%\">\r\n            <el-table-column prop=\"loginTime\" label=\"登录时间\" width=\"180\" />\r\n            <el-table-column prop=\"ip\" label=\"登录IP\" width=\"140\" />\r\n            <el-table-column prop=\"location\" label=\"登录地点\" min-width=\"140\" />\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"scope.row.status === '1' ? 'success' : 'danger'\">\r\n                  {{ scope.row.status === '1' ? '成功' : '失败' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>操作日志</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$router.push('/dashboard/log/operation')\">\r\n              查看更多\r\n            </el-button>\r\n          </div>\r\n          <el-table :data=\"operationLogs\" stripe style=\"width: 100%\">\r\n            <el-table-column prop=\"operateTime\" label=\"操作时间\" width=\"180\" />\r\n            <el-table-column prop=\"module\" label=\"操作模块\" width=\"140\" />\r\n            <el-table-column prop=\"operation\" label=\"操作内容\" min-width=\"140\" />\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"scope.row.status === '1' ? 'success' : 'danger'\">\r\n                  {{ scope.row.status === '1' ? '成功' : '失败' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { getLoginLogList } from '@/api/log/login'\r\nimport { getOperLogList } from '@/api/log/operation'\r\nimport { getDashboardStats, getProvinceDeviceStats } from '@/api/dashboard'\r\n\r\nconst provinceMap = {\r\n  'guangdong': '440000',\r\n  'beijing': '110000',\r\n  'shanghai': '310000',\r\n  'tianjin': '120000',\r\n  'chongqing': '500000',\r\n  'hebei': '130000',\r\n  'shanxi': '140000',\r\n  'liaoning': '210000',\r\n  'jilin': '220000',\r\n  'heilongjiang': '230000',\r\n  'jiangsu': '320000',\r\n  'zhejiang': '330000',\r\n  'anhui': '340000',\r\n  'fujian': '350000',\r\n  'jiangxi': '360000',\r\n  'shandong': '370000',\r\n  'henan': '410000',\r\n  'hubei': '420000',\r\n  'hunan': '430000',\r\n  'guangxi': '450000',\r\n  'hainan': '460000',\r\n  'sichuan': '510000',\r\n  'guizhou': '520000',\r\n  'yunnan': '530000',\r\n  'xizang': '540000',\r\n  'shaanxi': '610000',\r\n  'gansu': '620000',\r\n  'qinghai': '630000',\r\n  'ningxia': '640000',\r\n  'xinjiang': '650000'\r\n}\r\n\r\nconst cityMap = {\r\n  '440000': {\r\n    'guangzhou': '440100',\r\n    'shenzhen': '440300',\r\n    'zhuhai': '440400',\r\n    'shantou': '440500',\r\n    'foshan': '440600',\r\n    'shaoguan': '440200',\r\n    'zhanjiang': '440800',\r\n    'zhaoqing': '441200',\r\n    'jiangmen': '440700',\r\n    'maoming': '440900',\r\n    'huizhou': '441300',\r\n    'meizhou': '441400',\r\n    'shanwei': '441500',\r\n    'heyuan': '441600',\r\n    'yangjiang': '441700',\r\n    'qingyuan': '441800',\r\n    'dongguan': '441900',\r\n    'zhongshan': '442000',\r\n    'chaozhou': '445100',\r\n    'jieyang': '445200',\r\n    'yunfu': '445300'\r\n  },\r\n  '110000': {\r\n    'dongcheng': '110101',\r\n    'xicheng': '110102',\r\n    'chaoyang': '110105',\r\n    'haidian': '110108',\r\n    'fengtai': '110106',\r\n    'shijingshan': '110107'\r\n  },\r\n  '310000': {\r\n    'huangpu': '310101',\r\n    'xuhui': '310104',\r\n    'changning': '310105',\r\n    'jingan': '310106',\r\n    'putuo': '310107',\r\n    'hongkou': '310109'\r\n  },\r\n  '320000': {\r\n    'nanjing': '320100',\r\n    'suzhou': '320500',\r\n    'wuxi': '320200',\r\n    'changzhou': '320400',\r\n    'nantong': '320600',\r\n    'yangzhou': '321000'\r\n  },\r\n  '330000': {\r\n    'hangzhou': '330100',\r\n    'ningbo': '330200',\r\n    'wenzhou': '330300',\r\n    'jiaxing': '330400',\r\n    'huzhou': '330500',\r\n    'shaoxing': '330600'\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  data() {\r\n    return {\r\n      // 登录日志\r\n      loginLogs: [],\r\n      // 操作日志\r\n      operationLogs: [],\r\n      // 添加新的地图关数据\r\n      mapChart: null,\r\n      currentArea: [], // 当前区域层级 [{name: '广东省', code: '440000'}, {name: '深圳市', code: '440300'}]\r\n      mapData: {\r\n        china: [], // 改为空数组，从后端获取数据\r\n        beijing: [\r\n          { name: '东城区', value: 25, hasDevice: true },\r\n          { name: '西城区', value: 30, hasDevice: true },\r\n          { name: '朝阳区', value: 45, hasDevice: true },\r\n          { name: '海淀区', value: 50, hasDevice: true },\r\n          { name: '丰台区', value: 28, hasDevice: true },\r\n          { name: '石景山区', value: 20, hasDevice: true }\r\n        ],\r\n        shanghai: [\r\n          { name: '黄浦区', value: 28, hasDevice: true },\r\n          { name: '徐汇区', value: 35, hasDevice: true },\r\n          { name: '长宁区', value: 30, hasDevice: true },\r\n          { name: '静安区', value: 40, hasDevice: true },\r\n          { name: '普陀区', value: 25, hasDevice: true },\r\n          { name: '虹口区', value: 22, hasDevice: true }\r\n        ],\r\n        jiangsu: [\r\n          { name: '南京', value: 55, children: true },\r\n          { name: '苏州', value: 60, children: true },\r\n          { name: '无锡', value: 45, children: true },\r\n          { name: '常州', value: 35, children: true },\r\n          { name: '南通', value: 30, children: true },\r\n          { name: '扬州', value: 25, children: true }\r\n        ],\r\n        zhejiang: [\r\n          { name: '杭州', value: 65, children: true },\r\n          { name: '宁波', value: 50, children: true },\r\n          { name: '温州', value: 40, children: true },\r\n          { name: '嘉兴', value: 30, children: true },\r\n          { name: '湖州', value: 25, children: true },\r\n          { name: '绍兴', value: 35, children: true }\r\n        ],\r\n        guangdong: [\r\n          { name: '广州', value: 45, children: true },\r\n          { name: '深圳', value: 50, children: true },\r\n          { name: '珠海', value: 25, children: true },\r\n          { name: '汕头', value: 20, children: true },\r\n          { name: '佛山', value: 35, children: true },\r\n          { name: '韶关', value: 15, children: true },\r\n          { name: '湛江', value: 18, children: true },\r\n          { name: '肇庆', value: 22, children: true },\r\n          { name: '江门', value: 28, children: true },\r\n          { name: '茂名', value: 20, children: true },\r\n          { name: '惠州', value: 30, children: true },\r\n          { name: '梅州', value: 15, children: true },\r\n          { name: '汕尾', value: 12, children: true },\r\n          { name: '河源', value: 18, children: true },\r\n          { name: '阳江', value: 16, children: true },\r\n          { name: '清远', value: 25, children: true },\r\n          { name: '东莞', value: 40, children: true },\r\n          { name: '中山', value: 32, children: true },\r\n          { name: '潮州', value: 15, children: true },\r\n          { name: '阳', value: 18, children: true },\r\n          { name: '云浮', value: 12, children: true }\r\n        ],\r\n        guangzhou: [\r\n          { name: '越秀区', value: 15, hasDevice: true },\r\n          { name: '海珠区', value: 12, hasDevice: true },\r\n          { name: '荔湾区', value: 10, hasDevice: true },\r\n          { name: '天河区', value: 20, hasDevice: true },\r\n          { name: '白云区', value: 18, hasDevice: true },\r\n          { name: '黄埔区', value: 15, hasDevice: true }\r\n        ],\r\n        shenzhen: [\r\n          { name: '福田区', value: 35, hasDevice: true },\r\n          { name: '罗湖区', value: 28, hasDevice: true },\r\n          { name: '南山区', value: 42, hasDevice: true },\r\n          { name: '宝安区', value: 38, hasDevice: true },\r\n          { name: '龙岗区', value: 32, hasDevice: true },\r\n          { name: '盐田区', value: 15, hasDevice: true },\r\n          { name: '龙华区', value: 30, hasDevice: true },\r\n          { name: '坪山区', value: 18, hasDevice: true },\r\n          { name: '光明区', value: 20, hasDevice: true },\r\n          { name: '大鹏新区', value: 12, hasDevice: true }\r\n        ]\r\n      },\r\n      // 修改查询参数\r\n      logQuery: {\r\n        pageNum: 1,\r\n        pageSize: 3,  // 只获取3条记录\r\n        orderByColumn: 'createTime',  // 按创建时间排序\r\n        isAsc: 'desc'  // 降序排序，最新的在前\r\n      },\r\n      // 添加统计数据\r\n      stats: {\r\n        todayUsers: 0,\r\n        todayUsersRate: 0,\r\n        todayOrders: 0,\r\n        todayOrdersRate: 0,\r\n        todayWithdraw: 0,\r\n        todayWithdrawRate: 0,\r\n        todayDevices: 0,\r\n        todayDevicesRate: 0\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initEchartsMap()\r\n    \r\n    this.getLoginLogs()\r\n    this.getOperationLogs()\r\n    this.getStats() // 添加获取统计数据的调用\r\n  \r\n  },\r\n  beforeDestroy() {\r\n    if (this.mapChart) {\r\n      this.mapChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    async initEchartsMap() {\r\n      try {\r\n        const chartDom = document.getElementById('deviceMap')\r\n        this.mapChart = echarts.init(chartDom)\r\n        \r\n        // 先加载中国地图\r\n        const response = await fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json')\r\n        const chinaMap = await response.json()\r\n        echarts.registerMap('china', chinaMap)\r\n        \r\n        // 获取省份设备数量数据\r\n        const res = await getProvinceDeviceStats()\r\n        if (res.code === 200) {\r\n          // 处理数据，添加 children 属性\r\n          this.mapData.china = res.data.map(item => ({\r\n            name: item.province.replace('省', '').replace('市', '').replace('自治区', ''), // 移除后缀以匹配地图数据\r\n            value: item.value,\r\n            children: true\r\n          }))\r\n        }\r\n        \r\n        // 设置地图配置\r\n        const option = {\r\n          tooltip: {\r\n            trigger: 'item',\r\n            formatter: '{b}<br/>设备数量：{c}'\r\n          },\r\n          visualMap: {\r\n            min: 0,\r\n            max: 200,\r\n            text: ['高', '低'],\r\n            realtime: false,\r\n            calculable: true,\r\n            inRange: {\r\n              color: ['#0a2b5a', '#4ecdc4']\r\n            },\r\n            textStyle: {\r\n              color: '#fff'  // 设置文字颜色为白色\r\n            }\r\n          },\r\n          series: [{\r\n            name: '设备分布',\r\n            type: 'map',\r\n            map: 'china',\r\n            roam: true,\r\n            label: {\r\n              show: true,\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            itemStyle: {\r\n              areaColor: '#0c2c5a',\r\n              borderColor: '#00fcff',\r\n              borderWidth: 1\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                color: '#fff'\r\n              },\r\n              itemStyle: {\r\n                areaColor: '#4ecdc4'\r\n              }\r\n            },\r\n            data: this.mapData.china\r\n          }]\r\n        }\r\n        \r\n        this.mapChart.setOption(option)\r\n        \r\n        // 添加点击事件处理\r\n        this.mapChart.off('click').on('click', async params => {\r\n          console.log('Clicked area:', params.name)\r\n          const data = this.mapData[params.name.toLowerCase()]\r\n          if (data && data.children) {\r\n            // 处理地区名称，移除各种后缀\r\n            const areaName = params.name.replace(/(省|市|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '')\r\n            const nextLevel = areaName.toLowerCase()\r\n            console.log('Next level:', nextLevel)\r\n\r\n            // 获取下一级区域的编码\r\n            const code = await this.getNextLevelCode(areaName, this.currentArea.length)\r\n            console.log('Next level code:', code)\r\n            if (code) {\r\n              this.currentArea.push({\r\n                name: params.name,\r\n                code: code\r\n              })\r\n              this.loadMap(nextLevel)\r\n            }\r\n          }\r\n        })\r\n\r\n        // 添加窗口大小改变事件监听\r\n        window.addEventListener('resize', () => {\r\n          this.mapChart.resize()\r\n        })\r\n\r\n      } catch (error) {\r\n        console.error('初始化地图失败:', error)\r\n      }\r\n    },\r\n\r\n    async loadMap(mapName, data = null) {\r\n      try {\r\n        if (mapName !== 'china') {\r\n          const code = this.getAreaCode(mapName)\r\n          console.log('Loading map for:', mapName, 'with code:', code)\r\n          const response = await fetch(`https://geo.datav.aliyun.com/areas_v3/bound/${code}_full.json`)\r\n          const mapJson = await response.json()\r\n          echarts.registerMap(mapName, mapJson)\r\n        }\r\n\r\n        const mapData = data || this.mapData[mapName.toLowerCase()] || []\r\n        console.log('Map data for:', mapName, mapData)\r\n\r\n        // 抽取地图配置\r\n        const mapOption = {\r\n          tooltip: {\r\n            trigger: 'item',\r\n            formatter: params => {\r\n              const value = params.value || 0\r\n              return `${params.name}\\n${value}`\r\n            }\r\n          },\r\n          visualMap: {\r\n            min: 0,\r\n            max: 200,\r\n            text: ['高', '低'],\r\n            realtime: false,\r\n            calculable: true,\r\n            inRange: {\r\n              color: ['#ffe4e1', '#ff4500']\r\n            }\r\n          },\r\n          series: [{\r\n            name: '设备分布',\r\n            type: 'map',\r\n            map: mapName,\r\n            roam: true,\r\n            zoom: 1.5,\r\n            center: [104.297, 35.861],\r\n            scaleLimit: {\r\n              min: 1,\r\n              max: 5\r\n            },\r\n            label: {\r\n              show: true,\r\n              formatter: params => {\r\n                const value = params.value || 0\r\n                return `${params.name}\\n${value}`\r\n              },\r\n              fontSize: 14,\r\n              color: '#fff',\r\n              backgroundColor: 'rgba(0, 150, 255, 0.5)',\r\n              padding: [4, 8],\r\n              borderRadius: 4,\r\n              textBorderWidth: 2,\r\n              textBorderColor: 'rgba(0, 0, 0, 0.8)'\r\n            },\r\n            itemStyle: {\r\n              areaColor: '#0c2c5a',\r\n              borderColor: '#00fcff',\r\n              borderWidth: 1.5,\r\n              shadowColor: 'rgba(0, 252, 255, 0.3)',\r\n              shadowBlur: 10\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                color: '#fff',\r\n                fontSize: 16,\r\n                backgroundColor: 'rgba(0, 150, 255, 0.8)'\r\n              },\r\n              itemStyle: {\r\n                areaColor: '#0052d9',\r\n                borderColor: '#00fcff',\r\n                borderWidth: 2,\r\n                shadowColor: 'rgba(0, 252, 255, 0.5)',\r\n                shadowBlur: 15\r\n              }\r\n            },\r\n            data: mapData.map(item => ({\r\n              name: item.name,\r\n              value: item.value || 0,\r\n              children: item.children,\r\n              hasDevice: item.hasDevice,\r\n              itemStyle: item.hasDevice ? {\r\n                areaColor: '#ff4500',\r\n                shadowColor: 'rgba(255, 69, 0, 0.5)',\r\n                shadowBlur: 10\r\n              } : null\r\n            }))\r\n          }]\r\n        }\r\n\r\n        this.mapChart.setOption(mapOption)\r\n\r\n        // 修改点击事件处理\r\n        this.mapChart.off('click').on('click', async params => {\r\n          console.log('Clicked area:', params.name)\r\n          const data = mapData.find(item => item.name === params.name)\r\n          if (data && data.children) {\r\n            // 处理地区名称，移除各种后缀\r\n            const areaName = params.name.replace(/(省|市|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '')\r\n            const nextLevel = areaName.toLowerCase()\r\n            console.log('Next level:', nextLevel)\r\n\r\n            // 获取下一级区域的编码\r\n            const code = await this.getNextLevelCode(areaName, this.currentArea.length)\r\n            console.log('Next level code:', code)\r\n            if (code) {\r\n              this.currentArea.push({\r\n                name: params.name,\r\n                code: code\r\n              })\r\n              this.loadMap(nextLevel)\r\n            }\r\n          }\r\n        })\r\n      } catch (error) {\r\n        console.error('加载地图数据失败:', error)\r\n      }\r\n    },\r\n\r\n    handleAreaClick(index) {\r\n      if (index === 0) {\r\n        this.currentArea = []\r\n        this.loadMap('china')\r\n      } else {\r\n        this.currentArea = this.currentArea.slice(0, index + 1)\r\n        const areaName = this.currentArea[index].name\r\n          .replace(/(省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '')\r\n        this.loadMap(areaName.toLowerCase())\r\n      }\r\n    },\r\n\r\n    getAreaCode(name) {\r\n      const currentLevel = this.currentArea.length\r\n      const areaName = name.toLowerCase()\r\n      console.log('Getting area code for:', name, 'level:', currentLevel)\r\n      \r\n      if (currentLevel === 0) {\r\n        return '100000'\r\n      } else if (currentLevel === 1) {\r\n        return provinceMap[areaName] || '100000'\r\n      } else if (currentLevel === 2) {\r\n        const parentCode = this.currentArea[0].code\r\n        const cityCode = cityMap[parentCode]?.[areaName]\r\n        console.log('City code:', cityCode, 'for parent:', parentCode)\r\n        return cityCode || parentCode\r\n      }\r\n      return '100000'\r\n    },\r\n\r\n    async getNextLevelCode(name, currentLevel) {\r\n      try {\r\n        const areaName = name.toLowerCase()\r\n        if (currentLevel === 0) {\r\n          // 获取省级编码\r\n          return provinceMap[areaName]\r\n        } else if (currentLevel === 1) {\r\n          // 获取市级编码\r\n          const parentCode = this.currentArea[0].code\r\n          return cityMap[parentCode]?.[areaName]\r\n        } else if (currentLevel === 2) {\r\n          // 获取区级编码\r\n          const parentCode = this.currentArea[1].code\r\n          const response = await fetch(`https://geo.datav.aliyun.com/areas_v3/bound/${parentCode}.json`)\r\n          const json = await response.json()\r\n          const district = json.features.find(f => \r\n            f.properties.name.includes(name) || \r\n            name.includes(f.properties.name)\r\n          )\r\n          return district?.properties.adcode\r\n        }\r\n      } catch (error) {\r\n        console.error('获取区域编码失败:', error)\r\n        return null\r\n      }\r\n    },\r\n\r\n    // 获取登录日志\r\n    async getLoginLogs() {\r\n      try {\r\n        const res = await getLoginLogList(this.logQuery)\r\n        if (res.code === 0 || res.code === 200) {\r\n        \r\n          this.loginLogs = res.data.map(item => ({ \r\n            loginTime: this.formatDateTime(item.loginTime),\r\n            ip: item.ipaddr,\r\n            location: `${item.loginLocation }`,\r\n            status: item.status\r\n          }))\r\n        } else {\r\n          this.$message.error(res.msg || '获取登录日志失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取登录日志失败:', error)\r\n        this.$message.error('获取登录日志失败')\r\n      }\r\n    },\r\n\r\n    // 获取操作日志\r\n    async getOperationLogs() {\r\n      try {\r\n        console.log('数据');\r\n        const res = await getOperLogList(this.logQuery)\r\n     \r\n        console.log(res.data);\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.operationLogs = res.data.map(item => ({\r\n            operateTime: this.formatDateTime(item.operTime),\r\n            module: item.title,  // 使用 title 作为模块名\r\n            operation: item.operType,  // 使用 operName 作为操作内容\r\n            status: item.status\r\n          }))\r\n        } else {\r\n          this.$message.error(res.msg || '获取操作日志失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取操作日志失败:', error)\r\n        this.$message.error('获取操作日志失败')\r\n      }\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      const date = new Date(time)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n\r\n    // 获取统计数据\r\n    async getStats() {\r\n      try {\r\n        const res = await getDashboardStats()\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.stats = {\r\n            todayUsers: res.data.todayUsers || 0,\r\n            todayUsersRate: res.data.todayUsersRate || 0,\r\n            todayOrders: res.data.todayOrders || 0,\r\n            todayOrdersRate: res.data.todayOrdersRate || 0,\r\n            todayWithdraw: res.data.todayWithdraw || 0,\r\n            todayWithdrawRate: res.data.todayWithdrawRate || 0,\r\n            todayDevices: res.data.todayDevices || 0,\r\n            todayDevicesRate: res.data.todayDevicesRate || 0\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('获取统计数据失败:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  height: calc(100vh - 84px);  // 修复滚动条问题\r\n  overflow-y: auto;  // 添加垂直滚动\r\n\r\n  .dashboard-card {\r\n    position: relative;\r\n    height: 120px;\r\n    overflow: hidden;\r\n\r\n    .card-header {\r\n      position: relative;\r\n      z-index: 1;\r\n\r\n      .title {\r\n        font-size: 14px;\r\n        color: #909399;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 24px;\r\n        font-weight: bold;\r\n        color: #303133;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .trend {\r\n        font-size: 13px;\r\n        color: #909399;\r\n\r\n        .up {\r\n          color: #67C23A;\r\n        }\r\n\r\n        .down {\r\n          color: #F56C6C;\r\n        }\r\n      }\r\n    }\r\n\r\n    .icon {\r\n      position: absolute;\r\n      right: 20px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      font-size: 48px;\r\n      opacity: 0.1;\r\n      transition: all 0.3s;\r\n\r\n      i {\r\n        color: #1890ff;\r\n      }\r\n    }\r\n\r\n    &:hover {\r\n      .icon {\r\n        opacity: 0.2;\r\n        transform: translateY(-50%) scale(1.1);\r\n      }\r\n    }\r\n  }\r\n\r\n  #userMap,\r\n  #deviceMap {\r\n    height: 700px;\r\n    width: 100%;\r\n    margin-top: -10px;\r\n    background: linear-gradient(to bottom, #020b1c, #0a2b5a);\r\n  }\r\n}\r\n\r\n.el-breadcrumb {\r\n  display: inline-block;\r\n  margin-left: 15px;\r\n  \r\n  :deep(.el-breadcrumb__item) {\r\n    cursor: pointer;\r\n    \r\n    &:hover {\r\n      color: #409EFF;\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;AA+IA,YAAAA,OAAA;AACA,SAAAC,eAAA;AACA,SAAAC,cAAA;AACA,SAAAC,iBAAA,EAAAC,sBAAA;AAEA,IAAAC,WAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AAEA,IAAAC,OAAA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MACA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,WAAA;MAAA;MACAC,OAAA;QACAC,KAAA;QAAA;QACAC,OAAA,GACA;UAAAR,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAX,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,EACA;QACAE,OAAA,GACA;UAAAZ,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAd,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,EACA;QACAE,SAAA,GACA;UAAAf,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,GACA;UAAAb,IAAA;UAAAS,KAAA;UAAAI,QAAA;QAAA,EACA;QACAG,SAAA,GACA;UAAAhB,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,EACA;QACAO,QAAA,GACA;UAAAjB,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA,GACA;UAAAV,IAAA;UAAAS,KAAA;UAAAC,SAAA;QAAA;MAEA;MACA;MACAQ,QAAA;QACAC,OAAA;QACAC,QAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;MACA;MACA;MACAC,KAAA;QACAC,UAAA;QACAC,cAAA;QACAC,WAAA;QACAC,eAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,YAAA;QACAC,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;IAEA,KAAAC,YAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,QAAA;EAEA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAjC,QAAA;MACA,KAAAA,QAAA,CAAAkC,OAAA;IACA;EACA;EACAC,OAAA;IACAN,cAAA,WAAAA,eAAA;MAAA,IAAAO,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,SAAA;QAAA,IAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,GAAA,EAAAC,MAAA;QAAA,OAAAP,mBAAA,GAAAQ,IAAA,UAAAC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;YAAA;cAAAF,SAAA,CAAAC,IAAA;cAEAR,QAAA,GAAAU,QAAA,CAAAC,cAAA;cACAhB,KAAA,CAAApC,QAAA,GAAAX,OAAA,CAAAgE,IAAA,CAAAZ,QAAA;;cAEA;cAAAO,SAAA,CAAAE,IAAA;cAAA,OACAI,KAAA;YAAA;cAAAZ,QAAA,GAAAM,SAAA,CAAAO,IAAA;cAAAP,SAAA,CAAAE,IAAA;cAAA,OACAR,QAAA,CAAAc,IAAA;YAAA;cAAAb,QAAA,GAAAK,SAAA,CAAAO,IAAA;cACAlE,OAAA,CAAAoE,WAAA,UAAAd,QAAA;;cAEA;cAAAK,SAAA,CAAAE,IAAA;cAAA,OACAzD,sBAAA;YAAA;cAAAmD,GAAA,GAAAI,SAAA,CAAAO,IAAA;cACA,IAAAX,GAAA,CAAAc,IAAA;gBACA;gBACAtB,KAAA,CAAAlC,OAAA,CAAAC,KAAA,GAAAyC,GAAA,CAAA/C,IAAA,CAAA8D,GAAA,WAAAC,IAAA;kBAAA;oBACAhE,IAAA,EAAAgE,IAAA,CAAAC,QAAA,CAAAC,OAAA,UAAAA,OAAA,UAAAA,OAAA;oBAAA;oBACAzD,KAAA,EAAAuD,IAAA,CAAAvD,KAAA;oBACAI,QAAA;kBACA;gBAAA;cACA;;cAEA;cACAoC,MAAA;gBACAkB,OAAA;kBACAC,OAAA;kBACAC,SAAA;gBACA;gBACAC,SAAA;kBACAC,GAAA;kBACAC,GAAA;kBACAC,IAAA;kBACAC,QAAA;kBACAC,UAAA;kBACAC,OAAA;oBACAC,KAAA;kBACA;kBACAC,SAAA;oBACAD,KAAA;kBACA;gBACA;gBACAE,MAAA;kBACA/E,IAAA;kBACAgF,IAAA;kBACAjB,GAAA;kBACAkB,IAAA;kBACAC,KAAA;oBACAC,IAAA;oBACAN,KAAA;oBACAO,QAAA;kBACA;kBACAC,SAAA;oBACAC,SAAA;oBACAC,WAAA;oBACAC,WAAA;kBACA;kBACAC,QAAA;oBACAP,KAAA;sBACAC,IAAA;sBACAN,KAAA;oBACA;oBACAQ,SAAA;sBACAC,SAAA;oBACA;kBACA;kBACArF,IAAA,EAAAuC,KAAA,CAAAlC,OAAA,CAAAC;gBACA;cACA;cAEAiC,KAAA,CAAApC,QAAA,CAAAsF,SAAA,CAAAzC,MAAA;;cAEA;cACAT,KAAA,CAAApC,QAAA,CAAAuF,GAAA,UAAAC,EAAA;gBAAA,IAAAC,IAAA,GAAApD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmD,QAAAC,MAAA;kBAAA,IAAA9F,IAAA,EAAA+F,QAAA,EAAAC,SAAA,EAAAnC,IAAA;kBAAA,OAAApB,mBAAA,GAAAQ,IAAA,UAAAgD,SAAAC,QAAA;oBAAA,kBAAAA,QAAA,CAAA9C,IAAA,GAAA8C,QAAA,CAAA7C,IAAA;sBAAA;wBACA8C,OAAA,CAAAC,GAAA,kBAAAN,MAAA,CAAA/F,IAAA;wBACAC,IAAA,GAAAuC,KAAA,CAAAlC,OAAA,CAAAyF,MAAA,CAAA/F,IAAA,CAAAsG,WAAA;wBAAA,MACArG,IAAA,IAAAA,IAAA,CAAAY,QAAA;0BAAAsF,QAAA,CAAA7C,IAAA;0BAAA;wBAAA;wBACA;wBACA0C,QAAA,GAAAD,MAAA,CAAA/F,IAAA,CAAAkE,OAAA;wBACA+B,SAAA,GAAAD,QAAA,CAAAM,WAAA;wBACAF,OAAA,CAAAC,GAAA,gBAAAJ,SAAA;;wBAEA;wBAAAE,QAAA,CAAA7C,IAAA;wBAAA,OACAd,KAAA,CAAA+D,gBAAA,CAAAP,QAAA,EAAAxD,KAAA,CAAAnC,WAAA,CAAAmG,MAAA;sBAAA;wBAAA1C,IAAA,GAAAqC,QAAA,CAAAxC,IAAA;wBACAyC,OAAA,CAAAC,GAAA,qBAAAvC,IAAA;wBACA,IAAAA,IAAA;0BACAtB,KAAA,CAAAnC,WAAA,CAAAoG,IAAA;4BACAzG,IAAA,EAAA+F,MAAA,CAAA/F,IAAA;4BACA8D,IAAA,EAAAA;0BACA;0BACAtB,KAAA,CAAAkE,OAAA,CAAAT,SAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAAE,QAAA,CAAAQ,IAAA;oBAAA;kBAAA,GAAAb,OAAA;gBAAA,CAEA;gBAAA,iBAAAc,EAAA;kBAAA,OAAAf,IAAA,CAAAgB,KAAA,OAAAC,SAAA;gBAAA;cAAA;;cAEA;cACAC,MAAA,CAAAC,gBAAA;gBACAxE,KAAA,CAAApC,QAAA,CAAA6G,MAAA;cACA;cAAA7D,SAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,SAAA,CAAAC,IAAA;cAAAD,SAAA,CAAA8D,EAAA,GAAA9D,SAAA;cAGAgD,OAAA,CAAAe,KAAA,aAAA/D,SAAA,CAAA8D,EAAA;YAAA;YAAA;cAAA,OAAA9D,SAAA,CAAAuD,IAAA;UAAA;QAAA,GAAA/D,QAAA;MAAA;IAEA;IAEA8D,OAAA,WAAAA,QAAAU,OAAA;MAAA,IAAAC,UAAA,GAAAP,SAAA;QAAAQ,MAAA;MAAA,OAAA7E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4E,SAAA;QAAA,IAAAtH,IAAA,EAAA6D,IAAA,EAAAhB,QAAA,EAAA0E,OAAA,EAAAlH,OAAA,EAAAmH,SAAA;QAAA,OAAA/E,mBAAA,GAAAQ,IAAA,UAAAwE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtE,IAAA,GAAAsE,SAAA,CAAArE,IAAA;YAAA;cAAArD,IAAA,GAAAoH,UAAA,CAAAb,MAAA,QAAAa,UAAA,QAAAO,SAAA,GAAAP,UAAA;cAAAM,SAAA,CAAAtE,IAAA;cAAA,MAEA+D,OAAA;gBAAAO,SAAA,CAAArE,IAAA;gBAAA;cAAA;cACAQ,IAAA,GAAAwD,MAAA,CAAAO,WAAA,CAAAT,OAAA;cACAhB,OAAA,CAAAC,GAAA,qBAAAe,OAAA,gBAAAtD,IAAA;cAAA6D,SAAA,CAAArE,IAAA;cAAA,OACAI,KAAA,gDAAAoE,MAAA,CAAAhE,IAAA;YAAA;cAAAhB,QAAA,GAAA6E,SAAA,CAAAhE,IAAA;cAAAgE,SAAA,CAAArE,IAAA;cAAA,OACAR,QAAA,CAAAc,IAAA;YAAA;cAAA4D,OAAA,GAAAG,SAAA,CAAAhE,IAAA;cACAlE,OAAA,CAAAoE,WAAA,CAAAuD,OAAA,EAAAI,OAAA;YAAA;cAGAlH,OAAA,GAAAL,IAAA,IAAAqH,MAAA,CAAAhH,OAAA,CAAA8G,OAAA,CAAAd,WAAA;cACAF,OAAA,CAAAC,GAAA,kBAAAe,OAAA,EAAA9G,OAAA;;cAEA;cACAmH,SAAA;gBACAtD,OAAA;kBACAC,OAAA;kBACAC,SAAA,WAAAA,UAAA0B,MAAA;oBACA,IAAAtF,KAAA,GAAAsF,MAAA,CAAAtF,KAAA;oBACA,UAAAqH,MAAA,CAAA/B,MAAA,CAAA/F,IAAA,QAAA8H,MAAA,CAAArH,KAAA;kBACA;gBACA;gBACA6D,SAAA;kBACAC,GAAA;kBACAC,GAAA;kBACAC,IAAA;kBACAC,QAAA;kBACAC,UAAA;kBACAC,OAAA;oBACAC,KAAA;kBACA;gBACA;gBACAE,MAAA;kBACA/E,IAAA;kBACAgF,IAAA;kBACAjB,GAAA,EAAAqD,OAAA;kBACAnC,IAAA;kBACA8C,IAAA;kBACAC,MAAA;kBACAC,UAAA;oBACA1D,GAAA;oBACAC,GAAA;kBACA;kBACAU,KAAA;oBACAC,IAAA;oBACAd,SAAA,WAAAA,UAAA0B,MAAA;sBACA,IAAAtF,KAAA,GAAAsF,MAAA,CAAAtF,KAAA;sBACA,UAAAqH,MAAA,CAAA/B,MAAA,CAAA/F,IAAA,QAAA8H,MAAA,CAAArH,KAAA;oBACA;oBACA2E,QAAA;oBACAP,KAAA;oBACAqD,eAAA;oBACAC,OAAA;oBACAC,YAAA;oBACAC,eAAA;oBACAC,eAAA;kBACA;kBACAjD,SAAA;oBACAC,SAAA;oBACAC,WAAA;oBACAC,WAAA;oBACA+C,WAAA;oBACAC,UAAA;kBACA;kBACA/C,QAAA;oBACAP,KAAA;sBACAC,IAAA;sBACAN,KAAA;sBACAO,QAAA;sBACA8C,eAAA;oBACA;oBACA7C,SAAA;sBACAC,SAAA;sBACAC,WAAA;sBACAC,WAAA;sBACA+C,WAAA;sBACAC,UAAA;oBACA;kBACA;kBACAvI,IAAA,EAAAK,OAAA,CAAAyD,GAAA,WAAAC,IAAA;oBAAA;sBACAhE,IAAA,EAAAgE,IAAA,CAAAhE,IAAA;sBACAS,KAAA,EAAAuD,IAAA,CAAAvD,KAAA;sBACAI,QAAA,EAAAmD,IAAA,CAAAnD,QAAA;sBACAH,SAAA,EAAAsD,IAAA,CAAAtD,SAAA;sBACA2E,SAAA,EAAArB,IAAA,CAAAtD,SAAA;wBACA4E,SAAA;wBACAiD,WAAA;wBACAC,UAAA;sBACA;oBACA;kBAAA;gBACA;cACA;cAEAlB,MAAA,CAAAlH,QAAA,CAAAsF,SAAA,CAAA+B,SAAA;;cAEA;cACAH,MAAA,CAAAlH,QAAA,CAAAuF,GAAA,UAAAC,EAAA;gBAAA,IAAA6C,KAAA,GAAAhG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+F,SAAA3C,MAAA;kBAAA,IAAA9F,IAAA,EAAA+F,QAAA,EAAAC,SAAA,EAAA0C,KAAA;kBAAA,OAAAjG,mBAAA,GAAAQ,IAAA,UAAA0F,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;sBAAA;wBACA8C,OAAA,CAAAC,GAAA,kBAAAN,MAAA,CAAA/F,IAAA;wBACAC,IAAA,GAAAK,OAAA,CAAAwI,IAAA,WAAA9E,IAAA;0BAAA,OAAAA,IAAA,CAAAhE,IAAA,KAAA+F,MAAA,CAAA/F,IAAA;wBAAA;wBAAA,MACAC,IAAA,IAAAA,IAAA,CAAAY,QAAA;0BAAAgI,SAAA,CAAAvF,IAAA;0BAAA;wBAAA;wBACA;wBACA0C,QAAA,GAAAD,MAAA,CAAA/F,IAAA,CAAAkE,OAAA;wBACA+B,SAAA,GAAAD,QAAA,CAAAM,WAAA;wBACAF,OAAA,CAAAC,GAAA,gBAAAJ,SAAA;;wBAEA;wBAAA4C,SAAA,CAAAvF,IAAA;wBAAA,OACAgE,MAAA,CAAAf,gBAAA,CAAAP,QAAA,EAAAsB,MAAA,CAAAjH,WAAA,CAAAmG,MAAA;sBAAA;wBAAA1C,KAAA,GAAA+E,SAAA,CAAAlF,IAAA;wBACAyC,OAAA,CAAAC,GAAA,qBAAAvC,KAAA;wBACA,IAAAA,KAAA;0BACAwD,MAAA,CAAAjH,WAAA,CAAAoG,IAAA;4BACAzG,IAAA,EAAA+F,MAAA,CAAA/F,IAAA;4BACA8D,IAAA,EAAAA;0BACA;0BACAwD,MAAA,CAAAZ,OAAA,CAAAT,SAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAA4C,SAAA,CAAAlC,IAAA;oBAAA;kBAAA,GAAA+B,QAAA;gBAAA,CAEA;gBAAA,iBAAAK,GAAA;kBAAA,OAAAN,KAAA,CAAA5B,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAAa,SAAA,CAAArE,IAAA;cAAA;YAAA;cAAAqE,SAAA,CAAAtE,IAAA;cAAAsE,SAAA,CAAAT,EAAA,GAAAS,SAAA;cAEAvB,OAAA,CAAAe,KAAA,cAAAQ,SAAA,CAAAT,EAAA;YAAA;YAAA;cAAA,OAAAS,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IAEA;IAEAyB,eAAA,WAAAA,gBAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAA5I,WAAA;QACA,KAAAqG,OAAA;MACA;QACA,KAAArG,WAAA,QAAAA,WAAA,CAAA6I,KAAA,IAAAD,KAAA;QACA,IAAAjD,QAAA,QAAA3F,WAAA,CAAA4I,KAAA,EAAAjJ,IAAA,CACAkE,OAAA;QACA,KAAAwC,OAAA,CAAAV,QAAA,CAAAM,WAAA;MACA;IACA;IAEAuB,WAAA,WAAAA,YAAA7H,IAAA;MACA,IAAAmJ,YAAA,QAAA9I,WAAA,CAAAmG,MAAA;MACA,IAAAR,QAAA,GAAAhG,IAAA,CAAAsG,WAAA;MACAF,OAAA,CAAAC,GAAA,2BAAArG,IAAA,YAAAmJ,YAAA;MAEA,IAAAA,YAAA;QACA;MACA,WAAAA,YAAA;QACA,OAAArJ,WAAA,CAAAkG,QAAA;MACA,WAAAmD,YAAA;QAAA,IAAAC,mBAAA;QACA,IAAAC,UAAA,QAAAhJ,WAAA,IAAAyD,IAAA;QACA,IAAAwF,QAAA,IAAAF,mBAAA,GAAArJ,OAAA,CAAAsJ,UAAA,eAAAD,mBAAA,uBAAAA,mBAAA,CAAApD,QAAA;QACAI,OAAA,CAAAC,GAAA,eAAAiD,QAAA,iBAAAD,UAAA;QACA,OAAAC,QAAA,IAAAD,UAAA;MACA;MACA;IACA;IAEA9C,gBAAA,WAAAA,iBAAAvG,IAAA,EAAAmJ,YAAA;MAAA,IAAAI,MAAA;MAAA,OAAA9G,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6G,SAAA;QAAA,IAAAxD,QAAA,EAAAyD,oBAAA,EAAAJ,UAAA,EAAAK,WAAA,EAAA5G,QAAA,EAAAc,IAAA,EAAA+F,QAAA;QAAA,OAAAjH,mBAAA,GAAAQ,IAAA,UAAA0G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxG,IAAA,GAAAwG,SAAA,CAAAvG,IAAA;YAAA;cAAAuG,SAAA,CAAAxG,IAAA;cAEA2C,QAAA,GAAAhG,IAAA,CAAAsG,WAAA;cAAA,MACA6C,YAAA;gBAAAU,SAAA,CAAAvG,IAAA;gBAAA;cAAA;cAAA,OAAAuG,SAAA,CAAAC,MAAA,WAEAhK,WAAA,CAAAkG,QAAA;YAAA;cAAA,MACAmD,YAAA;gBAAAU,SAAA,CAAAvG,IAAA;gBAAA;cAAA;cACA;cACA+F,UAAA,GAAAE,MAAA,CAAAlJ,WAAA,IAAAyD,IAAA;cAAA,OAAA+F,SAAA,CAAAC,MAAA,YAAAL,oBAAA,GACA1J,OAAA,CAAAsJ,UAAA,eAAAI,oBAAA,uBAAAA,oBAAA,CAAAzD,QAAA;YAAA;cAAA,MACAmD,YAAA;gBAAAU,SAAA,CAAAvG,IAAA;gBAAA;cAAA;cACA;cACA+F,WAAA,GAAAE,MAAA,CAAAlJ,WAAA,IAAAyD,IAAA;cAAA+F,SAAA,CAAAvG,IAAA;cAAA,OACAI,KAAA,gDAAAoE,MAAA,CAAAuB,WAAA;YAAA;cAAAvG,QAAA,GAAA+G,SAAA,CAAAlG,IAAA;cAAAkG,SAAA,CAAAvG,IAAA;cAAA,OACAR,QAAA,CAAAc,IAAA;YAAA;cAAAA,IAAA,GAAAiG,SAAA,CAAAlG,IAAA;cACAgG,QAAA,GAAA/F,IAAA,CAAAmG,QAAA,CAAAjB,IAAA,WAAAkB,CAAA;gBAAA,OACAA,CAAA,CAAAC,UAAA,CAAAjK,IAAA,CAAAkK,QAAA,CAAAlK,IAAA,KACAA,IAAA,CAAAkK,QAAA,CAAAF,CAAA,CAAAC,UAAA,CAAAjK,IAAA;cAAA,CACA;cAAA,OAAA6J,SAAA,CAAAC,MAAA,WACAH,QAAA,aAAAA,QAAA,uBAAAA,QAAA,CAAAM,UAAA,CAAAE,MAAA;YAAA;cAAAN,SAAA,CAAAvG,IAAA;cAAA;YAAA;cAAAuG,SAAA,CAAAxG,IAAA;cAAAwG,SAAA,CAAA3C,EAAA,GAAA2C,SAAA;cAGAzD,OAAA,CAAAe,KAAA,cAAA0C,SAAA,CAAA3C,EAAA;cAAA,OAAA2C,SAAA,CAAAC,MAAA,WACA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAlD,IAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IAEA;IAEA;IACAtH,YAAA,WAAAA,aAAA;MAAA,IAAAkI,MAAA;MAAA,OAAA3H,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0H,SAAA;QAAA,IAAArH,GAAA;QAAA,OAAAN,mBAAA,GAAAQ,IAAA,UAAAoH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlH,IAAA,GAAAkH,SAAA,CAAAjH,IAAA;YAAA;cAAAiH,SAAA,CAAAlH,IAAA;cAAAkH,SAAA,CAAAjH,IAAA;cAAA,OAEA5D,eAAA,CAAA0K,MAAA,CAAAlJ,QAAA;YAAA;cAAA8B,GAAA,GAAAuH,SAAA,CAAA5G,IAAA;cACA,IAAAX,GAAA,CAAAc,IAAA,UAAAd,GAAA,CAAAc,IAAA;gBAEAsG,MAAA,CAAAlK,SAAA,GAAA8C,GAAA,CAAA/C,IAAA,CAAA8D,GAAA,WAAAC,IAAA;kBAAA;oBACAwG,SAAA,EAAAJ,MAAA,CAAAK,cAAA,CAAAzG,IAAA,CAAAwG,SAAA;oBACAE,EAAA,EAAA1G,IAAA,CAAA2G,MAAA;oBACAC,QAAA,KAAA9C,MAAA,CAAA9D,IAAA,CAAA6G,aAAA;oBACAC,MAAA,EAAA9G,IAAA,CAAA8G;kBACA;gBAAA;cACA;gBACAV,MAAA,CAAAW,QAAA,CAAA5D,KAAA,CAAAnE,GAAA,CAAAgI,GAAA;cACA;cAAAT,SAAA,CAAAjH,IAAA;cAAA;YAAA;cAAAiH,SAAA,CAAAlH,IAAA;cAAAkH,SAAA,CAAArD,EAAA,GAAAqD,SAAA;cAEAnE,OAAA,CAAAe,KAAA,cAAAoD,SAAA,CAAArD,EAAA;cACAkD,MAAA,CAAAW,QAAA,CAAA5D,KAAA;YAAA;YAAA;cAAA,OAAAoD,SAAA,CAAA5D,IAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA;IAEA;IAEA;IACAlI,gBAAA,WAAAA,iBAAA;MAAA,IAAA8I,MAAA;MAAA,OAAAxI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuI,SAAA;QAAA,IAAAlI,GAAA;QAAA,OAAAN,mBAAA,GAAAQ,IAAA,UAAAiI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/H,IAAA,GAAA+H,SAAA,CAAA9H,IAAA;YAAA;cAAA8H,SAAA,CAAA/H,IAAA;cAEA+C,OAAA,CAAAC,GAAA;cAAA+E,SAAA,CAAA9H,IAAA;cAAA,OACA3D,cAAA,CAAAsL,MAAA,CAAA/J,QAAA;YAAA;cAAA8B,GAAA,GAAAoI,SAAA,CAAAzH,IAAA;cAEAyC,OAAA,CAAAC,GAAA,CAAArD,GAAA,CAAA/C,IAAA;cACA,IAAA+C,GAAA,CAAAc,IAAA,UAAAd,GAAA,CAAAc,IAAA;gBACAmH,MAAA,CAAA9K,aAAA,GAAA6C,GAAA,CAAA/C,IAAA,CAAA8D,GAAA,WAAAC,IAAA;kBAAA;oBACAqH,WAAA,EAAAJ,MAAA,CAAAR,cAAA,CAAAzG,IAAA,CAAAsH,QAAA;oBACAC,MAAA,EAAAvH,IAAA,CAAAwH,KAAA;oBAAA;oBACAC,SAAA,EAAAzH,IAAA,CAAA0H,QAAA;oBAAA;oBACAZ,MAAA,EAAA9G,IAAA,CAAA8G;kBACA;gBAAA;cACA;gBACAG,MAAA,CAAAF,QAAA,CAAA5D,KAAA,CAAAnE,GAAA,CAAAgI,GAAA;cACA;cAAAI,SAAA,CAAA9H,IAAA;cAAA;YAAA;cAAA8H,SAAA,CAAA/H,IAAA;cAAA+H,SAAA,CAAAlE,EAAA,GAAAkE,SAAA;cAEAhF,OAAA,CAAAe,KAAA,cAAAiE,SAAA,CAAAlE,EAAA;cACA+D,MAAA,CAAAF,QAAA,CAAA5D,KAAA;YAAA;YAAA;cAAA,OAAAiE,SAAA,CAAAzE,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IAEA;IAEA;IACAT,cAAA,WAAAA,eAAAkB,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAArE,MAAA,CAAAgE,IAAA,OAAAhE,MAAA,CAAAkE,KAAA,OAAAlE,MAAA,CAAAsE,GAAA,OAAAtE,MAAA,CAAAwE,KAAA,OAAAxE,MAAA,CAAA0E,OAAA,OAAA1E,MAAA,CAAA4E,OAAA;IACA;IAEA;IACAtK,QAAA,WAAAA,SAAA;MAAA,IAAAwK,MAAA;MAAA,OAAAnK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkK,SAAA;QAAA,IAAA7J,GAAA;QAAA,OAAAN,mBAAA,GAAAQ,IAAA,UAAA4J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1J,IAAA,GAAA0J,SAAA,CAAAzJ,IAAA;YAAA;cAAAyJ,SAAA,CAAA1J,IAAA;cAAA0J,SAAA,CAAAzJ,IAAA;cAAA,OAEA1D,iBAAA;YAAA;cAAAoD,GAAA,GAAA+J,SAAA,CAAApJ,IAAA;cACA,IAAAX,GAAA,CAAAc,IAAA,UAAAd,GAAA,CAAAc,IAAA;gBACA8I,MAAA,CAAArL,KAAA;kBACAC,UAAA,EAAAwB,GAAA,CAAA/C,IAAA,CAAAuB,UAAA;kBACAC,cAAA,EAAAuB,GAAA,CAAA/C,IAAA,CAAAwB,cAAA;kBACAC,WAAA,EAAAsB,GAAA,CAAA/C,IAAA,CAAAyB,WAAA;kBACAC,eAAA,EAAAqB,GAAA,CAAA/C,IAAA,CAAA0B,eAAA;kBACAC,aAAA,EAAAoB,GAAA,CAAA/C,IAAA,CAAA2B,aAAA;kBACAC,iBAAA,EAAAmB,GAAA,CAAA/C,IAAA,CAAA4B,iBAAA;kBACAC,YAAA,EAAAkB,GAAA,CAAA/C,IAAA,CAAA6B,YAAA;kBACAC,gBAAA,EAAAiB,GAAA,CAAA/C,IAAA,CAAA8B,gBAAA;gBACA;cACA;cAAAgL,SAAA,CAAAzJ,IAAA;cAAA;YAAA;cAAAyJ,SAAA,CAAA1J,IAAA;cAAA0J,SAAA,CAAA7F,EAAA,GAAA6F,SAAA;cAEA3G,OAAA,CAAAe,KAAA,cAAA4F,SAAA,CAAA7F,EAAA;YAAA;YAAA;cAAA,OAAA6F,SAAA,CAAApG,IAAA;UAAA;QAAA,GAAAkG,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}