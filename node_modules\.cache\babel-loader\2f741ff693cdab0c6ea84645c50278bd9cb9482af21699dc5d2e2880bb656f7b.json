{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport { install as GridSimpleComponent } from '../component/grid/installSimple.js';\nexport { install as GridComponent } from '../component/grid/install.js';\nexport { install as PolarComponent } from '../component/polar/install.js';\nexport { install as RadarComponent } from '../component/radar/install.js';\nexport { install as GeoComponent } from '../component/geo/install.js';\nexport { install as SingleAxisComponent } from '../component/singleAxis/install.js';\nexport { install as ParallelComponent } from '../component/parallel/install.js';\nexport { install as CalendarComponent } from '../component/calendar/install.js';\nexport { install as GraphicComponent } from '../component/graphic/install.js';\nexport { install as ToolboxComponent } from '../component/toolbox/install.js';\nexport { install as TooltipComponent } from '../component/tooltip/install.js';\nexport { install as AxisPointerComponent } from '../component/axisPointer/install.js';\nexport { install as BrushComponent } from '../component/brush/install.js';\nexport { install as TitleComponent } from '../component/title/install.js';\nexport { install as TimelineComponent } from '../component/timeline/install.js';\nexport { install as MarkPointComponent } from '../component/marker/installMarkPoint.js';\nexport { install as MarkLineComponent } from '../component/marker/installMarkLine.js';\nexport { install as MarkAreaComponent } from '../component/marker/installMarkArea.js';\nexport { install as LegendComponent } from '../component/legend/install.js';\nexport { install as LegendScrollComponent } from '../component/legend/installLegendScroll.js';\nexport { install as LegendPlainComponent } from '../component/legend/installLegendPlain.js';\nexport { install as DataZoomComponent } from '../component/dataZoom/install.js';\nexport { install as DataZoomInsideComponent } from '../component/dataZoom/installDataZoomInside.js';\nexport { install as DataZoomSliderComponent } from '../component/dataZoom/installDataZoomSlider.js';\nexport { install as VisualMapComponent } from '../component/visualMap/install.js';\nexport { install as VisualMapContinuousComponent } from '../component/visualMap/installVisualMapContinuous.js';\nexport { install as VisualMapPiecewiseComponent } from '../component/visualMap/installVisualMapPiecewise.js';\nexport { install as AriaComponent } from '../component/aria/install.js';\nexport { install as TransformComponent } from '../component/transform/install.js';\nexport { install as DatasetComponent } from '../component/dataset/install.js';", "map": {"version": 3, "names": ["install", "GridSimpleComponent", "GridComponent", "PolarComponent", "RadarComponent", "GeoComponent", "SingleAxisComponent", "ParallelComponent", "CalendarComponent", "GraphicComponent", "ToolboxComponent", "TooltipComponent", "AxisPointerComponent", "BrushComponent", "TitleComponent", "TimelineComponent", "MarkPointComponent", "MarkLineComponent", "MarkAreaComponent", "LegendComponent", "LegendScrollComponent", "LegendPlainComponent", "DataZoomComponent", "DataZoomInsideComponent", "DataZoomSliderComponent", "VisualMapComponent", "VisualMapContinuousComponent", "VisualMapPiecewiseComponent", "AriaComponent", "TransformComponent", "DatasetComponent"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/echarts/lib/export/components.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport { install as GridSimpleComponent } from '../component/grid/installSimple.js';\nexport { install as GridComponent } from '../component/grid/install.js';\nexport { install as PolarComponent } from '../component/polar/install.js';\nexport { install as RadarComponent } from '../component/radar/install.js';\nexport { install as GeoComponent } from '../component/geo/install.js';\nexport { install as SingleAxisComponent } from '../component/singleAxis/install.js';\nexport { install as ParallelComponent } from '../component/parallel/install.js';\nexport { install as CalendarComponent } from '../component/calendar/install.js';\nexport { install as GraphicComponent } from '../component/graphic/install.js';\nexport { install as ToolboxComponent } from '../component/toolbox/install.js';\nexport { install as TooltipComponent } from '../component/tooltip/install.js';\nexport { install as AxisPointerComponent } from '../component/axisPointer/install.js';\nexport { install as BrushComponent } from '../component/brush/install.js';\nexport { install as TitleComponent } from '../component/title/install.js';\nexport { install as TimelineComponent } from '../component/timeline/install.js';\nexport { install as MarkPointComponent } from '../component/marker/installMarkPoint.js';\nexport { install as MarkLineComponent } from '../component/marker/installMarkLine.js';\nexport { install as MarkAreaComponent } from '../component/marker/installMarkArea.js';\nexport { install as LegendComponent } from '../component/legend/install.js';\nexport { install as LegendScrollComponent } from '../component/legend/installLegendScroll.js';\nexport { install as LegendPlainComponent } from '../component/legend/installLegendPlain.js';\nexport { install as DataZoomComponent } from '../component/dataZoom/install.js';\nexport { install as DataZoomInsideComponent } from '../component/dataZoom/installDataZoomInside.js';\nexport { install as DataZoomSliderComponent } from '../component/dataZoom/installDataZoomSlider.js';\nexport { install as VisualMapComponent } from '../component/visualMap/install.js';\nexport { install as VisualMapContinuousComponent } from '../component/visualMap/installVisualMapContinuous.js';\nexport { install as VisualMapPiecewiseComponent } from '../component/visualMap/installVisualMapPiecewise.js';\nexport { install as AriaComponent } from '../component/aria/install.js';\nexport { install as TransformComponent } from '../component/transform/install.js';\nexport { install as DatasetComponent } from '../component/dataset/install.js';"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,mBAAmB,QAAQ,oCAAoC;AACnF,SAASD,OAAO,IAAIE,aAAa,QAAQ,8BAA8B;AACvE,SAASF,OAAO,IAAIG,cAAc,QAAQ,+BAA+B;AACzE,SAASH,OAAO,IAAII,cAAc,QAAQ,+BAA+B;AACzE,SAASJ,OAAO,IAAIK,YAAY,QAAQ,6BAA6B;AACrE,SAASL,OAAO,IAAIM,mBAAmB,QAAQ,oCAAoC;AACnF,SAASN,OAAO,IAAIO,iBAAiB,QAAQ,kCAAkC;AAC/E,SAASP,OAAO,IAAIQ,iBAAiB,QAAQ,kCAAkC;AAC/E,SAASR,OAAO,IAAIS,gBAAgB,QAAQ,iCAAiC;AAC7E,SAAST,OAAO,IAAIU,gBAAgB,QAAQ,iCAAiC;AAC7E,SAASV,OAAO,IAAIW,gBAAgB,QAAQ,iCAAiC;AAC7E,SAASX,OAAO,IAAIY,oBAAoB,QAAQ,qCAAqC;AACrF,SAASZ,OAAO,IAAIa,cAAc,QAAQ,+BAA+B;AACzE,SAASb,OAAO,IAAIc,cAAc,QAAQ,+BAA+B;AACzE,SAASd,OAAO,IAAIe,iBAAiB,QAAQ,kCAAkC;AAC/E,SAASf,OAAO,IAAIgB,kBAAkB,QAAQ,yCAAyC;AACvF,SAAShB,OAAO,IAAIiB,iBAAiB,QAAQ,wCAAwC;AACrF,SAASjB,OAAO,IAAIkB,iBAAiB,QAAQ,wCAAwC;AACrF,SAASlB,OAAO,IAAImB,eAAe,QAAQ,gCAAgC;AAC3E,SAASnB,OAAO,IAAIoB,qBAAqB,QAAQ,4CAA4C;AAC7F,SAASpB,OAAO,IAAIqB,oBAAoB,QAAQ,2CAA2C;AAC3F,SAASrB,OAAO,IAAIsB,iBAAiB,QAAQ,kCAAkC;AAC/E,SAAStB,OAAO,IAAIuB,uBAAuB,QAAQ,gDAAgD;AACnG,SAASvB,OAAO,IAAIwB,uBAAuB,QAAQ,gDAAgD;AACnG,SAASxB,OAAO,IAAIyB,kBAAkB,QAAQ,mCAAmC;AACjF,SAASzB,OAAO,IAAI0B,4BAA4B,QAAQ,sDAAsD;AAC9G,SAAS1B,OAAO,IAAI2B,2BAA2B,QAAQ,qDAAqD;AAC5G,SAAS3B,OAAO,IAAI4B,aAAa,QAAQ,8BAA8B;AACvE,SAAS5B,OAAO,IAAI6B,kBAAkB,QAAQ,mCAAmC;AACjF,SAAS7B,OAAO,IAAI8B,gBAAgB,QAAQ,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}