{"version": 3, "file": "get-outer-width.js", "sourceRoot": "", "sources": ["../src/get-outer-width.ts"], "names": [], "mappings": "AACA,OAAO,QAAQ,MAAM,aAAa,CAAC;AACnC,OAAO,QAAQ,MAAM,aAAa,CAAC;AAEnC,MAAM,CAAC,OAAO,UAAU,aAAa,CAAC,EAAe,EAAE,YAAkB;IACvE,IAAM,KAAK,GAAG,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;IACzC,IAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3D,IAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7D,IAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC;IACjE,IAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5D,IAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;IAC1D,OAAO,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;AAClE,CAAC"}