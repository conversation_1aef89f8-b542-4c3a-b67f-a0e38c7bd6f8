{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport { use } from '../../extension.js';\nimport TooltipModel from './TooltipModel.js';\nimport TooltipView from './TooltipView.js';\nimport { noop } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  use(installAxisPointer);\n  registers.registerComponentModel(TooltipModel);\n  registers.registerComponentView(TooltipView);\n  /**\n   * @action\n   * @property {string} type\n   * @property {number} seriesIndex\n   * @property {number} dataIndex\n   * @property {number} [x]\n   * @property {number} [y]\n   */\n  registers.registerAction({\n    type: 'showTip',\n    event: 'showTip',\n    update: 'tooltip:manuallyShowTip'\n  }, noop);\n  registers.registerAction({\n    type: 'hideTip',\n    event: 'hideTip',\n    update: 'tooltip:manuallyHideTip'\n  }, noop);\n}", "map": {"version": 3, "names": ["install", "installAxisPointer", "use", "TooltipModel", "TooltipView", "noop", "registers", "registerComponentModel", "registerComponentView", "registerAction", "type", "event", "update"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/echarts/lib/component/tooltip/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport { use } from '../../extension.js';\nimport TooltipModel from './TooltipModel.js';\nimport TooltipView from './TooltipView.js';\nimport { noop } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  use(installAxisPointer);\n  registers.registerComponentModel(TooltipModel);\n  registers.registerComponentView(TooltipView);\n  /**\n   * @action\n   * @property {string} type\n   * @property {number} seriesIndex\n   * @property {number} dataIndex\n   * @property {number} [x]\n   * @property {number} [y]\n   */\n  registers.registerAction({\n    type: 'showTip',\n    event: 'showTip',\n    update: 'tooltip:manuallyShowTip'\n  }, noop);\n  registers.registerAction({\n    type: 'hideTip',\n    event: 'hideTip',\n    update: 'tooltip:manuallyHideTip'\n  }, noop);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,2BAA2B;AACzE,SAASC,GAAG,QAAQ,oBAAoB;AACxC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,OAAO,SAASL,OAAOA,CAACM,SAAS,EAAE;EACjCJ,GAAG,CAACD,kBAAkB,CAAC;EACvBK,SAAS,CAACC,sBAAsB,CAACJ,YAAY,CAAC;EAC9CG,SAAS,CAACE,qBAAqB,CAACJ,WAAW,CAAC;EAC5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,SAAS,CAACG,cAAc,CAAC;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,EAAEP,IAAI,CAAC;EACRC,SAAS,CAACG,cAAc,CAAC;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,EAAEP,IAAI,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}