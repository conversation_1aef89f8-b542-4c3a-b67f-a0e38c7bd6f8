{"ast": null, "code": "require(\"core-js/modules/es.error.cause.js\");\nrequire(\"core-js/modules/es.error.to-string.js\");\nrequire(\"core-js/modules/es.array.concat.js\");\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/web.timers.js\");\n// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\nfunction defaultSetTimout() {\n  throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout() {\n  throw new Error('clearTimeout has not been defined');\n}\n(function () {\n  try {\n    if (typeof setTimeout === 'function') {\n      cachedSetTimeout = setTimeout;\n    } else {\n      cachedSetTimeout = defaultSetTimout;\n    }\n  } catch (e) {\n    cachedSetTimeout = defaultSetTimout;\n  }\n  try {\n    if (typeof clearTimeout === 'function') {\n      cachedClearTimeout = clearTimeout;\n    } else {\n      cachedClearTimeout = defaultClearTimeout;\n    }\n  } catch (e) {\n    cachedClearTimeout = defaultClearTimeout;\n  }\n})();\nfunction runTimeout(fun) {\n  if (cachedSetTimeout === setTimeout) {\n    //normal enviroments in sane situations\n    return setTimeout(fun, 0);\n  }\n  // if setTimeout wasn't available but was latter defined\n  if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n    cachedSetTimeout = setTimeout;\n    return setTimeout(fun, 0);\n  }\n  try {\n    // when when somebody has screwed with setTimeout but no I.E. maddness\n    return cachedSetTimeout(fun, 0);\n  } catch (e) {\n    try {\n      // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n      return cachedSetTimeout.call(null, fun, 0);\n    } catch (e) {\n      // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n      return cachedSetTimeout.call(this, fun, 0);\n    }\n  }\n}\nfunction runClearTimeout(marker) {\n  if (cachedClearTimeout === clearTimeout) {\n    //normal enviroments in sane situations\n    return clearTimeout(marker);\n  }\n  // if clearTimeout wasn't available but was latter defined\n  if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n    cachedClearTimeout = clearTimeout;\n    return clearTimeout(marker);\n  }\n  try {\n    // when when somebody has screwed with setTimeout but no I.E. maddness\n    return cachedClearTimeout(marker);\n  } catch (e) {\n    try {\n      // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n      return cachedClearTimeout.call(null, marker);\n    } catch (e) {\n      // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n      // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n      return cachedClearTimeout.call(this, marker);\n    }\n  }\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\nfunction cleanUpNextTick() {\n  if (!draining || !currentQueue) {\n    return;\n  }\n  draining = false;\n  if (currentQueue.length) {\n    queue = currentQueue.concat(queue);\n  } else {\n    queueIndex = -1;\n  }\n  if (queue.length) {\n    drainQueue();\n  }\n}\nfunction drainQueue() {\n  if (draining) {\n    return;\n  }\n  var timeout = runTimeout(cleanUpNextTick);\n  draining = true;\n  var len = queue.length;\n  while (len) {\n    currentQueue = queue;\n    queue = [];\n    while (++queueIndex < len) {\n      if (currentQueue) {\n        currentQueue[queueIndex].run();\n      }\n    }\n    queueIndex = -1;\n    len = queue.length;\n  }\n  currentQueue = null;\n  draining = false;\n  runClearTimeout(timeout);\n}\nprocess.nextTick = function (fun) {\n  var args = new Array(arguments.length - 1);\n  if (arguments.length > 1) {\n    for (var i = 1; i < arguments.length; i++) {\n      args[i - 1] = arguments[i];\n    }\n  }\n  queue.push(new Item(fun, args));\n  if (queue.length === 1 && !draining) {\n    runTimeout(drainQueue);\n  }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n  this.fun = fun;\n  this.array = array;\n}\nItem.prototype.run = function () {\n  this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\nfunction noop() {}\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\nprocess.listeners = function (name) {\n  return [];\n};\nprocess.binding = function (name) {\n  throw new Error('process.binding is not supported');\n};\nprocess.cwd = function () {\n  return '/';\n};\nprocess.chdir = function (dir) {\n  throw new Error('process.chdir is not supported');\n};\nprocess.umask = function () {\n  return 0;\n};", "map": {"version": 3, "names": ["process", "module", "exports", "cachedSetTimeout", "cachedClearTimeout", "defaultSetTimout", "Error", "defaultClearTimeout", "setTimeout", "e", "clearTimeout", "runTimeout", "fun", "call", "runClearTimeout", "marker", "queue", "draining", "currentQueue", "queueIndex", "cleanUpNextTick", "length", "concat", "drainQueue", "timeout", "len", "run", "nextTick", "args", "Array", "arguments", "i", "push", "<PERSON><PERSON>", "array", "prototype", "apply", "title", "browser", "env", "argv", "version", "versions", "noop", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "name", "binding", "cwd", "chdir", "dir", "umask"], "sources": ["F:/常规项目/华通宝/adminweb/node_modules/process/browser.js"], "sourcesContent": ["// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n"], "mappings": ";;;;;AAAA;AACA,IAAIA,OAAO,GAAGC,MAAM,CAACC,OAAO,GAAG,CAAC,CAAC;;AAEjC;AACA;AACA;AACA;;AAEA,IAAIC,gBAAgB;AACpB,IAAIC,kBAAkB;AAEtB,SAASC,gBAAgBA,CAAA,EAAG;EACxB,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;AACtD;AACA,SAASC,mBAAmBA,CAAA,EAAI;EAC5B,MAAM,IAAID,KAAK,CAAC,mCAAmC,CAAC;AACxD;AACC,aAAY;EACT,IAAI;IACA,IAAI,OAAOE,UAAU,KAAK,UAAU,EAAE;MAClCL,gBAAgB,GAAGK,UAAU;IACjC,CAAC,MAAM;MACHL,gBAAgB,GAAGE,gBAAgB;IACvC;EACJ,CAAC,CAAC,OAAOI,CAAC,EAAE;IACRN,gBAAgB,GAAGE,gBAAgB;EACvC;EACA,IAAI;IACA,IAAI,OAAOK,YAAY,KAAK,UAAU,EAAE;MACpCN,kBAAkB,GAAGM,YAAY;IACrC,CAAC,MAAM;MACHN,kBAAkB,GAAGG,mBAAmB;IAC5C;EACJ,CAAC,CAAC,OAAOE,CAAC,EAAE;IACRL,kBAAkB,GAAGG,mBAAmB;EAC5C;AACJ,CAAC,EAAE,CAAC;AACJ,SAASI,UAAUA,CAACC,GAAG,EAAE;EACrB,IAAIT,gBAAgB,KAAKK,UAAU,EAAE;IACjC;IACA,OAAOA,UAAU,CAACI,GAAG,EAAE,CAAC,CAAC;EAC7B;EACA;EACA,IAAI,CAACT,gBAAgB,KAAKE,gBAAgB,IAAI,CAACF,gBAAgB,KAAKK,UAAU,EAAE;IAC5EL,gBAAgB,GAAGK,UAAU;IAC7B,OAAOA,UAAU,CAACI,GAAG,EAAE,CAAC,CAAC;EAC7B;EACA,IAAI;IACA;IACA,OAAOT,gBAAgB,CAACS,GAAG,EAAE,CAAC,CAAC;EACnC,CAAC,CAAC,OAAMH,CAAC,EAAC;IACN,IAAI;MACA;MACA,OAAON,gBAAgB,CAACU,IAAI,CAAC,IAAI,EAAED,GAAG,EAAE,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAMH,CAAC,EAAC;MACN;MACA,OAAON,gBAAgB,CAACU,IAAI,CAAC,IAAI,EAAED,GAAG,EAAE,CAAC,CAAC;IAC9C;EACJ;AAGJ;AACA,SAASE,eAAeA,CAACC,MAAM,EAAE;EAC7B,IAAIX,kBAAkB,KAAKM,YAAY,EAAE;IACrC;IACA,OAAOA,YAAY,CAACK,MAAM,CAAC;EAC/B;EACA;EACA,IAAI,CAACX,kBAAkB,KAAKG,mBAAmB,IAAI,CAACH,kBAAkB,KAAKM,YAAY,EAAE;IACrFN,kBAAkB,GAAGM,YAAY;IACjC,OAAOA,YAAY,CAACK,MAAM,CAAC;EAC/B;EACA,IAAI;IACA;IACA,OAAOX,kBAAkB,CAACW,MAAM,CAAC;EACrC,CAAC,CAAC,OAAON,CAAC,EAAC;IACP,IAAI;MACA;MACA,OAAOL,kBAAkB,CAACS,IAAI,CAAC,IAAI,EAAEE,MAAM,CAAC;IAChD,CAAC,CAAC,OAAON,CAAC,EAAC;MACP;MACA;MACA,OAAOL,kBAAkB,CAACS,IAAI,CAAC,IAAI,EAAEE,MAAM,CAAC;IAChD;EACJ;AAIJ;AACA,IAAIC,KAAK,GAAG,EAAE;AACd,IAAIC,QAAQ,GAAG,KAAK;AACpB,IAAIC,YAAY;AAChB,IAAIC,UAAU,GAAG,CAAC,CAAC;AAEnB,SAASC,eAAeA,CAAA,EAAG;EACvB,IAAI,CAACH,QAAQ,IAAI,CAACC,YAAY,EAAE;IAC5B;EACJ;EACAD,QAAQ,GAAG,KAAK;EAChB,IAAIC,YAAY,CAACG,MAAM,EAAE;IACrBL,KAAK,GAAGE,YAAY,CAACI,MAAM,CAACN,KAAK,CAAC;EACtC,CAAC,MAAM;IACHG,UAAU,GAAG,CAAC,CAAC;EACnB;EACA,IAAIH,KAAK,CAACK,MAAM,EAAE;IACdE,UAAU,CAAC,CAAC;EAChB;AACJ;AAEA,SAASA,UAAUA,CAAA,EAAG;EAClB,IAAIN,QAAQ,EAAE;IACV;EACJ;EACA,IAAIO,OAAO,GAAGb,UAAU,CAACS,eAAe,CAAC;EACzCH,QAAQ,GAAG,IAAI;EAEf,IAAIQ,GAAG,GAAGT,KAAK,CAACK,MAAM;EACtB,OAAMI,GAAG,EAAE;IACPP,YAAY,GAAGF,KAAK;IACpBA,KAAK,GAAG,EAAE;IACV,OAAO,EAAEG,UAAU,GAAGM,GAAG,EAAE;MACvB,IAAIP,YAAY,EAAE;QACdA,YAAY,CAACC,UAAU,CAAC,CAACO,GAAG,CAAC,CAAC;MAClC;IACJ;IACAP,UAAU,GAAG,CAAC,CAAC;IACfM,GAAG,GAAGT,KAAK,CAACK,MAAM;EACtB;EACAH,YAAY,GAAG,IAAI;EACnBD,QAAQ,GAAG,KAAK;EAChBH,eAAe,CAACU,OAAO,CAAC;AAC5B;AAEAxB,OAAO,CAAC2B,QAAQ,GAAG,UAAUf,GAAG,EAAE;EAC9B,IAAIgB,IAAI,GAAG,IAAIC,KAAK,CAACC,SAAS,CAACT,MAAM,GAAG,CAAC,CAAC;EAC1C,IAAIS,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;IACtB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACT,MAAM,EAAEU,CAAC,EAAE,EAAE;MACvCH,IAAI,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGD,SAAS,CAACC,CAAC,CAAC;IAC9B;EACJ;EACAf,KAAK,CAACgB,IAAI,CAAC,IAAIC,IAAI,CAACrB,GAAG,EAAEgB,IAAI,CAAC,CAAC;EAC/B,IAAIZ,KAAK,CAACK,MAAM,KAAK,CAAC,IAAI,CAACJ,QAAQ,EAAE;IACjCN,UAAU,CAACY,UAAU,CAAC;EAC1B;AACJ,CAAC;;AAED;AACA,SAASU,IAAIA,CAACrB,GAAG,EAAEsB,KAAK,EAAE;EACtB,IAAI,CAACtB,GAAG,GAAGA,GAAG;EACd,IAAI,CAACsB,KAAK,GAAGA,KAAK;AACtB;AACAD,IAAI,CAACE,SAAS,CAACT,GAAG,GAAG,YAAY;EAC7B,IAAI,CAACd,GAAG,CAACwB,KAAK,CAAC,IAAI,EAAE,IAAI,CAACF,KAAK,CAAC;AACpC,CAAC;AACDlC,OAAO,CAACqC,KAAK,GAAG,SAAS;AACzBrC,OAAO,CAACsC,OAAO,GAAG,IAAI;AACtBtC,OAAO,CAACuC,GAAG,GAAG,CAAC,CAAC;AAChBvC,OAAO,CAACwC,IAAI,GAAG,EAAE;AACjBxC,OAAO,CAACyC,OAAO,GAAG,EAAE,CAAC,CAAC;AACtBzC,OAAO,CAAC0C,QAAQ,GAAG,CAAC,CAAC;AAErB,SAASC,IAAIA,CAAA,EAAG,CAAC;AAEjB3C,OAAO,CAAC4C,EAAE,GAAGD,IAAI;AACjB3C,OAAO,CAAC6C,WAAW,GAAGF,IAAI;AAC1B3C,OAAO,CAAC8C,IAAI,GAAGH,IAAI;AACnB3C,OAAO,CAAC+C,GAAG,GAAGJ,IAAI;AAClB3C,OAAO,CAACgD,cAAc,GAAGL,IAAI;AAC7B3C,OAAO,CAACiD,kBAAkB,GAAGN,IAAI;AACjC3C,OAAO,CAACkD,IAAI,GAAGP,IAAI;AACnB3C,OAAO,CAACmD,eAAe,GAAGR,IAAI;AAC9B3C,OAAO,CAACoD,mBAAmB,GAAGT,IAAI;AAElC3C,OAAO,CAACqD,SAAS,GAAG,UAAUC,IAAI,EAAE;EAAE,OAAO,EAAE;AAAC,CAAC;AAEjDtD,OAAO,CAACuD,OAAO,GAAG,UAAUD,IAAI,EAAE;EAC9B,MAAM,IAAIhD,KAAK,CAAC,kCAAkC,CAAC;AACvD,CAAC;AAEDN,OAAO,CAACwD,GAAG,GAAG,YAAY;EAAE,OAAO,GAAG;AAAC,CAAC;AACxCxD,OAAO,CAACyD,KAAK,GAAG,UAAUC,GAAG,EAAE;EAC3B,MAAM,IAAIpD,KAAK,CAAC,gCAAgC,CAAC;AACrD,CAAC;AACDN,OAAO,CAAC2D,KAAK,GAAG,YAAW;EAAE,OAAO,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}