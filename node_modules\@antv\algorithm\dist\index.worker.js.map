{"version": 3, "file": "index.worker.js", "mappings": "mBACA,IAAIA,EAAsB,CCA1BA,EAAwB,CAACC,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDH,EAAwB,CAACS,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFV,EAAyBC,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,GAAO,G,kXCgC9D,QAnCkB,SAACC,EAAsBC,GAC/B,IAAAC,EAAiBF,EAAS,MAAnBG,EAAUH,EAAS,MAC5BI,EAAmB,GAEnBC,EAEF,CAAC,EAEL,IAAKH,EACH,MAAM,IAAII,MAAM,uBAuBlB,OApBIJ,GACFA,EAAMK,SAAQ,SAACC,EAAMC,GACnBJ,EAAQG,EAAKE,IAAMD,EAEnBL,EAAOO,KADe,GAExB,IAGER,GACFA,EAAMI,SAAQ,SAACK,GACL,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzBG,EAASV,EAAQQ,GACjBG,EAASX,EAAQS,IACjBC,GAAqB,IAAXA,IAAmBC,GAAqB,IAAXA,IAC7CZ,EAAOW,GAAQC,GAAU,EACpBf,IACHG,EAAOY,GAAQD,GAAU,GAE7B,IAEKX,CACT,ECnCA,IAAMa,EAAoB,SAACC,EAAGC,GAC5B,OAAID,IAAMC,CAKZ,EAKA,aAKE,WAAYpB,EAAOqB,QAAA,IAAAA,IAAAA,EAAA,MACjBC,KAAKtB,MAAQA,EACbsB,KAAKD,KAAOA,CACd,CAKF,OAHE,YAAAE,SAAA,SAASC,GACP,OAAOA,EAAWA,EAASF,KAAKtB,OAAS,UAAGsB,KAAKtB,MACnD,EACF,EAbA,G,QAeA,WAOE,WAAYyB,QAAA,IAAAA,IAAAA,EAAA,GACVH,KAAKI,KAAO,KACZJ,KAAKK,KAAO,KACZL,KAAKM,QAAUH,CACjB,CA+MF,OAzME,YAAAI,QAAA,SAAQ7B,GAEN,IAAM8B,EAAU,IAAIC,EAAe/B,EAAOsB,KAAKI,MAO/C,OANAJ,KAAKI,KAAOI,EAEPR,KAAKK,OACRL,KAAKK,KAAOG,GAGPR,IACT,EAMA,YAAAU,OAAA,SAAOhC,GACL,IAAM8B,EAAU,IAAIC,EAAe/B,GAGnC,OAAKsB,KAAKI,MAQVJ,KAAKK,KAAKN,KAAOS,EACjBR,KAAKK,KAAOG,EAELR,OAVLA,KAAKI,KAAOI,EACZR,KAAKK,KAAOG,EAELR,KAQX,EAMA,YAAAW,OAAA,SAAOjC,GACL,IAAKsB,KAAKI,KACR,OAAO,KAMT,IAHA,IAAIQ,EAAa,KAGVZ,KAAKI,MAAQJ,KAAKM,QAAQN,KAAKI,KAAK1B,MAAOA,IAChDkC,EAAaZ,KAAKI,KAClBJ,KAAKI,KAAOJ,KAAKI,KAAKL,KAGxB,IAAIc,EAAcb,KAAKI,KAEvB,GAAoB,OAAhBS,EAEF,KAAOA,EAAYd,MACbC,KAAKM,QAAQO,EAAYd,KAAKrB,MAAOA,IACvCkC,EAAaC,EAAYd,KACzBc,EAAYd,KAAOc,EAAYd,KAAKA,MAEpCc,EAAcA,EAAYd,KAUhC,OAJIC,KAAKM,QAAQN,KAAKK,KAAK3B,MAAOA,KAChCsB,KAAKK,KAAOQ,GAGPD,CACT,EAMA,YAAAE,KAAA,SAAK,G,IAAE,IAAApC,MAAAA,OAAK,IAAG,OAAAqC,EAAS,EAAE,IAAAb,SAAAA,OAAQ,IAAG,OAAAa,EAAS,EAC5C,IAAKf,KAAKI,KACR,OAAO,KAKT,IAFA,IAAIS,EAAcb,KAAKI,KAEhBS,GAAa,CAElB,GAAIX,GAAYA,EAASW,EAAYnC,OACnC,OAAOmC,EAIT,QAAcE,IAAVrC,GAAuBsB,KAAKM,QAAQO,EAAYnC,MAAOA,GACzD,OAAOmC,EAGTA,EAAcA,EAAYd,I,CAG5B,OAAO,IACT,EAKA,YAAAiB,WAAA,WACE,IAAMC,EAAcjB,KAAKK,KAEzB,GAAIL,KAAKI,OAASJ,KAAKK,KAIrB,OAFAL,KAAKI,KAAO,KACZJ,KAAKK,KAAO,KACLY,EAIT,IADA,IAAIJ,EAAcb,KAAKI,KAChBS,EAAYd,MACZc,EAAYd,KAAKA,KAGpBc,EAAcA,EAAYd,KAF1Bc,EAAYd,KAAO,KAQvB,OAFAC,KAAKK,KAAOQ,EAELI,CACT,EAKA,YAAAC,WAAA,WACE,IAAKlB,KAAKI,KACR,OAAO,KAGT,IAAMe,EAAcnB,KAAKI,KASzB,OAPIJ,KAAKI,KAAKL,KACZC,KAAKI,KAAOJ,KAAKI,KAAKL,MAEtBC,KAAKI,KAAO,KACZJ,KAAKK,KAAO,MAGPc,CACT,EAMA,YAAAC,UAAA,SAAUC,GAAV,WAEE,OADAA,EAAOnC,SAAQ,SAACR,GAAU,SAAKgC,OAAOhC,EAAZ,IACnBsB,IACT,EAKA,YAAAsB,QAAA,WAKE,IAJA,IAAMzC,EAAQ,GAEVgC,EAAcb,KAAKI,KAEhBS,GACLhC,EAAMS,KAAKuB,GACXA,EAAcA,EAAYd,KAG5B,OAAOlB,CACT,EAKA,YAAA0C,QAAA,WAIE,IAHA,IAAIV,EAAcb,KAAKI,KACnBoB,EAAW,KACXC,EAAW,KACRZ,GAELY,EAAWZ,EAAYd,KAGvBc,EAAYd,KAAOyB,EAGnBA,EAAWX,EACXA,EAAcY,EAGhBzB,KAAKK,KAAOL,KAAKI,KACjBJ,KAAKI,KAAOoB,CACd,EAEA,YAAAvB,SAAA,SAASC,GACP,YADO,IAAAA,IAAAA,OAAA,GACAF,KAAKsB,UACTI,KAAI,SAACvC,GAAS,OAAAA,EAAKc,SAASC,EAAd,IACdD,UACL,EACF,EA1NA,G,ECxBA,WAGE,aACED,KAAK2B,WAAa,IAAI,CACxB,CAsCF,OAjCS,YAAAC,QAAP,WACE,OAAQ5B,KAAK2B,WAAWvB,IAC1B,EAKO,YAAAyB,KAAP,WACE,OAAK7B,KAAK2B,WAAWvB,KAGdJ,KAAK2B,WAAWvB,KAAK1B,MAFnB,IAGX,EAMO,YAAAoD,QAAP,SAAepD,GACbsB,KAAK2B,WAAWjB,OAAOhC,EACzB,EAKO,YAAAqD,QAAP,WACE,IAAMC,EAAahC,KAAK2B,WAAWT,aACnC,OAAOc,EAAaA,EAAWtD,MAAQ,IACzC,EAEO,YAAAuB,SAAP,SAAgBC,GACd,OAAOF,KAAK2B,WAAW1B,SAASC,EAClC,EACF,EA3CA,GCMO,IAAM,EAAe,SAAC+B,EAAgBnD,EAA0BoD,QAA1B,IAAApD,IAAAA,EAAA,IAC3C,IAAMqD,EAAerD,EAAMsD,QAAO,SAAA7C,GAAQ,OAAAA,EAAKC,SAAWyC,GAAU1C,EAAKE,SAAWwC,CAA1C,IAC1C,MAAa,WAATC,EAKKC,EAAaC,QAHQ,SAAC7C,GAC3B,OAAOA,EAAKC,SAAWyC,CACzB,IACgDP,KAAI,SAACnC,GAAS,OAAAA,EAAKE,MAAL,IAEnD,WAATyC,EAKKC,EAAaC,QAHQ,SAAC7C,GAC3B,OAAOA,EAAKE,SAAWwC,CACzB,IACgDP,KAAI,SAACnC,GAAS,OAAAA,EAAKC,MAAL,IAOzD2C,EAAaT,KAHQ,SAACnC,GAC3B,OAAOA,EAAKC,SAAWyC,EAAS1C,EAAKE,OAASF,EAAKC,MACrD,GAEF,EAgBa6C,EAAmB,SAACJ,EAAgBnD,GAC/C,OAAOA,EAAMsD,QAAO,SAAA7C,GAAQ,OAAAA,EAAKC,SAAWyC,GAAU1C,EAAKE,SAAWwC,CAA1C,GAC9B,EAMaK,EAAW,SAACC,QAAA,IAAAA,IAAAA,EAAA,GACvB,IAAMC,EAAU,UAAGC,KAAKC,UAAWC,MAAM,KAAK,GAAGC,OAAO,EAAG,GACrDC,EAAU,UAAGJ,KAAKC,UAAWC,MAAM,KAAK,GAAGC,OAAO,EAAG,GAC3D,MAAO,UAAGL,EAAK,YAAIC,GAAO,OAAGK,EAC/B,EC8BA,QA/C2B,SACzBlE,EACAmE,EACAC,EACAnE,QAAA,IAAAA,IAAAA,GAAA,GAEA,IAAMoE,EApCR,SAAuBA,QAAA,IAAAA,IAAAA,EAAiC,CAAC,GACvD,IAKQC,EALFC,EAAoBF,EAEpBG,EAAe,WAAO,EAEtBC,GACEH,EAAO,CAAC,EACP,SAAC,G,IACA5D,EADM,OAEZ,OAAK4D,EAAK5D,KACR4D,EAAK5D,IAAM,GACJ,EAGX,GAOF,OAJA6D,EAAkBG,eAAiBL,EAAUK,gBAAkBD,EAC/DF,EAAkBI,MAAQN,EAAUM,OAASH,EAC7CD,EAAkBK,MAAQP,EAAUO,OAASJ,EAEtCD,CACT,CAcoBM,CAAcT,GAC1BU,EAAY,IAAI,EAEd,EAAe9E,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAGlB2E,EAAU3B,QAAQgB,GAKlB,IAHA,IAAIY,EAAe,G,aAIjB,IAAM7C,EAAsB4C,EAAU1B,UACtCiB,EAAUM,MAAM,CACdK,QAAS9C,EACT+C,SAAUF,IAIZ,EAAa7C,EAAa/B,EAAOF,EAAW,cAAWmC,GAAW7B,SAAQ,SAACuC,GAEvEuB,EAAUK,eAAe,CACvBO,SAAUF,EACVC,QAAS9C,EACTd,KAAM0B,KAGRgC,EAAU3B,QAAQL,EAEtB,IAEAuB,EAAUO,MAAM,CACdI,QAAS9C,EACT+C,SAAUF,IAIZA,EAAe7C,C,GA1BT4C,EAAU7B,W,GA4BpB,EC/EO,IAAMiC,EAA4B,SAAClF,GAqBxC,IApBQ,MAA2BA,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxBgF,EAAgC,GAChCC,EAAU,CAAC,EACXC,EAA0B,GAE1BC,EAAe,SAAC9E,GACpB6E,EAAU1E,KAAKH,GACf4E,EAAQ5E,EAAKE,KAAM,EAEnB,IADA,IAAM6E,EAAY,EAAa/E,EAAKE,GAAIP,G,WAC/BM,GACP,IAAM+E,EAAWD,EAAU9E,GAC3B,IAAK2E,EAAQI,GAAW,CACtB,IAAMC,EAAavF,EAAMuD,QAAO,SAAAjD,GAAQ,OAAAA,EAAKE,KAAO8E,CAAZ,IACpCC,EAAWC,OAAS,GACtBJ,EAAaG,EAAW,G,GALrBhF,EAAI,EAAGA,EAAI8E,EAAUG,SAAUjF,E,EAA/BA,EASX,EAESA,EAAI,EAAGA,EAAIP,EAAMwF,OAAQjF,IAAK,CACrC,IAAMD,EAAON,EAAMO,GACnB,IAAK2E,EAAQ5E,EAAKE,IAAK,CAErB4E,EAAa9E,GAEb,IADA,IAAMmF,EAAY,GACXN,EAAUK,OAAS,GACxBC,EAAUhF,KAAK0E,EAAUO,OAE3BT,EAAcxE,KAAKgF,E,EAGvB,OAAOR,CACT,EAUa,EAAgC,SAACnF,GAiD5C,IAhDQ,MAA2BA,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxBkF,EAA0B,GAC1BQ,EAAU,CAAC,EACXC,EAAU,CAAC,EACXC,EAAU,CAAC,EACXZ,EAAgC,GAClCvB,EAAQ,EAEN0B,EAAe,SAAC9E,GAEpBsF,EAAQtF,EAAKE,IAAMkD,EACnBmC,EAAQvF,EAAKE,IAAMkD,EACnBA,GAAS,EACTyB,EAAU1E,KAAKH,GACfqF,EAAQrF,EAAKE,KAAM,EAInB,IADA,IAAM6E,EAAY,EAAa/E,EAAKE,GAAIP,EAAO,UAAUsD,QAAO,SAACuC,GAAM,OAAA9F,EAAM6C,KAAI,SAAAvC,GAAQ,OAAAA,EAAKE,EAAL,IAASuF,QAAQD,IAAM,CAAzC,I,WAC9DvF,GACP,IAAMyF,EAAeX,EAAU9E,GAC/B,GAAKqF,EAAQI,IAA2C,IAA1BJ,EAAQI,GAO3BL,EAAQK,KAEjBH,EAAQvF,EAAKE,IAAMoD,KAAKqC,IAAIJ,EAAQvF,EAAKE,IAAKoF,EAAQI,SATG,CACzD,IAAMT,EAAavF,EAAMuD,QAAO,SAAAjD,GAAQ,OAAAA,EAAKE,KAAOwF,CAAZ,IACpCT,EAAWC,OAAS,GACtBJ,EAAaG,EAAW,IAG1BM,EAAQvF,EAAKE,IAAMoD,KAAKqC,IAAIJ,EAAQvF,EAAKE,IAAKqF,EAAQG,G,GARjDzF,EAAI,EAAGA,EAAI8E,EAAUG,OAAQjF,I,EAA7BA,GAgBT,GAAIsF,EAAQvF,EAAKE,MAAQoF,EAAQtF,EAAKE,IAAK,CAEzC,IADA,IAAMiF,EAAY,GACXN,EAAUK,OAAS,GAAG,CAC3B,IAAMU,EAAUf,EAAUO,MAG1B,GAFAC,EAAQO,EAAQ1F,KAAM,EACtBiF,EAAUhF,KAAKyF,GACXA,IAAY5F,EAAM,K,CAEpBmF,EAAUD,OAAS,GACrBP,EAAcxE,KAAKgF,E,CAGzB,EAEmB,MAAAzF,EAAA,eAAO,CAArB,IAAMM,EAAI,KACRsF,EAAQtF,EAAKE,KAA4B,IAArBoF,EAAQtF,EAAKE,KACpC4E,EAAa9E,E,CAIjB,OAAO2E,CACT,EAEe,SAAS,EAAuBnF,EAAsBC,GACnE,OAAIA,EAAiB,EAA8BD,GAC5CkF,EAA0BlF,EACnC,CC9GA,IAAMqG,EAAS,SAACrG,GACd,IAAMsG,EAAsB,CAAC,EACrB,EAA2BtG,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAiB9B,OAfAD,EAAMK,SAAQ,SAACC,GACb8F,EAAQ9F,EAAKE,IAAM,CACjB2F,OAAQ,EACRE,SAAU,EACVC,UAAW,EAEf,IAEArG,EAAMI,SAAQ,SAACK,GACb0F,EAAQ1F,EAAKC,QAAQwF,SACrBC,EAAQ1F,EAAKC,QAAQ2F,YACrBF,EAAQ1F,EAAKE,QAAQuF,SACrBC,EAAQ1F,EAAKE,QAAQyF,UACvB,IAEOD,CACT,EAEA,UAOO,IAAMG,EAAc,SAACzG,EAAsBsD,GAEhD,OADmB+C,EAAOrG,GACXsD,GACN+C,EAAOrG,GAAWsD,GAAQiD,SAE5B,CACT,EAOaG,EAAe,SAAC1G,EAAsBsD,GAEjD,OADmB+C,EAAOrG,GACXsD,GACN+C,EAAOrG,GAAWsD,GAAQkD,UAE5B,CACT,EClBA,SAASG,EACP3G,EACAkC,EACA6C,EACAV,EACApE,QAAA,IAAAA,IAAAA,GAAA,GAEAoE,EAAUM,MAAM,CACdK,QAAS9C,EACT+C,SAAUF,IAGJ,MAAe/E,EAAS,MAEhC,EAAakC,OAFA,IAAG,KAAE,EAEejC,EAAW,cAAWmC,GAAW7B,SAAQ,SAACuC,GAEvEuB,EAAUK,eAAe,CACvBO,SAAUF,EACVC,QAAS9C,EACTd,KAAM0B,KAGR6D,EAA0B3G,EAAW8C,EAAUZ,EAAamC,EAAWpE,EAE3E,IAEAoE,EAAUO,MAAM,CACdI,QAAS9C,EACT+C,SAAUF,GAEd,CAQe,SAAS6B,EACtB5G,EACAmE,EACAE,EACApE,QAAA,IAAAA,IAAAA,GAAA,GAEA0G,EAA0B3G,EAAWmE,EAAa,GAzEpD,SAAuBE,QAAA,IAAAA,IAAAA,EAAiC,CAAC,GACvD,IAKQC,EALFC,EAAoBF,EAEpBG,EAAe,WAAO,EAEtBC,GACEH,EAAO,CAAC,EACP,SAAC,G,IAAElD,EAAI,OACZ,OAAKkD,EAAKlD,KACRkD,EAAKlD,IAAQ,GACN,EAGX,GAOF,OAJAmD,EAAkBG,eAAiBL,EAAUK,gBAAkBD,EAC/DF,EAAkBI,MAAQN,EAAUM,OAASH,EAC7CD,EAAkBK,MAAQP,EAAUO,OAASJ,EAEtCD,CACT,CAoDwD,CAAcF,GAAYpE,EAClF,CC4QA,QApV4B,SAACD,GAG3B,IAAI6G,EAEA,KAEI,EAAe7G,EAAS,MAE1B8G,EAAe,CAAC,EAGhBC,EAAe,CAAC,EAGhBC,EAAc,CAAC,EAGfC,EAAa,CAAC,QAXP,IAAG,KAAE,GAcZ1G,SAAQ,SAACC,GACbuG,EAAavG,EAAKE,IAAMF,CAC1B,IA6CA,IA3CA,IAAM6D,EAAiC,CACrCM,MAAO,SAAC,G,IAAWzC,EAAW,UAAY6C,EAAY,WACpD,GAAIiC,EAAY9E,GAAc,CAE5B2E,EAAQ,CAAC,EAKT,IAHA,IAAIK,EAAmBhF,EACnBiF,EAAoBpC,EAEjBoC,IAAsBjF,GAC3B2E,EAAMK,GAAoBC,EAC1BD,EAAmBC,EACnBA,EAAoBL,EAAaK,GAGnCN,EAAMK,GAAoBC,C,MAG1BH,EAAY9E,GAAeA,SACpB6E,EAAa7E,GAGpB4E,EAAa5E,GAAe6C,CAEhC,EACAH,MAAO,SAAC,G,IAAW1C,EAAW,UAG5B+E,EAAW/E,GAAeA,SACnB8E,EAAY9E,EACrB,EACAwC,eAAgB,SAAC,G,IAAQ5B,EAAQ,OAE/B,OAAI+D,IAKII,EAAWnE,EACrB,GAIK1D,OAAOgI,KAAKL,GAAcrB,QAI/B,EAAI1F,EAFuBZ,OAAOgI,KAAKL,GAAc,GAElB1C,GAGrC,OAAOwC,CACT,ECoIO,SAASQ,EAAcC,EAAIC,EAAMC,GACtC,GAAIA,GAA6B,IAArBC,UAAU/B,OAAc,IAAK,IAA4BgC,EAAxBjH,EAAI,EAAGkH,EAAIJ,EAAK7B,OAAYjF,EAAIkH,EAAGlH,KACxEiH,GAAQjH,KAAK8G,IACRG,IAAIA,EAAKE,MAAMlI,UAAUmI,MAAMjI,KAAK2H,EAAM,EAAG9G,IAClDiH,EAAGjH,GAAK8G,EAAK9G,IAGrB,OAAO6G,EAAGQ,OAAOJ,GAAME,MAAMlI,UAAUmI,MAAMjI,KAAK2H,GACpD,CArE6BnI,OAAO2I,OA0GX3I,OAAO2I,OAyDkB,mBAApBC,iBAAiCA,gBC3T/D,IAAM1G,EAAW,GAAGA,SAIpB,QAFe,SAACvB,EAAYwD,GAA0B,OAAAjC,EAAS1B,KAAKG,KAAW,WAAawD,EAAO,GAA7C,ECKtD,WAAgBxD,GACd,OAAOkI,EAAOlI,EAAO,WACtB,ECPD,WAAgBA,GACd,OAAO6H,MAAMM,QACXN,MAAMM,QAAQnI,GACdkI,EAAOlI,EAAO,QACjB,ECHYX,OAAOgI,KCHpB,IAAMe,EAAeP,MAAMlI,UCAb,SAAU0I,EAAKC,EAAYC,QAAA,IAAAA,IAAAA,EAAA,IAAYC,KACnD,IAAMC,EAAI,GAEV,GAAIZ,MAAMM,QAAQG,GAChB,IAAK,IAAI5H,EAAI,EAAGgI,EAAMJ,EAAI3C,OAAQjF,EAAIgI,EAAKhI,IAAM,CAC/C,IAAMiI,EAAOL,EAAI5H,GAEZ6H,EAAMK,IAAID,KACbF,EAAE7H,KAAK+H,GACPJ,EAAMM,IAAIF,GAAM,G,CAItB,OAAOF,CACT,CDbeL,EAAaU,OACZV,EAAalC,QEAd2B,MAAMlI,UAAUmJ,OCCRzJ,OAAOM,UAAUC,eCDtBmJ,OAAOC,WAAYD,OAAOC,UCFvBjF,KAAKkF,GCAXlF,KAAKkF,GCIL5J,OAAOsD,OCJFtD,OAAOM,UC8B3B,QA5Bc,SAARuJ,EAAiBzJ,GACrB,GAAmB,iBAARA,GAA4B,OAARA,EAC7B,OAAOA,EAET,IAAI0J,EACJ,GAAIhB,EAAQ1I,GAAM,CAChB0J,EAAM,GACN,IAAK,IAAIzI,EAAI,EAAGkH,EAAInI,EAAIkG,OAAQjF,EAAIkH,EAAGlH,IACf,iBAAXjB,EAAIiB,IAA6B,MAAVjB,EAAIiB,GACpCyI,EAAIzI,GAAKwI,EAAMzJ,EAAIiB,IAEnByI,EAAIzI,GAAKjB,EAAIiB,E,MAKjB,IAAK,IAAM0I,KADXD,EAAM,GACU1J,EACQ,iBAAXA,EAAI2J,IAA6B,MAAV3J,EAAI2J,GACpCD,EAAIC,GAAKF,EAAMzJ,EAAI2J,IAEnBD,EAAIC,GAAK3J,EAAI2J,GAKnB,OAAOD,CACT,ECnBA,IAAIE,ECJmBhK,OAAOM,UAAUC,eCFjBP,OAAOM,UAAUC,eCKxC,SAAgB0J,EAAaC,GAC3B,IAAKC,EAAWF,GACd,MAAM,IAAIG,UAAU,uBAiBL,IAAIjB,GAGtB,CHhBD,EACE,SAACkB,EAAWC,QAAA,IAAAA,IAAAA,EAAA,IACF,IIdIC,EJcJC,EAA6DF,EAAIE,SAAvDC,EAAmDH,EAAIG,WAA3CC,EAAuCJ,EAAII,WAA/BC,EAA2BL,EAAIK,UAApBC,EAAgBN,EAAIM,YAKzE,OAJKZ,IACHA,EAAMa,SAASC,cAAc,UAAUC,WAAW,OAEpDf,EAAKM,KAAO,CAACK,EAAWC,EAAaF,EAAeF,EAAQ,KAAMC,GAAYO,KAAK,KAC5EhB,EAAKiB,aInBAV,EJmBqBF,EIlB5BxB,EAAO0B,EAAK,UJkBwBF,EAAO,KAAIa,KACtD,IKnBF,oBAAAC,IACE,KAAAxH,IAA4B,EA0B9B,CAxBEwH,EAAA7K,UAAAiJ,IAAA,SAAIzJ,GACF,YAAyBkD,IAAlB,KAAKW,IAAI7D,EAClB,EAEAqL,EAAA7K,UAAAH,IAAA,SAAIL,EAAasL,GACf,IAAMC,EAAI,KAAK1H,IAAI7D,GACnB,YAAakD,IAANqI,EAAkBD,EAAMC,CACjC,EAEAF,EAAA7K,UAAAkJ,IAAA,SAAI1J,EAAaa,GACf,KAAKgD,IAAI7D,GAAOa,CAClB,EAEAwK,EAAA7K,UAAAgL,MAAA,WACE,KAAK3H,IAAM,EACb,EAEAwH,EAAA7K,UAAA,gBAAOR,UACE,KAAK6D,IAAI7D,EAClB,EAEAqL,EAAA7K,UAAAiL,KAAA,WACE,OAAOvL,OAAOgI,KAAK,KAAKrE,KAAK2C,MAC/B,CACF,CA3BA,GCiFA,QA9DiB,SACf1F,EACAa,EACAZ,EACA2K,GAEQ,MAA2B5K,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxB0K,EAAU,GACVC,EAAQ,CAAC,EACTC,EAAI,CAAC,EACLC,EAAQ,CAAC,EACf9K,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAAMC,EAAKF,EAAKE,GAChBmK,EAAQlK,KAAKD,GACbqK,EAAErK,GAAMuK,IACJvK,IAAOG,IAAQkK,EAAErK,GAAM,EAC7B,IAGA,IADA,IAAMwK,EAAUhL,EAAMwF,O,WACbjF,GAEP,IAAM0K,EAvCQ,SAChBJ,EACA7K,EACA4K,GAKA,IAFA,IACIK,EADAC,EAASH,IAEJxK,EAAI,EAAGA,EAAIP,EAAMwF,OAAQjF,IAAK,CACrC,IAAM6C,EAASpD,EAAMO,GAAGC,IACnBoK,EAAMxH,IAAWyH,EAAEzH,IAAW8H,IACjCA,EAASL,EAAEzH,GACX6H,EAAUjL,EAAMO,G,CAGpB,OAAO0K,CACT,CAuBoBE,CAAUN,EAAG7K,EAAO4K,GAC9BQ,EAAYH,EAAQzK,GAG1B,GAFAoK,EAAMQ,IAAa,EAEfP,EAAEO,KAAeL,I,iBAErB,IAAIM,EAA6B,GACnBA,EAAVtL,E3BbyB,SAACqD,EAAgBnD,GAChD,OAAOA,EAAMsD,QAAO,SAAA7C,GAAQ,OAAAA,EAAKC,SAAWyC,CAAhB,GAC9B,C2BWiCkI,CAAkBF,EAAWnL,GACtCuD,EAAiB4H,EAAWnL,GAEhDoL,EAAahL,SAAQ,SAAAK,GACnB,IAAM6K,EAAa7K,EAAKE,OAClB4K,EAAa9K,EAAKC,OAClB8K,EAAIF,IAAeH,EAAYI,EAAaD,EAC5CG,EAAShB,GAAsBhK,EAAKgK,GAAsBhK,EAAKgK,GAAsB,EACvFG,EAAEY,GAAKZ,EAAEI,EAAQzK,IAAMkL,GACzBb,EAAEY,GAAKZ,EAAEI,EAAQzK,IAAMkL,EACvBZ,EAAMW,GAAK,CAACR,EAAQzK,KACXqK,EAAEY,KAAOZ,EAAEI,EAAQzK,IAAMkL,GAClCZ,EAAMW,GAAGhL,KAAKwK,EAAQzK,GAE1B,G,EAvBOD,EAAI,EAAGA,EAAIyK,EAASzK,I,IA0B7BuK,EAAMnK,GAAU,CAACA,GAEjB,IAAMgL,EAAQ,CAAC,EACf,IAAK,IAAM/K,KAAUiK,EACfA,EAAEjK,KAAYmK,KAChBa,EAAajL,EAAQC,EAAQkK,EAAOa,GAKxC,IAAME,EAAO,CAAC,EACd,IAAK,IAAMjL,KAAU+K,EACnBE,EAAKjL,GAAU+K,EAAM/K,GAAQ,GAE/B,MAAO,CAAE4E,OAAQqF,EAAGgB,KAAI,EAAEC,QAASH,EACrC,EAIA,SAASC,EAAajL,EAAQC,EAAQkK,EAAOiB,GAC3C,GAAIpL,IAAWC,EACb,MAAO,CAACD,GAEV,GAAIoL,EAAWnL,GACb,OAAOmL,EAAWnL,GAGpB,IADA,IAAM+K,EAAQ,GACG,MAAAb,EAAMlK,GAAN,eAAe,CAA3B,IACGoL,EAAYJ,EAAajL,EADpB,KACkCmK,EAAOiB,GACpD,IAAKC,EAAW,OAChB,IAAoB,UAAAA,EAAA,eAAW,CAA1B,IAAIC,EAAO,KACV,EAAQA,GAAUN,EAAMlL,KAAK,OAAIwL,GAAS,GAAF,CAAErL,IAAM,IAC/C+K,EAAMlL,KAAK,CAACwL,EAASrL,G,EAI9B,OADAmL,EAAWnL,GAAU+K,EACdI,EAAWnL,EACpB,CCpGO,IAAMsL,EAAmB,SAC9BpM,EACAqM,EACAC,EACArM,EACA2K,GAEM,MAA4B,EAChC5K,EACAqM,EACApM,EACA2K,GAJMlF,EAAM,SAAEqG,EAAI,OAAEC,EAAO,UAM7B,MAAO,CAAEtG,OAAQA,EAAO4G,GAAMP,KAAMA,EAAKO,GAAMN,QAASA,EAAQM,GAClE,EAEaC,EAAc,SACzBvM,EACAqM,EACAC,EACArM,G,MAEA,GAAIoM,IAAUC,EAAK,MAAO,CAAC,CAACD,IAEpB,MAAerM,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAEZiF,EAAU,CAACiH,GACXG,IAAS,MAAMH,IAAQ,EAAI,GAC3BI,EAAoB,GACpBT,EAAU,GACZzG,EAAYtF,EACZ,EAAaoM,EAAOlM,EAAO,UAC3B,EAAakM,EAAOlM,GAGxB,IAFAsM,EAAM9L,KAAK4E,GAEJH,EAAQM,OAAS,GAAK+G,EAAM/G,OAAS,GAAG,CAC7C,IAAMgH,EAAWD,EAAMA,EAAM/G,OAAS,GACtC,GAAIgH,EAAShH,OAAb,CACE,IAAMiH,EAAQD,EAASE,QAgBzB,GAfMD,IACFvH,EAAQzE,KAAKgM,GACbH,EAAUG,IAAS,EACnBpH,EAAYtF,EACR,EAAa0M,EAAOxM,EAAO,UAC3B,EAAawM,EAAOxM,GACxBsM,EAAM9L,KAAK4E,EAAU9B,QAAO,SAAA+B,GAAY,OAACgH,EAAUhH,EAAX,MASxCJ,EAAQA,EAAQM,OAAS,KAAO4G,EAAK,CACvC,IAAMP,EAAO3G,EAAQrC,KAAI,SAAAvC,GAAQ,OAAAA,CAAA,IACjCwL,EAAQrL,KAAKoL,GAEPvL,EAAO4E,EAAQQ,MACrB4G,EAAUhM,IAAQ,EAClBiM,EAAM7G,K,MAvBR,CAWE,IAAMpF,EAAO4E,EAAQQ,MACrB4G,EAAUhM,IAAQ,EAClBiM,EAAM7G,K,EAcV,OAAOoG,CACT,ECpCA,QA9BsB,SAAChM,EAAsBC,GAK3C,IAJA,IAAM4M,EAAiB,EAAa7M,EAAWC,GAEzC6M,EAAiB,GACjBnC,EAAOkC,EAAenH,OACnBjF,EAAI,EAAGA,EAAIkK,EAAMlK,GAAK,EAAG,CAChCqM,EAAKrM,GAAK,GACV,IAAK,IAAIsM,EAAI,EAAGA,EAAIpC,EAAMoC,GAAK,EACzBtM,IAAMsM,EACRD,EAAKrM,GAAGsM,GAAK,EACqB,IAAzBF,EAAepM,GAAGsM,IAAaF,EAAepM,GAAGsM,GAG1DD,EAAKrM,GAAGsM,GAAKF,EAAepM,GAAGsM,GAF/BD,EAAKrM,GAAGsM,GAAK9B,G,CAOnB,IAAK,IAAI9B,EAAI,EAAGA,EAAIwB,EAAMxB,GAAK,EAC7B,IAAS1I,EAAI,EAAGA,EAAIkK,EAAMlK,GAAK,EAC7B,IAASsM,EAAI,EAAGA,EAAIpC,EAAMoC,GAAK,EACzBD,EAAKrM,GAAGsM,GAAKD,EAAKrM,GAAG0I,GAAK2D,EAAK3D,GAAG4D,KACpCD,EAAKrM,GAAGsM,GAAKD,EAAKrM,GAAG0I,GAAK2D,EAAK3D,GAAG4D,IAK1C,OAAOD,CACT,ECuHA,EA1IyB,SACvB9M,EACAC,EACA2K,EACAoC,QAFA,IAAA/M,IAAAA,GAAA,QACA,IAAA2K,IAAAA,EAAA,eACA,IAAAoC,IAAAA,EAAA,KAGQ,MAA2BhN,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAExB8M,EAAW,CAAC,EACZ5M,EAAU,CAAC,EAEjBH,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAAMyM,EAAcvJ,IACpBnD,EAAK2M,UAAYD,EACjBD,EAASC,GAAO,CACdxM,GAAIwM,EACJhN,MAAO,CAACM,IAEVH,EAAQG,EAAKE,IAAM,CACjBF,KAAI,EACJ4M,IAAK3M,EAET,IAGA,IAAM4M,EAAY,EAAarN,EAAWC,GAEpCqN,EAAK,GAQL/H,EAAY,CAAC,EACnB8H,EAAU9M,SAAQ,SAACgN,EAAK9M,GACtB,IAAI0I,EAAI,EACFqE,EAAMtN,EAAMO,GAAGC,GACrB6E,EAAUiI,GAAO,CAAC,EAClBD,EAAIhN,SAAQ,SAACkN,EAAOV,GAClB,GAAKU,EAAL,CACAtE,GAAKsE,EACL,IAAMC,EAAMxN,EAAM6M,GAAGrM,GACrB6E,EAAUiI,GAAKE,GAAOD,CAHJ,CAIpB,IACAH,EAAG3M,KAAKwI,EACV,IAIA,IAFA,IAAIwE,EAAO,E,aAGT,IAAIC,GAAU,EAuCd,GAtCA1N,EAAMK,SAAQ,SAAAC,GACZ,IAAMqN,EAAmB,CAAC,EAC1BzO,OAAOgI,KAAK7B,EAAU/E,EAAKE,KAAKH,SAAQ,SAAAuN,GACtC,IAAMC,EAAiBxI,EAAU/E,EAAKE,IAAIoN,GAEpCE,EADe3N,EAAQyN,GAAYtN,KACF2M,UAClCU,EAAiBG,KAAoBH,EAAiBG,GAAqB,GAChFH,EAAiBG,IAAsBD,CACzC,IAEA,IAAIE,GAAY,IACZC,EAAiB,GASrB,GARA9O,OAAOgI,KAAKyG,GAAkBtN,SAAQ,SAAA4M,GAChCc,EAAYJ,EAAiBV,IAC/Bc,EAAYJ,EAAiBV,GAC7Be,EAAiB,CAACf,IACTc,IAAcJ,EAAiBV,IACxCe,EAAevN,KAAKwM,EAExB,IAC8B,IAA1Be,EAAexI,QAAgBwI,EAAe,KAAO1N,EAAK2M,UAA9D,CACA,IAAMgB,EAAiBD,EAAejI,QAAQzF,EAAK2M,WAEnD,GADIgB,GAAkB,GAAGD,EAAerF,OAAOsF,EAAgB,GAC3DD,GAAkBA,EAAexI,OAAQ,CAC3CkI,GAAU,EAGV,IAAMQ,EAAcnB,EAASzM,EAAK2M,WAC5BkB,EAAuBD,EAAYlO,MAAM+F,QAAQzF,GACvD4N,EAAYlO,MAAM2I,OAAOwF,EAAsB,GAG/C,IAAMC,EAAYxK,KAAKyK,MAAMzK,KAAKC,SAAWmK,EAAexI,QACtD8I,EAAcvB,EAASiB,EAAeI,IAC5CE,EAAYtO,MAAMS,KAAKH,GACvBA,EAAK2M,UAAYqB,EAAY9N,E,CAfgD,CAiBjF,KACKkN,E,cACLD,G,EAzCKA,EAAOX,G,gBA6Cd5N,OAAOgI,KAAK6F,GAAU1M,SAAQ,SAAA4M,GAC5B,IAAMsB,EAAUxB,EAASE,GACpBsB,EAAQvO,OAAUuO,EAAQvO,MAAMwF,eAC5BuH,EAASE,EAEpB,IAGA,IAAMuB,EAAe,GACfC,EAAiB,CAAC,EACxBxO,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzBgL,EAAShL,EAAKgK,IAAuB,EACrCgE,EAAkBvO,EAAQQ,GAAQL,KAAK2M,UACvC0B,EAAkBxO,EAAQS,GAAQN,KAAK2M,UACvC2B,EAAY,UAAGF,EAAe,cAAMC,GAC1C,GAAIF,EAAeG,GACjBH,EAAeG,GAAWlD,QAAUA,EACpC+C,EAAeG,GAAWC,YACrB,CACL,IAAMC,EAAU,CACdnO,OAAQ+N,EACR9N,OAAQ+N,EACRjD,OAAM,EACNmD,MAAO,GAETJ,EAAeG,GAAaE,EAC5BN,EAAa/N,KAAKqO,E,CAEtB,IAEA,IAAMC,EAAgB,GAItB,OAHA7P,OAAOgI,KAAK6F,GAAU1M,SAAQ,SAAA4M,GAC5B8B,EAActO,KAAKsM,EAASE,GAC9B,IACO,CACLF,SAAUgC,EACVP,aAAY,EAEhB,ECUA,QAxJA,WAGE,WAAYrG,GACVhH,KAAKgH,IAAMA,CACb,CAiJF,OA/IE,YAAA6G,OAAA,WACE,OAAO7N,KAAKgH,KAAO,EACrB,EAEA,YAAA8G,IAAA,SAAIC,G,MACIC,EAAWD,EAAY/G,IAC7B,KAAa,QAAR,EAAAhH,KAAKgH,WAAG,eAAE3C,QACb,OAAO,IAAI4J,EAAOD,GAEpB,KAAKA,aAAQ,EAARA,EAAU3J,QACb,OAAO,IAAI4J,EAAOjO,KAAKgH,KAEzB,GAAIhH,KAAKgH,IAAI3C,SAAW2J,EAAS3J,OAAQ,CACvC,IAAI6J,EAAM,GACV,IAAK,IAAI3L,KAASvC,KAAKgH,IACrBkH,EAAI3L,GAASvC,KAAKgH,IAAIzE,GAASyL,EAASzL,GAE1C,OAAO,IAAI0L,EAAOC,E,CAEtB,EAEA,YAAAC,SAAA,SAASJ,G,MACDC,EAAWD,EAAY/G,IAC7B,KAAa,QAAR,EAAAhH,KAAKgH,WAAG,eAAE3C,QACb,OAAO,IAAI4J,EAAOD,GAEpB,KAAKA,aAAQ,EAARA,EAAU3J,QACb,OAAO,IAAI4J,EAAOjO,KAAKgH,KAEzB,GAAIhH,KAAKgH,IAAI3C,SAAW2J,EAAS3J,OAAQ,CACvC,IAAI6J,EAAM,GACV,IAAK,IAAI3L,KAASvC,KAAKgH,IACrBkH,EAAI3L,GAASvC,KAAKgH,IAAIzE,GAASyL,EAASzL,GAE1C,OAAO,IAAI0L,EAAOC,E,CAEtB,EAEA,YAAAE,IAAA,SAAI/J,GACF,IAAI6J,EAAM,GACV,GAAe,IAAX7J,EACF,IAAK,IAAI9B,KAASvC,KAAKgH,IACrBkH,EAAI3L,GAASvC,KAAKgH,IAAIzE,GAAS8B,EAGnC,OAAO,IAAI4J,EAAOC,EACpB,EAEA,YAAAG,OAAA,WACE,IAAIH,EAAM,GACV,IAAK,IAAI3L,KAASvC,KAAKgH,IACrBkH,EAAI3L,IAAWvC,KAAKgH,IAAIzE,GAE1B,OAAO,IAAI0L,EAAOC,EACpB,EAGA,YAAAI,wBAAA,SAAwBP,G,MAChBC,EAAWD,EAAY/G,IAC7B,KAAa,QAAR,EAAAhH,KAAKgH,WAAG,eAAE3C,WAAW2J,aAAQ,EAARA,EAAU3J,QAClC,OAAO,EAET,GAAIrE,KAAKgH,IAAI3C,SAAW2J,EAAS3J,OAAQ,CACvC,IAAI6J,EAAM,EACV,IAAK,IAAI3L,KAASvC,KAAKgH,IACrBkH,GAAOzL,KAAK8L,IAAIvO,KAAKgH,IAAIzE,GAASwL,EAAY/G,IAAIzE,GAAQ,GAE5D,OAAO2L,C,CAEX,EAGA,YAAAM,kBAAA,SAAkBT,G,MACVC,EAAWD,EAAY/G,IAC7B,KAAa,QAAR,EAAAhH,KAAKgH,WAAG,eAAE3C,WAAW2J,aAAQ,EAARA,EAAU3J,QAClC,OAAO,EAET,GAAIrE,KAAKgH,IAAI3C,SAAW2J,EAAS3J,OAAQ,CACvC,IAAI6J,EAAM,EACV,IAAK,IAAI3L,KAASvC,KAAKgH,IACrBkH,GAAOzL,KAAK8L,IAAIvO,KAAKgH,IAAIzE,GAASwL,EAAY/G,IAAIzE,GAAQ,GAE5D,OAAOE,KAAKgM,KAAKP,E,CAEjBQ,QAAQC,MAAM,yCAElB,EAGA,YAAAC,UAAA,WACE,IAAIV,EAAM,GACJW,EAAW,EAAM7O,KAAKgH,KAC5B6H,EAASC,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,IACxB,IAAMiP,EAAMF,EAASA,EAASxK,OAAS,GACjCS,EAAM+J,EAAS,GACrB,IAAK,IAAItM,KAASvC,KAAKgH,IACrBkH,EAAI3L,IAAUvC,KAAKgH,IAAIzE,GAASuC,IAAQiK,EAAMjK,GAEhD,OAAO,IAAImJ,EAAOC,EACpB,EAGA,YAAAc,MAAA,W,MACE,KAAa,QAAR,EAAAhP,KAAKgH,WAAG,eAAE3C,QACb,OAAO,EAET,IAAI6J,EAAM,EACR,IAAK,IAAI3L,KAASvC,KAAKgH,IACrBkH,GAAOzL,KAAK8L,IAAIvO,KAAKgH,IAAIzE,GAAQ,GAErC,OAAOE,KAAKgM,KAAKP,EACnB,EAGA,YAAAe,IAAA,SAAIlB,G,MACIC,EAAWD,EAAY/G,IAC7B,KAAa,QAAR,EAAAhH,KAAKgH,WAAG,eAAE3C,WAAW2J,aAAQ,EAARA,EAAU3J,QAClC,OAAO,EAET,GAAIrE,KAAKgH,IAAI3C,SAAW2J,EAAS3J,OAAQ,CACvC,IAAI6J,EAAM,EACV,IAAK,IAAI3L,KAASvC,KAAKgH,IACrBkH,GAAOlO,KAAKgH,IAAIzE,GAASwL,EAAY/G,IAAIzE,GAE3C,OAAO2L,C,CAEPQ,QAAQC,MAAM,yCAElB,EAGA,YAAAO,MAAA,SAAMnB,G,MACEC,EAAWD,EAAY/G,IAC7B,IAAY,QAAR,EAAAhH,KAAKgH,WAAG,eAAE3C,WAAW2J,aAAQ,EAARA,EAAU3J,QACjC,OAAO,EAET,IAAK,IAAI9B,KAASvC,KAAKgH,IACrB,GAAIhH,KAAKgH,IAAIzE,KAAWyL,EAASzL,GAC/B,OAAO,EAGX,OAAO,CACT,EACF,EAtJA,GCNO,ICmDK4M,GAAZ,SAAYA,GACV,uCACD,CAFD,CAAYA,IAAAA,EAAY,KCzCjB,IAmCMC,EAAS,SAACC,EAAyBC,EAAyBC,GAEvE,IAAMC,EArCyB,SAACH,EAAyBC,EAAyBC,GAClF,IAAIxJ,EAAO,IAEPuJ,aAAY,EAAZA,EAAcjL,QAChB0B,EAAOuJ,GAGPD,EAASnQ,SAAQ,SAAAuQ,GACf1J,EAAOA,EAAKU,OAAO1I,OAAOgI,KAAK0J,GACjC,IACA1J,EAAOgB,EAAKhB,IAGd,IAAMyJ,EAA8B,CAAC,EAarC,OAZAzJ,EAAK7G,SAAQ,SAAArB,GACX,IAAIa,EAAQ,GACZ2Q,EAASnQ,SAAQ,SAAAuQ,QACG1O,IAAd0O,EAAK5R,IAAoC,KAAd4R,EAAK5R,IAClCa,EAAMY,KAAKmQ,EAAK5R,GAEpB,IACIa,EAAM2F,UAAWkL,aAAc,EAAdA,EAAgBG,SAAS7R,MAC5C2R,EAAe3R,GAAOkJ,EAAKrI,GAE/B,IAEO8Q,CACT,CAUyBG,CAAkBN,EAAUC,EAAcC,GAC3DK,EAAa,GACnB,IAAK7R,OAAOgI,KAAKyJ,GAAgBnL,OAC/B,OAAOuL,EAIT,IAEMC,EAFW9R,OAAOsD,OAAOmO,GAEFM,OAAM,SAAApR,GAAS,OAAAA,EAAMoR,OAAM,SAAAzI,GAAQ,MAAkB,iBAAX,CAAP,GAApB,IA2B5C,OAxBAgI,EAASnQ,SAAQ,SAACuQ,EAAMlN,GACtB,IAAIwN,EAAO,GACXhS,OAAOgI,KAAKyJ,GAAgBtQ,SAAQ,SAAArB,GAClC,IAAMmS,EAAWP,EAAK5R,GAChBoS,EAAcT,EAAe3R,GAC7BqS,EAAaD,EAAYE,WAAU,SAAAzR,GAAS,OAAAsR,IAAatR,CAAb,IAC9C0R,EAAU,GAEd,GAAIP,EACFO,EAAQ9Q,KAAK0Q,QAGb,IAAI,IAAI5Q,EAAI,EAAGA,EAAI6Q,EAAY5L,OAAQjF,IACjCA,IAAM8Q,EACRE,EAAQ9Q,KAAK,GAEb8Q,EAAQ9Q,KAAK,GAInByQ,EAAOA,EAAKtJ,OAAO2J,EACrB,IACAR,EAAWrN,GAASwN,CACtB,IACOH,CACT,EC7EMS,EAAgB,SACpBxR,EACAmN,EACAC,EACAqE,GAKA,IAHA,IAAMjM,EAAS2H,EAAU3H,OACnBkM,EAAQ,EAAID,EACdE,EAAa,EACRpR,EAAI,EAAGA,EAAIiF,EAAQjF,IAE1B,IADA,IAAMqR,EAAW5R,EAAMO,GAAG0M,UACjBJ,EAAI,EAAGA,EAAIrH,EAAQqH,IAEtB+E,IADa5R,EAAM6M,GAAGI,YAK1B0E,IAHcxE,EAAU5M,GAAGsM,IAAM,IACtBO,EAAG7M,IAAM,IACT6M,EAAGP,IAAM,GACa6E,GAIrC,OADAC,GAAe,EAAID,EAErB,EAGMG,EAAwB,SAC5B7R,EACA8R,QADA,IAAA9R,IAAAA,EAAA,IAKA,IAFA,IAAMwF,EAASxF,EAAMwF,OACjBuM,EAAkB,IAAI,EAAO,IACxBxR,EAAI,EAAGA,EAAIiF,EAAQjF,IAC1BwR,EAAkBA,EAAgB9C,IAAI,IAAI,EAAO6C,EAAoBvR,KAGvE,IAAMyR,EAAgBD,EAAgBxC,IAAI/J,GAE1CwM,EAAcjC,YAEd,IAAIkC,EAAmB,EACvB,IAAS1R,EAAI,EAAGA,EAAIiF,EAAQjF,IAG1B0R,IAFMC,EAAc,IAAI,EAAOJ,EAAoBvR,KACPkP,wBAAwBuC,GAKtE,IAAIG,EAA8B,GAIlC,IAHAnS,EAAMK,SAAQ,WACZ8R,EAA4B1R,KAAK,GACnC,IACSF,EAAI,EAAGA,EAAIiF,EAAQjF,IAAK,CAC/B,IAAM2R,EAAc,IAAI,EAAOJ,EAAoBvR,IACnDP,EAAMO,GAAoB,gBAAI,EAC9B,IAAK,IAAIsM,EAAI,EAAGA,EAAIrH,EAAQqH,IAC1B,GAAKtM,IAAMsM,EAAX,CAIA,IAAMuF,EAAc,IAAI,EAAON,EAAoBjF,IACnDsF,EAA4B5R,GAAGsM,GAAKqF,EAAYzC,wBAAwB2C,GACxEpS,EAAMO,GAAoB,iBAAK4R,EAA4B5R,GAAGsM,E,MAL5DsF,EAA4B5R,GAAGsM,GAAK,C,CAU1C,IAAIwF,EAA6B,EAC3BX,EAAQ,EAAIlM,EAASyM,EAC3B,IAAS1R,EAAI,EAAGA,EAAIiF,EAAQjF,IAC1B,KAAMqR,EAAW5R,EAAMO,GAAG0M,UAC1B,IAASJ,EAAI,EAAGA,EAAIrH,EAAQqH,IAAK,CAC/B,IAAMyF,EAAWtS,EAAM6M,GAAGI,UACrB1M,IAAMsM,GAAK+E,IAAaU,IAE7BD,GADkBrS,EAAMO,GAAGgS,gBAAkBvS,EAAM6M,GAAG0F,gBAAmB3O,KAAK8L,IAAIgC,EAAO,GAAKS,EAA4B5R,GAAGsM,GAAK6E,E,CAJjG,CAQrC,OAAO9I,OAAOyJ,EAAmBG,QAAQ,GAC3C,EA2TA,QA5SgB,SACd1S,EACAC,EACA2K,EACA+H,EACAJ,EACAK,EACAjC,EACAC,EACAiC,QAPA,IAAA5S,IAAAA,GAAA,QACA,IAAA2K,IAAAA,EAAA,eACA,IAAA+H,IAAAA,EAAA,WACA,IAAAJ,IAAAA,GAAA,QACA,IAAAK,IAAAA,OAAA,QACA,IAAAjC,IAAAA,EAAA,SACA,IAAAC,IAAAA,EAAA,CAA4B,YAC5B,IAAAiC,IAAAA,EAAA,GAGQ,MAA2B7S,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAE1B6R,EAAsB,GAC1B,GAAIO,EAAoB,CACtBrS,EAAMK,SAAQ,SAACC,EAAMoD,GACnBpD,EAAKsS,WAAatS,EAAKsS,YAAc,CAAC,EACtCtS,EAAKuS,YAAcnP,CACrB,IAEA,IAAI,EAAe,GACf1D,EAAMiR,OAAM,SAAA3Q,GAAQ,OAAAA,EAAKb,eAAe,WAApB,MACtB,EAAeiI,MAAML,KAAK,IAAIyL,IAAI9S,EAAM6C,KAAI,SAAAvC,GAAQ,OAAAA,EAAKyS,QAAL,MACpD/S,EAAMK,SAAQ,SAAAC,GACZA,EAAKsS,WAAWG,SAAW,EAAazB,WAAU,SAAAyB,GAAY,OAAAA,IAAazS,EAAKyS,QAAlB,GAChE,KAGF,IAAMH,ECrEsB,SAAC5S,EAAOhB,QAAA,IAAAA,IAAAA,OAAA,GACtC,IAAMgU,EAAgB,GAStB,OARAhT,EAAMK,SAAQ,SAAAC,QACA4B,IAARlD,GACFgU,EAAcvS,KAAKH,QAEH4B,IAAd5B,EAAKtB,IACPgU,EAAcvS,KAAKH,EAAKtB,GAE5B,IACOgU,CACT,CD0DuBC,CAAiBjT,EAAO0S,GAE3CZ,EAAsBvB,EAAOqC,EAAYnC,EAAcC,E,CAGzD,IAAIjN,EAAW,EAETsJ,EAAuB,CAAC,EACxB5M,EAAU,CAAC,EAEjBH,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAAMyM,EAAckG,OAAOzP,KAC3BnD,EAAK2M,UAAYD,EACjBD,EAASC,GAAO,CACdxM,GAAIwM,EACJhN,MAAO,CAACM,IAEVH,EAAQG,EAAKE,IAAM,CACjBF,KAAI,EACJ4M,IAAK3M,EAET,IAEA,IAAM4M,EAAY,EAAarN,EAAWC,GAEpCqN,EAAK,GAQL/H,EAAY,CAAC,EAEfoM,EAAI,EACRtE,EAAU9M,SAAQ,SAACgN,EAAK9M,GACtB,IAAI0I,EAAI,EACFqE,EAAMtN,EAAMO,GAAGC,GACrB6E,EAAUiI,GAAO,CAAC,EAClBD,EAAIhN,SAAQ,SAACkN,EAAOV,GAClB,GAAKU,EAAL,CACAtE,GAAKsE,EACL,IAAMC,EAAMxN,EAAM6M,GAAGrM,GACrB6E,EAAUiI,GAAKE,GAAOD,EACtBkE,GAAKlE,CAJa,CAKpB,IACAH,EAAG3M,KAAKwI,EACV,IAEAwI,GAAK,EAQL,IANA,IAAI0B,EAAkBpI,IAClBqI,EAAqBrI,IACrB0C,EAAO,EAEP4F,EAAa,GACbC,EAAgB,CAAC,IACR,CAETH,EADEd,GAAsBrS,EAAMiR,OAAM,SAAA3Q,GAAQ,OAAAA,EAAKb,eAAe,aAApB,IAC1B+R,EAAcxR,EAAOmN,EAAWC,EAAIqE,GAAKI,EAAsB7R,EAAO8R,GAAuBa,EAE7FnB,EAAcxR,EAAOmN,EAAWC,EAAIqE,GAI3C,IAAThE,IACF2F,EAAqBD,EACrBE,EAAarT,EACbsT,EAAgBvG,GAGlB,IAAMwG,EAA0BJ,EAAkB,GAAKA,EAAkBC,GAAsBD,EAAkBC,EAAqBX,EAYtI,GAVIU,EAAkBC,IACpBC,EAAarT,EAAM6C,KAAI,SAAAvC,GAAQ,OAC7BA,KAAI,EACJ2M,UAAW3M,EAAK2M,UAFa,IAI/BqG,EAAgB,EAAMvG,GACtBqG,EAAqBD,GAIlBI,GAA2B9F,EAAO,IACrC,MAEFA,IAEAvO,OAAOgI,KAAK6F,GAAU1M,SAAQ,SAAA4M,GAE5B,IAAIuG,EAAS,EACbvT,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzBgO,EAAkBvO,EAAQQ,GAAQL,KAAK2M,UACvC0B,EAAkBxO,EAAQS,GAAQN,KAAK2M,WACxCyB,IAAoBzB,GAAa0B,IAAoB1B,GACpD0B,IAAoB1B,GAAayB,IAAoBzB,KACzDuG,GAAmB9S,EAAKgK,IAAiC,EAE7D,IACAqC,EAASE,GAAWuG,OAASA,CAC/B,IAIAxT,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAEI+N,EAFEJ,EAAcnB,EAASzM,EAAK2M,WAC9BwG,EAAe,EAGbC,EAActG,EAAG7M,IAAM,EAAIkR,GAG7BkC,EAAO,EACLC,EAAmB1F,EAAYlO,MACrC4T,EAAiBvT,SAAQ,SAAAwT,GACvB,IAAMC,EAAY3T,EAAQ0T,EAAOrT,IAAI0M,IACrCyG,GAAQxG,EAAU5M,GAAGuT,IAAc,CACrC,IAEA,IAAMC,EAAmBJ,EAAOzF,EAAYsF,OAASE,EAE/CM,EAA8BJ,EAAiBrQ,QAAO,SAAAsQ,GAAU,OAAAA,EAAOrT,KAAOF,EAAKE,EAAnB,IAClEyT,EAAyB,GAC7BD,EAA4B3T,SAAQ,SAAC6T,EAAYxQ,GAC/CuQ,EAAuBvQ,GAASoO,EAAoBoC,EAAWrB,YACjE,IAEA,IAAMsB,EAA2BtC,EAAsBmC,EAA6BlC,GAAuBa,EAGrGyB,EAAkB/O,EAAU/E,EAAKE,IA6CvC,GA5CAtB,OAAOgI,KAAKkN,GAAiB/T,SAAQ,SAAAgU,GACnC,IACMvG,EADe3N,EAAQkU,GAAgB/T,KACN2M,UAGvC,GAAIa,IAAsBxN,EAAK2M,UAA/B,CACA,IAAMqH,EAAkBvH,EAASe,GAC3ByG,EAAeD,EAAgBtU,MAGrC,GAAKuU,GAAiBA,EAAa/O,OAAnC,CAGA,IAAIgP,EAAsB,EAC1BD,EAAalU,SAAQ,SAAAoU,GACnB,IAAMC,EAAWvU,EAAQsU,EAAMjU,IAAI0M,IACnCsH,GAAuBrH,EAAU5M,GAAGmU,IAAa,CACnD,IAGA,IAAMC,EAAgBH,EAAsBF,EAAgBd,OAASE,EAE/DkB,EAAsBL,EAAa3M,OAAO,CAACtH,IAC7CuU,EAAsB,GAC1BD,EAAqBvU,SAAQ,SAACyU,EAASpR,GACrCmR,EAAoBnR,GAASoO,EAAoBgD,EAAQjC,YAC3D,IAEA,IAAMkC,EAAwBlD,EAAsB+C,EAAsB9C,GAAuBa,EAG7FqC,EAAWL,EAAgBZ,EAC3B1B,IACF2C,EAAYL,EAAgBI,GAA0BhB,EAAmBI,IAIvEa,EAAWvB,IACbA,EAAeuB,EACf1G,EAAcgG,EA7BiC,CALD,CAoClD,IAGIb,EAAe,EAAG,CACpBnF,EAAYtO,MAAMS,KAAKH,GACvB,IAAM,EAAoBA,EAAK2M,UAC/B3M,EAAK2M,UAAYqB,EAAY9N,GAE7B,IAAM2N,EAAuBD,EAAYlO,MAAM+F,QAAQzF,GAEvD4N,EAAYlO,MAAM2I,OAAOwF,EAAsB,GAG/C,IAAI,EAAwB,EACxB,EAAoB,EACxBlO,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzBgO,EAAkBvO,EAAQQ,GAAQL,KAAK2M,UACvC0B,EAAkBxO,EAAQS,GAAQN,KAAK2M,WACxCyB,IAAoBJ,EAAY9N,IAAMmO,IAAoBL,EAAY9N,IACrEmO,IAAoBL,EAAY9N,IAAMkO,IAAoBJ,EAAY9N,MAC1E,GAAiDE,EAAKgK,IAAiC,IAEpFgE,IAAoB,GAAqBC,IAAoB,GAC5DA,IAAoB,GAAqBD,IAAoB,KACjE,GAAyChO,EAAKgK,IAAiC,EAEnF,IAGA4D,EAAYkF,OAAS,EACrBtF,EAAYsF,OAAS,C,CAEzB,G,CAIF,IAAMyB,EAAkB,CAAC,EACrBC,EAAa,EACjBhW,OAAOgI,KAAKoM,GAAejT,SAAQ,SAAC4M,GAClC,IAAMsB,EAAU+E,EAAcrG,GAC9B,GAAKsB,EAAQvO,OAAUuO,EAAQvO,MAAMwF,OAArC,CAIA,IAAM2P,EAAQjC,OAAOgC,EAAa,GAC9BC,IAAUlI,IAGdsB,EAAQ/N,GAAK2U,EACb5G,EAAQvO,MAAQuO,EAAQvO,MAAM6C,KAAI,SAAA2F,GAAQ,OAAGhI,GAAIgI,EAAKhI,GAAIyM,UAAWkI,EAA3B,IAC1C7B,EAAc6B,GAAS5G,EACvB0G,EAAgBhI,GAAakI,SACtB7B,EAAcrG,GACrBiI,I,aAZS5B,EAAcrG,EAazB,IAEAoG,EAAWhT,SAAQ,SAAA+U,GACT,IAAA9U,EAAoB8U,EAAQ,KAAtBnI,EAAcmI,EAAQ,UAC/B9U,IACLA,EAAK2M,UAAYA,EACb3M,EAAK2M,WAAagI,EAAgB3U,EAAK2M,aAAY3M,EAAK2M,UAAYgI,EAAgB3U,EAAK2M,YAC/F,IAEA,IAAMuB,EAAe,GACfC,EAAiB,CAAC,EACxBxO,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzBgL,EAAShL,EAAKgK,IAAuB,EACrCgE,EAAkBvO,EAAQQ,GAAQL,KAAK2M,UACvC0B,EAAkBxO,EAAQS,GAAQN,KAAK2M,UAC7C,GAAKyB,GAAoBC,EAAzB,CACA,IAAMC,EAAY,UAAGF,EAAe,cAAMC,GAC1C,GAAIF,EAAeG,GACjBH,EAAeG,GAAWlD,QAAUA,EACpC+C,EAAeG,GAAWC,YACrB,CACL,IAAMC,EAAU,CACdnO,OAAQ+N,EACR9N,OAAQ+N,EACRjD,OAAM,EACNmD,MAAO,GAETJ,EAAeG,GAAaE,EAC5BN,EAAa/N,KAAKqO,E,CAb4B,CAelD,IACA,IAAMC,EAAgB,GAItB,OAHA7P,OAAOgI,KAAKoM,GAAejT,SAAQ,SAAA4M,GACjC8B,EAActO,KAAK6S,EAAcrG,GACnC,IACO,CACLF,SAAUgC,EACVP,aAAY,EAEhB,E,EE3YA,WAKE,WAAY6G,GACVlU,KAAK0N,MAAQwG,EAAM7P,OACnBrE,KAAKmU,OAAS,CAAC,EACf,IAAgB,UAAAD,EAAA,eAAO,CAAlB,IAAM9U,EAAC,KACVY,KAAKmU,OAAO/U,GAAKA,C,CAErB,CA8BF,OA3BE,YAAA0B,KAAA,SAAKuG,GACH,KAAOrH,KAAKmU,OAAO9M,KAAUA,GAC3BA,EAAOrH,KAAKmU,OAAO9M,GAErB,OAAOA,CACT,EAEA,YAAA+M,MAAA,SAAMvU,EAAGC,GACP,IAAMuU,EAAQrU,KAAKc,KAAKjB,GAClByU,EAAQtU,KAAKc,KAAKhB,GAEpBuU,IAAUC,IAGVD,EAAQC,GACNtU,KAAKmU,OAAOrU,KAAOA,GAAGE,KAAKoU,MAAMpU,KAAKmU,OAAOrU,GAAID,GACrDG,KAAKmU,OAAOrU,GAAKE,KAAKmU,OAAOtU,KAEzBG,KAAKmU,OAAOtU,KAAOA,GAAGG,KAAKoU,MAAMpU,KAAKmU,OAAOtU,GAAIC,GACrDE,KAAKmU,OAAOtU,GAAKG,KAAKmU,OAAOrU,IAEjC,EAGA,YAAAyU,UAAA,SAAU1U,EAAGC,GACX,OAAOE,KAAKc,KAAKjB,KAAOG,KAAKc,KAAKhB,EACpC,EACF,EAzCA,GCHA,IAAM0U,EAAiB,SAAC3U,EAAGC,GACzB,OAAOD,EAAIC,CACb,E,QAEA,WAKE,WAAY2U,QAAA,IAAAA,IAAAA,EAAA,GACVzU,KAAKyU,UAAYA,EACjBzU,KAAK0U,KAAO,EACd,CA6EF,OA3EE,YAAAC,QAAA,SAAQpS,GACN,OAAO,EAAIA,EAAQ,CACrB,EAEA,YAAAqS,SAAA,SAASrS,GACP,OAAO,EAAIA,EAAQ,CACrB,EAEA,YAAAsS,UAAA,SAAUtS,GACR,OAAc,IAAVA,EACK,KAEFE,KAAKyK,OAAO3K,EAAQ,GAAK,EAClC,EAEA,YAAAX,QAAA,WACE,OAAO5B,KAAK0U,KAAKrQ,QAAU,CAC7B,EAEA,YAAAyQ,IAAA,WACE,OAAO9U,KAAK4B,eAAYb,EAAYf,KAAK0U,KAAK,EAChD,EAEA,YAAAK,OAAA,WACE,IAAMD,EAAM9U,KAAK8U,MACXE,EAAShV,KAAK0U,KAAKnQ,MAKzB,OAJIvE,KAAK0U,KAAKrQ,OAAS,IACrBrE,KAAK0U,KAAK,GAAKM,EACfhV,KAAKiV,SAAS,IAETH,CACT,EAEA,YAAAI,OAAA,SAAOxW,GACL,GAAc,OAAVA,EAAgB,CAClBsB,KAAK0U,KAAKpV,KAAKZ,GACf,IAAM6D,EAAQvC,KAAK0U,KAAKrQ,OAAS,EAEjC,OADArE,KAAKmV,OAAO5S,IACL,C,CAET,OAAO,CACT,EAEA,YAAA4S,OAAA,SAAO5S,GAEL,IADA,IAAI4R,EAASnU,KAAK6U,UAAUtS,GACrBA,GAASA,EAAQ,GAAKvC,KAAKyU,UAAUzU,KAAK0U,KAAKP,GAASnU,KAAK0U,KAAKnS,IAAU,GAAG,CAEpF,IAAM6S,EAAMpV,KAAK0U,KAAKP,GACtBnU,KAAK0U,KAAKP,GAAUnU,KAAK0U,KAAKnS,GAC9BvC,KAAK0U,KAAKnS,GAAS6S,EAEnB7S,EAAQ4R,EACRA,EAASnU,KAAK6U,UAAUtS,E,CAE5B,EAEA,YAAA0S,SAAA,SAAS1S,G,MACH8S,EAAU9S,EACR+S,EAAOtV,KAAK2U,QAAQpS,GACpBgT,EAAQvV,KAAK4U,SAASrS,GACtB+G,EAAOtJ,KAAK0U,KAAKrQ,OACV,OAATiR,GAAiBA,EAAOhM,GAAQtJ,KAAKyU,UAAUzU,KAAK0U,KAAKW,GAAUrV,KAAK0U,KAAKY,IAAS,EACxFD,EAAUC,EAEA,OAAVC,GACAA,EAAQjM,GACRtJ,KAAKyU,UAAUzU,KAAK0U,KAAKW,GAAUrV,KAAK0U,KAAKa,IAAU,IAEvDF,EAAUE,GAERhT,IAAU8S,IACZ,EAAyC,CAACrV,KAAK0U,KAAKW,GAAUrV,KAAK0U,KAAKnS,IAAvEvC,KAAK0U,KAAKnS,GAAM,KAAEvC,KAAK0U,KAAKW,GAAQ,KACrCrV,KAAKiV,SAASI,GAElB,EACF,EArFA,GCOA,IAAMG,EAAU,SAAC7W,EAAsB4L,GACrC,IAAMkL,EAAgB,GACd,EAA2B9W,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAC9B,GAAqB,IAAjBD,EAAMwF,OACR,OAAOoR,EAIT,IAAMC,EAAW7W,EAAM,GACjBkF,EAAU,IAAI4N,IACpB5N,EAAQ+J,IAAI4H,GAGZ,IAOMC,EAAY,IAAI,GAPA,SAAC9V,EAAeC,GACpC,OAAIyK,EACK1K,EAAE0K,OAASzK,EAAEyK,OAEf,CAET,IAMA,IAJAlI,EAAiBqT,EAASrW,GAAIP,GAAOI,SAAQ,SAACK,GAC5CoW,EAAUT,OAAO3V,EACnB,KAEQoW,EAAU/T,WAAW,CAE3B,IAAMgU,EAAuBD,EAAUZ,SACjCvV,EAASoW,EAASpW,OAClBC,EAASmW,EAASnW,OACpBsE,EAAQuD,IAAI9H,IAAWuE,EAAQuD,IAAI7H,KACvCgW,EAAcnW,KAAKsW,GAEd7R,EAAQuD,IAAI9H,KACfuE,EAAQ+J,IAAItO,GACZ6C,EAAiB7C,EAAQV,GAAOI,SAAQ,SAACK,GACvCoW,EAAUT,OAAO3V,EACnB,KAEGwE,EAAQuD,IAAI7H,KACfsE,EAAQ+J,IAAIrO,GACZ4C,EAAiB5C,EAAQX,GAAOI,SAAQ,SAACK,GACvCoW,EAAUT,OAAO3V,EACnB,K,CAGJ,OAAOkW,CACT,EASMI,EAAa,SAAClX,EAAsB4L,GACxC,IAAMkL,EAAgB,GACd,EAA2B9W,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAC9B,GAAqB,IAAjBD,EAAMwF,OACR,OAAOoR,EAIT,IAAMK,EAAchX,EAAM4C,KAAI,SAACnC,GAAS,OAAAA,CAAA,IACpCgL,GACFuL,EAAYhH,MAAK,SAACjP,EAAGC,GACnB,OAAOD,EAAE0K,OAASzK,EAAEyK,MACtB,IAMF,IAJA,IAAMwL,EAAc,IAAI,EAAUlX,EAAM6C,KAAI,SAACiD,GAAM,OAAAA,EAAEtF,EAAF,KAI5CyW,EAAYzR,OAAS,GAAG,CAC7B,IAAM2R,EAAUF,EAAYvK,QACtB/L,EAASwW,EAAQxW,OACjBC,EAASuW,EAAQvW,OAClBsW,EAAYxB,UAAU/U,EAAQC,KACjCgW,EAAcnW,KAAK0W,GACnBD,EAAY3B,MAAM5U,EAAQC,G,CAG9B,OAAOgW,CACT,EAoBA,QAV4B,SAAC9W,EAAsB4L,EAAiB0L,GAKlE,OAAKA,EAJS,CACZC,KAAMV,EACNW,QAASN,GAIEI,GAAMtX,EAAW4L,GAFZsL,EAAWlX,EAAW4L,EAG1C,EC1CA,EA5DiB,SAAC5L,EAAsByX,EAAkBC,GAGjC,iBAAZD,IAAsBA,EAAU,MACnB,iBAAbC,IAAuBA,EAAW,KAa7C,IAXA,IAMIC,EANAC,EAAW,EACXC,EAAa,EACbC,EAAgB,IAEZ,EAA2B9X,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxB4X,EAAa7X,EAAMwF,OAEnBsS,EAAW,CAAC,EACZC,EAAY,CAAC,EAGVlL,EAAI,EAAGA,EAAIgL,IAAchL,EAGhCiL,EADM1U,GADA9C,EAAON,EAAM6M,IACCrM,IACA,EAAIqX,EACxBE,EAAU3U,GAAW,EAAIyU,EAI3B,IADA,IAAMG,EAAa,EAAOlY,GACnB8X,EAAgB,GAAKF,EAAWH,GAAS,CAE9C,IADAI,EAAa,EACJ9K,EAAI,EAAGA,EAAIgL,IAAchL,EAAG,CACnC,IACMzJ,GADA9C,EAAON,EAAM6M,IACCrM,GAEpB,GADAiX,EAAc,EACuB,IAAjCO,EAAW1X,EAAKE,IAAI6F,SACtByR,EAAS1U,GAAU,MACd,CAEL,IADA,IAAMiC,EAAY,EAAajC,EAAQnD,EAAO,UACrCM,EAAI,EAAGA,EAAI8E,EAAUG,SAAUjF,EAAG,CACzC,IAAM+E,EAAWD,EAAU9E,GACrB+F,EAAoB0R,EAAW1S,GAAUgB,UAC3CA,EAAY,IAAGmR,GAAgBM,EAAUzS,GAAYgB,E,CAE3DwR,EAAS1U,GAAUoU,EAAWC,EAC9BE,GAAcG,EAAS1U,E,EAM3B,IAFAuU,GAAc,EAAIA,GAAcE,EAChCH,EAAW,EACF7K,EAAI,EAAGA,EAAIgL,IAAchL,EAAG,CACnC,IAAMvM,EAENmX,EAAcK,EADR1U,GADA9C,EAAON,EAAM6M,IACCrM,IACamX,EACjCD,GAAY9T,KAAKqU,IAAIR,EAAcM,EAAU3U,IAC7C2U,EAAU3U,GAAUqU,C,CAEtBG,GAAiB,C,CAGnB,OAAOG,CACT,ECnEO,IAGMG,EAAoB,KAIjC,EAME,SACE1X,EACA6G,EACAD,EACA+Q,QAHA,IAAA3X,IAAAA,GAd0B,QAe1B,IAAA6G,IAAAA,GAd0B,QAe1B,IAAAD,IAAAA,GAf0B,QAgB1B,IAAA+Q,IAAAA,EAf6B,MAiB7BhX,KAAKX,GAAKA,EACVW,KAAKkG,KAAOA,EACZlG,KAAKiG,GAAKA,EACVjG,KAAKgX,MAAQA,CACf,EAGF,aAQE,WAAY3X,EAAqB2X,QAArB,IAAA3X,IAAAA,GAjCgB,QAiCK,IAAA2X,IAAAA,EAAA,GAC/BhX,KAAKX,GAAKA,EACVW,KAAKgX,MAAQA,EACbhX,KAAKlB,MAAQ,GACbkB,KAAKiX,QAAU,CAAC,CAClB,CAMF,OAJE,YAAAC,QAAA,SAAQ3X,GACNS,KAAKlB,MAAMQ,KAAKC,GAChBS,KAAKiX,QAAQ1X,EAAKF,IAAME,CAC1B,EACF,EAnBA,GAqBA,aAeE,WACEF,EACA8X,EACAvY,QAFA,IAAAS,IAAAA,GA9D0B,QA+D1B,IAAA8X,IAAAA,GAAA,QACA,IAAAvY,IAAAA,GAAA,GAEAoB,KAAKX,GAAKA,EACVW,KAAKmX,mBAAqBA,EAC1BnX,KAAKlB,MAAQ,GACbkB,KAAKnB,MAAQ,GACbmB,KAAKhB,QAAU,CAAC,EAChBgB,KAAKiX,QAAU,CAAC,EAChBjX,KAAKoX,aAAe,CAAC,EACrBpX,KAAKqX,aAAe,CAAC,EACrBrX,KAAKsX,QAAU,EACftX,KAAKpB,SAAWA,CAClB,CAkCF,OAhCE,YAAA2Y,WAAA,WACE,OAAOvX,KAAKnB,MAAMwF,MACpB,EAEA,YAAAmT,QAAA,SAAQnY,EAAY2X,GAClB,IAAIhX,KAAKhB,QAAQK,GAAjB,CACA,IAAMF,EAAO,IAAIsY,EAAKpY,EAAI2X,GAC1BhX,KAAKnB,MAAMS,KAAKH,GAChBa,KAAKhB,QAAQK,GAAMF,EACda,KAAKoX,aAAaJ,KAAQhX,KAAKoX,aAAaJ,GAAS,IAC1DhX,KAAKoX,aAAaJ,GAAO1X,KAAKD,EALF,CAM9B,EAEA,YAAA6X,QAAA,SAAQ7X,EAAY6G,EAAcD,EAAY+Q,GAE5C,IADIhX,KAAKmX,yBAA6BpW,IAAP1B,KAAkBA,EAAKW,KAAKsX,aACvDtX,KAAKhB,QAAQkH,IAASlG,KAAKhB,QAAQiH,IAAOjG,KAAKhB,QAAQiH,GAAIgR,QAAQ5X,IAAvE,CAEA,IAAME,EAAO,IAAImY,EAAKrY,EAAI6G,EAAMD,EAAI+Q,GASpC,GARAhX,KAAKlB,MAAMQ,KAAKC,GAChBS,KAAKiX,QAAQ5X,GAAME,EAEnBS,KAAKhB,QAAQkH,GAAMgR,QAAQ3X,GAEtBS,KAAKqX,aAAaL,KAAQhX,KAAKqX,aAAaL,GAAS,IAC1DhX,KAAKqX,aAAaL,GAAO1X,KAAKC,IAEzBS,KAAKpB,SAAU,CAClB,IAAM+Y,EAAQ,IAAID,EAAKrY,EAAI4G,EAAIC,EAAM8Q,GACrChX,KAAKhB,QAAQiH,GAAIiR,QAAQS,GACzB3X,KAAKqX,aAAaL,GAAO1X,KAAKqY,E,CAbxB,CAeV,EACF,EAhEA,GCbA,aASE,WACEC,EACAC,EACAC,EACAC,EACAC,GAEAhY,KAAK4X,SAAWA,EAChB5X,KAAK6X,OAASA,EACd7X,KAAKiY,kBAAoB,CACvBC,WAAYJ,GAAiBf,EAC7BgB,UAAWA,GDpDgB,KCqD3BI,WAAYH,GAAejB,EAE/B,CAaF,OAXE,YAAAqB,QAAA,SAAQC,GACN,OACErY,KAAK4X,WAAaS,EAAMC,UACxBtY,KAAK6X,SAAWQ,EAAMR,QACtB7X,KAAKiY,oBAAsBI,EAAMJ,iBAErC,EAEA,YAAAM,WAAA,SAAWF,GACT,OAAQrY,KAAKoY,QAAQC,EACvB,EACF,EApCA,GAuCA,cAIE,aACErY,KAAKwY,OAAS,GACdxY,KAAKyY,YAAc,EACrB,CAmEF,OAjEE,YAAAL,QAAA,SAAQC,GACN,IAAMK,EAAU1Y,KAAKyY,YAAYpU,OAEjC,GAAIqU,IADYL,EAAMhU,OACG,OAAO,EAChC,IAAK,IAAIjF,EAAI,EAAGA,EAAIsZ,EAAStZ,IAC3B,GAAIY,KAAKyY,YAAYrZ,KAAOiZ,EAAMjZ,GAAI,OAAO,EAE/C,OAAO,CACT,EAEA,YAAAmZ,WAAA,SAAWF,GACT,OAAQrY,KAAKoY,QAAQC,EACvB,EAGA,YAAAM,SAAA,SAASf,EAAUC,EAAQC,EAAeC,EAAWC,GAInD,OAHAhY,KAAKyY,YAAYnZ,KACf,IAAIsZ,EAAQhB,EAAUC,EAAQC,EAAeC,EAAWC,IAEnDhY,KAAKyY,WACd,EAGA,YAAAI,QAAA,SAAQC,EAAmCla,QAAnC,IAAAka,IAAAA,GDrGqB,QCqGc,IAAAla,IAAAA,GAAA,GACzC,IAAMma,EAAQ,IAAIC,EAAMF,GAAS,EAAMla,GAUvC,OATAoB,KAAKyY,YAAYvZ,SAAQ,SAAC+Z,GACxB,IAAMC,EAAaD,EAAQrB,SACrBuB,EAAWF,EAAQpB,OACnB,EAAwCoB,EAAQhB,kBAA9CC,EAAU,aAAEH,EAAS,YAAEI,EAAU,aAErCD,IAAenB,GAAmBgC,EAAMvB,QAAQ0B,EAAYhB,GAC5DC,IAAepB,GAAmBgC,EAAMvB,QAAQ2B,EAAUhB,GAC1DD,IAAenB,GAAqBoB,IAAeD,GAAaa,EAAM7B,aAAQnW,EAAWmY,EAAYC,EAAUpB,EACrH,IACOgB,CACT,EAGA,YAAAK,YAAA,WACEpZ,KAAKwY,OAAS,GAGd,IAFA,IAAIa,OAAUtY,EAEL3B,EADUY,KAAKyY,YAAYpU,OACV,EAAGjF,GAAK,EAAGA,IAAK,CACxC,IAAM6Z,EAAUjZ,KAAKyY,YAAYrZ,GAC3Bka,EAAcL,EAAQrB,SACtB2B,EAAYN,EAAQpB,OAExByB,EAAcC,SACDxY,IAAZsY,GAAyBE,IAAcF,KAExCrZ,KAAKwY,OAAOlZ,KAAKF,GACjBia,EAAUC,E,CAGd,OAAOtZ,KAAKwY,MACd,EAEA,YAAAjB,WAAA,WACE,IAAMvY,EAAU,CAAC,EAKjB,OAJAgB,KAAKyY,YAAYvZ,SAAQ,SAAC+Z,GACnBja,EAAQia,EAAQrB,YAAW5Y,EAAQia,EAAQrB,WAAY,GACvD5Y,EAAQia,EAAQpB,UAAS7Y,EAAQia,EAAQpB,SAAU,EAC1D,IACO9Z,OAAOgI,KAAK/G,GAASqF,MAC9B,EACF,EA1EA,GA4EA,cAME,WAAYmV,GAKV,GAJAxZ,KAAKyZ,IAAM,CAAC,EACZzZ,KAAK0Z,UAAY,CAAC,EAClB1Z,KAAK2Z,UAAY,CAAC,EAClB3Z,KAAKlB,MAAQ,GACR0a,EAAL,CACA,KAAOA,GAAM,CACX,IAAMI,EAAIJ,EAAKja,KACfS,KAAKlB,MAAMQ,KAAKsa,GAChB5Z,KAAK0Z,UAAUE,EAAE1T,MAAQ,EACzBlG,KAAK0Z,UAAUE,EAAE3T,IAAM,EACvBjG,KAAK2Z,UAAUC,EAAEva,IAAM,EACvBma,EAAOA,EAAKK,O,CAGd7Z,KAAKlB,MAAQkB,KAAKlB,MAAMyC,SAVP,CAWnB,CASF,OAPE,YAAAuY,QAAA,SAAQ3a,GACN,OAAmC,IAA5Ba,KAAK0Z,UAAUva,EAAKE,GAC7B,EAEA,YAAA0a,QAAA,SAAQxa,GACN,OAAmC,IAA5BS,KAAK2Z,UAAUpa,EAAKF,GAC7B,EACF,EA/BA,GA6DA,cAeE,WAAY,G,IACV2a,EAAM,SACN,IAAAC,WAAAA,OAAU,IAAG,IAAC,EACd,IAAAC,WAAAA,OAAU,IAAG,IAAC,EACd,IAAAC,WAAAA,OAAU,IAAG,IAAC,EACd,IAAArF,IAAAA,OAAG,IAAG,KAAE,EACR,IAAAlW,SAAAA,OAAQ,IAAG,GAAK,EAChB,IAAAwb,QAAAA,OAAO,IAAG,GAAK,EAGfpa,KAAKga,OAASA,EACdha,KAAKqa,QAAU,IAAIC,GACnBta,KAAKua,QAAU,EACfva,KAAKwa,uBAAyB,GAC9Bxa,KAAKya,kBAAoB,GACzBza,KAAKia,WAAaA,EAClBja,KAAK8U,IAAMA,EACX9U,KAAKpB,SAAWA,EAChBoB,KAAKsX,QAAU,EAEftX,KAAKma,WAAaA,EAClBna,KAAKka,WAAaA,EAClBla,KAAKoa,QAAUA,EACXpa,KAAKma,WAAana,KAAKka,aAAYla,KAAKma,WAAana,KAAKka,YAC9Dla,KAAK0a,SAAW,EAClB,CAilBF,OA9kBE,YAAAC,qBAAA,SAAqB5B,EAAcnB,GAAnC,WACQgD,EAAS,GACT5b,EAAU+Z,EAAM/Z,QAMtB,OALA4Y,EAAS9Y,MAAMI,SAAQ,SAACK,IAClB,EAAKX,UAAYgZ,EAASZ,OAAShY,EAAQO,EAAK0G,IAAI+Q,QACtD4D,EAAOtb,KAAKC,EAChB,IAEOqb,CACT,EAEA,YAAAC,iBAAA,SACE9B,EACA+B,EACAC,EACAC,GAEA,IAAKhb,KAAKpB,UAAYkc,IAAUC,EAAO,OAAO,KAK9C,IAJA,IAAM/b,EAAU+Z,EAAM/Z,QAEhBic,EADUjc,EAAQ+b,EAAM9U,IACDnH,MACvBoc,EAAaD,EAAa5W,OACvBjF,EAAI,EAAGA,EAAI8b,EAAY9b,IAAK,CACnC,IAAMG,EAAO0b,EAAa7b,GAC1B,IAAI4b,EAAQjB,QAAQxa,IAASA,EAAK0G,KAAO6U,EAAM5U,KAC/C,GAAKlG,KAAKpB,UASR,GACEI,EAAQ8b,EAAM5U,MAAM8Q,MAAQhY,EAAQ+b,EAAM9U,IAAI+Q,OAC7ChY,EAAQ8b,EAAM5U,MAAM8Q,QAAUhY,EAAQ+b,EAAM9U,IAAI+Q,OAC/C8D,EAAM9D,OAASzX,EAAKyX,MAEtB,OAAOzX,OAbT,GACEub,EAAM9D,MAAQzX,EAAKyX,OAClB8D,EAAM9D,QAAUzX,EAAKyX,OACpBhY,EAAQ8b,EAAM7U,IAAI+Q,OAAShY,EAAQ+b,EAAM9U,IAAI+Q,MAE/C,OAAOzX,C,CAYb,OAAO,IACT,EAEA,YAAA4b,qBAAA,SACEpC,EACAqC,EACAC,EACAL,GAMA,IAJA,IAAMJ,EAAS,GACTU,EAAoBF,EAAcnV,GAClCnH,EAAQia,EAAM/Z,QAAQsc,GAAmBxc,MACzCoc,EAAapc,EAAMuF,OAChBjF,EAAI,EAAGA,EAAI8b,EAAY9b,IAAK,CACnC,IAAMG,EAAOT,EAAMM,GACbyY,EAASkB,EAAM/Z,QAAQO,EAAK0G,IAC9BoV,GAAgBxD,EAAOb,QAAUgE,EAAQlB,QAAQjC,IACnD+C,EAAOtb,KAAKC,E,CAGhB,OAAOqb,CACT,EAEA,YAAAW,uBAAA,SACExC,EACAqC,EACAC,EACAL,GAQA,IANA,IAAMJ,EAAS,GACT5b,EAAU+Z,EAAM/Z,QAChBgZ,EAAchZ,EAAQoc,EAAcnV,IAAI+Q,MAExClY,EADWE,EAAQoc,EAAclV,MAChBpH,MACjBoc,EAAapc,EAAMuF,OAChBjF,EAAI,EAAGA,EAAI8b,EAAY9b,IAAK,CACnC,IAAMG,EAAOT,EAAMM,GACboc,EAAiBxc,EAAQO,EAAK0G,IAAI+Q,MAEtCoE,EAAcnV,KAAO1G,EAAK0G,IAC1BoV,EAAeG,GACfR,EAAQlB,QAAQ9a,EAAQO,EAAK0G,OAK7BmV,EAAcpE,MAAQzX,EAAKyX,OAC1BoE,EAAcpE,QAAUzX,EAAKyX,OAASgB,GAAewD,IAEtDZ,EAAOtb,KAAKC,E,CAGhB,OAAOqb,CACT,EAEA,YAAAa,WAAA,SAAWC,GACT,IAAMC,EAAW,CAAC,EAIlB,OAHAD,EAAUxc,SAAQ,SAAC0c,GACZD,EAASC,EAAI9C,WAAU6C,EAASC,EAAI9C,UAAW,EACtD,IACO/a,OAAOgI,KAAK4V,GAAUtX,MAC/B,EAEA,YAAAwX,aAAA,SACE1d,GAMA,IAAI2d,OAAW/a,EA0Bf,OAzBAhD,OAAOgI,KAAK5H,GAAKe,SAAQ,SAAC+Y,GAClB,MAAwC9Z,EAAI8Z,GAA1CC,EAAU,aAAEH,EAAS,YAAEI,EAAU,aACpC2D,GASH5D,EAAa4D,EAAS5D,YACrBA,IAAe4D,EAAS5D,YACvBH,EAAY+D,EAAS/D,WACtBG,IAAe4D,EAAS5D,YACvBH,IAAc+D,EAAS/D,WACvBI,EAAa2D,EAAS3D,cAExB2D,EAAW,CACT5D,WAAU,EACVH,UAAS,EACTI,WAAU,IAlBZ2D,EAAW,CACT5D,WAAU,EACVH,UAAS,EACTI,WAAU,EAkBhB,IACO2D,CACT,EAEA,YAAAC,MAAA,sBACQ1B,EAAUra,KAAKqa,QAErB,GADIra,KAAKoa,SAAS1L,QAAQsN,IAAI,iBAAkB3B,GACb,IAA/BA,EAAQ5B,YAAYpU,OAAc,OAAO,EAC7C,IAAMzF,EAAWoB,KAAKpB,SAChBma,EAAQsB,EAAQxB,SD5YK,EC4YoBja,GACzCI,EAAU+Z,EAAM/Z,QAChBid,EAAa,IAAI3B,GACjB4B,EAAa,CAAC,EACpBnD,EAAMla,MAAMK,SAAQ,SAACC,GACE,EAAKwb,qBAAqB5B,EAAO5Z,GACzCD,SAAQ,SAACK,GACpB,IAAI4c,EAAYnd,EAAQO,EAAK0G,IACvBgS,EAAoB,UAAG9Y,EAAK6X,MAAK,YAAIzX,EAAKyX,MAAK,YAAImF,EAAUnF,OAC9DkF,EAAKjE,KACRiE,EAAKjE,GAAqB,CACxByD,UAAW,GACXxD,WAAY/Y,EAAK6X,MACjBe,UAAWxY,EAAKyX,MAChBmB,WAAYgE,EAAUnF,QAE1B,IAAMwC,EAAa,CACjBV,QAASC,EAAM1Z,GACfE,KAAI,EACJsa,QAAS,MAEXqC,EAAKjE,GAAmByD,UAAUpc,KAAKka,EACzC,GACF,IAGA,IAAIsC,EAAW9b,KAAK6b,aAAaK,GACjC,GAAKJ,EAAL,CACAG,EAAWxD,YAAYnZ,KACrB,IAAIsZ,EACF,EACA,EACAkD,EAAS5D,WACT4D,EAAS/D,UACT+D,EAAS3D,aAKb,IAAMiE,EAAe,SAACV,GAWpB,IATA,IAAMlD,EAASyD,EAAW7C,cACpBiC,EACJY,EAAWxD,YAAY,GAAGR,kBAAkBC,WACxCmE,EAASJ,EAAWxD,YAAYD,EAAO,IAAIX,OAE3CyE,EAAqB,CAAC,EACxBC,GAAO,EACTC,EAAQ,EACNvR,EAAMrM,GAAY,EAAI,E,WACjBQ,GACP,GAAImd,E,cAEJb,EAAUxc,SAAQ,SAACud,GACjB,IAAMzB,EAAU,IAAI0B,GAAQD,GACtBE,EAAe,EAAK9B,iBACxB9B,EACAiC,EAAQlc,MAAM0Z,EAAOpZ,IACrB4b,EAAQlc,MAAM0Z,EAAO,IACrBwC,GAEE2B,IAEGL,EAAaK,EAAa3F,SAC7BsF,EAAaK,EAAa3F,OAAS,CACjC0E,UAAW,GACX3D,UAAW4E,EAAa3F,QAG5BsF,EAAaK,EAAa3F,OAAO0E,UAAUpc,KAAK,CAC9CwZ,QAASC,EAAM1Z,GACfE,KAAM+c,EACNzC,QAAS4C,IAEXD,EAAQP,EAAWxD,YAAYD,EAAOpZ,IAAIwY,SAC1C2E,GAAO,EAEX,G,EA3BOnd,EAAIoZ,EAAOnU,OAAS,EAAGjF,EAAI6L,G,YAA3B7L,GAAgCA,KA8BzC,GAAImd,EAAM,CACR,IAAMK,EAAuB,EAAKf,aAAaS,GAC/CL,EAAWxD,YAAYnZ,KACrB,IAAIsZ,EACFyD,EACAG,EACAzF,EACA6F,EAAqB7E,UACrBhB,IAGJ,IAAM,EAAMkF,EAAWxD,YAAYpU,OAAS,EAC5C,OAAI,EAAKgW,QAAQ5B,YAAY,KAASwD,EAAWxD,YAAY,IAEtD2D,EACLE,EAAaM,EAAqB7E,WAAW2D,U,CAGjD,IAAMmB,EAAoB,CAAC,EAC3BN,GAAO,EACP,IAAIO,EAAU,EACdpB,EAAUxc,SAAQ,SAACud,GACjB,IAAMzB,EAAU,IAAI0B,GAAQD,GACtBM,EAAmB,EAAK5B,qBAC5BpC,EACAiC,EAAQlc,MAAM0Z,EAAO,IACrB6C,EACAL,GAEE+B,EAAiB1Y,OAAS,IAC5BkY,GAAO,EACPO,EAAUT,EACVU,EAAiB7d,SAAQ,SAACK,GACxB,IAAM1B,EAAM,UAAG0B,EAAKyX,MAAK,YAAIhY,EAAQO,EAAK0G,IAAI+Q,OACzC6F,EAAYhf,KACfgf,EAAYhf,GAAO,CACjB6d,UAAW,GACX3D,UAAWxY,EAAKyX,MAChBmB,WAAYnZ,EAAQO,EAAK0G,IAAI+Q,QAEjC6F,EAAYhf,GAAK6d,UAAUpc,KAAK,CAC9BwZ,QAASC,EAAM1Z,GACfE,KAAI,EACJsa,QAAS4C,GAEb,IAEJ,IAEA,IAAMO,EAAaxE,EAAOnU,O,WACjBjF,GACP,GAAImd,E,cACJ,IAAM7d,EAAQ8Z,EAAOpZ,GACrBsc,EAAUxc,SAAQ,SAACud,GACjB,IAAMzB,EAAU,IAAI0B,GAAQD,GACtBQ,EAAqB,EAAK1B,uBAC9BxC,EACAiC,EAAQlc,MAAMJ,GACd2c,EACAL,GAEEiC,EAAmB5Y,OAAS,IAC9BkY,GAAO,EACPO,EAAUb,EAAWxD,YAAY/Z,GAAOkZ,SACxCqF,EAAmB/d,SAAQ,SAACK,GAC1B,IAAM1B,EAAM,UAAG0B,EAAKyX,MAAK,YAAIhY,EAAQO,EAAK0G,IAAI+Q,OACzC6F,EAAYhf,KACfgf,EAAYhf,GAAO,CACjB6d,UAAW,GACX3D,UAAWxY,EAAKyX,MAChBmB,WAAYnZ,EAAQO,EAAK0G,IAAI+Q,QAEjC6F,EAAYhf,GAAK6d,UAAUpc,KAAK,CAC9BwZ,QAASC,EAAM1Z,GACfE,KAAI,EACJsa,QAAS4C,GAEb,IAEJ,G,EA7BF,IAASrd,EAAI,EAAGA,EAAI4d,G,YAAX5d,GAAuBA,KAgChC,IAAKmd,EAAM,OAAO,EAElB,IAAMW,EAA0B,EAAKrB,aAAagB,GAClDZ,EAAWxD,YAAYnZ,KACrB,IAAIsZ,EACFkE,EACAT,EAAS,EACTtF,EACAmG,EAAwBnF,UACxBmF,EAAwB/E,aAG5B,IAAMpM,EAAMkQ,EAAWxD,YAAYpU,OAAS,EAC5C,OAAIgW,EAAQ5B,YAAY1M,KAASkQ,EAAWxD,YAAY1M,IAEjDqQ,EACLS,EACE,UAAGK,EAAwBnF,UAAS,YAAImF,EAAwB/E,aAChEuD,UAEN,EACM7d,EAAM,UAAGie,EAAS5D,WAAU,YAAI4D,EAAS/D,UAAS,YAAI+D,EAAS3D,YACrE,OAAOiE,EAAaF,EAAKre,GAAK6d,UA7JT,CA8JvB,EAEA,YAAAyB,OAAA,WACE,KAAInd,KAAKqa,QAAQ9C,aAAevX,KAAKka,YAArC,CACAla,KAAKsX,UACL,IAAMyB,EAAQ/Y,KAAKqa,QAAQxB,QAAQ7Y,KAAKsX,QAAStX,KAAKpB,UACtDoB,KAAKya,kBAAkBnb,KAAK,EAAMyZ,GAHqB,CAIzD,EAEA,YAAAqE,eAAA,SAAe1B,GAAf,WAEE,KADgB1b,KAAKyb,WAAWC,GAClB1b,KAAKia,aACdja,KAAK+b,QAAV,CACA/b,KAAKmd,SAEL,IAAMtT,EAAU7J,KAAKqa,QAAQ9C,aACvBiB,EAASxY,KAAKqa,QAAQjB,cACtBiD,EAASrc,KAAKqa,QAAQ5B,YAAYD,EAAO,IAAIX,OAC7CwD,EAAerb,KAAKqa,QAAQ5B,YAAY,GAAGR,kBAC9CC,WAEG2E,EAAoB,CAAC,EACrBP,EAAqB,CAAC,EAE5BZ,EAAUxc,SAAQ,SAACud,GAKjB,IAJA,IAAM1D,EAAQ,EAAKiB,OAAOyC,EAAE3D,SACtB9Z,EAAU+Z,EAAM/Z,QAChBgc,EAAU,IAAI0B,GAAQD,GAEnBrd,EAAIoZ,EAAOnU,OAAS,EAAGjF,GAAK,EAAGA,IAAK,CAC3C,IAAMud,EAAe,EAAK9B,iBACxB9B,EACAiC,EAAQlc,MAAM0Z,EAAOpZ,IACrB4b,EAAQlc,MAAM0Z,EAAO,IACrBwC,GAEF,GAAI2B,EAAc,CAChB,IAAM9e,EAAM,UAAG,EAAKwc,QAAQ5B,YAAYD,EAAOpZ,IAAIwY,SAAQ,YACzD+E,EAAa3F,OAEVsF,EAAaze,KAChBye,EAAaze,GAAO,CAClB6d,UAAW,GACXvC,SAAU,EAAKkB,QAAQ5B,YAAYD,EAAOpZ,IAAIwY,SAC9CG,UAAW4E,EAAa3F,QAE5BsF,EAAaze,GAAK6d,UAAUpc,KAAK,CAC/BwZ,QAAS2D,EAAE3D,QACXvZ,KAAMod,EACN9C,QAAS4C,G,EAMf,KAAI5S,GAAW,EAAKsQ,YAApB,CACyB,EAAKgB,qBAC5BpC,EACAiC,EAAQlc,MAAM0Z,EAAO,IACrB6C,EACAL,GAEe9b,SAAQ,SAACK,GACxB,IAAM1B,EAAM,UAAGwe,EAAM,YAAI9c,EAAKyX,MAAK,YAAIhY,EAAQO,EAAK0G,IAAI+Q,OACnD6F,EAAYhf,KACfgf,EAAYhf,GAAO,CACjB6d,UAAW,GACXxC,WAAYmD,EACZtE,UAAWxY,EAAKyX,MAChBmB,WAAYnZ,EAAQO,EAAK0G,IAAI+Q,QAEjC6F,EAAYhf,GAAK6d,UAAUpc,KAAK,CAC9BwZ,QAAS2D,EAAE3D,QACXvZ,KAAI,EACJsa,QAAS4C,GAEb,I,eAGSrd,GACoB,EAAKmc,uBAC9BxC,EACAiC,EAAQlc,MAAM0Z,EAAOpZ,IACrBic,EACAL,GAEiB9b,SAAQ,SAACK,GAC1B,IAAM1B,EAAM,UAAG,EAAKwc,QAAQ5B,YAAYD,EAAOpZ,IAAIwY,SAAQ,YACzDrY,EAAKyX,MAAK,YACRhY,EAAQO,EAAK0G,IAAI+Q,OAChB6F,EAAYhf,KACfgf,EAAYhf,GAAO,CACjB6d,UAAW,GACXxC,WAAY,EAAKmB,QAAQ5B,YAAYD,EAAOpZ,IAAIwY,SAChDG,UAAWxY,EAAKyX,MAChBmB,WAAYnZ,EAAQO,EAAK0G,IAAI+Q,QAEjC6F,EAAYhf,GAAK6d,UAAUpc,KAAK,CAC9BwZ,QAAS2D,EAAE3D,QACXvZ,KAAI,EACJsa,QAAS4C,GAEb,G,EAvBF,IAASrd,EAAI,EAAGA,EAAIoZ,EAAOnU,OAAQjF,I,EAA1BA,EAxB6B,CAiDxC,IAGArB,OAAOgI,KAAKuW,GAAcpd,SAAQ,SAACrB,GAC3B,MAA0Bye,EAAaze,GAArCsb,EAAQ,WAAEpB,EAAS,YAC3B,EAAKsC,QAAQ5B,YAAYnZ,KACvB,IAAIsZ,EAAQyD,EAAQlD,EAAU,KAAMpB,EAAW,OAEjD,EAAKqF,eAAed,EAAaze,GAAK6d,WACtC,EAAKrB,QAAQ5B,YAAYlU,KAC3B,IAGAxG,OAAOgI,KAAK8W,GAAa3d,SAAQ,SAACrB,GAC1B,MAAwCgf,EAAYhf,GAAlDqb,EAAU,aAAEnB,EAAS,YAAEI,EAAU,aACzC,EAAKkC,QAAQ5B,YAAYnZ,KACvB,IAAIsZ,EACFM,EACAmD,EAAS,EACTtF,EACAgB,EACAI,IAGJ,EAAKiF,eAAeP,EAAYhf,GAAK6d,WACrC,EAAKrB,QAAQ5B,YAAYlU,KAC3B,GAtHyB,CAuH3B,EAEA,YAAA8Y,+BAAA,WACE,IAAMrD,EAASha,KAAKga,OACdpb,EAAWoB,KAAKpB,SAChBqb,EAAaja,KAAKia,WAClBO,EAAyBxa,KAAKwa,uBAChC8C,EAAmB,CAAC,EACtBC,EAAsB,CAAC,EAEnBC,EAAmB,CAAC,EAEpBC,EAA2B,CAAC,EA6DlC,OA5DA1f,OAAOgI,KAAKiU,GAAQ9a,SAAQ,SAACrB,GAE3B,IAAMkb,EAAQiB,EAAOnc,GACfmB,EAAU+Z,EAAM/Z,QAEtB+Z,EAAMla,MAAMK,SAAQ,SAACC,EAAMC,GAEzB,IAAMse,EAAYve,EAAK6X,MACjB2G,EAAe,UAAG9f,EAAG,YAAI6f,GAC/B,IAAKF,EAAiBG,GAAe,CACnC,IAAIrG,EAAUgG,EAAiBI,IAAc,EAC7CpG,IACAgG,EAAiBI,GAAapG,C,CAEhCkG,EAAiBG,GAAgB,CAC/BC,SAAU/f,EACVmZ,MAAO0G,GAGTve,EAAKL,MAAMI,SAAQ,SAACK,GAClB,IAAI2Y,EAAawF,EACbvF,EAAanZ,EAAQO,EAAK0G,IAAI+Q,MAClC,IAAKpY,GAAYsZ,EAAaC,EAAY,CACxC,IAAM/C,EAAM+C,EACZA,EAAaD,EACbA,EAAa9C,C,CAEf,IAAM2C,EAAYxY,EAAKyX,MAEjB6G,EAAuB,UAAGhgB,EAAG,YAAIqa,EAAU,YAAIH,EAAS,YAAII,GAC5D2F,EAAkB,UAAG5F,EAAU,YAAIH,EAAS,YAAII,GAEtD,IAAKoF,EAAoBO,GAAkB,CACzC,IAAIxG,EAAUiG,EAAoBO,IAAoB,EACtDxG,IACAiG,EAAoBO,GAAmBxG,C,CAEzCmG,EAAyBI,GAAwB,CAC/C/E,QAASjb,EACTqa,WAAU,EACVH,UAAS,EACTI,WAAU,EAEd,GACF,GACF,IAGApa,OAAOgI,KAAKuX,GAAkBpe,SAAQ,SAAC8X,GAErC,KADcsG,EAAiBtG,GACnBiD,GAAZ,CACA,IAAM8D,EAAI,CAAElf,MAAO,GAAIC,MAAO,IAC9Bif,EAAElf,MAAMS,KAAK,CACXD,GAAI,IACJ2X,MAAK,IAEPwD,EAAuBlb,KAAKye,EANE,CAQhC,IAEOvD,CACT,EAEA,YAAAwD,IAAA,sBAIE,GAFAhe,KAAKwa,uBAAyBxa,KAAKqd,mCAE/Brd,KAAKma,WAAa,GAAtB,CAEA,IAAMH,EAASha,KAAKga,OAIdkC,GAHWlc,KAAKpB,SAGH,CAAC,GACpBb,OAAOgI,KAAKiU,GAAQ9a,SAAQ,SAAC4Z,GAC3B,IAAMC,EAAQiB,EAAOlB,GACf9Z,EAAU+Z,EAAM/Z,QAEtB+Z,EAAMla,MAAMK,SAAQ,SAACC,GACM,EAAKwb,qBAAqB5B,EAAO5Z,GAEzCD,SAAQ,SAACK,GACxB,IAAIsY,EAAS7Y,EAAQO,EAAK0G,IACpBgS,EAAoB,UAAG9Y,EAAK6X,MAAK,YAAIzX,EAAKyX,MAAK,YAAIa,EAAOb,OAC3DkF,EAAKjE,KACRiE,EAAKjE,GAAqB,CACxByD,UAAW,GACXxD,WAAY/Y,EAAK6X,MACjBe,UAAWxY,EAAKyX,MAChBmB,WAAYN,EAAOb,QAEvB,IAAMwC,EAAa,CACjBV,QAAO,EACPvZ,KAAI,EACJsa,QAAS,MAEXqC,EAAKjE,GAAmByD,UAAUpc,KAAKka,EACzC,GACF,GACF,IAGAzb,OAAOgI,KAAKmW,GAAMhd,SAAQ,SAAC+Y,GACnB,MAAmDiE,EACvDjE,GADMyD,EAAS,YAAExD,EAAU,aAAEH,EAAS,YAAEI,EAAU,aAIpD,EAAKkC,QAAQ5B,YAAYnZ,KACvB,IAAIsZ,EAAQ,EAAG,EAAGV,EAAYH,EAAWI,IAE3C,EAAKiF,eAAe1B,GACpB,EAAKrB,QAAQ5B,YAAYlU,KAC3B,GA7C+B,CA8CjC,EACF,EAznBA,GA0rBM0Z,GAAqB,UC/1B3B,IAAMC,GAAqB,SACzBvf,EACAwf,EACAC,EACAtW,QADA,IAAAsW,IAAAA,EAAA,gBACA,IAAAtW,IAAAA,EAAA,GAEA,IAAMuW,EAAwB,GACxBxf,EAAQF,EAAUE,MAIxB,OAHAsf,EAAIjf,SAAQ,SAACgN,EAAe9M,GAC1Bif,EAAM/e,KAAKgf,GAAkBzf,EAAOqN,EAAK9M,EAAGgf,EAAetW,GAC7D,IACOuW,CACT,EAEMC,GAAoB,SAACzf,EAAOqN,EAAK9M,EAAGgf,EAAetW,GACvD,IAAMyW,EAAe,CAACnf,GAChB8E,EAAY,GACZsa,EAAgB,CAAC,EAiBvB,OAhBAtS,EAAIhN,SAAQ,SAACkK,EAAGsC,GACd,GAAItC,GAAKtB,GAAK1I,IAAMsM,EAAG,CACrB6S,EAAajf,KAAKoM,GAClBxH,EAAU5E,KAAKT,EAAM6M,IACrB,IAAMsL,EAAQnY,EAAM6M,GAAG0S,GAClBI,EAAcxH,IAEjBwH,EAAcxH,GAAOtJ,QACrB8Q,EAAcxH,GAAOyH,MAAMnf,KAAK8J,IAHPoV,EAAcxH,GAAS,CAAEtJ,MAAO,EAAG+Q,MAAO,CAACrV,G,CAM1E,IAEArL,OAAOgI,KAAKyY,GAAetf,SAAQ,SAAA8X,GACjCwH,EAAcxH,GAAOyH,MAAQD,EAAcxH,GAAOyH,MAAM3P,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,GACzE,IACO,CACL4e,QAAStf,EACT6C,OAAQpD,EAAMO,GAAGC,GACjBsf,SAAUJ,EACVra,UAAS,EACT0a,YAAaL,EAAala,OAAS,EACnCwa,kBAAmBL,EAEvB,EAsEMM,GAAmC,SACvCC,EACAC,EACArgB,EACAsgB,GAEA,IAAMpgB,EAAQF,EAAUE,MAyBxB,OAxBKogB,IAAuBA,EAAwB,CAAC,GACrDlhB,OAAOgI,KAAKgZ,GAAa7f,SAAQ,SAAArB,G,QAC/B,IAAIohB,IAAyBA,EAAsBphB,GAAnD,CACAohB,EAAsBphB,GAAO,CAAEgB,MAAO,GAAIC,MAAO,IACjD,IAAMogB,EAAOH,EAAYlhB,GACnBshB,EAA4C,QAAzB,EAAAH,EAAcE,EAAKlU,cAAM,eAAE2T,SAC9CS,EAAwC,QAAvB,EAAAJ,EAAcE,EAAKjU,YAAI,eAAE0T,SAChD,GAAKQ,GAAqBC,EAA1B,CACA,IAAMC,EAAS,IAAI1N,IAAIyN,GACjBE,EAAYH,EAAiB/c,QAAO,SAAAmd,GAAK,OAAAF,EAAO/X,IAAIiY,EAAX,IAC/C,GAAKD,GAAcA,EAAUjb,OAA7B,CAGA,IAFA,IAAMmb,EAAiB,CAAC,EAClBC,EAAkBH,EAAUjb,OACzBjF,EAAI,EAAGA,EAAIqgB,EAAiBrgB,IAAK,CACxC,IAAMD,EAAON,EAAMygB,EAAUlgB,IAC7B6f,EAAsBphB,GAAKgB,MAAMS,KAAKH,GACtCqgB,EAAergB,EAAKE,KAAM,C,CAG5BV,EAAUG,MAAMI,SAAQ,SAAAK,GAClBigB,EAAejgB,EAAKC,SAAWggB,EAAejgB,EAAKE,SACrDwf,EAAsBphB,GAAKiB,MAAMQ,KAAKC,EAC1C,GAZ2C,CAHK,CALe,CAqBjE,IACO0f,CACT,EASMS,GAAkB,SAAC3G,EAAO4G,EAAWvB,EAAewB,G,QAClD5gB,EAAU,CAAC,EACjB+Z,EAAMla,MAAMK,SAAQ,SAAAC,GAClBH,EAAQG,EAAKE,IAAMF,CACrB,IACA,IAAIuO,EAAQ,EACZ,QAAqB,QAAhB,EAAAiS,aAAS,EAATA,EAAW7gB,aAAK,eAAEuF,UAA0B,QAAhB,EAAAsb,aAAS,EAATA,EAAW9gB,aAAK,eAAEwF,QAAS,EAAU,GACtE0U,EAAMja,MAAMI,SAAQ,SAAA0a,GAClB,IAAMiG,EAAc7gB,EAAQ4a,EAAEpa,QAAQ4e,GAChC0B,EAAc9gB,EAAQ4a,EAAEna,QAAQ2e,GAChC2B,EAAgBJ,aAAS,EAATA,EAAW9gB,MAAM,GAAGuf,GACpC4B,EAAgBL,aAAS,EAATA,EAAW9gB,MAAM,GAAGuf,GACpC6B,EAAeN,aAAS,EAATA,EAAW7gB,MAAM,GAAG8gB,GAErChG,EAAEgG,KAAmBK,IAEtBJ,IAAgBE,GAAiBD,IAAgBE,GACjDH,IAAgBG,GAAiBF,IAAgBC,IAElDrS,GAEJ,IACOA,EACT,EA6EMwS,GAAc,SAACrhB,EAAOuf,GAC1B,IAAMpf,EAAmB,CAAC,EACxBoY,EAAyB,CAAC,EAO5B,OANAvY,EAAMK,SAAQ,SAACC,EAAMC,GACnBJ,EAAQG,EAAKE,IAAM,CAAE0M,IAAK3M,EAAGD,KAAI,EAAE6F,OAAQ,EAAGE,SAAU,EAAGC,UAAW,GACtE,IAAM6R,EAAQ7X,EAAKif,GACdhH,EAAaJ,KAAQI,EAAaJ,GAAS,IAChDI,EAAaJ,GAAO1X,KAAKH,EAC3B,IACO,CAAEH,QAAO,EAAEoY,aAAY,EAChC,EAEM+I,GAAc,SAClBrhB,EACA8gB,EACA5gB,GAEA,IAAMiY,EAAU,CAAC,EACfI,EAAe,CAAC,EAkBlB,OAjBAvY,EAAMI,SAAQ,SAACK,EAAMH,GACnB6X,EAAQ,UAAG3U,IAAc,CAAEyJ,IAAK3M,EAAGG,KAAI,GACvC,IAAMyX,EAAQzX,EAAKqgB,GACdvI,EAAaL,KAAQK,EAAaL,GAAS,IAChDK,EAAaL,GAAO1X,KAAKC,GAEzB,IAAM6gB,EAAaphB,EAAQO,EAAKC,QAC5B4gB,IACFA,EAAWpb,SACXob,EAAWjb,aAEb,IAAMf,EAAapF,EAAQO,EAAKE,QAC5B2E,IACFA,EAAWY,SACXZ,EAAWc,WAEf,IACO,CAAE+R,QAAO,EAAEI,aAAY,EAChC,EAQMgJ,GAAY,SAACxhB,EAAOsf,EAAKvf,GAC7B,IAAMyF,EAAS8Z,EAAI9Z,OACb3C,EAAM,CAAC,EAYb,OAXAyc,EAAIjf,SAAQ,SAACgN,EAAK9M,GAGhB,IAFA,IAAM4L,EAAQpM,EAAW,EAAIQ,EAAI,EAC3BkhB,EAAMzhB,EAAMO,GAAGC,GACZqM,EAAIV,EAAOU,EAAIrH,EAAQqH,IAC9B,GAAItM,IAAMsM,EAAV,CACA,IAAM6U,EAAM1hB,EAAM6M,GAAGrM,GACfoM,EAAOS,EAAIR,GACjBhK,EAAI,UAAG4e,EAAG,YAAIC,IAAS9U,EAClB7M,IAAU8C,EAAI,UAAG6e,EAAG,YAAID,IAAS7U,EAJjB,CAMzB,IACO/J,CACT,EAQM8e,GAAa,SACjBzH,EACA0H,EACAC,EACA1hB,EACA2hB,EACAC,EACAjB,EACAvB,EACAwB,EACAiB,EACAC,G,MAEMjjB,EAAM,UAAG4iB,EAAMphB,GAAE,YAAIqhB,EAAMrhB,IACjC,GAAIwhB,GAAgBA,EAAahjB,GAAM,OAAOgjB,EAAahjB,GAC3D,IAAIkjB,EAAoBD,EAA0BA,EAAwBjjB,QAAOkD,EAEjF,IAAKggB,EAAmB,CACtB,IAAMC,IAAO,MACVnjB,GAAM,CACLmN,MAAOhM,EAAQyhB,EAAMphB,IAAI0M,IACzBd,IAAKjM,EAAQ0hB,EAAMrhB,IAAI0M,IACvBwK,SAAUoK,G,GAUdI,GANAD,EAA0BhC,GACxBkC,EACAJ,EACA7H,EACA+H,IAE0CjjB,E,CAG9C,OAAO6hB,GAAgBqB,EAAmBpB,EAAWvB,EAAewB,EACtE,EAKMqB,GAAiC,SAACC,EAA8BC,EAAeC,EAAgBC,G,UAC/FC,EAAuE,QAA3C,EAAAJ,EAA6BC,UAAc,eAAEnc,OACzEuc,EAAyE,QAA3C,EAAAL,EAA6BC,UAAc,eAAEjc,SAC3Esc,EAA0E,QAA3C,EAAAN,EAA6BC,UAAc,eAAEhc,UAwBhF,YAtBoDpE,IAAhDmgB,EAA6BC,KAC/BG,EAA4B1X,IAC5B2X,EAA8B3X,IAC9B4X,EAA+B5X,IAC/ByX,EAAoBF,GAAejiB,SAAQ,SAAAuiB,GACzC,IAAMC,EAAoBN,EAAeK,EAAqBpiB,IAAI2F,OAC9Dsc,EAA4BI,IAC9BJ,EAA4BI,GAC9B,IAAMC,EAAsBP,EAAeK,EAAqBpiB,IAAI6F,SAChEqc,EAA8BI,IAChCJ,EAA8BI,GAChC,IAAMC,EAAuBR,EAAeK,EAAqBpiB,IAAI8F,UACjEqc,EAA+BI,IACjCJ,EAA+BI,EACnC,IACAV,EAA6BC,GAAiB,CAC5Cnc,OAAQsc,EACRpc,SAAUqc,EACVpc,UAAWqc,IAIR,CACLF,0BAAyB,EACzBC,4BAA2B,EAC3BC,6BAA4B,EAEhC,EAiwBA,SArvBc,SACZ7iB,EACAkjB,EACAjjB,EACAkJ,EACAzD,EACA+Z,EACAwB,G,MAEA,QANA,IAAAhhB,IAAAA,GAAA,QAGA,IAAAwf,IAAAA,EAAA,gBACA,IAAAwB,IAAAA,EAAA,WAEKjhB,GAAcA,EAAUE,MAA7B,CASA,IAAMgL,EAAUlL,EAAUE,MAAMwF,OAChC,GAAKwF,EAAL,CAEA,IAAMsU,EAAM,EAAcxf,EAAWC,GAI/BkjB,EAAa,EAAcD,EAASjjB,GAIpCmjB,EAAS1B,GAAU1hB,EAAUE,MAAOsf,EAAKvf,GAIzCojB,EAAgB3B,GAAUwB,EAAQhjB,MAAOijB,EAAYljB,GAIrD,EAA4BshB,GAAYvhB,EAAUE,MAAOuf,GAAvDpf,EAAO,UAAEoY,EAAY,eACvB,EAAiE8I,GACrE2B,EAAQhjB,MACRuf,GAFegD,EAAc,UAAgBC,EAAmB,eAMlElB,GAAYxhB,EAAUG,MAAO8gB,EAAe5gB,GAEpC,IAAcijB,EAAwB9B,GAC5C0B,EAAQ/iB,MACR8gB,EACAwB,GACD,aAGGc,EAAmB,GACvBJ,SAAAA,EAAY5iB,SAAQ,SAAAgN,GAClBgW,EAAmBA,EAAiBzb,OAAOyF,EAC7C,IACK7H,IAAQA,EAAS5B,KAAKsM,IAAG,MAARtM,KAAI,OAAQyf,GAAkB,GAAF,CAAE,IAAC,KAChDpa,IAAGA,EAAIzD,GAMZ,IAAMuc,EAAiB1C,GAAmBvf,EAAWwf,EAAKC,EAAetW,GACnEqa,EAAwBjE,GAAmB2D,EAASC,EAAY1D,EAAetW,GAY/Esa,EAhbsB,SAC5Bta,EACA+B,EACAwY,EACAzB,EACAzC,GAGA,IAAImE,EAAsB7f,KAAK8f,KAAKF,EAAiBxY,GAC/CkV,EAAc,CAAC,EACjByD,EAAqB,EAwCzB,OArCA5B,EAAe1hB,SAAQ,SAACujB,EAAMrjB,GAM5B,IAJA,IAAIsjB,EAAoB,EACpBC,EAAiB,EACfze,EAAYue,EAAK9D,SACjBC,EAAc6D,EAAK7D,YAAc,EAChC8D,EAAoBJ,GAAqB,CAK9C,IAHA,IAAIM,EAAO1e,EAAU,EAAIzB,KAAKyK,MAAMzK,KAAKC,SAAWkc,IAChDiE,EAAiB,GAEd9D,EAAY,UAAG3f,EAAC,YAAIwjB,KAAW7D,EAAY,UAAG6D,EAAI,YAAIxjB,OAC3DwjB,EAAOngB,KAAKyK,MAAMzK,KAAKC,SAAWmH,OAClCgZ,EACqB,EAAIhZ,MAE3B,GAAIgZ,EAAiB,EAAIhZ,IAEvBkV,EAAY,UAAG3f,EAAC,YAAIwjB,IAAU,CAC5B5X,MAAO5L,EACP6L,IAAK2X,EACLrM,SAAU4H,EAAI/e,GAAGwjB,IAEnBF,MACAF,GAE0BH,GAAgB,OAAOtD,EAGnD,KADA4D,EACqB,EAAI9Y,EAAS,K,CAGhC6Y,EAAoBJ,IAEtBA,GAAuBA,GADXA,EAAsBI,KACmB7Y,EAAUzK,EAAI,GAEvE,IACO2f,CACT,CA6XuB+D,CACnBhb,EACA+B,EAHqBpH,KAAKqC,IAAI,IAAM+E,GAAWA,EAAU,GAAM,GAK/D+W,EACAzC,GAOE4E,EAAUjE,GAAiCsD,EAAcxB,EAAgBjiB,GAqBvEqkB,EDiWM,SAACC,GAGX,IAAAjJ,EAIEiJ,EAAM,OAHR,EAGEA,EAAM,SAHRrkB,OAAQ,IAAG,GAAK,EAChB,EAEEqkB,EAAM,cAFR7E,OAAa,IAAG,EAAAH,GAAkB,EAClC,EACEgF,EAAM,cADRrD,OAAa,IAAG,EAAA3B,GAAkB,EAE9BiF,EA7Ea,SACnBlJ,EACApb,EACAwf,EACAwB,GAEA,IAAMhF,EAAmC,CAAC,EAgB1C,OAfA7c,OAAOgI,KAAKiU,GAAQ9a,SAAQ,SAACrB,EAAKuB,GAChC,IAAM2Z,EAAQiB,EAAOnc,GACfslB,EAAS,IAAInK,EAAM5Z,GAAG,EAAMR,GAC5BwkB,EAAa,CAAC,EACpBrK,EAAMla,MAAMK,SAAQ,SAACC,EAAMuM,GACzByX,EAAO3L,QAAQ9L,EAAGvM,EAAKif,IACvBgF,EAAWjkB,EAAKE,IAAMqM,CACxB,IACAqN,EAAMja,MAAMI,SAAQ,SAACK,EAAMuI,GACzB,IAAMub,EAAYD,EAAW7jB,EAAKC,QAC5B8jB,EAAYF,EAAW7jB,EAAKE,QAClC0jB,EAAOjM,SAAS,EAAGmM,EAAWC,EAAW/jB,EAAKqgB,GAChD,IACIuD,GAAUA,EAAO5L,eAAcqD,EAAOuI,EAAO9jB,IAAM8jB,EACzD,IACOvI,CACT,CAsD0B2I,CACtBvJ,EACApb,EACAwf,EACAwB,GAEM3F,EAAqDgJ,EAAM,WAA/C9I,EAAyC8I,EAAM,WAAnC/I,EAA6B+I,EAAM,WAAvB7I,EAAiB6I,EAAM,QAAdnO,EAAQmO,EAAM,IAY7DO,EAAa,IAAIC,GATJ,CACjBzJ,OAAQkJ,EACRjJ,WAAU,EACVE,WAAU,EACVD,WAAU,EACVpF,IAAG,EACHsF,QAAO,EACPxb,SAAQ,IAGV4kB,EAAWxF,MAEX,IAAMpD,EAzEa,SACnBZ,EACAoE,EACAwB,GAEA,IAAMhF,EAAS,GAkBf,OAjBAZ,EAAO9a,SAAQ,SAAC6Z,GACd,IAAMpa,EAAY,CAAEE,MAAO,GAAIC,MAAO,IACtCia,EAAMla,MAAMK,SAAQ,SAACC,G,MACnBR,EAAUE,MAAMS,OAAI,GAClBD,GAAI,UAAGF,EAAKE,MACX+e,GAAgBjf,EAAK6X,M,GAE1B,IACA+B,EAAMja,MAAMI,SAAQ,SAACK,G,MACnBZ,EAAUG,MAAMQ,OAAI,GAClBE,OAAQ,UAAGD,EAAK2G,MAChBzG,OAAQ,UAAGF,EAAK0G,MACf2Z,GAAgBrgB,EAAKyX,M,GAE1B,IACA4D,EAAOtb,KAAKX,EACd,IACOic,CACT,CAiDiB8I,CACbF,EAAW/I,kBACX2D,EACAwB,GAEF,OAAOhF,CACT,CCpYwB,CAfP,CACbZ,OAAQ+I,EACR3E,cAAa,EACbwB,cAAa,EACb3F,WAPa,EAQbC,WAPa,EAQbC,WAPa,EAQbvb,SAAQ,IAQ0B4H,MAAM,EAnB9B,IAqBNmd,EAAeX,EAAc3e,OAG7Buf,EAAkB,GACxBZ,EAAc9jB,SAAQ,SAACygB,EAAWvgB,GAChCwkB,EAAgBxkB,GAAK,CAAC,EACtBrB,OAAOgI,KAAKgd,GAAS7jB,SAAQ,SAAArB,GAC3B,IAAMkb,EAAQgK,EAAQllB,GAChBgmB,EAAoBnE,GAAgB3G,EAAO4G,EAAWvB,EAAewB,GAC3EgE,EAAgBxkB,GAAGvB,GAAOgmB,CAC5B,GACF,IAQM,MAjWuB,SAACD,EAAiBD,EAAcG,GAG7D,IAFA,IAAIC,EAAYna,IACdoa,EAAuB,E,WAChB5kB,GAEP,IAAM6kB,EAAYL,EAAgBxkB,GAE5B8kB,EAAkBnmB,OAAOgI,KAAKke,GAAWnV,MAAK,SAACjP,EAAGC,GACtD,OAAOmkB,EAAUpkB,GAAKokB,EAAUnkB,EAClC,IAIM8L,EAAW,GACjBsY,EAAgBhlB,SAAQ,SAACrB,EAAK6N,GACvBE,EAASF,EAHC,MAIbE,EAASF,EAJI,IAIY,CAAEsO,OAAQ,GAAImK,WAAY,EAAGC,SAAU,IAClExY,EAASF,EALM,IAKQsO,OAAO1a,KAAKzB,GACnC+N,EAASF,EANM,IAMQyY,YAAcF,EAAUpmB,EACjD,IAGA,IAAIwmB,EAAe,EACbC,EAAY,GAClB1Y,EAAS1M,SAAQ,SAAAqlB,GAEf,IAAMH,EAAWG,EAAgBJ,WAAaI,EAAgBvK,OAAO3V,OACrEkgB,EAAgBH,SAAWA,EAC3BE,EAAUhlB,KAAK8kB,GAGf,IAAII,EAAqB,EACnBC,EAAYF,EAAgBlgB,OAClCkgB,EAAgBvK,OAAO9a,SAAQ,SAACwlB,EAAWhZ,GACzC,IAAMiZ,EAAcV,EAAUS,GAC9BH,EAAgBvK,OAAO9a,SAAQ,SAAC0lB,EAAW9c,GACrC4D,IAAM5D,IACV0c,GAAsB/hB,KAAKqU,IAAI6N,EAAcV,EAAUW,IACzD,GACF,IAEAP,GADAG,GAAuBC,GAAaA,EAAY,GAAM,CAExD,IAEAJ,GAAgBzY,EAASvH,OAGzB,IAAIwgB,EAAe,EACnBP,EAAUplB,SAAQ,SAAC4lB,EAAWpZ,GAC5B4Y,EAAUplB,SAAQ,SAAC6lB,EAAWjd,GACxB4D,IAAM5D,IACV+c,GAAgBpiB,KAAKqU,IAAIgO,EAAYC,GACvC,IACAF,GAAiBP,EAAUjgB,QAAUigB,EAAUjgB,OAAS,GAAM,CAChE,IAGA,IAAM2gB,EAASH,EAAeR,EAC1BN,EAAYiB,IACdjB,EAAYiB,EACZhB,EAAuB5kB,E,EAzDlBA,EAAI,EAAGA,EAAIukB,EAAcvkB,I,EAAzBA,GA4DT,MAAO,CACLugB,UAAWmE,EAAWE,GACtBiB,kBAAmBrB,EAAgBI,GAEvC,CA8RyDkB,CACrDtB,EACAD,EACAX,GAHiBmC,EAAG,YAAqBC,EAAO,oBAQ9CC,EAAaxD,EAAQhjB,MAAM,GAC7BymB,EAAa,GACbtO,EAAwB,QAAhB,EAAA6K,EAAQhjB,MAAM,UAAE,eAAGuf,GAC3BmH,GAA0B,IAC5B1D,EAAQhjB,MAAMK,SAAQ,SAAAC,GACpB,IAAMqmB,EAASrmB,EAAKif,GACdqH,EAAqBrO,EAAaoO,IACpCC,aAAkB,EAAlBA,EAAoBphB,QAASkhB,IAC/BA,EAA0BE,EAAmBphB,OAC7CihB,EAAaG,EACbzO,EAAQwO,EACRH,EAAalmB,EAEjB,IAKA,IAAM+hB,EAA+B,CAAC,EAClCwE,EAAqB,CAAC,EACxBC,EAAiB,CAAC,EAClBC,EAAoB,CAAC,EAEjBC,EAAgB,CAAC,EACjBC,EAAoB,CAAC,EAC3B/nB,OAAOgI,KAAKsb,GAAqBniB,SAAQ,SAAC6mB,EAAQra,GAChDma,EAAcE,GAAU,GACpBnnB,IACFknB,EAAkBC,GAAU,IAE9B,IAAIC,GAAU,IACRC,EAAyB5E,EAAoB0E,GAC7CG,EAAqB,CAAC,EAC5BD,EAAuB/mB,SAAQ,SAAAinB,GAC7B,IAAM1a,EAAOuW,EAAc,UAAGqD,EAAWhmB,GAAE,YAAI8mB,EAAe9mB,KAQ9D,GAPAoM,GAAQoa,EAAcE,GAAQzmB,KAAKmM,GAC/Bua,EAAUva,IAAMua,EAAUva,GAC9Bya,EAAmB,UAAGb,EAAWhmB,GAAE,YAAI8mB,EAAe9mB,KAAQ,CAC5D2L,MAAO,EACPC,IAAKmW,EAAe+E,EAAe9mB,IAAI0M,IACvCwK,SAAU9K,GAER7M,EAAU,CACZ,IAAMwnB,EAAWpE,EAAc,UAAGmE,EAAe9mB,GAAE,YAAIgmB,EAAWhmB,KAClE+mB,GAAYN,EAAkBC,GAAQzmB,KAAK8mB,E,CAE/C,IAGAP,EAAcE,GAAUF,EAAcE,GAAQjX,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,IACzDlB,IAAUknB,EAAkBC,GAAUD,EAAkBC,GAAQjX,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,KAKnF4lB,EAAqB5G,GACnBoH,EACA/D,EACAN,EACA6D,GAGF,IAAIW,EAA6B,GAejC,GAdAtoB,OAAOgI,KAAKmgB,GAAoBhnB,SAAQ,SAAArB,GACtC,GAAI8nB,EAAe9nB,GACjBwoB,EAA2B/mB,KAAKqmB,EAAe9nB,QADjD,CAIA,IAAMyoB,EAAkBZ,EAAmB7nB,GAC3C8nB,EAAe9nB,GAAO6hB,GAAgB4G,EAAiBnB,EAAK/G,EAAewB,GAC3EyG,EAA2B/mB,KAAKqmB,EAAe9nB,G,CACjD,IAGAwoB,EAA6BA,EAA2BvX,MAAK,SAACjP,EAAGC,GAAM,OAAAA,EAAID,CAAJ,IACvE+lB,EAAkB,UAAGP,EAAWhmB,GAAE,YAAI0mB,IAAYM,EAE9CN,IAAW/O,EAGf,IADA,I,WACS1G,GACP,IAAMgD,EAAQgS,EAAWhV,GAGnBiW,EAAoB3F,EAAe5hB,EAAQsU,EAAMjU,IAAI0M,KACrDya,EAA4BD,EAAkB1H,kBAAkBkH,GAChEU,EAAmBpF,EAAoB0E,GAAQ1hB,OACrD,IAAKmiB,GAA6BA,EAA0B9Y,MAAQ+Y,E,OAClEnB,EAAW9d,OAAO8I,EAAG,G,WAOvB,IADA,IAAIoW,GAAgB,EACX/hB,EAAI,EAAGA,EAAI8hB,EAAkB9hB,IACpC,GAAI6hB,EAA0B/H,MAAM9Z,GAAKkhB,EAAcE,GAAQphB,GAAI,CACjE+hB,GAAgB,EAChB,K,CAGJ,GAAIA,E,OACFpB,EAAW9d,OAAO8I,EAAG,G,WASvB,IAAMqW,EAAe,CAAC,EACtBJ,EAAkBriB,UAAUhF,SAAQ,SAAA0nB,GAClC,IAAMnb,EAAOsW,EAAO,UAAGzO,EAAMjU,GAAE,YAAIunB,EAAavnB,KAChDsnB,EAAa,UAAGrT,EAAMjU,GAAE,YAAIunB,EAAavnB,KAAQ,CAC/C2L,MAAOhM,EAAQsU,EAAMjU,IAAI0M,IACzBd,IAAKjM,EAAQ4nB,EAAavnB,IAAI0M,IAC9BwK,SAAU9K,EAEd,IAEAsX,EAAUjE,GAAiC6H,EAAc/F,EAAgBjiB,EAAWokB,GAEpF,IAAI8D,EAAsB,GAC1B9oB,OAAOgI,KAAK4gB,GAAcznB,SAAQ,SAAArB,GAChC,GAAIunB,EAAQvnB,GACVgpB,EAAoBvnB,KAAK8lB,EAAQvnB,QADnC,CAIA,IAAMipB,EAAW/D,EAAQllB,GACzBunB,EAAQvnB,GAAO6hB,GAAgBoH,EAAU3B,EAAK/G,EAAewB,GAC7DiH,EAAoBvnB,KAAK8lB,EAAQvnB,G,CACnC,IAGAgpB,EAAsBA,EAAoB/X,MAAK,SAACjP,EAAGC,GAAM,OAAAA,EAAID,CAAJ,IAEzD,IAAIknB,GAAgB,EACpB,IAASpiB,EAAI,EAAGA,EAAI8hB,EAAkB9hB,IACpC,GAAIkiB,EAAoBliB,GAAK0hB,EAA2B1hB,GAAI,CAC1DoiB,GAAgB,EAChB,K,CAGJ,OAAIA,GACFzB,EAAW9d,OAAO8I,EAAG,G,iBADvB,C,EAhEOA,IADagV,aAAU,EAAVA,EAAYjhB,SAAU,GACf,EAAGiM,GAAK,EAAGA,I,EAA/BA,EAqEX,IAEA,IAAM0W,EAAkB,GAQxB1B,SAAAA,EAAYpmB,SAAQ,SAAA+nB,GAelB,IAdA,IAAMvI,EAAU1f,EAAQioB,EAAU5nB,IAAI0M,IAShCmb,EARqB5I,GACzB3f,EAAUE,MACVsf,EAAIO,GACJA,EACAN,EACA/Z,GAGuCH,UAIrCijB,GAAY,EACP/nB,EAFW8nB,EAAc7iB,OAEP,EAAGjF,GAAK,EAAGA,IAAK,CAEzC,GAAI8nB,EAAc7iB,OAAS,EAAIwd,EAAQhjB,MAAMwF,OAE3C,YADA8iB,GAAY,GAGd,IAAMP,EAAeM,EAAc9nB,GAC7B+hB,EAAgByF,EAAaxI,GAEnC,GAAKiD,EAAoBF,IAAmBE,EAAoBF,GAAe9c,OAO/E,GAAKwhB,EAAc1E,IAAmB0E,EAAc1E,GAAe9c,OAAnE,CAKA,IAAMxG,EAAM,UAAGopB,EAAU5nB,GAAE,YAAIunB,EAAavnB,IAGtC+nB,EAAkBrF,EAAOlkB,GAC3BkO,EAAM8Z,EAAc1E,GAAe9c,OAAS,EAEhD,GAAI+iB,EAD4BvB,EAAc1E,GAAepV,GAE3Dmb,EAAc1f,OAAOpI,EAAG,OAD1B,CAKF,GAAIR,EAAU,CACZ,IAAMyoB,EAAU,UAAGT,EAAavnB,GAAE,YAAI4nB,EAAU5nB,IAC1CioB,EAAoBvF,EAAOsF,GAGjC,GAFAtb,EAAM+Z,EAAkB3E,GAAe9c,OAAS,EAE5CijB,EADgCxB,EAAkB3E,GAAepV,GACd,CACrDmb,EAAc1f,OAAOpI,EAAG,GACxB,Q,EAKF,IAAMmoB,EAAiBnC,EAAQvnB,GAC3BunB,EAAQvnB,GACR2iB,GACE7hB,EACAsoB,EACAL,EACA5nB,EACAooB,EACAxG,EACAuE,EACA/G,EACAwB,EACAwF,EACArC,GAEAyE,EAAa,UAAGnC,EAAWhmB,GAAE,YAAI8hB,GAGvC,GAAIoG,EADF3B,EAAkB4B,GAAY5B,EAAkB4B,GAAYnjB,OAAS,GAErE6iB,EAAc1f,OAAOpI,EAAG,OAD1B,CAMM,MAIF6hB,GAA+BC,EAA8BC,EAAeC,EAAeC,GAH7FC,EAAyB,4BACE,8BACC,+BAG1BtiB,EAAQ4nB,EAAavnB,IAAI2F,OAASsc,GACpC4F,EAAc1f,OAAOpI,EAAG,E,QA1DxB8nB,EAAc1f,OAAOpI,EAAG,QAPxB8nB,EAAc1f,OAAOpI,EAAG,E,CAuEvB+nB,GACHH,EAAgB1nB,KAAK,CACnBT,MAAO,CAACooB,GAAWxgB,OAAOygB,IAGhC,IAMQ,IAAQO,EAAkC,EAAS5F,EAASwD,EAAWhmB,IAAI,GAAM,OAErFqoB,EAAwC,CAAC,EACzC9oB,GACFb,OAAOgI,KAAK0hB,GAA+BvoB,SAAQ,SAAA+C,GACjD,IAAMyb,EAAY0D,EAAenf,GAAQ9C,KAAKif,GACzCsJ,EAAsChK,GAGzCgK,EAAsChK,GAAWpe,KAC/CmoB,EAA8BxlB,IAHhCylB,EAAsChK,GAAa,CAAC+J,EAA8BxlB,GAKtF,IACAlE,OAAOgI,KAAK2hB,GAAuCxoB,SAAQ,SAAAsmB,GACzDkC,EAAsClC,GAAQ1W,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,GAC/D,KAEA4nB,EAAwC7B,EAK1C,IADA,I,WACSzmB,GACP,IAAMuoB,EAAiBX,EAAgB5nB,GACjC6nB,EAAYU,EAAe9oB,MAAM,GAEjC+oB,EAA6B,CAAC,EAC9BC,EAAmB,CAAC,EAC1BF,EAAe9oB,MAAMK,SAAQ,SAACC,EAAM2oB,GAClCD,EAAiB1oB,EAAKE,IAAM,CAC1B0M,IAAK+b,EACL3oB,KAAI,EACJ6F,OAAQ,EACRE,SAAU,EACVC,UAAW,GAEb,IAAM4iB,EAAa5oB,EAAKif,GACnBwJ,EAA2BG,GAC3BH,EAA2BG,KADaH,EAA2BG,GAAc,CAExF,IAIA,IAAMC,EAAiB,GACjBC,EAAoB,CAAC,EAC3BtpB,EAAUG,MAAMI,SAAQ,SAAAK,GAClBsoB,EAAiBtoB,EAAKC,SAAWqoB,EAAiBtoB,EAAKE,UACzDuoB,EAAe1oB,KAAKC,GACf0oB,EAAkB1oB,EAAKqgB,IACvBqI,EAAkB1oB,EAAKqgB,MADiBqI,EAAkB1oB,EAAKqgB,IAAkB,EAEtFiI,EAAiBtoB,EAAKC,QAAQwF,SAC9B6iB,EAAiBtoB,EAAKE,QAAQuF,SAC9B6iB,EAAiBtoB,EAAKC,QAAQ2F,YAC9B0iB,EAAiBtoB,EAAKE,QAAQyF,WAElC,IAKA,IAFA,IAAMgjB,EAAsBnqB,OAAOgI,KAAKkc,GAAqB5d,OACzD8jB,GAAoB,EACfvO,EAAI,EAAGA,EAAIsO,EAAqBtO,IAAK,CAC5C,IAAM,EAAQ7b,OAAOgI,KAAKkc,GAAqBrI,GAC/C,IACGqO,EAAkB,IACnBA,EAAkB,GAAShG,EAAoB,GAAO5d,OACtD,CACA8jB,GAAoB,EACpB,K,EAGJ,GAAIA,E,OACFnB,EAAgBxf,OAAOpI,EAAG,G,WAK5B,IAAIgpB,EAAmBJ,EAAe3jB,OAGtC,GAAI+jB,EAAmBvG,EAAQ/iB,MAAMuF,O,OACnC2iB,EAAgBxf,OAAOpI,EAAG,G,QAG5B,IAAIipB,GAAwB,E,WACnBzO,GACP,IAAMra,EAAOyoB,EAAepO,GACtB7B,EAAYxY,EAAKqgB,GACjB0I,EAAwBrG,EAAoBlK,GAGlD,IAAKuQ,IAA0BA,EAAsBjkB,OAGnD,OAFA4jB,EAAkBlQ,KAEduQ,GAAyBL,EAAkBlQ,GAAauQ,EAAsBjkB,QAChFgkB,GAAwB,E,UAG1BL,EAAexgB,OAAOoS,EAAG,GACzBiO,EAAiBtoB,EAAKC,QAAQwF,SAC9B6iB,EAAiBtoB,EAAKE,QAAQuF,SAC9B6iB,EAAiBtoB,EAAKC,QAAQ2F,YAC9B0iB,EAAiBtoB,EAAKE,QAAQyF,W,YAKhC,IAAM2a,EAAcgI,EAAiBtoB,EAAKC,QAAQL,KAAKif,GACjD0B,EAAc+H,EAAiBtoB,EAAKE,QAAQN,KAAKif,GAEnDmK,GAAc,EAgBlB,OAfAD,EAAsBppB,SAAQ,SAAAspB,GAC5B,IAAMC,EAAgBrH,EAAeoH,EAAYhpB,QAAQL,KACnDupB,EAAgBtH,EAAeoH,EAAY/oB,QAAQN,KAEvDspB,EAAcrK,KAAmByB,GACjC6I,EAActK,KAAmB0B,IAEjCyI,GAAc,GAEb3pB,GACD6pB,EAAcrK,KAAmB0B,GACjC4I,EAActK,KAAmByB,IAEjC0I,GAAc,EAClB,IACKA,OAAL,GACEN,EAAkBlQ,KAEduQ,GAAyBL,EAAkBlQ,GAAauQ,EAAsBjkB,QAChFgkB,GAAwB,E,UAG1BL,EAAexgB,OAAOoS,EAAG,GACzBiO,EAAiBtoB,EAAKC,QAAQwF,SAC9B6iB,EAAiBtoB,EAAKE,QAAQuF,SAC9B6iB,EAAiBtoB,EAAKC,QAAQ2F,YAC9B0iB,EAAiBtoB,EAAKE,QAAQyF,W,cApDlC,IAAS0U,EAAIwO,EAAmB,EAAGxO,GAAK,G,YAA/BA,GAAkCA,KA0D3C,GAAIyO,E,OACFrB,EAAgBxf,OAAOpI,EAAG,G,WAI5BuoB,EAAe7oB,MAAQkpB,EAEf,IAAQW,EAAuB,EACrChB,EACAA,EAAe9oB,MAAM,GAAGQ,IACxB,GACD,OA8CD,GA7CAtB,OAAOgI,KAAK4iB,GACTpnB,UACArC,SAAQ,SAAA0pB,GACP,GAAIA,IAAajB,EAAe9oB,MAAM,GAAGQ,KAAMgpB,EAA/C,CAEA,GAAIM,EAAmBC,KAAchf,IAAU,CAC7C,IAAMif,EAAkBhB,EAAiBe,GAAUzpB,KAAKif,GAExD,GADAwJ,EAA2BiB,KAEzBjB,EAA2BiB,GAC3BxH,EAAoBwH,GAAiBxkB,OAGrC,YADAgkB,GAAwB,GAG1B,IAAMtc,EAAM4b,EAAe9oB,MAAM+F,QAAQijB,EAAiBe,GAAUzpB,MAGpE,OAFAwoB,EAAe9oB,MAAM2I,OAAOuE,EAAK,QACjC8b,EAAiBe,QAAY7nB,E,CAI/B,IAAM+nB,EAAS9pB,EAAQ4pB,GAAUzpB,KAAKif,GACtC,IACGsJ,EAAsCoB,KACtCpB,EAAsCoB,GAAQzkB,QAC/CskB,EAAmBC,GACjBlB,EAAsCoB,GACpCpB,EAAsCoB,GAAQzkB,OAAS,GAE3D,CAGA,GAFMwkB,EAAkBhB,EAAiBe,GAAUzpB,KAAKif,GACxDwJ,EAA2BiB,KAEzBjB,EAA2BiB,GAC3BxH,EAAoBwH,GAAiBxkB,OAGrC,YADAgkB,GAAwB,GAGpBtc,EAAM4b,EAAe9oB,MAAM+F,QAAQijB,EAAiBe,GAAUzpB,MACpEwoB,EAAe9oB,MAAM2I,OAAOuE,EAAK,GACjC8b,EAAiBe,QAAY7nB,C,CAtC6C,CAwC9E,IAEEsnB,E,OACFrB,EAAgBxf,OAAOpI,EAAG,G,WAM5B,IAFA,IAAI2pB,GAAgB,EAChBC,EAAY,EACTD,IAAkBV,GAAuB,CAQ9C,GAPAU,GAAgB,EAGEnqB,EAAYipB,EAAiBZ,EAAU5nB,IAAI2F,OAASoc,EAAeiE,EAAWhmB,IAAI2F,QAClG6iB,EAAiBZ,EAAU5nB,IAAI6F,SAAWkc,EAAeiE,EAAWhmB,IAAI6F,UACxE2iB,EAAiBZ,EAAU5nB,IAAI8F,UAAYic,EAAeiE,EAAWhmB,IAAI8F,UACzE0iB,EAAiBZ,EAAU5nB,IAAI2F,OAASoc,EAAeiE,EAAWhmB,IAAI2F,OACzD,CACbqjB,GAAwB,EACxB,K,CAGF,GACET,EAA2BX,EAAU7I,IACrCiD,EAAoB4F,EAAU7I,IAAgB/Z,OAC9C,CACAgkB,GAAwB,EACxB,K,CAKF,IADA,IACSvqB,EADuB6pB,EAAe9oB,MAAMwF,OACd,EAAGvG,GAAK,EAAGA,IAAK,CACrD,IAAMmrB,EAAStB,EAAe9oB,MAAMf,GAC9B+Y,EAAagR,EAAiBoB,EAAO5pB,IAAI2F,OACzCkkB,EAAerB,EAAiBoB,EAAO5pB,IAAI6F,SAC3CikB,EAAgBtB,EAAiBoB,EAAO5pB,IAAI8F,UAC5C4iB,EAAakB,EAAO7K,GAEpB,EAIF6C,GAA+BC,EAA8B6G,EAAY3G,EAAeC,GAH1FC,EAAyB,4BACzBC,EAA2B,8BAC3BC,EAA4B,+BAO9B,GAJwB5iB,EAAYiY,EAAayK,GAC/C4H,EAAe3H,GACf4H,EAAgB3H,EAChB3K,EAAayK,EACM,CAGnB,GAFAsG,EAA2BqB,EAAO7K,MAGhCwJ,EAA2BqB,EAAO7K,IAClCiD,EAAoB4H,EAAO7K,IAAgB/Z,OAC3C,CACAgkB,GAAwB,EACxB,K,CAEFV,EAAe9oB,MAAM2I,OAAO1J,EAAG,GAC/B+pB,EAAiBoB,EAAO5pB,SAAM0B,EAC9BgoB,GAAgB,C,EAGpB,GAAIV,IAA2BU,GAA+B,IAAdC,EAAkB,MAGlE,IAAK,IAAII,GADThB,EAAmBJ,EAAe3jB,QACF,EAAG+kB,GAAK,EAAGA,IAAK,CAC9C,IAAMC,EAAQrB,EAAeoB,GAC7B,IAAKvB,EAAiBwB,EAAM7pB,UAAYqoB,EAAiBwB,EAAM5pB,QAAS,CACtEuoB,EAAexgB,OAAO4hB,EAAG,GACzB,IAAMrR,EAAYsR,EAAMzJ,GAWxB,GAVAqI,EAAkBlQ,KACd8P,EAAiBwB,EAAM7pB,UACzBqoB,EAAiBwB,EAAM7pB,QAAQwF,SAC/B6iB,EAAiBwB,EAAM7pB,QAAQ2F,aAE7B0iB,EAAiBwB,EAAM5pB,UACzBooB,EAAiBwB,EAAM5pB,QAAQuF,SAC/B6iB,EAAiBwB,EAAM5pB,QAAQyF,YAI/B+c,EAAoBlK,IACpBkQ,EAAkBlQ,GAAakK,EAAoBlK,GAAW1T,OAC9D,CACAgkB,GAAwB,EACxB,K,CAEFU,GAAgB,C,EAGpBC,G,CAGF,OAAIX,GAOFA,GACAV,EAAe9oB,MAAMwF,OAASwd,EAAQhjB,MAAMwF,QAC5C2jB,EAAe3jB,OAASwd,EAAQ/iB,MAAMuF,QARtC2iB,EAAgBxf,OAAOpI,EAAG,G,iBAK5B,C,EApROA,EADe4nB,EAAgB3iB,OACP,EAAGjF,GAAK,G,YAAhCA,GAAmCA,KAqS5C,IAAIkqB,EAAgBtC,EAAgB3iB,O,WAC3BjF,GACP,IAAMmqB,EAAMvC,EAAgB5nB,GACtBoqB,EAAa,CAAC,EACpBD,EAAIzqB,MAAMI,SAAQ,SAAAK,GAChB,IAAM1B,EAAM,UAAG0B,EAAKC,OAAM,YAAID,EAAKE,OAAM,YAAIF,EAAKyX,OAC7CwS,EAAW3rB,GACX2rB,EAAW3rB,KADM2rB,EAAW3rB,GAAO,CAE1C,IAEA,I,eAAS6N,GACP,IAAM+d,EAAMzC,EAAgBtb,GACtBge,EAAa,CAAC,EACpBD,EAAI3qB,MAAMI,SAAQ,SAAAK,GAChB,IAAM1B,EAAM,UAAG0B,EAAKC,OAAM,YAAID,EAAKE,OAAM,YAAIF,EAAKyX,OAC7C0S,EAAW7rB,GACX6rB,EAAW7rB,KADM6rB,EAAW7rB,GAAO,CAE1C,IAEA,IAAI8rB,GAAO,EACP5rB,OAAOgI,KAAK2jB,GAAYrlB,SAAWtG,OAAOgI,KAAKyjB,GAAYnlB,OAC7DslB,GAAO,EAEP5rB,OAAOgI,KAAKyjB,GAAYtqB,SAAQ,SAAArB,GAC1B6rB,EAAW7rB,KAAS2rB,EAAW3rB,KAAM8rB,GAAO,EAClD,IAEEA,GACF3C,EAAgBxf,OAAOkE,EAAG,E,EAlBrBA,EAAI4d,EAAgB,EAAG5d,EAAItM,EAAGsM,I,EAA9BA,GAqBT4d,EAAgBtC,EAAgB3iB,M,EA9BlC,IAASjF,EAAI,EAAGA,GAAKkqB,EAAgB,EAAGlqB,I,EAA/BA,GAiCT,OAAO4nB,CA/tBa,CAVsB,CA0uB5C,ECxrCO,ICGD,GAA+B,oBAAT4C,KAAwBA,KAAO,CAAC,EAO5D,GAAIC,UAAY,SAACC,GACT,MAA2BA,EAAMra,KAA/Bsa,EAAc,iBAAEta,EAAI,OAG5B,GAAIsa,EAGJ,GAAyC,mBAA9B,EAAUA,GAKrB,GAAIC,YAAY,CAAED,eDOT,gBCZT,CACE,IAAMnP,EAAS,EAAUmP,GAAe,MAAzB,EAA6Bta,GAC5C,GAAIua,YAAY,CAAED,eDSX,UCT4Cta,KAAMmL,G,CAI7D,C", "sources": ["webpack://Algorithm/webpack/bootstrap", "webpack://Algorithm/webpack/runtime/define property getters", "webpack://Algorithm/webpack/runtime/hasOwnProperty shorthand", "webpack://Algorithm/webpack/runtime/make namespace object", "webpack://Algorithm/./src/adjacent-matrix.ts", "webpack://Algorithm/./src/structs/linked-list.ts", "webpack://Algorithm/./src/structs/queue.ts", "webpack://Algorithm/./src/util.ts", "webpack://Algorithm/./src/bfs.ts", "webpack://Algorithm/./src/connected-component.ts", "webpack://Algorithm/./src/degree.ts", "webpack://Algorithm/./src/dfs.ts", "webpack://Algorithm/./src/detect-cycle.ts", "webpack://Algorithm/./node_modules/_tslib@2.6.1@tslib/tslib.es6.mjs", "webpack://Algorithm/../src/is-type.ts", "webpack://Algorithm/../src/is-function.ts", "webpack://Algorithm/../src/is-array.ts", "webpack://Algorithm/../src/keys.ts", "webpack://Algorithm/../src/pull.ts", "webpack://Algorithm/../src/uniq.ts", "webpack://Algorithm/../src/pull-at.ts", "webpack://Algorithm/../src/group-by.ts", "webpack://Algorithm/../src/is-integer.ts", "webpack://Algorithm/../src/to-degree.ts", "webpack://Algorithm/../src/to-radian.ts", "webpack://Algorithm/../src/values.ts", "webpack://Algorithm/../src/is-prototype.ts", "webpack://Algorithm/../src/clone.ts", "webpack://Algorithm/../src/measure-text-width.ts", "webpack://Algorithm/../src/is-empty.ts", "webpack://Algorithm/../src/pick.ts", "webpack://Algorithm/../src/memoize.ts", "webpack://Algorithm/../src/is-string.ts", "webpack://Algorithm/../src/cache.ts", "webpack://Algorithm/./src/dijkstra.ts", "webpack://Algorithm/./src/find-path.ts", "webpack://Algorithm/./src/floydWarshall.ts", "webpack://Algorithm/./src/label-propagation.ts", "webpack://Algorithm/./src/utils/vector.ts", "webpack://Algorithm/./src/constants/time.ts", "webpack://Algorithm/./src/types.ts", "webpack://Algorithm/./src/utils/data-preprocessing.ts", "webpack://Algorithm/./src/louvain.ts", "webpack://Algorithm/./src/utils/node-properties.ts", "webpack://Algorithm/./src/structs/union-find.ts", "webpack://Algorithm/./src/structs/binary-heap.ts", "webpack://Algorithm/./src/mts.ts", "webpack://Algorithm/./src/pageRank.ts", "webpack://Algorithm/./src/gSpan/struct.ts", "webpack://Algorithm/./src/gSpan/gSpan.ts", "webpack://Algorithm/./src/gaddi.ts", "webpack://Algorithm/./src/workers/constant.ts", "webpack://Algorithm/./src/workers/index.worker.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { GraphData, Matrix } from \"./types\";\n\nconst adjMatrix = (graphData: GraphData, directed?: boolean): Matrix[] => {\n  const { nodes, edges } = graphData;\n  const matrix: Matrix[] = [];\n  // map node with index in data.nodes\n  const nodeMap: {\n    [key: string]: number;\n  } = {};\n\n  if (!nodes) {\n    throw new Error(\"invalid nodes data!\");\n  }\n\n  if (nodes) {\n    nodes.forEach((node, i) => {\n      nodeMap[node.id] = i;\n      const row: number[] = [];\n      matrix.push(row);\n    });\n  }\n\n  if (edges) {\n    edges.forEach((edge) => {\n      const { source, target } = edge;\n      const sIndex = nodeMap[source as string];\n      const tIndex = nodeMap[target as string];\n      if ((!sIndex && sIndex !== 0) || (!tIndex && tIndex !== 0)) return;\n      matrix[sIndex][tIndex] = 1;\n      if (!directed) {\n        matrix[tIndex][sIndex] = 1;\n      }\n    });\n  }\n  return matrix;\n};\n\nexport default adjMatrix;\n", "const defaultComparator = (a, b) => {\n  if (a === b) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * 链表中单个元素节点\n */\nexport class LinkedListNode {\n  public value;\n\n  public next: LinkedListNode;\n\n  constructor(value, next: LinkedListNode = null) {\n    this.value = value;\n    this.next = next;\n  }\n\n  toString(callback?: any) {\n    return callback ? callback(this.value) : `${this.value}`;\n  }\n}\n\nexport default class LinkedList {\n  public head: LinkedListNode;\n\n  public tail: LinkedListNode;\n\n  public compare: Function;\n\n  constructor(comparator = defaultComparator) {\n    this.head = null;\n    this.tail = null;\n    this.compare = comparator;\n  }\n\n  /**\n   * 将指定元素添加到链表头部\n   * @param value\n   */\n  prepend(value) {\n    // 在头部添加一个节点\n    const newNode = new LinkedListNode(value, this.head);\n    this.head = newNode;\n\n    if (!this.tail) {\n      this.tail = newNode;\n    }\n\n    return this;\n  }\n\n  /**\n   * 将指定元素添加到链表中\n   * @param value\n   */\n  append(value) {\n    const newNode = new LinkedListNode(value);\n\n    // 如果不存在头节点，则将创建的新节点作为头节点\n    if (!this.head) {\n      this.head = newNode;\n      this.tail = newNode;\n\n      return this;\n    }\n\n    // 将新节点附加到链表末尾\n    this.tail.next = newNode;\n    this.tail = newNode;\n\n    return this;\n  }\n\n  /**\n   * 删除指定元素\n   * @param value 要删除的元素\n   */\n  delete(value): LinkedListNode {\n    if (!this.head) {\n      return null;\n    }\n\n    let deleteNode = null;\n\n    // 如果删除的是头部元素，则将next作为头元素\n    while (this.head && this.compare(this.head.value, value)) {\n      deleteNode = this.head;\n      this.head = this.head.next;\n    }\n\n    let currentNode = this.head;\n\n    if (currentNode !== null) {\n      // 如果删除了节点以后，将next节点前移\n      while (currentNode.next) {\n        if (this.compare(currentNode.next.value, value)) {\n          deleteNode = currentNode.next;\n          currentNode.next = currentNode.next.next;\n        } else {\n          currentNode = currentNode.next;\n        }\n      }\n    }\n\n    // 检查尾部节点是否被删除\n    if (this.compare(this.tail.value, value)) {\n      this.tail = currentNode;\n    }\n\n    return deleteNode;\n  }\n\n  /**\n   * 查找指定的元素\n   * @param param0\n   */\n  find({ value = undefined, callback = undefined }): LinkedListNode {\n    if (!this.head) {\n      return null;\n    }\n\n    let currentNode = this.head;\n\n    while (currentNode) {\n      // 如果指定了 callback，则按指定的 callback 查找\n      if (callback && callback(currentNode.value)) {\n        return currentNode;\n      }\n\n      // 如果指定了 value，则按 value 查找\n      if (value !== undefined && this.compare(currentNode.value, value)) {\n        return currentNode;\n      }\n\n      currentNode = currentNode.next;\n    }\n\n    return null;\n  }\n\n  /**\n   * 删除尾部节点\n   */\n  deleteTail() {\n    const deletedTail = this.tail;\n\n    if (this.head === this.tail) {\n      // 链表中只有一个元素\n      this.head = null;\n      this.tail = null;\n      return deletedTail;\n    }\n\n    let currentNode = this.head;\n    while (currentNode.next) {\n      if (!currentNode.next.next) {\n        currentNode.next = null;\n      } else {\n        currentNode = currentNode.next;\n      }\n    }\n\n    this.tail = currentNode;\n\n    return deletedTail;\n  }\n\n  /**\n   * 删除头部节点\n   */\n  deleteHead() {\n    if (!this.head) {\n      return null;\n    }\n\n    const deletedHead = this.head;\n\n    if (this.head.next) {\n      this.head = this.head.next;\n    } else {\n      this.head = null;\n      this.tail = null;\n    }\n\n    return deletedHead;\n  }\n\n  /**\n   * 将一组元素转成链表中的节点\n   * @param values 链表中的元素\n   */\n  fromArray(values) {\n    values.forEach((value) => this.append(value));\n    return this;\n  }\n\n  /**\n   * 将链表中的节点转成数组元素\n   */\n  toArray() {\n    const nodes = [];\n\n    let currentNode = this.head;\n\n    while (currentNode) {\n      nodes.push(currentNode);\n      currentNode = currentNode.next;\n    }\n\n    return nodes;\n  }\n\n  /**\n   * 反转链表中的元素节点\n   */\n  reverse() {\n    let currentNode = this.head;\n    let prevNode = null;\n    let nextNode = null;\n    while (currentNode) {\n      // 存储下一个元素节点\n      nextNode = currentNode.next;\n\n      // 更改当前节点的下一个节点，以便将它连接到上一个节点上\n      currentNode.next = prevNode;\n\n      // 将 prevNode 和 currentNode 向前移动一步\n      prevNode = currentNode;\n      currentNode = nextNode;\n    }\n\n    this.tail = this.head;\n    this.head = prevNode;\n  }\n\n  toString(callback = undefined) {\n    return this.toArray()\n      .map((node) => node.toString(callback))\n      .toString();\n  }\n}\n", "import LinkedList from './linked-list';\n\nexport default class Queue {\n  public linkedList: LinkedList;\n\n  constructor() {\n    this.linkedList = new LinkedList();\n  }\n\n  /**\n   * 队列是否为空\n   */\n  public isEmpty() {\n    return !this.linkedList.head;\n  }\n\n  /**\n   * 读取队列头部的元素， 不删除队列中的元素\n   */\n  public peek() {\n    if (!this.linkedList.head) {\n      return null;\n    }\n    return this.linkedList.head.value;\n  }\n\n  /**\n   * 在队列的尾部新增一个元素\n   * @param value\n   */\n  public enqueue(value) {\n    this.linkedList.append(value);\n  }\n\n  /**\n   * 删除队列中的头部元素，如果队列为空，则返回 null\n   */\n  public dequeue() {\n    const removeHead = this.linkedList.deleteHead();\n    return removeHead ? removeHead.value : null;\n  }\n\n  public toString(callback?: any) {\n    return this.linkedList.toString(callback);\n  }\n}\n", "import { EdgeConfig, GraphData, Matrix } from './types'\n\n/**\n * 获取指定节点的所有邻居\n * @param nodeId 节点 ID\n * @param edges 图中的所有边数据\n * @param type 邻居类型\n */\nexport const getNeighbors = (nodeId: string, edges: EdgeConfig[] = [], type?: 'target' | 'source' | undefined): string[] => {\n  const currentEdges = edges.filter(edge => edge.source === nodeId || edge.target === nodeId)\n  if (type === 'target') {\n    // 当前节点为 source，它所指向的目标节点\n    const neighhborsConverter = (edge: EdgeConfig) => {\n      return edge.source === nodeId;\n    };\n    return currentEdges.filter(neighhborsConverter).map((edge) => edge.target);\n  }\n  if (type === 'source') {\n    // 当前节点为 target，它所指向的源节点\n    const neighhborsConverter = (edge: EdgeConfig) => {\n      return edge.target === nodeId;\n    };\n    return currentEdges.filter(neighhborsConverter).map((edge) => edge.source);\n  }\n\n  // 若未指定 type ，则返回所有邻居\n  const neighhborsConverter = (edge: EdgeConfig) => {\n    return edge.source === nodeId ? edge.target : edge.source;\n  };\n  return currentEdges.map(neighhborsConverter);\n}\n\n/**\n * 获取指定节点的出边\n * @param nodeId 节点 ID\n * @param edges 图中的所有边数据\n */\nexport const getOutEdgesNodeId = (nodeId: string, edges: EdgeConfig[]) => {\n  return edges.filter(edge => edge.source === nodeId)\n}\n\n/**\n * 获取指定节点的边，包括出边和入边\n * @param nodeId 节点 ID\n * @param edges 图中的所有边数据\n */\nexport const getEdgesByNodeId = (nodeId: string, edges: EdgeConfig[]) => {\n  return edges.filter(edge => edge.source === nodeId || edge.target === nodeId)\n}\n\n/**\n * 生成唯一的 ID，规则是序号 + 时间戳\n * @param index 序号\n */\nexport const uniqueId = (index: number = 0) => {\n  const random1 = `${Math.random()}`.split('.')[1].substr(0, 5);\n  const random2 = `${Math.random()}`.split('.')[1].substr(0, 5);\n  return `${index}-${random1}${random2}`\n};\n", "import Queue from './structs/queue'\nimport { GraphData, IAlgorithmCallbacks } from './types';\nimport { getNeighbors } from './util';\n\n/**\n *\n * @param callbacks\n * allowTraversal: 确定 BFS 是否从顶点沿着边遍历到其邻居，默认情况下，同一个节点只能遍历一次\n * enterNode: 当 BFS 访问某个节点时调用\n * leaveNode: 当 BFS 访问访问结束某个节点时调用\n */\nfunction initCallbacks(callbacks: IAlgorithmCallbacks = {} as IAlgorithmCallbacks) {\n  const initiatedCallback = callbacks;\n\n  const stubCallback = () => {};\n\n  const allowTraversalCallback = (() => {\n    const seen = {};\n    return ({ next }) => {\n      const id = next;\n      if (!seen[id]) {\n        seen[id] = true;\n        return true;\n      }\n      return false;\n    };\n  })();\n\n  initiatedCallback.allowTraversal = callbacks.allowTraversal || allowTraversalCallback;\n  initiatedCallback.enter = callbacks.enter || stubCallback;\n  initiatedCallback.leave = callbacks.leave || stubCallback;\n\n  return initiatedCallback;\n}\n\n/**\n * 广度优先遍历图\n * @param graph Graph 图实例\n * @param startNode 开始遍历的节点\n * @param originalCallbacks 回调\n */\nconst breadthFirstSearch = (\n  graphData: GraphData,\n  startNodeId: string,\n  originalCallbacks?: IAlgorithmCallbacks,\n  directed: boolean = true\n) => {\n  const callbacks = initCallbacks(originalCallbacks);\n  const nodeQueue = new Queue();\n\n  const { edges = [] } = graphData\n\n  // 初始化队列元素\n  nodeQueue.enqueue(startNodeId);\n\n  let previousNode = '';\n\n  // 遍历队列中的所有顶点\n  while (!nodeQueue.isEmpty()) {\n    const currentNode: string = nodeQueue.dequeue();\n    callbacks.enter({\n      current: currentNode,\n      previous: previousNode,\n    });\n\n    // 将所有邻居添加到队列中以便遍历\n    getNeighbors(currentNode, edges, directed ? 'target' : undefined).forEach((nextNode) => {\n      if (\n        callbacks.allowTraversal({\n          previous: previousNode,\n          current: currentNode,\n          next: nextNode,\n        })\n      ) {\n        nodeQueue.enqueue(nextNode);\n      }\n    });\n\n    callbacks.leave({\n      current: currentNode,\n      previous: previousNode,\n    });\n\n    // 下一次循环之前存储当前顶点\n    previousNode = currentNode;\n  }\n};\n\nexport default breadthFirstSearch;\n", "import { GraphData, NodeConfig } from \"./types\";\nimport { getNeighbors } from \"./util\";\n\n/**\n * Generate all connected components for an undirected graph\n * @param graph\n */\nexport const detectConnectedComponents = (graphData: GraphData): NodeConfig[][] => {\n  const { nodes = [], edges = [] } = graphData\n  const allComponents: NodeConfig[][] = [];\n  const visited = {};\n  const nodeStack: NodeConfig[] = [];\n\n  const getComponent = (node: NodeConfig) => {\n    nodeStack.push(node);\n    visited[node.id] = true;\n    const neighbors = getNeighbors(node.id, edges);\n    for (let i = 0; i < neighbors.length; ++i) {\n      const neighbor = neighbors[i];\n      if (!visited[neighbor]) {\n        const targetNode = nodes.filter(node => node.id === neighbor)\n        if (targetNode.length > 0) {\n          getComponent(targetNode[0]);\n        }\n      }\n    }\n  };\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!visited[node.id]) {\n      // 对于无向图进行dfs遍历，每一次调用后都得到一个连通分量\n      getComponent(node);\n      const component = [];\n      while (nodeStack.length > 0) {\n        component.push(nodeStack.pop());\n      }\n      allComponents.push(component);\n    }\n  }\n  return allComponents;\n}\n\n/**\n * Tarjan's Algorithm 复杂度  O(|V|+|E|)\n * For directed graph only\n * a directed graph is said to be strongly connected if \"every vertex is reachable from every other vertex\".\n * refer: http://en.wikipedia.org/wiki/Tarjan%27s_strongly_connected_components_algorithm\n * @param graph\n * @return a list of strongly connected components\n */\nexport const detectStrongConnectComponents = (graphData: GraphData): NodeConfig[][] => {\n  const { nodes = [], edges = [] } = graphData\n  const nodeStack: NodeConfig[] = [];\n  const inStack = {}; // 辅助判断是否已经在stack中，减少查找开销\n  const indices = {};\n  const lowLink = {};\n  const allComponents: NodeConfig[][] = [];\n  let index = 0;\n\n  const getComponent = (node: NodeConfig) => {\n    // Set the depth index for v to the smallest unused index\n    indices[node.id] = index;\n    lowLink[node.id] = index;\n    index += 1;\n    nodeStack.push(node);\n    inStack[node.id] = true;\n\n    // 考虑每个邻接点\n    const neighbors = getNeighbors(node.id, edges, 'target').filter((n) => nodes.map(node => node.id).indexOf(n) > -1);\n    for (let i = 0; i < neighbors.length; i++) {\n      const targetNodeID = neighbors[i];\n      if (!indices[targetNodeID] && indices[targetNodeID] !== 0) {\n        const targetNode = nodes.filter(node => node.id === targetNodeID)\n        if (targetNode.length > 0) {\n          getComponent(targetNode[0]);\n        }\n        // tree edge\n        lowLink[node.id] = Math.min(lowLink[node.id], lowLink[targetNodeID]);\n      } else if (inStack[targetNodeID]) {\n        // back edge, target node is in the current SCC\n        lowLink[node.id] = Math.min(lowLink[node.id], indices[targetNodeID]);\n      }\n    }\n\n    // If node is a root node, generate an SCC\n    if (lowLink[node.id] === indices[node.id]) {\n      const component = [];\n      while (nodeStack.length > 0) {\n        const tmpNode = nodeStack.pop();\n        inStack[tmpNode.id] = false;\n        component.push(tmpNode);\n        if (tmpNode === node) break;\n      }\n      if (component.length > 0) {\n        allComponents.push(component);\n      }\n    }\n  };\n\n  for (const node of nodes) {\n    if (!indices[node.id] && indices[node.id] !== 0) {\n      getComponent(node);\n    }\n  }\n\n  return allComponents;\n}\n\nexport default function getConnectedComponents(graphData: GraphData, directed?: boolean): NodeConfig[][] {\n  if (directed) return detectStrongConnectComponents(graphData);\n  return detectConnectedComponents(graphData);\n}\n", "import { GraphData, DegreeType } from \"./types\";\n\nconst degree = (graphData: GraphData): DegreeType => {\n  const degrees: DegreeType = {};\n  const { nodes = [], edges = [] } = graphData\n\n  nodes.forEach((node) => {\n    degrees[node.id] = {\n      degree: 0,\n      inDegree: 0,\n      outDegree: 0,\n    };\n  });\n\n  edges.forEach((edge) => {\n    degrees[edge.source].degree++;\n    degrees[edge.source].outDegree++;\n    degrees[edge.target].degree++;\n    degrees[edge.target].inDegree++;\n  });\n\n  return degrees;\n};\n\nexport default degree;\n\n/**\n * 获取指定节点的入度\n * @param graphData 图数据\n * @param nodeId 节点ID\n */\nexport const getInDegree = (graphData: GraphData, nodeId: string): number => {\n  const nodeDegree = degree(graphData)\n  if (nodeDegree[nodeId]) {\n    return degree(graphData)[nodeId].inDegree\n  }\n  return 0\n}\n\n/**\n * 获取指定节点的出度\n * @param graphData 图数据\n * @param nodeId 节点ID\n */\nexport const getOutDegree = (graphData: GraphData, nodeId: string): number => {\n  const nodeDegree = degree(graphData)\n  if (nodeDegree[nodeId]) {\n    return degree(graphData)[nodeId].outDegree\n  }\n  return 0\n}\n", "import { IAlgorithmCallbacks, GraphData } from './types';\nimport { getNeighbors } from './util';\n\nfunction initCallbacks(callbacks: IAlgorithmCallbacks = {} as IAlgorithmCallbacks) {\n  const initiatedCallback = callbacks;\n\n  const stubCallback = () => {};\n\n  const allowTraversalCallback = (() => {\n    const seen = {};\n    return ({ next }) => {\n      if (!seen[next]) {\n        seen[next] = true;\n        return true;\n      }\n      return false;\n    };\n  })();\n\n  initiatedCallback.allowTraversal = callbacks.allowTraversal || allowTraversalCallback;\n  initiatedCallback.enter = callbacks.enter || stubCallback;\n  initiatedCallback.leave = callbacks.leave || stubCallback;\n\n  return initiatedCallback;\n}\n\n/**\n * @param {Graph} graph\n * @param {GraphNode} currentNode\n * @param {GraphNode} previousNode\n * @param {Callbacks} callbacks\n */\nfunction depthFirstSearchRecursive(\n  graphData: GraphData,\n  currentNode: string,\n  previousNode: string,\n  callbacks: IAlgorithmCallbacks,\n  directed: boolean = true,\n) {\n  callbacks.enter({\n    current: currentNode,\n    previous: previousNode,\n  });\n\n  const { edges = [] } = graphData;\n\n  getNeighbors(currentNode, edges, directed ? 'target' : undefined).forEach((nextNode) => {\n    if (\n      callbacks.allowTraversal({\n        previous: previousNode,\n        current: currentNode,\n        next: nextNode,\n      })\n    ) {\n      depthFirstSearchRecursive(graphData, nextNode, currentNode, callbacks, directed);\n    }\n  });\n\n  callbacks.leave({\n    current: currentNode,\n    previous: previousNode,\n  });\n}\n\n/**\n * 深度优先遍历图\n * @param data GraphData 图数据\n * @param startNodeId 开始遍历的节点的 ID\n * @param originalCallbacks 回调\n */\nexport default function depthFirstSearch(\n  graphData: GraphData,\n  startNodeId: string,\n  callbacks?: IAlgorithmCallbacks,\n  directed: boolean = true,\n) {\n  depthFirstSearchRecursive(graphData, startNodeId, '', initCallbacks(callbacks), directed);\n}\n", "import dfs from './dfs';\nimport getConnectedComponents, { detectStrongConnectComponents } from './connected-component';\nimport { GraphData, IAlgorithmCallbacks, NodeConfig } from './types';\nimport { getNeighbors } from './util';\n\nconst detectDirectedCycle = (graphData: GraphData): {\n  [key: string]: string;\n} => {\n  let cycle: {\n    [key: string]: string;\n  } = null;\n\n  const { nodes = [] } = graphData\n\n  const dfsParentMap = {};\n\n  // 所有没有被访问的节点集合\n  const unvisitedSet = {};\n\n  // 正在被访问的节点集合\n  const visitingSet = {};\n\n  // 所有已经被访问过的节点集合\n  const visitedSet = {};\n\n  // 初始化 unvisitedSet\n  nodes.forEach((node) => {\n    unvisitedSet[node.id] = node;\n  });\n\n  const callbacks: IAlgorithmCallbacks = {\n    enter: ({ current: currentNode, previous: previousNode }) => {\n      if (visitingSet[currentNode]) {\n        // 如果当前节点正在访问中，则说明检测到环路了\n        cycle = {};\n\n        let currentCycleNode = currentNode;\n        let previousCycleNode = previousNode;\n\n        while (previousCycleNode !== currentNode) {\n          cycle[currentCycleNode] = previousCycleNode;\n          currentCycleNode = previousCycleNode;\n          previousCycleNode = dfsParentMap[previousCycleNode];\n        }\n\n        cycle[currentCycleNode] = previousCycleNode;\n      } else {\n        // 如果不存在正在访问集合中，则将其放入正在访问集合，并从未访问集合中删除\n        visitingSet[currentNode] = currentNode;\n        delete unvisitedSet[currentNode];\n\n        // 更新 DSF parents 列表\n        dfsParentMap[currentNode] = previousNode;\n      }\n    },\n    leave: ({ current: currentNode }) => {\n      // 如果所有的节点的子节点都已经访问过了，则从正在访问集合中删除掉，并将其移入到已访问集合中，\n      // 同时也意味着当前节点的所有邻居节点都被访问过了\n      visitedSet[currentNode] = currentNode;\n      delete visitingSet[currentNode];\n    },\n    allowTraversal: ({ next: nextNode }) => {\n      // 如果检测到环路则需要终止所有进一步的遍历，否则会导致无限循环遍历\n      if (cycle) {\n        return false;\n      }\n\n      // 仅允许遍历没有访问的节点，visitedSet 中的都已经访问过了\n      return !visitedSet[nextNode];\n    },\n  };\n\n  // 开始遍历节点\n  while (Object.keys(unvisitedSet).length) {\n    // 从第一个节点开始进行 DFS 遍历\n    const firsetUnVisitedKey = Object.keys(unvisitedSet)[0];\n\n    dfs(graphData, firsetUnVisitedKey, callbacks);\n  }\n\n  return cycle;\n};\n\n/**\n * 检测无向图中的所有Base cycles\n * refer: https://www.codeproject.com/Articles/1158232/Enumerating-All-Cycles-in-an-Undirected-Graph\n * @param graph\n * @param nodeIds 节点 ID 的数组\n * @param include 包含或排除指定的节点\n * @return [{[key: string]: INode}] 返回一组base cycles\n */\nexport const detectAllUndirectedCycle = (graphData: GraphData, nodeIds?: string[], include = true) => {\n  const allCycles = [];\n  const components = getConnectedComponents(graphData, false);\n\n  // loop through all connected components\n  for (const component of components) {\n    if (!component.length) continue;\n    const root = component[0];\n    const rootId = root.id;\n\n    const stack = [root];\n    const parent = { [rootId]: root };\n    const used = { [rootId]: new Set() };\n\n    // walk a spanning tree to find cycles\n    while (stack.length > 0) {\n      const curNode = stack.pop();\n      const curNodeId = curNode.id;\n      const neighbors = getNeighbors(curNodeId, graphData.edges);\n      for (let i = 0; i < neighbors.length; i += 1) {\n        const neighborId = neighbors[i];\n        const neighbor = graphData.nodes.find(node => node.id === neighborId)\n        // const neighborId = neighbor.get('id');\n        if (neighborId === curNodeId) {\n          // 自环\n          allCycles.push({ [neighborId]: curNode });\n        } else if (!(neighborId in used)) {\n          // visit a new node\n          parent[neighborId] = curNode;\n          stack.push(neighbor);\n          used[neighborId] = new Set([curNode]);\n        } else if (!used[curNodeId].has(neighbor)) {\n          // a cycle found\n          let cycleValid = true;\n          const cyclePath = [neighbor, curNode];\n          let p = parent[curNodeId];\n          while (used[neighborId].size && !used[neighborId].has(p)) {\n            cyclePath.push(p);\n            if (p === parent[p.id]) break;\n            else p = parent[p.id];\n          }\n          cyclePath.push(p);\n\n          if (nodeIds && include) {\n            // 如果有指定包含的节点\n            cycleValid = false;\n            if (cyclePath.findIndex((node) => nodeIds.indexOf(node.id) > -1) > -1) {\n              cycleValid = true;\n            }\n          } else if (nodeIds && !include) {\n            // 如果有指定不包含的节点\n            if (cyclePath.findIndex((node) => nodeIds.indexOf(node.id) > -1) > -1) {\n              cycleValid = false;\n            }\n          }\n\n          // 把 node list 形式转换为 cycle 的格式\n          if (cycleValid) {\n            const cycle = {};\n            for (let index = 1; index < cyclePath.length; index += 1) {\n              cycle[cyclePath[index - 1].id] = cyclePath[index];\n            }\n            if (cyclePath.length) {\n              cycle[cyclePath[cyclePath.length - 1].id] = cyclePath[0];\n            }\n            allCycles.push(cycle);\n          }\n\n          used[neighborId].add(curNode);\n        }\n      }\n    }\n  }\n\n  return allCycles;\n};\n\n/**\n * Johnson's algorithm, 时间复杂度 O((V + E)(C + 1))$ and space bounded by O(V + E)\n * refer: https://www.cs.tufts.edu/comp/150GA/homeworks/hw1/Johnson%2075.PDF\n * refer: https://networkx.github.io/documentation/stable/_modules/networkx/algorithms/cycles.html#simple_cycles\n * @param graph\n * @param nodeIds 节点 ID 的数组\n * @param include 包含或排除指定的节点\n * @return [{[key: string]: INode}] 返回所有的 simple cycles\n */\nexport const detectAllDirectedCycle = (graphData: GraphData, nodeIds?: string[], include = true) => {\n  const path = []; // stack of nodes in current path\n  const blocked = new Set();\n  const B = []; // remember portions of the graph that yield no elementary circuit\n  const allCycles = [];\n  const idx2Node: {\n    [key: string]: NodeConfig;\n  } = {};\n  const node2Idx = {};\n\n  // 辅助函数： unblock all blocked nodes\n  const unblock = (thisNode: NodeConfig) => {\n    const stack = [thisNode];\n    while (stack.length > 0) {\n      const node = stack.pop();\n      if (blocked.has(node)) {\n        blocked.delete(node);\n        B[node.id].forEach((n) => {\n          stack.push(n);\n        });\n        B[node.id].clear();\n      }\n    }\n  };\n\n  const circuit = (node: NodeConfig, start: NodeConfig, adjList) => {\n    let closed = false; // whether a path is closed\n    if (nodeIds && include === false && nodeIds.indexOf(node.id) > -1) return closed;\n    path.push(node);\n    blocked.add(node);\n\n    const neighbors = adjList[node.id];\n    for (let i = 0; i < neighbors.length; i += 1) {\n      const neighbor = idx2Node[neighbors[i]];\n      if (neighbor === start) {\n        const cycle = {};\n        for (let index = 1; index < path.length; index += 1) {\n          cycle[path[index - 1].id] = path[index];\n        }\n        if (path.length) {\n          cycle[path[path.length - 1].id] = path[0];\n        }\n        allCycles.push(cycle);\n        closed = true;\n      } else if (!blocked.has(neighbor)) {\n        if (circuit(neighbor, start, adjList)) {\n          closed = true;\n        }\n      }\n    }\n\n    if (closed) {\n      unblock(node);\n    } else {\n      for (let i = 0; i < neighbors.length; i += 1) {\n        const neighbor = idx2Node[neighbors[i]];\n        if (!B[neighbor.id].has(node)) {\n          B[neighbor.id].add(node);\n        }\n      }\n    }\n    path.pop();\n    return closed;\n  };\n\n  const { nodes = [] } = graphData;\n\n  // Johnson's algorithm 要求给节点赋顺序，先按节点在数组中的顺序\n  for (let i = 0; i < nodes.length; i += 1) {\n    const node = nodes[i];\n    const nodeId = node.id;\n    node2Idx[nodeId] = i;\n    idx2Node[i] = node;\n  }\n  // 如果有指定包含的节点，则把指定节点排序在前，以便提早结束搜索\n  if (nodeIds && include) {\n    for (let i = 0; i < nodeIds.length; i++) {\n      const nodeId = nodeIds[i];\n      node2Idx[nodes[i].id] = node2Idx[nodeId];\n      node2Idx[nodeId] = 0;\n      idx2Node[0] = nodes.find(node => node.id === nodeId);\n      idx2Node[node2Idx[nodes[i].id]] = nodes[i];\n    }\n  }\n\n  // 返回 节点顺序 >= nodeOrder 的强连通分量的adjList\n  const getMinComponentAdj = (components: NodeConfig[][]) => {\n    let minCompIdx;\n    let minIdx = Infinity;\n\n    // Find least component and the lowest node\n    for (let i = 0; i < components.length; i += 1) {\n      const comp = components[i];\n      for (let j = 0; j < comp.length; j++) {\n        const nodeIdx = node2Idx[comp[j].id];\n        if (nodeIdx < minIdx) {\n          minIdx = nodeIdx;\n          minCompIdx = i;\n        }\n      }\n    }\n\n    const component = components[minCompIdx];\n    const adjList = [];\n    for (let i = 0; i < component.length; i += 1) {\n      const node = component[i];\n      adjList[node.id] = [];\n      for (const neighbor of getNeighbors(node.id, graphData.edges, 'target').filter((n) => component.map(c => c.id).indexOf(n) > -1)) {\n        // 对自环情况 (点连向自身) 特殊处理：记录自环，但不加入adjList\n        if (neighbor === node.id && !(include === false && nodeIds.indexOf(node.id) > -1)) {\n          allCycles.push({ [node.id]: node });\n        } else {\n          adjList[node.id].push(node2Idx[neighbor]);\n        }\n      }\n    }\n\n    return {\n      component,\n      adjList,\n      minIdx,\n    };\n  };\n\n  let nodeIdx = 0;\n  while (nodeIdx < nodes.length) {\n    const subgraphNodes = nodes.filter((n) => node2Idx[n.id] >= nodeIdx);\n    const sccs = detectStrongConnectComponents({ nodes: subgraphNodes, edges: graphData.edges }).filter(\n      (component) => component.length > 1,\n    );\n    if (sccs.length === 0) break;\n\n    const scc = getMinComponentAdj(sccs);\n    const { minIdx, adjList, component } = scc;\n    if (component.length > 1) {\n      component.forEach((node) => {\n        B[node.id] = new Set();\n      });\n      const startNode = idx2Node[minIdx];\n      // startNode 不在指定要包含的节点中，提前结束搜索\n      if (nodeIds && include && nodeIds.indexOf(startNode.id) === -1) return allCycles;\n      circuit(startNode, startNode, adjList);\n      nodeIdx = minIdx + 1;\n    } else {\n      break;\n    }\n  }\n  return allCycles;\n};\n\n/**\n * 查找图中所有满足要求的圈\n * @param graph\n * @param directed 是否为有向图\n * @param nodeIds 节点 ID 的数组，若不指定，则返回图中所有的圈\n * @param include 包含或排除指定的节点\n * @return [{[key: string]: Node}] 包含所有环的数组，每个环用一个Object表示，其中key为节点id，value为该节点在环中指向的下一个节点\n */\nexport const detectAllCycles = (\n  graphData: GraphData,\n  directed?: boolean,\n  nodeIds?: string[],\n  include = true,\n) => {\n  if (directed) return detectAllDirectedCycle(graphData, nodeIds, include);\n  return detectAllUndirectedCycle(graphData, nodeIds, include);\n};\n\nexport default detectDirectedCycle;\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n  return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose;\n    if (async) {\n        if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n        dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n        if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n        dispose = value[Symbol.dispose];\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  function next() {\n    while (env.stack.length) {\n      var rec = env.stack.pop();\n      try {\n        var result = rec.dispose && rec.dispose.call(rec.value);\n        if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n      }\n      catch (e) {\n          fail(e);\n      }\n    }\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n};\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "import { isArray } from '@antv/util';\nimport { GraphData, NodeConfig, EdgeConfig } from './types';\nimport { getOutEdgesNodeId, getEdgesByNodeId } from './util';\n\nconst minVertex = (\n  D: { [key: string]: number },\n  nodes: NodeConfig[],\n  marks: { [key: string]: boolean },\n): NodeConfig => {\n  // 找出最小的点\n  let minDis = Infinity;\n  let minNode;\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeId = nodes[i].id;\n    if (!marks[nodeId] && D[nodeId] <= minDis) {\n      minDis = D[nodeId];\n      minNode = nodes[i];\n    }\n  }\n  return minNode;\n};\n\nconst dijkstra = (\n  graphData: GraphData,\n  source: string,\n  directed?: boolean,\n  weightPropertyName?: string,\n) => {\n  const { nodes = [], edges = [] } = graphData;\n  const nodeIds = [];\n  const marks = {};\n  const D = {};\n  const prevs = {}; // key: 顶点, value: 顶点的前驱点数组（可能有多条等长的最短路径）\n  nodes.forEach((node, i) => {\n    const id = node.id;\n    nodeIds.push(id);\n    D[id] = Infinity;\n    if (id === source) D[id] = 0;\n  });\n\n  const nodeNum = nodes.length;\n  for (let i = 0; i < nodeNum; i++) {\n    // Process the vertices\n    const minNode = minVertex(D, nodes, marks);\n    const minNodeId = minNode.id;\n    marks[minNodeId] = true;\n\n    if (D[minNodeId] === Infinity) continue; // Unreachable vertices cannot be the intermediate point\n\n    let relatedEdges: EdgeConfig[] = [];\n    if (directed) relatedEdges = getOutEdgesNodeId(minNodeId, edges);\n    else relatedEdges = getEdgesByNodeId(minNodeId, edges);\n\n    relatedEdges.forEach(edge => {\n      const edgeTarget = edge.target;\n      const edgeSource = edge.source;\n      const w = edgeTarget === minNodeId ? edgeSource : edgeTarget;\n      const weight = weightPropertyName && edge[weightPropertyName] ? edge[weightPropertyName] : 1;\n      if (D[w] > D[minNode.id] + weight) {\n        D[w] = D[minNode.id] + weight;\n        prevs[w] = [minNode.id];\n      } else if (D[w] === D[minNode.id] + weight) {\n        prevs[w].push(minNode.id);\n      }\n    });\n  }\n\n  prevs[source] = [source];\n  // 每个节点存可能存在多条最短路径\n  const paths = {};\n  for (const target in D) {\n    if (D[target] !== Infinity) {\n      findAllPaths(source, target, prevs, paths);\n    }\n  }\n\n  // 兼容之前单路径\n  const path = {};\n  for (const target in paths) {\n    path[target] = paths[target][0];\n  }\n  return { length: D, path, allPath: paths };\n};\n\nexport default dijkstra;\n\nfunction findAllPaths(source, target, prevs, foundPaths) {\n  if (source === target) {\n    return [source];\n  }\n  if (foundPaths[target]) {\n    return foundPaths[target];\n  }\n  const paths = [];\n  for (let prev of prevs[target]) {\n    const prevPaths = findAllPaths(source, prev, prevs, foundPaths);\n    if (!prevPaths) return;\n    for (let prePath of prevPaths) {\n      if (isArray(prePath)) paths.push([...prePath, target]);\n      else paths.push([prePath, target]);\n    }\n  }\n  foundPaths[target] = paths;\n  return foundPaths[target];\n}\n", "import dijkstra from './dijkstra';\nimport { GraphData } from './types';\nimport { getNeighbors } from './util';\n\nexport const findShortestPath = (\n  graphData: GraphData,\n  start: string,\n  end: string,\n  directed?: boolean,\n  weightPropertyName?: string\n) => {\n  const { length, path, allPath } = dijkstra(\n    graphData,\n    start,\n    directed,\n    weightPropertyName\n  );\n  return { length: length[end], path: path[end], allPath: allPath[end] };\n};\n\nexport const findAllPath = (\n  graphData: GraphData,\n  start: string,\n  end: string,\n  directed?: boolean\n) => {\n  if (start === end) return [[start]];\n\n  const { edges = [] } = graphData;\n\n  const visited = [start];\n  const isVisited = { [start]: true };\n  const stack: string[][] = []; // 辅助栈，用于存储访问过的节点的邻居节点\n  const allPath = [];\n  let neighbors = directed\n    ? getNeighbors(start, edges, 'target')\n    : getNeighbors(start, edges);\n  stack.push(neighbors);\n\n  while (visited.length > 0 && stack.length > 0) {\n    const children = stack[stack.length - 1];\n    if (children.length) {\n      const child = children.shift();\n      if (child) {\n        visited.push(child);\n        isVisited[child] = true;\n        neighbors = directed\n          ? getNeighbors(child, edges, 'target')\n          : getNeighbors(child, edges);\n        stack.push(neighbors.filter(neighbor => !isVisited[neighbor]));\n      }\n    } else {\n      const node = visited.pop();\n      isVisited[node] = false;\n      stack.pop();\n      continue;\n    }\n\n    if (visited[visited.length - 1] === end) {\n      const path = visited.map(node => node);\n      allPath.push(path);\n\n      const node = visited.pop();\n      isVisited[node] = false;\n      stack.pop();\n    }\n  }\n\n  return allPath;\n};\n", "import getAdjMatrix from \"./adjacent-matrix\";\nimport { GraphData, Matrix } from \"./types\";\n\nconst floydWarshall = (graphData: GraphData, directed?: boolean) => {\n  const adjacentMatrix = getAdjMatrix(graphData, directed);\n\n  const dist: Matrix[] = [];\n  const size = adjacentMatrix.length;\n  for (let i = 0; i < size; i += 1) {\n    dist[i] = [];\n    for (let j = 0; j < size; j += 1) {\n      if (i === j) {\n        dist[i][j] = 0;\n      } else if (adjacentMatrix[i][j] === 0 || !adjacentMatrix[i][j]) {\n        dist[i][j] = Infinity;\n      } else {\n        dist[i][j] = adjacentMatrix[i][j];\n      }\n    }\n  }\n  // floyd\n  for (let k = 0; k < size; k += 1) {\n    for (let i = 0; i < size; i += 1) {\n      for (let j = 0; j < size; j += 1) {\n        if (dist[i][j] > dist[i][k] + dist[k][j]) {\n          dist[i][j] = dist[i][k] + dist[k][j];\n        }\n      }\n    }\n  }\n  return dist;\n};\n\nexport default floydWarshall;\n", "\nimport getAdjMatrix from './adjacent-matrix'\nimport { uniqueId } from './util';\nimport { GraphData, ClusterData } from './types';\n\n/**\n * 标签传播算法\n * @param graphData 图数据\n * @param directed 是否有向图，默认为 false\n * @param weightPropertyName 权重的属性字段\n * @param maxIteration 最大迭代次数\n */\nconst labelPropagation = (\n  graphData: GraphData,\n  directed: boolean = false,\n  weightPropertyName: string = 'weight',\n  maxIteration: number = 1000\n): ClusterData => {\n  // the origin data\n  const { nodes = [], edges = [] } = graphData;\n\n  const clusters = {};\n  const nodeMap = {};\n  // init the clusters and nodeMap\n  nodes.forEach((node, i) => {\n    const cid: string = uniqueId();\n    node.clusterId = cid;\n    clusters[cid] = {\n      id: cid,\n      nodes: [node]\n    };\n    nodeMap[node.id] = {\n      node,\n      idx: i\n    };\n  });\n\n  // the adjacent matrix of calNodes inside clusters\n  const adjMatrix = getAdjMatrix(graphData, directed);\n  // the sum of each row in adjacent matrix\n  const ks = [];\n  /**\n   * neighbor nodes (id for key and weight for value) for each node\n   * neighbors = {\n   *  id(node_id): { id(neighbor_1_id): weight(weight of the edge), id(neighbor_2_id): weight(weight of the edge), ... },\n   *  ...\n   * }\n   */\n  const neighbors = {};\n  adjMatrix.forEach((row, i) => {\n    let k = 0;\n    const iid = nodes[i].id;\n    neighbors[iid] = {};\n    row.forEach((entry, j) => {\n      if (!entry) return;\n      k += entry;\n      const jid = nodes[j].id;\n      neighbors[iid][jid] = entry;\n    });\n    ks.push(k);\n  });\n\n  let iter = 0;\n\n  while (iter < maxIteration) {\n    let changed = false;\n    nodes.forEach(node => {\n      const neighborClusters = {};\n      Object.keys(neighbors[node.id]).forEach(neighborId => {\n        const neighborWeight = neighbors[node.id][neighborId];\n        const neighborNode = nodeMap[neighborId].node;\n        const neighborClusterId = neighborNode.clusterId;\n        if (!neighborClusters[neighborClusterId]) neighborClusters[neighborClusterId] = 0;\n        neighborClusters[neighborClusterId] += neighborWeight;\n      });\n      // find the cluster with max weight\n      let maxWeight = -Infinity;\n      let bestClusterIds = [];\n      Object.keys(neighborClusters).forEach(clusterId => {\n        if (maxWeight < neighborClusters[clusterId]) {\n          maxWeight = neighborClusters[clusterId];\n          bestClusterIds = [clusterId];\n        } else if (maxWeight === neighborClusters[clusterId]) {\n          bestClusterIds.push(clusterId);\n        }\n      });\n      if (bestClusterIds.length === 1 && bestClusterIds[0] === node.clusterId) return;\n      const selfClusterIdx = bestClusterIds.indexOf(node.clusterId);\n      if (selfClusterIdx >= 0) bestClusterIds.splice(selfClusterIdx, 1);\n      if (bestClusterIds && bestClusterIds.length) {\n        changed = true;\n\n        // remove from origin cluster\n        const selfCluster = clusters[node.clusterId as string];\n        const nodeInSelfClusterIdx = selfCluster.nodes.indexOf(node);\n        selfCluster.nodes.splice(nodeInSelfClusterIdx, 1);\n\n        // move the node to the best cluster\n        const randomIdx = Math.floor(Math.random() * bestClusterIds.length);\n        const bestCluster = clusters[bestClusterIds[randomIdx]];\n        bestCluster.nodes.push(node);\n        node.clusterId = bestCluster.id;\n      }\n    });\n    if (!changed) break;\n    iter++;\n  }\n\n  // delete the empty clusters\n  Object.keys(clusters).forEach(clusterId => {\n    const cluster = clusters[clusterId];\n    if (!cluster.nodes || !cluster.nodes.length) {\n      delete clusters[clusterId];\n    }\n  });\n\n  // get the cluster edges\n  const clusterEdges = [];\n  const clusterEdgeMap = {};\n  edges.forEach(edge => {\n    const { source, target } = edge;\n    const weight = edge[weightPropertyName] || 1;\n    const sourceClusterId = nodeMap[source].node.clusterId;\n    const targetClusterId = nodeMap[target].node.clusterId;\n    const newEdgeId = `${sourceClusterId}---${targetClusterId}`;\n    if (clusterEdgeMap[newEdgeId]) {\n      clusterEdgeMap[newEdgeId].weight += weight;\n      clusterEdgeMap[newEdgeId].count++;\n    } else {\n      const newEdge = {\n        source: sourceClusterId,\n        target: targetClusterId,\n        weight,\n        count: 1\n      };\n      clusterEdgeMap[newEdgeId] = newEdge;\n      clusterEdges.push(newEdge);\n    }\n  });\n\n  const clustersArray = [];\n  Object.keys(clusters).forEach(clusterId => {\n    clustersArray.push(clusters[clusterId]);\n  });\n  return {\n    clusters: clustersArray,\n    clusterEdges\n  }\n}\n\nexport default labelPropagation;\n", "\n/**\n * 向量运算\n */\nimport { clone } from '@antv/util';\n\nclass Vector {\n  arr: number[];\n\n  constructor(arr) {\n    this.arr = arr;\n  }\n\n  getArr() {\n    return this.arr || [];\n  }\n\n  add(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length) {\n      return new Vector(otherArr);\n    }\n    if (!otherArr?.length) {\n      return new Vector(this.arr);\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = [];\n      for (let index in this.arr) {\n        res[index] = this.arr[index] + otherArr[index];\n      }\n      return new Vector(res);\n    }\n  }\n\n  subtract(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length) {\n      return new Vector(otherArr);\n    }\n    if (!otherArr?.length) {\n      return new Vector(this.arr);\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = [];\n      for (let index in this.arr) {\n        res[index] = this.arr[index] - otherArr[index];\n      }\n      return new Vector(res);\n    }\n  }\n\n  avg(length) {\n    let res = [];\n    if (length !== 0) {\n      for (let index in this.arr) {\n        res[index] = this.arr[index] / length;\n      }\n    }\n    return new Vector(res);\n  }\n\n  negate() {\n    let res = [];\n    for (let index in this.arr) {\n      res[index] = - this.arr[index];\n    }\n    return new Vector(res);\n  }\n\n  // 平方欧式距离\n  squareEuclideanDistance(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length || !otherArr?.length) {\n      return 0;\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = 0;\n      for (let index in this.arr) {\n        res += Math.pow(this.arr[index] - otherVector.arr[index], 2);\n      }\n      return res;\n    }\n  }\n\n  // 欧式距离\n  euclideanDistance(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length || !otherArr?.length) {\n      return 0;\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = 0;\n      for (let index in this.arr) {\n        res += Math.pow(this.arr[index] - otherVector.arr[index], 2);\n      }\n      return Math.sqrt(res);\n    } else {\n      console.error('The two vectors are unequal in length.')\n    }\n  }\n\n  // 归一化处理\n  normalize() {\n    let res = [];\n    const cloneArr = clone(this.arr);\n    cloneArr.sort((a, b) => a - b);\n    const max = cloneArr[cloneArr.length - 1];\n    const min = cloneArr[0];\n    for (let index in this.arr) {\n      res[index] = (this.arr[index] - min) / (max - min);\n    }\n    return new Vector(res);\n  }\n\n  // 2范数 or 模长\n  norm2() {\n    if (!this.arr?.length) {\n      return 0;\n    }\n    let res = 0;\n      for (let index in this.arr) {\n        res += Math.pow(this.arr[index], 2);\n      }\n    return Math.sqrt(res);\n  }\n\n  // 两个向量的点积\n  dot(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length || !otherArr?.length) {\n      return 0;\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = 0;\n      for (let index in this.arr) {\n        res += this.arr[index] * otherVector.arr[index];\n      }\n      return res;\n    } else {\n      console.error('The two vectors are unequal in length.')\n    }\n  }\n\n  // 两个向量比较\n  equal(otherVector) {\n    const otherArr = otherVector.arr;\n    if (this.arr?.length !== otherArr?.length) {\n      return false;\n    }\n    for (let index in this.arr) {\n      if (this.arr[index] !== otherArr[index]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n\nexport default Vector;\n", "export const secondReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2})$/;\nexport const dateReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2}) (\\d{1,2}):(\\d{1,2}):(\\d{1,2})$/;\n", "\nexport type Matrix = number[];\n\nexport interface NodeConfig {\n  id: string;\n  clusterId?: string;\n  [key: string]: any;\n}\n\nexport interface EdgeConfig {\n  source: string;\n  target: string;\n  weight?: number;\n  [key: string]: any;\n}\n\nexport interface GraphData {\n  nodes?: NodeConfig[];\n  edges?: EdgeConfig[];\n}\n\nexport interface Cluster {\n  id: string;\n  nodes: NodeConfig[];\n  sumTot?: number;\n}\n\nexport interface ClusterData {\n  clusters: Cluster[];\n  clusterEdges: EdgeConfig[];\n}\n\nexport interface ClusterMap {\n  [key: string]: Cluster\n}\n\n// 图算法回调方法接口定义\nexport interface IAlgorithmCallbacks {\n  enter?: (param: { current: string; previous: string }) => void;\n  leave?: (param: { current: string; previous?: string }) => void;\n  allowTraversal?: (param: { previous?: string; current?: string; next: string }) => boolean;\n}\n\nexport interface DegreeType {\n  [key: string]: {\n    degree: number;\n    inDegree: number;\n    outDegree: number;\n  }\n}\n\nexport enum DistanceType {\n  EuclideanDistance = 'euclideanDistance',\n}\n\nexport interface PlainObject {\n  [key: string]: any;\n}\n\n// 数据集中属性/特征值分布的map\nexport interface KeyValueMap {\n  [key:string]: any[];\n}\n\nexport interface IAlgorithm {\n  getAdjMatrix: (graphData: GraphData, directed?: boolean) => Matrix[],\n  breadthFirstSearch: (\n    graphData: GraphData,\n    startNodeId: string,\n    originalCallbacks?: IAlgorithmCallbacks,\n    directed?: boolean\n  ) => void,\n  connectedComponent: (graphData: GraphData, directed?: boolean) => NodeConfig[][],\n  getDegree: (graphData: GraphData) => DegreeType,\n  getInDegree: (graphData: GraphData, nodeId: string) => number,\n  getOutDegree: (graphData: GraphData, nodeId: string) => number,\n  detectCycle: (graphData: GraphData) => { [key: string]: string },\n  detectDirectedCycle: (graphData: GraphData) => { [key: string]: string },\n  detectAllCycles: (graphData: GraphData, directed?: boolean, nodeIds?: string[], include?: boolean) => any,\n  detectAllDirectedCycle: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  detectAllUndirectedCycle: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  depthFirstSearch: (graphData: GraphData, startNodeId: string, callbacks?: IAlgorithmCallbacks) => void,\n  dijkstra: (graphData: GraphData, source: string, directed?: boolean, weightPropertyName?: string) => { length: object, allPath: object, path: object},\n  findAllPath: (graphData: GraphData, start: string, end: string, directed?: boolean) => any,\n  findShortestPath: (graphData: GraphData, start: string, end: string, directed?: boolean, weightPropertyName?: string) => any,\n  floydWarshall: (graphData: GraphData, directed?: boolean) => Matrix[],\n  labelPropagation: (graphData: GraphData, directed?: boolean, weightPropertyName?: string, maxIteration?: number) => ClusterData,\n  louvain: (graphData: GraphData, directed: boolean, weightPropertyName: string, threshold: number) => ClusterData,\n  minimumSpanningTree: (graphData: GraphData, weight?: string, algo?: string) => EdgeConfig[],\n  pageRank: (graphData: GraphData, epsilon?: number, linkProb?: number) => { [key: string]: number},\n  getNeighbors: (nodeId: string, edges?: EdgeConfig[], type?: 'target' | 'source' | undefined) => string[],\n  Stack: any,\n  GADDI: (graphData: GraphData, pattern: GraphData, directed: boolean, k: number, length: number, nodeLabelProp: string, edgeLabelProp: string) => GraphData[],\n  getAdjMatrixAsync: (graphData: GraphData, directed?: boolean) => Matrix[],\n  connectedComponentAsync: (graphData: GraphData, directed?: boolean) => NodeConfig[][],\n  getDegreeAsync: (graphData: GraphData) => DegreeType,\n  getInDegreeAsync: (graphData: GraphData, nodeId: string) => number,\n  getOutDegreeAsync: (graphData: GraphData, nodeId: string) => number,\n  detectCycleAsync: (graphData: GraphData) => { [key: string]: string },\n  detectDirectedCycleAsync: (graphData: GraphData) => { [key: string]: string },\n  detectAllCyclesAsync: (graphData: GraphData, directed?: boolean, nodeIds?: string[], include?: boolean) => any,\n  detectAllDirectedCycleAsync: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  detectAllUndirectedCycleAsync: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  dijkstraAsync: (graphData: GraphData, source: string, directed?: boolean, weightPropertyName?: string) => { length: object, allPath: object, path: object},\n  findAllPathAsync: (graphData: GraphData, start: string, end: string, directed?: boolean) => any,\n  findShortestPathAsync: (graphData: GraphData, start: string, end: string, directed?: boolean, weightPropertyName?: string) => any,\n  floydWarshallAsync: (graphData: GraphData, directed?: boolean) => Matrix[],\n  labelPropagationAsync: (graphData: GraphData, directed?: boolean, weightPropertyName?: string, maxIteration?: number) => ClusterData,\n  louvainAsync: (graphData: GraphData, directed: boolean, weightPropertyName: string, threshold: number) => ClusterData,\n  minimumSpanningTreeAsync: (graphData: GraphData, weight?: string, algo?: string) => EdgeConfig[],\n  pageRankAsync: (graphData: GraphData, epsilon?: number, linkProb?: number) => { [key: string]: number},\n  getNeighborsAsync: (nodeId: string, edges?: EdgeConfig[], type?: 'target' | 'source' | undefined) => string[],\n  GADDIAsync: (graphData: GraphData, pattern: GraphData, directed: boolean, k: number, length: number, nodeLabelProp: string, edgeLabelProp: string) => GraphData[],\n}", "import { uniq } from '@antv/util';\nimport { PlainObject, DistanceType, GraphData, KeyValueMap } from '../types';\nimport Vector from './vector';\n\n/**\n * 获取数据中所有的属性及其对应的值\n * @param dataList 数据集\n * @param involvedKeys 参与计算的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n */\nexport const getAllKeyValueMap = (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => {\n  let keys = [];\n  // 指定了参与计算的keys时，使用指定的keys\n  if (involvedKeys?.length) {\n    keys = involvedKeys;\n  } else {\n    // 未指定抽取的keys时，提取数据中所有的key\n    dataList.forEach(data => {\n      keys = keys.concat(Object.keys(data));\n    })\n    keys = uniq(keys);\n  }\n  // 获取所有值非空的key的value数组\n  const allKeyValueMap: KeyValueMap = {};\n  keys.forEach(key => {\n    let value = [];\n    dataList.forEach(data => {\n      if (data[key] !== undefined && data[key] !== '') {\n        value.push(data[key]);\n      }\n    })\n    if (value.length && !uninvolvedKeys?.includes(key)) {\n      allKeyValueMap[key] = uniq(value);\n    }\n  })\n\n  return allKeyValueMap;\n}\n\n/**\n * one-hot编码：数据特征提取\n * @param dataList 数据集\n * @param involvedKeys 参与计算的的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n */\nexport const oneHot = (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => {\n  // 获取数据中所有的属性/特征及其对应的值\n  const allKeyValueMap = getAllKeyValueMap(dataList, involvedKeys, uninvolvedKeys);\n  const oneHotCode = [];\n  if (!Object.keys(allKeyValueMap).length) {\n    return oneHotCode;\n  }\n\n  // 获取所有的属性/特征值\n  const allValue = Object.values(allKeyValueMap);\n  // 是否所有属性/特征的值都是数值型\n  const isAllNumber = allValue.every(value => value.every(item => (typeof(item) === 'number')));\n\n  // 对数据进行one-hot编码\n  dataList.forEach((data, index) => {\n    let code = [];\n    Object.keys(allKeyValueMap).forEach(key => {\n      const keyValue = data[key];\n      const allKeyValue = allKeyValueMap[key];\n      const valueIndex = allKeyValue.findIndex(value => keyValue === value);\n      let subCode = [];\n      // 如果属性/特征所有的值都能转成数值型，不满足分箱，则直接用值（todo: 为了收敛更快，需做归一化处理）\n      if (isAllNumber) {\n        subCode.push(keyValue);\n      } else {\n        // 进行one-hot编码\n        for(let i = 0; i < allKeyValue.length; i++) {\n          if (i === valueIndex) {\n            subCode.push(1);\n          } else {\n            subCode.push(0);\n          }\n        }\n      }\n      code = code.concat(subCode);\n    })\n    oneHotCode[index] = code;\n  })\n  return oneHotCode;\n}\n\n/**\n * getDistance：获取两个元素之间的距离\n * @param item\n * @param otherItem\n * @param distanceType 距离类型\n * @param graphData 图数据\n */\nexport const getDistance = (item, otherItem, distanceType: DistanceType = DistanceType.EuclideanDistance, graphData?: GraphData) => {\n  let distance = 0;\n  switch (distanceType) {\n    case DistanceType.EuclideanDistance:\n      distance = new Vector(item).euclideanDistance(new Vector(otherItem));\n      break;\n    default:\n      break;\n  }\n  return distance;\n}\n\nexport default {\n  getAllKeyValueMap,\n  oneHot,\n  getDistance,\n}\n", "import { clone } from '@antv/util';\nimport getAdjMatrix from './adjacent-matrix';\nimport { NodeConfig, ClusterData, GraphData, ClusterMap } from './types';\nimport Vector from './utils/vector';\nimport { getAllProperties } from './utils/node-properties';\nimport { oneHot } from './utils/data-preprocessing';\n\nconst getModularity = (\n  nodes: NodeConfig[],\n  adjMatrix: number[][],\n  ks: number[],\n  m: number\n) => {\n  const length = adjMatrix.length;\n  const param = 2 * m;\n  let modularity = 0;\n  for (let i = 0; i < length; i++) {\n    const clusteri = nodes[i].clusterId;\n    for (let j = 0; j < length; j++) {\n      const clusterj = nodes[j].clusterId;\n      if (clusteri !== clusterj) continue;\n      const entry = adjMatrix[i][j] || 0;\n      const ki = ks[i] || 0;\n      const kj = ks[j] || 0;\n      modularity += (entry - ki * kj / param);\n    }\n  }\n  modularity *= (1 / param);\n  return modularity;\n}\n\n// 模块惯性度，衡量属性相似度\nconst getInertialModularity = (\n  nodes: NodeConfig[] = [],\n  allPropertiesWeight: number[][],\n) => {\n  const length = nodes.length;\n  let totalProperties = new Vector([]);\n  for (let i = 0; i < length; i++) {\n    totalProperties = totalProperties.add(new Vector(allPropertiesWeight[i]));\n  }\n  // 均值向量\n  const avgProperties = totalProperties.avg(length);\n\n  avgProperties.normalize();\n  // 节点集合的方差: 节点v与均值向量的平方欧式距离之和\n  let variance: number = 0;\n  for (let i = 0; i < length; i++) {\n    const propertiesi = new Vector(allPropertiesWeight[i]);\n    const squareEuclideanDistance = propertiesi.squareEuclideanDistance(avgProperties);\n    variance += squareEuclideanDistance;\n  }\n\n  // 任意两点间的欧式平方距离\n  let squareEuclideanDistanceInfo = [];\n  nodes.forEach(() => {\n    squareEuclideanDistanceInfo.push([]);\n  });\n  for (let i = 0; i < length; i++) {\n    const propertiesi = new Vector(allPropertiesWeight[i]);\n    nodes[i]['clusterInertial'] = 0;\n    for (let j = 0; j < length; j++) {\n      if ( i === j) {\n        squareEuclideanDistanceInfo[i][j] = 0;\n        continue;\n      }\n      const propertiesj = new Vector(allPropertiesWeight[j]);\n      squareEuclideanDistanceInfo[i][j] = propertiesi.squareEuclideanDistance(propertiesj);\n      nodes[i]['clusterInertial'] += squareEuclideanDistanceInfo[i][j];\n    }\n  }\n\n  // 计算模块惯性度\n  let inertialModularity: number = 0;\n  const param = 2 * length * variance;\n  for (let i = 0; i < length; i++) {\n    const clusteri = nodes[i].clusterId;\n    for (let j = 0; j < length; j++) {\n      const clusterj = nodes[j].clusterId;\n      if ( i === j || clusteri !== clusterj) continue;\n      const inertial = (nodes[i].clusterInertial * nodes[j].clusterInertial) / Math.pow(param, 2) - squareEuclideanDistanceInfo[i][j] / param;\n      inertialModularity += inertial;\n    }\n  }\n  return Number(inertialModularity.toFixed(4));\n}\n\n\n/**\n * 社区发现 louvain 算法\n * @param graphData 图数据\n * @param directed 是否有向图，默认为 false\n * @param weightPropertyName 权重的属性字段\n * @param threshold 差值阈值\n * @param inertialModularity 是否使用惯性模块度（即节点属性相似性）\n * @param propertyKey 属性的字段名\n * @param involvedKeys 参与计算的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n * @param inertialWeight 惯性模块度权重\n */\nconst louvain = (\n  graphData: GraphData,\n  directed: boolean = false,\n  weightPropertyName: string = 'weight',\n  threshold: number = 0.0001,\n  inertialModularity: boolean = false,\n  propertyKey: string = undefined,\n  involvedKeys: string[] = [],\n  uninvolvedKeys: string[] = ['id'],\n  inertialWeight: number = 1,\n): ClusterData => {\n  // the origin data\n  const { nodes = [], edges = [] } = graphData;\n\n  let allPropertiesWeight = [];\n  if (inertialModularity) {\n    nodes.forEach((node, index) => {\n      node.properties = node.properties || {};\n      node.originIndex = index;\n    })\n  \n    let nodeTypeInfo = [];\n    if (nodes.every(node => node.hasOwnProperty('nodeType'))) {\n      nodeTypeInfo = Array.from(new Set(nodes.map(node => node.nodeType)));\n      nodes.forEach(node => {\n        node.properties.nodeType = nodeTypeInfo.findIndex(nodeType => nodeType === node.nodeType);\n      })\n    }\n    // 所有节点属性集合\n    const properties = getAllProperties(nodes, propertyKey);\n    // 所有节点属性one-hot特征向量集合\n    allPropertiesWeight = oneHot(properties, involvedKeys, uninvolvedKeys);\n  }\n \n  let uniqueId = 1;\n\n  const clusters: ClusterMap = {};\n  const nodeMap = {};\n  // init the clusters and nodeMap\n  nodes.forEach((node, i) => {\n    const cid: string = String(uniqueId++);\n    node.clusterId = cid;\n    clusters[cid] = {\n      id: cid,\n      nodes: [node]\n    };\n    nodeMap[node.id] = {\n      node,\n      idx: i\n    };\n  });\n  // the adjacent matrix of calNodes inside clusters\n  const adjMatrix = getAdjMatrix(graphData, directed);\n  // the sum of each row in adjacent matrix\n  const ks = [];\n  /**\n   * neighbor nodes (id for key and weight for value) for each node\n   * neighbors = {\n   *  id(node_id): { id(neighbor_1_id): weight(weight of the edge), id(neighbor_2_id): weight(weight of the edge), ... },\n   *  ...\n   * }\n   */\n  const neighbors = {};\n  // the sum of the weights of all edges in the graph\n  let m = 0;\n  adjMatrix.forEach((row, i) => {\n    let k = 0;\n    const iid = nodes[i].id;\n    neighbors[iid] = {};\n    row.forEach((entry, j) => {\n      if (!entry) return;\n      k += entry;\n      const jid = nodes[j].id;\n      neighbors[iid][jid] = entry;\n      m += entry;\n    });\n    ks.push(k);\n  });\n\n  m /= 2;\n\n  let totalModularity = Infinity;\n  let previousModularity = Infinity;\n  let iter = 0;\n\n  let finalNodes = [];\n  let finalClusters = {};\n  while (true) {\n    if (inertialModularity && nodes.every(node => node.hasOwnProperty('properties'))) {\n      totalModularity = getModularity(nodes, adjMatrix, ks, m) + getInertialModularity(nodes, allPropertiesWeight) * inertialWeight;\n    } else {\n      totalModularity = getModularity(nodes, adjMatrix, ks, m);\n    }\n   \n    // 第一次迭代previousModularity直接赋值\n    if (iter === 0) {\n      previousModularity = totalModularity;\n      finalNodes = nodes;\n      finalClusters = clusters;\n    }\n\n    const increaseWithinThreshold = totalModularity > 0 && totalModularity > previousModularity && totalModularity - previousModularity < threshold;\n    // 总模块度增加才更新最优解\n    if (totalModularity > previousModularity) {\n      finalNodes = nodes.map(node => ({\n        node,\n        clusterId: node.clusterId\n      }));\n      finalClusters = clone(clusters);\n      previousModularity = totalModularity;\n    }\n\n    // whether to terminate the iterations\n    if ( increaseWithinThreshold || iter > 100) {\n      break;\n    };\n    iter++;\n    // pre compute some values for current clusters\n    Object.keys(clusters).forEach(clusterId => {\n      // sum of weights of edges to nodes in cluster\n      let sumTot = 0;\n      edges.forEach(edge => {\n        const { source, target } = edge;\n        const sourceClusterId = nodeMap[source].node.clusterId;\n        const targetClusterId = nodeMap[target].node.clusterId;\n        if ((sourceClusterId === clusterId && targetClusterId !== clusterId)\n          || (targetClusterId === clusterId && sourceClusterId !== clusterId)) {\n          sumTot = sumTot + (edge[weightPropertyName] as number || 1);\n        }\n      });\n      clusters[clusterId].sumTot = sumTot;\n    });\n\n\n    // move the nodes to increase the delta modularity\n    nodes.forEach((node, i) => {\n      const selfCluster = clusters[node.clusterId as string];\n      let bestIncrease = 0;\n      let bestCluster;\n\n      const commonParam = ks[i] / (2 * m);\n\n      // sum of weights of edges from node to nodes in cluster\n      let kiin = 0;\n      const selfClusterNodes = selfCluster.nodes;\n      selfClusterNodes.forEach(scNode => {\n        const scNodeIdx = nodeMap[scNode.id].idx;\n        kiin += adjMatrix[i][scNodeIdx] || 0;\n      });\n      // the modurarity for **removing** the node i from the origin cluster of node i\n      const removeModurarity = kiin - selfCluster.sumTot * commonParam;\n      // nodes for **removing** node i into this neighbor cluster\n      const selfClusterNodesAfterRemove = selfClusterNodes.filter(scNode => scNode.id !== node.id);\n      let propertiesWeightRemove = [];\n      selfClusterNodesAfterRemove.forEach((nodeRemove, index) => {\n        propertiesWeightRemove[index] = allPropertiesWeight[nodeRemove.originIndex];\n      })\n      // the inertialModularity for **removing** the node i from the origin cluster of node i\n      const removeInertialModularity = getInertialModularity(selfClusterNodesAfterRemove, allPropertiesWeight) * inertialWeight;\n\n      // the neightbors of the node\n      const nodeNeighborIds = neighbors[node.id];\n      Object.keys(nodeNeighborIds).forEach(neighborNodeId => {\n        const neighborNode = nodeMap[neighborNodeId].node;\n        const neighborClusterId = neighborNode.clusterId;\n\n        // if the node and the neighbor of node are in the same cluster, reutrn\n        if (neighborClusterId === node.clusterId) return;\n        const neighborCluster = clusters[neighborClusterId];\n        const clusterNodes = neighborCluster.nodes;\n\n        // if the cluster is empty, remove the cluster and return\n        if (!clusterNodes || !clusterNodes.length) return;\n\n        // sum of weights of edges from node to nodes in cluster\n        let neighborClusterKiin = 0;\n        clusterNodes.forEach(cNode => {\n          const cNodeIdx = nodeMap[cNode.id].idx;\n          neighborClusterKiin += adjMatrix[i][cNodeIdx] || 0;\n        });\n\n        // the modurarity for **adding** node i into this neighbor cluster\n        const addModurarity = neighborClusterKiin - neighborCluster.sumTot * commonParam;\n        // nodes for **adding** node i into this neighbor cluster\n        const clusterNodesAfterAdd= clusterNodes.concat([node]);\n        let propertiesWeightAdd = [];\n        clusterNodesAfterAdd.forEach((nodeAdd, index) => {\n          propertiesWeightAdd[index] = allPropertiesWeight[nodeAdd.originIndex];\n        })\n        // the inertialModularity for **adding** node i into this neighbor cluster\n        const addInertialModularity = getInertialModularity(clusterNodesAfterAdd, allPropertiesWeight) * inertialWeight;\n\n        // the increase modurarity is the difference between addModurarity and removeModurarity\n        let increase = addModurarity - removeModurarity;\n        if (inertialModularity) {\n          increase = (addModurarity + addInertialModularity) - (removeModurarity + removeInertialModularity);\n        }\n\n        // find the best cluster to move node i into\n        if (increase > bestIncrease) {\n          bestIncrease = increase;\n          bestCluster = neighborCluster;\n        }\n      });\n\n      // if found a best cluster to move into\n      if (bestIncrease > 0) {\n        bestCluster.nodes.push(node);\n        const previousClusterId = node.clusterId;\n        node.clusterId = bestCluster.id;\n        // move the node to the best cluster\n        const nodeInSelfClusterIdx = selfCluster.nodes.indexOf(node);\n        // remove from origin cluster\n        selfCluster.nodes.splice(nodeInSelfClusterIdx, 1);\n        // update sumTot for clusters\n        // sum of weights of edges to nodes in cluster\n        let neighborClusterSumTot = 0;\n        let selfClusterSumTot = 0;\n        edges.forEach(edge => {\n          const { source, target } = edge;\n          const sourceClusterId = nodeMap[source].node.clusterId;\n          const targetClusterId = nodeMap[target].node.clusterId;\n          if ((sourceClusterId === bestCluster.id && targetClusterId !== bestCluster.id)\n            || (targetClusterId === bestCluster.id && sourceClusterId !== bestCluster.id)) {\n            neighborClusterSumTot = neighborClusterSumTot + (edge[weightPropertyName] as number || 1);\n          }\n          if ((sourceClusterId === previousClusterId && targetClusterId !== previousClusterId)\n            || (targetClusterId === previousClusterId && sourceClusterId !== previousClusterId)) {\n            selfClusterSumTot = selfClusterSumTot + (edge[weightPropertyName] as number || 1);\n          }\n        });\n\n        // the nodes of the clusters to move into and remove are changed, update their sumTot\n        bestCluster.sumTot = neighborClusterSumTot;\n        selfCluster.sumTot = selfClusterSumTot;\n      }\n    });\n  }\n\n  // delete the empty clusters, assign increasing clusterId\n  const newClusterIdMap = {}\n  let clusterIdx = 0;\n  Object.keys(finalClusters).forEach((clusterId) => {\n    const cluster = finalClusters[clusterId];\n    if (!cluster.nodes || !cluster.nodes.length) {\n      delete finalClusters[clusterId];\n      return;\n    }\n    const newId = String(clusterIdx + 1);\n    if (newId === clusterId) {\n      return;\n    }\n    cluster.id = newId;\n    cluster.nodes = cluster.nodes.map(item => ({ id: item.id, clusterId: newId }));\n    finalClusters[newId] = cluster;\n    newClusterIdMap[clusterId] = newId;\n    delete finalClusters[clusterId];\n    clusterIdx ++;\n  });\n  // restore node clusterId\n  finalNodes.forEach(nodeInfo => {\n    const { node, clusterId } = nodeInfo;\n    if (!node) return;\n    node.clusterId = clusterId;\n    if (node.clusterId && newClusterIdMap[node.clusterId]) node.clusterId = newClusterIdMap[node.clusterId]\n  })\n  // get the cluster edges\n  const clusterEdges = [];\n  const clusterEdgeMap = {};\n  edges.forEach(edge => {\n    const { source, target } = edge;\n    const weight = edge[weightPropertyName] || 1;\n    const sourceClusterId = nodeMap[source].node.clusterId;\n    const targetClusterId = nodeMap[target].node.clusterId;\n    if (!sourceClusterId || !targetClusterId) return;\n    const newEdgeId = `${sourceClusterId}---${targetClusterId}`;\n    if (clusterEdgeMap[newEdgeId]) {\n      clusterEdgeMap[newEdgeId].weight += weight;\n      clusterEdgeMap[newEdgeId].count++;\n    } else {\n      const newEdge = {\n        source: sourceClusterId,\n        target: targetClusterId,\n        weight,\n        count: 1\n      };\n      clusterEdgeMap[newEdgeId] = newEdge;\n      clusterEdges.push(newEdge);\n    }\n  });\n  const clustersArray = [];\n  Object.keys(finalClusters).forEach(clusterId => {\n    clustersArray.push(finalClusters[clusterId]);\n  });\n  return {\n    clusters: clustersArray,\n    clusterEdges\n  }\n}\n\nexport default louvain;\n", "import { NodeConfig } from '../types';\nimport { secondReg, dateReg } from '../constants/time';\n\n// 获取所有属性并排序\nexport const getAllSortProperties = (nodes: NodeConfig[] = [], n: number = 100) => {\n  const propertyKeyInfo = {};\n  nodes.forEach(node => {\n    if (!node.properties) {\n      return;\n    }\n    Object.keys(node.properties).forEach(propertyKey => {\n      // 目前过滤只保留可以转成数值型的或日期型的, todo: 统一转成one-hot特征向量或者embedding\n      if (propertyKey === 'id' || !`${node.properties[propertyKey]}`.match(secondReg) && \n        !`${node.properties[propertyKey]}`.match(dateReg) && \n        isNaN(Number(node.properties[propertyKey]))) {\n        if (propertyKeyInfo.hasOwnProperty(propertyKey)) {\n            delete propertyKeyInfo[propertyKey];\n        }\n        return;\n      }\n      if (propertyKeyInfo.hasOwnProperty(propertyKey)) {\n        propertyKeyInfo[propertyKey] += 1;\n      } else {\n        propertyKeyInfo[propertyKey] = 1;\n      }\n    })\n  })\n\n  // 取top50的属性\n  const sortKeys = Object.keys(propertyKeyInfo).sort((a,b) => propertyKeyInfo[b] - propertyKeyInfo[a]);\n  return sortKeys.length < n ? sortKeys : sortKeys.slice(0, n);\n}\n  \nconst processProperty = (properties, propertyKeys) => propertyKeys.map(key => {\n  if (properties.hasOwnProperty(key)) {\n    // // 可以转成数值的直接转成数值\n    // if (!isNaN(Number(properties[key]))) {\n    //   return Number(properties[key]);\n    // }\n    // // 时间型的转成时间戳\n    // if (properties[key].match(secondReg) || properties[key].match(dateReg)) {\n    //   // @ts-ignore\n    //   return Number(Date.parse(new Date(properties[key]))) / 1000;\n    // }\n    return properties[key];\n  }\n  return 0;\n})\n  \n// 获取属性特征权重\nexport const getPropertyWeight = (nodes: NodeConfig[]) => {\n  const propertyKeys = getAllSortProperties(nodes);\n  let allPropertiesWeight = [];\n  for (let i = 0; i < nodes.length; i++) {\n    allPropertiesWeight[i] = processProperty(nodes[i].properties, propertyKeys);\n  }\n  return allPropertiesWeight;\n}\n\n// 获取所有节点的属性集合\nexport const getAllProperties = (nodes, key = undefined) => {\n  const allProperties = [];\n  nodes.forEach(node => {\n    if (key === undefined) {\n      allProperties.push(node);\n    }\n    if (node[key] !== undefined) {\n      allProperties.push(node[key]);\n    }\n  })\n  return allProperties;\n}\n\nexport default {\n  getAllSortProperties,\n  getPropertyWeight,\n  getAllProperties\n}\n", "/**\n * 并查集 Disjoint set to support quick union\n */\nexport default class UnionFind {\n  count: number;\n\n  parent: {};\n\n  constructor(items: (number | string)[]) {\n    this.count = items.length;\n    this.parent = {};\n    for (const i of items) {\n      this.parent[i] = i;\n    }\n  }\n\n  // find the root of the item\n  find(item) {\n    while (this.parent[item] !== item) {\n      item = this.parent[item];\n    }\n    return item;\n  }\n\n  union(a, b) {\n    const rootA = this.find(a);\n    const rootB = this.find(b);\n\n    if (rootA === rootB) return;\n\n    // make the element with smaller root the parent\n    if (rootA < rootB) {\n      if (this.parent[b] !== b) this.union(this.parent[b], a);\n      this.parent[b] = this.parent[a];\n    } else {\n      if (this.parent[a] !== a) this.union(this.parent[a], b);\n      this.parent[a] = this.parent[b];\n    }\n  }\n\n  // whether a and b are connected, i.e. a and b have the same root\n  connected(a, b) {\n    return this.find(a) === this.find(b);\n  }\n}\n", "const defaultCompare = (a, b) => {\n  return a - b;\n};\n\nexport default class MinBinaryHeap {\n  list: any[];\n\n  compareFn: (a: any, b: any) => number;\n\n  constructor(compareFn = defaultCompare) {\n    this.compareFn = compareFn;\n    this.list = [];\n  }\n\n  getLeft(index) {\n    return 2 * index + 1;\n  }\n\n  getRight(index) {\n    return 2 * index + 2;\n  }\n\n  getParent(index) {\n    if (index === 0) {\n      return null;\n    }\n    return Math.floor((index - 1) / 2);\n  }\n\n  isEmpty() {\n    return this.list.length <= 0;\n  }\n\n  top() {\n    return this.isEmpty() ? undefined : this.list[0];\n  }\n\n  delMin() {\n    const top = this.top();\n    const bottom = this.list.pop();\n    if (this.list.length > 0) {\n      this.list[0] = bottom;\n      this.moveDown(0);\n    }\n    return top;\n  }\n\n  insert(value) {\n    if (value !== null) {\n      this.list.push(value);\n      const index = this.list.length - 1;\n      this.moveUp(index);\n      return true;\n    }\n    return false;\n  }\n\n  moveUp(index) {\n    let parent = this.getParent(index);\n    while (index && index > 0 && this.compareFn(this.list[parent], this.list[index]) > 0) {\n      // swap\n      const tmp = this.list[parent];\n      this.list[parent] = this.list[index];\n      this.list[index] = tmp;\n      // [this.list[index], this.list[parent]] = [this.list[parent], this.list[index]]\n      index = parent;\n      parent = this.getParent(index);\n    }\n  }\n\n  moveDown(index) {\n    let element = index;\n    const left = this.getLeft(index);\n    const right = this.getRight(index);\n    const size = this.list.length;\n    if (left !== null && left < size && this.compareFn(this.list[element], this.list[left]) > 0) {\n      element = left;\n    } else if (\n      right !== null &&\n      right < size &&\n      this.compareFn(this.list[element], this.list[right]) > 0\n    ) {\n      element = right;\n    }\n    if (index !== element) {\n      [this.list[index], this.list[element]] = [this.list[element], this.list[index]];\n      this.moveDown(element);\n    }\n  }\n}\n", "import UnionFind from './structs/union-find';\nimport MinBinaryHeap from './structs/binary-heap';\nimport { GraphData, EdgeConfig } from './types';\nimport { getEdgesByNodeId } from './util';\n\n/**\n * Prim algorithm，use priority queue，复杂度 O(E+V*logV), V: 节点数量，E: 边的数量\n * refer: https://en.wikipedia.org/wiki/Prim%27s_algorithm\n * @param graph\n * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致\n */\nconst primMST = (graphData: GraphData, weight?: string) => {\n  const selectedEdges = [];\n  const { nodes = [], edges = [] } = graphData;\n  if (nodes.length === 0) {\n    return selectedEdges;\n  }\n\n  // 从nodes[0]开始\n  const currNode = nodes[0];\n  const visited = new Set();\n  visited.add(currNode);\n\n  // 用二叉堆维护距已加入节点的其他节点的边的权值\n  const compareWeight = (a: EdgeConfig, b: EdgeConfig) => {\n    if (weight) {\n      return a.weight - b.weight;\n    }\n    return 0;\n\n  };\n  const edgeQueue = new MinBinaryHeap(compareWeight);\n  getEdgesByNodeId(currNode.id, edges).forEach((edge) => {\n    edgeQueue.insert(edge);\n  });\n\n  while (!edgeQueue.isEmpty()) {\n    // 选取与已加入的结点之间边权最小的结点\n    const currEdge: EdgeConfig = edgeQueue.delMin();\n    const source = currEdge.source;\n    const target = currEdge.target;\n    if (visited.has(source) && visited.has(target)) continue;\n    selectedEdges.push(currEdge);\n\n    if (!visited.has(source)) {\n      visited.add(source);\n      getEdgesByNodeId(source, edges).forEach((edge) => {\n        edgeQueue.insert(edge);\n      });\n    }\n    if (!visited.has(target)) {\n      visited.add(target);\n      getEdgesByNodeId(target, edges).forEach((edge) => {\n        edgeQueue.insert(edge);\n      });\n    }\n  }\n  return selectedEdges;\n};\n\n/**\n * Kruskal algorithm，复杂度 O(E*logE), E: 边的数量\n * refer: https://en.wikipedia.org/wiki/Kruskal%27s_algorithm\n * @param graph\n * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致\n * @return IEdge[] 返回构成MST的边的数组\n */\nconst kruskalMST = (graphData: GraphData, weight?: string): EdgeConfig[] => {\n  const selectedEdges = [];\n  const { nodes = [], edges = [] } = graphData\n  if (nodes.length === 0) {\n    return selectedEdges;\n  }\n\n  // 若指定weight，则将所有的边按权值从小到大排序\n  const weightEdges = edges.map((edge) => edge);\n  if (weight) {\n    weightEdges.sort((a, b) => {\n      return a.weight - b.weight;\n    });\n  }\n  const disjointSet = new UnionFind(nodes.map((n) => n.id));\n\n  // 从权值最小的边开始，如果这条边连接的两个节点于图G中不在同一个连通分量中，则添加这条边\n  // 直到遍历完所有点或边\n  while (weightEdges.length > 0) {\n    const curEdge = weightEdges.shift();\n    const source = curEdge.source;\n    const target = curEdge.target;\n    if (!disjointSet.connected(source, target)) {\n      selectedEdges.push(curEdge);\n      disjointSet.union(source, target);\n    }\n  }\n  return selectedEdges;\n};\n\n/**\n * 最小生成树\n * refer: https://en.wikipedia.org/wiki/Kruskal%27s_algorithm\n * @param graph\n * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致\n * @param algo 'prim' | 'kruskal' 算法类型\n * @return EdgeConfig[] 返回构成MST的边的数组\n */\nconst minimumSpanningTree = (graphData: GraphData, weight?: string, algo?: string): EdgeConfig[] => {\n  const algos = {\n    prim: primMST,\n    kruskal: kruskalMST,\n  };\n  if (!algo) return kruskalMST(graphData, weight);\n\n  return algos[algo](graphData, weight);\n}\n\nexport default minimumSpanningTree\n", "import { GraphData } from \"./types\";\nimport degree from './degree'\nimport { getNeighbors } from \"./util\";\n\n/**\n * PageRank https://en.wikipedia.org/wiki/PageRank\n * refer: https://github.com/anvaka/ngraph.pagerank\n * @param graph \n * @param epsilon 判断是否收敛的精度值，默认 0.000001\n * @param linkProb 阻尼系数（dumping factor），指任意时刻，用户访问到某节点后继续访问该节点链接的下一个节点的概率，经验值 0.85\n */\nconst pageRank = (graphData: GraphData, epsilon?: number, linkProb?: number): {\n  [key: string]: number;\n} => {\n  if (typeof epsilon !== 'number') epsilon = 0.000001;\n  if (typeof linkProb !== 'number') linkProb = 0.85;\n\n  let distance = 1;\n  let leakedRank = 0;\n  let maxIterations = 1000;\n\n  const { nodes = [], edges = [] } = graphData;\n  const nodesCount = nodes.length;\n  let currentRank;\n  const curRanks = {};\n  const prevRanks = {}\n\n  // Initialize pageranks 初始化\n  for (let j = 0; j < nodesCount; ++j) {\n    const node = nodes[j];\n    const nodeId = node.id;\n    curRanks[nodeId] = (1 / nodesCount)\n    prevRanks[nodeId] = (1 / nodesCount)\n  }\n\n  const nodeDegree = degree(graphData)\n  while (maxIterations > 0 && distance > epsilon) {\n    leakedRank = 0;\n    for (let j = 0; j < nodesCount; ++j) {\n      const node = nodes[j];\n      const nodeId = node.id;\n      currentRank = 0;\n      if (nodeDegree[node.id].inDegree === 0) {\n        curRanks[nodeId] = 0;\n      } else {\n        const neighbors = getNeighbors(nodeId, edges, 'source');\n        for (let i = 0; i < neighbors.length; ++i) {\n          const neighbor = neighbors[i];\n          const outDegree: number = nodeDegree[neighbor].outDegree;\n          if (outDegree > 0) currentRank += (prevRanks[neighbor] / outDegree);\n        }\n        curRanks[nodeId] = linkProb * currentRank;\n        leakedRank += curRanks[nodeId];\n      }\n    }\n\n    leakedRank = (1 - leakedRank) / nodesCount;\n    distance = 0;\n    for (let j = 0; j < nodesCount; ++j) {\n      const node = nodes[j];\n      const nodeId = node.id;\n      currentRank = curRanks[nodeId] + leakedRank;\n      distance += Math.abs(currentRank - prevRanks[nodeId]);\n      prevRanks[nodeId] = currentRank;\n    }\n    maxIterations -= 1\n  }\n\n  return prevRanks;\n}\n\nexport default pageRank\n", "import { indexOf } from \"@antv/util\";\n\nexport const VACANT_EDGE_ID = -1;\nexport const VACANT_NODE_ID = -1;\nexport const VACANT_EDGE_LABEL = \"-1\";\nexport const VACANT_NODE_LABEL = \"-1\";\nexport const VACANT_GRAPH_ID = -1;\nexport const AUTO_EDGE_ID = \"-1\";\n\nexport class Edge {\n  public id: number;\n  public from: number;\n  public to: number;\n  public label: string;\n\n  constructor(\n    id = VACANT_EDGE_ID,\n    from = VACANT_NODE_ID,\n    to = VACANT_NODE_ID,\n    label = VACANT_EDGE_LABEL\n  ) {\n    this.id = id;\n    this.from = from;\n    this.to = to;\n    this.label = label;\n  }\n}\n\nexport class Node {\n  public id: number;\n  public from: number;\n  public to: number;\n  public label: string;\n  public edges: Edge[];\n  public edgeMap: {};\n\n  constructor(id = VACANT_NODE_ID, label = VACANT_NODE_LABEL) {\n    this.id = id;\n    this.label = label;\n    this.edges = [];\n    this.edgeMap = {};\n  }\n\n  addEdge(edge) {\n    this.edges.push(edge);\n    this.edgeMap[edge.id] = edge;\n  }\n}\n\nexport class Graph {\n  public id: number;\n  public from: number;\n  public to: number;\n  public label: string;\n  public edgeIdAutoIncrease: boolean;\n  public nodes: Node[];\n  public edges: Edge[];\n  public nodeMap: {};\n  public edgeMap: {};\n  public nodeLabelMap: {}; // key 是 label，value 是节点 id 的数组\n  public edgeLabelMap: {};\n  private counter: number; // 自增用于自动生成边 id\n  public directed: boolean;\n\n  constructor(\n    id = VACANT_NODE_ID,\n    edgeIdAutoIncrease = true,\n    directed = false\n  ) {\n    this.id = id;\n    this.edgeIdAutoIncrease = edgeIdAutoIncrease;\n    this.edges = [];\n    this.nodes = [];\n    this.nodeMap = {};\n    this.edgeMap = {};\n    this.nodeLabelMap = {};\n    this.edgeLabelMap = {};\n    this.counter = 0;\n    this.directed = directed;\n  }\n\n  getNodeNum() {\n    return this.nodes.length;\n  }\n\n  addNode(id: number, label: string) {\n    if (this.nodeMap[id]) return;\n    const node = new Node(id, label);\n    this.nodes.push(node);\n    this.nodeMap[id] = node;\n    if (!this.nodeLabelMap[label]) this.nodeLabelMap[label] = [];\n    this.nodeLabelMap[label].push(id);\n  }\n\n  addEdge(id: number, from: number, to: number, label: string) {\n    if (this.edgeIdAutoIncrease || id === undefined) id = this.counter++;\n    if (this.nodeMap[from] && this.nodeMap[to] && this.nodeMap[to].edgeMap[id])\n      return;\n    const edge = new Edge(id, from, to, label);\n    this.edges.push(edge);\n    this.edgeMap[id] = edge;\n\n    this.nodeMap[from].addEdge(edge);\n\n    if (!this.edgeLabelMap[label]) this.edgeLabelMap[label] = [];\n    this.edgeLabelMap[label].push(edge);\n\n    if (!this.directed) {\n      const rEdge = new Edge(id, to, from, label);\n      this.nodeMap[to].addEdge(rEdge);\n      this.edgeLabelMap[label].push(rEdge);\n    }\n  }\n}\n", "import { GraphData } from \"../types\";\nimport { clone } from \"@antv/util\";\nimport {\n  Graph,\n  Edge,\n  VACANT_NODE_LABEL,\n  VACANT_GRAPH_ID,\n  Node,\n  VACANT_EDGE_LABEL,\n} from \"./struct\";\n\nexport interface EdgeMap {\n  [key: string]: {\n    // key 的格式为 source-target\n    idx: number; // 该边在原图 graphData.edges 的序号\n    edge: any;\n  };\n}\n\nexport interface NodeMap {\n  [key: string]: {\n    // key 格式为 node.id\n    idx: number; // 该j客店在原图 graphData.nodes 的序号\n    node: any;\n    degree: number;\n    inDegree: number;\n    outDegree: number;\n  };\n}\n\ninterface PDFS {\n  graphId: number;\n  edge: any;\n  preNode: any;\n}\n\nclass DFSedge {\n  public fromNode: number;\n  public toNode: number;\n  public nodeEdgeNodeLabel: {\n    nodeLabel1: string;\n    edgeLabel: string;\n    nodeLabel2: string;\n  };\n\n  constructor(\n    fromNode: number,\n    toNode: number,\n    fromNodeLabel: string,\n    edgeLabel: string,\n    toNodeLabel: string\n  ) {\n    this.fromNode = fromNode;\n    this.toNode = toNode;\n    this.nodeEdgeNodeLabel = {\n      nodeLabel1: fromNodeLabel || VACANT_NODE_LABEL,\n      edgeLabel: edgeLabel || VACANT_EDGE_LABEL,\n      nodeLabel2: toNodeLabel || VACANT_NODE_LABEL,\n    };\n  }\n\n  equalTo(other) {\n    return (\n      this.fromNode === other.formNode &&\n      this.toNode === other.toNode &&\n      this.nodeEdgeNodeLabel === other.nodeEdgeNodeLabel\n    );\n  }\n\n  notEqualTo(other) {\n    return !this.equalTo(other);\n  }\n}\n\n// DFScode 是 DESedge 的数组\nclass DFScode {\n  public dfsEdgeList: DFSedge[];\n  public rmpath: any;\n\n  constructor() {\n    this.rmpath = [];\n    this.dfsEdgeList = [];\n  }\n\n  equalTo(other) {\n    const aLength = this.dfsEdgeList.length;\n    const bLength = other.length;\n    if (aLength !== bLength) return false;\n    for (let i = 0; i < aLength; i++) {\n      if (this.dfsEdgeList[i] !== other[i]) return false;\n    }\n    return true;\n  }\n\n  notEqualTo(other) {\n    return !this.equalTo(other);\n  }\n\n  /** 增加一条 edge 到 DFScode */\n  pushBack(fromNode, toNode, fromNodeLabel, edgeLabel, toNodeLabel) {\n    this.dfsEdgeList.push(\n      new DFSedge(fromNode, toNode, fromNodeLabel, edgeLabel, toNodeLabel)\n    );\n    return this.dfsEdgeList;\n  }\n\n  /** 根据 dfs 构建图 */\n  toGraph(graphId: number = VACANT_GRAPH_ID, directed = false) {\n    const graph = new Graph(graphId, true, directed);\n    this.dfsEdgeList.forEach((dfsEdge) => {\n      const fromNodeId = dfsEdge.fromNode;\n      const toNodeId = dfsEdge.toNode;\n      const { nodeLabel1, edgeLabel, nodeLabel2 } = dfsEdge.nodeEdgeNodeLabel;\n\n      if (nodeLabel1 !== VACANT_NODE_LABEL) graph.addNode(fromNodeId, nodeLabel1);\n      if (nodeLabel2 !== VACANT_NODE_LABEL) graph.addNode(toNodeId, nodeLabel2);\n      if (nodeLabel1 !== VACANT_NODE_LABEL && nodeLabel2 !== nodeLabel1)  graph.addEdge(undefined, fromNodeId, toNodeId, edgeLabel);\n    });\n    return graph;\n  }\n\n  // 建立 rightmost path\n  buildRmpath() {\n    this.rmpath = [];\n    let oldFrom = undefined;\n    const selfLength = this.dfsEdgeList.length;\n    for (let i = selfLength - 1; i >= 0; i--) {\n      const dfsEdge = this.dfsEdgeList[i];\n      const fromNodeIdx = dfsEdge.fromNode;\n      const toNodeIdx = dfsEdge.toNode;\n      if (\n        fromNodeIdx < toNodeIdx &&\n        (oldFrom === undefined || toNodeIdx === oldFrom)\n      ) {\n        this.rmpath.push(i);\n        oldFrom = fromNodeIdx;\n      }\n    }\n    return this.rmpath;\n  }\n\n  getNodeNum() {\n    const nodeMap = {};\n    this.dfsEdgeList.forEach((dfsEdge) => {\n      if (!nodeMap[dfsEdge.fromNode]) nodeMap[dfsEdge.fromNode] = true;\n      if (!nodeMap[dfsEdge.toNode]) nodeMap[dfsEdge.toNode] = true;\n    });\n    return Object.keys(nodeMap).length;\n  }\n}\n\nclass History {\n  public his: object;\n  public edges: Edge[];\n  public nodesUsed: object;\n  public edgesUsed: object;\n\n  constructor(pdfs: PDFS) {\n    this.his = {};\n    this.nodesUsed = {};\n    this.edgesUsed = {};\n    this.edges = [];\n    if (!pdfs) return;\n    while (pdfs) {\n      const e = pdfs.edge;\n      this.edges.push(e);\n      this.nodesUsed[e.from] = 1;\n      this.nodesUsed[e.to] = 1;\n      this.edgesUsed[e.id] = 1;\n      pdfs = pdfs.preNode;\n    }\n    // 倒序\n    this.edges = this.edges.reverse();\n  }\n\n  hasNode(node: Node) {\n    return this.nodesUsed[node.id] === 1;\n  }\n\n  hasEdge(edge: Edge) {\n    return this.edgesUsed[edge.id] === 1;\n  }\n}\n\ninterface Root {\n  [key: string]: {\n    projected: PDFS[];\n    nodeLabel1?: string;\n    edgeLabel?: string;\n    nodeLabel2?: string;\n    fromNodeId?: number;\n    toNodeId?: number;\n  };\n}\n\ninterface GraphDataMap {\n  [key: string]: GraphData;\n}\ninterface GraphMap {\n  [key: number]: Graph;\n}\n\ninterface AlgorithmProps {\n  graphs: GraphMap; // 图数据\n  minSupport: number; // 算法参数，最小支持数量，根据 graphs 内图的数量指定\n  directed?: boolean; // 是否有向图，默认为 false\n  minNodeNum?: number; // 每个子图中边的最少个数，默认为 1\n  maxNodeNum?: number; // 每个子图中边的最多个数，默认为 4\n  top?: number; // 返回前 top 个频繁子图，默认为 10\n  verbose?: boolean;\n}\n\nclass GSpan {\n  public graphs: GraphMap;\n  public dfsCode: DFScode;\n  public support: number;\n  public frequentSize1Subgraphs: GraphData[];\n  public frequentSubgraphs: Graph[];\n  public reportDF: [];\n  public maxNodeNum: number;\n  public minNodeNum: number;\n  public minSupport: number;\n  public top: number;\n  public directed: boolean;\n  private counter: number; // 用于生成图的 id，自增\n  public verbose: boolean;\n\n  constructor({\n    graphs,\n    minSupport = 2,\n    minNodeNum = 1,\n    maxNodeNum = 4,\n    top = 10,\n    directed = false,\n    verbose = false,\n  }: AlgorithmProps) {\n    // -------- 第零步，初始化-------\n    this.graphs = graphs;\n    this.dfsCode = new DFScode();\n    this.support = 0;\n    this.frequentSize1Subgraphs = [];\n    this.frequentSubgraphs = [];\n    this.minSupport = minSupport;\n    this.top = top;\n    this.directed = directed;\n    this.counter = 0;\n    // TODO? timestamp = {}\n    this.maxNodeNum = maxNodeNum;\n    this.minNodeNum = minNodeNum;\n    this.verbose = verbose;\n    if (this.maxNodeNum < this.minNodeNum) this.maxNodeNum = this.minNodeNum;\n    this.reportDF = []; // matrix\n  }\n\n  // Line 352\n  findForwardRootEdges(graph: Graph, fromNode: Node): Edge[] {\n    const result = [];\n    const nodeMap = graph.nodeMap;\n    fromNode.edges.forEach((edge) => {\n      if (this.directed || fromNode.label <= nodeMap[edge.to].label)\n        result.push(edge);\n    });\n\n    return result;\n  }\n\n  findBackwardEdge(\n    graph: Graph,\n    edge1: Edge,\n    edge2: Edge,\n    history: History\n  ): Edge {\n    if (!this.directed && edge1 === edge2) return null;\n    const nodeMap = graph.nodeMap;\n    const edge2To = nodeMap[edge2.to];\n    const edge2ToEdges = edge2To.edges;\n    const edgeLength = edge2ToEdges.length;\n    for (let i = 0; i < edgeLength; i++) {\n      const edge = edge2ToEdges[i];\n      if (history.hasEdge(edge) || edge.to !== edge1.from) continue;\n      if (!this.directed) {\n        if (\n          edge1.label < edge.label ||\n          (edge1.label === edge.label &&\n            nodeMap[edge1.to].label <= nodeMap[edge2.to].label)\n        ) {\n          return edge;\n        }\n      } else {\n        if (\n          nodeMap[edge1.from].label < nodeMap[edge2.to].label ||\n          (nodeMap[edge1.from].label === nodeMap[edge2.to].label &&\n            edge1.label <= edge.label)\n        ) {\n          return edge;\n        }\n      }\n    }\n    return null;\n  }\n\n  findForwardPureEdges(\n    graph,\n    rightmostEdge,\n    minNodeLabel,\n    history: History\n  ): Edge[] {\n    const result = [];\n    const rightmostEdgeToId = rightmostEdge.to;\n    const edges = graph.nodeMap[rightmostEdgeToId].edges;\n    const edgeLength = edges.length;\n    for (let i = 0; i < edgeLength; i++) {\n      const edge = edges[i];\n      const toNode = graph.nodeMap[edge.to];\n      if (minNodeLabel <= toNode.label && !history.hasNode(toNode)) {\n        result.push(edge);\n      }\n    }\n    return result;\n  }\n\n  findForwardRmpathEdges(\n    graph: Graph,\n    rightmostEdge: Edge,\n    minNodeLabel: string,\n    history: History\n  ): Edge[] {\n    const result = [];\n    const nodeMap = graph.nodeMap;\n    const toNodeLabel = nodeMap[rightmostEdge.to].label;\n    const fromNode = nodeMap[rightmostEdge.from];\n    const edges = fromNode.edges;\n    const edgeLength = edges.length;\n    for (let i = 0; i < edgeLength; i++) {\n      const edge = edges[i];\n      const newToNodeLabel = nodeMap[edge.to].label;\n      if (\n        rightmostEdge.to === edge.to ||\n        minNodeLabel > newToNodeLabel ||\n        history.hasNode(nodeMap[edge.to])\n      ) {\n        continue;\n      }\n      if (\n        rightmostEdge.label < edge.label ||\n        (rightmostEdge.label === edge.label && toNodeLabel <= newToNodeLabel)\n      ) {\n        result.push(edge);\n      }\n    }\n    return result;\n  }\n\n  getSupport(projected: PDFS[]): number {\n    const graphMap = {};\n    projected.forEach((pro) => {\n      if (!graphMap[pro.graphId]) graphMap[pro.graphId] = true;\n    });\n    return Object.keys(graphMap).length;\n  }\n\n  findMinLabel(\n    obj: Root\n  ): {\n    nodeLabel1?: string;\n    edgeLabel: string;\n    nodeLabel2?: string;\n  } {\n    let minLabel = undefined;\n    Object.keys(obj).forEach((nodeEdgeNodeLabel) => {\n      const { nodeLabel1, edgeLabel, nodeLabel2 } = obj[nodeEdgeNodeLabel];\n      if (!minLabel) {\n        minLabel = {\n          nodeLabel1,\n          edgeLabel,\n          nodeLabel2,\n        };\n        return;\n      }\n      if (\n        nodeLabel1 < minLabel.nodeLabel1 ||\n        (nodeLabel1 === minLabel.nodeLabel1 &&\n          edgeLabel < minLabel.edgeLabel) ||\n        (nodeLabel1 === minLabel.nodeLabel1 &&\n          edgeLabel === minLabel.edgeLabel &&\n          nodeLabel2 < minLabel.nodeLabel2)\n      ) {\n        minLabel = {\n          nodeLabel1,\n          edgeLabel,\n          nodeLabel2,\n        };\n      }\n    });\n    return minLabel;\n  }\n\n  isMin() {\n    const dfsCode = this.dfsCode;\n    if (this.verbose) console.log(\"isMin checking\", dfsCode);\n    if (dfsCode.dfsEdgeList.length === 1) return true;\n    const directed = this.directed;\n    const graph = dfsCode.toGraph(VACANT_GRAPH_ID, directed);\n    const nodeMap = graph.nodeMap;\n    const dfsCodeMin = new DFScode();\n    const root: Root = {};\n    graph.nodes.forEach((node) => {\n      const forwardEdges = this.findForwardRootEdges(graph, node);\n      forwardEdges.forEach((edge) => {\n        let otherNode = nodeMap[edge.to];\n        const nodeEdgeNodeLabel = `${node.label}-${edge.label}-${otherNode.label}`;\n        if (!root[nodeEdgeNodeLabel])\n          root[nodeEdgeNodeLabel] = {\n            projected: [],\n            nodeLabel1: node.label,\n            edgeLabel: edge.label,\n            nodeLabel2: otherNode.label,\n          };\n        const pdfs: PDFS = {\n          graphId: graph.id,\n          edge,\n          preNode: null,\n        };\n        root[nodeEdgeNodeLabel].projected.push(pdfs);\n      });\n    });\n\n    // 比较 root 中每一项的 nodeEdgeNodeLabel 大小，按照 nodeLabel1、edgeLabe、nodeLabel2 的顺序比较\n    let minLabel = this.findMinLabel(root); // line 419\n    if (!minLabel) return;\n    dfsCodeMin.dfsEdgeList.push(\n      new DFSedge(\n        0,\n        1,\n        minLabel.nodeLabel1,\n        minLabel.edgeLabel,\n        minLabel.nodeLabel2\n      )\n    );\n\n    // line 423\n    const projectIsMin = (projected: PDFS[]) => {\n      // right most path\n      const rmpath = dfsCodeMin.buildRmpath();\n      const minNodeLabel =\n        dfsCodeMin.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1;\n      const maxToC = dfsCodeMin.dfsEdgeList[rmpath[0]].toNode; // node id\n\n      const backwardRoot: Root = {};\n      let flag = false,\n        newTo = 0;\n      let end = directed ? -1 : 0; // 遍历到 1 还是到 0\n      for (let i = rmpath.length - 1; i > end; i--) {\n        if (flag) break;\n        // line 435\n        projected.forEach((p) => {\n          const history = new History(p);\n          const backwardEdge = this.findBackwardEdge(\n            graph,\n            history.edges[rmpath[i]],\n            history.edges[rmpath[0]],\n            history\n          );\n          if (backwardEdge) {\n            // Line 441\n            if (!backwardRoot[backwardEdge.label]) {\n              backwardRoot[backwardEdge.label] = {\n                projected: [],\n                edgeLabel: backwardEdge.label,\n              };\n            }\n            backwardRoot[backwardEdge.label].projected.push({\n              graphId: graph.id,\n              edge: backwardRoot,\n              preNode: p,\n            });\n            newTo = dfsCodeMin.dfsEdgeList[rmpath[i]].fromNode;\n            flag = true;\n          }\n        });\n      }\n\n      if (flag) {\n        const minBackwardEdgeLabel = this.findMinLabel(backwardRoot);\n        dfsCodeMin.dfsEdgeList.push(\n          new DFSedge(\n            maxToC,\n            newTo,\n            VACANT_NODE_LABEL,\n            minBackwardEdgeLabel.edgeLabel,\n            VACANT_NODE_LABEL\n          )\n        );\n        const idx = dfsCodeMin.dfsEdgeList.length - 1;\n        if (this.dfsCode.dfsEdgeList[idx] !== dfsCodeMin.dfsEdgeList[idx])\n          return false;\n        return projectIsMin(\n          backwardRoot[minBackwardEdgeLabel.edgeLabel].projected\n        );\n      }\n      const forwardRoot: Root = {};\n      flag = false;\n      let newFrom = 0;\n      projected.forEach((p) => {\n        const history = new History(p);\n        const forwardPureEdges = this.findForwardPureEdges(\n          graph,\n          history.edges[rmpath[0]],\n          minNodeLabel,\n          history\n        );\n        if (forwardPureEdges.length > 0) {\n          flag = true;\n          newFrom = maxToC;\n          forwardPureEdges.forEach((edge) => {\n            const key = `${edge.label}-${nodeMap[edge.to].label}`;\n            if (!forwardRoot[key])\n              forwardRoot[key] = {\n                projected: [],\n                edgeLabel: edge.label,\n                nodeLabel2: nodeMap[edge.to].label,\n              };\n            forwardRoot[key].projected.push({\n              graphId: graph.id,\n              edge,\n              preNode: p,\n            });\n          });\n        }\n      });\n\n      const pathLength = rmpath.length;\n      for (let i = 0; i < pathLength; i++) {\n        if (flag) break;\n        const value = rmpath[i];\n        projected.forEach((p) => {\n          const history = new History(p);\n          const forwardRmpathEdges = this.findForwardRmpathEdges(\n            graph,\n            history.edges[value],\n            minNodeLabel,\n            history\n          );\n          if (forwardRmpathEdges.length > 0) {\n            flag = true;\n            newFrom = dfsCodeMin.dfsEdgeList[value].fromNode;\n            forwardRmpathEdges.forEach((edge) => {\n              const key = `${edge.label}-${nodeMap[edge.to].label}`;\n              if (!forwardRoot[key])\n                forwardRoot[key] = {\n                  projected: [],\n                  edgeLabel: edge.label,\n                  nodeLabel2: nodeMap[edge.to].label,\n                };\n              forwardRoot[key].projected.push({\n                graphId: graph.id,\n                edge,\n                preNode: p,\n              });\n            });\n          }\n        });\n      }\n\n      if (!flag) return true;\n\n      const forwardMinEdgeNodeLabel = this.findMinLabel(forwardRoot);\n      dfsCodeMin.dfsEdgeList.push(\n        new DFSedge(\n          newFrom,\n          maxToC + 1,\n          VACANT_NODE_LABEL,\n          forwardMinEdgeNodeLabel.edgeLabel,\n          forwardMinEdgeNodeLabel.nodeLabel2\n        )\n      );\n      const idx = dfsCodeMin.dfsEdgeList.length - 1;\n      if (dfsCode.dfsEdgeList[idx] !== dfsCodeMin.dfsEdgeList[idx])\n        return false;\n      return projectIsMin(\n        forwardRoot[\n          `${forwardMinEdgeNodeLabel.edgeLabel}-${forwardMinEdgeNodeLabel.nodeLabel2}`\n        ].projected\n      );\n    };\n    const key = `${minLabel.nodeLabel1}-${minLabel.edgeLabel}-${minLabel.nodeLabel2}`;\n    return projectIsMin(root[key].projected);\n  }\n\n  report() {\n    if (this.dfsCode.getNodeNum() < this.minNodeNum) return;\n    this.counter++;\n    const graph = this.dfsCode.toGraph(this.counter, this.directed);\n    this.frequentSubgraphs.push(clone(graph));\n  }\n\n  subGraphMining(projected) {\n    const support = this.getSupport(projected);\n    if (support < this.minSupport) return;\n    if (!this.isMin()) return;\n    this.report();\n\n    const nodeNum = this.dfsCode.getNodeNum();\n    const rmpath = this.dfsCode.buildRmpath();\n    const maxToC = this.dfsCode.dfsEdgeList[rmpath[0]].toNode;\n    const minNodeLabel = this.dfsCode.dfsEdgeList[0].nodeEdgeNodeLabel\n      .nodeLabel1;\n\n    const forwardRoot: Root = {};\n    const backwardRoot: Root = {};\n\n    projected.forEach((p) => {\n      const graph = this.graphs[p.graphId];\n      const nodeMap = graph.nodeMap;\n      const history = new History(p);\n      // backward Line 526\n      for (let i = rmpath.length - 1; i >= 0; i--) {\n        const backwardEdge = this.findBackwardEdge(\n          graph,\n          history.edges[rmpath[i]],\n          history.edges[rmpath[0]],\n          history\n        );\n        if (backwardEdge) {\n          const key = `${this.dfsCode.dfsEdgeList[rmpath[i]].fromNode}-${\n            backwardEdge.label\n          }`;\n          if (!backwardRoot[key])\n            backwardRoot[key] = {\n              projected: [],\n              toNodeId: this.dfsCode.dfsEdgeList[rmpath[i]].fromNode,\n              edgeLabel: backwardEdge.label,\n            };\n          backwardRoot[key].projected.push({\n            graphId: p.graphId,\n            edge: backwardEdge,\n            preNode: p,\n          });\n        }\n      }\n\n      // pure forward\n      if (nodeNum >= this.maxNodeNum) return;\n      const forwardPureEdges = this.findForwardPureEdges(\n        graph,\n        history.edges[rmpath[0]],\n        minNodeLabel,\n        history\n      );\n      forwardPureEdges.forEach((edge) => {\n        const key = `${maxToC}-${edge.label}-${nodeMap[edge.to].label}`;\n        if (!forwardRoot[key])\n          forwardRoot[key] = {\n            projected: [],\n            fromNodeId: maxToC,\n            edgeLabel: edge.label,\n            nodeLabel2: nodeMap[edge.to].label,\n          };\n        forwardRoot[key].projected.push({\n          graphId: p.graphId,\n          edge,\n          preNode: p,\n        });\n      });\n\n      // rmpath forward\n      for (let i = 0; i < rmpath.length; i++) {\n        const forwardRmpathEdges = this.findForwardRmpathEdges(\n          graph,\n          history.edges[rmpath[i]],\n          minNodeLabel,\n          history\n        );\n        forwardRmpathEdges.forEach((edge) => {\n          const key = `${this.dfsCode.dfsEdgeList[rmpath[i]].fromNode}-${\n            edge.label\n          }-${nodeMap[edge.to].label}`;\n          if (!forwardRoot[key])\n            forwardRoot[key] = {\n              projected: [],\n              fromNodeId: this.dfsCode.dfsEdgeList[rmpath[i]].fromNode,\n              edgeLabel: edge.label,\n              nodeLabel2: nodeMap[edge.to].label,\n            };\n          forwardRoot[key].projected.push({\n            graphId: p.graphId,\n            edge,\n            preNode: p,\n          });\n        });\n      }\n    });\n\n    // backward\n    Object.keys(backwardRoot).forEach((key) => {\n      const { toNodeId, edgeLabel } = backwardRoot[key];\n      this.dfsCode.dfsEdgeList.push(\n        new DFSedge(maxToC, toNodeId, \"-1\", edgeLabel, \"-1\")\n      );\n      this.subGraphMining(backwardRoot[key].projected);\n      this.dfsCode.dfsEdgeList.pop();\n    });\n\n    // forward\n    Object.keys(forwardRoot).forEach((key) => {\n      const { fromNodeId, edgeLabel, nodeLabel2 } = forwardRoot[key];\n      this.dfsCode.dfsEdgeList.push(\n        new DFSedge(\n          fromNodeId,\n          maxToC + 1,\n          VACANT_NODE_LABEL,\n          edgeLabel,\n          nodeLabel2\n        )\n      );\n      this.subGraphMining(forwardRoot[key].projected);\n      this.dfsCode.dfsEdgeList.pop();\n    });\n  }\n\n  generate1EdgeFrequentSubGraphs() {\n    const graphs = this.graphs;\n    const directed = this.directed;\n    const minSupport = this.minSupport;\n    const frequentSize1Subgraphs = this.frequentSize1Subgraphs;\n    let nodeLabelCounter = {},\n      nodeEdgeNodeCounter = {};\n    // 保存各个图和各自节点的关系 map，key 格式为 graphKey-node类型\n    const nodeLableCounted = {};\n    // 保存各个图和各自边的关系 map，key 格式为 graphKey-fromNode类型-edge类型-toNode类型\n    const nodeEdgeNodeLabelCounted = {};\n    Object.keys(graphs).forEach((key) => {\n      // Line 271\n      const graph = graphs[key];\n      const nodeMap = graph.nodeMap;\n      // 遍历节点，记录对应图 与 每个节点的 label 到 nodeLableCounted\n      graph.nodes.forEach((node, i) => {\n        // Line 272\n        const nodeLabel = node.label;\n        const graphNodeKey = `${key}-${nodeLabel}`;\n        if (!nodeLableCounted[graphNodeKey]) {\n          let counter = nodeLabelCounter[nodeLabel] || 0;\n          counter++;\n          nodeLabelCounter[nodeLabel] = counter;\n        }\n        nodeLableCounted[graphNodeKey] = {\n          graphKey: key,\n          label: nodeLabel,\n        };\n        // 遍历该节点的所有边，记录各个图和各自边的关系到 nodeEdgeNodeLabelCounted. Line 276\n        node.edges.forEach((edge) => {\n          let nodeLabel1 = nodeLabel;\n          let nodeLabel2 = nodeMap[edge.to].label;\n          if (!directed && nodeLabel1 > nodeLabel2) {\n            const tmp = nodeLabel2;\n            nodeLabel2 = nodeLabel1;\n            nodeLabel1 = tmp;\n          }\n          const edgeLabel = edge.label;\n\n          const graphNodeEdgeNodeKey = `${key}-${nodeLabel1}-${edgeLabel}-${nodeLabel2}`;\n          const nodeEdgeNodeKey = `${nodeLabel1}-${edgeLabel}-${nodeLabel2}`;\n\n          if (!nodeEdgeNodeCounter[nodeEdgeNodeKey]) {\n            let counter = nodeEdgeNodeCounter[nodeEdgeNodeKey] || 0;\n            counter++;\n            nodeEdgeNodeCounter[nodeEdgeNodeKey] = counter; // Line281\n          }\n          nodeEdgeNodeLabelCounted[graphNodeEdgeNodeKey] = {\n            graphId: key,\n            nodeLabel1,\n            edgeLabel,\n            nodeLabel2,\n          };\n        });\n      });\n    });\n\n    // 计算频繁的节点\n    Object.keys(nodeLabelCounter).forEach((label) => {\n      const count = nodeLabelCounter[label];\n      if (count < minSupport) return;\n      const g = { nodes: [], edges: [] };\n      g.nodes.push({\n        id: \"0\",\n        label,\n      });\n      frequentSize1Subgraphs.push(g);\n      // if (minNodeNum <= 1) reportSize1 TODO\n    });\n\n    return frequentSize1Subgraphs;\n  }\n\n  run() {\n    // -------- 第一步, _generate_1edge_frequent_subgraphs：频繁的单个节点-------\n    this.frequentSize1Subgraphs = this.generate1EdgeFrequentSubGraphs();\n\n    if (this.maxNodeNum < 2) return;\n\n    const graphs = this.graphs;\n    const directed = this.directed;\n\n    // PDFS 数组的 map Line 304\n    const root: Root = {};\n    Object.keys(graphs).forEach((graphId: any) => {\n      const graph = graphs[graphId];\n      const nodeMap = graph.nodeMap;\n      // Line 306\n      graph.nodes.forEach((node) => {\n        const forwardRootEdges = this.findForwardRootEdges(graph, node);\n        // Line 308\n        forwardRootEdges.forEach((edge) => {\n          let toNode = nodeMap[edge.to];\n          const nodeEdgeNodeLabel = `${node.label}-${edge.label}-${toNode.label}`;\n          if (!root[nodeEdgeNodeLabel])\n            root[nodeEdgeNodeLabel] = {\n              projected: [],\n              nodeLabel1: node.label as string,\n              edgeLabel: edge.label as string,\n              nodeLabel2: toNode.label as string,\n            };\n          const pdfs: PDFS = {\n            graphId,\n            edge,\n            preNode: null,\n          };\n          root[nodeEdgeNodeLabel].projected.push(pdfs);\n        });\n      });\n    });\n\n    // Line 313\n    Object.keys(root).forEach((nodeEdgeNodeLabel) => {\n      const { projected, nodeLabel1, edgeLabel, nodeLabel2 } = root[\n        nodeEdgeNodeLabel\n      ];\n\n      this.dfsCode.dfsEdgeList.push(\n        new DFSedge(0, 1, nodeLabel1, edgeLabel, nodeLabel2)\n      );\n      this.subGraphMining(projected);\n      this.dfsCode.dfsEdgeList.pop();\n    });\n  }\n}\n\nconst formatGraphs = (\n  graphs: GraphDataMap,\n  directed: boolean,\n  nodeLabelProp: string,\n  edgeLabelProp: string\n): GraphMap => {\n  const result: { [key: number]: Graph } = {};\n  Object.keys(graphs).forEach((key, i) => {\n    const graph = graphs[key];\n    const fGraph = new Graph(i, true, directed);\n    const nodeIdxMap = {};\n    graph.nodes.forEach((node, j) => {\n      fGraph.addNode(j, node[nodeLabelProp]);\n      nodeIdxMap[node.id] = j;\n    });\n    graph.edges.forEach((edge, k) => {\n      const sourceIdx = nodeIdxMap[edge.source];\n      const targetIdx = nodeIdxMap[edge.target];\n      fGraph.addEdge(-1, sourceIdx, targetIdx, edge[edgeLabelProp]);\n    });\n    if (fGraph && fGraph.getNodeNum()) result[fGraph.id] = fGraph;\n  });\n  return result;\n};\n\nconst toGraphDatas = (\n  graphs: Graph[],\n  nodeLabelProp: string,\n  edgeLabelProp: string\n) => {\n  const result = [];\n  graphs.forEach((graph) => {\n    const graphData = { nodes: [], edges: [] };\n    graph.nodes.forEach((node) => {\n      graphData.nodes.push({\n        id: `${node.id}`,\n        [nodeLabelProp]: node.label,\n      });\n    });\n    graph.edges.forEach((edge) => {\n      graphData.edges.push({\n        source: `${edge.from}`,\n        target: `${edge.to}`,\n        [edgeLabelProp]: edge.label,\n      });\n    });\n    result.push(graphData);\n  });\n  return result;\n};\n\ninterface Props {\n  graphs: GraphDataMap; // 图数据\n  minSupport: number; // 算法参数，最小支持数量，根据 graphs 内图的数量指定\n  directed?: boolean; // 是否有向图，默认为 false\n  nodeLabelProp?: string; // 节点类型的属性名\n  edgeLabelProp?: string; // 边类型的属性名\n  minNodeNum?: number; // 每个子图中节点的最少个数，默认为 1\n  maxNodeNum?: number; // 每个子图中节点的最多个数，默认为 4\n  top?: number; // 返回前 top 个频繁子图，默认为 10\n  verbose?: boolean;\n}\n\nconst DEFAULT_LABEL_NAME = \"cluster\";\n\n/**\n * gSpan 频繁子图计算算法（frequent graph mining）\n * @param params 参数\n */\nconst gSpan = (params: Props): GraphData[] => {\n  // ------- 将图数据 GraphData 的 map 转换为格式 -------\n  const {\n    graphs,\n    directed = false,\n    nodeLabelProp = DEFAULT_LABEL_NAME,\n    edgeLabelProp = DEFAULT_LABEL_NAME,\n  } = params;\n  const formattedGraphs = formatGraphs(\n    graphs,\n    directed,\n    nodeLabelProp,\n    edgeLabelProp\n  );\n  const { minSupport, maxNodeNum, minNodeNum, verbose, top } = params;\n\n  // ------- 初始化与执行算法 -------\n  const algoParams = {\n    graphs: formattedGraphs,\n    minSupport,\n    maxNodeNum,\n    minNodeNum,\n    top,\n    verbose,\n    directed,\n  };\n  const calculator = new GSpan(algoParams);\n  calculator.run();\n\n  const result = toGraphDatas(\n    calculator.frequentSubgraphs,\n    nodeLabelProp,\n    edgeLabelProp\n  );\n  return result;\n};\n\nexport default gSpan;\n", "import floydWarshall from './floydWarshall';\nimport { GraphData, Matrix } from './types';\nimport gSpan, { EdgeMap, NodeMap } from './gSpan/gSpan';\nimport dijkstra from './dijkstra';\nimport { uniqueId } from './util';\n\n/** 节点对 map */\ninterface NodePairMap {\n  [key: string]: {\n    // key 的格式为 startNodeIdx-endNodeIdx\n    start: number; // 第一个节点的 idx\n    end: number; // 第二个节点的 idx\n    distance: number; // 两节点最短路径长度\n  };\n}\n\ninterface LabelMap {\n  [label: string]: any;\n}\n\n/** 邻居单元类型 */\ninterface NeighborUnit {\n  nodeId: string;\n  nodeIdx: number;\n  nodeIdxs: number[]; // the first one is nodeIdx\n  neighbors: any[]; //\n  neighborNum: number;\n  nodeLabelCountMap: {\n    [label: string]: {\n      count: number;\n      dists: number[]; // 按照从小到大排序的距离数组\n    };\n  };\n}\n\n/** 节点对的邻居交集的诱导子图 map */\ninterface InterGraphMap {\n  [key: string]: GraphData; // key 格式由节点对的 idx 组成：beginIdx-endIdx，和 nodePairMap 对应\n}\n\n/**\n * 为 graphData 中每个节点生成邻居单元数组\n * @param graphData\n * @param spm\n * @param nodeLabelProp\n * @param k k-近邻\n */\nconst findKNeighborUnits = (\n  graphData: GraphData,\n  spm: Matrix[],\n  nodeLabelProp: string = 'cluster',\n  k: number = 2,\n): NeighborUnit[] => {\n  const units: NeighborUnit[] = [];\n  const nodes = graphData.nodes;\n  spm.forEach((row: number[], i) => {\n    units.push(findKNeighborUnit(nodes, row, i, nodeLabelProp, k));\n  });\n  return units;\n};\n\nconst findKNeighborUnit = (nodes, row, i, nodeLabelProp, k) => {\n  const unitNodeIdxs = [i];\n  const neighbors = [];\n  const labelCountMap = {};\n  row.forEach((v, j) => {\n    if (v <= k && i !== j) {\n      unitNodeIdxs.push(j);\n      neighbors.push(nodes[j]);\n      const label = nodes[j][nodeLabelProp];\n      if (!labelCountMap[label]) labelCountMap[label] = { count: 1, dists: [v] };\n      else {\n        labelCountMap[label].count++;\n        labelCountMap[label].dists.push(v);\n      }\n    }\n  });\n  // 将 labelCountMap 中的 dists 按照从小到大排序，方便后面使用\n  Object.keys(labelCountMap).forEach(label => {\n    labelCountMap[label].dists = labelCountMap[label].dists.sort((a, b) => a - b);\n  });\n  return {\n    nodeIdx: i,\n    nodeId: nodes[i].id,\n    nodeIdxs: unitNodeIdxs,\n    neighbors,\n    neighborNum: unitNodeIdxs.length - 1,\n    nodeLabelCountMap: labelCountMap,\n  };\n};\n\n/**\n * 随机寻找点对，满足距离小于 k\n * @param k 参数 k，表示 k-近邻\n * @param nodeNum 参数 length\n * @param maxNodePairNum 寻找点对的数量不超过 maxNodePairNum\n * @param spm 最短路径矩阵\n */\nconst findNodePairsRandomly = (\n  k: number,\n  nodeNum: number,\n  maxNodePairNum: number,\n  kNeighborUnits: NeighborUnit[],\n  spm: Matrix[],\n): NodePairMap => {\n  // 每个节点需要随机找出的点对数\n  let nodePairNumEachNode = Math.ceil(maxNodePairNum / nodeNum);\n  const nodePairMap = {};\n  let foundNodePairCount = 0;\n\n  // 遍历节点，为每个节点随机找出 nodePairNumEachNode 个点对，满足距离小于 k。找到的点对数量超过 maxNodePairNum 或所有节点遍历结束时终止\n  kNeighborUnits.forEach((unit, i) => {\n    // 若未达到 nodePairNumEachNode，或循环次数小于最大循环次数(2 * nodeNum)，继续循环\n    let nodePairForICount = 0;\n    let outerLoopCount = 0;\n    const neighbors = unit.nodeIdxs; // the first one is the center node\n    const neighborNum = unit.neighborNum - 1;\n    while (nodePairForICount < nodePairNumEachNode) {\n      // 另一端节点在节点数组中的的 index\n      let oidx = neighbors[1 + Math.floor(Math.random() * neighborNum)];\n      let innerLoopCount = 0;\n      // 若随机得到的另一端 idx 不符合条件，则继续 random。条件是不是同一个节点、这个点对没有被记录过、距离小于 k\n      while (nodePairMap[`${i}-${oidx}`] || nodePairMap[`${oidx}-${i}`]) {\n        oidx = Math.floor(Math.random() * nodeNum);\n        innerLoopCount++;\n        if (innerLoopCount > 2 * nodeNum) break; // 循环次数大于最大循环次数(2 * nodeNum)跳出循环，避免死循环\n      }\n      if (innerLoopCount < 2 * nodeNum) {\n        // 未达到最大循环次数，说明找到了合适的另一端\n        nodePairMap[`${i}-${oidx}`] = {\n          start: i,\n          end: oidx,\n          distance: spm[i][oidx],\n        };\n        nodePairForICount++;\n        foundNodePairCount++;\n        // 如果当前找到的点对数量达到了上限，返回结果\n        if (foundNodePairCount >= maxNodePairNum) return nodePairMap;\n      }\n      outerLoopCount++;\n      if (outerLoopCount > 2 * nodeNum) break; // 循环次数大于最大循环次数(2 * nodeNum)跳出循环，避免死循环\n    }\n    // 这个节点没有找到足够 nodePairNumEachNode 的点对。更新 nodePairNumEachNode，让后续节点找更多的点对\n    if (nodePairForICount < nodePairNumEachNode) {\n      const gap = nodePairNumEachNode - nodePairForICount;\n      nodePairNumEachNode = (nodePairNumEachNode + gap) / (nodeNum - i - 1);\n    }\n  });\n  return nodePairMap;\n};\n\n/**\n * 计算所有 nodePairMap 中节点对的相交邻居诱导子图\n * @param nodePairMap 节点对 map，key 为 node1.id-node2.id，value 为 { startNodeIdx, endNodeIdx, distance }\n * @param neighborUnits 每个节点的邻居元数组\n * @param graphData 原图数据\n * @param edgeMap 边的 map，方便检索\n * @param cachedInducedGraphMap 缓存的结果，下次进入该函数将继续更新该缓存，若 key 在缓存中存在则不需要重复计算\n */\nconst getIntersectNeighborInducedGraph = (\n  nodePairMap: NodePairMap,\n  neighborUnits: NeighborUnit[],\n  graphData: GraphData,\n  cachedInducedGraphMap?: InterGraphMap,\n): InterGraphMap => {\n  const nodes = graphData.nodes;\n  if (!cachedInducedGraphMap) cachedInducedGraphMap = {};\n  Object.keys(nodePairMap).forEach(key => {\n    if (cachedInducedGraphMap && cachedInducedGraphMap[key]) return;\n    cachedInducedGraphMap[key] = { nodes: [], edges: [] };\n    const pair = nodePairMap[key];\n    const startUnitNodeIds = neighborUnits[pair.start]?.nodeIdxs;\n    const endUnitNodeIds = neighborUnits[pair.end]?.nodeIdxs;\n    if (!startUnitNodeIds || !endUnitNodeIds) return; // 不存在邻元，返回空图\n    const endSet = new Set(endUnitNodeIds);\n    const intersect = startUnitNodeIds.filter(x => endSet.has(x)); // 可能会爆栈（在 1580 + 6 nodes full-connected 时出现）\n    if (!intersect || !intersect.length) return; // 没有交集，返回空图\n    const intersectIdMap = {};\n    const intersectLength = intersect.length;\n    for (let i = 0; i < intersectLength; i++) {\n      const node = nodes[intersect[i]];\n      cachedInducedGraphMap[key].nodes.push(node); // 将交集中的点加入诱导子图\n      intersectIdMap[node.id] = true;\n    }\n    // 遍历所有边数据，如果边的两端都在交集中，将该边加入诱导子图\n    graphData.edges.forEach(edge => {\n      if (intersectIdMap[edge.source] && intersectIdMap[edge.target])\n        cachedInducedGraphMap[key].edges.push(edge);\n    });\n  });\n  return cachedInducedGraphMap;\n};\n\n/**\n * 计算 strcutre 在 graph 上的匹配数量\n * @param graph 图数据\n * @param structure 目前支持只有两个节点一条边的最简单结构\n * @param nodeLabelProp 节点类型字段名\n * @param edgeLabelProp 边类型字段名\n */\nconst getMatchedCount = (graph, structure, nodeLabelProp, edgeLabelProp) => {\n  const nodeMap = {};\n  graph.nodes.forEach(node => {\n    nodeMap[node.id] = node;\n  });\n  let count = 0;\n  if (!structure?.edges?.length || structure?.nodes?.length < 2) return 0;\n  graph.edges.forEach(e => {\n    const sourceLabel = nodeMap[e.source][nodeLabelProp];\n    const targetLabel = nodeMap[e.target][nodeLabelProp];\n    const strNodeLabel1 = structure?.nodes[0][nodeLabelProp];\n    const strNodeLabel2 = structure?.nodes[1][nodeLabelProp];\n    const strEdgeLabel = structure?.edges[0][edgeLabelProp];\n\n    if (e[edgeLabelProp] !== strEdgeLabel) return;\n    if (\n      (sourceLabel === strNodeLabel1 && targetLabel === strNodeLabel2) ||\n      (sourceLabel === strNodeLabel2 && targetLabel === strNodeLabel1)\n    ) {\n      count++;\n    }\n  });\n  return count;\n};\n\n/**\n * structures 中寻找最具有代表性的一个。这个结构是使得 matchedCountMap 的分组方式类内间距最小，类间间距最大\n * @param matchedCountMap 每个 structure 分类后的各图匹配数量，格式 { [strcture.idx]: { [interInducedGraphKey]: count } }\n * @param structureNum strcuture 个数，与 matchedCountMap.length 对应\n * @param structures\n */\nconst findRepresentStructure = (matchedCountMap, structureNum, structures) => {\n  let maxOffset = Infinity,\n    representClusterType = 0;\n  for (let i = 0; i < structureNum; i++) {\n    // 一种分组的 map，key 是 intGraph 的 key，value 是 structures[i] 的匹配个数\n    const countMapI = matchedCountMap[i];\n    // 按照 value 为该组排序，生成 keys 的数组：\n    const sortedGraphKeys = Object.keys(countMapI).sort((a, b) => {\n      return countMapI[a] - countMapI[b];\n    });\n\n    // 共 100 个 graphKeys，将 graphKeys 按顺序分为 groupNum 组\n    const groupNum = 10;\n    const clusters = []; // 总共有 groupNum 个项\n    sortedGraphKeys.forEach((key, j) => {\n      if (!clusters[j % groupNum])\n        clusters[j % groupNum] = { graphs: [], totalCount: 0, aveCount: 0 };\n      clusters[j % groupNum].graphs.push(key);\n      clusters[j % groupNum].totalCount += countMapI[key];\n    });\n\n    // 计算 cluster 与 cluster 之间的距离 innerDist，每个 cluster 内部的距离 intraDist\n    let aveIntraDist = 0; // 该类的类内平均值\n    const aveCounts = []; // 类内平均匹配数量，将用于计算类间距离\n    clusters.forEach(graphsInCluster => {\n      // 类内均值\n      const aveCount = graphsInCluster.totalCount / graphsInCluster.graphs.length;\n      graphsInCluster.aveCount = aveCount;\n      aveCounts.push(aveCount);\n\n      // 对于每类，计算类内间距平均值\n      let aveIntraPerCluster = 0;\n      const graphsNum = graphsInCluster.length;\n      graphsInCluster.graphs.forEach((graphKey1, j) => {\n        const graph1Count = countMapI[graphKey1];\n        graphsInCluster.graphs.forEach((graphKey2, k) => {\n          if (j === k) return;\n          aveIntraPerCluster += Math.abs(graph1Count - countMapI[graphKey2]);\n        });\n      });\n      aveIntraPerCluster /= (graphsNum * (graphsNum - 1)) / 2;\n      aveIntraDist += aveIntraPerCluster;\n    });\n\n    aveIntraDist /= clusters.length;\n\n    // 用类内均值计算类间距\n    let aveInterDist = 0; // 类间间距平均值\n    aveCounts.forEach((aveCount1, j) => {\n      aveCounts.forEach((aveCount2, k) => {\n        if (j === k) return;\n        aveInterDist += Math.abs(aveCount1 - aveCount2);\n      });\n      aveInterDist /= (aveCounts.length * (aveCounts.length - 1)) / 2;\n    });\n\n    // 寻找 (类间间距均值-类内间距均值) 最大的一种分组方式（对应的 structure 就是最终要找的唯一 DS(G)）\n    const offset = aveInterDist - aveIntraDist;\n    if (maxOffset < offset) {\n      maxOffset = offset;\n      representClusterType = i;\n    }\n  }\n  return {\n    structure: structures[representClusterType],\n    structureCountMap: matchedCountMap[representClusterType],\n  };\n};\n\nconst getNodeMaps = (nodes, nodeLabelProp): { nodeMap: NodeMap; nodeLabelMap: LabelMap } => {\n  const nodeMap: NodeMap = {},\n    nodeLabelMap: LabelMap = {};\n  nodes.forEach((node, i) => {\n    nodeMap[node.id] = { idx: i, node, degree: 0, inDegree: 0, outDegree: 0 };\n    const label = node[nodeLabelProp];\n    if (!nodeLabelMap[label]) nodeLabelMap[label] = [];\n    nodeLabelMap[label].push(node);\n  });\n  return { nodeMap, nodeLabelMap };\n};\n\nconst getEdgeMaps = (\n  edges,\n  edgeLabelProp,\n  nodeMap: NodeMap,\n): { edgeMap: EdgeMap; edgeLabelMap: LabelMap } => {\n  const edgeMap = {},\n    edgeLabelMap = {};\n  edges.forEach((edge, i) => {\n    edgeMap[`${uniqueId}`] = { idx: i, edge };\n    const label = edge[edgeLabelProp];\n    if (!edgeLabelMap[label]) edgeLabelMap[label] = [];\n    edgeLabelMap[label].push(edge);\n\n    const sourceNode = nodeMap[edge.source];\n    if (sourceNode) {\n      sourceNode.degree++;\n      sourceNode.outDegree++;\n    }\n    const targetNode = nodeMap[edge.target];\n    if (targetNode) {\n      targetNode.degree++;\n      targetNode.inDegree++;\n    }\n  });\n  return { edgeMap, edgeLabelMap };\n};\n\n/**\n * 输出最短路径的 map，key 为 sourceNode.id-targetNode.id，value 为这两个节点的最短路径长度\n * @param nodes\n * @param spm\n * @param directed\n */\nconst getSpmMap = (nodes, spm, directed): { [key: string]: number } => {\n  const length = spm.length;\n  const map = {};\n  spm.forEach((row, i) => {\n    const start = directed ? 0 : i + 1;\n    const iId = nodes[i].id;\n    for (let j = start; j < length; j++) {\n      if (i === j) continue;\n      const jId = nodes[j].id;\n      const dist = row[j];\n      map[`${iId}-${jId}`] = dist;\n      if (!directed) map[`${jId}-${iId}`] = dist;\n    }\n  });\n  return map;\n};\n\n/**\n * 计算一对节点（node1，node2）的 NDS 距离\n * @param graph 原图数据\n * @param node1\n * @param node2\n */\nconst getNDSDist = (\n  graph,\n  node1,\n  node2,\n  nodeMap,\n  spDist,\n  kNeighborUnits,\n  structure,\n  nodeLabelProp,\n  edgeLabelProp,\n  cachedNDSMap,\n  cachedInterInducedGraph,\n) => {\n  const key = `${node1.id}-${node2.id}`;\n  if (cachedNDSMap && cachedNDSMap[key]) return cachedNDSMap[key];\n  let interInducedGraph = cachedInterInducedGraph ? cachedInterInducedGraph[key] : undefined;\n  // 若没有缓存相交邻居诱导子图，计算\n  if (!interInducedGraph) {\n    const pairMap: NodePairMap = {\n      [key]: {\n        start: nodeMap[node1.id].idx,\n        end: nodeMap[node2.id].idx,\n        distance: spDist,\n      },\n    };\n\n    cachedInterInducedGraph = getIntersectNeighborInducedGraph(\n      pairMap,\n      kNeighborUnits,\n      graph,\n      cachedInterInducedGraph,\n    );\n    interInducedGraph = cachedInterInducedGraph[key];\n  }\n\n  return getMatchedCount(interInducedGraph, structure, nodeLabelProp, edgeLabelProp);\n};\n\n/**\n * 计算 pattern 上绩点的度数并存储到 minPatternNodeLabelDegreeMap\n */\nconst stashPatternNodeLabelDegreeMap = (minPatternNodeLabelDegreeMap, neighborLabel, patternNodeMap, patternNodeLabelMap) => {\n  let minPatternNodeLabelDegree = minPatternNodeLabelDegreeMap[neighborLabel]?.degree;\n  let minPatternNodeLabelInDegree = minPatternNodeLabelDegreeMap[neighborLabel]?.inDegree;\n  let minPatternNodeLabelOutDegree = minPatternNodeLabelDegreeMap[neighborLabel]?.outDegree;\n\n  if (minPatternNodeLabelDegreeMap[neighborLabel] === undefined) {\n    minPatternNodeLabelDegree = Infinity;\n    minPatternNodeLabelInDegree = Infinity;\n    minPatternNodeLabelOutDegree = Infinity;\n    patternNodeLabelMap[neighborLabel].forEach(patternNodeWithLabel => {\n      const patternNodeDegree = patternNodeMap[patternNodeWithLabel.id].degree;\n      if (minPatternNodeLabelDegree > patternNodeDegree)\n        minPatternNodeLabelDegree = patternNodeDegree;\n      const patternNodeInDegree = patternNodeMap[patternNodeWithLabel.id].inDegree;\n      if (minPatternNodeLabelInDegree > patternNodeInDegree)\n        minPatternNodeLabelInDegree = patternNodeInDegree;\n      const patternNodeOutDegree = patternNodeMap[patternNodeWithLabel.id].outDegree;\n      if (minPatternNodeLabelOutDegree > patternNodeOutDegree)\n        minPatternNodeLabelOutDegree = patternNodeOutDegree;\n    });\n    minPatternNodeLabelDegreeMap[neighborLabel] = {\n      degree: minPatternNodeLabelDegree,\n      inDegree: minPatternNodeLabelInDegree,\n      outDegree: minPatternNodeLabelOutDegree\n    };\n  }\n\n  return {\n    minPatternNodeLabelDegree, \n    minPatternNodeLabelInDegree,\n    minPatternNodeLabelOutDegree\n  }\n}\n\n/**\n * GADDI 模式匹配\n * @param graphData 原图数据\n * @param pattern 搜索图（需要在原图上搜索的模式）数据\n * @param directed 是否计算有向图，默认 false\n * @param k 参数 k，表示 k-近邻\n * @param length 参数 length\n * @param nodeLabelProp 节点数据中代表节点标签（分类信息）的属性名。默认为 cluster\n * @param edgeLabelProp 边数据中代表边标签（分类信息）的属性名。默认为 cluster\n */\nconst GADDI = (\n  graphData: GraphData,\n  pattern: GraphData,\n  directed: boolean = false,\n  k: number,\n  length: number,\n  nodeLabelProp: string = 'cluster',\n  edgeLabelProp: string = 'cluster',\n): GraphData[] => {\n  if (!graphData || !graphData.nodes) return;\n  // 分为三步：\n  // 0. 预计算：节点/边数，邻接矩阵、最短路径矩阵\n  // 1. 处理原图 graphData。再分为 1~5 小步\n  // 2. 匹配\n\n  // console.log(\"----- stage-pre: preprocessing -------\");\n\n  // -------- 第零步，预计算：节点/边数，邻接矩阵、最短路径矩阵-------\n  const nodeNum = graphData.nodes.length;\n  if (!nodeNum) return;\n  // console.log(\"----- stage-pre.1: calc shortest path matrix for graph -------\");\n  const spm = floydWarshall(graphData, directed);\n  // console.log(\n  //   \"----- stage-pre.2: calc shortest path matrix for pattern -------\"\n  // );\n  const patternSpm = floydWarshall(pattern, directed);\n  // console.log(\n  //   \"----- stage-pre.3: calc shortest path matrix map for graph -------\"\n  // );\n  const spmMap = getSpmMap(graphData.nodes, spm, directed);\n  // console.log(\n  //   \"----- stage-pre.4: calc shortest path matrix map for pattern -------\"\n  // );\n  const patternSpmMap = getSpmMap(pattern.nodes, patternSpm, directed);\n\n  // console.log(\"----- stage-pre.5: establish maps -------\");\n  // 节点的 map，以 id 为 id 映射，方便后续快速检索\n  const { nodeMap, nodeLabelMap } = getNodeMaps(graphData.nodes, nodeLabelProp);\n  const { nodeMap: patternNodeMap, nodeLabelMap: patternNodeLabelMap } = getNodeMaps(\n    pattern.nodes,\n    nodeLabelProp,\n  );\n\n  // 计算节点度数\n  getEdgeMaps(graphData.edges, edgeLabelProp, nodeMap);\n\n  const { edgeLabelMap: patternEdgeLabelMap } = getEdgeMaps(\n    pattern.edges,\n    edgeLabelProp,\n    patternNodeMap,\n  );\n\n  // 若未指定 length，自动计算 pattern 半径（最短路径最大值）\n  let patternSpmSpread = [];\n  patternSpm?.forEach(row => {\n    patternSpmSpread = patternSpmSpread.concat(row);\n  })\n  if (!length) length = Math.max(...patternSpmSpread, 2);\n  if (!k) k = length;\n\n  // console.log(\"params\", directed, length, k);\n\n  // console.log(\"----- stage-pre.6: calc k neighbor units -------\");\n  // 计算每个节点的 k 邻元集合\n  const kNeighborUnits = findKNeighborUnits(graphData, spm, nodeLabelProp, k);\n  const patternKNeighborUnits = findKNeighborUnits(pattern, patternSpm, nodeLabelProp, k);\n\n  // console.log(\n  //   \"----- stage0: going to processing graph and find intersect neighbor induced graphs -------\"\n  // );\n\n  // console.log(\"----- stage0.1: going to select random node pairs -------\");\n  // -------- 第一步，处理原图 graphData-------\n\n  // 1.1. 随机选择最多 100 个点对，满足距离小于 Length 和 k\n  // 当 graphData 少于 20 个节点，则不能找出 100 个点对，只找出不多于 n(n-1)/2 个点对\n  const maxNodePairNum = Math.min(100, (nodeNum * (nodeNum - 1)) / 2);\n  const nodePairsMap = findNodePairsRandomly(\n    k,\n    nodeNum,\n    maxNodePairNum,\n    kNeighborUnits,\n    spm,\n  );\n\n  // console.log(\n  //   \"----- stage0.2: going to calculate intersect neighbor induced graphs -------\"\n  // );\n  // 1.2. 生成上面节点对的相应相交邻居诱导子图。格式为 {'beginNodeIdx-endNodeIdx': {nodes: [], edges: []}}\n  let intGMap = getIntersectNeighborInducedGraph(nodePairsMap, kNeighborUnits, graphData);\n  // 1.3. 使用 gSpan 算法（frequent graph mining）计算 ISIntG 的前 10 个频率最高的子结构（3-4条边）\n  const top = 10,\n    minSupport = 1,\n    minNodeNum = 1,\n    maxNodeNum = 4;\n  const params = {\n    graphs: intGMap,\n    nodeLabelProp,\n    edgeLabelProp,\n    minSupport,\n    minNodeNum,\n    maxNodeNum,\n    directed,\n  };\n\n  // console.log(\n  //   \"----- stage1: (gSpan) going to find frequent structure dsG -------\"\n  // );\n  // console.log(\"----- stage1.1: going to run gSpan -------\");\n  // 暂时假设生成的 sub structure 都只有一条边\n  const freStructures = gSpan(params).slice(0, top);\n  // structureNum 可能小于 top\n  const structureNum = freStructures.length;\n\n  // 1.4. 计算上述 10 个子结构在 intGMap 中每个诱导子图的匹配个数\n  const matchedCountMap = [];\n  freStructures.forEach((structure, i) => {\n    matchedCountMap[i] = {};\n    Object.keys(intGMap).forEach(key => {\n      const graph = intGMap[key];\n      const subStructureCount = getMatchedCount(graph, structure, nodeLabelProp, edgeLabelProp);\n      matchedCountMap[i][key] = subStructureCount;\n    });\n  });\n\n  // console.log(\n  //   \"----- stage1.1: going to find the most represent strucutre -------\"\n  // );\n\n  // 1.5. 对于每个子结构，根据匹配个数为 intGMap 中的诱导子图分组，生成 structureNum 种分组\n  // 计算每种分组的类间距和类内间距，找到类间距最大、类内间距最小的一种分组，这种分组对应的子结构被选为唯一代表性子结构 DS(G)\n  const { structure: dsG, structureCountMap: ndsDist } = findRepresentStructure(\n    matchedCountMap,\n    structureNum,\n    freStructures,\n  );\n\n  // -------- 第二步，匹配-------\n  // 2.1 找到从 Q 中的一个节点作为起始节点，寻找 G 中的匹配。这个其实节点的标签可以在 G 中找到最多的节点\n  let beginPNode = pattern.nodes[0],\n    candidates = [],\n    label = pattern.nodes[0]?.[nodeLabelProp],\n    maxNodeNumWithSameLabel = -Infinity;\n  pattern.nodes.forEach(node => {\n    const pLabel = node[nodeLabelProp];\n    const nodesWithSameLabel = nodeLabelMap[pLabel]\n    if (nodesWithSameLabel?.length > maxNodeNumWithSameLabel) {\n      maxNodeNumWithSameLabel = nodesWithSameLabel.length;\n      candidates = nodesWithSameLabel;\n      label = pLabel;\n      beginPNode = node;\n    }\n  });\n\n  // console.log(\"----- stage2: going to find candidates -------\");\n\n  // 全局缓存，避免重复计算\n  const minPatternNodeLabelDegreeMap = {}; // key 是 label，value 是该 label 节点的最小度数\n  let patternIntGraphMap = {},\n    patternNDSDist = {}, // key 为 node.id-node.id\n    patternNDSDistMap = {}; // key 为 node.id-label2，value nds距离值数组（按从大到小排序，无需关心具体对应哪个 node2）\n  // 2.2.2 对于 Q 中的另一个标签的 k 个节点，计算它们到 node 的最短路径以及 NDS 距离\n  const patternSpDist = {};\n  const patternSpDistBack = {};\n  Object.keys(patternNodeLabelMap).forEach((label2, j) => {\n    patternSpDist[label2] = [];\n    if (directed) {\n      patternSpDistBack[label2] = [];\n    }\n    let maxDist = -Infinity;\n    const patternNodesWithLabel2 = patternNodeLabelMap[label2];\n    const patternNodePairMap = {};\n    patternNodesWithLabel2.forEach(nodeWithLabel2 => {\n      const dist = patternSpmMap[`${beginPNode.id}-${nodeWithLabel2.id}`];\n      dist && patternSpDist[label2].push(dist);\n      if (maxDist < dist) maxDist = dist;\n      patternNodePairMap[`${beginPNode.id}-${nodeWithLabel2.id}`] = {\n        start: 0,\n        end: patternNodeMap[nodeWithLabel2.id].idx,\n        distance: dist,\n      };\n      if (directed) {\n        const distBack = patternSpmMap[`${nodeWithLabel2.id}-${beginPNode.id}`];\n        distBack && patternSpDistBack[label2].push(distBack);\n      }\n    });\n\n    // spDist[label2] 按照从小到大排序\n    patternSpDist[label2] = patternSpDist[label2].sort((a, b) => a - b);\n    if (directed) patternSpDistBack[label2] = patternSpDistBack[label2].sort((a, b) => a - b);\n\n    // 计算 Q 中所有 label2 节点到 beginPNode 的 NDS 距离\n    // 所有 label2 节点到 beginPNode 的邻居相交诱导子图：\n    // key: node1.id-node2.id\n    patternIntGraphMap = getIntersectNeighborInducedGraph(\n      patternNodePairMap,\n      patternKNeighborUnits,\n      pattern,\n      patternIntGraphMap,\n    );\n    // pattern 中 beginNode 到当前 label2 节点 的 NDS 距离（数组，无需关心具体对应到哪个节点）\n    let currentPatternNDSDistArray = [];\n    Object.keys(patternNodePairMap).forEach(key => {\n      if (patternNDSDist[key]) {\n        currentPatternNDSDistArray.push(patternNDSDist[key]);\n        return; // 缓存过则不需要再次计算\n      }\n      const patternIntGraph = patternIntGraphMap[key];\n      patternNDSDist[key] = getMatchedCount(patternIntGraph, dsG, nodeLabelProp, edgeLabelProp);\n      currentPatternNDSDistArray.push(patternNDSDist[key]);\n    });\n\n    // 根据值为 currentPatternNDSDist 从大到小排序\n    currentPatternNDSDistArray = currentPatternNDSDistArray.sort((a, b) => b - a);\n    patternNDSDistMap[`${beginPNode.id}-${label2}`] = currentPatternNDSDistArray;\n\n    if (label2 === label) return;\n\n    const candidatesNum = candidates?.length || 0;\n    for (let m = candidatesNum - 1; m >= 0; m--) {\n      const cNode = candidates[m];\n\n      // prune1：若 candidates 中节点 cNode 的 kNeighborUnits 中标签为 label2 的节点个数少于 pattern 中 label2 个数，删去它\n      const graphNeighborUnit = kNeighborUnits[nodeMap[cNode.id].idx];\n      const graphNeighborUnitCountMap = graphNeighborUnit.nodeLabelCountMap[label2];\n      const patternLabel2Num = patternNodeLabelMap[label2].length;\n      if (!graphNeighborUnitCountMap || graphNeighborUnitCountMap.count < patternLabel2Num) {\n        candidates.splice(m, 1);\n        continue;\n      }\n\n      // prune2：若 candidates 中节点 cNode 到 kNeighborUnits 中标签为 label2 的节点最短路径大于 patternSpDist[label2]，删去它\n      // (prune2 规则即：candidate 相关的最短路径的最大 spDist[label2].length 个，按照大小顺序依次和 patternSpDist[label2] 中的值比较，只要遇到一个是 G > Q 的，就删去这个 candidate)\n      let prune2Invalid = false;\n      for (let n = 0; n < patternLabel2Num; n++) {\n        if (graphNeighborUnitCountMap.dists[n] > patternSpDist[label2][n]) {\n          prune2Invalid = true;\n          break;\n        }\n      }\n      if (prune2Invalid) {\n        candidates.splice(m, 1);\n        continue;\n      }\n\n      // prune3：若 candidates 中节点 cNode 到 kNeighborUnits 中标签为 label2 的节点 NDS 距离小于 patternNDSDist[beginNode.id-label2]，删去它\n      // TODO：prune3，currentPatternNDSDistArray 与 currentNDSDist 的比较\n\n      // 计算 G 中所有 label2 节点到 cNode 的 NDS 距离\n      // 所有 label2 节点到 cNode 的邻居相交诱导子图：\n      const cNodePairMap = {};\n      graphNeighborUnit.neighbors.forEach(neighborNode => {\n        const dist = spmMap[`${cNode.id}-${neighborNode.id}`];\n        cNodePairMap[`${cNode.id}-${neighborNode.id}`] = {\n          start: nodeMap[cNode.id].idx,\n          end: nodeMap[neighborNode.id].idx,\n          distance: dist,\n        };\n      });\n      // 更新 intGMap\n      intGMap = getIntersectNeighborInducedGraph(cNodePairMap, kNeighborUnits, graphData, intGMap);\n      // candidate 到它周围 label2 节点的 NDS 距离, key 是 node.id-node.id\n      let currentNDSDistArray = [];\n      Object.keys(cNodePairMap).forEach(key => {\n        if (ndsDist[key]) {\n          currentNDSDistArray.push(ndsDist[key]);\n          return; // 缓存过则不需要再次计算\n        }\n        const intGraph = intGMap[key];\n        ndsDist[key] = getMatchedCount(intGraph, dsG, nodeLabelProp, edgeLabelProp);\n        currentNDSDistArray.push(ndsDist[key]);\n      });\n\n      // 根据值为 currentNDSDistArray 从大到小排序\n      currentNDSDistArray = currentNDSDistArray.sort((a, b) => b - a);\n\n      let prune3Invalid = false;\n      for (let n = 0; n < patternLabel2Num; n++) {\n        if (currentNDSDistArray[n] < currentPatternNDSDistArray[n]) {\n          prune3Invalid = true;\n          break;\n        }\n      }\n      if (prune3Invalid) {\n        candidates.splice(m, 1);\n        continue;\n      }\n    }\n  });\n\n  const candidateGraphs = [];\n\n  // console.log(\n  //   \"----- stage3: going to splice neighbors for each candidate graph -------\"\n  // );\n\n  // candidates 经过筛选后，以每个 candidate 为中心，生成 Length-neighbor 的邻居诱导子图\n  // 并在诱导子图中去除不可能在 Q 上找到匹配的点：在 Q 上不存在的 label，其他 label 到 candidate 的最大最短距离符合 Q、NDS 距离符合 Q\n  candidates?.forEach(candidate => {\n    const nodeIdx = nodeMap[candidate.id].idx;\n    const lengthNeighborUnit = findKNeighborUnit(\n      graphData.nodes,\n      spm[nodeIdx],\n      nodeIdx,\n      nodeLabelProp,\n      length,\n    );\n\n    const neighborNodes = lengthNeighborUnit.neighbors;\n\n    // 删除不可能找到匹配的邻居点\n    const neighborNum = neighborNodes.length;\n    let unmatched = false;\n    for (let i = neighborNum - 1; i >= 0; i--) {\n      // 如果通过裁剪，符合条件的节点数量已过少，说明不能匹配这个 candidate 相关的图\n      if (neighborNodes.length + 1 < pattern.nodes.length) {\n        unmatched = true;\n        return;\n      }\n      const neighborNode = neighborNodes[i];\n      const neighborLabel = neighborNode[nodeLabelProp];\n      // prune1: 若该邻居点的 label 不存在于 pattern 中，移除这个点\n      if (!patternNodeLabelMap[neighborLabel] || !patternNodeLabelMap[neighborLabel].length) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n      // prune2: 若该邻居点到 candidate 的最短路径比和它有相同 label 的节点到 beginPNode 的最大最短路径长度长，移除这个点\n      // prune2.1: 如果没有这个标签到 beginPNode 的距离记录，说明 pattern 上（可能 beginPNode 是这个 label）没有其他这个 label 的节点\n      if (!patternSpDist[neighborLabel] || !patternSpDist[neighborLabel].length) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n      const key = `${candidate.id}-${neighborNode.id}`;\n\n      // prune2.2\n      const distToCandidate = spmMap[key];\n      let idx = patternSpDist[neighborLabel].length - 1;\n      let maxDistWithLabelInPattern = patternSpDist[neighborLabel][idx]; // patternSpDist[neighborLabel] 已经按照从小到大排序\n      if (distToCandidate > maxDistWithLabelInPattern) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n    if (directed) {\n      const keyBack = `${neighborNode.id}-${candidate.id}`;\n      const distFromCandidate = spmMap[keyBack];\n      idx = patternSpDistBack[neighborLabel].length - 1;\n      let maxBackDistWithLabelInPattern = patternSpDistBack[neighborLabel][idx];\n      if (distFromCandidate > maxBackDistWithLabelInPattern) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n    }\n\n      // prune3: 若该邻居点到 candidate 的 NDS 距离比和它有相同 label 的节点到 beginPNode 的最小 NDS 距离小，移除这个点\n      const ndsToCandidate = ndsDist[key]\n        ? ndsDist[key]\n        : getNDSDist(\n            graphData,\n            candidate,\n            neighborNode,\n            nodeMap,\n            distToCandidate,\n            kNeighborUnits,\n            dsG,\n            nodeLabelProp,\n            edgeLabelProp,\n            ndsDist,\n            intGMap,\n          );\n      const patternKey = `${beginPNode.id}-${neighborLabel}`;\n      const minNdsWithLabelInPattern =\n        patternNDSDistMap[patternKey][patternNDSDistMap[patternKey].length - 1]; // patternNDSDist[key] 一定存在\n      if (ndsToCandidate < minNdsWithLabelInPattern) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n      // prune4: 若该邻居点的度数小于 pattern 同 label 节点最小度数，删去该点\n      const {\n        minPatternNodeLabelDegree,\n        minPatternNodeLabelInDegree,\n        minPatternNodeLabelOutDegree\n      } = stashPatternNodeLabelDegreeMap(minPatternNodeLabelDegreeMap, neighborLabel, patternNodeMap,patternNodeLabelMap);\n\n      if (nodeMap[neighborNode.id].degree < minPatternNodeLabelDegree) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n    }\n\n    // 节点在个数上符合匹配（不少于 pattern 的节点个数），现在筛选相关边\n    if (!unmatched) {\n      candidateGraphs.push({\n        nodes: [candidate].concat(neighborNodes),\n      });\n    }\n  });\n\n  // console.log(\n  //   \"----- stage4: going to splice edges and neighbors for each candidate graph -------\"\n  // );\n\n  const { length: undirectedLengthsToBeginPNode } = dijkstra(pattern, beginPNode.id, false);\n\n  let undirectedLengthsToBeginPNodeLabelMap = {};\n  if (directed) {\n    Object.keys(undirectedLengthsToBeginPNode).forEach(nodeId => {\n      const nodeLabel = patternNodeMap[nodeId].node[nodeLabelProp];\n      if (!undirectedLengthsToBeginPNodeLabelMap[nodeLabel])\n        undirectedLengthsToBeginPNodeLabelMap[nodeLabel] = [undirectedLengthsToBeginPNode[nodeId]];\n      else\n        undirectedLengthsToBeginPNodeLabelMap[nodeLabel].push(\n          undirectedLengthsToBeginPNode[nodeId],\n        );\n    });\n    Object.keys(undirectedLengthsToBeginPNodeLabelMap).forEach(pLabel => {\n      undirectedLengthsToBeginPNodeLabelMap[pLabel].sort((a, b) => a - b);\n    });\n  } else {\n    undirectedLengthsToBeginPNodeLabelMap = patternSpDist;\n  }\n\n  // 现在 candidateGraphs 里面只有节点，进行边的筛选\n  let candidateGraphNum = candidateGraphs.length;\n  for (let i = candidateGraphNum - 1; i >= 0; i--) {\n    const candidateGraph = candidateGraphs[i];\n    const candidate = candidateGraph.nodes[0];\n\n    const candidateNodeLabelCountMap = {};\n    const candidateNodeMap = {};\n    candidateGraph.nodes.forEach((node, q) => {\n      candidateNodeMap[node.id] = {\n        idx: q,\n        node,\n        degree: 0,\n        inDegree: 0,\n        outDegree: 0\n      };\n      const cNodeLabel = node[nodeLabelProp];\n      if (!candidateNodeLabelCountMap[cNodeLabel]) candidateNodeLabelCountMap[cNodeLabel] = 1;\n      else candidateNodeLabelCountMap[cNodeLabel]++;\n    });\n\n    // 根据 candidate 和 neighborNodes 中的节点生成 G 的诱导子图\n    // 即，将 graphData 上两端都在 candidateGraph.nodes 中的边放入 candidateEdges\n    const candidateEdges = [];\n    const edgeLabelCountMap = {};\n    graphData.edges.forEach(edge => {\n      if (candidateNodeMap[edge.source] && candidateNodeMap[edge.target]) {\n        candidateEdges.push(edge);\n        if (!edgeLabelCountMap[edge[edgeLabelProp]]) edgeLabelCountMap[edge[edgeLabelProp]] = 1;\n        else edgeLabelCountMap[edge[edgeLabelProp]]++;\n        candidateNodeMap[edge.source].degree++;\n        candidateNodeMap[edge.target].degree++;\n        candidateNodeMap[edge.source].outDegree++;\n        candidateNodeMap[edge.target].inDegree++;\n      }\n    });\n\n    // prune：若有一个 edgeLabel 在 candidateGraph 上的个数少于 pattern，去除该图\n    const pattenrEdgeLabelNum = Object.keys(patternEdgeLabelMap).length;\n    let prunedByEdgeLabel = false;\n    for (let e = 0; e < pattenrEdgeLabelNum; e++) {\n      const label = Object.keys(patternEdgeLabelMap)[e];\n      if (\n        !edgeLabelCountMap[label] ||\n        edgeLabelCountMap[label] < patternEdgeLabelMap[label].length\n      ) {\n        prunedByEdgeLabel = true;\n        break;\n      }\n    }\n    if (prunedByEdgeLabel) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    // 遍历 candidateEdges，进行边的筛选\n    let candidateEdgeNum = candidateEdges.length;\n\n    // prune：若边数过少，去除该图\n    if (candidateEdgeNum < pattern.edges.length) {\n      candidateGraphs.splice(i, 1);\n      break;\n    }\n    let candidateGraphInvalid = false;\n    for (let e = candidateEdgeNum - 1; e >= 0; e--) {\n      const edge = candidateEdges[e];\n      const edgeLabel = edge[edgeLabelProp];\n      const patternEdgesWithLabel = patternEdgeLabelMap[edgeLabel];\n\n      // prune 1: 若边的 label 不存在于 pattern 边 label 中，去除该边\n      if (!patternEdgesWithLabel || !patternEdgesWithLabel.length) {\n        edgeLabelCountMap[edgeLabel]--;\n        // 若这个 label 的 count 减少之后，该 label 的边数不足，去除该图\n        if (patternEdgesWithLabel && edgeLabelCountMap[edgeLabel] < patternEdgesWithLabel.length) {\n          candidateGraphInvalid = true;\n          break;\n        }\n        candidateEdges.splice(e, 1);\n        candidateNodeMap[edge.source].degree--;\n        candidateNodeMap[edge.target].degree--;\n        candidateNodeMap[edge.source].outDegree--;\n        candidateNodeMap[edge.target].inDegree--;\n        continue;\n      }\n\n      // prune 2: 若边的 label +两端 label 的三元组关系不能在 pattern 中找到，去除该边\n      const sourceLabel = candidateNodeMap[edge.source].node[nodeLabelProp];\n      const targetLabel = candidateNodeMap[edge.target].node[nodeLabelProp];\n\n      let edgeMatched = false;\n      patternEdgesWithLabel.forEach(patternEdge => {\n        const patternSource = patternNodeMap[patternEdge.source].node;\n        const patternTarget = patternNodeMap[patternEdge.target].node;\n        if (\n          patternSource[nodeLabelProp] === sourceLabel &&\n          patternTarget[nodeLabelProp] === targetLabel\n        )\n          edgeMatched = true;\n        if (\n          !directed &&\n          patternSource[nodeLabelProp] === targetLabel &&\n          patternTarget[nodeLabelProp] === sourceLabel\n        )\n          edgeMatched = true;\n      });\n      if (!edgeMatched) {\n        edgeLabelCountMap[edgeLabel]--;\n        // 若这个 label 的 count 减少之后，该 label 的边数不足，去除该图\n        if (patternEdgesWithLabel && edgeLabelCountMap[edgeLabel] < patternEdgesWithLabel.length) {\n          candidateGraphInvalid = true;\n          break;\n        }\n        candidateEdges.splice(e, 1);\n        candidateNodeMap[edge.source].degree--;\n        candidateNodeMap[edge.target].degree--;\n        candidateNodeMap[edge.source].outDegree--;\n        candidateNodeMap[edge.target].inDegree--;\n        continue;\n      }\n    }\n\n    // prune2: 删除边的过程中，发现边数过少/边 label 数过少时，去除该图\n    if (candidateGraphInvalid) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    candidateGraph.edges = candidateEdges;\n\n    const { length: lengthsToCandidate } = dijkstra(\n      candidateGraph,\n      candidateGraph.nodes[0].id,\n      false, // 此处计算路径长度用于判断是否连通，因此使用无向图\n    );\n    Object.keys(lengthsToCandidate)\n      .reverse()\n      .forEach(targetId => {\n        if (targetId === candidateGraph.nodes[0].id || candidateGraphInvalid) return;\n        // prune4: 通过上述裁剪，可能导致该邻居子图变为不连通。裁剪掉目前在这个邻居子图中和 candidate（第一个节点）不连通的节点\n        if (lengthsToCandidate[targetId] === Infinity) {\n          const targetNodeLabel = candidateNodeMap[targetId].node[nodeLabelProp];\n          candidateNodeLabelCountMap[targetNodeLabel]--;\n          if (\n            candidateNodeLabelCountMap[targetNodeLabel] <\n            patternNodeLabelMap[targetNodeLabel].length\n          ) {\n            candidateGraphInvalid = true;\n            return;\n          }\n          const idx = candidateGraph.nodes.indexOf(candidateNodeMap[targetId].node);\n          candidateGraph.nodes.splice(idx, 1);\n          candidateNodeMap[targetId] = undefined;\n          return;\n        }\n        // prune5: 经过边裁剪后，可能又出现了最短路径过长的节点 （比 pattern 中同 label 的节点到 beginNode 最大最短距离远），删去这些节点\n        const nLabel = nodeMap[targetId].node[nodeLabelProp];\n        if (\n          !undirectedLengthsToBeginPNodeLabelMap[nLabel] ||\n          !undirectedLengthsToBeginPNodeLabelMap[nLabel].length ||\n          lengthsToCandidate[targetId] >\n            undirectedLengthsToBeginPNodeLabelMap[nLabel][\n              undirectedLengthsToBeginPNodeLabelMap[nLabel].length - 1\n            ]\n        ) {\n          const targetNodeLabel = candidateNodeMap[targetId].node[nodeLabelProp];\n          candidateNodeLabelCountMap[targetNodeLabel]--;\n          if (\n            candidateNodeLabelCountMap[targetNodeLabel] <\n            patternNodeLabelMap[targetNodeLabel].length\n          ) {\n            candidateGraphInvalid = true;\n            return;\n          }\n          const idx = candidateGraph.nodes.indexOf(candidateNodeMap[targetId].node);\n          candidateGraph.nodes.splice(idx, 1);\n          candidateNodeMap[targetId] = undefined;\n        }\n      });\n\n    if (candidateGraphInvalid) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    let degreeChanged = true;\n    let loopCount = 0;\n    while (degreeChanged && !candidateGraphInvalid) {\n      degreeChanged = false;\n\n      // candidate 度数不足，删去该图\n      const condition = directed ? (candidateNodeMap[candidate.id].degree < patternNodeMap[beginPNode.id].degree || \n        candidateNodeMap[candidate.id].inDegree < patternNodeMap[beginPNode.id].inDegree ||\n        candidateNodeMap[candidate.id].outDegree < patternNodeMap[beginPNode.id].outDegree) :\n        candidateNodeMap[candidate.id].degree < patternNodeMap[beginPNode.id].degree;\n      if (condition) {\n        candidateGraphInvalid = true;\n        break;\n      }\n      // candidate label 个数不足，删去该图\n      if (\n        candidateNodeLabelCountMap[candidate[nodeLabelProp]] <\n        patternNodeLabelMap[candidate[nodeLabelProp]].length\n      ) {\n        candidateGraphInvalid = true;\n        break;\n      }\n\n      // prune6：去除度数过小的节点\n      const currentCandidateNodeNum = candidateGraph.nodes.length;\n      for (let o = currentCandidateNodeNum - 1; o >= 0; o--) {\n        const cgNode = candidateGraph.nodes[o];\n        const nodeDegree = candidateNodeMap[cgNode.id].degree;\n        const nodeInDegree = candidateNodeMap[cgNode.id].inDegree;\n        const nodeOutDegree = candidateNodeMap[cgNode.id].outDegree;\n        const cNodeLabel = cgNode[nodeLabelProp];\n        \n        const {\n          minPatternNodeLabelDegree,\n          minPatternNodeLabelInDegree,\n          minPatternNodeLabelOutDegree\n        } = stashPatternNodeLabelDegreeMap(minPatternNodeLabelDegreeMap, cNodeLabel, patternNodeMap,patternNodeLabelMap);\n        \n        const deleteCondition = directed ? (nodeDegree < minPatternNodeLabelDegree || \n          nodeInDegree < minPatternNodeLabelInDegree ||\n          nodeOutDegree < minPatternNodeLabelOutDegree) :\n          nodeDegree < minPatternNodeLabelDegree;\n        if (deleteCondition) {\n          candidateNodeLabelCountMap[cgNode[nodeLabelProp]]--;\n          // 节点 label 个数不足\n          if (\n            candidateNodeLabelCountMap[cgNode[nodeLabelProp]] <\n            patternNodeLabelMap[cgNode[nodeLabelProp]].length\n          ) {\n            candidateGraphInvalid = true;\n            break;\n          }\n          candidateGraph.nodes.splice(o, 1);\n          candidateNodeMap[cgNode.id] = undefined;\n          degreeChanged = true;\n        }\n      }\n      if (candidateGraphInvalid || (!degreeChanged && loopCount !== 0)) break;\n      // 经过 prune5 节点裁剪，删去端点已经不在 candidateGraph 中的边\n      candidateEdgeNum = candidateEdges.length;\n      for (let y = candidateEdgeNum - 1; y >= 0; y--) {\n        const cedge = candidateEdges[y];\n        if (!candidateNodeMap[cedge.source] || !candidateNodeMap[cedge.target]) {\n          candidateEdges.splice(y, 1);\n          const edgeLabel = cedge[edgeLabelProp];\n          edgeLabelCountMap[edgeLabel]--;\n          if (candidateNodeMap[cedge.source]) {\n            candidateNodeMap[cedge.source].degree--;\n            candidateNodeMap[cedge.source].outDegree--;\n          }\n          if (candidateNodeMap[cedge.target]) {\n            candidateNodeMap[cedge.target].degree--;\n            candidateNodeMap[cedge.target].inDegree--;\n          }\n          // 边 label 数量不足\n          if (\n            patternEdgeLabelMap[edgeLabel] &&\n            edgeLabelCountMap[edgeLabel] < patternEdgeLabelMap[edgeLabel].length\n          ) {\n            candidateGraphInvalid = true;\n            break;\n          }\n          degreeChanged = true;\n        }\n      }\n      loopCount++;\n    }\n\n    if (candidateGraphInvalid) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    // prune: 若节点/边数过少，节点/边 label 过少，去掉这个图\n    if (\n      candidateGraphInvalid ||\n      candidateGraph.nodes.length < pattern.nodes.length ||\n      candidateEdges.length < pattern.edges.length\n    ) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n  }\n\n  // 此时已经生成的多个 candidateGraphs，可能有重复\n\n  // console.log(\n  //   \"----- stage5: going to splice dulplicated candidate graphs -------\"\n  // );\n\n  // 删去 candidateGraphs 中一模一样的子图，通过边的 node-node-edgeLabel 作为 key，这类边个数作为 value，进行匹配\n  let currentLength = candidateGraphs.length;\n  for (let i = 0; i <= currentLength - 1; i++) {\n    const cg1 = candidateGraphs[i];\n    const cg1EdgeMap = {}; // [node1.id-node2.id-edge.label]: count\n    cg1.edges.forEach(edge => {\n      const key = `${edge.source}-${edge.target}-${edge.label}`;\n      if (!cg1EdgeMap[key]) cg1EdgeMap[key] = 1;\n      else cg1EdgeMap[key]++;\n    });\n\n    for (let j = currentLength - 1; j > i; j--) {\n      const cg2 = candidateGraphs[j];\n      const cg2EdgeMap = {}; // [node1.id-node2.id-edge.label]: count\n      cg2.edges.forEach(edge => {\n        const key = `${edge.source}-${edge.target}-${edge.label}`;\n        if (!cg2EdgeMap[key]) cg2EdgeMap[key] = 1;\n        else cg2EdgeMap[key]++;\n      });\n\n      let same = true;\n      if (Object.keys(cg2EdgeMap).length !== Object.keys(cg1EdgeMap).length) {\n        same = false;\n      } else {\n        Object.keys(cg1EdgeMap).forEach(key => {\n          if (cg2EdgeMap[key] !== cg1EdgeMap[key]) same = false;\n        });\n      }\n      if (same) {\n        candidateGraphs.splice(j, 1);\n      }\n    }\n    currentLength = candidateGraphs.length;\n  }\n\n  return candidateGraphs;\n};\n\nexport default GADDI;\n", "export const ALGORITHM = {\n  pageRank: 'pageRank',\n  breadthFirstSearch: 'breadthFirstSearch',\n  connectedComponent: 'connectedComponent',\n  depthFirstSearch: 'depthFirstSearch',\n  detectCycle: 'detectCycle',\n  detectDirectedCycle: 'detectDirectedCycle',\n  detectAllCycles: 'detectAllCycles',\n  detectAllDirectedCycle: 'detectAllDirectedCycle',\n  detectAllUndirectedCycle: 'detectAllUndirectedCycle',\n  dijkstra: 'dijkstra',\n  findAllPath: 'findAllPath',\n  findShortestPath: 'findShortestPath',\n  floydWarshall: 'floydWarshall',\n  getAdjMatrix: 'getAdjMatrix',\n  getDegree: 'getDegree',\n  getInDegree: 'getInDegree',\n  getNeighbors: 'getNeighbors',\n  getOutDegree: 'getOutDegree',\n  labelPropagation: 'labelPropagation',\n  louvain: 'louvain',\n  GADDI: 'GADDI',\n  minimumSpanningTree: 'minimumSpanningTree',\n  SUCCESS: 'SUCCESS',\n  FAILURE: 'FAILURE',\n};\n\nexport const MESSAGE = {\n  SUCCESS: 'SUCCESS',\n  FAILURE: 'FAILURE',\n};\n", "import * as algorithm from './algorithm';\nimport { MESSAGE } from './constant';\n\nconst ctx: Worker = (typeof self !== 'undefined') ? self : {} as any;\n\ninterface Event {\n  type: string;\n  data: any;\n}\n\nctx.onmessage = (event: Event) => {\n  const { _algorithmType, data } = event.data;\n  // 如果发送内容没有私有类型。说明不是自己发的。不管\n  // fix: https://github.com/antvis/algorithm/issues/25\n  if(!_algorithmType){\n    return;\n  }\n  if (typeof algorithm[_algorithmType] === 'function') {\n    const result = algorithm[_algorithmType](...data);\n    ctx.postMessage({ _algorithmType: MESSAGE.SUCCESS, data: result });\n    return;\n  }\n  ctx.postMessage({ _algorithmType: MESSAGE.FAILURE });\n};\n\n// https://stackoverflow.com/questions/50210416/webpack-worker-loader-fails-to-compile-typescript-worker\nexport default null as any;\n"], "names": ["__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "graphData", "directed", "nodes", "edges", "matrix", "nodeMap", "Error", "for<PERSON>ach", "node", "i", "id", "push", "edge", "source", "target", "sIndex", "tIndex", "defaultComparator", "a", "b", "next", "this", "toString", "callback", "comparator", "head", "tail", "compare", "prepend", "newNode", "LinkedListNode", "append", "delete", "deleteNode", "currentNode", "find", "undefined", "deleteTail", "deletedTail", "deleteHead", "deletedHead", "fromArray", "values", "toArray", "reverse", "prevNode", "nextNode", "map", "linkedList", "isEmpty", "peek", "enqueue", "dequeue", "removeHead", "nodeId", "type", "currentEdges", "filter", "getEdgesByNodeId", "uniqueId", "index", "random1", "Math", "random", "split", "substr", "random2", "startNodeId", "originalCallbacks", "callbacks", "seen", "initiatedCallback", "stub<PERSON><PERSON><PERSON>", "allowTraversalCallback", "allowTraversal", "enter", "leave", "initCallbacks", "nodeQueue", "previousNode", "current", "previous", "detectConnectedComponents", "allComponents", "visited", "nodeStack", "getComponent", "neighbors", "neighbor", "targetNode", "length", "component", "pop", "inStack", "indices", "lowLink", "n", "indexOf", "targetNodeID", "min", "tmpNode", "degree", "degrees", "inDegree", "outDegree", "getInDegree", "getOutDegree", "depthFirstSearchRecursive", "depthFirstSearch", "cycle", "dfsParentMap", "unvisitedSet", "visitingSet", "visitedSet", "currentCycleNode", "previousCycleNode", "keys", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "ar", "l", "Array", "slice", "concat", "create", "SuppressedError", "isType", "isArray", "arrPrototype", "uniq", "arr", "cache", "Map", "r", "len", "item", "has", "set", "splice", "Number", "isInteger", "PI", "clone", "rst", "k", "ctx", "f", "resolver", "isFunction", "TypeError", "text", "font", "str", "fontSize", "fontFamily", "fontWeight", "fontStyle", "fontVariant", "document", "createElement", "getContext", "join", "measureText", "width", "default_1", "def", "v", "clear", "size", "weightPropertyName", "nodeIds", "marks", "D", "prevs", "Infinity", "nodeNum", "minNode", "minDis", "minVertex", "minNodeId", "relatedEdges", "getOutEdgesNodeId", "edgeTarget", "edgeSource", "w", "weight", "paths", "find<PERSON>llP<PERSON>s", "path", "allPath", "foundP<PERSON>s", "prevPaths", "prePath", "findShortestPath", "start", "end", "find<PERSON>ll<PERSON>ath", "isVisited", "stack", "children", "child", "shift", "adjacentMatrix", "dist", "j", "maxIteration", "clusters", "cid", "clusterId", "idx", "adjMatrix", "ks", "row", "iid", "entry", "jid", "iter", "changed", "neighborClusters", "neighborId", "neighborWeight", "neighborClusterId", "maxWeight", "bestClusterIds", "selfClusterIdx", "selfCluster", "nodeInSelfClusterIdx", "randomIdx", "floor", "bestCluster", "cluster", "clusterEdges", "clusterEdgeMap", "sourceClusterId", "targetClusterId", "newEdgeId", "count", "newEdge", "clustersArray", "getArr", "add", "otherVector", "otherArr", "Vector", "res", "subtract", "avg", "negate", "squareEuclideanDistance", "pow", "euclideanDistance", "sqrt", "console", "error", "normalize", "cloneArr", "sort", "max", "norm2", "dot", "equal", "DistanceType", "oneHot", "dataList", "<PERSON><PERSON><PERSON><PERSON>", "uninvolvedKeys", "allKeyValueMap", "data", "includes", "getAllKeyValueMap", "oneHotCode", "isAllNumber", "every", "code", "keyValue", "allKeyValue", "valueIndex", "findIndex", "subCode", "getModularity", "m", "param", "modularity", "clusteri", "getInertialModularity", "allPropertiesWeight", "totalProperties", "avgProperties", "variance", "propertiesi", "squareEuclideanDistanceInfo", "propertiesj", "inertialModularity", "clusterj", "clusterInertial", "toFixed", "threshold", "propertyKey", "inertialWeight", "properties", "originIndex", "Set", "nodeType", "allProperties", "getAllProperties", "String", "totalModularity", "previousModularity", "finalNodes", "finalClusters", "increaseWithinThreshold", "sumTot", "bestIncrease", "commonParam", "kiin", "selfClusterNodes", "scNode", "scNodeIdx", "removeModurarity", "selfClusterNodesAfterRemove", "propertiesWeightRemove", "nodeRemove", "removeInertialModularity", "nodeNeighborIds", "neighborNodeId", "neighborCluster", "clusterNodes", "neighborClusterKiin", "cNode", "cNodeIdx", "addModurarity", "clusterNodesAfterAdd", "propertiesWeightAdd", "nodeAdd", "addInertialModularity", "increase", "newClusterIdMap", "clusterIdx", "newId", "nodeInfo", "items", "parent", "union", "rootA", "rootB", "connected", "defaultCompare", "compareFn", "list", "getLeft", "getRight", "getParent", "top", "del<PERSON>in", "bottom", "moveDown", "insert", "moveUp", "tmp", "element", "left", "right", "primMST", "<PERSON><PERSON><PERSON>", "currNode", "edgeQueue", "currEdge", "kruskalMST", "weightEdges", "disjointSet", "curEdge", "algo", "prim", "kruskal", "epsilon", "linkProb", "currentRank", "distance", "leakedRank", "maxIterations", "nodesCount", "curRanks", "prevRanks", "nodeDegree", "abs", "VACANT_NODE_LABEL", "label", "edgeMap", "addEdge", "edgeIdAutoIncrease", "nodeLabelMap", "edgeLabelMap", "counter", "getNodeNum", "addNode", "Node", "Edge", "rEdge", "fromNode", "toNode", "fromNodeLabel", "edgeLabel", "toNodeLabel", "nodeEdgeNodeLabel", "nodeLabel1", "nodeLabel2", "equalTo", "other", "formNode", "notEqualTo", "rmpath", "dfsEdgeList", "a<PERSON><PERSON><PERSON>", "pushBack", "DFSedge", "toGraph", "graphId", "graph", "Graph", "dfsEdge", "fromNodeId", "toNodeId", "buildRmpath", "oldFrom", "fromNodeIdx", "toNodeIdx", "pdfs", "his", "nodesUsed", "edgesUsed", "e", "preNode", "hasNode", "hasEdge", "graphs", "minSupport", "minNodeNum", "maxNodeNum", "verbose", "dfsCode", "DFScode", "support", "frequentSize1Subgraphs", "frequentSubgraphs", "reportDF", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "findBackwardEdge", "edge1", "edge2", "history", "edge2ToEdges", "edge<PERSON><PERSON><PERSON>", "findForwardPureEdges", "rightmostEdge", "minNodeLabel", "rightmostEdgeToId", "find<PERSON>orwardRmpath<PERSON>dges", "newToNodeLabel", "getSupport", "projected", "graphMap", "pro", "findMinLabel", "minLabel", "isMin", "log", "dfsCodeMin", "root", "otherNode", "projectIsMin", "maxToC", "backwardRoot", "flag", "newTo", "p", "History", "backwardEdge", "minBackwardEdgeLabel", "forwardRoot", "newFrom", "forwardPureEdges", "<PERSON><PERSON><PERSON><PERSON>", "forwardRmpath<PERSON><PERSON>", "forwardMinEdgeNodeLabel", "report", "subGraphMining", "generate1EdgeFrequentSubGraphs", "nodeLabelCounter", "nodeEdgeNodeCounter", "nodeLableCounted", "nodeEdgeNodeLabelCounted", "nodeLabel", "graphNodeKey", "graph<PERSON>ey", "graphNodeEdgeNodeKey", "nodeEdgeNodeKey", "g", "run", "DEFAULT_LABEL_NAME", "findKNeighborUnits", "spm", "nodeLabelProp", "units", "findKNeighborUnit", "unitNodeIdxs", "labelCountMap", "dists", "nodeIdx", "nodeIdxs", "neighborNum", "nodeLabelCountMap", "getIntersectNeighborInducedGraph", "nodePairMap", "neighborUnits", "cachedInducedGraphMap", "pair", "startUnitNodeIds", "endUnitNodeIds", "endSet", "intersect", "x", "intersectIdMap", "intersectLength", "getMatchedCount", "structure", "edgeLabelProp", "sourceLabel", "targetLabel", "strNodeLabel1", "strNodeLabel2", "strEdge<PERSON>abel", "getNodeMaps", "getEdgeMaps", "sourceNode", "getSpmMap", "iId", "jId", "getNDSDist", "node1", "node2", "spDist", "kNeighborUnits", "cachedNDSMap", "cachedInterInducedGraph", "interInducedGraph", "pairMap", "stashPatternNodeLabelDegreeMap", "minPatternNodeLabelDegreeMap", "neighborLabel", "patternNodeMap", "patternNodeLabelMap", "minPatternNodeLabelDegree", "minPatternNodeLabelInDegree", "minPatternNodeLabelOutDegree", "patternNodeWithLabel", "patternNodeDegree", "patternNodeInDegree", "patternNodeOutDegree", "pattern", "patternSpm", "spmMap", "patternSpmMap", "patternEdgeLabelMap", "patternSpmSpread", "patternKNeighborUnits", "nodePairsMap", "maxNodePairNum", "nodePairNumEachNode", "ceil", "foundNodePairCount", "unit", "nodePairForICount", "outerLoopCount", "oidx", "innerLoopCount", "findNodePairsRandomly", "intGMap", "freStructures", "params", "formattedGraphs", "fGraph", "nodeIdxMap", "sourceIdx", "targetIdx", "formatGraphs", "calculator", "GSpan", "toGraphDatas", "structureNum", "matchedCountMap", "subStructureCount", "structures", "maxOffset", "representClusterType", "countMapI", "sortedGraphKeys", "totalCount", "aveCount", "aveIntraDist", "aveCounts", "graphsInCluster", "aveIntraPerCluster", "graphsNum", "graphKey1", "graph1Count", "graphKey2", "aveInterDist", "aveCount1", "aveCount2", "offset", "structureCountMap", "findRepresentStructure", "dsG", "ndsDist", "beginPNode", "candidates", "maxNodeNumWithSameLabel", "p<PERSON><PERSON><PERSON>", "nodesWithSameLabel", "patternIntGraphMap", "patternNDSDist", "patternNDSDistMap", "patternSpDist", "patternSpDistBack", "label2", "maxDist", "patternNodesWithLabel2", "patternNodePairMap", "nodeWithLabel2", "distBack", "currentPatternNDSDistArray", "patternIntGraph", "graphNeighborUnit", "graphNeighborUnitCountMap", "patternLabel2Num", "prune2Invalid", "cNodePairMap", "neighborNode", "currentNDSDistArray", "intGraph", "prune3Invalid", "candidateGraphs", "candidate", "neighborNodes", "unmatched", "distToCandidate", "keyBack", "distFromCandidate", "ndsToCandidate", "pattern<PERSON>ey", "undirectedLengthsToBeginPNode", "undirectedLengthsToBeginPNodeLabelMap", "<PERSON><PERSON><PERSON><PERSON>", "candidateNodeLabelCountMap", "candidateNodeMap", "q", "cNodeLabel", "candidate<PERSON><PERSON>", "edgeLabelCountMap", "pattenr<PERSON>dge<PERSON>abel<PERSON>um", "pruned<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "candidateGraphInvalid", "patternEdgesWithLabel", "edgeMatched", "patternEdge", "patternSource", "patternTarget", "lengthsToCandidate", "targetId", "targetNodeLabel", "nLabel", "degreeChanged", "loopCount", "cgNode", "nodeInDegree", "nodeOutDegree", "y", "cedge", "<PERSON><PERSON><PERSON><PERSON>", "cg1", "cg1EdgeMap", "cg2", "cg2EdgeMap", "same", "self", "onmessage", "event", "_algorithmType", "postMessage"], "sourceRoot": ""}