import type { QueryPool, QueryPoolType } from '../api';
import { ResourceType } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export declare class QueryPool_GL extends ResourceBase_GL implements QueryPool {
    type: ResourceType.QueryPool;
    gl_query_type: number;
    gl_query: WebGLQuery[];
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: {
            elemCount: number;
            type: QueryPoolType;
        };
    });
    queryResultOcclusion(dstOffs: number): boolean | null;
    destroy(): void;
}
