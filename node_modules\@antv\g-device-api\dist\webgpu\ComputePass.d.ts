/// <reference types="@webgpu/types" />
import type { <PERSON><PERSON><PERSON>, Binding<PERSON>, ComputePass, ComputePipeline } from '../api';
export declare class ComputePass_WebGPU implements ComputePass {
    frameCommandEncoder: GPUCommandEncoder | null;
    private gpuComputePassDescriptor;
    private gpuComputePassEncoder;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpucomputepassencoder-dispatchworkgroups
     */
    dispatchWorkgroups(workgroupCountX: number, workgroupCountY?: number, workgroupCountZ?: number): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpucomputepassencoder-dispatchworkgroupsindirect
     */
    dispatchWorkgroupsIndirect(indirectBuffer: Buffer, indirectOffset: number): void;
    finish(): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpucommandencoder-begincomputepass
     */
    beginComputePass(commandEncoder: GPUCommandEncoder): void;
    setPipeline(pipeline_: ComputePipeline): void;
    setBindings(bindings_: Bindings): void;
    pushDebugGroup(name: string): void;
    popDebugGroup(): void;
    insertDebugMarker(markerLabel: string): void;
}
