import { IElement } from '@antv/g-base';
/**
 * 将边和填充设置的颜色转换成线性渐变对象
 * @param {CanvasRenderingContext2D} context canvas 上下文
 * @param {IElement}                 element  图形元素
 * @param {string}                   gradientStr   颜色
 * @returns {any} 渐变对象
 */
export declare function parseLineGradient(context: CanvasRenderingContext2D, element: IElement, gradientStr: string): CanvasGradient;
/**
 * 将边和填充设置的颜色转换成圆形渐变对象
 * @param {CanvasRenderingContext2D} context canvas 上下文
 * @param {IElement}                 element  图形元素
 * @param {string}                   gradientStr   颜色
 * @returns {any} 渐变对象
 */
export declare function parseRadialGradient(context: CanvasRenderingContext2D, element: IElement, gradientStr: string): string | CanvasGradient;
/**
 * 边和填充设置的颜色转换成 pattern
 * @param {CanvasRenderingContext2D} context canvas 上下文
 * @param {IElement}                 element  图形元素
 * @param {string}                   patternStr   生成 pattern 的字符串
 */
export declare function parsePattern(context: CanvasRenderingContext2D, element: IElement, patternStr: string): any;
export declare function parseStyle(context: CanvasRenderingContext2D, element: IElement, color: string | CanvasPattern): any;
export declare function parseRadius(radius: any): number[];
