{"ast": null, "code": "import \"core-js/modules/es.array.fill.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { extend } from 'zrender/lib/core/util.js';\nfunction normalize(a) {\n  if (!(a instanceof Array)) {\n    a = [a, a];\n  }\n  return a;\n}\nexport default function graphEdgeVisual(ecModel) {\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var graph = seriesModel.getGraph();\n    var edgeData = seriesModel.getEdgeData();\n    var symbolType = normalize(seriesModel.get('edgeSymbol'));\n    var symbolSize = normalize(seriesModel.get('edgeSymbolSize'));\n    // const colorQuery = ['lineStyle', 'color'] as const;\n    // const opacityQuery = ['lineStyle', 'opacity'] as const;\n    edgeData.setVisual('fromSymbol', symbolType && symbolType[0]);\n    edgeData.setVisual('toSymbol', symbolType && symbolType[1]);\n    edgeData.setVisual('fromSymbolSize', symbolSize && symbolSize[0]);\n    edgeData.setVisual('toSymbolSize', symbolSize && symbolSize[1]);\n    edgeData.setVisual('style', seriesModel.getModel('lineStyle').getLineStyle());\n    edgeData.each(function (idx) {\n      var itemModel = edgeData.getItemModel(idx);\n      var edge = graph.getEdgeByIndex(idx);\n      var symbolType = normalize(itemModel.getShallow('symbol', true));\n      var symbolSize = normalize(itemModel.getShallow('symbolSize', true));\n      // Edge visual must after node visual\n      var style = itemModel.getModel('lineStyle').getLineStyle();\n      var existsStyle = edgeData.ensureUniqueItemVisual(idx, 'style');\n      extend(existsStyle, style);\n      switch (existsStyle.stroke) {\n        case 'source':\n          {\n            var nodeStyle = edge.node1.getVisual('style');\n            existsStyle.stroke = nodeStyle && nodeStyle.fill;\n            break;\n          }\n        case 'target':\n          {\n            var nodeStyle = edge.node2.getVisual('style');\n            existsStyle.stroke = nodeStyle && nodeStyle.fill;\n            break;\n          }\n      }\n      symbolType[0] && edge.setVisual('fromSymbol', symbolType[0]);\n      symbolType[1] && edge.setVisual('toSymbol', symbolType[1]);\n      symbolSize[0] && edge.setVisual('fromSymbolSize', symbolSize[0]);\n      symbolSize[1] && edge.setVisual('toSymbolSize', symbolSize[1]);\n    });\n  });\n}", "map": {"version": 3, "names": ["extend", "normalize", "a", "Array", "graphEdgeVisual", "ecModel", "eachSeriesByType", "seriesModel", "graph", "getGraph", "edgeData", "getEdgeData", "symbolType", "get", "symbolSize", "setVisual", "getModel", "getLineStyle", "each", "idx", "itemModel", "getItemModel", "edge", "getEdgeByIndex", "getShallow", "style", "existsStyle", "ensureUniqueItemVisual", "stroke", "nodeStyle", "node1", "getVisual", "fill", "node2"], "sources": ["G:/备份9/adminweb/node_modules/echarts/lib/chart/graph/edgeVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { extend } from 'zrender/lib/core/util.js';\nfunction normalize(a) {\n  if (!(a instanceof Array)) {\n    a = [a, a];\n  }\n  return a;\n}\nexport default function graphEdgeVisual(ecModel) {\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var graph = seriesModel.getGraph();\n    var edgeData = seriesModel.getEdgeData();\n    var symbolType = normalize(seriesModel.get('edgeSymbol'));\n    var symbolSize = normalize(seriesModel.get('edgeSymbolSize'));\n    // const colorQuery = ['lineStyle', 'color'] as const;\n    // const opacityQuery = ['lineStyle', 'opacity'] as const;\n    edgeData.setVisual('fromSymbol', symbolType && symbolType[0]);\n    edgeData.setVisual('toSymbol', symbolType && symbolType[1]);\n    edgeData.setVisual('fromSymbolSize', symbolSize && symbolSize[0]);\n    edgeData.setVisual('toSymbolSize', symbolSize && symbolSize[1]);\n    edgeData.setVisual('style', seriesModel.getModel('lineStyle').getLineStyle());\n    edgeData.each(function (idx) {\n      var itemModel = edgeData.getItemModel(idx);\n      var edge = graph.getEdgeByIndex(idx);\n      var symbolType = normalize(itemModel.getShallow('symbol', true));\n      var symbolSize = normalize(itemModel.getShallow('symbolSize', true));\n      // Edge visual must after node visual\n      var style = itemModel.getModel('lineStyle').getLineStyle();\n      var existsStyle = edgeData.ensureUniqueItemVisual(idx, 'style');\n      extend(existsStyle, style);\n      switch (existsStyle.stroke) {\n        case 'source':\n          {\n            var nodeStyle = edge.node1.getVisual('style');\n            existsStyle.stroke = nodeStyle && nodeStyle.fill;\n            break;\n          }\n        case 'target':\n          {\n            var nodeStyle = edge.node2.getVisual('style');\n            existsStyle.stroke = nodeStyle && nodeStyle.fill;\n            break;\n          }\n      }\n      symbolType[0] && edge.setVisual('fromSymbol', symbolType[0]);\n      symbolType[1] && edge.setVisual('toSymbol', symbolType[1]);\n      symbolSize[0] && edge.setVisual('fromSymbolSize', symbolSize[0]);\n      symbolSize[1] && edge.setVisual('toSymbolSize', symbolSize[1]);\n    });\n  });\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,0BAA0B;AACjD,SAASC,SAASA,CAACC,CAAC,EAAE;EACpB,IAAI,EAAEA,CAAC,YAAYC,KAAK,CAAC,EAAE;IACzBD,CAAC,GAAG,CAACA,CAAC,EAAEA,CAAC,CAAC;EACZ;EACA,OAAOA,CAAC;AACV;AACA,eAAe,SAASE,eAAeA,CAACC,OAAO,EAAE;EAC/CA,OAAO,CAACC,gBAAgB,CAAC,OAAO,EAAE,UAAUC,WAAW,EAAE;IACvD,IAAIC,KAAK,GAAGD,WAAW,CAACE,QAAQ,CAAC,CAAC;IAClC,IAAIC,QAAQ,GAAGH,WAAW,CAACI,WAAW,CAAC,CAAC;IACxC,IAAIC,UAAU,GAAGX,SAAS,CAACM,WAAW,CAACM,GAAG,CAAC,YAAY,CAAC,CAAC;IACzD,IAAIC,UAAU,GAAGb,SAAS,CAACM,WAAW,CAACM,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC7D;IACA;IACAH,QAAQ,CAACK,SAAS,CAAC,YAAY,EAAEH,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7DF,QAAQ,CAACK,SAAS,CAAC,UAAU,EAAEH,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3DF,QAAQ,CAACK,SAAS,CAAC,gBAAgB,EAAED,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IACjEJ,QAAQ,CAACK,SAAS,CAAC,cAAc,EAAED,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/DJ,QAAQ,CAACK,SAAS,CAAC,OAAO,EAAER,WAAW,CAACS,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAC7EP,QAAQ,CAACQ,IAAI,CAAC,UAAUC,GAAG,EAAE;MAC3B,IAAIC,SAAS,GAAGV,QAAQ,CAACW,YAAY,CAACF,GAAG,CAAC;MAC1C,IAAIG,IAAI,GAAGd,KAAK,CAACe,cAAc,CAACJ,GAAG,CAAC;MACpC,IAAIP,UAAU,GAAGX,SAAS,CAACmB,SAAS,CAACI,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;MAChE,IAAIV,UAAU,GAAGb,SAAS,CAACmB,SAAS,CAACI,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;MACpE;MACA,IAAIC,KAAK,GAAGL,SAAS,CAACJ,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MAC1D,IAAIS,WAAW,GAAGhB,QAAQ,CAACiB,sBAAsB,CAACR,GAAG,EAAE,OAAO,CAAC;MAC/DnB,MAAM,CAAC0B,WAAW,EAAED,KAAK,CAAC;MAC1B,QAAQC,WAAW,CAACE,MAAM;QACxB,KAAK,QAAQ;UACX;YACE,IAAIC,SAAS,GAAGP,IAAI,CAACQ,KAAK,CAACC,SAAS,CAAC,OAAO,CAAC;YAC7CL,WAAW,CAACE,MAAM,GAAGC,SAAS,IAAIA,SAAS,CAACG,IAAI;YAChD;UACF;QACF,KAAK,QAAQ;UACX;YACE,IAAIH,SAAS,GAAGP,IAAI,CAACW,KAAK,CAACF,SAAS,CAAC,OAAO,CAAC;YAC7CL,WAAW,CAACE,MAAM,GAAGC,SAAS,IAAIA,SAAS,CAACG,IAAI;YAChD;UACF;MACJ;MACApB,UAAU,CAAC,CAAC,CAAC,IAAIU,IAAI,CAACP,SAAS,CAAC,YAAY,EAAEH,UAAU,CAAC,CAAC,CAAC,CAAC;MAC5DA,UAAU,CAAC,CAAC,CAAC,IAAIU,IAAI,CAACP,SAAS,CAAC,UAAU,EAAEH,UAAU,CAAC,CAAC,CAAC,CAAC;MAC1DE,UAAU,CAAC,CAAC,CAAC,IAAIQ,IAAI,CAACP,SAAS,CAAC,gBAAgB,EAAED,UAAU,CAAC,CAAC,CAAC,CAAC;MAChEA,UAAU,CAAC,CAAC,CAAC,IAAIQ,IAAI,CAACP,SAAS,CAAC,cAAc,EAAED,UAAU,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}