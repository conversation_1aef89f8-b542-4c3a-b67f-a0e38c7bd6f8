import type { B<PERSON><PERSON>, Binding<PERSON>, ComputePass, ComputePipeline } from '../api';
export declare class ComputePass_GL implements ComputePass {
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpucomputepassencoder-dispatch
     */
    dispatchWorkgroups(workgroupCountX: number, workgroupCountY?: number, workgroupCountZ?: number): void;
    dispatchWorkgroupsIndirect(indirectBuffer: Buffer, indirectOffset: number): void;
    setPipeline(pipeline_: ComputePipeline): void;
    setBindings(bindings_: Bindings): void;
    pushDebugGroup(name: string): void;
    popDebugGroup(): void;
    insertDebugMarker(markerLabel: string): void;
}
