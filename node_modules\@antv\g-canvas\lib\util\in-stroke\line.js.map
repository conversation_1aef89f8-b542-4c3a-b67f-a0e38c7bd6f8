{"version": 3, "file": "line.js", "sourceRoot": "", "sources": ["../../../src/util/in-stroke/line.ts"], "names": [], "mappings": ";;AAAA,uCAAgD;AAEhD,SAAwB,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;IAC5D,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAM,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;IAChC,6CAA6C;IAC7C,0CAA0C;IAC1C,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,GAAG,SAAS,CAAC,EAAE;QACvG,OAAO,KAAK,CAAC;KACd;IACD,sCAAsC;IACtC,OAAO,aAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,SAAS,GAAG,CAAC,CAAC;AACrE,CAAC;AAbD,yBAaC"}