{"version": 3, "file": "arc-params.js", "sourceRoot": "", "sources": ["../../src/util/arc-params.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,QAAQ,CAAC;AAEpD,OAAO;AACP,SAAS,IAAI,CAAC,CAAC;IACb,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,sBAAsB;AACtB,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC;IAClB,oCAAoC;IACpC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,CAAC;AAED,OAAO;AACP,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC;IAClB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC;AAED,wEAAwE;AACxE,MAAM,CAAC,OAAO,UAAU,YAAY,CAAC,UAAU,EAAE,MAAM;IACrD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,IAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACxD,IAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5B,SAAS;IACT,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACzB,IAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACzB,SAAS;IACT,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACrB,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACrB,IAAM,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;IAC7F,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;IAClG,IAAM,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAE7D,IAAI,MAAM,GAAG,CAAC,EAAE;QACd,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;IACD,IAAM,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAEvD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAElE,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,CAAC,IAAI,CAAC,CAAC,CAAC;KACT;IACD,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,GAAG,CAAC,CAAC;KACP;IAED,2CAA2C;IAC3C,IAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,IAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAEzC,SAAS;IACT,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;IACnF,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;IAEnF,WAAW;IACX,IAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IAC7C,WAAW;IACX,IAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IACvD,0BAA0B;IAC1B,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEhC,wBAAwB;IACxB,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE1B,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;QACtB,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;KAClB;IACD,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,GAAG,CAAC,CAAC;KACZ;IACD,IAAI,SAAS,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;QACjC,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;KAC/B;IACD,IAAI,SAAS,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;QACjC,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;KAC/B;IACD,OAAO;QACL,EAAE,IAAA;QACF,EAAE,IAAA;QACF,6BAA6B;QAC7B,EAAE,EAAE,WAAW,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9C,EAAE,EAAE,WAAW,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9C,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,KAAK,GAAG,MAAM;QACxB,SAAS,WAAA;QACT,OAAO,SAAA;QACP,SAAS,WAAA;KACV,CAAC;AACJ,CAAC"}