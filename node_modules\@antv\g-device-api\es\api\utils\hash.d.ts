import type { BindingsDescriptor, Buffer<PERSON>inding, InputLayoutBufferDescriptor, InputLayoutDescriptor, RenderPipelineDescriptor, SamplerBinding, SamplerDescriptor, StencilFaceState, TextureBinding, VertexAttributeDescriptor } from '../interfaces';
type EqualFunc<K> = (a: K, b: K) => boolean;
export declare function arrayEqual<T>(a: T[], b: T[], e: EqualFunc<T>): boolean;
type CopyFunc<T> = (a: T) => T;
export declare function arrayCopy<T>(a: T[], copyFunc: CopyFunc<T>): T[];
export declare function bindingsDescriptorEquals(a: BindingsDescriptor, b: BindingsDescriptor): boolean;
export declare function stencilFaceStateEquals(a: Readonly<Partial<StencilFaceState>>, b: Readonly<Partial<StencilFaceState>>): boolean;
export declare function renderPipelineDescriptorEquals(a: Readonly<RenderPipelineDescriptor>, b: Readonly<RenderPipelineDescriptor>): boolean;
export declare function vertexAttributeDescriptorEquals(a: Readonly<VertexAttributeDescriptor>, b: Readonly<VertexAttributeDescriptor>): boolean;
export declare function inputLayoutBufferDescriptorEquals(a: Readonly<InputLayoutBufferDescriptor | null>, b: Readonly<InputLayoutBufferDescriptor | null>): boolean;
export declare function inputLayoutDescriptorEquals(a: Readonly<InputLayoutDescriptor>, b: Readonly<InputLayoutDescriptor>): boolean;
export declare function samplerDescriptorEquals(a: Readonly<SamplerDescriptor>, b: Readonly<SamplerDescriptor>): boolean;
export declare function samplerBindingCopy(a: Readonly<SamplerBinding>): SamplerBinding;
export declare function bufferBindingCopy(a: Readonly<BufferBinding>): BufferBinding;
export declare function textureBindingCopy(a: Readonly<TextureBinding>): TextureBinding;
export declare function bindingsDescriptorCopy(a: Readonly<BindingsDescriptor>): BindingsDescriptor;
export declare function renderPipelineDescriptorCopy(a: Readonly<RenderPipelineDescriptor>): RenderPipelineDescriptor;
export declare function vertexAttributeDescriptorCopy(a: Readonly<VertexAttributeDescriptor>): VertexAttributeDescriptor;
export declare function inputLayoutBufferDescriptorCopy(a: Readonly<InputLayoutBufferDescriptor | null>): InputLayoutBufferDescriptor | null;
export declare function inputLayoutDescriptorCopy(a: Readonly<InputLayoutDescriptor>): InputLayoutDescriptor;
export {};
