{"ast": null, "code": "import _objectSpread from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport request from '@/utils/request';\n\n// 获取用户列表\nexport function getUserList(params) {\n  // 转换日期范围\n  var query = _objectSpread({}, params);\n  if (query.dateRange && query.dateRange.length === 2) {\n    query.startDate = query.dateRange[0];\n    query.endDate = query.dateRange[1];\n  }\n  delete query.dateRange;\n  return request({\n    url: '/user/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 获取用户详情\nexport function getUserDetail(id) {\n  return request({\n    url: \"/user/\".concat(id),\n    method: 'get'\n  });\n}\n\n// 更新用户状态\nexport function updateUserStatus(id, status) {\n  return request({\n    url: \"/user/\".concat(id, \"/status/\").concat(status),\n    method: 'put'\n  });\n}\n\n// 重置用户密码\nexport function resetUserPassword(id) {\n  return request({\n    url: \"/user/\".concat(id, \"/reset\"),\n    method: 'put'\n  });\n}\n\n// 用户充值\nexport function rechargeUser(id, data) {\n  return request({\n    url: \"/user/\".concat(id, \"/recharge\"),\n    method: 'post',\n    data: data\n  });\n}\n\n// 获取用户银行卡列表\nexport function getUserBankCards(userId) {\n  console.log('请求银行卡列表:', userId); // 添加日志\n  return request({\n    url: \"/user/\".concat(userId, \"/bank-cards\"),\n    method: 'get'\n  });\n}", "map": {"version": 3, "names": ["request", "getUserList", "params", "query", "_objectSpread", "date<PERSON><PERSON><PERSON>", "length", "startDate", "endDate", "url", "method", "getUserDetail", "id", "concat", "updateUserStatus", "status", "resetUserPassword", "rechargeUser", "data", "getUserBankCards", "userId", "console", "log"], "sources": ["E:/新项目/整理6/adminweb/src/api/user/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取用户列表\r\nexport function getUserList(params) {\r\n  // 转换日期范围\r\n  const query = { ...params }\r\n  if (query.dateRange && query.dateRange.length === 2) {\r\n    query.startDate = query.dateRange[0]\r\n    query.endDate = query.dateRange[1]\r\n  }\r\n  delete query.dateRange\r\n\r\n  return request({\r\n    url: '/user/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 获取用户详情\r\nexport function getUserDetail(id) {\r\n  return request({\r\n    url: `/user/${id}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新用户状态\r\nexport function updateUserStatus(id, status) {\r\n  return request({\r\n    url: `/user/${id}/status/${status}`,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 重置用户密码\r\nexport function resetUserPassword(id) {\r\n  return request({\r\n    url: `/user/${id}/reset`,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 用户充值\r\nexport function rechargeUser(id, data) {\r\n  return request({\r\n    url: `/user/${id}/recharge`,\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取用户银行卡列表\r\nexport function getUserBankCards(userId) {\r\n  console.log('请求银行卡列表:', userId)  // 添加日志\r\n  return request({\r\n    url: `/user/${userId}/bank-cards`,\r\n    method: 'get'\r\n  })\r\n} "], "mappings": ";;AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC;EACA,IAAMC,KAAK,GAAAC,aAAA,KAAQF,MAAM,CAAE;EAC3B,IAAIC,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IACnDH,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC;IACpCF,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC;EACpC;EACA,OAAOF,KAAK,CAACE,SAAS;EAEtB,OAAOL,OAAO,CAAC;IACbS,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAEC;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAOZ,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,CAAE;IAClBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,gBAAgBA,CAACF,EAAE,EAAEG,MAAM,EAAE;EAC3C,OAAOf,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,cAAAC,MAAA,CAAWE,MAAM,CAAE;IACnCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,iBAAiBA,CAACJ,EAAE,EAAE;EACpC,OAAOZ,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,WAAQ;IACxBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,YAAYA,CAACL,EAAE,EAAEM,IAAI,EAAE;EACrC,OAAOlB,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,cAAW;IAC3BF,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvCC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,MAAM,CAAC,EAAE;EACjC,OAAOpB,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWO,MAAM,gBAAa;IACjCV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}