/// <reference types="@webgpu/types" />
import type { ComputePipeline, ComputePipelineDescriptor } from '../api';
import { ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class ComputePipeline_WebGPU extends ResourceBase_WebGPU implements ComputePipeline {
    type: ResourceType.ComputePipeline;
    descriptor: ComputePipelineDescriptor;
    gpuComputePipeline: GPUComputePipeline | null;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: ComputePipelineDescriptor;
    });
    getBindGroupLayout(index: number): GPUBindGroupLayout;
}
