import { DeviceContribution } from '../api';
import { Device_WebGPU } from './Device';
export interface WebGPUDeviceOptions {
    shaderCompilerPath: string;
    xrCompatible: boolean;
    onContextLost: () => void;
}
export declare class WebGPUDeviceContribution implements DeviceContribution {
    private pluginOptions;
    constructor(pluginOptions: Partial<WebGPUDeviceOptions>);
    createSwapChain($canvas: HTMLCanvasElement): Promise<Device_WebGPU>;
}
