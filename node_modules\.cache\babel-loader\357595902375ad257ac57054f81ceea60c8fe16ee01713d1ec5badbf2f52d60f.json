{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport RadarModel from '../../coord/radar/RadarModel.js';\nimport RadarView from './RadarView.js';\nimport Radar from '../../coord/radar/Radar.js';\nexport function install(registers) {\n  registers.registerCoordinateSystem('radar', Radar);\n  registers.registerComponentModel(RadarModel);\n  registers.registerComponentView(RadarView);\n  registers.registerVisual({\n    seriesType: 'radar',\n    reset: function reset(seriesModel) {\n      var data = seriesModel.getData();\n      // itemVisual symbol is for selected data\n      data.each(function (idx) {\n        data.setItemVisual(idx, 'legendIcon', 'roundRect');\n      });\n      // visual is for unselected data\n      data.setVisual('legendIcon', 'roundRect');\n    }\n  });\n}", "map": {"version": 3, "names": ["RadarModel", "RadarView", "Radar", "install", "registers", "registerCoordinateSystem", "registerComponentModel", "registerComponentView", "registerVisual", "seriesType", "reset", "seriesModel", "data", "getData", "each", "idx", "setItemVisual", "setVisual"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/echarts/lib/component/radar/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport RadarModel from '../../coord/radar/RadarModel.js';\nimport RadarView from './RadarView.js';\nimport Radar from '../../coord/radar/Radar.js';\nexport function install(registers) {\n  registers.registerCoordinateSystem('radar', Radar);\n  registers.registerComponentModel(RadarModel);\n  registers.registerComponentView(RadarView);\n  registers.registerVisual({\n    seriesType: 'radar',\n    reset: function (seriesModel) {\n      var data = seriesModel.getData();\n      // itemVisual symbol is for selected data\n      data.each(function (idx) {\n        data.setItemVisual(idx, 'legendIcon', 'roundRect');\n      });\n      // visual is for unselected data\n      data.setVisual('legendIcon', 'roundRect');\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,UAAU,MAAM,iCAAiC;AACxD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,wBAAwB,CAAC,OAAO,EAAEH,KAAK,CAAC;EAClDE,SAAS,CAACE,sBAAsB,CAACN,UAAU,CAAC;EAC5CI,SAAS,CAACG,qBAAqB,CAACN,SAAS,CAAC;EAC1CG,SAAS,CAACI,cAAc,CAAC;IACvBC,UAAU,EAAE,OAAO;IACnBC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,WAAW,EAAE;MAC5B,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;MAChC;MACAD,IAAI,CAACE,IAAI,CAAC,UAAUC,GAAG,EAAE;QACvBH,IAAI,CAACI,aAAa,CAACD,GAAG,EAAE,YAAY,EAAE,WAAW,CAAC;MACpD,CAAC,CAAC;MACF;MACAH,IAAI,CAACK,SAAS,CAAC,YAAY,EAAE,WAAW,CAAC;IAC3C;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}