{"name": "@antv/event-emitter", "version": "0.1.3", "description": "event emitter for antvis.", "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "files": ["src", "lib", "esm", "README.md", "LICENSE"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "**************:antvis/event-emitter.git"}, "scripts": {"build": "run-s clean lib", "clean": "<PERSON><PERSON>f lib esm", "lib": "run-p lib:*", "lib:cjs": "tsc -p tsconfig.json --target ES5 --module commonjs --outDir lib", "lib:esm": "tsc -p tsconfig.json --target ES5 --module ESNext --outDir esm", "test": "jest"}, "devDependencies": {"@types/jest": "^24.0.18", "jest": "^24.9.0", "jest-electron": "^0.1.7", "npm-run-all": "^4.1.5", "prettier": "^2.0.2", "rimraf": "^2.6.2", "ts-jest": "^24.1.0", "typescript": "^3.6.4"}, "jest": {"preset": "ts-jest", "runner": "jest-electron/runner", "testEnvironment": "jest-electron/environment", "collectCoverage": true, "collectCoverageFrom": ["src/**/*.ts", "!**/node_modules/**"], "testRegex": "/__tests__/.*spec\\.ts$"}}