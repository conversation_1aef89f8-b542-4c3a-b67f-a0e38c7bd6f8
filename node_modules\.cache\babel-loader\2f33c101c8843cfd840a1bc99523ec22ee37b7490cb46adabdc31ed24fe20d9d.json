{"ast": null, "code": "import \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: 'CopyTrade',\n  data: function data() {\n    return {\n      // 当前激活的标签页\n      activeTab: 'leader',\n      // 带单管理相关\n      leaderLoading: false,\n      leaderList: [],\n      leaderTotal: 0,\n      leaderOpen: false,\n      leaderTitle: '',\n      leaderForm: {},\n      leaderDateRange: [],\n      leaderQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        leaderNickname: null,\n        status: null\n      },\n      leaderRules: {\n        leaderNickname: [{\n          required: true,\n          message: '带单人昵称不能为空',\n          trigger: 'blur'\n        }],\n        symbol: [{\n          required: true,\n          message: '交易对不能为空',\n          trigger: 'blur'\n        }],\n        periodNo: [{\n          required: true,\n          message: '期号不能为空',\n          trigger: 'blur'\n        }]\n      },\n      // 跟单管理相关\n      followLoading: false,\n      followList: [],\n      followTotal: 0,\n      followDateRange: [],\n      followQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        followerNickname: null,\n        status: null\n      },\n      // 跟单明细相关\n      historyLoading: false,\n      historyList: [],\n      historyTotal: 0,\n      historyDateRange: [],\n      historyQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        followerNickname: null,\n        resultStatus: null\n      },\n      // 选中数组\n      ids: [],\n      single: true,\n      multiple: true\n    };\n  },\n  created: function created() {\n    this.getLeaderList();\n  },\n  methods: {\n    // 标签页切换\n    handleTabClick: function handleTabClick(tab) {\n      if (tab.name === 'leader') {\n        this.getLeaderList();\n      } else if (tab.name === 'follow') {\n        this.getFollowList();\n      } else if (tab.name === 'history') {\n        this.getHistoryList();\n      }\n    },\n    // 带单管理相关方法\n    getLeaderList: function getLeaderList() {\n      this.leaderLoading = true;\n      // TODO: 调用API获取带单管理数据\n      this.leaderLoading = false;\n    },\n    handleLeaderQuery: function handleLeaderQuery() {\n      this.leaderQueryParams.pageNum = 1;\n      this.getLeaderList();\n    },\n    resetLeaderQuery: function resetLeaderQuery() {\n      this.leaderDateRange = [];\n      this.resetForm('leaderQueryForm');\n      this.handleLeaderQuery();\n    },\n    handleAddLeader: function handleAddLeader() {\n      this.resetLeaderForm();\n      this.leaderOpen = true;\n      this.leaderTitle = '添加带单管理';\n    },\n    handleUpdateLeader: function handleUpdateLeader(row) {\n      this.resetLeaderForm();\n      // TODO: 获取详情数据\n      this.leaderOpen = true;\n      this.leaderTitle = '修改带单管理';\n    },\n    handleViewLeader: function handleViewLeader(row) {\n      // TODO: 实现查看详情\n    },\n    handleDeleteLeader: function handleDeleteLeader(row) {\n      // TODO: 实现删除功能\n    },\n    handleLeaderSelectionChange: function handleLeaderSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    handleLeaderSizeChange: function handleLeaderSizeChange(val) {\n      this.leaderQueryParams.pageSize = val;\n      this.getLeaderList();\n    },\n    handleLeaderCurrentChange: function handleLeaderCurrentChange(val) {\n      this.leaderQueryParams.pageNum = val;\n      this.getLeaderList();\n    },\n    resetLeaderForm: function resetLeaderForm() {\n      this.leaderForm = {\n        id: null,\n        leaderNickname: null,\n        symbol: null,\n        periodNo: null,\n        marginBalance: 900000,\n        remark: null,\n        status: 0\n      };\n    },\n    submitLeaderForm: function submitLeaderForm() {\n      var _this = this;\n      this.$refs['leaderForm'].validate(function (valid) {\n        if (valid) {\n          // TODO: 提交表单\n          _this.leaderOpen = false;\n          _this.getLeaderList();\n        }\n      });\n    },\n    // 跟单管理相关方法\n    getFollowList: function getFollowList() {\n      this.followLoading = true;\n      // TODO: 调用API获取跟单管理数据\n      this.followLoading = false;\n    },\n    handleFollowQuery: function handleFollowQuery() {\n      this.followQueryParams.pageNum = 1;\n      this.getFollowList();\n    },\n    resetFollowQuery: function resetFollowQuery() {\n      this.followDateRange = [];\n      this.resetForm('followQueryForm');\n      this.handleFollowQuery();\n    },\n    handleViewFollow: function handleViewFollow(row) {\n      // TODO: 实现查看详情\n    },\n    handleFollowSizeChange: function handleFollowSizeChange(val) {\n      this.followQueryParams.pageSize = val;\n      this.getFollowList();\n    },\n    handleFollowCurrentChange: function handleFollowCurrentChange(val) {\n      this.followQueryParams.pageNum = val;\n      this.getFollowList();\n    },\n    // 跟单明细相关方法\n    getHistoryList: function getHistoryList() {\n      this.historyLoading = true;\n      // TODO: 调用API获取跟单明细数据\n      this.historyLoading = false;\n    },\n    handleHistoryQuery: function handleHistoryQuery() {\n      this.historyQueryParams.pageNum = 1;\n      this.getHistoryList();\n    },\n    resetHistoryQuery: function resetHistoryQuery() {\n      this.historyDateRange = [];\n      this.resetForm('historyQueryForm');\n      this.handleHistoryQuery();\n    },\n    handleViewHistory: function handleViewHistory(row) {\n      // TODO: 实现查看详情\n    },\n    handleHistorySizeChange: function handleHistorySizeChange(val) {\n      this.historyQueryParams.pageSize = val;\n      this.getHistoryList();\n    },\n    handleHistoryCurrentChange: function handleHistoryCurrentChange(val) {\n      this.historyQueryParams.pageNum = val;\n      this.getHistoryList();\n    },\n    // 状态相关方法\n    getLeaderStatusText: function getLeaderStatusText(status) {\n      var statusMap = {\n        0: '未开始',\n        1: '准备中',\n        2: '已开始',\n        3: '结算中',\n        4: '已结束'\n      };\n      return statusMap[status] || '未知';\n    },\n    getLeaderStatusType: function getLeaderStatusType(status) {\n      var typeMap = {\n        0: 'info',\n        1: 'warning',\n        2: 'success',\n        3: 'primary',\n        4: 'danger'\n      };\n      return typeMap[status] || 'info';\n    },\n    getFollowStatusText: function getFollowStatusText(status) {\n      return this.getLeaderStatusText(status);\n    },\n    getFollowStatusType: function getFollowStatusType(status) {\n      return this.getLeaderStatusType(status);\n    },\n    getHistoryResultText: function getHistoryResultText(status) {\n      var resultMap = {\n        0: '未结算',\n        1: '盈利',\n        2: '亏损'\n      };\n      return resultMap[status] || '未知';\n    },\n    getHistoryResultType: function getHistoryResultType(status) {\n      var typeMap = {\n        0: 'info',\n        1: 'success',\n        2: 'danger'\n      };\n      return typeMap[status] || 'info';\n    },\n    // 通用方法\n    formatDateTime: function formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm:ss');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "activeTab", "leader<PERSON><PERSON><PERSON>", "leaderList", "leader<PERSON><PERSON><PERSON>", "leader<PERSON><PERSON>", "leader<PERSON><PERSON><PERSON>", "leader<PERSON><PERSON>", "leader<PERSON><PERSON><PERSON><PERSON><PERSON>", "leader<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageNum", "pageSize", "leader<PERSON><PERSON><PERSON>", "status", "leader<PERSON><PERSON>", "required", "message", "trigger", "symbol", "periodNo", "followLoading", "followList", "followTotal", "followDateRange", "followQueryParams", "followerNickname", "historyLoading", "historyList", "historyTotal", "historyDateRange", "historyQueryParams", "resultStatus", "ids", "single", "multiple", "created", "getLeaderList", "methods", "handleTabClick", "tab", "getFollowList", "getHistoryList", "handleLeaderQuery", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetForm", "handleAddLeader", "resetLeaderForm", "handleUpdateLeader", "row", "handleViewLeader", "handleDeleteLeader", "handleLeaderSelectionChange", "selection", "map", "item", "id", "length", "handleLeaderSizeChange", "val", "handleLeaderCurrentChange", "marginBalance", "remark", "submitLeaderForm", "_this", "$refs", "validate", "valid", "handleFollowQuery", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleViewFollow", "handleFollowSizeChange", "handleFollowCurrentChange", "handleHist<PERSON><PERSON><PERSON>y", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleViewHistory", "handleHistorySizeChange", "handleHistoryCurrentChange", "getLeaderStatusText", "statusMap", "getLeaderStatusType", "typeMap", "getFollowStatusText", "getFollowStatusType", "getHistoryResultText", "resultMap", "getHistoryResultType", "formatDateTime", "dateTime", "$moment", "format"], "sources": ["src/views/exchange/copy-trade/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 标签页 -->\r\n      <el-tabs class=\"deal-tabs\" v-model=\"activeTab\" @tab-click=\"handleTabClick\">\r\n        <el-tab-pane label=\"带单管理\" name=\"leader\">\r\n          <!-- 带单管理内容 -->\r\n          <div class=\"tab-content\">\r\n            <!-- 搜索区域 -->\r\n            <div class=\"filter-container\">\r\n              <el-row :gutter=\"20\" class=\"filter-row\">\r\n                <el-col :span=\"6\">\r\n                  <el-input\r\n                    v-model.trim=\"leaderQueryParams.leaderNickname\"\r\n                    placeholder=\"请输入带单人昵称\"\r\n                    clearable\r\n                    class=\"filter-item\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-select\r\n                    v-model=\"leaderQueryParams.status\"\r\n                    placeholder=\"请选择状态\"\r\n                    clearable\r\n                    class=\"filter-item\"\r\n                  >\r\n                    <el-option label=\"未开始\" :value=\"0\" />\r\n                    <el-option label=\"准备中\" :value=\"1\" />\r\n                    <el-option label=\"已开始\" :value=\"2\" />\r\n                    <el-option label=\"结算中\" :value=\"3\" />\r\n                    <el-option label=\"已结束\" :value=\"4\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-date-picker\r\n                    v-model=\"leaderDateRange\"\r\n                    type=\"daterange\"\r\n                    range-separator=\"至\"\r\n                    start-placeholder=\"开始日期\"\r\n                    end-placeholder=\"结束日期\"\r\n                    class=\"filter-item\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleLeaderQuery\">搜索</el-button>\r\n                  <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetLeaderQuery\">重置</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 操作工具栏 -->\r\n            <el-row :gutter=\"10\" class=\"mb8\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddLeader\">新增</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdateLeader\">修改</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDeleteLeader\">删除</el-button>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <!-- 带单管理表格 -->\r\n            <el-table\r\n              v-loading=\"leaderLoading\"\r\n              :data=\"leaderList\"\r\n              @selection-change=\"handleLeaderSelectionChange\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n              <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"带单人\" align=\"center\" prop=\"leaderNickname\" min-width=\"100\" />\r\n              <el-table-column label=\"交易对\" align=\"center\" prop=\"symbol\" min-width=\"100\" />\r\n              <el-table-column label=\"期号\" align=\"center\" prop=\"periodNo\" min-width=\"120\" />\r\n              <el-table-column label=\"当前价格\" align=\"center\" prop=\"currentPrice\" min-width=\"120\" />\r\n              <el-table-column label=\"保证金\" align=\"center\" prop=\"marginBalance\" min-width=\"120\" />\r\n              <el-table-column label=\"当前收益\" align=\"center\" prop=\"currentProfit\" min-width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.currentProfit >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.currentProfit }}\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"收益率\" align=\"center\" prop=\"profitRate\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.profitRate >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.profitRate }}%\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"跟单人数\" align=\"center\" prop=\"followerCount\" min-width=\"100\" />\r\n              <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getLeaderStatusType(scope.row.status)\">\r\n                    {{ getLeaderStatusText(scope.row.status) }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"开始时间\" align=\"center\" prop=\"startTime\" width=\"180\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.startTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\" fixed=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"handleViewLeader(scope.row)\"\r\n                  >查看</el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-edit\"\r\n                    @click=\"handleUpdateLeader(scope.row)\"\r\n                  >修改</el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"handleDeleteLeader(scope.row)\"\r\n                  >删除</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 分页区域 -->\r\n            <div class=\"pagination-container\">\r\n              <el-pagination\r\n                background\r\n                @size-change=\"handleLeaderSizeChange\"\r\n                @current-change=\"handleLeaderCurrentChange\"\r\n                :current-page=\"leaderQueryParams.pageNum\"\r\n                :page-sizes=\"[10, 20, 30, 50]\"\r\n                :page-size=\"leaderQueryParams.pageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                :total=\"leaderTotal\">\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"跟单管理\" name=\"follow\">\r\n          <!-- 跟单管理内容 -->\r\n          <div class=\"tab-content\">\r\n            <!-- 搜索区域 -->\r\n            <div class=\"filter-container\">\r\n              <el-row :gutter=\"20\" class=\"filter-row\">\r\n                <el-col :span=\"6\">\r\n                  <el-input\r\n                    v-model.trim=\"followQueryParams.followerNickname\"\r\n                    placeholder=\"请输入跟单人昵称\"\r\n                    clearable\r\n                    class=\"filter-item\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-select\r\n                    v-model=\"followQueryParams.status\"\r\n                    placeholder=\"请选择状态\"\r\n                    clearable\r\n                    class=\"filter-item\"\r\n                  >\r\n                    <el-option label=\"未开始\" :value=\"0\" />\r\n                    <el-option label=\"准备中\" :value=\"1\" />\r\n                    <el-option label=\"已开始\" :value=\"2\" />\r\n                    <el-option label=\"结算中\" :value=\"3\" />\r\n                    <el-option label=\"已结束\" :value=\"4\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-date-picker\r\n                    v-model=\"followDateRange\"\r\n                    type=\"daterange\"\r\n                    range-separator=\"至\"\r\n                    start-placeholder=\"开始日期\"\r\n                    end-placeholder=\"结束日期\"\r\n                    class=\"filter-item\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleFollowQuery\">搜索</el-button>\r\n                  <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetFollowQuery\">重置</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 跟单管理表格 -->\r\n            <el-table\r\n              v-loading=\"followLoading\"\r\n              :data=\"followList\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"跟单人\" align=\"center\" prop=\"followerNickname\" min-width=\"100\" />\r\n              <el-table-column label=\"期号\" align=\"center\" prop=\"periodNo\" min-width=\"120\" />\r\n              <el-table-column label=\"跟单金额\" align=\"center\" prop=\"followAmount\" min-width=\"120\" />\r\n              <el-table-column label=\"实时收益\" align=\"center\" prop=\"realTimeProfit\" min-width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.realTimeProfit >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.realTimeProfit }}\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"最终收益\" align=\"center\" prop=\"finalProfit\" min-width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.finalProfit >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.finalProfit }}\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"收益率\" align=\"center\" prop=\"profitRate\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.profitRate >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.profitRate }}%\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getFollowStatusType(scope.row.status)\">\r\n                    {{ getFollowStatusText(scope.row.status) }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"跟单时间\" align=\"center\" prop=\"followTime\" width=\"180\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.followTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\" fixed=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"handleViewFollow(scope.row)\"\r\n                  >查看</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 分页区域 -->\r\n            <div class=\"pagination-container\">\r\n              <el-pagination\r\n                background\r\n                @size-change=\"handleFollowSizeChange\"\r\n                @current-change=\"handleFollowCurrentChange\"\r\n                :current-page=\"followQueryParams.pageNum\"\r\n                :page-sizes=\"[10, 20, 30, 50]\"\r\n                :page-size=\"followQueryParams.pageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                :total=\"followTotal\">\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"跟单明细\" name=\"history\">\r\n          <!-- 跟单明细内容 -->\r\n          <div class=\"tab-content\">\r\n            <!-- 搜索区域 -->\r\n            <div class=\"filter-container\">\r\n              <el-row :gutter=\"20\" class=\"filter-row\">\r\n                <el-col :span=\"6\">\r\n                  <el-input\r\n                    v-model.trim=\"historyQueryParams.followerNickname\"\r\n                    placeholder=\"请输入跟单人昵称\"\r\n                    clearable\r\n                    class=\"filter-item\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-select\r\n                    v-model=\"historyQueryParams.resultStatus\"\r\n                    placeholder=\"请选择结果\"\r\n                    clearable\r\n                    class=\"filter-item\"\r\n                  >\r\n                    <el-option label=\"未结算\" :value=\"0\" />\r\n                    <el-option label=\"盈利\" :value=\"1\" />\r\n                    <el-option label=\"亏损\" :value=\"2\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-date-picker\r\n                    v-model=\"historyDateRange\"\r\n                    type=\"daterange\"\r\n                    range-separator=\"至\"\r\n                    start-placeholder=\"开始日期\"\r\n                    end-placeholder=\"结束日期\"\r\n                    class=\"filter-item\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleHistoryQuery\">搜索</el-button>\r\n                  <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetHistoryQuery\">重置</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 跟单明细表格 -->\r\n            <el-table\r\n              v-loading=\"historyLoading\"\r\n              :data=\"historyList\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"跟单人\" align=\"center\" prop=\"followerNickname\" min-width=\"100\" />\r\n              <el-table-column label=\"期号\" align=\"center\" prop=\"periodNo\" min-width=\"120\" />\r\n              <el-table-column label=\"交易对\" align=\"center\" prop=\"symbol\" min-width=\"100\" />\r\n              <el-table-column label=\"盈亏\" align=\"center\" prop=\"profit\" min-width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.profit >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.profit }}\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"收益率\" align=\"center\" prop=\"profitRate\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.profitRate >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.profitRate }}%\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"结果\" align=\"center\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getHistoryResultType(scope.row.resultStatus)\">\r\n                    {{ getHistoryResultText(scope.row.resultStatus) }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"跟单时间\" align=\"center\" prop=\"followTime\" width=\"180\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.followTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"结算时间\" align=\"center\" prop=\"settleTime\" width=\"180\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.settleTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\" fixed=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"handleViewHistory(scope.row)\"\r\n                  >查看</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 分页区域 -->\r\n            <div class=\"pagination-container\">\r\n              <el-pagination\r\n                background\r\n                @size-change=\"handleHistorySizeChange\"\r\n                @current-change=\"handleHistoryCurrentChange\"\r\n                :current-page=\"historyQueryParams.pageNum\"\r\n                :page-sizes=\"[10, 20, 30, 50]\"\r\n                :page-size=\"historyQueryParams.pageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                :total=\"historyTotal\">\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <!-- 带单管理对话框 -->\r\n      <el-dialog :title=\"leaderTitle\" :visible.sync=\"leaderOpen\" width=\"600px\" append-to-body>\r\n        <el-form ref=\"leaderForm\" :model=\"leaderForm\" :rules=\"leaderRules\" label-width=\"100px\">\r\n          <el-form-item label=\"带单人昵称\" prop=\"leaderNickname\">\r\n            <el-input v-model=\"leaderForm.leaderNickname\" placeholder=\"请输入带单人昵称\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"交易对\" prop=\"symbol\">\r\n            <el-input v-model=\"leaderForm.symbol\" placeholder=\"请输入交易对\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"期号\" prop=\"periodNo\">\r\n            <el-input v-model=\"leaderForm.periodNo\" placeholder=\"请输入期号\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"保证金\" prop=\"marginBalance\">\r\n            <el-input-number v-model=\"leaderForm.marginBalance\" :precision=\"8\" :step=\"0.00000001\" :min=\"0\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"策略说明\" prop=\"remark\">\r\n            <el-input v-model=\"leaderForm.remark\" type=\"textarea\" placeholder=\"请输入策略说明\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-radio-group v-model=\"leaderForm.status\">\r\n              <el-radio :label=\"0\">未开始</el-radio>\r\n              <el-radio :label=\"1\">准备中</el-radio>\r\n              <el-radio :label=\"2\">已开始</el-radio>\r\n              <el-radio :label=\"3\">结算中</el-radio>\r\n              <el-radio :label=\"4\">已结束</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitLeaderForm\">确 定</el-button>\r\n          <el-button @click=\"leaderOpen = false\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CopyTrade',\r\n  data() {\r\n    return {\r\n      // 当前激活的标签页\r\n      activeTab: 'leader',\r\n      \r\n      // 带单管理相关\r\n      leaderLoading: false,\r\n      leaderList: [],\r\n      leaderTotal: 0,\r\n      leaderOpen: false,\r\n      leaderTitle: '',\r\n      leaderForm: {},\r\n      leaderDateRange: [],\r\n      leaderQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        leaderNickname: null,\r\n        status: null\r\n      },\r\n      leaderRules: {\r\n        leaderNickname: [\r\n          { required: true, message: '带单人昵称不能为空', trigger: 'blur' }\r\n        ],\r\n        symbol: [\r\n          { required: true, message: '交易对不能为空', trigger: 'blur' }\r\n        ],\r\n        periodNo: [\r\n          { required: true, message: '期号不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 跟单管理相关\r\n      followLoading: false,\r\n      followList: [],\r\n      followTotal: 0,\r\n      followDateRange: [],\r\n      followQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        followerNickname: null,\r\n        status: null\r\n      },\r\n\r\n      // 跟单明细相关\r\n      historyLoading: false,\r\n      historyList: [],\r\n      historyTotal: 0,\r\n      historyDateRange: [],\r\n      historyQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        followerNickname: null,\r\n        resultStatus: null\r\n      },\r\n\r\n      // 选中数组\r\n      ids: [],\r\n      single: true,\r\n      multiple: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getLeaderList()\r\n  },\r\n  methods: {\r\n    // 标签页切换\r\n    handleTabClick(tab) {\r\n      if (tab.name === 'leader') {\r\n        this.getLeaderList()\r\n      } else if (tab.name === 'follow') {\r\n        this.getFollowList()\r\n      } else if (tab.name === 'history') {\r\n        this.getHistoryList()\r\n      }\r\n    },\r\n\r\n    // 带单管理相关方法\r\n    getLeaderList() {\r\n      this.leaderLoading = true\r\n      // TODO: 调用API获取带单管理数据\r\n      this.leaderLoading = false\r\n    },\r\n    handleLeaderQuery() {\r\n      this.leaderQueryParams.pageNum = 1\r\n      this.getLeaderList()\r\n    },\r\n    resetLeaderQuery() {\r\n      this.leaderDateRange = []\r\n      this.resetForm('leaderQueryForm')\r\n      this.handleLeaderQuery()\r\n    },\r\n    handleAddLeader() {\r\n      this.resetLeaderForm()\r\n      this.leaderOpen = true\r\n      this.leaderTitle = '添加带单管理'\r\n    },\r\n    handleUpdateLeader(row) {\r\n      this.resetLeaderForm()\r\n      // TODO: 获取详情数据\r\n      this.leaderOpen = true\r\n      this.leaderTitle = '修改带单管理'\r\n    },\r\n    handleViewLeader(row) {\r\n      // TODO: 实现查看详情\r\n    },\r\n    handleDeleteLeader(row) {\r\n      // TODO: 实现删除功能\r\n    },\r\n    handleLeaderSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    handleLeaderSizeChange(val) {\r\n      this.leaderQueryParams.pageSize = val\r\n      this.getLeaderList()\r\n    },\r\n    handleLeaderCurrentChange(val) {\r\n      this.leaderQueryParams.pageNum = val\r\n      this.getLeaderList()\r\n    },\r\n    resetLeaderForm() {\r\n      this.leaderForm = {\r\n        id: null,\r\n        leaderNickname: null,\r\n        symbol: null,\r\n        periodNo: null,\r\n        marginBalance: 900000,\r\n        remark: null,\r\n        status: 0\r\n      }\r\n    },\r\n    submitLeaderForm() {\r\n      this.$refs['leaderForm'].validate((valid) => {\r\n        if (valid) {\r\n          // TODO: 提交表单\r\n          this.leaderOpen = false\r\n          this.getLeaderList()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 跟单管理相关方法\r\n    getFollowList() {\r\n      this.followLoading = true\r\n      // TODO: 调用API获取跟单管理数据\r\n      this.followLoading = false\r\n    },\r\n    handleFollowQuery() {\r\n      this.followQueryParams.pageNum = 1\r\n      this.getFollowList()\r\n    },\r\n    resetFollowQuery() {\r\n      this.followDateRange = []\r\n      this.resetForm('followQueryForm')\r\n      this.handleFollowQuery()\r\n    },\r\n    handleViewFollow(row) {\r\n      // TODO: 实现查看详情\r\n    },\r\n    handleFollowSizeChange(val) {\r\n      this.followQueryParams.pageSize = val\r\n      this.getFollowList()\r\n    },\r\n    handleFollowCurrentChange(val) {\r\n      this.followQueryParams.pageNum = val\r\n      this.getFollowList()\r\n    },\r\n\r\n    // 跟单明细相关方法\r\n    getHistoryList() {\r\n      this.historyLoading = true\r\n      // TODO: 调用API获取跟单明细数据\r\n      this.historyLoading = false\r\n    },\r\n    handleHistoryQuery() {\r\n      this.historyQueryParams.pageNum = 1\r\n      this.getHistoryList()\r\n    },\r\n    resetHistoryQuery() {\r\n      this.historyDateRange = []\r\n      this.resetForm('historyQueryForm')\r\n      this.handleHistoryQuery()\r\n    },\r\n    handleViewHistory(row) {\r\n      // TODO: 实现查看详情\r\n    },\r\n    handleHistorySizeChange(val) {\r\n      this.historyQueryParams.pageSize = val\r\n      this.getHistoryList()\r\n    },\r\n    handleHistoryCurrentChange(val) {\r\n      this.historyQueryParams.pageNum = val\r\n      this.getHistoryList()\r\n    },\r\n\r\n    // 状态相关方法\r\n    getLeaderStatusText(status) {\r\n      const statusMap = {\r\n        0: '未开始',\r\n        1: '准备中',\r\n        2: '已开始',\r\n        3: '结算中',\r\n        4: '已结束'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n    getLeaderStatusType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'warning',\r\n        2: 'success',\r\n        3: 'primary',\r\n        4: 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n    getFollowStatusText(status) {\r\n      return this.getLeaderStatusText(status)\r\n    },\r\n    getFollowStatusType(status) {\r\n      return this.getLeaderStatusType(status)\r\n    },\r\n    getHistoryResultText(status) {\r\n      const resultMap = {\r\n        0: '未结算',\r\n        1: '盈利',\r\n        2: '亏损'\r\n      }\r\n      return resultMap[status] || '未知'\r\n    },\r\n    getHistoryResultType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n\r\n    // 通用方法\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm:ss')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.tab-content {\r\n  padding: 10px 0;\r\n}\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n}\r\n.filter-item {\r\n  width: 100%;\r\n}\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n.text-success {\r\n  color: #67c23a;\r\n}\r\n.text-danger {\r\n  color: #f56c6c;\r\n}\r\n\r\n/* 自定义标签页样式 */\r\n.deal-tabs /deep/ .el-tabs__item {\r\n  color: #fff !important;\r\n  background: transparent !important;\r\n  border-radius: 12px 12px 0 0;\r\n  position: relative;\r\n  transition: background 0.2s, color 0.2s;\r\n  z-index: 1;\r\n  height: 48px;\r\n  line-height: 48px;\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-sizing: border-box;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  padding: 0 24px;\r\n}\r\n.deal-tabs /deep/ .el-tabs__item.is-active {\r\n  color: #111 !important;\r\n  background: #FFD700 !important;\r\n  font-weight: bold;\r\n  box-shadow: 0 -2px 8px rgba(255,215,0,0.08);\r\n  z-index: 2;\r\n}\r\n.deal-tabs /deep/ .el-tabs__item.is-active::after {\r\n  content: '';\r\n  display: block;\r\n  height: 4px;\r\n  background: #FFD700;\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  border-radius: 2px;\r\n}\r\n.deal-tabs /deep/ .el-tabs__nav {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n</style> "], "mappings": ";;;AA0aA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MAEA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,eAAA;MACAC,iBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;QACAC,MAAA;MACA;MACAC,WAAA;QACAF,cAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,MAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,QAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAG,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,eAAA;MACAC,iBAAA;QACAd,OAAA;QACAC,QAAA;QACAc,gBAAA;QACAZ,MAAA;MACA;MAEA;MACAa,cAAA;MACAC,WAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,kBAAA;QACApB,OAAA;QACAC,QAAA;QACAc,gBAAA;QACAM,YAAA;MACA;MAEA;MACAC,GAAA;MACAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,IAAAA,GAAA,CAAAxC,IAAA;QACA,KAAAqC,aAAA;MACA,WAAAG,GAAA,CAAAxC,IAAA;QACA,KAAAyC,aAAA;MACA,WAAAD,GAAA,CAAAxC,IAAA;QACA,KAAA0C,cAAA;MACA;IACA;IAEA;IACAL,aAAA,WAAAA,cAAA;MACA,KAAAlC,aAAA;MACA;MACA,KAAAA,aAAA;IACA;IACAwC,iBAAA,WAAAA,kBAAA;MACA,KAAAjC,iBAAA,CAAAC,OAAA;MACA,KAAA0B,aAAA;IACA;IACAO,gBAAA,WAAAA,iBAAA;MACA,KAAAnC,eAAA;MACA,KAAAoC,SAAA;MACA,KAAAF,iBAAA;IACA;IACAG,eAAA,WAAAA,gBAAA;MACA,KAAAC,eAAA;MACA,KAAAzC,UAAA;MACA,KAAAC,WAAA;IACA;IACAyC,kBAAA,WAAAA,mBAAAC,GAAA;MACA,KAAAF,eAAA;MACA;MACA,KAAAzC,UAAA;MACA,KAAAC,WAAA;IACA;IACA2C,gBAAA,WAAAA,iBAAAD,GAAA;MACA;IAAA,CACA;IACAE,kBAAA,WAAAA,mBAAAF,GAAA;MACA;IAAA,CACA;IACAG,2BAAA,WAAAA,4BAAAC,SAAA;MACA,KAAApB,GAAA,GAAAoB,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAAtB,MAAA,GAAAmB,SAAA,CAAAI,MAAA;MACA,KAAAtB,QAAA,IAAAkB,SAAA,CAAAI,MAAA;IACA;IACAC,sBAAA,WAAAA,uBAAAC,GAAA;MACA,KAAAjD,iBAAA,CAAAE,QAAA,GAAA+C,GAAA;MACA,KAAAtB,aAAA;IACA;IACAuB,yBAAA,WAAAA,0BAAAD,GAAA;MACA,KAAAjD,iBAAA,CAAAC,OAAA,GAAAgD,GAAA;MACA,KAAAtB,aAAA;IACA;IACAU,eAAA,WAAAA,gBAAA;MACA,KAAAvC,UAAA;QACAgD,EAAA;QACA3C,cAAA;QACAM,MAAA;QACAC,QAAA;QACAyC,aAAA;QACAC,MAAA;QACAhD,MAAA;MACA;IACA;IACAiD,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAH,KAAA,CAAA1D,UAAA;UACA0D,KAAA,CAAA3B,aAAA;QACA;MACA;IACA;IAEA;IACAI,aAAA,WAAAA,cAAA;MACA,KAAApB,aAAA;MACA;MACA,KAAAA,aAAA;IACA;IACA+C,iBAAA,WAAAA,kBAAA;MACA,KAAA3C,iBAAA,CAAAd,OAAA;MACA,KAAA8B,aAAA;IACA;IACA4B,gBAAA,WAAAA,iBAAA;MACA,KAAA7C,eAAA;MACA,KAAAqB,SAAA;MACA,KAAAuB,iBAAA;IACA;IACAE,gBAAA,WAAAA,iBAAArB,GAAA;MACA;IAAA,CACA;IACAsB,sBAAA,WAAAA,uBAAAZ,GAAA;MACA,KAAAlC,iBAAA,CAAAb,QAAA,GAAA+C,GAAA;MACA,KAAAlB,aAAA;IACA;IACA+B,yBAAA,WAAAA,0BAAAb,GAAA;MACA,KAAAlC,iBAAA,CAAAd,OAAA,GAAAgD,GAAA;MACA,KAAAlB,aAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAAf,cAAA;MACA;MACA,KAAAA,cAAA;IACA;IACA8C,kBAAA,WAAAA,mBAAA;MACA,KAAA1C,kBAAA,CAAApB,OAAA;MACA,KAAA+B,cAAA;IACA;IACAgC,iBAAA,WAAAA,kBAAA;MACA,KAAA5C,gBAAA;MACA,KAAAe,SAAA;MACA,KAAA4B,kBAAA;IACA;IACAE,iBAAA,WAAAA,kBAAA1B,GAAA;MACA;IAAA,CACA;IACA2B,uBAAA,WAAAA,wBAAAjB,GAAA;MACA,KAAA5B,kBAAA,CAAAnB,QAAA,GAAA+C,GAAA;MACA,KAAAjB,cAAA;IACA;IACAmC,0BAAA,WAAAA,2BAAAlB,GAAA;MACA,KAAA5B,kBAAA,CAAApB,OAAA,GAAAgD,GAAA;MACA,KAAAjB,cAAA;IACA;IAEA;IACAoC,mBAAA,WAAAA,oBAAAhE,MAAA;MACA,IAAAiE,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAjE,MAAA;IACA;IACAkE,mBAAA,WAAAA,oBAAAlE,MAAA;MACA,IAAAmE,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAnE,MAAA;IACA;IACAoE,mBAAA,WAAAA,oBAAApE,MAAA;MACA,YAAAgE,mBAAA,CAAAhE,MAAA;IACA;IACAqE,mBAAA,WAAAA,oBAAArE,MAAA;MACA,YAAAkE,mBAAA,CAAAlE,MAAA;IACA;IACAsE,oBAAA,WAAAA,qBAAAtE,MAAA;MACA,IAAAuE,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAvE,MAAA;IACA;IACAwE,oBAAA,WAAAA,qBAAAxE,MAAA;MACA,IAAAmE,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAnE,MAAA;IACA;IAEA;IACAyE,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,YAAAC,OAAA,CAAAD,QAAA,EAAAE,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}