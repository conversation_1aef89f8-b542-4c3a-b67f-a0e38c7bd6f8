/// <reference types="@webgpu/types" />
import type { <PERSON><PERSON>, SamplerDescriptor } from '../api';
import { ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class Sampler_WebGPU extends ResourceBase_WebGPU implements Sampler {
    type: ResourceType.Sampler;
    gpuSampler: GPUSampler;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: SamplerDescriptor;
    });
}
