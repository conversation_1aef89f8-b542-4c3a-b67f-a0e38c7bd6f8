{"version": 3, "file": "rect.js", "sourceRoot": "", "sources": ["../../src/shape/rect.ts"], "names": [], "mappings": "AAAA;;;GAGG;;AAEH,OAAO,SAAS,MAAM,QAAQ,CAAC;AAC/B,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AACrC,OAAO,MAAM,MAAM,wBAAwB,CAAC;AAC5C,OAAO,gBAAgB,MAAM,+BAA+B,CAAC;AAC7D,OAAO,aAAa,MAAM,+BAA+B,CAAC;AAE1D;IAAmB,wBAAS;IAA5B;;IA0EA,CAAC;IAzEC,8BAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6BACK,KAAK,KACR,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,KAAK,EAAE,CAAC,EACR,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,CAAC,IACT;IACJ,CAAC;IAED,+BAAgB,GAAhB,UAAiB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;QAChD,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;QACrB,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;QACrB,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1B,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,UAAU;QACV,IAAI,CAAC,MAAM,EAAE;YACX,IAAM,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;YAChC,YAAY;YACZ,IAAI,MAAM,IAAI,QAAQ,EAAE;gBACtB,OAAO,KAAK,CAAC,IAAI,GAAG,SAAS,EAAE,IAAI,GAAG,SAAS,EAAE,KAAK,GAAG,SAAS,EAAE,MAAM,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC/F;YACD,MAAM;YACN,IAAI,MAAM,EAAE;gBACV,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC/C;YACD,IAAI,QAAQ,EAAE;gBACZ,OAAO,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC3D;SACF;aAAM;YACL,IAAI,KAAK,GAAG,KAAK,CAAC;YAClB,IAAI,QAAQ,EAAE;gBACZ,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC9E;YACD,sBAAsB;YACtB,2BAA2B;YAC3B,IAAI,CAAC,KAAK,IAAI,MAAM,EAAE;gBACpB,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACnC;YACD,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,yBAAU,GAAV,UAAW,OAAO;QAChB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAClB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAClB,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1B,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAE5B,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,cAAc;YACd,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SACnC;aAAM;YACC,IAAA,KAAmB,WAAW,CAAC,MAAM,CAAC,EAArC,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAuB,CAAC;YAC7C,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1B,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;YAClC,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACrE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC;YAC3C,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YAC7E,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;YACnC,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3E,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YAC1B,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;YACpE,OAAO,CAAC,SAAS,EAAE,CAAC;SACrB;IACH,CAAC;IACH,WAAC;AAAD,CAAC,AA1ED,CAAmB,SAAS,GA0E3B;AAED,eAAe,IAAI,CAAC"}