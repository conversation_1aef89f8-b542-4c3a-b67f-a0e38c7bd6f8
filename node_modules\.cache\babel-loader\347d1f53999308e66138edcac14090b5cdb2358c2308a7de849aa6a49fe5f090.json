{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport request from '@/utils/request';\n\n// 获取转账记录列表\nexport function getTransferList(params) {\n  return request({\n    url: '/finance/transfer/list',\n    method: 'get',\n    params: params,\n    error: function error(_error) {\n      console.error('请求失败:', _error);\n      return Promise.reject(_error);\n    }\n  });\n}\n\n// 获取转账统计信息\nexport function getTransferStatistics() {\n  return request({\n    url: '/finance/transfer/statistics',\n    method: 'get'\n  });\n}\n\n// 导出转账记录\nexport function exportTransferRecord(params) {\n  return request({\n    url: '/finance/transfer/export',\n    method: 'get',\n    params: params,\n    responseType: 'blob'\n  });\n}", "map": {"version": 3, "names": ["request", "getTransferList", "params", "url", "method", "error", "console", "Promise", "reject", "getTransferStatistics", "exportTransferRecord", "responseType"], "sources": ["F:/常规项目/华通宝/adminweb/src/api/finance/transfer.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取转账记录列表\r\nexport function getTransferList(params) {\r\n \r\n  return request({\r\n    url: '/finance/transfer/list',\r\n    method: 'get',\r\n    params,\r\n    error: (error) => {\r\n      console.error('请求失败:', error)\r\n      return Promise.reject(error)\r\n    }\r\n  })\r\n}\r\n\r\n// 获取转账统计信息\r\nexport function getTransferStatistics() {\r\n  return request({\r\n    url: '/finance/transfer/statistics',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 导出转账记录\r\nexport function exportTransferRecord(params) {\r\n  return request({\r\n    url: '/finance/transfer/export',\r\n    method: 'get',\r\n    params,\r\n    responseType: 'blob'\r\n  })\r\n} "], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAE;EAEtC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA,MAAM;IACNG,KAAK,EAAE,SAAPA,KAAKA,CAAGA,MAAK,EAAK;MAChBC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,MAAK,CAAC;MAC7B,OAAOE,OAAO,CAACC,MAAM,CAACH,MAAK,CAAC;IAC9B;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,qBAAqBA,CAAA,EAAG;EACtC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,oBAAoBA,CAACR,MAAM,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA,MAAM;IACNS,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}