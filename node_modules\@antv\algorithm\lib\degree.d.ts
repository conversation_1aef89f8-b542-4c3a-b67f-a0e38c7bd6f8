import { GraphData, DegreeType } from "./types";
declare const degree: (graphData: GraphData) => DegreeType;
export default degree;
/**
 * 获取指定节点的入度
 * @param graphData 图数据
 * @param nodeId 节点ID
 */
export declare const getInDegree: (graphData: GraphData, nodeId: string) => number;
/**
 * 获取指定节点的出度
 * @param graphData 图数据
 * @param nodeId 节点ID
 */
export declare const getOutDegree: (graphData: GraphData, nodeId: string) => number;
