{"ast": null, "code": "import _regeneratorRuntime from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _objectSpread from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getJobLogList, cleanJobLog } from '@/api/log/task';\nimport moment from 'moment';\nexport default {\n  name: 'LogTask',\n  data: function data() {\n    return {\n      loading: false,\n      listQuery: {\n        page: 1,\n        limit: 10,\n        jobType: '',\n        executionResult: '',\n        dateRange: []\n      },\n      total: 0,\n      tableData: [],\n      typeMap: {\n        'SYSTEM': {\n          label: '系统任务',\n          type: 'primary'\n        },\n        'MONITOR': {\n          label: '监控任务',\n          type: 'warning'\n        },\n        'BACKUP': {\n          label: '备份任务',\n          type: 'success'\n        }\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var params, res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _this.loading = true;\n              params = _objectSpread({}, _this.listQuery);\n              if (params.dateRange && params.dateRange.length === 2) {\n                params.startDate = params.dateRange[0];\n                params.endDate = params.dateRange[1];\n              }\n              delete params.dateRange;\n              _context.next = 7;\n              return getJobLogList(params);\n            case 7:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.tableData = res.data;\n                _this.total = res.total;\n              } else {\n                _this.$message.error(res.msg || '获取任务日志失败');\n              }\n              _context.next = 15;\n              break;\n            case 11:\n              _context.prev = 11;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('获取任务日志失败:', _context.t0);\n              _this.$message.error('获取任务日志失败');\n            case 15:\n              _context.prev = 15;\n              _this.loading = false;\n              return _context.finish(15);\n            case 18:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 11, 15, 18]]);\n      }))();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    handleClean: function handleClean() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return _this2.$confirm('是否确认清空所有任务日志数据?', '警告', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n              });\n            case 3:\n              _context2.next = 5;\n              return cleanJobLog();\n            case 5:\n              res = _context2.sent;\n              if (res.code === 0) {\n                _this2.$message.success('清空成功');\n                _this2.getList();\n              } else {\n                _this2.$message.error(res.msg || '清空失败');\n              }\n              _context2.next = 12;\n              break;\n            case 9:\n              _context2.prev = 9;\n              _context2.t0 = _context2[\"catch\"](0);\n              if (_context2.t0 !== 'cancel') {\n                console.error('清空任务日志失败:', _context2.t0);\n                _this2.$message.error('清空任务日志失败');\n              }\n            case 12:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 9]]);\n      }))();\n    },\n    handleReset: function handleReset() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        jobType: '',\n        executionResult: '',\n        dateRange: []\n      };\n      this.getList();\n    },\n    formatDateTime: function formatDateTime(time) {\n      if (!time) {\n        return '-';\n      }\n      return moment(time).format('YYYY-MM-DD HH:mm:ss');\n    },\n    getTypeTag: function getTypeTag(type) {\n      var _this$typeMap$type;\n      return ((_this$typeMap$type = this.typeMap[type]) === null || _this$typeMap$type === void 0 ? void 0 : _this$typeMap$type.type) || '';\n    },\n    getTypeText: function getTypeText(type) {\n      var _this$typeMap$type2;\n      return ((_this$typeMap$type2 = this.typeMap[type]) === null || _this$typeMap$type2 === void 0 ? void 0 : _this$typeMap$type2.label) || '未知';\n    }\n  }\n};", "map": {"version": 3, "names": ["getJobLogList", "cleanJobLog", "moment", "name", "data", "loading", "list<PERSON>uery", "page", "limit", "jobType", "executionResult", "date<PERSON><PERSON><PERSON>", "total", "tableData", "typeMap", "label", "type", "created", "getList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "params", "res", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "length", "startDate", "endDate", "sent", "code", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleSizeChange", "val", "handleCurrentChange", "handleClean", "_this2", "_callee2", "_callee2$", "_context2", "$confirm", "confirmButtonText", "cancelButtonText", "success", "handleReset", "formatDateTime", "time", "format", "getTypeTag", "_this$typeMap$type", "getTypeText", "_this$typeMap$type2"], "sources": ["src/views/log/task/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <div class=\"filter-line\">\r\n          <el-select\r\n            v-model=\"listQuery.jobType\"\r\n            placeholder=\"任务类型\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 120px\"\r\n            class=\"filter-item\"\r\n          >\r\n            <el-option label=\"系统任务\" value=\"SYSTEM\" />\r\n            <el-option label=\"监控任务\" value=\"MONITOR\" />\r\n            <el-option label=\"备份任务\" value=\"BACKUP\" />\r\n          </el-select>\r\n          <el-select\r\n            v-model=\"listQuery.executionResult\"\r\n            placeholder=\"执行结果\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 120px\"\r\n            class=\"filter-item\"\r\n          >\r\n            <el-option label=\"成功\" value=\"成功\" />\r\n            <el-option label=\"失败\" value=\"失败\" />\r\n          </el-select>\r\n          <el-date-picker\r\n            v-model=\"listQuery.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"YYYY-MM-DD\"\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            class=\"filter-item\"\r\n          />\r\n          <div class=\"filter-buttons\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"getList\" :loading=\"loading\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" size=\"small\" @click=\"handleReset\">重置</el-button>\r\n            <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" @click=\"handleClean\">清空</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n        <el-table-column label=\"任务名称\" prop=\"jobName\" align=\"center\" min-width=\"120\" />\r\n        <el-table-column label=\"任务类型\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getTypeTag(scope.row.jobType)\">\r\n              {{ getTypeText(scope.row.jobType) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"执行时间\" align=\"center\" min-width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.executionTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"耗时(ms)\" prop=\"executionDuration\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.executionDuration }}ms</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"执行结果\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.executionResult === '成功' ? 'success' : 'danger'\">\r\n              {{ scope.row.executionResult }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"执行信息\" prop=\"executionMessage\" align=\"center\" min-width=\"200\" show-overflow-tooltip />\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getJobLogList, cleanJobLog } from '@/api/log/task'\r\nimport moment from 'moment'\r\n\r\nexport default {\r\n  name: 'LogTask',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        jobType: '',\r\n        executionResult: '',\r\n        dateRange: []\r\n      },\r\n      total: 0,\r\n      tableData: [],\r\n      typeMap: {\r\n        'SYSTEM': { label: '系统任务', type: 'primary' },\r\n        'MONITOR': { label: '监控任务', type: 'warning' },\r\n        'BACKUP': { label: '备份任务', type: 'success' }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    async getList() {\r\n      try {\r\n        this.loading = true\r\n        const params = { ...this.listQuery }\r\n        if (params.dateRange && params.dateRange.length === 2) {\r\n          params.startDate = params.dateRange[0]\r\n          params.endDate = params.dateRange[1]\r\n        }\r\n        delete params.dateRange\r\n\r\n        const res = await getJobLogList(params)\r\n        if (res.code === 0) {\r\n          this.tableData = res.data\r\n          this.total = res.total\r\n        } else {\r\n          this.$message.error(res.msg || '获取任务日志失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取任务日志失败:', error)\r\n        this.$message.error('获取任务日志失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n\r\n    async handleClean() {\r\n      try {\r\n        await this.$confirm('是否确认清空所有任务日志数据?', '警告', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        const res = await cleanJobLog()\r\n        if (res.code === 0) {\r\n          this.$message.success('清空成功')\r\n          this.getList()\r\n        } else {\r\n          this.$message.error(res.msg || '清空失败')\r\n        }\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          console.error('清空任务日志失败:', error)\r\n          this.$message.error('清空任务日志失败')\r\n        }\r\n      }\r\n    },\r\n\r\n    handleReset() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        jobType: '',\r\n        executionResult: '',\r\n        dateRange: []\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    formatDateTime(time) {\r\n      if (!time) {\r\n        return '-'\r\n      }\r\n      return moment(time).format('YYYY-MM-DD HH:mm:ss')\r\n    },\r\n\r\n    getTypeTag(type) {\r\n      return this.typeMap[type]?.type || ''\r\n    },\r\n\r\n    getTypeText(type) {\r\n      return this.typeMap[type]?.label || '未知'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    \r\n    .filter-line {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 15px 10px;\r\n      align-items: center;\r\n    }\r\n\r\n    .filter-item {\r\n      margin: 0;\r\n    }\r\n\r\n    .filter-buttons {\r\n      white-space: nowrap;\r\n      \r\n      .el-button {\r\n        margin-left: 0;\r\n        margin-right: 10px;\r\n        \r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;AAsGA,SAAAA,aAAA,EAAAC,WAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,eAAA;QACAC,SAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACAC,OAAA;QACA;UAAAC,KAAA;UAAAC,IAAA;QAAA;QACA;UAAAD,KAAA;UAAAC,IAAA;QAAA;QACA;UAAAD,KAAA;UAAAC,IAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAV,KAAA,CAAAf,OAAA;cACAoB,MAAA,GAAAO,aAAA,KAAAZ,KAAA,CAAAd,SAAA;cACA,IAAAmB,MAAA,CAAAd,SAAA,IAAAc,MAAA,CAAAd,SAAA,CAAAsB,MAAA;gBACAR,MAAA,CAAAS,SAAA,GAAAT,MAAA,CAAAd,SAAA;gBACAc,MAAA,CAAAU,OAAA,GAAAV,MAAA,CAAAd,SAAA;cACA;cACA,OAAAc,MAAA,CAAAd,SAAA;cAAAkB,QAAA,CAAAE,IAAA;cAAA,OAEA/B,aAAA,CAAAyB,MAAA;YAAA;cAAAC,GAAA,GAAAG,QAAA,CAAAO,IAAA;cACA,IAAAV,GAAA,CAAAW,IAAA;gBACAjB,KAAA,CAAAP,SAAA,GAAAa,GAAA,CAAAtB,IAAA;gBACAgB,KAAA,CAAAR,KAAA,GAAAc,GAAA,CAAAd,KAAA;cACA;gBACAQ,KAAA,CAAAkB,QAAA,CAAAC,KAAA,CAAAb,GAAA,CAAAc,GAAA;cACA;cAAAX,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAY,EAAA,GAAAZ,QAAA;cAEAa,OAAA,CAAAH,KAAA,cAAAV,QAAA,CAAAY,EAAA;cACArB,KAAA,CAAAkB,QAAA,CAAAC,KAAA;YAAA;cAAAV,QAAA,CAAAC,IAAA;cAEAV,KAAA,CAAAf,OAAA;cAAA,OAAAwB,QAAA,CAAAc,MAAA;YAAA;YAAA;cAAA,OAAAd,QAAA,CAAAe,IAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IAEAqB,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAxC,SAAA,CAAAE,KAAA,GAAAsC,GAAA;MACA,KAAA5B,OAAA;IACA;IAEA6B,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAxC,SAAA,CAAAC,IAAA,GAAAuC,GAAA;MACA,KAAA5B,OAAA;IACA;IAEA8B,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAA5B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAA;QAAA,IAAAxB,GAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAwB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtB,IAAA,GAAAsB,SAAA,CAAArB,IAAA;YAAA;cAAAqB,SAAA,CAAAtB,IAAA;cAAAsB,SAAA,CAAArB,IAAA;cAAA,OAEAkB,MAAA,CAAAI,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAvC,IAAA;cACA;YAAA;cAAAoC,SAAA,CAAArB,IAAA;cAAA,OACA9B,WAAA;YAAA;cAAAyB,GAAA,GAAA0B,SAAA,CAAAhB,IAAA;cACA,IAAAV,GAAA,CAAAW,IAAA;gBACAY,MAAA,CAAAX,QAAA,CAAAkB,OAAA;gBACAP,MAAA,CAAA/B,OAAA;cACA;gBACA+B,MAAA,CAAAX,QAAA,CAAAC,KAAA,CAAAb,GAAA,CAAAc,GAAA;cACA;cAAAY,SAAA,CAAArB,IAAA;cAAA;YAAA;cAAAqB,SAAA,CAAAtB,IAAA;cAAAsB,SAAA,CAAAX,EAAA,GAAAW,SAAA;cAEA,IAAAA,SAAA,CAAAX,EAAA;gBACAC,OAAA,CAAAH,KAAA,cAAAa,SAAA,CAAAX,EAAA;gBACAQ,MAAA,CAAAX,QAAA,CAAAC,KAAA;cACA;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA;IAEA;IAEAO,WAAA,WAAAA,YAAA;MACA,KAAAnD,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,eAAA;QACAC,SAAA;MACA;MACA,KAAAO,OAAA;IACA;IAEAwC,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAzD,MAAA,CAAAyD,IAAA,EAAAC,MAAA;IACA;IAEAC,UAAA,WAAAA,WAAA7C,IAAA;MAAA,IAAA8C,kBAAA;MACA,SAAAA,kBAAA,QAAAhD,OAAA,CAAAE,IAAA,eAAA8C,kBAAA,uBAAAA,kBAAA,CAAA9C,IAAA;IACA;IAEA+C,WAAA,WAAAA,YAAA/C,IAAA;MAAA,IAAAgD,mBAAA;MACA,SAAAA,mBAAA,QAAAlD,OAAA,CAAAE,IAAA,eAAAgD,mBAAA,uBAAAA,mBAAA,CAAAjD,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}