/**
 * @fileoverview 矩形
 * <AUTHOR>
 */
import ShapeBase from './base';
declare class Rect extends ShapeBase {
    getDefaultAttrs(): {
        x: number;
        y: number;
        width: number;
        height: number;
        radius: number;
        lineWidth: number;
        lineAppendWidth: number;
        strokeOpacity: number;
        fillOpacity: number;
        matrix: any;
        opacity: number;
    };
    isInStrokeOrPath(x: any, y: any, isStroke: any, isFill: any, lineWidth: any): boolean;
    createPath(context: any): void;
}
export default Rect;
