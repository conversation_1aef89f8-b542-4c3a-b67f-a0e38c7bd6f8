{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取系统参数\nexport function getParams() {\n  return request({\n    url: '/system/params',\n    method: 'get'\n  });\n}\n\n// 更新系统参数\nexport function updateParams(data) {\n  return request({\n    url: '/system/params',\n    method: 'put',\n    data: data\n  });\n}", "map": {"version": 3, "names": ["request", "getParams", "url", "method", "updateParams", "data"], "sources": ["F:/常规项目/华通云/adminweb/src/api/system/params.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取系统参数\r\nexport function getParams() {\r\n  return request({\r\n    url: '/system/params',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新系统参数\r\nexport function updateParams(data) {\r\n  return request({\r\n    url: '/system/params',\r\n    method: 'put',\r\n    data\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAOL,OAAO,CAAC;IACbE,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}