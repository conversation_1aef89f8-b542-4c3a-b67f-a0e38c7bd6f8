export declare function parseUniformName(name: string): {
    name: string;
    isArray: boolean;
    length: number;
};
declare function getSamplerSetter(): (gl: WebGLRenderingContextBase, location: WebGLUniformLocation, value: any) => boolean;
export declare const UNIFORM_SETTERS: {
    5126: any;
    35664: any;
    35665: any;
    35666: any;
    5124: any;
    35667: any;
    35668: any;
    35669: any;
    35670: any;
    35671: any;
    35672: any;
    35673: any;
    35674: any;
    35675: any;
    35676: any;
    5125: any;
    36294: any;
    36295: any;
    36296: any;
    35685: any;
    35686: any;
    35687: any;
    35688: any;
    35689: any;
    35690: any;
    35678: typeof getSamplerSetter;
    35680: typeof getSamplerSetter;
    35679: typeof getSamplerSetter;
    35682: typeof getSamplerSetter;
    36289: typeof getSamplerSetter;
    36292: typeof getSamplerSetter;
    36293: typeof getSamplerSetter;
    36298: typeof getSamplerSetter;
    36299: typeof getSamplerSetter;
    36300: typeof getSamplerSetter;
    36303: typeof getSamplerSetter;
    36306: typeof getSamplerSetter;
    36307: typeof getSamplerSetter;
    36308: typeof getSamplerSetter;
    36311: typeof getSamplerSetter;
};
export declare function getUniformSetter(gl: WebGLRenderingContext, location: WebGLUniformLocation, info: WebGLActiveInfo): any;
export {};
