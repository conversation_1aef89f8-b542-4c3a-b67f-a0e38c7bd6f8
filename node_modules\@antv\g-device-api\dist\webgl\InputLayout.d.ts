import { Format, ResourceType } from '../api';
import type { InputLayout, InputLayoutBufferDescriptor, InputLayoutDescriptor } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
import { Program_GL } from './Program';
export declare class InputLayout_GL extends ResourceBase_GL implements InputLayout {
    type: ResourceType.InputLayout;
    vertexBufferDescriptors: (InputLayoutBufferDescriptor | null)[];
    indexBufferFormat: Format | null;
    indexBufferType: GLenum | null;
    indexBufferCompByteSize: number | null;
    vao: WebGLVertexArrayObject;
    program: Program_GL;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: InputLayoutDescriptor;
    });
    destroy(): void;
}
