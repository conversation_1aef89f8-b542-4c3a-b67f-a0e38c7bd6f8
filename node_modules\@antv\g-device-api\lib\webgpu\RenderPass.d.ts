/// <reference types="@webgpu/types" />
import { <PERSON><PERSON><PERSON>, Buffer, IndexBufferDescriptor, InputLayout, RenderBundle, RenderPass, RenderPassDescriptor, RenderPipeline, VertexBufferDescriptor } from '../api';
import { Device_WebGPU } from './Device';
export declare class RenderPass_WebGPU implements RenderPass {
    private device;
    frameCommandEncoder: GPUCommandEncoder | null;
    descriptor: RenderPassDescriptor;
    private renderBundle;
    private gpuRenderPassEncoder;
    private gpuRenderPassDescriptor;
    private gpuColorAttachments;
    private gpuDepthStencilAttachment;
    private gfxColorAttachment;
    private gfxColorAttachmentLevel;
    private gfxColorResolveTo;
    private gfxColorResolveToLevel;
    private gfxDepthStencilAttachment;
    private gfxDepthStencilResolveTo;
    constructor(device: Device_WebGPU);
    private getEncoder;
    private getTextureView;
    private setRenderPassDescriptor;
    beginRenderPass(commandEncoder: GPUCommandEncoder, renderPassDescriptor: RenderPassDescriptor): void;
    private flipY;
    setViewport(x: number, y: number, w: number, h: number, minDepth?: number, maxDepth?: number): void;
    setScissorRect(x: number, y: number, w: number, h: number): void;
    setPipeline(pipeline_: RenderPipeline): void;
    setVertexInput(inputLayout_: InputLayout | null, vertexBuffers: (VertexBufferDescriptor | null)[] | null, indexBuffer: IndexBufferDescriptor | null): void;
    setBindings(bindings_: Bindings): void;
    setStencilReference(ref: number): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpurendercommandsmixin-draw
     */
    draw(vertexCount: number, instanceCount?: number, firstVertex?: number, firstInstance?: number): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpurendercommandsmixin-drawindexed
     */
    drawIndexed(indexCount: number, instanceCount?: number, firstIndex?: number, baseVertex?: number, firstInstance?: number): void;
    /**
     * @see https://www.w3.org/TR/webgpu/#dom-gpurendercommandsmixin-drawindirect
     */
    drawIndirect(indirectBuffer: Buffer, indirectOffset: number): void;
    drawIndexedIndirect(indirectBuffer: Buffer, indirectOffset: number): void;
    beginOcclusionQuery(queryIndex: number): void;
    endOcclusionQuery(): void;
    pushDebugGroup(name: string): void;
    popDebugGroup(): void;
    insertDebugMarker(markerLabel: string): void;
    beginBundle(renderBundle: RenderBundle): void;
    endBundle(): void;
    executeBundles(renderBundles: RenderBundle[]): void;
    finish(): void;
    private copyAttachment;
}
