declare class Vector {
    arr: number[];
    constructor(arr: any);
    getArr(): number[];
    add(otherVector: any): Vector;
    subtract(otherVector: any): Vector;
    avg(length: any): Vector;
    negate(): Vector;
    squareEuclideanDistance(otherVector: any): number;
    euclideanDistance(otherVector: any): number;
    normalize(): Vector;
    norm2(): number;
    dot(otherVector: any): number;
    equal(otherVector: any): boolean;
}
export default Vector;
