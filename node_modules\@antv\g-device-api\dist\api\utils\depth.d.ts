import type { mat4 } from 'gl-matrix';
import { CompareFunction } from '../interfaces';
/**
 * @see https://forum.babylonjs.com/t/reverse-depth-buffer-z-buffer/6905/2
 */
export declare const IsDepthReversed = true;
export declare function reverseDepthForPerspectiveProjectionMatrix(m: mat4, isDepthReversed?: boolean): void;
export declare function reverseDepthForOrthographicProjectionMatrix(m: mat4, isDepthReversed?: boolean): void;
export declare function reverseDepthForCompareFunction(compareFunction: CompareFunction, isDepthReversed?: boolean): CompareFunction;
export declare function reverseDepthForClearValue(n: number, isDepthReversed?: boolean): number;
export declare function reverseDepthForDepthOffset(n: number, isDepthReversed?: boolean): number;
export declare function compareDepthValues(a: number, b: number, op: CompareFunction, isDepthReversed?: boolean): boolean;
