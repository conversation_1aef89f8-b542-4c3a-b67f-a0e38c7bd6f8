{"ast": null, "code": "import \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport request from '@/utils/request';\nexport default {\n  name: 'AccountTransferRecord',\n  data: function data() {\n    return {\n      loading: false,\n      total: 0,\n      tableData: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        username: '',\n        email: '',\n        fromAccountType: '',\n        toAccountType: '',\n        status: ''\n      },\n      detailDialogVisible: false,\n      detailRow: {}\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      request({\n        url: '/api/accountTransferRecord/list',\n        method: 'get',\n        params: this.queryParams\n      }).then(function (res) {\n        _this.tableData = res.records || [];\n        _this.total = res.total || 0;\n      })[\"finally\"](function () {\n        _this.loading = false;\n      });\n    },\n    handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery: function resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        username: '',\n        email: '',\n        fromAccountType: '',\n        toAccountType: '',\n        status: ''\n      };\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.queryParams.pageSize = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.getList();\n    },\n    showDetail: function showDetail(row) {\n      this.detailRow = row;\n      this.detailDialogVisible = true;\n    },\n    formatDateTime: function formatDateTime(val) {\n      if (!val) return '';\n      if (typeof val === 'string') return val.replace('T', ' ').slice(0, 19);\n      var d = new Date(val);\n      return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0') + ' ' + String(d.getHours()).padStart(2, '0') + ':' + String(d.getMinutes()).padStart(2, '0') + ':' + String(d.getSeconds()).padStart(2, '0');\n    },\n    accountTypeText: function accountTypeText(type) {\n      var map = {\n        fund: '资金账户',\n        commission: '佣金账户',\n        copy: '跟单账户',\n        profit: '利润账户'\n      };\n      return map[type] || type || '-';\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "loading", "total", "tableData", "queryParams", "pageNum", "pageSize", "username", "email", "fromAccountType", "toAccountType", "status", "detailDialogVisible", "detailRow", "created", "getList", "methods", "_this", "url", "method", "params", "then", "res", "records", "handleQuery", "reset<PERSON><PERSON>y", "handleSizeChange", "val", "handleCurrentChange", "showDetail", "row", "formatDateTime", "replace", "slice", "d", "Date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getSeconds", "accountTypeText", "type", "map", "fund", "commission", "copy", "profit"], "sources": ["src/views/exchange/transfer/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 筛选区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"8\" type=\"flex\" align=\"middle\">\r\n          <el-col :span=\"4\">\r\n            <el-input v-model.trim=\"queryParams.username\" placeholder=\"用户名\" clearable class=\"filter-item\" />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-input v-model.trim=\"queryParams.email\" placeholder=\"邮箱\" clearable class=\"filter-item\" />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-select v-model=\"queryParams.fromAccountType\" placeholder=\"转出账户类型\" clearable class=\"filter-item\">\r\n              <el-option label=\"资金账户\" value=\"fund\" />\r\n              <el-option label=\"佣金账户\" value=\"commission\" />\r\n              <el-option label=\"跟单账户\" value=\"copy\" />\r\n              <el-option label=\"利润账户\" value=\"profit\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-select v-model=\"queryParams.toAccountType\" placeholder=\"转入账户类型\" clearable class=\"filter-item\">\r\n              <el-option label=\"资金账户\" value=\"fund\" />\r\n              <el-option label=\"佣金账户\" value=\"commission\" />\r\n              <el-option label=\"跟单账户\" value=\"copy\" />\r\n              <el-option label=\"利润账户\" value=\"profit\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"状态\" clearable class=\"filter-item\">\r\n              <el-option label=\"处理中\" :value=\"0\" />\r\n              <el-option label=\"成功\" :value=\"1\" />\r\n              <el-option label=\"失败\" :value=\"2\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"4\" style=\"display: flex; gap: 8px;\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n      <!-- 数据表格 -->\r\n      <el-table :data=\"tableData\" v-loading=\"loading\" border style=\"width: 100%; margin-top: 16px;\">\r\n        <el-table-column type=\"index\" label=\"序号\" align=\"center\" width=\"60\" />\r\n        <el-table-column prop=\"username\" label=\"用户名\" align=\"center\" min-width=\"100\" />\r\n        <el-table-column prop=\"userId\" label=\"用户ID\" align=\"center\" min-width=\"80\" />\r\n        <el-table-column prop=\"fromAccountType\" label=\"转出账户类型\" align=\"center\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ accountTypeText(scope.row.fromAccountType) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"toAccountType\" label=\"转入账户类型\" align=\"center\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ accountTypeText(scope.row.toAccountType) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"amount\" label=\"划转金额(USDT)\" align=\"center\" min-width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.amount }} \r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" align=\"center\" min-width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.status === 0\" type=\"warning\">处理中</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status === 1\" type=\"success\">成功</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status === 2\" type=\"danger\">失败</el-tag>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"createTime\" label=\"创建时间\" align=\"center\" min-width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.createTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"80\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"mini\" @click=\"showDetail(scope.row)\">详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"queryParams.pageNum\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"queryParams.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n      <!-- 详情弹窗 -->\r\n      <el-dialog :visible.sync=\"detailDialogVisible\" title=\"划转详情\" width=\"800px\">\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"用户名\">{{ detailRow.username }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户ID\">{{ detailRow.userId }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"转出账户类型\">{{ accountTypeText(detailRow.fromAccountType) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"转入账户类型\">{{ accountTypeText(detailRow.toAccountType) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"划转金额\">{{ detailRow.amount }} USDT</el-descriptions-item>\r\n          <el-descriptions-item label=\"转出前余额\">{{ detailRow.fromBalanceBefore }} USDT</el-descriptions-item>\r\n          <el-descriptions-item label=\"转出后余额\">{{ detailRow.fromBalanceAfter }} USDT</el-descriptions-item>\r\n          <el-descriptions-item label=\"转入前余额\">{{ detailRow.toBalanceBefore }} USDT</el-descriptions-item>\r\n          <el-descriptions-item label=\"转入后余额\">{{ detailRow.toBalanceAfter }} USDT</el-descriptions-item>\r\n          <el-descriptions-item label=\"状态\">\r\n            <el-tag v-if=\"detailRow.status === 0\" type=\"warning\">处理中</el-tag>\r\n            <el-tag v-else-if=\"detailRow.status === 1\" type=\"success\">成功</el-tag>\r\n            <el-tag v-else-if=\"detailRow.status === 2\" type=\"danger\">失败</el-tag>\r\n            <span v-else>-</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\">{{ formatDateTime(detailRow.createTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"更新时间\">{{ formatDateTime(detailRow.updateTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"备注\" :span=\"2\">{{ detailRow.remark }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request'\r\nexport default {\r\n  name: 'AccountTransferRecord',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      total: 0,\r\n      tableData: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        username: '',\r\n        email: '',\r\n        fromAccountType: '',\r\n        toAccountType: '',\r\n        status: ''\r\n      },\r\n      detailDialogVisible: false,\r\n      detailRow: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true\r\n      request({\r\n        url: '/api/accountTransferRecord/list',\r\n        method: 'get',\r\n        params: this.queryParams\r\n      }).then(res => {\r\n        this.tableData = res.records || []\r\n        this.total = res.total || 0\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        username: '',\r\n        email: '',\r\n        fromAccountType: '',\r\n        toAccountType: '',\r\n        status: ''\r\n      }\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.queryParams.pageSize = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.queryParams.pageNum = val\r\n      this.getList()\r\n    },\r\n    showDetail(row) {\r\n      this.detailRow = row\r\n      this.detailDialogVisible = true\r\n    },\r\n    formatDateTime(val) {\r\n      if (!val) return ''\r\n      if (typeof val === 'string') return val.replace('T', ' ').slice(0, 19)\r\n      const d = new Date(val)\r\n      return d.getFullYear() + '-' + String(d.getMonth()+1).padStart(2,'0') + '-' + String(d.getDate()).padStart(2,'0') + ' ' + String(d.getHours()).padStart(2,'0') + ':' + String(d.getMinutes()).padStart(2,'0') + ':' + String(d.getSeconds()).padStart(2,'0')\r\n    },\r\n    accountTypeText(type) {\r\n      const map = { fund: '资金账户', commission: '佣金账户', copy: '跟单账户', profit: '利润账户' }\r\n      return map[type] || type || '-'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n}\r\n.filter-item {\r\n  width: 100%;\r\n}\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n</style> "], "mappings": ";;;;;;AAyHA,OAAAA,OAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,KAAA;QACAC,eAAA;QACAC,aAAA;QACAC,MAAA;MACA;MACAC,mBAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAhB,OAAA;MACAH,OAAA;QACAoB,GAAA;QACAC,MAAA;QACAC,MAAA,OAAAhB;MACA,GAAAiB,IAAA,WAAAC,GAAA;QACAL,KAAA,CAAAd,SAAA,GAAAmB,GAAA,CAAAC,OAAA;QACAN,KAAA,CAAAf,KAAA,GAAAoB,GAAA,CAAApB,KAAA;MACA;QACAe,KAAA,CAAAhB,OAAA;MACA;IACA;IACAuB,WAAA,WAAAA,YAAA;MACA,KAAApB,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACAU,UAAA,WAAAA,WAAA;MACA,KAAArB,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,KAAA;QACAC,eAAA;QACAC,aAAA;QACAC,MAAA;MACA;MACA,KAAAI,OAAA;IACA;IACAW,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAvB,WAAA,CAAAE,QAAA,GAAAqB,GAAA;MACA,KAAAZ,OAAA;IACA;IACAa,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAvB,WAAA,CAAAC,OAAA,GAAAsB,GAAA;MACA,KAAAZ,OAAA;IACA;IACAc,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAjB,SAAA,GAAAiB,GAAA;MACA,KAAAlB,mBAAA;IACA;IACAmB,cAAA,WAAAA,eAAAJ,GAAA;MACA,KAAAA,GAAA;MACA,WAAAA,GAAA,sBAAAA,GAAA,CAAAK,OAAA,WAAAC,KAAA;MACA,IAAAC,CAAA,OAAAC,IAAA,CAAAR,GAAA;MACA,OAAAO,CAAA,CAAAE,WAAA,WAAAC,MAAA,CAAAH,CAAA,CAAAI,QAAA,QAAAC,QAAA,iBAAAF,MAAA,CAAAH,CAAA,CAAAM,OAAA,IAAAD,QAAA,iBAAAF,MAAA,CAAAH,CAAA,CAAAO,QAAA,IAAAF,QAAA,iBAAAF,MAAA,CAAAH,CAAA,CAAAQ,UAAA,IAAAH,QAAA,iBAAAF,MAAA,CAAAH,CAAA,CAAAS,UAAA,IAAAJ,QAAA;IACA;IACAK,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;QAAAC,UAAA;QAAAC,IAAA;QAAAC,MAAA;MAAA;MACA,OAAAJ,GAAA,CAAAD,IAAA,KAAAA,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}