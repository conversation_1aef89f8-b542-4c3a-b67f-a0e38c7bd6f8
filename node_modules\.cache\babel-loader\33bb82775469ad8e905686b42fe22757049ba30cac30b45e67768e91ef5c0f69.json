{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.string.trim.js\";\nvar render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"app-container\"\n  }, [_c(\"el-card\", {\n    staticClass: \"box-card\"\n  }, [_c(\"el-tabs\", {\n    staticClass: \"deal-tabs\",\n    on: {\n      \"tab-click\": _vm.handleTabClick\n    },\n    model: {\n      value: _vm.activeTab,\n      callback: function callback($$v) {\n        _vm.activeTab = $$v;\n      },\n      expression: \"activeTab\"\n    }\n  }, [_c(\"el-tab-pane\", {\n    attrs: {\n      label: \"带单管理\",\n      name: \"leader\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"tab-content\"\n  }, [_c(\"div\", {\n    staticStyle: {\n      display: \"flex\",\n      \"justify-content\": \"flex-end\",\n      \"align-items\": \"center\",\n      \"margin-bottom\": \"10px\"\n    }\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: function click($event) {\n        _vm.showConfigDialog = true;\n      }\n    }\n  }, [_vm._v(\"带单配置\")])], 1), _c(\"el-table\", {\n    directives: [{\n      name: \"loading\",\n      rawName: \"v-loading\",\n      value: _vm.leaderLoading,\n      expression: \"leaderLoading\"\n    }],\n    staticStyle: {\n      width: \"100%\",\n      \"margin-top\": \"0\"\n    },\n    attrs: {\n      data: _vm.leaderList,\n      border: \"\"\n    }\n  }, [_c(\"el-table-column\", {\n    attrs: {\n      type: \"index\",\n      label: \"序号\",\n      align: \"center\",\n      width: \"60\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"userNickname\",\n      label: \"昵称\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"userAvatar\",\n      label: \"头像\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [scope.row.userAvatar ? _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: scope.row.userAvatar\n          },\n          on: {\n            error: function error(e) {\n              return e.target.src = require(\"@/assets/default.png\");\n            }\n          }\n        }) : _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: require(\"@/assets/default.png\")\n          }\n        })];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"symbol\",\n      label: \"交易对\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"status\",\n      label: \"状态\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getStatusText(scope.row.status)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"startTime\",\n      label: \"跟单时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.startTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"endTime\",\n      label: \"跟单结束时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.endTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"currentPrice\",\n      label: \"开仓价\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"closePrice\",\n      label: \"平仓价\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"positionStatus\",\n      label: \"持仓状态\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getPositionStatusText(scope.row.positionStatus)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"positionAmount\",\n      label: \"持仓数量\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"positionProfit\",\n      label: \"持仓盈亏\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"leverType\",\n      label: \"杠杆类型\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getLeverTypeText(scope.row.leverType)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"winOrLose\",\n      label: \"做多/做空\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getWinOrLoseText(scope.row.winOrLose)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"copyType\",\n      label: \"带单类型\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getCopyTypeText(scope.row.copyType)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"totalProfit\",\n      label: \"历史累计收益\",\n      align: \"center\",\n      \"min-width\": \"150\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"currentProfit\",\n      label: \"本次带单总收益\",\n      align: \"center\",\n      \"min-width\": \"150\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"profitRate\",\n      label: \"本次带单收益率\",\n      align: \"center\",\n      \"min-width\": \"150\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"winRate\",\n      label: \"胜率\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followerCount\",\n      label: \"累计跟单人数\",\n      align: \"center\",\n      \"min-width\": \"150\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"marginBalance\",\n      label: \"保证金余额\",\n      align: \"center\",\n      \"min-width\": \"150\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"takeProfit\",\n      label: \"止盈\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"stopLoss\",\n      label: \"止损\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"isStopProfit\",\n      label: \"是否止盈止损\",\n      align: \"center\",\n      \"min-width\": \"130\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getYesNoText(scope.row.isStopProfit)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"periodNo\",\n      label: \"期号\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"positionTime\",\n      label: \"持仓时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.positionTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"positionEndTime\",\n      label: \"持仓结束时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.positionEndTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"createTime\",\n      label: \"创建时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.createTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"updateTime\",\n      label: \"更新时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.updateTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"isEnabled\",\n      label: \"启用\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-tag\", {\n          attrs: {\n            type: scope.row.isEnabled === 1 ? \"success\" : \"info\"\n          }\n        }, [_vm._v(\" \" + _vm._s(_vm.getEnabledText(scope.row.isEnabled)) + \" \")])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      label: \"操作\",\n      align: \"center\",\n      width: \"80\",\n      fixed: \"right\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-button\", {\n          attrs: {\n            type: \"text\",\n            size: \"mini\"\n          },\n          on: {\n            click: function click($event) {\n              return _vm.showLeaderDetail(scope.row);\n            }\n          }\n        }, [_vm._v(\"详情\")])];\n      }\n    }])\n  })], 1), _c(\"div\", {\n    staticClass: \"pagination-container\"\n  }, [_c(\"el-pagination\", {\n    attrs: {\n      background: \"\",\n      \"current-page\": _vm.leaderQueryParams.pageNum,\n      \"page-sizes\": [10, 20, 30, 50],\n      \"page-size\": _vm.leaderQueryParams.pageSize,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      total: _vm.leaderTotal\n    },\n    on: {\n      \"size-change\": _vm.handleLeaderSizeChange,\n      \"current-change\": _vm.handleLeaderCurrentChange\n    }\n  })], 1), _c(\"el-dialog\", {\n    attrs: {\n      visible: _vm.leaderDetailDialogVisible,\n      title: \"带单人详情\",\n      width: \"800px\"\n    },\n    on: {\n      \"update:visible\": function updateVisible($event) {\n        _vm.leaderDetailDialogVisible = $event;\n      }\n    }\n  }, [_c(\"el-descriptions\", {\n    attrs: {\n      column: 2,\n      border: \"\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"昵称\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.userNickname))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"头像\"\n    }\n  }, [_vm.leaderDetailRow.userAvatar ? [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: _vm.leaderDetailRow.userAvatar\n    },\n    on: {\n      error: function error(e) {\n        return e.target.src = require(\"@/assets/default.png\");\n      }\n    }\n  })] : [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: require(\"@/assets/default.png\")\n    }\n  })]], 2), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"交易对\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.symbol))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"开仓价\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.currentPrice))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"平仓价\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.closePrice))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓数量\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.positionAmount))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓盈亏\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.positionProfit))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓状态\"\n    }\n  }, [_vm._v(_vm._s(_vm.getPositionStatusText(_vm.leaderDetailRow.positionStatus)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.leaderDetailRow.positionTime)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓结束时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.leaderDetailRow.positionEndTime)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"期号\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.periodNo))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"跟单开始时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.leaderDetailRow.startTime)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"跟单结束时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.leaderDetailRow.endTime)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"状态\"\n    }\n  }, [_vm._v(_vm._s(_vm.getStatusText(_vm.leaderDetailRow.status)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"历史累计收益\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.totalProfit))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"胜率\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.winRate))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"累计跟单人数\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.followerCount))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"本次带单总收益\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.currentProfit))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"本次带单收益率\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.profitRate))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"保证金余额\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.marginBalance))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"杠杆类型\"\n    }\n  }, [_vm._v(_vm._s(_vm.getLeverTypeText(_vm.leaderDetailRow.leverType)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"做多/做空\"\n    }\n  }, [_vm._v(_vm._s(_vm.getWinOrLoseText(_vm.leaderDetailRow.winOrLose)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"带单类型\"\n    }\n  }, [_vm._v(_vm._s(_vm.getCopyTypeText(_vm.leaderDetailRow.copyType)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"止盈\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.takeProfit))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"止损\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.stopLoss))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"是否止盈止损\"\n    }\n  }, [_vm._v(_vm._s(_vm.getYesNoText(_vm.leaderDetailRow.isStopProfit)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"策略说明\"\n    }\n  }, [_vm._v(_vm._s(_vm.leaderDetailRow.remark))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"创建时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.leaderDetailRow.createTime)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"更新时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.leaderDetailRow.updateTime)))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"启用\"\n    }\n  }, [_vm._v(_vm._s(_vm.getEnabledText(_vm.leaderDetailRow.isEnabled)))])], 1)], 1)], 1)]), _c(\"el-tab-pane\", {\n    attrs: {\n      label: \"跟单管理\",\n      name: \"follow\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"tab-content\"\n  }, [_c(\"div\", {\n    staticClass: \"filter-container\"\n  }, [_c(\"el-row\", {\n    staticClass: \"filter-row\",\n    attrs: {\n      gutter: 8,\n      type: \"flex\",\n      align: \"middle\"\n    }\n  }, [_c(\"el-col\", {\n    attrs: {\n      span: 3\n    }\n  }, [_c(\"el-input\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"用户名\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.detailQueryParams.followerUsername,\n      callback: function callback($$v) {\n        _vm.$set(_vm.detailQueryParams, \"followerUsername\", typeof $$v === \"string\" ? $$v.trim() : $$v);\n      },\n      expression: \"detailQueryParams.followerUsername\"\n    }\n  })], 1), _c(\"el-col\", {\n    attrs: {\n      span: 3\n    }\n  }, [_c(\"el-input\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"UID\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.detailQueryParams.followerUid,\n      callback: function callback($$v) {\n        _vm.$set(_vm.detailQueryParams, \"followerUid\", typeof $$v === \"string\" ? $$v.trim() : $$v);\n      },\n      expression: \"detailQueryParams.followerUid\"\n    }\n  })], 1), _c(\"el-col\", {\n    attrs: {\n      span: 4\n    }\n  }, [_c(\"el-input\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"邮箱\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.detailQueryParams.followerEmail,\n      callback: function callback($$v) {\n        _vm.$set(_vm.detailQueryParams, \"followerEmail\", typeof $$v === \"string\" ? $$v.trim() : $$v);\n      },\n      expression: \"detailQueryParams.followerEmail\"\n    }\n  })], 1), _c(\"el-col\", {\n    attrs: {\n      span: 3\n    }\n  }, [_c(\"el-select\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"跟单状态\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.detailQueryParams.status,\n      callback: function callback($$v) {\n        _vm.$set(_vm.detailQueryParams, \"status\", $$v);\n      },\n      expression: \"detailQueryParams.status\"\n    }\n  }, [_c(\"el-option\", {\n    attrs: {\n      label: \"全部\",\n      value: \"\"\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"未开始\",\n      value: 0\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"准备中\",\n      value: 1\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"已开始\",\n      value: 2\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"结算中\",\n      value: 3\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"已结束\",\n      value: 4\n    }\n  })], 1)], 1), _c(\"el-col\", {\n    attrs: {\n      span: 3\n    }\n  }, [_c(\"el-select\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"是否一键跟单\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.detailQueryParams.isFollowing,\n      callback: function callback($$v) {\n        _vm.$set(_vm.detailQueryParams, \"isFollowing\", $$v);\n      },\n      expression: \"detailQueryParams.isFollowing\"\n    }\n  }, [_c(\"el-option\", {\n    attrs: {\n      label: \"全部\",\n      value: \"\"\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"否\",\n      value: 0\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"是\",\n      value: 1\n    }\n  })], 1)], 1), _c(\"el-col\", {\n    staticStyle: {\n      display: \"flex\",\n      gap: \"8px\"\n    },\n    attrs: {\n      span: 4\n    }\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      icon: \"el-icon-search\"\n    },\n    on: {\n      click: _vm.handleDetailQuery\n    }\n  }, [_vm._v(\"搜索\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"success\",\n      icon: \"el-icon-refresh\"\n    },\n    on: {\n      click: _vm.resetDetailQuery\n    }\n  }, [_vm._v(\"重置\")])], 1)], 1)], 1), _c(\"el-table\", {\n    directives: [{\n      name: \"loading\",\n      rawName: \"v-loading\",\n      value: _vm.detailLoading,\n      expression: \"detailLoading\"\n    }],\n    staticStyle: {\n      width: \"100%\",\n      \"margin-top\": \"16px\"\n    },\n    attrs: {\n      data: _vm.detailList,\n      border: \"\"\n    }\n  }, [_c(\"el-table-column\", {\n    attrs: {\n      type: \"index\",\n      label: \"序号\",\n      align: \"center\",\n      width: \"60\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followerUsername\",\n      label: \"跟单用户名\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followerEmail\",\n      label: \"跟单邮箱\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followerUserNo\",\n      label: \"跟单UID\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followerAvatar\",\n      label: \"跟单头像\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [scope.row.followerAvatar ? _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: scope.row.followerAvatar\n          },\n          on: {\n            error: function error(e) {\n              return e.target.src = require(\"@/assets/default.png\");\n            }\n          }\n        }) : _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: require(\"@/assets/default.png\")\n          }\n        })];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"leaderUsername\",\n      label: \"带单用户名\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"leaderEmail\",\n      label: \"带单邮箱\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"leaderUserNo\",\n      label: \"带单UID\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"leaderAvatar\",\n      label: \"带单头像\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [scope.row.leaderAvatar ? _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: scope.row.leaderAvatar\n          },\n          on: {\n            error: function error(e) {\n              return e.target.src = require(\"@/assets/default.png\");\n            }\n          }\n        }) : _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: require(\"@/assets/default.png\")\n          }\n        })];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"periodNo\",\n      label: \"期号\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followAmount\",\n      label: \"保证金额\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"status\",\n      label: \"跟单状态\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getLeaderStatusText(scope.row.status)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"isFollowing\",\n      label: \"是否一键跟单\",\n      align: \"center\",\n      \"min-width\": \"130\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-tag\", {\n          attrs: {\n            type: scope.row.isFollowing === 1 ? \"success\" : \"info\"\n          }\n        }, [_vm._v(\" \" + _vm._s(scope.row.isFollowing === 1 ? \"是\" : \"否\") + \" \")])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"resultStatus\",\n      label: \"结算结果\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-tag\", {\n          attrs: {\n            type: _vm.getHistoryResultType(scope.row.resultStatus)\n          }\n        }, [_vm._v(\" \" + _vm._s(_vm.getHistoryResultText(scope.row.resultStatus)) + \" \")])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"isReturned\",\n      label: \"是否返本\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-tag\", {\n          attrs: {\n            type: scope.row.isReturned === 1 ? \"success\" : \"info\"\n          }\n        }, [_vm._v(\" \" + _vm._s(scope.row.isReturned === 1 ? \"是\" : \"否\") + \" \")])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"isSettled\",\n      label: \"是否已结算\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-tag\", {\n          attrs: {\n            type: scope.row.isSettled === 1 ? \"success\" : \"info\"\n          }\n        }, [_vm._v(\" \" + _vm._s(scope.row.isSettled === 1 ? \"是\" : \"否\") + \" \")])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followTime\",\n      label: \"跟单时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.followTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"settleTime\",\n      label: \"结算时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.settleTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      label: \"操作\",\n      align: \"center\",\n      width: \"80\",\n      fixed: \"right\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-button\", {\n          attrs: {\n            type: \"text\",\n            size: \"mini\"\n          },\n          on: {\n            click: function click($event) {\n              return _vm.showDetailDetail(scope.row);\n            }\n          }\n        }, [_vm._v(\"详情\")])];\n      }\n    }])\n  })], 1), _c(\"div\", {\n    staticClass: \"pagination-container\"\n  }, [_c(\"el-pagination\", {\n    attrs: {\n      background: \"\",\n      \"current-page\": _vm.detailQueryParams.pageNum,\n      \"page-sizes\": [10, 20, 30, 50],\n      \"page-size\": _vm.detailQueryParams.pageSize,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      total: _vm.detailTotal\n    },\n    on: {\n      \"size-change\": _vm.handleDetailSizeChange,\n      \"current-change\": _vm.handleDetailCurrentChange\n    }\n  })], 1), _c(\"el-dialog\", {\n    attrs: {\n      visible: _vm.detailDetailDialogVisible,\n      title: \"跟单明细详情\",\n      width: \"900px\"\n    },\n    on: {\n      \"update:visible\": function updateVisible($event) {\n        _vm.detailDetailDialogVisible = $event;\n      }\n    }\n  }, [_c(\"el-descriptions\", {\n    attrs: {\n      column: 2,\n      border: \"\",\n      \"label-style\": \"width: 150px;\",\n      title: \"跟单人信息\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"用户名\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.followerUsername || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"邮箱\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.followerEmail || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"UID\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.followerUserNo || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"头像\"\n    }\n  }, [_vm.detailDetailRow.followerAvatar ? [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: _vm.detailDetailRow.followerAvatar\n    },\n    on: {\n      error: function error(e) {\n        return e.target.src = require(\"@/assets/default.png\");\n      }\n    }\n  })] : [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: require(\"@/assets/default.png\")\n    }\n  })]], 2)], 1), _c(\"el-descriptions\", {\n    staticStyle: {\n      \"margin-top\": \"16px\"\n    },\n    attrs: {\n      column: 2,\n      border: \"\",\n      \"label-style\": \"width: 150px;\",\n      title: \"带单人信息\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"用户名\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.leaderUsername || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"邮箱\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.leaderEmail || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"UID\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.leaderUserNo || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"头像\"\n    }\n  }, [_vm.detailDetailRow.leaderAvatar ? [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: _vm.detailDetailRow.leaderAvatar\n    },\n    on: {\n      error: function error(e) {\n        return e.target.src = require(\"@/assets/default.png\");\n      }\n    }\n  })] : [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: require(\"@/assets/default.png\")\n    }\n  })]], 2)], 1), _c(\"el-descriptions\", {\n    staticStyle: {\n      \"margin-top\": \"16px\"\n    },\n    attrs: {\n      column: 2,\n      border: \"\",\n      \"label-style\": \"width: 150px;\",\n      title: \"业务信息\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"期号\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.periodNo || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"保证金额\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.followAmount || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"跟单状态\"\n    }\n  }, [_vm._v(_vm._s(_vm.getLeaderStatusText(_vm.detailDetailRow.status) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"是否一键跟单\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.isFollowing === 1 ? \"是\" : _vm.detailDetailRow.isFollowing === 0 ? \"否\" : \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"结算结果\"\n    }\n  }, [_vm._v(_vm._s(_vm.getHistoryResultText(_vm.detailDetailRow.resultStatus) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"是否返本\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.isReturned === 1 ? \"是\" : _vm.detailDetailRow.isReturned === 0 ? \"否\" : \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"是否已结算\"\n    }\n  }, [_vm._v(_vm._s(_vm.detailDetailRow.isSettled === 1 ? \"是\" : _vm.detailDetailRow.isSettled === 0 ? \"否\" : \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"跟单时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.detailDetailRow.followTime) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"结算时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.detailDetailRow.settleTime) || \"-\"))])], 1)], 1)], 1)]), _c(\"el-tab-pane\", {\n    attrs: {\n      label: \"跟单明细\",\n      name: \"history\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"tab-content\"\n  }, [_c(\"div\", {\n    staticClass: \"filter-container\"\n  }, [_c(\"el-row\", {\n    staticClass: \"filter-row\",\n    attrs: {\n      gutter: 8,\n      type: \"flex\",\n      align: \"middle\"\n    }\n  }, [_c(\"el-col\", {\n    attrs: {\n      span: 4\n    }\n  }, [_c(\"el-input\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"跟单人用户名\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.historyQueryParams.followerUsername,\n      callback: function callback($$v) {\n        _vm.$set(_vm.historyQueryParams, \"followerUsername\", typeof $$v === \"string\" ? $$v.trim() : $$v);\n      },\n      expression: \"historyQueryParams.followerUsername\"\n    }\n  })], 1), _c(\"el-col\", {\n    attrs: {\n      span: 4\n    }\n  }, [_c(\"el-input\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"跟单人邮箱\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.historyQueryParams.followerEmail,\n      callback: function callback($$v) {\n        _vm.$set(_vm.historyQueryParams, \"followerEmail\", typeof $$v === \"string\" ? $$v.trim() : $$v);\n      },\n      expression: \"historyQueryParams.followerEmail\"\n    }\n  })], 1), _c(\"el-col\", {\n    attrs: {\n      span: 4\n    }\n  }, [_c(\"el-select\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"是否返本\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.historyQueryParams.isReturned,\n      callback: function callback($$v) {\n        _vm.$set(_vm.historyQueryParams, \"isReturned\", $$v);\n      },\n      expression: \"historyQueryParams.isReturned\"\n    }\n  }, [_c(\"el-option\", {\n    attrs: {\n      label: \"全部\",\n      value: \"\"\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"否\",\n      value: 0\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"是\",\n      value: 1\n    }\n  })], 1)], 1), _c(\"el-col\", {\n    attrs: {\n      span: 4\n    }\n  }, [_c(\"el-select\", {\n    staticClass: \"filter-item\",\n    attrs: {\n      placeholder: \"结算结果\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.historyQueryParams.resultStatus,\n      callback: function callback($$v) {\n        _vm.$set(_vm.historyQueryParams, \"resultStatus\", $$v);\n      },\n      expression: \"historyQueryParams.resultStatus\"\n    }\n  }, [_c(\"el-option\", {\n    attrs: {\n      label: \"全部\",\n      value: \"\"\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"未结算\",\n      value: 0\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"盈利\",\n      value: 1\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"亏损\",\n      value: 2\n    }\n  })], 1)], 1), _c(\"el-col\", {\n    staticStyle: {\n      display: \"flex\",\n      gap: \"8px\"\n    },\n    attrs: {\n      span: 4\n    }\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      icon: \"el-icon-search\"\n    },\n    on: {\n      click: _vm.handleHistoryQuery\n    }\n  }, [_vm._v(\"搜索\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"success\",\n      icon: \"el-icon-refresh\"\n    },\n    on: {\n      click: _vm.resetHistoryQuery\n    }\n  }, [_vm._v(\"重置\")])], 1)], 1)], 1), _c(\"el-table\", {\n    directives: [{\n      name: \"loading\",\n      rawName: \"v-loading\",\n      value: _vm.historyLoading,\n      expression: \"historyLoading\"\n    }],\n    staticStyle: {\n      width: \"100%\",\n      \"margin-top\": \"16px\"\n    },\n    attrs: {\n      data: _vm.historyList,\n      border: \"\"\n    }\n  }, [_c(\"el-table-column\", {\n    attrs: {\n      type: \"index\",\n      label: \"序号\",\n      align: \"center\",\n      width: \"60\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"periodNo\",\n      label: \"期号\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followerNickname\",\n      label: \"跟单人昵称\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followerAvatar\",\n      label: \"跟单人头像\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [scope.row.followerAvatar ? _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: scope.row.followerAvatar\n          },\n          on: {\n            error: function error(e) {\n              return e.target.src = require(\"@/assets/default.png\");\n            }\n          }\n        }) : _c(\"img\", {\n          staticStyle: {\n            width: \"40px\",\n            height: \"40px\",\n            \"border-radius\": \"50%\"\n          },\n          attrs: {\n            src: require(\"@/assets/default.png\")\n          }\n        })];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"symbol\",\n      label: \"交易对\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followAmount\",\n      label: \"跟单金额\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"positionCount\",\n      label: \"持仓个数\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"profit\",\n      label: \"盈亏\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"profitRate\",\n      label: \"收益率%\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"leverType\",\n      label: \"杠杆类型\",\n      align: \"center\",\n      \"min-width\": \"80\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getLeverTypeText(scope.row.leverType)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"winOrLose\",\n      label: \"做多/做空\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getWinOrLoseText(scope.row.winOrLose)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"copyType\",\n      label: \"带单类型\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getCopyTypeText(scope.row.copyType)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"takeProfit\",\n      label: \"止盈\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"stopLoss\",\n      label: \"止损\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"isStopProfit\",\n      label: \"是否止盈止损\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.getYesNoText(scope.row.isStopProfit)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"resultStatus\",\n      label: \"结算结果\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-tag\", {\n          attrs: {\n            type: _vm.getHistoryResultType(scope.row.resultStatus)\n          }\n        }, [_vm._v(\" \" + _vm._s(_vm.getHistoryResultText(scope.row.resultStatus)) + \" \")])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"isReturned\",\n      label: \"是否返本\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-tag\", {\n          attrs: {\n            type: scope.row.isReturned === 1 ? \"success\" : \"info\"\n          }\n        }, [_vm._v(\" \" + _vm._s(scope.row.isReturned === 1 ? \"是\" : \"否\") + \" \")])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"followTime\",\n      label: \"跟单时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.followTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"settleTime\",\n      label: \"结算时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.settleTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"remark\",\n      label: \"备注\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"createTime\",\n      label: \"创建时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.createTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"updateTime\",\n      label: \"更新时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.updateTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      label: \"操作\",\n      align: \"center\",\n      width: \"80\",\n      fixed: \"right\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-button\", {\n          attrs: {\n            type: \"text\",\n            size: \"mini\"\n          },\n          on: {\n            click: function click($event) {\n              return _vm.showHistoryDetail(scope.row);\n            }\n          }\n        }, [_vm._v(\"详情\")])];\n      }\n    }])\n  })], 1), _c(\"div\", {\n    staticClass: \"pagination-container\"\n  }, [_c(\"el-pagination\", {\n    attrs: {\n      background: \"\",\n      \"current-page\": _vm.historyQueryParams.pageNum,\n      \"page-sizes\": [10, 20, 30, 50],\n      \"page-size\": _vm.historyQueryParams.pageSize,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      total: _vm.historyTotal\n    },\n    on: {\n      \"size-change\": _vm.handleHistorySizeChange,\n      \"current-change\": _vm.handleHistoryCurrentChange\n    }\n  })], 1), _c(\"el-dialog\", {\n    attrs: {\n      visible: _vm.historyDetailDialogVisible,\n      title: \"跟单明细详情\",\n      width: \"900px\"\n    },\n    on: {\n      \"update:visible\": function updateVisible($event) {\n        _vm.historyDetailDialogVisible = $event;\n      }\n    }\n  }, [_c(\"el-descriptions\", {\n    attrs: {\n      column: 2,\n      border: \"\",\n      \"label-style\": \"width: 150px;\",\n      title: \"跟单人信息\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"用户名\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.followerUsername || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"邮箱\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.followerEmail || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"UID\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.followerUserNo || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"头像\"\n    }\n  }, [_vm.historyDetailRow.followerAvatar ? [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: _vm.historyDetailRow.followerAvatar\n    },\n    on: {\n      error: function error(e) {\n        return e.target.src = require(\"@/assets/default.png\");\n      }\n    }\n  })] : [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: require(\"@/assets/default.png\")\n    }\n  })]], 2)], 1), _c(\"el-descriptions\", {\n    staticStyle: {\n      \"margin-top\": \"16px\"\n    },\n    attrs: {\n      column: 2,\n      border: \"\",\n      \"label-style\": \"width: 150px;\",\n      title: \"带单人信息\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"昵称\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.leaderNickname || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"头像\"\n    }\n  }, [_vm.historyDetailRow.leaderAvatar ? [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: _vm.historyDetailRow.leaderAvatar\n    },\n    on: {\n      error: function error(e) {\n        return e.target.src = require(\"@/assets/default.png\");\n      }\n    }\n  })] : [_c(\"img\", {\n    staticStyle: {\n      width: \"40px\",\n      height: \"40px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: require(\"@/assets/default.png\")\n    }\n  })]], 2)], 1), _c(\"el-descriptions\", {\n    staticStyle: {\n      \"margin-top\": \"16px\"\n    },\n    attrs: {\n      column: 2,\n      border: \"\",\n      \"label-style\": \"width: 150px;\",\n      title: \"业务信息\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"期号\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.periodNo || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"交易对\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.symbol || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"跟单金额\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.followAmount || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"开仓价\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.openPrice || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"平仓价\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.closePrice || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓个数\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.positionCount || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓状态\"\n    }\n  }, [_vm._v(_vm._s(_vm.getPositionStatusText(_vm.historyDetailRow.positionStatus) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.historyDetailRow.positionTime) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"持仓结束时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.historyDetailRow.positionEndTime) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"杠杆类型\"\n    }\n  }, [_vm._v(_vm._s(_vm.getLeverTypeText(_vm.historyDetailRow.leverType) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"做多/做空\"\n    }\n  }, [_vm._v(_vm._s(_vm.getWinOrLoseText(_vm.historyDetailRow.winOrLose) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"带单类型\"\n    }\n  }, [_vm._v(_vm._s(_vm.getCopyTypeText(_vm.historyDetailRow.copyType) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"止盈\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.takeProfit || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"止损\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.stopLoss || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"是否止盈止损\"\n    }\n  }, [_vm._v(_vm._s(_vm.getYesNoText(_vm.historyDetailRow.isStopProfit) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"盈亏\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.profit || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"收益率%\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.profitRate || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"是否返本\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.isReturned === 1 ? \"是\" : _vm.historyDetailRow.isReturned === 0 ? \"否\" : \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"备注\"\n    }\n  }, [_vm._v(_vm._s(_vm.historyDetailRow.remark || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"创建时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.historyDetailRow.createTime) || \"-\"))]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"更新时间\"\n    }\n  }, [_vm._v(_vm._s(_vm.formatDateTime(_vm.historyDetailRow.updateTime) || \"-\"))])], 1)], 1)], 1)])], 1), _c(\"el-dialog\", {\n    attrs: {\n      title: _vm.leaderTitle,\n      visible: _vm.leaderOpen,\n      width: \"600px\",\n      \"append-to-body\": \"\"\n    },\n    on: {\n      \"update:visible\": function updateVisible($event) {\n        _vm.leaderOpen = $event;\n      }\n    }\n  }, [_c(\"el-form\", {\n    ref: \"leaderForm\",\n    attrs: {\n      model: _vm.leaderForm,\n      rules: _vm.leaderRules,\n      \"label-width\": \"100px\"\n    }\n  }, [_c(\"el-form-item\", {\n    attrs: {\n      label: \"带单人昵称\",\n      prop: \"leaderNickname\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      placeholder: \"请输入带单人昵称\"\n    },\n    model: {\n      value: _vm.leaderForm.leaderNickname,\n      callback: function callback($$v) {\n        _vm.$set(_vm.leaderForm, \"leaderNickname\", $$v);\n      },\n      expression: \"leaderForm.leaderNickname\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"交易对\",\n      prop: \"symbol\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      placeholder: \"请输入交易对\"\n    },\n    model: {\n      value: _vm.leaderForm.symbol,\n      callback: function callback($$v) {\n        _vm.$set(_vm.leaderForm, \"symbol\", $$v);\n      },\n      expression: \"leaderForm.symbol\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"期号\",\n      prop: \"periodNo\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      placeholder: \"请输入期号\"\n    },\n    model: {\n      value: _vm.leaderForm.periodNo,\n      callback: function callback($$v) {\n        _vm.$set(_vm.leaderForm, \"periodNo\", $$v);\n      },\n      expression: \"leaderForm.periodNo\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"保证金\",\n      prop: \"marginBalance\"\n    }\n  }, [_c(\"el-input-number\", {\n    attrs: {\n      precision: 8,\n      step: 0.00000001,\n      min: 0\n    },\n    model: {\n      value: _vm.leaderForm.marginBalance,\n      callback: function callback($$v) {\n        _vm.$set(_vm.leaderForm, \"marginBalance\", $$v);\n      },\n      expression: \"leaderForm.marginBalance\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"策略说明\",\n      prop: \"remark\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      type: \"textarea\",\n      placeholder: \"请输入策略说明\"\n    },\n    model: {\n      value: _vm.leaderForm.remark,\n      callback: function callback($$v) {\n        _vm.$set(_vm.leaderForm, \"remark\", $$v);\n      },\n      expression: \"leaderForm.remark\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"状态\",\n      prop: \"status\"\n    }\n  }, [_c(\"el-radio-group\", {\n    model: {\n      value: _vm.leaderForm.status,\n      callback: function callback($$v) {\n        _vm.$set(_vm.leaderForm, \"status\", $$v);\n      },\n      expression: \"leaderForm.status\"\n    }\n  }, [_c(\"el-radio\", {\n    attrs: {\n      label: 0\n    }\n  }, [_vm._v(\"未开始\")]), _c(\"el-radio\", {\n    attrs: {\n      label: 1\n    }\n  }, [_vm._v(\"准备中\")]), _c(\"el-radio\", {\n    attrs: {\n      label: 2\n    }\n  }, [_vm._v(\"已开始\")]), _c(\"el-radio\", {\n    attrs: {\n      label: 3\n    }\n  }, [_vm._v(\"结算中\")]), _c(\"el-radio\", {\n    attrs: {\n      label: 4\n    }\n  }, [_vm._v(\"已结束\")])], 1)], 1)], 1), _c(\"div\", {\n    staticClass: \"dialog-footer\",\n    attrs: {\n      slot: \"footer\"\n    },\n    slot: \"footer\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.submitLeaderForm\n    }\n  }, [_vm._v(\"确 定\")]), _c(\"el-button\", {\n    on: {\n      click: function click($event) {\n        _vm.leaderOpen = false;\n      }\n    }\n  }, [_vm._v(\"取 消\")])], 1)], 1), _c(\"el-dialog\", {\n    attrs: {\n      visible: _vm.showConfigDialog,\n      title: \"带单配置管理\",\n      width: \"1100px\"\n    },\n    on: {\n      \"update:visible\": function updateVisible($event) {\n        _vm.showConfigDialog = $event;\n      }\n    }\n  }, [_c(\"el-table\", {\n    staticStyle: {\n      width: \"100%\"\n    },\n    attrs: {\n      data: _vm.configList,\n      border: \"\"\n    }\n  }, [_c(\"el-table-column\", {\n    attrs: {\n      prop: \"id\",\n      label: \"ID\",\n      align: \"center\",\n      width: \"60\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"name\",\n      label: \"名称\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"copyType\",\n      label: \"带单类型\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", [_vm._v(_vm._s(_vm.getCopyTypeText(scope.row.copyType)))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"leverType\",\n      label: \"杠杆类型\",\n      align: \"center\",\n      \"min-width\": \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", [_vm._v(_vm._s(_vm.getLeverTypeText(scope.row.leverType)))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"minFollowAmount\",\n      label: \"最低跟单金额\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"maxFollowAmount\",\n      label: \"最高跟单金额\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"minFollowCount\",\n      label: \"最低跟单人数\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"maxFollowCount\",\n      label: \"最高跟单人数\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"lockTime\",\n      label: \"锁仓时间(天)\",\n      align: \"center\",\n      \"min-width\": \"120\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"createTime\",\n      label: \"创建时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.createTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"updateTime\",\n      label: \"更新时间\",\n      align: \"center\",\n      \"min-width\": \"160\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_vm._v(\" \" + _vm._s(_vm.formatDateTime(scope.row.updateTime)) + \" \")];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      label: \"操作\",\n      align: \"center\",\n      width: \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-button\", {\n          attrs: {\n            type: \"primary\",\n            size: \"mini\"\n          },\n          on: {\n            click: function click($event) {\n              return _vm.editConfig(scope.row);\n            }\n          }\n        }, [_vm._v(\"修改\")])];\n      }\n    }])\n  })], 1), _c(\"span\", {\n    staticClass: \"dialog-footer\",\n    attrs: {\n      slot: \"footer\"\n    },\n    slot: \"footer\"\n  }, [_c(\"el-button\", {\n    on: {\n      click: function click($event) {\n        _vm.showConfigDialog = false;\n      }\n    }\n  }, [_vm._v(\"关闭\")])], 1)], 1), _c(\"el-dialog\", {\n    attrs: {\n      visible: _vm.editConfigDialogVisible,\n      title: \"编辑带单配置\",\n      width: \"600px\"\n    },\n    on: {\n      \"update:visible\": function updateVisible($event) {\n        _vm.editConfigDialogVisible = $event;\n      }\n    }\n  }, [_c(\"el-form\", {\n    ref: \"editConfigForm\",\n    attrs: {\n      model: _vm.editConfigForm,\n      rules: _vm.editConfigRules,\n      \"label-width\": \"120px\"\n    }\n  }, [_c(\"el-form-item\", {\n    attrs: {\n      label: \"名称\",\n      prop: \"name\"\n    }\n  }, [_c(\"el-input\", {\n    model: {\n      value: _vm.editConfigForm.name,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"name\", $$v);\n      },\n      expression: \"editConfigForm.name\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"带单类型\",\n      prop: \"copyType\"\n    }\n  }, [_c(\"el-select\", {\n    model: {\n      value: _vm.editConfigForm.copyType,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"copyType\", $$v);\n      },\n      expression: \"editConfigForm.copyType\"\n    }\n  }, [_c(\"el-option\", {\n    attrs: {\n      label: \"短线\",\n      value: 0\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"中线\",\n      value: 1\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"长线\",\n      value: 2\n    }\n  })], 1)], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"杠杆类型\",\n      prop: \"leverType\"\n    }\n  }, [_c(\"el-select\", {\n    model: {\n      value: _vm.editConfigForm.leverType,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"leverType\", $$v);\n      },\n      expression: \"editConfigForm.leverType\"\n    }\n  }, [_c(\"el-option\", {\n    attrs: {\n      label: \"x5\",\n      value: 0\n    }\n  }), _c(\"el-option\", {\n    attrs: {\n      label: \"x10\",\n      value: 1\n    }\n  })], 1)], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"最低跟单金额\",\n      prop: \"minFollowAmount\"\n    }\n  }, [_c(\"el-input-number\", {\n    attrs: {\n      min: 0\n    },\n    model: {\n      value: _vm.editConfigForm.minFollowAmount,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"minFollowAmount\", $$v);\n      },\n      expression: \"editConfigForm.minFollowAmount\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"最高跟单金额\",\n      prop: \"maxFollowAmount\"\n    }\n  }, [_c(\"el-input-number\", {\n    attrs: {\n      min: 0\n    },\n    model: {\n      value: _vm.editConfigForm.maxFollowAmount,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"maxFollowAmount\", $$v);\n      },\n      expression: \"editConfigForm.maxFollowAmount\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"最低跟单人数\",\n      prop: \"minFollowCount\"\n    }\n  }, [_c(\"el-input-number\", {\n    attrs: {\n      min: 0\n    },\n    model: {\n      value: _vm.editConfigForm.minFollowCount,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"minFollowCount\", $$v);\n      },\n      expression: \"editConfigForm.minFollowCount\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"最高跟单人数\",\n      prop: \"maxFollowCount\"\n    }\n  }, [_c(\"el-input-number\", {\n    attrs: {\n      min: 0\n    },\n    model: {\n      value: _vm.editConfigForm.maxFollowCount,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"maxFollowCount\", $$v);\n      },\n      expression: \"editConfigForm.maxFollowCount\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      label: \"锁仓时间(天)\",\n      prop: \"lockTime\"\n    }\n  }, [_c(\"el-input-number\", {\n    attrs: {\n      min: 0\n    },\n    model: {\n      value: _vm.editConfigForm.lockTime,\n      callback: function callback($$v) {\n        _vm.$set(_vm.editConfigForm, \"lockTime\", $$v);\n      },\n      expression: \"editConfigForm.lockTime\"\n    }\n  })], 1)], 1), _c(\"span\", {\n    staticClass: \"dialog-footer\",\n    attrs: {\n      slot: \"footer\"\n    },\n    slot: \"footer\"\n  }, [_c(\"el-button\", {\n    on: {\n      click: function click($event) {\n        _vm.editConfigDialogVisible = false;\n      }\n    }\n  }, [_vm._v(\"取消\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      loading: _vm.saveConfigLoading\n    },\n    on: {\n      click: _vm.saveEditConfig\n    }\n  }, [_vm._v(\"保存\")])], 1)], 1)], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "handleTabClick", "model", "value", "activeTab", "callback", "$$v", "expression", "attrs", "label", "name", "staticStyle", "display", "type", "click", "$event", "showConfigDialog", "_v", "directives", "rawName", "leader<PERSON><PERSON><PERSON>", "width", "data", "leaderList", "border", "align", "prop", "scopedSlots", "_u", "key", "fn", "scope", "row", "userAvatar", "height", "src", "error", "e", "target", "require", "_s", "getStatusText", "status", "formatDateTime", "startTime", "endTime", "getPositionStatusText", "positionStatus", "getLeverTypeText", "leverType", "getWinOrLoseText", "winOr<PERSON>ose", "getCopyTypeText", "copyType", "getYesNoText", "isStopProfit", "positionTime", "positionEndTime", "createTime", "updateTime", "isEnabled", "getEnabledText", "fixed", "size", "showLeaderDetail", "background", "leader<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageNum", "pageSize", "layout", "total", "leader<PERSON><PERSON><PERSON>", "handleLeaderSizeChange", "handleLeaderCurrentChange", "visible", "leaderDetailDialogVisible", "title", "updateVisible", "column", "leaderDetail<PERSON>ow", "userNickname", "symbol", "currentPrice", "closePrice", "positionAmount", "positionProfit", "periodNo", "totalProfit", "winRate", "followerCount", "currentProfit", "profitRate", "marginBalance", "takeProfit", "stopLoss", "remark", "gutter", "span", "placeholder", "clearable", "detailQueryParams", "followerUsername", "$set", "trim", "followerUid", "followerEmail", "isFollowing", "gap", "icon", "handleDetailQuery", "resetDetail<PERSON><PERSON>y", "detailLoading", "detailList", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "getLeaderStatusText", "getHistoryResultType", "resultStatus", "getHistoryResultText", "isReturned", "isSettled", "followTime", "settleTime", "showDetailDetail", "detailTotal", "handleDetailSizeChange", "handleDetailCurrentChange", "detailDetailDialogVisible", "detailDetailRow", "followerUserNo", "leaderUsername", "leaderEmail", "leaderUser<PERSON>o", "followAmount", "historyQueryParams", "handleHist<PERSON><PERSON><PERSON>y", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "historyLoading", "historyList", "showHistoryDetail", "historyTotal", "handleHistorySizeChange", "handleHistoryCurrentChange", "historyDetailDialogVisible", "historyDetailRow", "leader<PERSON><PERSON><PERSON>", "openPrice", "positionCount", "profit", "leader<PERSON><PERSON><PERSON>", "leader<PERSON><PERSON>", "ref", "leader<PERSON><PERSON>", "rules", "leader<PERSON><PERSON>", "precision", "step", "min", "slot", "submitLeaderForm", "configList", "editConfig", "editConfigDialogVisible", "editConfigForm", "editConfigRules", "minFollowAmount", "maxFollowAmount", "minFollowCount", "max<PERSON><PERSON><PERSON><PERSON>ount", "lockTime", "loading", "saveConfigLoading", "saveEditConfig", "staticRenderFns", "_withStripped"], "sources": ["E:/最新项目文件/交易所/adminweb/src/views/exchange/copy-trade/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"app-container\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-tabs\",\n            {\n              staticClass: \"deal-tabs\",\n              on: { \"tab-click\": _vm.handleTabClick },\n              model: {\n                value: _vm.activeTab,\n                callback: function ($$v) {\n                  _vm.activeTab = $$v\n                },\n                expression: \"activeTab\",\n              },\n            },\n            [\n              _c(\n                \"el-tab-pane\",\n                { attrs: { label: \"带单管理\", name: \"leader\" } },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"tab-content\" },\n                    [\n                      _c(\n                        \"div\",\n                        {\n                          staticStyle: {\n                            display: \"flex\",\n                            \"justify-content\": \"flex-end\",\n                            \"align-items\": \"center\",\n                            \"margin-bottom\": \"10px\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-button\",\n                            {\n                              attrs: { type: \"primary\" },\n                              on: {\n                                click: function ($event) {\n                                  _vm.showConfigDialog = true\n                                },\n                              },\n                            },\n                            [_vm._v(\"带单配置\")]\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-table\",\n                        {\n                          directives: [\n                            {\n                              name: \"loading\",\n                              rawName: \"v-loading\",\n                              value: _vm.leaderLoading,\n                              expression: \"leaderLoading\",\n                            },\n                          ],\n                          staticStyle: { width: \"100%\", \"margin-top\": \"0\" },\n                          attrs: { data: _vm.leaderList, border: \"\" },\n                        },\n                        [\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              type: \"index\",\n                              label: \"序号\",\n                              align: \"center\",\n                              width: \"60\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"userNickname\",\n                              label: \"昵称\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"userAvatar\",\n                              label: \"头像\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    scope.row.userAvatar\n                                      ? _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: { src: scope.row.userAvatar },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        })\n                                      : _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"symbol\",\n                              label: \"交易对\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"status\",\n                              label: \"状态\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getStatusText(scope.row.status)\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"startTime\",\n                              label: \"跟单时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.startTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"endTime\",\n                              label: \"跟单结束时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(scope.row.endTime)\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"currentPrice\",\n                              label: \"开仓价\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"closePrice\",\n                              label: \"平仓价\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"positionStatus\",\n                              label: \"持仓状态\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getPositionStatusText(\n                                            scope.row.positionStatus\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"positionAmount\",\n                              label: \"持仓数量\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"positionProfit\",\n                              label: \"持仓盈亏\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"leverType\",\n                              label: \"杠杆类型\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getLeverTypeText(\n                                            scope.row.leverType\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"winOrLose\",\n                              label: \"做多/做空\",\n                              align: \"center\",\n                              \"min-width\": \"120\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getWinOrLoseText(\n                                            scope.row.winOrLose\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"copyType\",\n                              label: \"带单类型\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getCopyTypeText(\n                                            scope.row.copyType\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"totalProfit\",\n                              label: \"历史累计收益\",\n                              align: \"center\",\n                              \"min-width\": \"150\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"currentProfit\",\n                              label: \"本次带单总收益\",\n                              align: \"center\",\n                              \"min-width\": \"150\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"profitRate\",\n                              label: \"本次带单收益率\",\n                              align: \"center\",\n                              \"min-width\": \"150\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"winRate\",\n                              label: \"胜率\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followerCount\",\n                              label: \"累计跟单人数\",\n                              align: \"center\",\n                              \"min-width\": \"150\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"marginBalance\",\n                              label: \"保证金余额\",\n                              align: \"center\",\n                              \"min-width\": \"150\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"takeProfit\",\n                              label: \"止盈\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"stopLoss\",\n                              label: \"止损\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"isStopProfit\",\n                              label: \"是否止盈止损\",\n                              align: \"center\",\n                              \"min-width\": \"130\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getYesNoText(\n                                            scope.row.isStopProfit\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"periodNo\",\n                              label: \"期号\",\n                              align: \"center\",\n                              \"min-width\": \"120\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"positionTime\",\n                              label: \"持仓时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.positionTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"positionEndTime\",\n                              label: \"持仓结束时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.positionEndTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"createTime\",\n                              label: \"创建时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.createTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"updateTime\",\n                              label: \"更新时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.updateTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"isEnabled\",\n                              label: \"启用\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-tag\",\n                                      {\n                                        attrs: {\n                                          type:\n                                            scope.row.isEnabled === 1\n                                              ? \"success\"\n                                              : \"info\",\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \" \" +\n                                            _vm._s(\n                                              _vm.getEnabledText(\n                                                scope.row.isEnabled\n                                              )\n                                            ) +\n                                            \" \"\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              label: \"操作\",\n                              align: \"center\",\n                              width: \"80\",\n                              fixed: \"right\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-button\",\n                                      {\n                                        attrs: { type: \"text\", size: \"mini\" },\n                                        on: {\n                                          click: function ($event) {\n                                            return _vm.showLeaderDetail(\n                                              scope.row\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"详情\")]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"pagination-container\" },\n                        [\n                          _c(\"el-pagination\", {\n                            attrs: {\n                              background: \"\",\n                              \"current-page\": _vm.leaderQueryParams.pageNum,\n                              \"page-sizes\": [10, 20, 30, 50],\n                              \"page-size\": _vm.leaderQueryParams.pageSize,\n                              layout: \"total, sizes, prev, pager, next, jumper\",\n                              total: _vm.leaderTotal,\n                            },\n                            on: {\n                              \"size-change\": _vm.handleLeaderSizeChange,\n                              \"current-change\": _vm.handleLeaderCurrentChange,\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-dialog\",\n                        {\n                          attrs: {\n                            visible: _vm.leaderDetailDialogVisible,\n                            title: \"带单人详情\",\n                            width: \"800px\",\n                          },\n                          on: {\n                            \"update:visible\": function ($event) {\n                              _vm.leaderDetailDialogVisible = $event\n                            },\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-descriptions\",\n                            { attrs: { column: 2, border: \"\" } },\n                            [\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"昵称\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.userNickname)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"头像\" } },\n                                [\n                                  _vm.leaderDetailRow.userAvatar\n                                    ? [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: _vm.leaderDetailRow.userAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        }),\n                                      ]\n                                    : [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                      ],\n                                ],\n                                2\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"交易对\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.symbol))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"开仓价\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.currentPrice)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"平仓价\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.closePrice))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓数量\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.positionAmount)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓盈亏\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.positionProfit)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓状态\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getPositionStatusText(\n                                        _vm.leaderDetailRow.positionStatus\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.leaderDetailRow.positionTime\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓结束时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.leaderDetailRow.positionEndTime\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"期号\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.periodNo))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"跟单开始时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.leaderDetailRow.startTime\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"跟单结束时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.leaderDetailRow.endTime\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"状态\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getStatusText(\n                                        _vm.leaderDetailRow.status\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"历史累计收益\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.totalProfit)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"胜率\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.winRate))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"累计跟单人数\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.followerCount)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"本次带单总收益\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.currentProfit)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"本次带单收益率\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.profitRate))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"保证金余额\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.leaderDetailRow.marginBalance)\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"杠杆类型\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getLeverTypeText(\n                                        _vm.leaderDetailRow.leverType\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"做多/做空\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getWinOrLoseText(\n                                        _vm.leaderDetailRow.winOrLose\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"带单类型\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getCopyTypeText(\n                                        _vm.leaderDetailRow.copyType\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"止盈\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.takeProfit))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"止损\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.stopLoss))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"是否止盈止损\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getYesNoText(\n                                        _vm.leaderDetailRow.isStopProfit\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"策略说明\" } },\n                                [_vm._v(_vm._s(_vm.leaderDetailRow.remark))]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"创建时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.leaderDetailRow.createTime\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"更新时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.leaderDetailRow.updateTime\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"启用\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getEnabledText(\n                                        _vm.leaderDetailRow.isEnabled\n                                      )\n                                    )\n                                  ),\n                                ]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ]\n              ),\n              _c(\n                \"el-tab-pane\",\n                { attrs: { label: \"跟单管理\", name: \"follow\" } },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"tab-content\" },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"filter-container\" },\n                        [\n                          _c(\n                            \"el-row\",\n                            {\n                              staticClass: \"filter-row\",\n                              attrs: {\n                                gutter: 8,\n                                type: \"flex\",\n                                align: \"middle\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 3 } },\n                                [\n                                  _c(\"el-input\", {\n                                    staticClass: \"filter-item\",\n                                    attrs: {\n                                      placeholder: \"用户名\",\n                                      clearable: \"\",\n                                    },\n                                    model: {\n                                      value:\n                                        _vm.detailQueryParams.followerUsername,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.detailQueryParams,\n                                          \"followerUsername\",\n                                          typeof $$v === \"string\"\n                                            ? $$v.trim()\n                                            : $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"detailQueryParams.followerUsername\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 3 } },\n                                [\n                                  _c(\"el-input\", {\n                                    staticClass: \"filter-item\",\n                                    attrs: {\n                                      placeholder: \"UID\",\n                                      clearable: \"\",\n                                    },\n                                    model: {\n                                      value: _vm.detailQueryParams.followerUid,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.detailQueryParams,\n                                          \"followerUid\",\n                                          typeof $$v === \"string\"\n                                            ? $$v.trim()\n                                            : $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"detailQueryParams.followerUid\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 4 } },\n                                [\n                                  _c(\"el-input\", {\n                                    staticClass: \"filter-item\",\n                                    attrs: {\n                                      placeholder: \"邮箱\",\n                                      clearable: \"\",\n                                    },\n                                    model: {\n                                      value:\n                                        _vm.detailQueryParams.followerEmail,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.detailQueryParams,\n                                          \"followerEmail\",\n                                          typeof $$v === \"string\"\n                                            ? $$v.trim()\n                                            : $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"detailQueryParams.followerEmail\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 3 } },\n                                [\n                                  _c(\n                                    \"el-select\",\n                                    {\n                                      staticClass: \"filter-item\",\n                                      attrs: {\n                                        placeholder: \"跟单状态\",\n                                        clearable: \"\",\n                                      },\n                                      model: {\n                                        value: _vm.detailQueryParams.status,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.detailQueryParams,\n                                            \"status\",\n                                            $$v\n                                          )\n                                        },\n                                        expression: \"detailQueryParams.status\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"全部\", value: \"\" },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"未开始\", value: 0 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"准备中\", value: 1 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"已开始\", value: 2 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"结算中\", value: 3 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"已结束\", value: 4 },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 3 } },\n                                [\n                                  _c(\n                                    \"el-select\",\n                                    {\n                                      staticClass: \"filter-item\",\n                                      attrs: {\n                                        placeholder: \"是否一键跟单\",\n                                        clearable: \"\",\n                                      },\n                                      model: {\n                                        value:\n                                          _vm.detailQueryParams.isFollowing,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.detailQueryParams,\n                                            \"isFollowing\",\n                                            $$v\n                                          )\n                                        },\n                                        expression:\n                                          \"detailQueryParams.isFollowing\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"全部\", value: \"\" },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"否\", value: 0 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"是\", value: 1 },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                {\n                                  staticStyle: { display: \"flex\", gap: \"8px\" },\n                                  attrs: { span: 4 },\n                                },\n                                [\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      attrs: {\n                                        type: \"primary\",\n                                        icon: \"el-icon-search\",\n                                      },\n                                      on: { click: _vm.handleDetailQuery },\n                                    },\n                                    [_vm._v(\"搜索\")]\n                                  ),\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      attrs: {\n                                        type: \"success\",\n                                        icon: \"el-icon-refresh\",\n                                      },\n                                      on: { click: _vm.resetDetailQuery },\n                                    },\n                                    [_vm._v(\"重置\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-table\",\n                        {\n                          directives: [\n                            {\n                              name: \"loading\",\n                              rawName: \"v-loading\",\n                              value: _vm.detailLoading,\n                              expression: \"detailLoading\",\n                            },\n                          ],\n                          staticStyle: { width: \"100%\", \"margin-top\": \"16px\" },\n                          attrs: { data: _vm.detailList, border: \"\" },\n                        },\n                        [\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              type: \"index\",\n                              label: \"序号\",\n                              align: \"center\",\n                              width: \"60\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followerUsername\",\n                              label: \"跟单用户名\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followerEmail\",\n                              label: \"跟单邮箱\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followerUserNo\",\n                              label: \"跟单UID\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followerAvatar\",\n                              label: \"跟单头像\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    scope.row.followerAvatar\n                                      ? _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: scope.row.followerAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        })\n                                      : _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"leaderUsername\",\n                              label: \"带单用户名\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"leaderEmail\",\n                              label: \"带单邮箱\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"leaderUserNo\",\n                              label: \"带单UID\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"leaderAvatar\",\n                              label: \"带单头像\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    scope.row.leaderAvatar\n                                      ? _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: scope.row.leaderAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        })\n                                      : _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"periodNo\",\n                              label: \"期号\",\n                              align: \"center\",\n                              \"min-width\": \"120\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followAmount\",\n                              label: \"保证金额\",\n                              align: \"center\",\n                              \"min-width\": \"120\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"status\",\n                              label: \"跟单状态\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getLeaderStatusText(\n                                            scope.row.status\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"isFollowing\",\n                              label: \"是否一键跟单\",\n                              align: \"center\",\n                              \"min-width\": \"130\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-tag\",\n                                      {\n                                        attrs: {\n                                          type:\n                                            scope.row.isFollowing === 1\n                                              ? \"success\"\n                                              : \"info\",\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \" \" +\n                                            _vm._s(\n                                              scope.row.isFollowing === 1\n                                                ? \"是\"\n                                                : \"否\"\n                                            ) +\n                                            \" \"\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"resultStatus\",\n                              label: \"结算结果\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-tag\",\n                                      {\n                                        attrs: {\n                                          type: _vm.getHistoryResultType(\n                                            scope.row.resultStatus\n                                          ),\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \" \" +\n                                            _vm._s(\n                                              _vm.getHistoryResultText(\n                                                scope.row.resultStatus\n                                              )\n                                            ) +\n                                            \" \"\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"isReturned\",\n                              label: \"是否返本\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-tag\",\n                                      {\n                                        attrs: {\n                                          type:\n                                            scope.row.isReturned === 1\n                                              ? \"success\"\n                                              : \"info\",\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \" \" +\n                                            _vm._s(\n                                              scope.row.isReturned === 1\n                                                ? \"是\"\n                                                : \"否\"\n                                            ) +\n                                            \" \"\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"isSettled\",\n                              label: \"是否已结算\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-tag\",\n                                      {\n                                        attrs: {\n                                          type:\n                                            scope.row.isSettled === 1\n                                              ? \"success\"\n                                              : \"info\",\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \" \" +\n                                            _vm._s(\n                                              scope.row.isSettled === 1\n                                                ? \"是\"\n                                                : \"否\"\n                                            ) +\n                                            \" \"\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followTime\",\n                              label: \"跟单时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.followTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"settleTime\",\n                              label: \"结算时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.settleTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              label: \"操作\",\n                              align: \"center\",\n                              width: \"80\",\n                              fixed: \"right\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-button\",\n                                      {\n                                        attrs: { type: \"text\", size: \"mini\" },\n                                        on: {\n                                          click: function ($event) {\n                                            return _vm.showDetailDetail(\n                                              scope.row\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"详情\")]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"pagination-container\" },\n                        [\n                          _c(\"el-pagination\", {\n                            attrs: {\n                              background: \"\",\n                              \"current-page\": _vm.detailQueryParams.pageNum,\n                              \"page-sizes\": [10, 20, 30, 50],\n                              \"page-size\": _vm.detailQueryParams.pageSize,\n                              layout: \"total, sizes, prev, pager, next, jumper\",\n                              total: _vm.detailTotal,\n                            },\n                            on: {\n                              \"size-change\": _vm.handleDetailSizeChange,\n                              \"current-change\": _vm.handleDetailCurrentChange,\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-dialog\",\n                        {\n                          attrs: {\n                            visible: _vm.detailDetailDialogVisible,\n                            title: \"跟单明细详情\",\n                            width: \"900px\",\n                          },\n                          on: {\n                            \"update:visible\": function ($event) {\n                              _vm.detailDetailDialogVisible = $event\n                            },\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-descriptions\",\n                            {\n                              attrs: {\n                                column: 2,\n                                border: \"\",\n                                \"label-style\": \"width: 150px;\",\n                                title: \"跟单人信息\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"用户名\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.followerUsername ||\n                                        \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"邮箱\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.followerEmail || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"UID\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.followerUserNo || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"头像\" } },\n                                [\n                                  _vm.detailDetailRow.followerAvatar\n                                    ? [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: _vm.detailDetailRow\n                                              .followerAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        }),\n                                      ]\n                                    : [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                      ],\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"el-descriptions\",\n                            {\n                              staticStyle: { \"margin-top\": \"16px\" },\n                              attrs: {\n                                column: 2,\n                                border: \"\",\n                                \"label-style\": \"width: 150px;\",\n                                title: \"带单人信息\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"用户名\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.leaderUsername || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"邮箱\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.leaderEmail || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"UID\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.leaderUserNo || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"头像\" } },\n                                [\n                                  _vm.detailDetailRow.leaderAvatar\n                                    ? [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: _vm.detailDetailRow\n                                              .leaderAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        }),\n                                      ]\n                                    : [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                      ],\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"el-descriptions\",\n                            {\n                              staticStyle: { \"margin-top\": \"16px\" },\n                              attrs: {\n                                column: 2,\n                                border: \"\",\n                                \"label-style\": \"width: 150px;\",\n                                title: \"业务信息\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"期号\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.detailDetailRow.periodNo || \"-\")\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"保证金额\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.followAmount || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"跟单状态\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getLeaderStatusText(\n                                        _vm.detailDetailRow.status\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"是否一键跟单\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.isFollowing === 1\n                                        ? \"是\"\n                                        : _vm.detailDetailRow.isFollowing === 0\n                                        ? \"否\"\n                                        : \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"结算结果\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getHistoryResultText(\n                                        _vm.detailDetailRow.resultStatus\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"是否返本\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.isReturned === 1\n                                        ? \"是\"\n                                        : _vm.detailDetailRow.isReturned === 0\n                                        ? \"否\"\n                                        : \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"是否已结算\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.detailDetailRow.isSettled === 1\n                                        ? \"是\"\n                                        : _vm.detailDetailRow.isSettled === 0\n                                        ? \"否\"\n                                        : \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"跟单时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.detailDetailRow.followTime\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"结算时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.detailDetailRow.settleTime\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ]\n              ),\n              _c(\n                \"el-tab-pane\",\n                { attrs: { label: \"跟单明细\", name: \"history\" } },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"tab-content\" },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"filter-container\" },\n                        [\n                          _c(\n                            \"el-row\",\n                            {\n                              staticClass: \"filter-row\",\n                              attrs: {\n                                gutter: 8,\n                                type: \"flex\",\n                                align: \"middle\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 4 } },\n                                [\n                                  _c(\"el-input\", {\n                                    staticClass: \"filter-item\",\n                                    attrs: {\n                                      placeholder: \"跟单人用户名\",\n                                      clearable: \"\",\n                                    },\n                                    model: {\n                                      value:\n                                        _vm.historyQueryParams.followerUsername,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.historyQueryParams,\n                                          \"followerUsername\",\n                                          typeof $$v === \"string\"\n                                            ? $$v.trim()\n                                            : $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"historyQueryParams.followerUsername\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 4 } },\n                                [\n                                  _c(\"el-input\", {\n                                    staticClass: \"filter-item\",\n                                    attrs: {\n                                      placeholder: \"跟单人邮箱\",\n                                      clearable: \"\",\n                                    },\n                                    model: {\n                                      value:\n                                        _vm.historyQueryParams.followerEmail,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.historyQueryParams,\n                                          \"followerEmail\",\n                                          typeof $$v === \"string\"\n                                            ? $$v.trim()\n                                            : $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"historyQueryParams.followerEmail\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 4 } },\n                                [\n                                  _c(\n                                    \"el-select\",\n                                    {\n                                      staticClass: \"filter-item\",\n                                      attrs: {\n                                        placeholder: \"是否返本\",\n                                        clearable: \"\",\n                                      },\n                                      model: {\n                                        value:\n                                          _vm.historyQueryParams.isReturned,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.historyQueryParams,\n                                            \"isReturned\",\n                                            $$v\n                                          )\n                                        },\n                                        expression:\n                                          \"historyQueryParams.isReturned\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"全部\", value: \"\" },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"否\", value: 0 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"是\", value: 1 },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 4 } },\n                                [\n                                  _c(\n                                    \"el-select\",\n                                    {\n                                      staticClass: \"filter-item\",\n                                      attrs: {\n                                        placeholder: \"结算结果\",\n                                        clearable: \"\",\n                                      },\n                                      model: {\n                                        value:\n                                          _vm.historyQueryParams.resultStatus,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.historyQueryParams,\n                                            \"resultStatus\",\n                                            $$v\n                                          )\n                                        },\n                                        expression:\n                                          \"historyQueryParams.resultStatus\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"全部\", value: \"\" },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"未结算\", value: 0 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"盈利\", value: 1 },\n                                      }),\n                                      _c(\"el-option\", {\n                                        attrs: { label: \"亏损\", value: 2 },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                {\n                                  staticStyle: { display: \"flex\", gap: \"8px\" },\n                                  attrs: { span: 4 },\n                                },\n                                [\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      attrs: {\n                                        type: \"primary\",\n                                        icon: \"el-icon-search\",\n                                      },\n                                      on: { click: _vm.handleHistoryQuery },\n                                    },\n                                    [_vm._v(\"搜索\")]\n                                  ),\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      attrs: {\n                                        type: \"success\",\n                                        icon: \"el-icon-refresh\",\n                                      },\n                                      on: { click: _vm.resetHistoryQuery },\n                                    },\n                                    [_vm._v(\"重置\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-table\",\n                        {\n                          directives: [\n                            {\n                              name: \"loading\",\n                              rawName: \"v-loading\",\n                              value: _vm.historyLoading,\n                              expression: \"historyLoading\",\n                            },\n                          ],\n                          staticStyle: { width: \"100%\", \"margin-top\": \"16px\" },\n                          attrs: { data: _vm.historyList, border: \"\" },\n                        },\n                        [\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              type: \"index\",\n                              label: \"序号\",\n                              align: \"center\",\n                              width: \"60\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"periodNo\",\n                              label: \"期号\",\n                              align: \"center\",\n                              \"min-width\": \"120\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followerNickname\",\n                              label: \"跟单人昵称\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followerAvatar\",\n                              label: \"跟单人头像\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    scope.row.followerAvatar\n                                      ? _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: scope.row.followerAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        })\n                                      : _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"symbol\",\n                              label: \"交易对\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followAmount\",\n                              label: \"跟单金额\",\n                              align: \"center\",\n                              \"min-width\": \"120\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"positionCount\",\n                              label: \"持仓个数\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"profit\",\n                              label: \"盈亏\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"profitRate\",\n                              label: \"收益率%\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"leverType\",\n                              label: \"杠杆类型\",\n                              align: \"center\",\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getLeverTypeText(\n                                            scope.row.leverType\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"winOrLose\",\n                              label: \"做多/做空\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getWinOrLoseText(\n                                            scope.row.winOrLose\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"copyType\",\n                              label: \"带单类型\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getCopyTypeText(\n                                            scope.row.copyType\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"takeProfit\",\n                              label: \"止盈\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"stopLoss\",\n                              label: \"止损\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"isStopProfit\",\n                              label: \"是否止盈止损\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.getYesNoText(\n                                            scope.row.isStopProfit\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"resultStatus\",\n                              label: \"结算结果\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-tag\",\n                                      {\n                                        attrs: {\n                                          type: _vm.getHistoryResultType(\n                                            scope.row.resultStatus\n                                          ),\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \" \" +\n                                            _vm._s(\n                                              _vm.getHistoryResultText(\n                                                scope.row.resultStatus\n                                              )\n                                            ) +\n                                            \" \"\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"isReturned\",\n                              label: \"是否返本\",\n                              align: \"center\",\n                              \"min-width\": \"100\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-tag\",\n                                      {\n                                        attrs: {\n                                          type:\n                                            scope.row.isReturned === 1\n                                              ? \"success\"\n                                              : \"info\",\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \" \" +\n                                            _vm._s(\n                                              scope.row.isReturned === 1\n                                                ? \"是\"\n                                                : \"否\"\n                                            ) +\n                                            \" \"\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"followTime\",\n                              label: \"跟单时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.followTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"settleTime\",\n                              label: \"结算时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.settleTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"remark\",\n                              label: \"备注\",\n                              align: \"center\",\n                              \"min-width\": \"120\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"createTime\",\n                              label: \"创建时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.createTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"updateTime\",\n                              label: \"更新时间\",\n                              align: \"center\",\n                              \"min-width\": \"160\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(\n                                          _vm.formatDateTime(\n                                            scope.row.updateTime\n                                          )\n                                        ) +\n                                        \" \"\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              label: \"操作\",\n                              align: \"center\",\n                              width: \"80\",\n                              fixed: \"right\",\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\n                                      \"el-button\",\n                                      {\n                                        attrs: { type: \"text\", size: \"mini\" },\n                                        on: {\n                                          click: function ($event) {\n                                            return _vm.showHistoryDetail(\n                                              scope.row\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"详情\")]\n                                    ),\n                                  ]\n                                },\n                              },\n                            ]),\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"pagination-container\" },\n                        [\n                          _c(\"el-pagination\", {\n                            attrs: {\n                              background: \"\",\n                              \"current-page\": _vm.historyQueryParams.pageNum,\n                              \"page-sizes\": [10, 20, 30, 50],\n                              \"page-size\": _vm.historyQueryParams.pageSize,\n                              layout: \"total, sizes, prev, pager, next, jumper\",\n                              total: _vm.historyTotal,\n                            },\n                            on: {\n                              \"size-change\": _vm.handleHistorySizeChange,\n                              \"current-change\": _vm.handleHistoryCurrentChange,\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-dialog\",\n                        {\n                          attrs: {\n                            visible: _vm.historyDetailDialogVisible,\n                            title: \"跟单明细详情\",\n                            width: \"900px\",\n                          },\n                          on: {\n                            \"update:visible\": function ($event) {\n                              _vm.historyDetailDialogVisible = $event\n                            },\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-descriptions\",\n                            {\n                              attrs: {\n                                column: 2,\n                                border: \"\",\n                                \"label-style\": \"width: 150px;\",\n                                title: \"跟单人信息\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"用户名\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.followerUsername ||\n                                        \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"邮箱\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.followerEmail || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"UID\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.followerUserNo || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"头像\" } },\n                                [\n                                  _vm.historyDetailRow.followerAvatar\n                                    ? [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: _vm.historyDetailRow\n                                              .followerAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        }),\n                                      ]\n                                    : [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                      ],\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"el-descriptions\",\n                            {\n                              staticStyle: { \"margin-top\": \"16px\" },\n                              attrs: {\n                                column: 2,\n                                border: \"\",\n                                \"label-style\": \"width: 150px;\",\n                                title: \"带单人信息\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"昵称\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.leaderNickname || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"头像\" } },\n                                [\n                                  _vm.historyDetailRow.leaderAvatar\n                                    ? [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: _vm.historyDetailRow\n                                              .leaderAvatar,\n                                          },\n                                          on: {\n                                            error: (e) =>\n                                              (e.target.src = require(\"@/assets/default.png\")),\n                                          },\n                                        }),\n                                      ]\n                                    : [\n                                        _c(\"img\", {\n                                          staticStyle: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            \"border-radius\": \"50%\",\n                                          },\n                                          attrs: {\n                                            src: require(\"@/assets/default.png\"),\n                                          },\n                                        }),\n                                      ],\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"el-descriptions\",\n                            {\n                              staticStyle: { \"margin-top\": \"16px\" },\n                              attrs: {\n                                column: 2,\n                                border: \"\",\n                                \"label-style\": \"width: 150px;\",\n                                title: \"业务信息\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"期号\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.historyDetailRow.periodNo || \"-\")\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"交易对\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.historyDetailRow.symbol || \"-\")\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"跟单金额\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.followAmount || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"开仓价\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.openPrice || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"平仓价\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.closePrice || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓个数\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.positionCount || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓状态\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getPositionStatusText(\n                                        _vm.historyDetailRow.positionStatus\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.historyDetailRow.positionTime\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"持仓结束时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.historyDetailRow.positionEndTime\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"杠杆类型\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getLeverTypeText(\n                                        _vm.historyDetailRow.leverType\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"做多/做空\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getWinOrLoseText(\n                                        _vm.historyDetailRow.winOrLose\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"带单类型\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getCopyTypeText(\n                                        _vm.historyDetailRow.copyType\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"止盈\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.takeProfit || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"止损\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.historyDetailRow.stopLoss || \"-\")\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"是否止盈止损\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.getYesNoText(\n                                        _vm.historyDetailRow.isStopProfit\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"盈亏\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.historyDetailRow.profit || \"-\")\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"收益率%\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.profitRate || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"是否返本\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.historyDetailRow.isReturned === 1\n                                        ? \"是\"\n                                        : _vm.historyDetailRow.isReturned === 0\n                                        ? \"否\"\n                                        : \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"备注\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(_vm.historyDetailRow.remark || \"-\")\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"创建时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.historyDetailRow.createTime\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                              _c(\n                                \"el-descriptions-item\",\n                                { attrs: { label: \"更新时间\" } },\n                                [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm.formatDateTime(\n                                        _vm.historyDetailRow.updateTime\n                                      ) || \"-\"\n                                    )\n                                  ),\n                                ]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-dialog\",\n            {\n              attrs: {\n                title: _vm.leaderTitle,\n                visible: _vm.leaderOpen,\n                width: \"600px\",\n                \"append-to-body\": \"\",\n              },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.leaderOpen = $event\n                },\n              },\n            },\n            [\n              _c(\n                \"el-form\",\n                {\n                  ref: \"leaderForm\",\n                  attrs: {\n                    model: _vm.leaderForm,\n                    rules: _vm.leaderRules,\n                    \"label-width\": \"100px\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"带单人昵称\", prop: \"leaderNickname\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { placeholder: \"请输入带单人昵称\" },\n                        model: {\n                          value: _vm.leaderForm.leaderNickname,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.leaderForm, \"leaderNickname\", $$v)\n                          },\n                          expression: \"leaderForm.leaderNickname\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"交易对\", prop: \"symbol\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { placeholder: \"请输入交易对\" },\n                        model: {\n                          value: _vm.leaderForm.symbol,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.leaderForm, \"symbol\", $$v)\n                          },\n                          expression: \"leaderForm.symbol\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"期号\", prop: \"periodNo\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { placeholder: \"请输入期号\" },\n                        model: {\n                          value: _vm.leaderForm.periodNo,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.leaderForm, \"periodNo\", $$v)\n                          },\n                          expression: \"leaderForm.periodNo\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"保证金\", prop: \"marginBalance\" } },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { precision: 8, step: 0.00000001, min: 0 },\n                        model: {\n                          value: _vm.leaderForm.marginBalance,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.leaderForm, \"marginBalance\", $$v)\n                          },\n                          expression: \"leaderForm.marginBalance\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"策略说明\", prop: \"remark\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: {\n                          type: \"textarea\",\n                          placeholder: \"请输入策略说明\",\n                        },\n                        model: {\n                          value: _vm.leaderForm.remark,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.leaderForm, \"remark\", $$v)\n                          },\n                          expression: \"leaderForm.remark\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"状态\", prop: \"status\" } },\n                    [\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          model: {\n                            value: _vm.leaderForm.status,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.leaderForm, \"status\", $$v)\n                            },\n                            expression: \"leaderForm.status\",\n                          },\n                        },\n                        [\n                          _c(\"el-radio\", { attrs: { label: 0 } }, [\n                            _vm._v(\"未开始\"),\n                          ]),\n                          _c(\"el-radio\", { attrs: { label: 1 } }, [\n                            _vm._v(\"准备中\"),\n                          ]),\n                          _c(\"el-radio\", { attrs: { label: 2 } }, [\n                            _vm._v(\"已开始\"),\n                          ]),\n                          _c(\"el-radio\", { attrs: { label: 3 } }, [\n                            _vm._v(\"结算中\"),\n                          ]),\n                          _c(\"el-radio\", { attrs: { label: 4 } }, [\n                            _vm._v(\"已结束\"),\n                          ]),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"dialog-footer\",\n                  attrs: { slot: \"footer\" },\n                  slot: \"footer\",\n                },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: { click: _vm.submitLeaderForm },\n                    },\n                    [_vm._v(\"确 定\")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      on: {\n                        click: function ($event) {\n                          _vm.leaderOpen = false\n                        },\n                      },\n                    },\n                    [_vm._v(\"取 消\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-dialog\",\n            {\n              attrs: {\n                visible: _vm.showConfigDialog,\n                title: \"带单配置管理\",\n                width: \"1100px\",\n              },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.showConfigDialog = $event\n                },\n              },\n            },\n            [\n              _c(\n                \"el-table\",\n                {\n                  staticStyle: { width: \"100%\" },\n                  attrs: { data: _vm.configList, border: \"\" },\n                },\n                [\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"id\",\n                      label: \"ID\",\n                      align: \"center\",\n                      width: \"60\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"name\",\n                      label: \"名称\",\n                      align: \"center\",\n                      \"min-width\": \"120\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"copyType\",\n                      label: \"带单类型\",\n                      align: \"center\",\n                      \"min-width\": \"100\",\n                    },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"span\", [\n                              _vm._v(\n                                _vm._s(_vm.getCopyTypeText(scope.row.copyType))\n                              ),\n                            ]),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"leverType\",\n                      label: \"杠杆类型\",\n                      align: \"center\",\n                      \"min-width\": \"100\",\n                    },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"span\", [\n                              _vm._v(\n                                _vm._s(\n                                  _vm.getLeverTypeText(scope.row.leverType)\n                                )\n                              ),\n                            ]),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"minFollowAmount\",\n                      label: \"最低跟单金额\",\n                      align: \"center\",\n                      \"min-width\": \"120\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"maxFollowAmount\",\n                      label: \"最高跟单金额\",\n                      align: \"center\",\n                      \"min-width\": \"120\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"minFollowCount\",\n                      label: \"最低跟单人数\",\n                      align: \"center\",\n                      \"min-width\": \"120\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"maxFollowCount\",\n                      label: \"最高跟单人数\",\n                      align: \"center\",\n                      \"min-width\": \"120\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"lockTime\",\n                      label: \"锁仓时间(天)\",\n                      align: \"center\",\n                      \"min-width\": \"120\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"createTime\",\n                      label: \"创建时间\",\n                      align: \"center\",\n                      \"min-width\": \"160\",\n                    },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _vm._v(\n                              \" \" +\n                                _vm._s(\n                                  _vm.formatDateTime(scope.row.createTime)\n                                ) +\n                                \" \"\n                            ),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"updateTime\",\n                      label: \"更新时间\",\n                      align: \"center\",\n                      \"min-width\": \"160\",\n                    },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _vm._v(\n                              \" \" +\n                                _vm._s(\n                                  _vm.formatDateTime(scope.row.updateTime)\n                                ) +\n                                \" \"\n                            ),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"操作\", align: \"center\", width: \"100\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"primary\", size: \"mini\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.editConfig(scope.row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"修改\")]\n                            ),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                ],\n                1\n              ),\n              _c(\n                \"span\",\n                {\n                  staticClass: \"dialog-footer\",\n                  attrs: { slot: \"footer\" },\n                  slot: \"footer\",\n                },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      on: {\n                        click: function ($event) {\n                          _vm.showConfigDialog = false\n                        },\n                      },\n                    },\n                    [_vm._v(\"关闭\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-dialog\",\n            {\n              attrs: {\n                visible: _vm.editConfigDialogVisible,\n                title: \"编辑带单配置\",\n                width: \"600px\",\n              },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.editConfigDialogVisible = $event\n                },\n              },\n            },\n            [\n              _c(\n                \"el-form\",\n                {\n                  ref: \"editConfigForm\",\n                  attrs: {\n                    model: _vm.editConfigForm,\n                    rules: _vm.editConfigRules,\n                    \"label-width\": \"120px\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"名称\", prop: \"name\" } },\n                    [\n                      _c(\"el-input\", {\n                        model: {\n                          value: _vm.editConfigForm.name,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.editConfigForm, \"name\", $$v)\n                          },\n                          expression: \"editConfigForm.name\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"带单类型\", prop: \"copyType\" } },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          model: {\n                            value: _vm.editConfigForm.copyType,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.editConfigForm, \"copyType\", $$v)\n                            },\n                            expression: \"editConfigForm.copyType\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", {\n                            attrs: { label: \"短线\", value: 0 },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"中线\", value: 1 },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"长线\", value: 2 },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"杠杆类型\", prop: \"leverType\" } },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          model: {\n                            value: _vm.editConfigForm.leverType,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.editConfigForm, \"leverType\", $$v)\n                            },\n                            expression: \"editConfigForm.leverType\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", { attrs: { label: \"x5\", value: 0 } }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"x10\", value: 1 },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    {\n                      attrs: { label: \"最低跟单金额\", prop: \"minFollowAmount\" },\n                    },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 0 },\n                        model: {\n                          value: _vm.editConfigForm.minFollowAmount,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.editConfigForm, \"minFollowAmount\", $$v)\n                          },\n                          expression: \"editConfigForm.minFollowAmount\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    {\n                      attrs: { label: \"最高跟单金额\", prop: \"maxFollowAmount\" },\n                    },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 0 },\n                        model: {\n                          value: _vm.editConfigForm.maxFollowAmount,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.editConfigForm, \"maxFollowAmount\", $$v)\n                          },\n                          expression: \"editConfigForm.maxFollowAmount\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    {\n                      attrs: { label: \"最低跟单人数\", prop: \"minFollowCount\" },\n                    },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 0 },\n                        model: {\n                          value: _vm.editConfigForm.minFollowCount,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.editConfigForm, \"minFollowCount\", $$v)\n                          },\n                          expression: \"editConfigForm.minFollowCount\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    {\n                      attrs: { label: \"最高跟单人数\", prop: \"maxFollowCount\" },\n                    },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 0 },\n                        model: {\n                          value: _vm.editConfigForm.maxFollowCount,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.editConfigForm, \"maxFollowCount\", $$v)\n                          },\n                          expression: \"editConfigForm.maxFollowCount\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"锁仓时间(天)\", prop: \"lockTime\" } },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 0 },\n                        model: {\n                          value: _vm.editConfigForm.lockTime,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.editConfigForm, \"lockTime\", $$v)\n                          },\n                          expression: \"editConfigForm.lockTime\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"span\",\n                {\n                  staticClass: \"dialog-footer\",\n                  attrs: { slot: \"footer\" },\n                  slot: \"footer\",\n                },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      on: {\n                        click: function ($event) {\n                          _vm.editConfigDialogVisible = false\n                        },\n                      },\n                    },\n                    [_vm._v(\"取消\")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: \"primary\",\n                        loading: _vm.saveConfigLoading,\n                      },\n                      on: { click: _vm.saveEditConfig },\n                    },\n                    [_vm._v(\"保存\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3B,CACEF,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EAAE,WAAW;IACxBC,EAAE,EAAE;MAAE,WAAW,EAAEJ,GAAG,CAACK;IAAe,CAAC;IACvCC,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACQ,SAAS;MACpBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAACQ,SAAS,GAAGE,GAAG;MACrB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEV,EAAE,CACA,aAAa,EACb;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EAAE,CAAC,EAC5C,CACEb,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,KAAK,EACL;IACEc,WAAW,EAAE;MACXC,OAAO,EAAE,MAAM;MACf,iBAAiB,EAAE,UAAU;MAC7B,aAAa,EAAE,QAAQ;MACvB,eAAe,EAAE;IACnB;EACF,CAAC,EACD,CACEf,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MAAEK,IAAI,EAAE;IAAU,CAAC;IAC1Bb,EAAE,EAAE;MACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAACoB,gBAAgB,GAAG,IAAI;MAC7B;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAACqB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,UAAU,EACV;IACEqB,UAAU,EAAE,CACV;MACER,IAAI,EAAE,SAAS;MACfS,OAAO,EAAE,WAAW;MACpBhB,KAAK,EAAEP,GAAG,CAACwB,aAAa;MACxBb,UAAU,EAAE;IACd,CAAC,CACF;IACDI,WAAW,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAE,YAAY,EAAE;IAAI,CAAC;IACjDb,KAAK,EAAE;MAAEc,IAAI,EAAE1B,GAAG,CAAC2B,UAAU;MAAEC,MAAM,EAAE;IAAG;EAC5C,CAAC,EACD,CACE3B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLK,IAAI,EAAE,OAAO;MACbJ,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACfJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFxB,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLA,KAAK,CAACC,GAAG,CAACC,UAAU,GAChBpC,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YAAE2B,GAAG,EAAEJ,KAAK,CAACC,GAAG,CAACC;UAAW,CAAC;UACpCjC,EAAE,EAAE;YACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;cAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;YAAA;UACnD;QACF,CAAC,CAAC,GACF1C,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;UACrC;QACF,CAAC,CAAC,CACP;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1C,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,QAAQ;MACdjB,KAAK,EAAE,KAAK;MACZgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,QAAQ;MACdjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC6C,aAAa,CAACV,KAAK,CAACC,GAAG,CAACU,MAAM,CACpC,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7C,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAACY,SACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF/C,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,SAAS;MACfjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAACZ,KAAK,CAACC,GAAG,CAACa,OAAO,CACtC,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFhD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,KAAK;MACZgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,KAAK;MACZgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACkD,qBAAqB,CACvBf,KAAK,CAACC,GAAG,CAACe,cACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFlD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoD,gBAAgB,CAClBjB,KAAK,CAACC,GAAG,CAACiB,SACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFpD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACsD,gBAAgB,CAClBnB,KAAK,CAACC,GAAG,CAACmB,SACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFtD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACwD,eAAe,CACjBrB,KAAK,CAACC,GAAG,CAACqB,QACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,aAAa;MACnBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,eAAe;MACrBjB,KAAK,EAAE,SAAS;MAChBgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,SAAS;MAChBgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,SAAS;MACfjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,eAAe;MACrBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,eAAe;MACrBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC0D,YAAY,CACdvB,KAAK,CAACC,GAAG,CAACuB,YACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAACwB,YACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF3D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,iBAAiB;MACvBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAACyB,eACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF5D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAAC0B,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAAC2B,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF9D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,QAAQ,EACR;UACEW,KAAK,EAAE;YACLK,IAAI,EACFkB,KAAK,CAACC,GAAG,CAAC4B,SAAS,KAAK,CAAC,GACrB,SAAS,GACT;UACR;QACF,CAAC,EACD,CACEhE,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACiE,cAAc,CAChB9B,KAAK,CAACC,GAAG,CAAC4B,SACZ,CACF,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF/D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLC,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACfJ,KAAK,EAAE,IAAI;MACXyC,KAAK,EAAE;IACT,CAAC;IACDnC,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,WAAW,EACX;UACEW,KAAK,EAAE;YAAEK,IAAI,EAAE,MAAM;YAAEkD,IAAI,EAAE;UAAO,CAAC;UACrC/D,EAAE,EAAE;YACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;cACvB,OAAOnB,GAAG,CAACoE,gBAAgB,CACzBjC,KAAK,CAACC,GACR,CAAC;YACH;UACF;QACF,CAAC,EACD,CAACpC,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,eAAe,EAAE;IAClBW,KAAK,EAAE;MACLyD,UAAU,EAAE,EAAE;MACd,cAAc,EAAErE,GAAG,CAACsE,iBAAiB,CAACC,OAAO;MAC7C,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC9B,WAAW,EAAEvE,GAAG,CAACsE,iBAAiB,CAACE,QAAQ;MAC3CC,MAAM,EAAE,yCAAyC;MACjDC,KAAK,EAAE1E,GAAG,CAAC2E;IACb,CAAC;IACDvE,EAAE,EAAE;MACF,aAAa,EAAEJ,GAAG,CAAC4E,sBAAsB;MACzC,gBAAgB,EAAE5E,GAAG,CAAC6E;IACxB;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACD5E,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLkE,OAAO,EAAE9E,GAAG,CAAC+E,yBAAyB;MACtCC,KAAK,EAAE,OAAO;MACdvD,KAAK,EAAE;IACT,CAAC;IACDrB,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAlB6E,aAAgBA,CAAY9D,MAAM,EAAE;QAClCnB,GAAG,CAAC+E,yBAAyB,GAAG5D,MAAM;MACxC;IACF;EACF,CAAC,EACD,CACElB,EAAE,CACA,iBAAiB,EACjB;IAAEW,KAAK,EAAE;MAAEsE,MAAM,EAAE,CAAC;MAAEtD,MAAM,EAAE;IAAG;EAAE,CAAC,EACpC,CACE3B,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACC,YAAY,CACzC,CAAC,CAEL,CAAC,EACDnF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACmF,eAAe,CAAC9C,UAAU,GAC1B,CACEpC,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEvC,GAAG,CAACmF,eAAe,CAAC9C;IAC3B,CAAC;IACDjC,EAAE,EAAE;MACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;QAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;MAAA;IACnD;EACF,CAAC,CAAC,CACH,GACD,CACE1C,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;IACrC;EACF,CAAC,CAAC,CACH,CACN,EACD,CACF,CAAC,EACD1C,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACE,MAAM,CAAC,CAAC,CAC7C,CAAC,EACDpF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACG,YAAY,CACzC,CAAC,CAEL,CAAC,EACDrF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACI,UAAU,CAAC,CAAC,CACjD,CAAC,EACDtF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACK,cAAc,CAC3C,CAAC,CAEL,CAAC,EACDvF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACM,cAAc,CAC3C,CAAC,CAEL,CAAC,EACDxF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACkD,qBAAqB,CACvBlD,GAAG,CAACmF,eAAe,CAAChC,cACtB,CACF,CACF,CAAC,CAEL,CAAC,EACDlD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACmF,eAAe,CAACvB,YACtB,CACF,CACF,CAAC,CAEL,CAAC,EACD3D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACmF,eAAe,CAACtB,eACtB,CACF,CACF,CAAC,CAEL,CAAC,EACD5D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACO,QAAQ,CAAC,CAAC,CAC/C,CAAC,EACDzF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACmF,eAAe,CAACnC,SACtB,CACF,CACF,CAAC,CAEL,CAAC,EACD/C,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACmF,eAAe,CAAClC,OACtB,CACF,CACF,CAAC,CAEL,CAAC,EACDhD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC6C,aAAa,CACf7C,GAAG,CAACmF,eAAe,CAACrC,MACtB,CACF,CACF,CAAC,CAEL,CAAC,EACD7C,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACQ,WAAW,CACxC,CAAC,CAEL,CAAC,EACD1F,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACS,OAAO,CAAC,CAAC,CAC9C,CAAC,EACD3F,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACU,aAAa,CAC1C,CAAC,CAEL,CAAC,EACD5F,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAU;EAAE,CAAC,EAC/B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACW,aAAa,CAC1C,CAAC,CAEL,CAAC,EACD7F,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAU;EAAE,CAAC,EAC/B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACY,UAAU,CAAC,CAAC,CACjD,CAAC,EACD9F,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACa,aAAa,CAC1C,CAAC,CAEL,CAAC,EACD/F,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoD,gBAAgB,CAClBpD,GAAG,CAACmF,eAAe,CAAC9B,SACtB,CACF,CACF,CAAC,CAEL,CAAC,EACDpD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACsD,gBAAgB,CAClBtD,GAAG,CAACmF,eAAe,CAAC5B,SACtB,CACF,CACF,CAAC,CAEL,CAAC,EACDtD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACwD,eAAe,CACjBxD,GAAG,CAACmF,eAAe,CAAC1B,QACtB,CACF,CACF,CAAC,CAEL,CAAC,EACDxD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACc,UAAU,CAAC,CAAC,CACjD,CAAC,EACDhG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACe,QAAQ,CAAC,CAAC,CAC/C,CAAC,EACDjG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC0D,YAAY,CACd1D,GAAG,CAACmF,eAAe,CAACxB,YACtB,CACF,CACF,CAAC,CAEL,CAAC,EACD1D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CAACb,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACmF,eAAe,CAACgB,MAAM,CAAC,CAAC,CAC7C,CAAC,EACDlG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACmF,eAAe,CAACrB,UACtB,CACF,CACF,CAAC,CAEL,CAAC,EACD7D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACmF,eAAe,CAACpB,UACtB,CACF,CACF,CAAC,CAEL,CAAC,EACD9D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACiE,cAAc,CAChBjE,GAAG,CAACmF,eAAe,CAACnB,SACtB,CACF,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,EACD/D,EAAE,CACA,aAAa,EACb;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EAAE,CAAC,EAC5C,CACEb,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,YAAY;IACzBS,KAAK,EAAE;MACLwF,MAAM,EAAE,CAAC;MACTnF,IAAI,EAAE,MAAM;MACZY,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE5B,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACwG,iBAAiB,CAACC,gBAAgB;MACxChG,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAACwG,iBAAiB,EACrB,kBAAkB,EAClB,OAAO9F,GAAG,KAAK,QAAQ,GACnBA,GAAG,CAACiG,IAAI,CAAC,CAAC,GACVjG,GACN,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACwG,iBAAiB,CAACI,WAAW;MACxCnG,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAACwG,iBAAiB,EACrB,aAAa,EACb,OAAO9F,GAAG,KAAK,QAAQ,GACnBA,GAAG,CAACiG,IAAI,CAAC,CAAC,GACVjG,GACN,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACwG,iBAAiB,CAACK,aAAa;MACrCpG,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAACwG,iBAAiB,EACrB,eAAe,EACf,OAAO9F,GAAG,KAAK,QAAQ,GACnBA,GAAG,CAACiG,IAAI,CAAC,CAAC,GACVjG,GACN,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACwG,iBAAiB,CAAC1D,MAAM;MACnCrC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAACwG,iBAAiB,EACrB,QAAQ,EACR9F,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEV,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAG;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEN,KAAK,EAAE;IAAE;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEN,KAAK,EAAE;IAAE;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEN,KAAK,EAAE;IAAE;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEN,KAAK,EAAE;IAAE;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEN,KAAK,EAAE;IAAE;EAClC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDN,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACwG,iBAAiB,CAACM,WAAW;MACnCrG,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAACwG,iBAAiB,EACrB,aAAa,EACb9F,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,EACD,CACEV,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAG;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEN,KAAK,EAAE;IAAE;EAChC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEN,KAAK,EAAE;IAAE;EAChC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDN,EAAE,CACA,QAAQ,EACR;IACEc,WAAW,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAE+F,GAAG,EAAE;IAAM,CAAC;IAC5CnG,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EACnB,CAAC,EACD,CACEpG,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLK,IAAI,EAAE,SAAS;MACf+F,IAAI,EAAE;IACR,CAAC;IACD5G,EAAE,EAAE;MAAEc,KAAK,EAAElB,GAAG,CAACiH;IAAkB;EACrC,CAAC,EACD,CAACjH,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDpB,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLK,IAAI,EAAE,SAAS;MACf+F,IAAI,EAAE;IACR,CAAC;IACD5G,EAAE,EAAE;MAAEc,KAAK,EAAElB,GAAG,CAACkH;IAAiB;EACpC,CAAC,EACD,CAAClH,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,UAAU,EACV;IACEqB,UAAU,EAAE,CACV;MACER,IAAI,EAAE,SAAS;MACfS,OAAO,EAAE,WAAW;MACpBhB,KAAK,EAAEP,GAAG,CAACmH,aAAa;MACxBxG,UAAU,EAAE;IACd,CAAC,CACF;IACDI,WAAW,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAE,YAAY,EAAE;IAAO,CAAC;IACpDb,KAAK,EAAE;MAAEc,IAAI,EAAE1B,GAAG,CAACoH,UAAU;MAAExF,MAAM,EAAE;IAAG;EAC5C,CAAC,EACD,CACE3B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLK,IAAI,EAAE,OAAO;MACbJ,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACfJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFxB,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,kBAAkB;MACxBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,eAAe;MACrBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLA,KAAK,CAACC,GAAG,CAACiF,cAAc,GACpBpH,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YACL2B,GAAG,EAAEJ,KAAK,CAACC,GAAG,CAACiF;UACjB,CAAC;UACDjH,EAAE,EAAE;YACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;cAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;YAAA;UACnD;QACF,CAAC,CAAC,GACF1C,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;UACrC;QACF,CAAC,CAAC,CACP;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1C,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,aAAa;MACnBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLA,KAAK,CAACC,GAAG,CAACkF,YAAY,GAClBrH,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YACL2B,GAAG,EAAEJ,KAAK,CAACC,GAAG,CAACkF;UACjB,CAAC;UACDlH,EAAE,EAAE;YACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;cAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;YAAA;UACnD;QACF,CAAC,CAAC,GACF1C,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;UACrC;QACF,CAAC,CAAC,CACP;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1C,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,QAAQ;MACdjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACuH,mBAAmB,CACrBpF,KAAK,CAACC,GAAG,CAACU,MACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7C,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,aAAa;MACnBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,QAAQ,EACR;UACEW,KAAK,EAAE;YACLK,IAAI,EACFkB,KAAK,CAACC,GAAG,CAAC0E,WAAW,KAAK,CAAC,GACvB,SAAS,GACT;UACR;QACF,CAAC,EACD,CACE9G,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJT,KAAK,CAACC,GAAG,CAAC0E,WAAW,KAAK,CAAC,GACvB,GAAG,GACH,GACN,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7G,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,QAAQ,EACR;UACEW,KAAK,EAAE;YACLK,IAAI,EAAEjB,GAAG,CAACwH,oBAAoB,CAC5BrF,KAAK,CAACC,GAAG,CAACqF,YACZ;UACF;QACF,CAAC,EACD,CACEzH,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC0H,oBAAoB,CACtBvF,KAAK,CAACC,GAAG,CAACqF,YACZ,CACF,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxH,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,QAAQ,EACR;UACEW,KAAK,EAAE;YACLK,IAAI,EACFkB,KAAK,CAACC,GAAG,CAACuF,UAAU,KAAK,CAAC,GACtB,SAAS,GACT;UACR;QACF,CAAC,EACD,CACE3H,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJT,KAAK,CAACC,GAAG,CAACuF,UAAU,KAAK,CAAC,GACtB,GAAG,GACH,GACN,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1H,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,QAAQ,EACR;UACEW,KAAK,EAAE;YACLK,IAAI,EACFkB,KAAK,CAACC,GAAG,CAACwF,SAAS,KAAK,CAAC,GACrB,SAAS,GACT;UACR;QACF,CAAC,EACD,CACE5H,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJT,KAAK,CAACC,GAAG,CAACwF,SAAS,KAAK,CAAC,GACrB,GAAG,GACH,GACN,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF3H,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAACyF,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF5H,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAAC0F,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7H,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLC,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACfJ,KAAK,EAAE,IAAI;MACXyC,KAAK,EAAE;IACT,CAAC;IACDnC,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,WAAW,EACX;UACEW,KAAK,EAAE;YAAEK,IAAI,EAAE,MAAM;YAAEkD,IAAI,EAAE;UAAO,CAAC;UACrC/D,EAAE,EAAE;YACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;cACvB,OAAOnB,GAAG,CAAC+H,gBAAgB,CACzB5F,KAAK,CAACC,GACR,CAAC;YACH;UACF;QACF,CAAC,EACD,CAACpC,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,eAAe,EAAE;IAClBW,KAAK,EAAE;MACLyD,UAAU,EAAE,EAAE;MACd,cAAc,EAAErE,GAAG,CAACwG,iBAAiB,CAACjC,OAAO;MAC7C,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC9B,WAAW,EAAEvE,GAAG,CAACwG,iBAAiB,CAAChC,QAAQ;MAC3CC,MAAM,EAAE,yCAAyC;MACjDC,KAAK,EAAE1E,GAAG,CAACgI;IACb,CAAC;IACD5H,EAAE,EAAE;MACF,aAAa,EAAEJ,GAAG,CAACiI,sBAAsB;MACzC,gBAAgB,EAAEjI,GAAG,CAACkI;IACxB;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDjI,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLkE,OAAO,EAAE9E,GAAG,CAACmI,yBAAyB;MACtCnD,KAAK,EAAE,QAAQ;MACfvD,KAAK,EAAE;IACT,CAAC;IACDrB,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAlB6E,aAAgBA,CAAY9D,MAAM,EAAE;QAClCnB,GAAG,CAACmI,yBAAyB,GAAGhH,MAAM;MACxC;IACF;EACF,CAAC,EACD,CACElB,EAAE,CACA,iBAAiB,EACjB;IACEW,KAAK,EAAE;MACLsE,MAAM,EAAE,CAAC;MACTtD,MAAM,EAAE,EAAE;MACV,aAAa,EAAE,eAAe;MAC9BoD,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE/E,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAAC3B,gBAAgB,IAClC,GACJ,CACF,CAAC,CAEL,CAAC,EACDxG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACvB,aAAa,IAAI,GACvC,CACF,CAAC,CAEL,CAAC,EACD5G,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACC,cAAc,IAAI,GACxC,CACF,CAAC,CAEL,CAAC,EACDpI,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACoI,eAAe,CAACf,cAAc,GAC9B,CACEpH,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEvC,GAAG,CAACoI,eAAe,CACrBf;IACL,CAAC;IACDjH,EAAE,EAAE;MACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;QAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;MAAA;IACnD;EACF,CAAC,CAAC,CACH,GACD,CACE1C,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;IACrC;EACF,CAAC,CAAC,CACH,CACN,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD1C,EAAE,CACA,iBAAiB,EACjB;IACEc,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCH,KAAK,EAAE;MACLsE,MAAM,EAAE,CAAC;MACTtD,MAAM,EAAE,EAAE;MACV,aAAa,EAAE,eAAe;MAC9BoD,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE/E,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACE,cAAc,IAAI,GACxC,CACF,CAAC,CAEL,CAAC,EACDrI,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACG,WAAW,IAAI,GACrC,CACF,CAAC,CAEL,CAAC,EACDtI,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACI,YAAY,IAAI,GACtC,CACF,CAAC,CAEL,CAAC,EACDvI,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACoI,eAAe,CAACd,YAAY,GAC5B,CACErH,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEvC,GAAG,CAACoI,eAAe,CACrBd;IACL,CAAC;IACDlH,EAAE,EAAE;MACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;QAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;MAAA;IACnD;EACF,CAAC,CAAC,CACH,GACD,CACE1C,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;IACrC;EACF,CAAC,CAAC,CACH,CACN,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD1C,EAAE,CACA,iBAAiB,EACjB;IACEc,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCH,KAAK,EAAE;MACLsE,MAAM,EAAE,CAAC;MACTtD,MAAM,EAAE,EAAE;MACV,aAAa,EAAE,eAAe;MAC9BoD,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE/E,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACoI,eAAe,CAAC1C,QAAQ,IAAI,GAAG,CAC5C,CAAC,CAEL,CAAC,EACDzF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACK,YAAY,IAAI,GACtC,CACF,CAAC,CAEL,CAAC,EACDxI,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACuH,mBAAmB,CACrBvH,GAAG,CAACoI,eAAe,CAACtF,MACtB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACD7C,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACtB,WAAW,KAAK,CAAC,GACjC,GAAG,GACH9G,GAAG,CAACoI,eAAe,CAACtB,WAAW,KAAK,CAAC,GACrC,GAAG,GACH,GACN,CACF,CAAC,CAEL,CAAC,EACD7G,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC0H,oBAAoB,CACtB1H,GAAG,CAACoI,eAAe,CAACX,YACtB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACDxH,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACT,UAAU,KAAK,CAAC,GAChC,GAAG,GACH3H,GAAG,CAACoI,eAAe,CAACT,UAAU,KAAK,CAAC,GACpC,GAAG,GACH,GACN,CACF,CAAC,CAEL,CAAC,EACD1H,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoI,eAAe,CAACR,SAAS,KAAK,CAAC,GAC/B,GAAG,GACH5H,GAAG,CAACoI,eAAe,CAACR,SAAS,KAAK,CAAC,GACnC,GAAG,GACH,GACN,CACF,CAAC,CAEL,CAAC,EACD3H,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACoI,eAAe,CAACP,UACtB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACD5H,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACoI,eAAe,CAACN,UACtB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,EACD7H,EAAE,CACA,aAAa,EACb;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EAAE,CAAC,EAC7C,CACEb,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,YAAY;IACzBS,KAAK,EAAE;MACLwF,MAAM,EAAE,CAAC;MACTnF,IAAI,EAAE,MAAM;MACZY,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE5B,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAAC0I,kBAAkB,CAACjC,gBAAgB;MACzChG,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAAC0I,kBAAkB,EACtB,kBAAkB,EAClB,OAAOhI,GAAG,KAAK,QAAQ,GACnBA,GAAG,CAACiG,IAAI,CAAC,CAAC,GACVjG,GACN,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,OAAO;MACpBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAAC0I,kBAAkB,CAAC7B,aAAa;MACtCpG,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAAC0I,kBAAkB,EACtB,eAAe,EACf,OAAOhI,GAAG,KAAK,QAAQ,GACnBA,GAAG,CAACiG,IAAI,CAAC,CAAC,GACVjG,GACN,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAAC0I,kBAAkB,CAACf,UAAU;MACnClH,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAAC0I,kBAAkB,EACtB,YAAY,EACZhI,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,EACD,CACEV,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAG;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEN,KAAK,EAAE;IAAE;EAChC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEN,KAAK,EAAE;IAAE;EAChC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDN,EAAE,CACA,QAAQ,EACR;IAAEW,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EAAE,CAAC,EACtB,CACEpG,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE;MACL0F,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE;IACb,CAAC;IACDjG,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAAC0I,kBAAkB,CAACjB,YAAY;MACrChH,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CACN1G,GAAG,CAAC0I,kBAAkB,EACtB,cAAc,EACdhI,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,EACD,CACEV,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAG;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEN,KAAK,EAAE;IAAE;EAClC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAE;EACjC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAE;EACjC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDN,EAAE,CACA,QAAQ,EACR;IACEc,WAAW,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAE+F,GAAG,EAAE;IAAM,CAAC;IAC5CnG,KAAK,EAAE;MAAEyF,IAAI,EAAE;IAAE;EACnB,CAAC,EACD,CACEpG,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLK,IAAI,EAAE,SAAS;MACf+F,IAAI,EAAE;IACR,CAAC;IACD5G,EAAE,EAAE;MAAEc,KAAK,EAAElB,GAAG,CAAC2I;IAAmB;EACtC,CAAC,EACD,CAAC3I,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDpB,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLK,IAAI,EAAE,SAAS;MACf+F,IAAI,EAAE;IACR,CAAC;IACD5G,EAAE,EAAE;MAAEc,KAAK,EAAElB,GAAG,CAAC4I;IAAkB;EACrC,CAAC,EACD,CAAC5I,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,UAAU,EACV;IACEqB,UAAU,EAAE,CACV;MACER,IAAI,EAAE,SAAS;MACfS,OAAO,EAAE,WAAW;MACpBhB,KAAK,EAAEP,GAAG,CAAC6I,cAAc;MACzBlI,UAAU,EAAE;IACd,CAAC,CACF;IACDI,WAAW,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAE,YAAY,EAAE;IAAO,CAAC;IACpDb,KAAK,EAAE;MAAEc,IAAI,EAAE1B,GAAG,CAAC8I,WAAW;MAAElH,MAAM,EAAE;IAAG;EAC7C,CAAC,EACD,CACE3B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLK,IAAI,EAAE,OAAO;MACbJ,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACfJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFxB,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,kBAAkB;MACxBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLA,KAAK,CAACC,GAAG,CAACiF,cAAc,GACpBpH,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YACL2B,GAAG,EAAEJ,KAAK,CAACC,GAAG,CAACiF;UACjB,CAAC;UACDjH,EAAE,EAAE;YACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;cAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;YAAA;UACnD;QACF,CAAC,CAAC,GACF1C,EAAE,CAAC,KAAK,EAAE;UACRc,WAAW,EAAE;YACXU,KAAK,EAAE,MAAM;YACba,MAAM,EAAE,MAAM;YACd,eAAe,EAAE;UACnB,CAAC;UACD1B,KAAK,EAAE;YACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;UACrC;QACF,CAAC,CAAC,CACP;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1C,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,QAAQ;MACdjB,KAAK,EAAE,KAAK;MACZgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,eAAe;MACrBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,QAAQ;MACdjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoD,gBAAgB,CAClBjB,KAAK,CAACC,GAAG,CAACiB,SACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFpD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,OAAO;MACdgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACsD,gBAAgB,CAClBnB,KAAK,CAACC,GAAG,CAACmB,SACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFtD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACwD,eAAe,CACjBrB,KAAK,CAACC,GAAG,CAACqB,QACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC0D,YAAY,CACdvB,KAAK,CAACC,GAAG,CAACuB,YACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,cAAc;MACpBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,QAAQ,EACR;UACEW,KAAK,EAAE;YACLK,IAAI,EAAEjB,GAAG,CAACwH,oBAAoB,CAC5BrF,KAAK,CAACC,GAAG,CAACqF,YACZ;UACF;QACF,CAAC,EACD,CACEzH,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC0H,oBAAoB,CACtBvF,KAAK,CAACC,GAAG,CAACqF,YACZ,CACF,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxH,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,QAAQ,EACR;UACEW,KAAK,EAAE;YACLK,IAAI,EACFkB,KAAK,CAACC,GAAG,CAACuF,UAAU,KAAK,CAAC,GACtB,SAAS,GACT;UACR;QACF,CAAC,EACD,CACE3H,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJT,KAAK,CAACC,GAAG,CAACuF,UAAU,KAAK,CAAC,GACtB,GAAG,GACH,GACN,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF1H,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAACyF,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF5H,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAAC0F,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7H,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,QAAQ;MACdjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAAC0B,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChBZ,KAAK,CAACC,GAAG,CAAC2B,UACZ,CACF,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF9D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLC,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACfJ,KAAK,EAAE,IAAI;MACXyC,KAAK,EAAE;IACT,CAAC;IACDnC,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,WAAW,EACX;UACEW,KAAK,EAAE;YAAEK,IAAI,EAAE,MAAM;YAAEkD,IAAI,EAAE;UAAO,CAAC;UACrC/D,EAAE,EAAE;YACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;cACvB,OAAOnB,GAAG,CAAC+I,iBAAiB,CAC1B5G,KAAK,CAACC,GACR,CAAC;YACH;UACF;QACF,CAAC,EACD,CAACpC,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,eAAe,EAAE;IAClBW,KAAK,EAAE;MACLyD,UAAU,EAAE,EAAE;MACd,cAAc,EAAErE,GAAG,CAAC0I,kBAAkB,CAACnE,OAAO;MAC9C,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC9B,WAAW,EAAEvE,GAAG,CAAC0I,kBAAkB,CAAClE,QAAQ;MAC5CC,MAAM,EAAE,yCAAyC;MACjDC,KAAK,EAAE1E,GAAG,CAACgJ;IACb,CAAC;IACD5I,EAAE,EAAE;MACF,aAAa,EAAEJ,GAAG,CAACiJ,uBAAuB;MAC1C,gBAAgB,EAAEjJ,GAAG,CAACkJ;IACxB;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDjJ,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLkE,OAAO,EAAE9E,GAAG,CAACmJ,0BAA0B;MACvCnE,KAAK,EAAE,QAAQ;MACfvD,KAAK,EAAE;IACT,CAAC;IACDrB,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAlB6E,aAAgBA,CAAY9D,MAAM,EAAE;QAClCnB,GAAG,CAACmJ,0BAA0B,GAAGhI,MAAM;MACzC;IACF;EACF,CAAC,EACD,CACElB,EAAE,CACA,iBAAiB,EACjB;IACEW,KAAK,EAAE;MACLsE,MAAM,EAAE,CAAC;MACTtD,MAAM,EAAE,EAAE;MACV,aAAa,EAAE,eAAe;MAC9BoD,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE/E,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAAC3C,gBAAgB,IACnC,GACJ,CACF,CAAC,CAEL,CAAC,EACDxG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACvC,aAAa,IAAI,GACxC,CACF,CAAC,CAEL,CAAC,EACD5G,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACf,cAAc,IAAI,GACzC,CACF,CAAC,CAEL,CAAC,EACDpI,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACoJ,gBAAgB,CAAC/B,cAAc,GAC/B,CACEpH,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEvC,GAAG,CAACoJ,gBAAgB,CACtB/B;IACL,CAAC;IACDjH,EAAE,EAAE;MACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;QAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;MAAA;IACnD;EACF,CAAC,CAAC,CACH,GACD,CACE1C,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;IACrC;EACF,CAAC,CAAC,CACH,CACN,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD1C,EAAE,CACA,iBAAiB,EACjB;IACEc,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCH,KAAK,EAAE;MACLsE,MAAM,EAAE,CAAC;MACTtD,MAAM,EAAE,EAAE;MACV,aAAa,EAAE,eAAe;MAC9BoD,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE/E,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACC,cAAc,IAAI,GACzC,CACF,CAAC,CAEL,CAAC,EACDpJ,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACoJ,gBAAgB,CAAC9B,YAAY,GAC7B,CACErH,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEvC,GAAG,CAACoJ,gBAAgB,CACtB9B;IACL,CAAC;IACDlH,EAAE,EAAE;MACFoC,KAAK,EAAE,SAAPA,KAAKA,CAAGC,CAAC;QAAA,OACNA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGI,OAAO,CAAC,sBAAsB,CAAC;MAAA;IACnD;EACF,CAAC,CAAC,CACH,GACD,CACE1C,EAAE,CAAC,KAAK,EAAE;IACRc,WAAW,EAAE;MACXU,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACd,eAAe,EAAE;IACnB,CAAC;IACD1B,KAAK,EAAE;MACL2B,GAAG,EAAEI,OAAO,CAAC,sBAAsB;IACrC;EACF,CAAC,CAAC,CACH,CACN,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD1C,EAAE,CACA,iBAAiB,EACjB;IACEc,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCH,KAAK,EAAE;MACLsE,MAAM,EAAE,CAAC;MACTtD,MAAM,EAAE,EAAE;MACV,aAAa,EAAE,eAAe;MAC9BoD,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACE/E,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACoJ,gBAAgB,CAAC1D,QAAQ,IAAI,GAAG,CAC7C,CAAC,CAEL,CAAC,EACDzF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACoJ,gBAAgB,CAAC/D,MAAM,IAAI,GAAG,CAC3C,CAAC,CAEL,CAAC,EACDpF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACX,YAAY,IAAI,GACvC,CACF,CAAC,CAEL,CAAC,EACDxI,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACE,SAAS,IAAI,GACpC,CACF,CAAC,CAEL,CAAC,EACDrJ,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAAC7D,UAAU,IAAI,GACrC,CACF,CAAC,CAEL,CAAC,EACDtF,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACG,aAAa,IAAI,GACxC,CACF,CAAC,CAEL,CAAC,EACDtJ,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACkD,qBAAqB,CACvBlD,GAAG,CAACoJ,gBAAgB,CAACjG,cACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACDlD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACoJ,gBAAgB,CAACxF,YACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACD3D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACoJ,gBAAgB,CAACvF,eACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACD5D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoD,gBAAgB,CAClBpD,GAAG,CAACoJ,gBAAgB,CAAC/F,SACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACDpD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACsD,gBAAgB,CAClBtD,GAAG,CAACoJ,gBAAgB,CAAC7F,SACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACDtD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACwD,eAAe,CACjBxD,GAAG,CAACoJ,gBAAgB,CAAC3F,QACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACDxD,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACnD,UAAU,IAAI,GACrC,CACF,CAAC,CAEL,CAAC,EACDhG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACoJ,gBAAgB,CAAClD,QAAQ,IAAI,GAAG,CAC7C,CAAC,CAEL,CAAC,EACDjG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC0D,YAAY,CACd1D,GAAG,CAACoJ,gBAAgB,CAACzF,YACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACD1D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACoJ,gBAAgB,CAACI,MAAM,IAAI,GAAG,CAC3C,CAAC,CAEL,CAAC,EACDvJ,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACrD,UAAU,IAAI,GACrC,CACF,CAAC,CAEL,CAAC,EACD9F,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoJ,gBAAgB,CAACzB,UAAU,KAAK,CAAC,GACjC,GAAG,GACH3H,GAAG,CAACoJ,gBAAgB,CAACzB,UAAU,KAAK,CAAC,GACrC,GAAG,GACH,GACN,CACF,CAAC,CAEL,CAAC,EACD1H,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACoJ,gBAAgB,CAACjD,MAAM,IAAI,GAAG,CAC3C,CAAC,CAEL,CAAC,EACDlG,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACoJ,gBAAgB,CAACtF,UACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,EACD7D,EAAE,CACA,sBAAsB,EACtB;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAChB/C,GAAG,CAACoJ,gBAAgB,CAACrF,UACvB,CAAC,IAAI,GACP,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACD9D,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLoE,KAAK,EAAEhF,GAAG,CAACyJ,WAAW;MACtB3E,OAAO,EAAE9E,GAAG,CAAC0J,UAAU;MACvBjI,KAAK,EAAE,OAAO;MACd,gBAAgB,EAAE;IACpB,CAAC;IACDrB,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAlB6E,aAAgBA,CAAY9D,MAAM,EAAE;QAClCnB,GAAG,CAAC0J,UAAU,GAAGvI,MAAM;MACzB;IACF;EACF,CAAC,EACD,CACElB,EAAE,CACA,SAAS,EACT;IACE0J,GAAG,EAAE,YAAY;IACjB/I,KAAK,EAAE;MACLN,KAAK,EAAEN,GAAG,CAAC4J,UAAU;MACrBC,KAAK,EAAE7J,GAAG,CAAC8J,WAAW;MACtB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CACE7J,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEiB,IAAI,EAAE;IAAiB;EAAE,CAAC,EACrD,CACE7B,EAAE,CAAC,UAAU,EAAE;IACbW,KAAK,EAAE;MAAE0F,WAAW,EAAE;IAAW,CAAC;IAClChG,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAAC4J,UAAU,CAACP,cAAc;MACpC5I,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAAC4J,UAAU,EAAE,gBAAgB,EAAElJ,GAAG,CAAC;MACjD,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEiB,IAAI,EAAE;IAAS;EAAE,CAAC,EAC3C,CACE7B,EAAE,CAAC,UAAU,EAAE;IACbW,KAAK,EAAE;MAAE0F,WAAW,EAAE;IAAS,CAAC;IAChChG,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAAC4J,UAAU,CAACvE,MAAM;MAC5B5E,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAAC4J,UAAU,EAAE,QAAQ,EAAElJ,GAAG,CAAC;MACzC,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEiB,IAAI,EAAE;IAAW;EAAE,CAAC,EAC5C,CACE7B,EAAE,CAAC,UAAU,EAAE;IACbW,KAAK,EAAE;MAAE0F,WAAW,EAAE;IAAQ,CAAC;IAC/BhG,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAAC4J,UAAU,CAAClE,QAAQ;MAC9BjF,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAAC4J,UAAU,EAAE,UAAU,EAAElJ,GAAG,CAAC;MAC3C,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEiB,IAAI,EAAE;IAAgB;EAAE,CAAC,EAClD,CACE7B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MAAEmJ,SAAS,EAAE,CAAC;MAAEC,IAAI,EAAE,UAAU;MAAEC,GAAG,EAAE;IAAE,CAAC;IACjD3J,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAAC4J,UAAU,CAAC5D,aAAa;MACnCvF,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAAC4J,UAAU,EAAE,eAAe,EAAElJ,GAAG,CAAC;MAChD,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEiB,IAAI,EAAE;IAAS;EAAE,CAAC,EAC5C,CACE7B,EAAE,CAAC,UAAU,EAAE;IACbW,KAAK,EAAE;MACLK,IAAI,EAAE,UAAU;MAChBqF,WAAW,EAAE;IACf,CAAC;IACDhG,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAAC4J,UAAU,CAACzD,MAAM;MAC5B1F,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAAC4J,UAAU,EAAE,QAAQ,EAAElJ,GAAG,CAAC;MACzC,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEiB,IAAI,EAAE;IAAS;EAAE,CAAC,EAC1C,CACE7B,EAAE,CACA,gBAAgB,EAChB;IACEK,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAAC4J,UAAU,CAAC9G,MAAM;MAC5BrC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAAC4J,UAAU,EAAE,QAAQ,EAAElJ,GAAG,CAAC;MACzC,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEV,EAAE,CAAC,UAAU,EAAE;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAE;EAAE,CAAC,EAAE,CACtCb,GAAG,CAACqB,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFpB,EAAE,CAAC,UAAU,EAAE;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAE;EAAE,CAAC,EAAE,CACtCb,GAAG,CAACqB,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFpB,EAAE,CAAC,UAAU,EAAE;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAE;EAAE,CAAC,EAAE,CACtCb,GAAG,CAACqB,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFpB,EAAE,CAAC,UAAU,EAAE;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAE;EAAE,CAAC,EAAE,CACtCb,GAAG,CAACqB,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFpB,EAAE,CAAC,UAAU,EAAE;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAE;EAAE,CAAC,EAAE,CACtCb,GAAG,CAACqB,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,eAAe;IAC5BS,KAAK,EAAE;MAAEsJ,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CACEjK,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MAAEK,IAAI,EAAE;IAAU,CAAC;IAC1Bb,EAAE,EAAE;MAAEc,KAAK,EAAElB,GAAG,CAACmK;IAAiB;EACpC,CAAC,EACD,CAACnK,GAAG,CAACqB,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,EACDpB,EAAE,CACA,WAAW,EACX;IACEG,EAAE,EAAE;MACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAAC0J,UAAU,GAAG,KAAK;MACxB;IACF;EACF,CAAC,EACD,CAAC1J,GAAG,CAACqB,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLkE,OAAO,EAAE9E,GAAG,CAACoB,gBAAgB;MAC7B4D,KAAK,EAAE,QAAQ;MACfvD,KAAK,EAAE;IACT,CAAC;IACDrB,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAlB6E,aAAgBA,CAAY9D,MAAM,EAAE;QAClCnB,GAAG,CAACoB,gBAAgB,GAAGD,MAAM;MAC/B;IACF;EACF,CAAC,EACD,CACElB,EAAE,CACA,UAAU,EACV;IACEc,WAAW,EAAE;MAAEU,KAAK,EAAE;IAAO,CAAC;IAC9Bb,KAAK,EAAE;MAAEc,IAAI,EAAE1B,GAAG,CAACoK,UAAU;MAAExI,MAAM,EAAE;IAAG;EAC5C,CAAC,EACD,CACE3B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,IAAI;MACVjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACfJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFxB,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,MAAM;MACZjB,KAAK,EAAE,IAAI;MACXgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACwD,eAAe,CAACrB,KAAK,CAACC,GAAG,CAACqB,QAAQ,CAAC,CAChD,CAAC,CACF,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,WAAW;MACjBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAACoD,gBAAgB,CAACjB,KAAK,CAACC,GAAG,CAACiB,SAAS,CAC1C,CACF,CAAC,CACF,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFpD,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,iBAAiB;MACvBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,iBAAiB;MACvBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBjB,KAAK,EAAE,QAAQ;MACfgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,UAAU;MAChBjB,KAAK,EAAE,SAAS;MAChBgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAACZ,KAAK,CAACC,GAAG,CAAC0B,UAAU,CACzC,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF7D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MACLkB,IAAI,EAAE,YAAY;MAClBjB,KAAK,EAAE,MAAM;MACbgB,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLnC,GAAG,CAACqB,EAAE,CACJ,GAAG,GACDrB,GAAG,CAAC4C,EAAE,CACJ5C,GAAG,CAAC+C,cAAc,CAACZ,KAAK,CAACC,GAAG,CAAC2B,UAAU,CACzC,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF9D,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEgB,KAAK,EAAE,QAAQ;MAAEJ,KAAK,EAAE;IAAM,CAAC;IACrDM,WAAW,EAAE/B,GAAG,CAACgC,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,WAAW,EACX;UACEW,KAAK,EAAE;YAAEK,IAAI,EAAE,SAAS;YAAEkD,IAAI,EAAE;UAAO,CAAC;UACxC/D,EAAE,EAAE;YACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;cACvB,OAAOnB,GAAG,CAACqK,UAAU,CAAClI,KAAK,CAACC,GAAG,CAAC;YAClC;UACF;QACF,CAAC,EACD,CAACpC,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDpB,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EAAE,eAAe;IAC5BS,KAAK,EAAE;MAAEsJ,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CACEjK,EAAE,CACA,WAAW,EACX;IACEG,EAAE,EAAE;MACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAACoB,gBAAgB,GAAG,KAAK;MAC9B;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLkE,OAAO,EAAE9E,GAAG,CAACsK,uBAAuB;MACpCtF,KAAK,EAAE,QAAQ;MACfvD,KAAK,EAAE;IACT,CAAC;IACDrB,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAlB6E,aAAgBA,CAAY9D,MAAM,EAAE;QAClCnB,GAAG,CAACsK,uBAAuB,GAAGnJ,MAAM;MACtC;IACF;EACF,CAAC,EACD,CACElB,EAAE,CACA,SAAS,EACT;IACE0J,GAAG,EAAE,gBAAgB;IACrB/I,KAAK,EAAE;MACLN,KAAK,EAAEN,GAAG,CAACuK,cAAc;MACzBV,KAAK,EAAE7J,GAAG,CAACwK,eAAe;MAC1B,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CACEvK,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEiB,IAAI,EAAE;IAAO;EAAE,CAAC,EACxC,CACE7B,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAACzJ,IAAI;MAC9BL,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,MAAM,EAAE7J,GAAG,CAAC;MAC3C,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEiB,IAAI,EAAE;IAAW;EAAE,CAAC,EAC9C,CACE7B,EAAE,CACA,WAAW,EACX;IACEK,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAAC9G,QAAQ;MAClChD,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,UAAU,EAAE7J,GAAG,CAAC;MAC/C,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEV,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAE;EACjC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAE;EACjC,CAAC,CAAC,EACFN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAE;EACjC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDN,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEiB,IAAI,EAAE;IAAY;EAAE,CAAC,EAC/C,CACE7B,EAAE,CACA,WAAW,EACX;IACEK,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAAClH,SAAS;MACnC5C,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,WAAW,EAAE7J,GAAG,CAAC;MAChD,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEV,EAAE,CAAC,WAAW,EAAE;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEN,KAAK,EAAE;IAAE;EAAE,CAAC,CAAC,EACrDN,EAAE,CAAC,WAAW,EAAE;IACdW,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEN,KAAK,EAAE;IAAE;EAClC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDN,EAAE,CACA,cAAc,EACd;IACEW,KAAK,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEiB,IAAI,EAAE;IAAkB;EACpD,CAAC,EACD,CACE7B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MAAEqJ,GAAG,EAAE;IAAE,CAAC;IACjB3J,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAACE,eAAe;MACzChK,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,iBAAiB,EAAE7J,GAAG,CAAC;MACtD,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IACEW,KAAK,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEiB,IAAI,EAAE;IAAkB;EACpD,CAAC,EACD,CACE7B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MAAEqJ,GAAG,EAAE;IAAE,CAAC;IACjB3J,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAACG,eAAe;MACzCjK,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,iBAAiB,EAAE7J,GAAG,CAAC;MACtD,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IACEW,KAAK,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEiB,IAAI,EAAE;IAAiB;EACnD,CAAC,EACD,CACE7B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MAAEqJ,GAAG,EAAE;IAAE,CAAC;IACjB3J,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAACI,cAAc;MACxClK,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,gBAAgB,EAAE7J,GAAG,CAAC;MACrD,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IACEW,KAAK,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEiB,IAAI,EAAE;IAAiB;EACnD,CAAC,EACD,CACE7B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MAAEqJ,GAAG,EAAE;IAAE,CAAC;IACjB3J,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAACK,cAAc;MACxCnK,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,gBAAgB,EAAE7J,GAAG,CAAC;MACrD,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,cAAc,EACd;IAAEW,KAAK,EAAE;MAAEC,KAAK,EAAE,SAAS;MAAEiB,IAAI,EAAE;IAAW;EAAE,CAAC,EACjD,CACE7B,EAAE,CAAC,iBAAiB,EAAE;IACpBW,KAAK,EAAE;MAAEqJ,GAAG,EAAE;IAAE,CAAC;IACjB3J,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuK,cAAc,CAACM,QAAQ;MAClCpK,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAAC0G,IAAI,CAAC1G,GAAG,CAACuK,cAAc,EAAE,UAAU,EAAE7J,GAAG,CAAC;MAC/C,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDV,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EAAE,eAAe;IAC5BS,KAAK,EAAE;MAAEsJ,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CACEjK,EAAE,CACA,WAAW,EACX;IACEG,EAAE,EAAE;MACFc,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBnB,GAAG,CAACsK,uBAAuB,GAAG,KAAK;MACrC;IACF;EACF,CAAC,EACD,CAACtK,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDpB,EAAE,CACA,WAAW,EACX;IACEW,KAAK,EAAE;MACLK,IAAI,EAAE,SAAS;MACf6J,OAAO,EAAE9K,GAAG,CAAC+K;IACf,CAAC;IACD3K,EAAE,EAAE;MAAEc,KAAK,EAAElB,GAAG,CAACgL;IAAe;EAClC,CAAC,EACD,CAAChL,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAI4J,eAAe,GAAG,EAAE;AACxBlL,MAAM,CAACmL,aAAa,GAAG,IAAI;AAE3B,SAASnL,MAAM,EAAEkL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}