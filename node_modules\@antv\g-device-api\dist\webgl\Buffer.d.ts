import { B<PERSON><PERSON>, BufferDescriptor } from '../api';
import { BufferUsage, ResourceType } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export declare class Buffer_GL extends ResourceBase_GL implements Buffer {
    type: ResourceType.Buffer;
    gl_buffer_pages: WebGLBuffer[];
    gl_target: GLenum;
    usage: BufferUsage;
    byteSize: number;
    pageByteSize: number;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: BufferDescriptor;
    });
    setSubData(dstByteOffset: number, data: Uint8Array, srcByteOffset?: number, byteSize?: number): void;
    destroy(): void;
    private createBufferPage;
}
