{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.some.js\";\nimport store from '@/store';\nexport default {\n  inserted: function inserted(el, binding, vnode) {\n    var value = binding.value;\n    var all_permission = \"*:*:*\";\n    var permissions = store.getters && store.getters.permissions;\n    if (value && value instanceof Array && value.length > 0) {\n      var permissionFlag = value;\n      var hasPermissions = permissions.some(function (permission) {\n        return all_permission === permission || permissionFlag.includes(permission);\n      });\n      if (!hasPermissions) {\n        el.parentNode && el.parentNode.removeChild(el);\n      }\n    } else {\n      throw new Error('请设置操作权限标签值');\n    }\n  }\n};", "map": {"version": 3, "names": ["store", "inserted", "el", "binding", "vnode", "value", "all_permission", "permissions", "getters", "Array", "length", "permissionFlag", "hasPermissions", "some", "permission", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/src/directive/permission/hasPermi.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nexport default {\r\n  inserted(el, binding, vnode) {\r\n    const { value } = binding\r\n    const all_permission = \"*:*:*\";\r\n    const permissions = store.getters && store.getters.permissions\r\n\r\n    if (value && value instanceof Array && value.length > 0) {\r\n      const permissionFlag = value\r\n\r\n      const hasPermissions = permissions.some(permission => {\r\n        return all_permission === permission || permissionFlag.includes(permission)\r\n      })\r\n\r\n      if (!hasPermissions) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    } else {\r\n      throw new Error('请设置操作权限标签值')\r\n    }\r\n  }\r\n} "], "mappings": ";;;;;;;;AAAA,OAAOA,KAAK,MAAM,SAAS;AAE3B,eAAe;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,cAAc,GAAG,OAAO;IAC9B,IAAMC,WAAW,GAAGP,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAACD,WAAW;IAE9D,IAAIF,KAAK,IAAIA,KAAK,YAAYI,KAAK,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,cAAc,GAAGN,KAAK;MAE5B,IAAMO,cAAc,GAAGL,WAAW,CAACM,IAAI,CAAC,UAAAC,UAAU,EAAI;QACpD,OAAOR,cAAc,KAAKQ,UAAU,IAAIH,cAAc,CAACI,QAAQ,CAACD,UAAU,CAAC;MAC7E,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,EAAE;QACnBV,EAAE,CAACc,UAAU,IAAId,EAAE,CAACc,UAAU,CAACC,WAAW,CAACf,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIgB,KAAK,CAAC,YAAY,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}