/// <reference types="@webgpu/types" />
import type { QueryPool, QueryPoolType } from '../api';
import { ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class QueryPool_WebGPU extends ResourceBase_WebGPU implements QueryPool {
    type: ResourceType.QueryPool;
    querySet: GPUQuerySet;
    resolveBuffer: GPUBuffer;
    cpuBuffer: GPUBuffer;
    results: BigUint64Array | null;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: {
            elemCount: number;
            type: QueryPoolType;
        };
    });
    queryResultOcclusion(dstOffs: number): boolean | null;
    destroy(): void;
}
