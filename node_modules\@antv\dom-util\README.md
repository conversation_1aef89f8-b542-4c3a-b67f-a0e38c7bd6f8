# dom-util

> 为 `antv` 开发的轻量级工具方法库。


## 安装下载

> tnpm i --save @antv/util

```js
// 所有的 api 是都这么引入，名字不同而已
import { createDom, getStyle } from '@antv/dom-util';

const dom = createDom('<div></div>').appendTo(document.body);
```


## API 文档

> 目前使用到的、且推荐使用的 API 文档，不在文档内的不建议使用。
* createDom(string) `HTMLElement` 创建 dom 对象，支持复杂字符串
* addEventListener(dom, callback) `Object` 添加事件
```js
const handler = addEventListener(dom, function(ev) {

});
handler.remove();
```
* getHeight(dom) `Number`
* getWidth(dom) `Number`
* getOuterHeight(dom) `Number`
* getOuterWidth(dom) `Number`
* getRatio() `Number` 获取屏幕像素比
* getStyle(styleName) 获取样式
* modifyCss(obj) 修改样式


