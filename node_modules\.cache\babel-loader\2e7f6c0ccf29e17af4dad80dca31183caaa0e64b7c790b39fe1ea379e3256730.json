{"ast": null, "code": "var _typeof = require(\"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u5B9D/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\n//! moment.js locale configuration\n//! locale : Kyrgyz [ky]\n//! author : Chyngyz Arystan uulu : https://github.com/chyngyz\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    0: '-чү',\n    1: '-чи',\n    2: '-чи',\n    3: '-чү',\n    4: '-чү',\n    5: '-чи',\n    6: '-чы',\n    7: '-чи',\n    8: '-чи',\n    9: '-чу',\n    10: '-чу',\n    20: '-чы',\n    30: '-чу',\n    40: '-чы',\n    50: '-чү',\n    60: '-чы',\n    70: '-чи',\n    80: '-чи',\n    90: '-чу',\n    100: '-чү'\n  };\n  var ky = moment.defineLocale('ky', {\n    months: 'январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь'.split('_'),\n    monthsShort: 'янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек'.split('_'),\n    weekdays: 'Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби'.split('_'),\n    weekdaysShort: 'Жек_Дүй_Шей_Шар_Бей_Жум_Ише'.split('_'),\n    weekdaysMin: 'Жк_Дй_Шй_Шр_Бй_Жм_Иш'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Бүгүн саат] LT',\n      nextDay: '[Эртең саат] LT',\n      nextWeek: 'dddd [саат] LT',\n      lastDay: '[Кечээ саат] LT',\n      lastWeek: '[Өткөн аптанын] dddd [күнү] [саат] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ичинде',\n      past: '%s мурун',\n      s: 'бирнече секунд',\n      ss: '%d секунд',\n      m: 'бир мүнөт',\n      mm: '%d мүнөт',\n      h: 'бир саат',\n      hh: '%d саат',\n      d: 'бир күн',\n      dd: '%d күн',\n      M: 'бир ай',\n      MM: '%d ай',\n      y: 'бир жыл',\n      yy: '%d жыл'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(чи|чы|чү|чу)/,\n    ordinal: function ordinal(number) {\n      var a = number % 10,\n        b = number >= 100 ? 100 : null;\n      return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return ky;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "suffixes", "ky", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "a", "b", "week", "dow", "doy"], "sources": ["F:/常规项目/华通宝/adminweb/node_modules/moment/locale/ky.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Kyrgyz [ky]\n//! author : Chyngyz Arystan uulu : https://github.com/chyngyz\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var suffixes = {\n        0: '-чү',\n        1: '-чи',\n        2: '-чи',\n        3: '-чү',\n        4: '-чү',\n        5: '-чи',\n        6: '-чы',\n        7: '-чи',\n        8: '-чи',\n        9: '-чу',\n        10: '-чу',\n        20: '-чы',\n        30: '-чу',\n        40: '-чы',\n        50: '-чү',\n        60: '-чы',\n        70: '-чи',\n        80: '-чи',\n        90: '-чу',\n        100: '-чү',\n    };\n\n    var ky = moment.defineLocale('ky', {\n        months: 'январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь'.split(\n            '_'\n        ),\n        monthsShort: 'янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек'.split(\n            '_'\n        ),\n        weekdays: 'Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби'.split(\n            '_'\n        ),\n        weekdaysShort: 'Жек_Дүй_Шей_Шар_Бей_Жум_Ише'.split('_'),\n        weekdaysMin: 'Жк_Дй_Шй_Шр_Бй_Жм_Иш'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Бүгүн саат] LT',\n            nextDay: '[Эртең саат] LT',\n            nextWeek: 'dddd [саат] LT',\n            lastDay: '[Кечээ саат] LT',\n            lastWeek: '[Өткөн аптанын] dddd [күнү] [саат] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s ичинде',\n            past: '%s мурун',\n            s: 'бирнече секунд',\n            ss: '%d секунд',\n            m: 'бир мүнөт',\n            mm: '%d мүнөт',\n            h: 'бир саат',\n            hh: '%d саат',\n            d: 'бир күн',\n            dd: '%d күн',\n            M: 'бир ай',\n            MM: '%d ай',\n            y: 'бир жыл',\n            yy: '%d жыл',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(чи|чы|чү|чу)/,\n        ordinal: function (number) {\n            var a = number % 10,\n                b = number >= 100 ? 100 : null;\n            return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return ky;\n\n})));\n"], "mappings": ";AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,QAAQ,GAAG;IACX,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,GAAG,EAAE;EACT,CAAC;EAED,IAAIC,EAAE,GAAGF,MAAM,CAACG,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,iFAAiF,CAACC,KAAK,CAC3F,GACJ,CAAC;IACDC,WAAW,EAAE,oDAAoD,CAACD,KAAK,CACnE,GACJ,CAAC;IACDE,QAAQ,EAAE,0DAA0D,CAACF,KAAK,CACtE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,uCAAuC;MACjDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,uBAAuB;IAC/CC,OAAO,EAAE,SAATA,OAAOA,CAAYC,MAAM,EAAE;MACvB,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;QACfE,CAAC,GAAGF,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;MAClC,OAAOA,MAAM,IAAIxC,QAAQ,CAACwC,MAAM,CAAC,IAAIxC,QAAQ,CAACyC,CAAC,CAAC,IAAIzC,QAAQ,CAAC0C,CAAC,CAAC,CAAC;IACpE,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO5C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}