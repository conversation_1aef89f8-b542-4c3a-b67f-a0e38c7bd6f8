/**
 * @fileoverview 矩阵运算，本来是要引入 gl-matrix, 但是考虑到 g-mobile 对大小有限制，同时 g-webgl 使用的 matrix 不一致
 * 所以，这里仅实现 2D 几个运算，上层自己引入 gl-matrix
 * <AUTHOR>
 */
/**
 * 3阶矩阵相乘
 * @param {number[]} a 矩阵1
 * @param {number[]} b 矩阵2
 */
export declare function multiplyMatrix(a: number[], b: number[]): any[];
/**
 * 3阶矩阵同2阶向量相乘
 * @param {number[]} m 矩阵
 * @param {number[]} v 二阶向量
 */
export declare function multiplyVec2(m: number[], v: number[]): any[];
/**
 * 矩阵的逆
 * @param {number[]} a 矩阵
 */
export declare function invert(a: number[]): any[];
