{"version": 3, "file": "index.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAmB,UAAID,IAEvBD,EAAgB,UAAIC,GACrB,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,6BCqClF,QAnCkB,SAACI,EAAsBC,GAC/B,IAAAC,EAAiBF,EAAS,MAAnBG,EAAUH,EAAS,MAC5BI,EAAmB,GAEnBC,EAEF,CAAC,EAEL,IAAKH,EACH,MAAM,IAAII,MAAM,uBAuBlB,OApBIJ,GACFA,EAAMK,SAAQ,SAACC,EAAMC,GACnBJ,EAAQG,EAAKE,IAAMD,EAEnBL,EAAOO,KADe,GAExB,IAGER,GACFA,EAAMI,SAAQ,SAACK,GACL,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzBG,EAASV,EAAQQ,GACjBG,EAASX,EAAQS,IACjBC,GAAqB,IAAXA,IAAmBC,GAAqB,IAAXA,IAC7CZ,EAAOW,GAAQC,GAAU,EACpBf,IACHG,EAAOY,GAAQD,GAAU,GAE7B,IAEKX,CACT,ECnCA,IAAMa,EAAoB,SAACC,EAAGC,GAC5B,OAAID,IAAMC,CAKZ,EAKA,aAKE,WAAYC,EAAOC,QAAA,IAAAA,IAAAA,EAAA,MACjBnC,KAAKkC,MAAQA,EACblC,KAAKmC,KAAOA,CACd,CAKF,OAHE,YAAAC,SAAA,SAASC,GACP,OAAOA,EAAWA,EAASrC,KAAKkC,OAAS,UAAGlC,KAAKkC,MACnD,EACF,EAbA,G,QAeA,WAOE,WAAYI,QAAA,IAAAA,IAAAA,EAAA,GACVtC,KAAKuC,KAAO,KACZvC,KAAKwC,KAAO,KACZxC,KAAKyC,QAAUH,CACjB,CA+MF,OAzME,YAAAI,QAAA,SAAQR,GAEN,IAAMS,EAAU,IAAIC,EAAeV,EAAOlC,KAAKuC,MAO/C,OANAvC,KAAKuC,KAAOI,EAEP3C,KAAKwC,OACRxC,KAAKwC,KAAOG,GAGP3C,IACT,EAMA,YAAA6C,OAAA,SAAOX,GACL,IAAMS,EAAU,IAAIC,EAAeV,GAGnC,OAAKlC,KAAKuC,MAQVvC,KAAKwC,KAAKL,KAAOQ,EACjB3C,KAAKwC,KAAOG,EAEL3C,OAVLA,KAAKuC,KAAOI,EACZ3C,KAAKwC,KAAOG,EAEL3C,KAQX,EAMA,YAAA8C,OAAA,SAAOZ,GACL,IAAKlC,KAAKuC,KACR,OAAO,KAMT,IAHA,IAAIQ,EAAa,KAGV/C,KAAKuC,MAAQvC,KAAKyC,QAAQzC,KAAKuC,KAAKL,MAAOA,IAChDa,EAAa/C,KAAKuC,KAClBvC,KAAKuC,KAAOvC,KAAKuC,KAAKJ,KAGxB,IAAIa,EAAchD,KAAKuC,KAEvB,GAAoB,OAAhBS,EAEF,KAAOA,EAAYb,MACbnC,KAAKyC,QAAQO,EAAYb,KAAKD,MAAOA,IACvCa,EAAaC,EAAYb,KACzBa,EAAYb,KAAOa,EAAYb,KAAKA,MAEpCa,EAAcA,EAAYb,KAUhC,OAJInC,KAAKyC,QAAQzC,KAAKwC,KAAKN,MAAOA,KAChClC,KAAKwC,KAAOQ,GAGPD,CACT,EAMA,YAAAE,KAAA,SAAK,G,IAAE,IAAAf,MAAAA,OAAK,IAAG,OAAAgB,EAAS,EAAE,IAAAb,SAAAA,OAAQ,IAAG,OAAAa,EAAS,EAC5C,IAAKlD,KAAKuC,KACR,OAAO,KAKT,IAFA,IAAIS,EAAchD,KAAKuC,KAEhBS,GAAa,CAElB,GAAIX,GAAYA,EAASW,EAAYd,OACnC,OAAOc,EAIT,QAAcE,IAAVhB,GAAuBlC,KAAKyC,QAAQO,EAAYd,MAAOA,GACzD,OAAOc,EAGTA,EAAcA,EAAYb,I,CAG5B,OAAO,IACT,EAKA,YAAAgB,WAAA,WACE,IAAMC,EAAcpD,KAAKwC,KAEzB,GAAIxC,KAAKuC,OAASvC,KAAKwC,KAIrB,OAFAxC,KAAKuC,KAAO,KACZvC,KAAKwC,KAAO,KACLY,EAIT,IADA,IAAIJ,EAAchD,KAAKuC,KAChBS,EAAYb,MACZa,EAAYb,KAAKA,KAGpBa,EAAcA,EAAYb,KAF1Ba,EAAYb,KAAO,KAQvB,OAFAnC,KAAKwC,KAAOQ,EAELI,CACT,EAKA,YAAAC,WAAA,WACE,IAAKrD,KAAKuC,KACR,OAAO,KAGT,IAAMe,EAActD,KAAKuC,KASzB,OAPIvC,KAAKuC,KAAKJ,KACZnC,KAAKuC,KAAOvC,KAAKuC,KAAKJ,MAEtBnC,KAAKuC,KAAO,KACZvC,KAAKwC,KAAO,MAGPc,CACT,EAMA,YAAAC,UAAA,SAAUC,GAAV,WAEE,OADAA,EAAOnC,SAAQ,SAACa,GAAU,SAAKW,OAAOX,EAAZ,IACnBlC,IACT,EAKA,YAAAyD,QAAA,WAKE,IAJA,IAAMzC,EAAQ,GAEVgC,EAAchD,KAAKuC,KAEhBS,GACLhC,EAAMS,KAAKuB,GACXA,EAAcA,EAAYb,KAG5B,OAAOnB,CACT,EAKA,YAAA0C,QAAA,WAIE,IAHA,IAAIV,EAAchD,KAAKuC,KACnBoB,EAAW,KACXC,EAAW,KACRZ,GAELY,EAAWZ,EAAYb,KAGvBa,EAAYb,KAAOwB,EAGnBA,EAAWX,EACXA,EAAcY,EAGhB5D,KAAKwC,KAAOxC,KAAKuC,KACjBvC,KAAKuC,KAAOoB,CACd,EAEA,YAAAvB,SAAA,SAASC,GACP,YADO,IAAAA,IAAAA,OAAA,GACArC,KAAKyD,UACTI,KAAI,SAACvC,GAAS,OAAAA,EAAKc,SAASC,EAAd,IACdD,UACL,EACF,EA1NA,G,ECxBA,WAGE,aACEpC,KAAK8D,WAAa,IAAI,CACxB,CAsCF,OAjCS,YAAAC,QAAP,WACE,OAAQ/D,KAAK8D,WAAWvB,IAC1B,EAKO,YAAAyB,KAAP,WACE,OAAKhE,KAAK8D,WAAWvB,KAGdvC,KAAK8D,WAAWvB,KAAKL,MAFnB,IAGX,EAMO,YAAA+B,QAAP,SAAe/B,GACblC,KAAK8D,WAAWjB,OAAOX,EACzB,EAKO,YAAAgC,QAAP,WACE,IAAMC,EAAanE,KAAK8D,WAAWT,aACnC,OAAOc,EAAaA,EAAWjC,MAAQ,IACzC,EAEO,YAAAE,SAAP,SAAgBC,GACd,OAAOrC,KAAK8D,WAAW1B,SAASC,EAClC,EACF,EA3CA,GCMO,IAAM+B,EAAe,SAACC,EAAgBpD,EAA0BqD,QAA1B,IAAArD,IAAAA,EAAA,IAC3C,IAAMsD,EAAetD,EAAMuD,QAAO,SAAA9C,GAAQ,OAAAA,EAAKC,SAAW0C,GAAU3C,EAAKE,SAAWyC,CAA1C,IAC1C,MAAa,WAATC,EAKKC,EAAaC,QAHQ,SAAC9C,GAC3B,OAAOA,EAAKC,SAAW0C,CACzB,IACgDR,KAAI,SAACnC,GAAS,OAAAA,EAAKE,MAAL,IAEnD,WAAT0C,EAKKC,EAAaC,QAHQ,SAAC9C,GAC3B,OAAOA,EAAKE,SAAWyC,CACzB,IACgDR,KAAI,SAACnC,GAAS,OAAAA,EAAKC,MAAL,IAOzD4C,EAAaV,KAHQ,SAACnC,GAC3B,OAAOA,EAAKC,SAAW0C,EAAS3C,EAAKE,OAASF,EAAKC,MACrD,GAEF,EAgBa8C,EAAmB,SAACJ,EAAgBpD,GAC/C,OAAOA,EAAMuD,QAAO,SAAA9C,GAAQ,OAAAA,EAAKC,SAAW0C,GAAU3C,EAAKE,SAAWyC,CAA1C,GAC9B,EAMaK,EAAW,SAACC,QAAA,IAAAA,IAAAA,EAAA,GACvB,IAAMC,EAAU,UAAGC,KAAKC,UAAWC,MAAM,KAAK,GAAGC,OAAO,EAAG,GACrDC,EAAU,UAAGJ,KAAKC,UAAWC,MAAM,KAAK,GAAGC,OAAO,EAAG,GAC3D,MAAO,UAAGL,EAAK,YAAIC,GAAO,OAAGK,EAC/B,ECnDO,IA4CMC,EAAgC,SAACpE,GAiD5C,IAhDQ,MAA2BA,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxBkE,EAA0B,GAC1BC,EAAU,CAAC,EACXC,EAAU,CAAC,EACXC,EAAU,CAAC,EACXC,EAAgC,GAClCZ,EAAQ,EAENa,EAAe,SAAClE,GAEpB+D,EAAQ/D,EAAKE,IAAMmD,EACnBW,EAAQhE,EAAKE,IAAMmD,EACnBA,GAAS,EACTQ,EAAU1D,KAAKH,GACf8D,EAAQ9D,EAAKE,KAAM,EAInB,IADA,IAAMiE,EAAYrB,EAAa9C,EAAKE,GAAIP,EAAO,UAAUuD,QAAO,SAACkB,GAAM,OAAA1E,EAAM6C,KAAI,SAAAvC,GAAQ,OAAAA,EAAKE,EAAL,IAASmE,QAAQD,IAAM,CAAzC,I,WAC9DnE,GACP,IAAMqE,EAAeH,EAAUlE,GAC/B,GAAK8D,EAAQO,IAA2C,IAA1BP,EAAQO,GAO3BR,EAAQQ,KAEjBN,EAAQhE,EAAKE,IAAMqD,KAAKgB,IAAIP,EAAQhE,EAAKE,IAAK6D,EAAQO,SATG,CACzD,IAAME,EAAa9E,EAAMwD,QAAO,SAAAlD,GAAQ,OAAAA,EAAKE,KAAOoE,CAAZ,IACpCE,EAAWC,OAAS,GACtBP,EAAaM,EAAW,IAG1BR,EAAQhE,EAAKE,IAAMqD,KAAKgB,IAAIP,EAAQhE,EAAKE,IAAK8D,EAAQM,G,GARjDrE,EAAI,EAAGA,EAAIkE,EAAUM,OAAQxE,I,EAA7BA,GAgBT,GAAI+D,EAAQhE,EAAKE,MAAQ6D,EAAQ/D,EAAKE,IAAK,CAEzC,IADA,IAAMwE,EAAY,GACXb,EAAUY,OAAS,GAAG,CAC3B,IAAME,EAAUd,EAAUe,MAG1B,GAFAd,EAAQa,EAAQzE,KAAM,EACtBwE,EAAUvE,KAAKwE,GACXA,IAAY3E,EAAM,K,CAEpB0E,EAAUD,OAAS,GACrBR,EAAc9D,KAAKuE,E,CAGzB,EAEmB,MAAAhF,EAAA,eAAO,CAArB,IAAMM,EAAI,KACR+D,EAAQ/D,EAAKE,KAA4B,IAArB6D,EAAQ/D,EAAKE,KACpCgE,EAAalE,E,CAIjB,OAAOiE,CACT,EAEe,SAASY,EAAuBrF,EAAsBC,GACnE,OAAIA,EAAiBmE,EAA8BpE,GAvGZ,SAACA,GAqBxC,IApBQ,MAA2BA,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxBsE,EAAgC,GAChCa,EAAU,CAAC,EACXjB,EAA0B,GAE1BK,EAAe,SAAClE,GACpB6D,EAAU1D,KAAKH,GACf8E,EAAQ9E,EAAKE,KAAM,EAEnB,IADA,IAAMiE,EAAYrB,EAAa9C,EAAKE,GAAIP,G,WAC/BM,GACP,IAAM8E,EAAWZ,EAAUlE,GAC3B,IAAK6E,EAAQC,GAAW,CACtB,IAAMP,EAAa9E,EAAMwD,QAAO,SAAAlD,GAAQ,OAAAA,EAAKE,KAAO6E,CAAZ,IACpCP,EAAWC,OAAS,GACtBP,EAAaM,EAAW,G,GALrBvE,EAAI,EAAGA,EAAIkE,EAAUM,SAAUxE,E,EAA/BA,EASX,EAESA,EAAI,EAAGA,EAAIP,EAAM+E,OAAQxE,IAAK,CACrC,IAAMD,EAAON,EAAMO,GACnB,IAAK6E,EAAQ9E,EAAKE,IAAK,CAErBgE,EAAalE,GAEb,IADA,IAAM0E,EAAY,GACXb,EAAUY,OAAS,GACxBC,EAAUvE,KAAK0D,EAAUe,OAE3BX,EAAc9D,KAAKuE,E,EAGvB,OAAOT,CACT,CAsESe,CAA0BxF,EACnC,CC9GA,IAAMyF,EAAS,SAACzF,GACd,IAAM0F,EAAsB,CAAC,EACrB,EAA2B1F,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAiB9B,OAfAD,EAAMK,SAAQ,SAACC,GACbkF,EAAQlF,EAAKE,IAAM,CACjB+E,OAAQ,EACRE,SAAU,EACVC,UAAW,EAEf,IAEAzF,EAAMI,SAAQ,SAACK,GACb8E,EAAQ9E,EAAKC,QAAQ4E,SACrBC,EAAQ9E,EAAKC,QAAQ+E,YACrBF,EAAQ9E,EAAKE,QAAQ2E,SACrBC,EAAQ9E,EAAKE,QAAQ6E,UACvB,IAEOD,CACT,EAEA,UCQA,SAASG,EACP7F,EACAkC,EACA4D,EACAC,EACA9F,QAAA,IAAAA,IAAAA,GAAA,GAEA8F,EAAUC,MAAM,CACdC,QAAS/D,EACTgE,SAAUJ,IAGJ,MAAe9F,EAAS,MAEhCsD,EAAapB,OAFA,IAAG,KAAE,EAEejC,EAAW,cAAWmC,GAAW7B,SAAQ,SAACuC,GAEvEiD,EAAUI,eAAe,CACvBD,SAAUJ,EACVG,QAAS/D,EACTb,KAAMyB,KAGR+C,EAA0B7F,EAAW8C,EAAUZ,EAAa6D,EAAW9F,EAE3E,IAEA8F,EAAUK,MAAM,CACdH,QAAS/D,EACTgE,SAAUJ,GAEd,CAQe,SAASO,EACtBrG,EACAsG,EACAP,EACA9F,QAAA,IAAAA,IAAAA,GAAA,GAEA4F,EAA0B7F,EAAWsG,EAAa,GAzEpD,SAAuBP,QAAA,IAAAA,IAAAA,EAAiC,CAAC,GACvD,IAKQQ,EALFC,EAAoBT,EAEpBU,EAAe,WAAO,EAEtBC,GACEH,EAAO,CAAC,EACP,SAAC,G,IAAElF,EAAI,OACZ,OAAKkF,EAAKlF,KACRkF,EAAKlF,IAAQ,GACN,EAGX,GAOF,OAJAmF,EAAkBL,eAAiBJ,EAAUI,gBAAkBO,EAC/DF,EAAkBR,MAAQD,EAAUC,OAASS,EAC7CD,EAAkBJ,MAAQL,EAAUK,OAASK,EAEtCD,CACT,CAoDwD,CAAcT,GAAY9F,EAClF,CCxEA,IAsFa0G,EAA2B,SAAC3G,EAAsB4G,EAAoBC,G,aAAA,IAAAA,IAAAA,GAAA,GAKjF,IAJA,IAAMC,EAAY,GAIM,MAHLzB,EAAuBrF,GAAW,GAG7B,eAAY,CAA/B,IAAMkF,EAAS,KAClB,GAAKA,EAAUD,OASf,IARA,IAAMrG,EAAOsG,EAAU,GACjB6B,EAASnI,EAAK8B,GAEdsG,EAAQ,CAACpI,GACTqI,IAAM,MAAMF,GAASnI,EAAI,GACzBsI,IAAI,MAAMH,GAAS,IAAII,IAAK,GAG3BH,EAAM/B,OAAS,GAIpB,IAHA,IAAMmC,EAAUJ,EAAM5B,MAChBiC,EAAYD,EAAQ1G,GACpBiE,EAAYrB,EAAa+D,EAAWrH,EAAUG,O,WAC3CM,G,MACD6G,EAAa3C,EAAUlE,GACvB8E,EAAWvF,EAAUE,MAAMiC,MAAK,SAAA3B,GAAQ,OAAAA,EAAKE,KAAO4G,CAAZ,IAE9C,GAAIA,IAAeD,EAEjBP,EAAUnG,OAAI,MAAI2G,GAAaF,EAAO,SACjC,GAAME,KAAcJ,GAKpB,IAAKA,EAAKG,GAAWE,IAAIhC,GAAW,CAKzC,IAHA,IAAIiC,GAAa,EACXC,EAAY,CAAClC,EAAU6B,GACzBM,EAAIT,EAAOI,GACRH,EAAKI,GAAYK,OAAST,EAAKI,GAAYC,IAAIG,KACpDD,EAAU9G,KAAK+G,GACXA,IAAMT,EAAOS,EAAEhH,MACdgH,EAAIT,EAAOS,EAAEhH,IAkBpB,GAhBA+G,EAAU9G,KAAK+G,GAEXd,GAAWC,GAEbW,GAAa,EACTC,EAAUG,WAAU,SAACpH,GAAS,OAAAoG,EAAQ/B,QAAQrE,EAAKE,KAAO,CAA5B,KAAkC,IAClE8G,GAAa,IAENZ,IAAYC,GAEjBY,EAAUG,WAAU,SAACpH,GAAS,OAAAoG,EAAQ/B,QAAQrE,EAAKE,KAAO,CAA5B,KAAkC,IAClE8G,GAAa,GAKbA,EAAY,CAEd,IADA,IAAMK,EAAQ,CAAC,EACNhE,EAAQ,EAAGA,EAAQ4D,EAAUxC,OAAQpB,GAAS,EACrDgE,EAAMJ,EAAU5D,EAAQ,GAAGnD,IAAM+G,EAAU5D,GAEzC4D,EAAUxC,SACZ4C,EAAMJ,EAAUA,EAAUxC,OAAS,GAAGvE,IAAM+G,EAAU,IAExDX,EAAUnG,KAAKkH,E,CAGjBX,EAAKI,GAAYQ,IAAIV,E,OAxCrBH,EAAOK,GAAcF,EACrBJ,EAAMrG,KAAK4E,GACX2B,EAAKI,GAAc,IAAIH,IAAI,CAACC,G,EAXvB3G,EAAI,EAAGA,EAAIkE,EAAUM,OAAQxE,GAAK,E,EAAlCA,E,CAuDb,OAAOqG,CACT,EAWaiB,EAAyB,SAAC/H,EAAsB4G,EAAoBC,QAAA,IAAAA,IAAAA,GAAA,GAoE/E,IAnEA,IAAMmB,EAAO,GACPC,EAAU,IAAId,IACde,EAAI,GACJpB,EAAY,GACZqB,EAEF,CAAC,EACCC,EAAW,CAAC,EAiBZC,EAAU,SAAC7H,EAAkB8H,EAAmBC,GACpD,IAAIC,GAAS,EACb,GAAI5B,IAAuB,IAAZC,GAAqBD,EAAQ/B,QAAQrE,EAAKE,KAAO,EAAG,OAAO8H,EAC1ER,EAAKrH,KAAKH,GACVyH,EAAQH,IAAItH,GAGZ,IADA,IAAMmE,EAAY4D,EAAQ/H,EAAKE,IACtBD,EAAI,EAAGA,EAAIkE,EAAUM,OAAQxE,GAAK,EAEzC,IADM8E,EAAW4C,EAASxD,EAAUlE,OACnB6H,EAAO,CAEtB,IADA,IAAMT,EAAQ,CAAC,EACNhE,EAAQ,EAAGA,EAAQmE,EAAK/C,OAAQpB,GAAS,EAChDgE,EAAMG,EAAKnE,EAAQ,GAAGnD,IAAMsH,EAAKnE,GAE/BmE,EAAK/C,SACP4C,EAAMG,EAAKA,EAAK/C,OAAS,GAAGvE,IAAMsH,EAAK,IAEzClB,EAAUnG,KAAKkH,GACfW,GAAS,C,MACCP,EAAQV,IAAIhC,IAClB8C,EAAQ9C,EAAU+C,EAAOC,KAC3BC,GAAS,GAKf,GAAIA,GAxCU,SAACC,GAEf,IADA,IAAMzB,EAAQ,CAACyB,GACRzB,EAAM/B,OAAS,GAAG,CACvB,IAAMzE,EAAOwG,EAAM5B,MACf6C,EAAQV,IAAI/G,KACdyH,EAAQjG,OAAOxB,GACf0H,EAAE1H,EAAKE,IAAIH,SAAQ,SAACqE,GAClBoC,EAAMrG,KAAKiE,EACb,IACAsD,EAAE1H,EAAKE,IAAIgI,Q,CAGjB,CA6BIC,CAAQnI,QAER,IAASC,EAAI,EAAGA,EAAIkE,EAAUM,OAAQxE,GAAK,EAAG,CAC5C,IAAM8E,EAAW4C,EAASxD,EAAUlE,IAC/ByH,EAAE3C,EAAS7E,IAAI6G,IAAI/G,IACtB0H,EAAE3C,EAAS7E,IAAIoH,IAAItH,E,CAKzB,OADAwH,EAAK5C,MACEoD,CACT,EAEQ,EAAexI,EAAS,MAAxBE,OAAK,IAAG,KAAE,EAGTO,EAAI,EAAGA,EAAIP,EAAM+E,OAAQxE,GAAK,EAAG,CACxC,IAAMD,EAAON,EAAMO,GACb8C,EAAS/C,EAAKE,GACpB0H,EAAS7E,GAAU9C,EACnB0H,EAAS1H,GAAKD,C,CAGhB,GAAIoG,GAAWC,E,gBACJpG,GACP,IAAM8C,EAASqD,EAAQnG,GACvB2H,EAASlI,EAAMO,GAAGC,IAAM0H,EAAS7E,GACjC6E,EAAS7E,GAAU,EACnB4E,EAAS,GAAKjI,EAAMiC,MAAK,SAAA3B,GAAQ,OAAAA,EAAKE,KAAO6C,CAAZ,IACjC4E,EAASC,EAASlI,EAAMO,GAAGC,KAAOR,EAAMO,E,EAL1C,IAASA,EAAI,EAAGA,EAAImG,EAAQ3B,OAAQxE,I,EAA3BA,E,CAiDX,IAvCA,IAAMmI,EAAqB,SAACC,GAK1B,I,MAJIC,EACAC,EAASC,IAGJvI,EAAI,EAAGA,EAAIoI,EAAW5D,OAAQxE,GAAK,EAE1C,IADA,IAAMwI,EAAOJ,EAAWpI,GACfyI,EAAI,EAAGA,EAAID,EAAKhE,OAAQiE,IAAK,CACpC,IAAM,EAAUd,EAASa,EAAKC,GAAGxI,IAC7B,EAAUqI,IACZA,EAAS,EACTD,EAAarI,E,CAKnB,IAAMyE,EAAY2D,EAAWC,GACvBP,EAAU,GAChB,IAAS9H,EAAI,EAAGA,EAAIyE,EAAUD,OAAQxE,GAAK,EAAG,CAC5C,IAAMD,EAAO0E,EAAUzE,GACvB8H,EAAQ/H,EAAKE,IAAM,GACnB,IAAuB,UAAA4C,EAAa9C,EAAKE,GAAIV,EAAUG,MAAO,UAAUuD,QAAO,SAACkB,GAAM,OAAAM,EAAUnC,KAAI,SAAAoG,GAAK,OAAAA,EAAEzI,EAAF,IAAMmE,QAAQD,IAAM,CAAvC,IAA/D,eAA0G,CAA5H,IAAMW,EAAQ,KAEbA,IAAa/E,EAAKE,KAAoB,IAAZmG,GAAqBD,EAAQ/B,QAAQrE,EAAKE,KAAO,EAG7E6H,EAAQ/H,EAAKE,IAAIC,KAAKyH,EAAS7C,IAF/BuB,EAAUnG,OAAI,MAAIH,EAAKE,IAAKF,EAAI,G,EAOtC,MAAO,CACL0E,UAAS,EACTqD,QAAO,EACPQ,OAAM,EAEV,EAEIK,EAAU,EACPA,EAAUlJ,EAAM+E,QAAQ,CAC7B,IAAMoE,EAAgBnJ,EAAMwD,QAAO,SAACkB,GAAM,OAAAwD,EAASxD,EAAElE,KAAO0I,CAAlB,IACpCE,EAAOlF,EAA8B,CAAElE,MAAOmJ,EAAelJ,MAAOH,EAAUG,QAASuD,QAC3F,SAACwB,GAAc,OAAAA,EAAUD,OAAS,CAAnB,IAEjB,GAAoB,IAAhBqE,EAAKrE,OAAc,MAEvB,IAAMsE,EAAMX,EAAmBU,GACvBP,EAA+BQ,EAAG,OAA1BhB,EAAuBgB,EAAG,QAAjBrE,EAAcqE,EAAG,UAC1C,KAAIrE,EAAUD,OAAS,GAUrB,MATAC,EAAU3E,SAAQ,SAACC,GACjB0H,EAAE1H,EAAKE,IAAM,IAAIyG,GACnB,IACA,IAAMqC,EAAYrB,EAASY,GAE3B,GAAInC,GAAWC,IAA8C,IAAnCD,EAAQ/B,QAAQ2E,EAAU9I,IAAY,OAAOoG,EACvEuB,EAAQmB,EAAWA,EAAWjB,GAC9Ba,EAAUL,EAAS,C,CAKvB,OAAOjC,CACT,EAoBA,QApV4B,SAAC9G,GAG3B,IAAI6H,EAEA,KAEI,EAAe7H,EAAS,MAE1ByJ,EAAe,CAAC,EAGhBC,EAAe,CAAC,EAGhBC,EAAc,CAAC,EAGfC,EAAa,CAAC,QAXP,IAAG,KAAE,GAcZrJ,SAAQ,SAACC,GACbkJ,EAAalJ,EAAKE,IAAMF,CAC1B,IA6CA,IA3CA,IAAMuF,EAAiC,CACrCC,MAAO,SAAC,G,IAAW9D,EAAW,UAAY4D,EAAY,WACpD,GAAI6D,EAAYzH,GAAc,CAE5B2F,EAAQ,CAAC,EAKT,IAHA,IAAIgC,EAAmB3H,EACnB4H,EAAoBhE,EAEjBgE,IAAsB5H,GAC3B2F,EAAMgC,GAAoBC,EAC1BD,EAAmBC,EACnBA,EAAoBL,EAAaK,GAGnCjC,EAAMgC,GAAoBC,C,MAG1BH,EAAYzH,GAAeA,SACpBwH,EAAaxH,GAGpBuH,EAAavH,GAAe4D,CAEhC,EACAM,MAAO,SAAC,G,IAAWlE,EAAW,UAG5B0H,EAAW1H,GAAeA,SACnByH,EAAYzH,EACrB,EACAiE,eAAgB,SAAC,G,IAAQrD,EAAQ,OAE/B,OAAI+E,IAKI+B,EAAW9G,EACrB,GAIKvD,OAAOwK,KAAKL,GAAczE,QAI/B,EAAIjF,EAFuBT,OAAOwK,KAAKL,GAAc,GAElB3D,GAGrC,OAAO8B,CACT,ECoIO,SAASmC,EAAcC,EAAIC,EAAMC,GACtC,GAAIA,GAA6B,IAArBC,UAAUnF,OAAc,IAAK,IAA4BoF,EAAxB5J,EAAI,EAAG6J,EAAIJ,EAAKjF,OAAYxE,EAAI6J,EAAG7J,KACxE4J,GAAQ5J,KAAKyJ,IACRG,IAAIA,EAAKE,MAAM1K,UAAU2K,MAAMzK,KAAKmK,EAAM,EAAGzJ,IAClD4J,EAAG5J,GAAKyJ,EAAKzJ,IAGrB,OAAOwJ,EAAGQ,OAAOJ,GAAME,MAAM1K,UAAU2K,MAAMzK,KAAKmK,GACpD,CArE6B3K,OAAOmL,OA0GXnL,OAAOmL,OAyDkB,mBAApBC,iBAAiCA,gBC3T/D,IAAMrJ,EAAW,GAAGA,SAIpB,QAFe,SAACF,EAAYoC,GAA0B,OAAAlC,EAASvB,KAAKqB,KAAW,WAAaoC,EAAO,GAA7C,ECKtD,WAAgBpC,GACd,OAAOwJ,EAAOxJ,EAAO,WACtB,ECPD,WAAgBA,GACd,OAAOmJ,MAAMM,QACXN,MAAMM,QAAQzJ,GACdwJ,EAAOxJ,EAAO,QACjB,ECHY7B,OAAOwK,KCHpB,IAAMe,EAAeP,MAAM1K,UACZiL,EAAaC,OACZD,EAAajG,QCAd0F,MAAM1K,UAAUkL,OC2B/B,MC3BA,WAAgBC,GACd,OAAOJ,EAAOI,EAAK,SACpB,ECJa,SAAUC,EAAKC,EAAYC,QAAA,IAAAA,IAAAA,EAAA,IAAYC,KACnD,IAAMC,EAAI,GAEV,GAAId,MAAMM,QAAQK,GAChB,IAAK,IAAIzK,EAAI,EAAG6K,EAAMJ,EAAIjG,OAAQxE,EAAI6K,EAAK7K,IAAM,CAC/C,IAAM8K,EAAOL,EAAIzK,GAEZ0K,EAAM5D,IAAIgE,KACbF,EAAE1K,KAAK4K,GACPJ,EAAMK,IAAID,GAAM,G,CAItB,OAAOF,CACT,CCXuB9L,OAAOM,UAAUC,eCDtB2L,OAAOC,WAAYD,OAAOC,UCFvB3H,KAAK4H,GCAX5H,KAAK4H,GCILpM,OAAOmD,OCJFnD,OAAOM,UC8B3B,QA5Bc,SAAR+L,EAAiBjM,GACrB,GAAmB,iBAARA,GAA4B,OAARA,EAC7B,OAAOA,EAET,IAAIkM,EACJ,GAAIhB,EAAQlL,GAAM,CAChBkM,EAAM,GACN,IAAK,IAAIpL,EAAI,EAAG6J,EAAI3K,EAAIsF,OAAQxE,EAAI6J,EAAG7J,IACf,iBAAXd,EAAIc,IAA6B,MAAVd,EAAIc,GACpCoL,EAAIpL,GAAKmL,EAAMjM,EAAIc,IAEnBoL,EAAIpL,GAAKd,EAAIc,E,MAKjB,IAAK,IAAMqL,KADXD,EAAM,GACUlM,EACQ,iBAAXA,EAAImM,IAA6B,MAAVnM,EAAImM,GACpCD,EAAIC,GAAKF,EAAMjM,EAAImM,IAEnBD,EAAIC,GAAKnM,EAAImM,GAKnB,OAAOD,CACT,ECvBuBtM,OAAOM,UAAUC,eCKxC,QAVqB,SAASsB,GAO5B,MAAwB,iBAAVA,GAAgC,OAAVA,CACtC,ECEA,EAVoB,SAASA,GAO3B,OAAiB,OAAVA,GAAmC,mBAAVA,GAAwB2K,SAAS3K,EAAM6D,OACzE,ECqCA,EAzCgB,SAAV+G,EAAW5K,EAAY6K,GAC3B,GAAI7K,IAAU6K,EACZ,OAAO,EAET,IAAK7K,IAAU6K,EACb,OAAO,EAET,GAAIC,EAAS9K,IAAU8K,EAASD,GAC9B,OAAO,EAET,GAAIE,EAAY/K,IAAU+K,EAAYF,GAAQ,CAC5C,GAAI7K,EAAM6D,SAAWgH,EAAMhH,OACzB,OAAO,EAGT,IADA,IAAI4G,GAAM,EACDpL,EAAI,EAAGA,EAAIW,EAAM6D,SACxB4G,EAAMG,EAAQ5K,EAAMX,GAAIwL,EAAMxL,KADEA,KAMlC,OAAOoL,C,CAET,GAAIO,EAAahL,IAAUgL,EAAaH,GAAQ,CAC9C,IAAMI,EAAY9M,OAAOwK,KAAK3I,GACxBkL,EAAY/M,OAAOwK,KAAKkC,GAC9B,GAAII,EAAUpH,SAAWqH,EAAUrH,OACjC,OAAO,EAGT,IADI4G,GAAM,EACDpL,EAAI,EAAGA,EAAI4L,EAAUpH,SAC5B4G,EAAMG,EAAQ5K,EAAMiL,EAAU5L,IAAKwL,EAAMI,EAAU5L,MADfA,KAMtC,OAAOoL,C,CAET,OAAO,CACT,EClCA,IAAIU,ECNmBhN,OAAOM,UAAUC,eCKxC,SAAgB0M,EAAaC,GAC3B,IAAKC,EAAWF,GACd,MAAM,IAAIG,UAAU,uBAiBL,IAAIvB,GAGtB,CFhBD,EACE,SAACwB,EAAWC,QAAA,IAAAA,IAAAA,EAAA,IACF,IAAAC,EAA6DD,EAAIC,SAAvDC,EAAmDF,EAAIE,WAA3CC,EAAuCH,EAAIG,WAA/BC,EAA2BJ,EAAII,UAApBC,EAAgBL,EAAIK,YAKzE,OAJKX,IACHA,EAAMY,SAASC,cAAc,UAAUC,WAAW,OAEpDd,EAAKM,KAAO,CAACI,EAAWC,EAAaF,EAAeF,EAAQ,KAAMC,GAAYO,KAAK,KAC5Ef,EAAKgB,YAAYrB,EAASU,GAAQA,EAAO,IAAIY,KACtD,IGnBF,oBAAAC,IACE,KAAA1K,IAA4B,EA0B9B,CAxBE0K,EAAA5N,UAAA0H,IAAA,SAAIlI,GACF,YAAyB+C,IAAlB,KAAKW,IAAI1D,EAClB,EAEAoO,EAAA5N,UAAAH,IAAA,SAAIL,EAAaqO,GACf,IAAMC,EAAI,KAAK5K,IAAI1D,GACnB,YAAa+C,IAANuL,EAAkBD,EAAMC,CACjC,EAEAF,EAAA5N,UAAA2L,IAAA,SAAInM,EAAa+B,GACf,KAAK2B,IAAI1D,GAAO+B,CAClB,EAEAqM,EAAA5N,UAAA6I,MAAA,WACE,KAAK3F,IAAM,EACb,EAEA0K,EAAA5N,UAAA,gBAAOR,UACE,KAAK0D,IAAI1D,EAClB,EAEAoO,EAAA5N,UAAA8H,KAAA,WACE,OAAOpI,OAAOwK,KAAK,KAAKhH,KAAKkC,MAC/B,CACF,CA3BA,GCiFA,QA9DiB,SACfjF,EACAa,EACAZ,EACA2N,GAEQ,MAA2B5N,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxByG,EAAU,GACViH,EAAQ,CAAC,EACTC,EAAI,CAAC,EACLC,EAAQ,CAAC,EACf7N,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAAMC,EAAKF,EAAKE,GAChBkG,EAAQjG,KAAKD,GACboN,EAAEpN,GAAMsI,IACJtI,IAAOG,IAAQiN,EAAEpN,GAAM,EAC7B,IAGA,IADA,IAAMsN,EAAU9N,EAAM+E,O,WACbxE,GAEP,IAAMwN,EAvCQ,SAChBH,EACA5N,EACA2N,GAKA,IAFA,IACII,EADAC,EAASlF,IAEJvI,EAAI,EAAGA,EAAIP,EAAM+E,OAAQxE,IAAK,CACrC,IAAM8C,EAASrD,EAAMO,GAAGC,IACnBmN,EAAMtK,IAAWuK,EAAEvK,IAAW2K,IACjCA,EAASJ,EAAEvK,GACX0K,EAAU/N,EAAMO,G,CAGpB,OAAOwN,CACT,CAuBoBE,CAAUL,EAAG5N,EAAO2N,GAC9BO,EAAYH,EAAQvN,GAG1B,GAFAmN,EAAMO,IAAa,EAEfN,EAAEM,KAAepF,I,iBAErB,IAAIqF,EAA6B,GACnBA,EAAVpO,E9BbyB,SAACsD,EAAgBpD,GAChD,OAAOA,EAAMuD,QAAO,SAAA9C,GAAQ,OAAAA,EAAKC,SAAW0C,CAAhB,GAC9B,C8BWiC+K,CAAkBF,EAAWjO,GACtCwD,EAAiByK,EAAWjO,GAEhDkO,EAAa9N,SAAQ,SAAAK,GACnB,IAAM2N,EAAa3N,EAAKE,OAClB0N,EAAa5N,EAAKC,OAClB4N,EAAIF,IAAeH,EAAYI,EAAaD,EAC5CG,EAASd,GAAsBhN,EAAKgN,GAAsBhN,EAAKgN,GAAsB,EACvFE,EAAEW,GAAKX,EAAEG,EAAQvN,IAAMgO,GACzBZ,EAAEW,GAAKX,EAAEG,EAAQvN,IAAMgO,EACvBX,EAAMU,GAAK,CAACR,EAAQvN,KACXoN,EAAEW,KAAOX,EAAEG,EAAQvN,IAAMgO,GAClCX,EAAMU,GAAG9N,KAAKsN,EAAQvN,GAE1B,G,EAvBOD,EAAI,EAAGA,EAAIuN,EAASvN,I,IA0B7BsN,EAAMlN,GAAU,CAACA,GAEjB,IAAM8N,EAAQ,CAAC,EACf,IAAK,IAAM7N,KAAUgN,EACfA,EAAEhN,KAAYkI,KAChB4F,EAAa/N,EAAQC,EAAQiN,EAAOY,GAKxC,IAAM3G,EAAO,CAAC,EACd,IAAK,IAAMlH,KAAU6N,EACnB3G,EAAKlH,GAAU6N,EAAM7N,GAAQ,GAE/B,MAAO,CAAEmE,OAAQ6I,EAAG9F,KAAI,EAAE6G,QAASF,EACrC,EAIA,SAASC,EAAa/N,EAAQC,EAAQiN,EAAOe,GAC3C,GAAIjO,IAAWC,EACb,MAAO,CAACD,GAEV,GAAIiO,EAAWhO,GACb,OAAOgO,EAAWhO,GAGpB,IADA,IAAM6N,EAAQ,GACG,MAAAZ,EAAMjN,GAAN,eAAe,CAA3B,IACGiO,EAAYH,EAAa/N,EADpB,KACkCkN,EAAOe,GACpD,IAAKC,EAAW,OAChB,IAAoB,UAAAA,EAAA,eAAW,CAA1B,IAAIC,EAAO,KACV,EAAQA,GAAUL,EAAMhO,KAAK,OAAIqO,GAAS,GAAF,CAAElO,IAAM,IAC/C6N,EAAMhO,KAAK,CAACqO,EAASlO,G,EAI9B,OADAgO,EAAWhO,GAAU6N,EACdG,EAAWhO,EACpB,CCvEA,QA9BsB,SAACd,EAAsBC,GAK3C,IAJA,IAAMgP,EAAiB,EAAajP,EAAWC,GAEzCiP,EAAiB,GACjBvH,EAAOsH,EAAehK,OACnBxE,EAAI,EAAGA,EAAIkH,EAAMlH,GAAK,EAAG,CAChCyO,EAAKzO,GAAK,GACV,IAAK,IAAIyI,EAAI,EAAGA,EAAIvB,EAAMuB,GAAK,EACzBzI,IAAMyI,EACRgG,EAAKzO,GAAGyI,GAAK,EACqB,IAAzB+F,EAAexO,GAAGyI,IAAa+F,EAAexO,GAAGyI,GAG1DgG,EAAKzO,GAAGyI,GAAK+F,EAAexO,GAAGyI,GAF/BgG,EAAKzO,GAAGyI,GAAKF,G,CAOnB,IAAK,IAAI8C,EAAI,EAAGA,EAAInE,EAAMmE,GAAK,EAC7B,IAASrL,EAAI,EAAGA,EAAIkH,EAAMlH,GAAK,EAC7B,IAASyI,EAAI,EAAGA,EAAIvB,EAAMuB,GAAK,EACzBgG,EAAKzO,GAAGyI,GAAKgG,EAAKzO,GAAGqL,GAAKoD,EAAKpD,GAAG5C,KACpCgG,EAAKzO,GAAGyI,GAAKgG,EAAKzO,GAAGqL,GAAKoD,EAAKpD,GAAG5C,IAK1C,OAAOgG,CACT,EC+HA,QAxJA,WAGE,WAAYhE,GACVhM,KAAKgM,IAAMA,CACb,CAiJF,OA/IE,YAAAiE,OAAA,WACE,OAAOjQ,KAAKgM,KAAO,EACrB,EAEA,YAAApD,IAAA,SAAIsH,G,MACIC,EAAWD,EAAYlE,IAC7B,KAAa,QAAR,EAAAhM,KAAKgM,WAAG,eAAEjG,QACb,OAAO,IAAIqK,EAAOD,GAEpB,KAAKA,aAAQ,EAARA,EAAUpK,QACb,OAAO,IAAIqK,EAAOpQ,KAAKgM,KAEzB,GAAIhM,KAAKgM,IAAIjG,SAAWoK,EAASpK,OAAQ,CACvC,IAAIsK,EAAM,GACV,IAAK,IAAI1L,KAAS3E,KAAKgM,IACrBqE,EAAI1L,GAAS3E,KAAKgM,IAAIrH,GAASwL,EAASxL,GAE1C,OAAO,IAAIyL,EAAOC,E,CAEtB,EAEA,YAAAC,SAAA,SAASJ,G,MACDC,EAAWD,EAAYlE,IAC7B,KAAa,QAAR,EAAAhM,KAAKgM,WAAG,eAAEjG,QACb,OAAO,IAAIqK,EAAOD,GAEpB,KAAKA,aAAQ,EAARA,EAAUpK,QACb,OAAO,IAAIqK,EAAOpQ,KAAKgM,KAEzB,GAAIhM,KAAKgM,IAAIjG,SAAWoK,EAASpK,OAAQ,CACvC,IAAIsK,EAAM,GACV,IAAK,IAAI1L,KAAS3E,KAAKgM,IACrBqE,EAAI1L,GAAS3E,KAAKgM,IAAIrH,GAASwL,EAASxL,GAE1C,OAAO,IAAIyL,EAAOC,E,CAEtB,EAEA,YAAAE,IAAA,SAAIxK,GACF,IAAIsK,EAAM,GACV,GAAe,IAAXtK,EACF,IAAK,IAAIpB,KAAS3E,KAAKgM,IACrBqE,EAAI1L,GAAS3E,KAAKgM,IAAIrH,GAASoB,EAGnC,OAAO,IAAIqK,EAAOC,EACpB,EAEA,YAAAG,OAAA,WACE,IAAIH,EAAM,GACV,IAAK,IAAI1L,KAAS3E,KAAKgM,IACrBqE,EAAI1L,IAAW3E,KAAKgM,IAAIrH,GAE1B,OAAO,IAAIyL,EAAOC,EACpB,EAGA,YAAAI,wBAAA,SAAwBP,G,MAChBC,EAAWD,EAAYlE,IAC7B,KAAa,QAAR,EAAAhM,KAAKgM,WAAG,eAAEjG,WAAWoK,aAAQ,EAARA,EAAUpK,QAClC,OAAO,EAET,GAAI/F,KAAKgM,IAAIjG,SAAWoK,EAASpK,OAAQ,CACvC,IAAIsK,EAAM,EACV,IAAK,IAAI1L,KAAS3E,KAAKgM,IACrBqE,GAAOxL,KAAK6L,IAAI1Q,KAAKgM,IAAIrH,GAASuL,EAAYlE,IAAIrH,GAAQ,GAE5D,OAAO0L,C,CAEX,EAGA,YAAAM,kBAAA,SAAkBT,G,MACVC,EAAWD,EAAYlE,IAC7B,KAAa,QAAR,EAAAhM,KAAKgM,WAAG,eAAEjG,WAAWoK,aAAQ,EAARA,EAAUpK,QAClC,OAAO,EAET,GAAI/F,KAAKgM,IAAIjG,SAAWoK,EAASpK,OAAQ,CACvC,IAAIsK,EAAM,EACV,IAAK,IAAI1L,KAAS3E,KAAKgM,IACrBqE,GAAOxL,KAAK6L,IAAI1Q,KAAKgM,IAAIrH,GAASuL,EAAYlE,IAAIrH,GAAQ,GAE5D,OAAOE,KAAK+L,KAAKP,E,CAEjBQ,QAAQC,MAAM,yCAElB,EAGA,YAAAC,UAAA,WACE,IAAIV,EAAM,GACJW,EAAW,EAAMhR,KAAKgM,KAC5BgF,EAASC,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,IACxB,IAAMiP,EAAMF,EAASA,EAASjL,OAAS,GACjCF,EAAMmL,EAAS,GACrB,IAAK,IAAIrM,KAAS3E,KAAKgM,IACrBqE,EAAI1L,IAAU3E,KAAKgM,IAAIrH,GAASkB,IAAQqL,EAAMrL,GAEhD,OAAO,IAAIuK,EAAOC,EACpB,EAGA,YAAAc,MAAA,W,MACE,KAAa,QAAR,EAAAnR,KAAKgM,WAAG,eAAEjG,QACb,OAAO,EAET,IAAIsK,EAAM,EACR,IAAK,IAAI1L,KAAS3E,KAAKgM,IACrBqE,GAAOxL,KAAK6L,IAAI1Q,KAAKgM,IAAIrH,GAAQ,GAErC,OAAOE,KAAK+L,KAAKP,EACnB,EAGA,YAAAe,IAAA,SAAIlB,G,MACIC,EAAWD,EAAYlE,IAC7B,KAAa,QAAR,EAAAhM,KAAKgM,WAAG,eAAEjG,WAAWoK,aAAQ,EAARA,EAAUpK,QAClC,OAAO,EAET,GAAI/F,KAAKgM,IAAIjG,SAAWoK,EAASpK,OAAQ,CACvC,IAAIsK,EAAM,EACV,IAAK,IAAI1L,KAAS3E,KAAKgM,IACrBqE,GAAOrQ,KAAKgM,IAAIrH,GAASuL,EAAYlE,IAAIrH,GAE3C,OAAO0L,C,CAEPQ,QAAQC,MAAM,yCAElB,EAGA,YAAAO,MAAA,SAAMnB,G,MACEC,EAAWD,EAAYlE,IAC7B,IAAY,QAAR,EAAAhM,KAAKgM,WAAG,eAAEjG,WAAWoK,aAAQ,EAARA,EAAUpK,QACjC,OAAO,EAET,IAAK,IAAIpB,KAAS3E,KAAKgM,IACrB,GAAIhM,KAAKgM,IAAIrH,KAAWwL,EAASxL,GAC/B,OAAO,EAGX,OAAO,CACT,EACF,EAtJA,GCNO,ICmDK2M,ECSCC,EAAmB,SAACvQ,EAAOb,QAAA,IAAAA,IAAAA,OAAA,GACtC,IAAMqR,EAAgB,GAStB,OARAxQ,EAAMK,SAAQ,SAAAC,QACA4B,IAAR/C,GACFqR,EAAc/P,KAAKH,QAEH4B,IAAd5B,EAAKnB,IACPqR,EAAc/P,KAAKH,EAAKnB,GAE5B,IACOqR,CACT,GDpBA,SAAYF,GACV,uCACD,CAFD,CAAYA,IAAAA,EAAY,KEzCjB,IAmCMG,EAAS,SAACC,EAAyBC,EAAyBC,GAEvE,IAAMC,EArCyB,SAACH,EAAyBC,EAAyBC,GAClF,IAAI/G,EAAO,IAEP8G,aAAY,EAAZA,EAAc5L,QAChB8E,EAAO8G,GAGPD,EAASrQ,SAAQ,SAAAyQ,GACfjH,EAAOA,EAAKU,OAAOlL,OAAOwK,KAAKiH,GACjC,IACAjH,EAAOkB,EAAKlB,IAGd,IAAMgH,EAA8B,CAAC,EAarC,OAZAhH,EAAKxJ,SAAQ,SAAAlB,GACX,IAAI+B,EAAQ,GACZwP,EAASrQ,SAAQ,SAAAyQ,QACG5O,IAAd4O,EAAK3R,IAAoC,KAAd2R,EAAK3R,IAClC+B,EAAMT,KAAKqQ,EAAK3R,GAEpB,IACI+B,EAAM6D,UAAW6L,aAAc,EAAdA,EAAgBG,SAAS5R,MAC5C0R,EAAe1R,GAAO4L,EAAK7J,GAE/B,IAEO2P,CACT,CAUyBG,CAAkBN,EAAUC,EAAcC,GAC3DK,EAAa,GACnB,IAAK5R,OAAOwK,KAAKgH,GAAgB9L,OAC/B,OAAOkM,EAIT,IAEMC,EAFW7R,OAAOmD,OAAOqO,GAEFM,OAAM,SAAAjQ,GAAS,OAAAA,EAAMiQ,OAAM,SAAA9F,GAAQ,MAAkB,iBAAX,CAAP,GAApB,IA2B5C,OAxBAqF,EAASrQ,SAAQ,SAACyQ,EAAMnN,GACtB,IAAIyN,EAAO,GACX/R,OAAOwK,KAAKgH,GAAgBxQ,SAAQ,SAAAlB,GAClC,IAAMkS,EAAWP,EAAK3R,GAChBmS,EAAcT,EAAe1R,GAC7BoS,EAAaD,EAAY5J,WAAU,SAAAxG,GAAS,OAAAmQ,IAAanQ,CAAb,IAC9CsQ,EAAU,GAEd,GAAIN,EACFM,EAAQ/Q,KAAK4Q,QAGb,IAAI,IAAI9Q,EAAI,EAAGA,EAAI+Q,EAAYvM,OAAQxE,IACjCA,IAAMgR,EACRC,EAAQ/Q,KAAK,GAEb+Q,EAAQ/Q,KAAK,GAInB2Q,EAAOA,EAAK7G,OAAOiH,EACrB,IACAP,EAAWtN,GAASyN,CACtB,IACOH,CACT,EASaQ,EAAc,SAACpG,EAAMqG,EAAWC,EAA6D7R,QAA7D,IAAA6R,IAAAA,EAA6BrB,EAAasB,mBACrF,IAAIC,EAAW,EAQf,OAPQF,IACDrB,EAAasB,oBAChBC,EAAW,IAAI,EAAOxG,GAAMsE,kBAAkB,IAAI,EAAO+B,KAKtDG,CACT,EChGMC,EAAgB,SACpB9R,EACA+R,EACAC,EACAC,GAKA,IAHA,IAAMlN,EAASgN,EAAUhN,OACnBmN,EAAQ,EAAID,EACdE,EAAa,EACR5R,EAAI,EAAGA,EAAIwE,EAAQxE,IAE1B,IADA,IAAM6R,EAAWpS,EAAMO,GAAG8R,UACjBrJ,EAAI,EAAGA,EAAIjE,EAAQiE,IAEtBoJ,IADapS,EAAMgJ,GAAGqJ,YAK1BF,IAHcJ,EAAUxR,GAAGyI,IAAM,IACtBgJ,EAAGzR,IAAM,IACTyR,EAAGhJ,IAAM,GACakJ,GAIrC,OADAC,GAAe,EAAID,EAErB,EAGMI,EAAwB,SAC5BtS,EACAuS,QADA,IAAAvS,IAAAA,EAAA,IAKA,IAFA,IAAM+E,EAAS/E,EAAM+E,OACjByN,EAAkB,IAAI,EAAO,IACxBjS,EAAI,EAAGA,EAAIwE,EAAQxE,IAC1BiS,EAAkBA,EAAgB5K,IAAI,IAAI,EAAO2K,EAAoBhS,KAGvE,IAAMkS,EAAgBD,EAAgBjD,IAAIxK,GAE1C0N,EAAc1C,YAEd,IAAI2C,EAAmB,EACvB,IAASnS,EAAI,EAAGA,EAAIwE,EAAQxE,IAG1BmS,IAFMC,EAAc,IAAI,EAAOJ,EAAoBhS,KACPkP,wBAAwBgD,GAKtE,IAAIG,EAA8B,GAIlC,IAHA5S,EAAMK,SAAQ,WACZuS,EAA4BnS,KAAK,GACnC,IACSF,EAAI,EAAGA,EAAIwE,EAAQxE,IAAK,CAC/B,IAAMoS,EAAc,IAAI,EAAOJ,EAAoBhS,IACnDP,EAAMO,GAAoB,gBAAI,EAC9B,IAAK,IAAIyI,EAAI,EAAGA,EAAIjE,EAAQiE,IAC1B,GAAKzI,IAAMyI,EAAX,CAIA,IAAM6J,EAAc,IAAI,EAAON,EAAoBvJ,IACnD4J,EAA4BrS,GAAGyI,GAAK2J,EAAYlD,wBAAwBoD,GACxE7S,EAAMO,GAAoB,iBAAKqS,EAA4BrS,GAAGyI,E,MAL5D4J,EAA4BrS,GAAGyI,GAAK,C,CAU1C,IAAI8J,EAA6B,EAC3BZ,EAAQ,EAAInN,EAAS2N,EAC3B,IAASnS,EAAI,EAAGA,EAAIwE,EAAQxE,IAC1B,KAAM6R,EAAWpS,EAAMO,GAAG8R,UAC1B,IAASrJ,EAAI,EAAGA,EAAIjE,EAAQiE,IAAK,CAC/B,IAAM+J,EAAW/S,EAAMgJ,GAAGqJ,UACrB9R,IAAMyI,GAAKoJ,IAAaW,IAE7BD,GADkB9S,EAAMO,GAAGyS,gBAAkBhT,EAAMgJ,GAAGgK,gBAAmBnP,KAAK6L,IAAIwC,EAAO,GAAKU,EAA4BrS,GAAGyI,GAAKkJ,E,CAJjG,CAQrC,OAAO3G,OAAOuH,EAAmBG,QAAQ,GAC3C,EA2TA,QA5SgB,SACdnT,EACAC,EACA2N,EACAwF,EACAJ,EACAK,EACAxC,EACAC,EACAwC,QAPA,IAAArT,IAAAA,GAAA,QACA,IAAA2N,IAAAA,EAAA,eACA,IAAAwF,IAAAA,EAAA,WACA,IAAAJ,IAAAA,GAAA,QACA,IAAAK,IAAAA,OAAA,QACA,IAAAxC,IAAAA,EAAA,SACA,IAAAC,IAAAA,EAAA,CAA4B,YAC5B,IAAAwC,IAAAA,EAAA,GAGQ,MAA2BtT,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAE1BsS,EAAsB,GAC1B,GAAIO,EAAoB,CACtB9S,EAAMK,SAAQ,SAACC,EAAMqD,GACnBrD,EAAK+S,WAAa/S,EAAK+S,YAAc,CAAC,EACtC/S,EAAKgT,YAAc3P,CACrB,IAEA,IAAI,EAAe,GACf3D,EAAMmR,OAAM,SAAA7Q,GAAQ,OAAAA,EAAKV,eAAe,WAApB,MACtB,EAAeyK,MAAML,KAAK,IAAI/C,IAAIjH,EAAM6C,KAAI,SAAAvC,GAAQ,OAAAA,EAAKiT,QAAL,MACpDvT,EAAMK,SAAQ,SAAAC,GACZA,EAAK+S,WAAWE,SAAW,EAAa7L,WAAU,SAAA6L,GAAY,OAAAA,IAAajT,EAAKiT,QAAlB,GAChE,KAGF,IAAMF,EAAa9C,EAAiBvQ,EAAOmT,GAE3CZ,EAAsB9B,EAAO4C,EAAY1C,EAAcC,E,CAGzD,IAAIlN,EAAW,EAET8P,EAAuB,CAAC,EACxBrT,EAAU,CAAC,EAEjBH,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAAMkT,EAAcC,OAAOhQ,KAC3BpD,EAAK+R,UAAYoB,EACjBD,EAASC,GAAO,CACdjT,GAAIiT,EACJzT,MAAO,CAACM,IAEVH,EAAQG,EAAKE,IAAM,CACjBF,KAAI,EACJqT,IAAKpT,EAET,IAEA,IAAMwR,EAAY,EAAajS,EAAWC,GAEpCiS,EAAK,GAQLvN,EAAY,CAAC,EAEfwN,EAAI,EACRF,EAAU1R,SAAQ,SAACuT,EAAKrT,GACtB,IAAIqL,EAAI,EACFiI,EAAM7T,EAAMO,GAAGC,GACrBiE,EAAUoP,GAAO,CAAC,EAClBD,EAAIvT,SAAQ,SAACyT,EAAO9K,GAClB,GAAK8K,EAAL,CACAlI,GAAKkI,EACL,IAAMC,EAAM/T,EAAMgJ,GAAGxI,GACrBiE,EAAUoP,GAAKE,GAAOD,EACtB7B,GAAK6B,CAJa,CAKpB,IACA9B,EAAGvR,KAAKmL,EACV,IAEAqG,GAAK,EAQL,IANA,IAAI+B,EAAkBlL,IAClBmL,EAAqBnL,IACrBoL,EAAO,EAEPC,EAAa,GACbC,EAAgB,CAAC,IACR,CAETJ,EADElB,GAAsB9S,EAAMmR,OAAM,SAAA7Q,GAAQ,OAAAA,EAAKV,eAAe,aAApB,IAC1BkS,EAAc9R,EAAO+R,EAAWC,EAAIC,GAAKK,EAAsBtS,EAAOuS,GAAuBa,EAE7FtB,EAAc9R,EAAO+R,EAAWC,EAAIC,GAI3C,IAATiC,IACFD,EAAqBD,EACrBG,EAAanU,EACboU,EAAgBZ,GAGlB,IAAMa,EAA0BL,EAAkB,GAAKA,EAAkBC,GAAsBD,EAAkBC,EAAqBf,EAYtI,GAVIc,EAAkBC,IACpBE,EAAanU,EAAM6C,KAAI,SAAAvC,GAAQ,OAC7BA,KAAI,EACJ+R,UAAW/R,EAAK+R,UAFa,IAI/B+B,EAAgB,EAAMZ,GACtBS,EAAqBD,GAIlBK,GAA2BH,EAAO,IACrC,MAEFA,IAEA7U,OAAOwK,KAAK2J,GAAUnT,SAAQ,SAAAgS,GAE5B,IAAIiC,EAAS,EACbrU,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzB6T,EAAkBpU,EAAQQ,GAAQL,KAAK+R,UACvCmC,EAAkBrU,EAAQS,GAAQN,KAAK+R,WACxCkC,IAAoBlC,GAAamC,IAAoBnC,GACpDmC,IAAoBnC,GAAakC,IAAoBlC,KACzDiC,GAAmB5T,EAAKgN,IAAiC,EAE7D,IACA8F,EAASnB,GAAWiC,OAASA,CAC/B,IAIAtU,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAEIkU,EAFEC,EAAclB,EAASlT,EAAK+R,WAC9BsC,EAAe,EAGbC,EAAc5C,EAAGzR,IAAM,EAAI0R,GAG7B4C,EAAO,EACLC,EAAmBJ,EAAY1U,MACrC8U,EAAiBzU,SAAQ,SAAA0U,GACvB,IAAMC,EAAY7U,EAAQ4U,EAAOvU,IAAImT,IACrCkB,GAAQ9C,EAAUxR,GAAGyU,IAAc,CACrC,IAEA,IAAMC,EAAmBJ,EAAOH,EAAYJ,OAASM,EAE/CM,EAA8BJ,EAAiBtR,QAAO,SAAAuR,GAAU,OAAAA,EAAOvU,KAAOF,EAAKE,EAAnB,IAClE2U,EAAyB,GAC7BD,EAA4B7U,SAAQ,SAAC+U,EAAYzR,GAC/CwR,EAAuBxR,GAAS4O,EAAoB6C,EAAW9B,YACjE,IAEA,IAAM+B,EAA2B/C,EAAsB4C,EAA6B3C,GAAuBa,EAGrGkC,EAAkB7Q,EAAUnE,EAAKE,IA6CvC,GA5CAnB,OAAOwK,KAAKyL,GAAiBjV,SAAQ,SAAAkV,GACnC,IACMC,EADerV,EAAQoV,GAAgBjV,KACN+R,UAGvC,GAAImD,IAAsBlV,EAAK+R,UAA/B,CACA,IAAMoD,EAAkBjC,EAASgC,GAC3BE,EAAeD,EAAgBzV,MAGrC,GAAK0V,GAAiBA,EAAa3Q,OAAnC,CAGA,IAAI4Q,EAAsB,EAC1BD,EAAarV,SAAQ,SAAAuV,GACnB,IAAMC,EAAW1V,EAAQyV,EAAMpV,IAAImT,IACnCgC,GAAuB5D,EAAUxR,GAAGsV,IAAa,CACnD,IAGA,IAAMC,EAAgBH,EAAsBF,EAAgBnB,OAASM,EAE/DmB,EAAsBL,EAAanL,OAAO,CAACjK,IAC7C0V,EAAsB,GAC1BD,EAAqB1V,SAAQ,SAAC4V,EAAStS,GACrCqS,EAAoBrS,GAAS4O,EAAoB0D,EAAQ3C,YAC3D,IAEA,IAAM4C,EAAwB5D,EAAsByD,EAAsBxD,GAAuBa,EAG7F+C,EAAWL,EAAgBb,EAC3BnC,IACFqD,EAAYL,EAAgBI,GAA0BjB,EAAmBI,IAIvEc,EAAWxB,IACbA,EAAewB,EACf1B,EAAcgB,EA7BiC,CALD,CAoClD,IAGId,EAAe,EAAG,CACpBF,EAAYzU,MAAMS,KAAKH,GACvB,IAAM,EAAoBA,EAAK+R,UAC/B/R,EAAK+R,UAAYoC,EAAYjU,GAE7B,IAAM4V,EAAuB1B,EAAY1U,MAAM2E,QAAQrE,GAEvDoU,EAAY1U,MAAM6K,OAAOuL,EAAsB,GAG/C,IAAI,EAAwB,EACxB,EAAoB,EACxBnW,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzB6T,EAAkBpU,EAAQQ,GAAQL,KAAK+R,UACvCmC,EAAkBrU,EAAQS,GAAQN,KAAK+R,WACxCkC,IAAoBE,EAAYjU,IAAMgU,IAAoBC,EAAYjU,IACrEgU,IAAoBC,EAAYjU,IAAM+T,IAAoBE,EAAYjU,MAC1E,GAAiDE,EAAKgN,IAAiC,IAEpF6G,IAAoB,GAAqBC,IAAoB,GAC5DA,IAAoB,GAAqBD,IAAoB,KACjE,GAAyC7T,EAAKgN,IAAiC,EAEnF,IAGA+G,EAAYH,OAAS,EACrBI,EAAYJ,OAAS,C,CAEzB,G,CAIF,IAAM+B,EAAkB,CAAC,EACrBC,EAAa,EACjBjX,OAAOwK,KAAKuK,GAAe/T,SAAQ,SAACgS,GAClC,IAAMkE,EAAUnC,EAAc/B,GAC9B,GAAKkE,EAAQvW,OAAUuW,EAAQvW,MAAM+E,OAArC,CAIA,IAAMyR,EAAQ9C,OAAO4C,EAAa,GAC9BE,IAAUnE,IAGdkE,EAAQ/V,GAAKgW,EACbD,EAAQvW,MAAQuW,EAAQvW,MAAM6C,KAAI,SAAAwI,GAAQ,OAAG7K,GAAI6K,EAAK7K,GAAI6R,UAAWmE,EAA3B,IAC1CpC,EAAcoC,GAASD,EACvBF,EAAgBhE,GAAamE,SACtBpC,EAAc/B,GACrBiE,I,aAZSlC,EAAc/B,EAazB,IAEA8B,EAAW9T,SAAQ,SAAAoW,GACT,IAAAnW,EAAoBmW,EAAQ,KAAtBpE,EAAcoE,EAAQ,UAC/BnW,IACLA,EAAK+R,UAAYA,EACb/R,EAAK+R,WAAagE,EAAgB/V,EAAK+R,aAAY/R,EAAK+R,UAAYgE,EAAgB/V,EAAK+R,YAC/F,IAEA,IAAMqE,EAAe,GACfC,EAAiB,CAAC,EACxB1W,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzB8N,EAAS9N,EAAKgN,IAAuB,EACrC6G,EAAkBpU,EAAQQ,GAAQL,KAAK+R,UACvCmC,EAAkBrU,EAAQS,GAAQN,KAAK+R,UAC7C,GAAKkC,GAAoBC,EAAzB,CACA,IAAMoC,EAAY,UAAGrC,EAAe,cAAMC,GAC1C,GAAImC,EAAeC,GACjBD,EAAeC,GAAWpI,QAAUA,EACpCmI,EAAeC,GAAWC,YACrB,CACL,IAAMC,EAAU,CACdnW,OAAQ4T,EACR3T,OAAQ4T,EACRhG,OAAM,EACNqI,MAAO,GAETF,EAAeC,GAAaE,EAC5BJ,EAAajW,KAAKqW,E,CAb4B,CAelD,IACA,IAAMC,EAAgB,GAItB,OAHA1X,OAAOwK,KAAKuK,GAAe/T,SAAQ,SAAAgS,GACjC0E,EAActW,KAAK2T,EAAc/B,GACnC,IACO,CACLmB,SAAUuD,EACVL,aAAY,EAEhB,ECvYA,IAAMM,EAAc,SAACrF,EAAcY,EAAqB5O,GAUtD,OARQgO,IACDrB,EAAasB,kBACLW,EAAoB5O,GAGpB,EAIjB,EAqMA,MC7LA,EApByB,SACvB0H,EACA4L,GAGA,IAAMC,EAAmB,IAAI,EAAOD,GAE9BE,EAAkBD,EAAiB/G,QAEnCiH,EAAa,IAAI,EAAO/L,GAExBgM,EAAYD,EAAWjH,QAEvBC,EAAM8G,EAAiB9G,IAAIgH,GAC3BE,EAAeH,EAAkBE,EAGvC,OADyBC,EAAelH,EAAMkH,EAAe,CAE/D,E,ECrBA,WAKE,WAAYC,GACVvY,KAAK6X,MAAQU,EAAMxS,OACnB/F,KAAK+H,OAAS,CAAC,EACf,IAAgB,UAAAwQ,EAAA,eAAO,CAAlB,IAAMhX,EAAC,KACVvB,KAAK+H,OAAOxG,GAAKA,C,CAErB,CA8BF,OA3BE,YAAA0B,KAAA,SAAKoJ,GACH,KAAOrM,KAAK+H,OAAOsE,KAAUA,GAC3BA,EAAOrM,KAAK+H,OAAOsE,GAErB,OAAOA,CACT,EAEA,YAAAmM,MAAA,SAAMxW,EAAGC,GACP,IAAMwW,EAAQzY,KAAKiD,KAAKjB,GAClB0W,EAAQ1Y,KAAKiD,KAAKhB,GAEpBwW,IAAUC,IAGVD,EAAQC,GACN1Y,KAAK+H,OAAO9F,KAAOA,GAAGjC,KAAKwY,MAAMxY,KAAK+H,OAAO9F,GAAID,GACrDhC,KAAK+H,OAAO9F,GAAKjC,KAAK+H,OAAO/F,KAEzBhC,KAAK+H,OAAO/F,KAAOA,GAAGhC,KAAKwY,MAAMxY,KAAK+H,OAAO/F,GAAIC,GACrDjC,KAAK+H,OAAO/F,GAAKhC,KAAK+H,OAAO9F,IAEjC,EAGA,YAAA0W,UAAA,SAAU3W,EAAGC,GACX,OAAOjC,KAAKiD,KAAKjB,KAAOhC,KAAKiD,KAAKhB,EACpC,EACF,EAzCA,GCHA,IAAM2W,EAAiB,SAAC5W,EAAGC,GACzB,OAAOD,EAAIC,CACb,E,QAEA,WAKE,WAAY4W,QAAA,IAAAA,IAAAA,EAAA,GACV7Y,KAAK6Y,UAAYA,EACjB7Y,KAAK8Y,KAAO,EACd,CA6EF,OA3EE,YAAAC,QAAA,SAAQpU,GACN,OAAO,EAAIA,EAAQ,CACrB,EAEA,YAAAqU,SAAA,SAASrU,GACP,OAAO,EAAIA,EAAQ,CACrB,EAEA,YAAAsU,UAAA,SAAUtU,GACR,OAAc,IAAVA,EACK,KAEFE,KAAKqU,OAAOvU,EAAQ,GAAK,EAClC,EAEA,YAAAZ,QAAA,WACE,OAAO/D,KAAK8Y,KAAK/S,QAAU,CAC7B,EAEA,YAAAoT,IAAA,WACE,OAAOnZ,KAAK+D,eAAYb,EAAYlD,KAAK8Y,KAAK,EAChD,EAEA,YAAAM,OAAA,WACE,IAAMD,EAAMnZ,KAAKmZ,MACXE,EAASrZ,KAAK8Y,KAAK5S,MAKzB,OAJIlG,KAAK8Y,KAAK/S,OAAS,IACrB/F,KAAK8Y,KAAK,GAAKO,EACfrZ,KAAKsZ,SAAS,IAETH,CACT,EAEA,YAAAI,OAAA,SAAOrX,GACL,GAAc,OAAVA,EAAgB,CAClBlC,KAAK8Y,KAAKrX,KAAKS,GACf,IAAMyC,EAAQ3E,KAAK8Y,KAAK/S,OAAS,EAEjC,OADA/F,KAAKwZ,OAAO7U,IACL,C,CAET,OAAO,CACT,EAEA,YAAA6U,OAAA,SAAO7U,GAEL,IADA,IAAIoD,EAAS/H,KAAKiZ,UAAUtU,GACrBA,GAASA,EAAQ,GAAK3E,KAAK6Y,UAAU7Y,KAAK8Y,KAAK/Q,GAAS/H,KAAK8Y,KAAKnU,IAAU,GAAG,CAEpF,IAAM8U,EAAMzZ,KAAK8Y,KAAK/Q,GACtB/H,KAAK8Y,KAAK/Q,GAAU/H,KAAK8Y,KAAKnU,GAC9B3E,KAAK8Y,KAAKnU,GAAS8U,EAEnB9U,EAAQoD,EACRA,EAAS/H,KAAKiZ,UAAUtU,E,CAE5B,EAEA,YAAA2U,SAAA,SAAS3U,G,MACH+U,EAAU/U,EACRgV,EAAO3Z,KAAK+Y,QAAQpU,GACpBiV,EAAQ5Z,KAAKgZ,SAASrU,GACtB8D,EAAOzI,KAAK8Y,KAAK/S,OACV,OAAT4T,GAAiBA,EAAOlR,GAAQzI,KAAK6Y,UAAU7Y,KAAK8Y,KAAKY,GAAU1Z,KAAK8Y,KAAKa,IAAS,EACxFD,EAAUC,EAEA,OAAVC,GACAA,EAAQnR,GACRzI,KAAK6Y,UAAU7Y,KAAK8Y,KAAKY,GAAU1Z,KAAK8Y,KAAKc,IAAU,IAEvDF,EAAUE,GAERjV,IAAU+U,IACZ,EAAyC,CAAC1Z,KAAK8Y,KAAKY,GAAU1Z,KAAK8Y,KAAKnU,IAAvE3E,KAAK8Y,KAAKnU,GAAM,KAAE3E,KAAK8Y,KAAKY,GAAQ,KACrC1Z,KAAKsZ,SAASI,GAElB,EACF,EArFA,GCOA,IAAMG,EAAU,SAAC/Y,EAAsB0O,GACrC,IAAMsK,EAAgB,GACd,EAA2BhZ,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAC9B,GAAqB,IAAjBD,EAAM+E,OACR,OAAO+T,EAIT,IAAMC,EAAW/Y,EAAM,GACjBoF,EAAU,IAAI6B,IACpB7B,EAAQwC,IAAImR,GAGZ,IAOMC,EAAY,IAAI,GAPA,SAAChY,EAAeC,GACpC,OAAIuN,EACKxN,EAAEwN,OAASvN,EAAEuN,OAEf,CAET,IAMA,IAJA/K,EAAiBsV,EAASvY,GAAIP,GAAOI,SAAQ,SAACK,GAC5CsY,EAAUT,OAAO7X,EACnB,KAEQsY,EAAUjW,WAAW,CAE3B,IAAMkW,EAAuBD,EAAUZ,SACjCzX,EAASsY,EAAStY,OAClBC,EAASqY,EAASrY,OACpBwE,EAAQiC,IAAI1G,IAAWyE,EAAQiC,IAAIzG,KACvCkY,EAAcrY,KAAKwY,GAEd7T,EAAQiC,IAAI1G,KACfyE,EAAQwC,IAAIjH,GACZ8C,EAAiB9C,EAAQV,GAAOI,SAAQ,SAACK,GACvCsY,EAAUT,OAAO7X,EACnB,KAEG0E,EAAQiC,IAAIzG,KACfwE,EAAQwC,IAAIhH,GACZ6C,EAAiB7C,EAAQX,GAAOI,SAAQ,SAACK,GACvCsY,EAAUT,OAAO7X,EACnB,K,CAGJ,OAAOoY,CACT,EASMI,EAAa,SAACpZ,EAAsB0O,GACxC,IAAMsK,EAAgB,GACd,EAA2BhZ,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAC9B,GAAqB,IAAjBD,EAAM+E,OACR,OAAO+T,EAIT,IAAMK,EAAclZ,EAAM4C,KAAI,SAACnC,GAAS,OAAAA,CAAA,IACpC8N,GACF2K,EAAYlJ,MAAK,SAACjP,EAAGC,GACnB,OAAOD,EAAEwN,OAASvN,EAAEuN,MACtB,IAMF,IAJA,IAAM4K,EAAc,IAAI,EAAUpZ,EAAM6C,KAAI,SAAC6B,GAAM,OAAAA,EAAElE,EAAF,KAI5C2Y,EAAYpU,OAAS,GAAG,CAC7B,IAAMsU,EAAUF,EAAYG,QACtB3Y,EAAS0Y,EAAQ1Y,OACjBC,EAASyY,EAAQzY,OAClBwY,EAAYzB,UAAUhX,EAAQC,KACjCkY,EAAcrY,KAAK4Y,GACnBD,EAAY5B,MAAM7W,EAAQC,G,CAG9B,OAAOkY,CACT,EC7FO,IAGMS,EAAoB,KAIjC,EAME,SACE/Y,EACAwJ,EACAD,EACAyP,QAHA,IAAAhZ,IAAAA,GAd0B,QAe1B,IAAAwJ,IAAAA,GAd0B,QAe1B,IAAAD,IAAAA,GAf0B,QAgB1B,IAAAyP,IAAAA,EAf6B,MAiB7Bxa,KAAKwB,GAAKA,EACVxB,KAAKgL,KAAOA,EACZhL,KAAK+K,GAAKA,EACV/K,KAAKwa,MAAQA,CACf,EAGF,aAQE,WAAYhZ,EAAqBgZ,QAArB,IAAAhZ,IAAAA,GAjCgB,QAiCK,IAAAgZ,IAAAA,EAAA,GAC/Bxa,KAAKwB,GAAKA,EACVxB,KAAKwa,MAAQA,EACbxa,KAAKiB,MAAQ,GACbjB,KAAKya,QAAU,CAAC,CAClB,CAMF,OAJE,YAAAC,QAAA,SAAQhZ,GACN1B,KAAKiB,MAAMQ,KAAKC,GAChB1B,KAAKya,QAAQ/Y,EAAKF,IAAME,CAC1B,EACF,EAnBA,GAqBA,aAeE,WACEF,EACAmZ,EACA5Z,QAFA,IAAAS,IAAAA,GA9D0B,QA+D1B,IAAAmZ,IAAAA,GAAA,QACA,IAAA5Z,IAAAA,GAAA,GAEAf,KAAKwB,GAAKA,EACVxB,KAAK2a,mBAAqBA,EAC1B3a,KAAKiB,MAAQ,GACbjB,KAAKgB,MAAQ,GACbhB,KAAKmB,QAAU,CAAC,EAChBnB,KAAKya,QAAU,CAAC,EAChBza,KAAK4a,aAAe,CAAC,EACrB5a,KAAK6a,aAAe,CAAC,EACrB7a,KAAK8a,QAAU,EACf9a,KAAKe,SAAWA,CAClB,CAkCF,OAhCE,YAAAga,WAAA,WACE,OAAO/a,KAAKgB,MAAM+E,MACpB,EAEA,YAAAiV,QAAA,SAAQxZ,EAAYgZ,GAClB,IAAIxa,KAAKmB,QAAQK,GAAjB,CACA,IAAMF,EAAO,IAAI2Z,EAAKzZ,EAAIgZ,GAC1Bxa,KAAKgB,MAAMS,KAAKH,GAChBtB,KAAKmB,QAAQK,GAAMF,EACdtB,KAAK4a,aAAaJ,KAAQxa,KAAK4a,aAAaJ,GAAS,IAC1Dxa,KAAK4a,aAAaJ,GAAO/Y,KAAKD,EALF,CAM9B,EAEA,YAAAkZ,QAAA,SAAQlZ,EAAYwJ,EAAcD,EAAYyP,GAE5C,IADIxa,KAAK2a,yBAA6BzX,IAAP1B,KAAkBA,EAAKxB,KAAK8a,aACvD9a,KAAKmB,QAAQ6J,IAAShL,KAAKmB,QAAQ4J,IAAO/K,KAAKmB,QAAQ4J,GAAI0P,QAAQjZ,IAAvE,CAEA,IAAME,EAAO,IAAIwZ,EAAK1Z,EAAIwJ,EAAMD,EAAIyP,GASpC,GARAxa,KAAKiB,MAAMQ,KAAKC,GAChB1B,KAAKya,QAAQjZ,GAAME,EAEnB1B,KAAKmB,QAAQ6J,GAAM0P,QAAQhZ,GAEtB1B,KAAK6a,aAAaL,KAAQxa,KAAK6a,aAAaL,GAAS,IAC1Dxa,KAAK6a,aAAaL,GAAO/Y,KAAKC,IAEzB1B,KAAKe,SAAU,CAClB,IAAMoa,EAAQ,IAAID,EAAK1Z,EAAIuJ,EAAIC,EAAMwP,GACrCxa,KAAKmB,QAAQ4J,GAAI2P,QAAQS,GACzBnb,KAAK6a,aAAaL,GAAO/Y,KAAK0Z,E,CAbxB,CAeV,EACF,EAhEA,GCbA,cASE,WACEC,EACAC,EACAC,EACAC,EACAC,GAEAxb,KAAKob,SAAWA,EAChBpb,KAAKqb,OAASA,EACdrb,KAAKyb,kBAAoB,CACvBC,WAAYJ,GAAiBf,EAC7BgB,UAAWA,GDpDgB,KCqD3BI,WAAYH,GAAejB,EAE/B,CAaF,OAXE,YAAAqB,QAAA,SAAQ7O,GACN,OACE/M,KAAKob,WAAarO,EAAM8O,UACxB7b,KAAKqb,SAAWtO,EAAMsO,QACtBrb,KAAKyb,oBAAsB1O,EAAM0O,iBAErC,EAEA,YAAAK,WAAA,SAAW/O,GACT,OAAQ/M,KAAK4b,QAAQ7O,EACvB,EACF,EApCA,GAuCA,cAIE,aACE/M,KAAK+b,OAAS,GACd/b,KAAKgc,YAAc,EACrB,CAmEF,OAjEE,YAAAJ,QAAA,SAAQ7O,GACN,IAAMkP,EAAUjc,KAAKgc,YAAYjW,OAEjC,GAAIkW,IADYlP,EAAMhH,OACG,OAAO,EAChC,IAAK,IAAIxE,EAAI,EAAGA,EAAI0a,EAAS1a,IAC3B,GAAIvB,KAAKgc,YAAYza,KAAOwL,EAAMxL,GAAI,OAAO,EAE/C,OAAO,CACT,EAEA,YAAAua,WAAA,SAAW/O,GACT,OAAQ/M,KAAK4b,QAAQ7O,EACvB,EAGA,YAAAmP,SAAA,SAASd,EAAUC,EAAQC,EAAeC,EAAWC,GAInD,OAHAxb,KAAKgc,YAAYva,KACf,IAAI0a,GAAQf,EAAUC,EAAQC,EAAeC,EAAWC,IAEnDxb,KAAKgc,WACd,EAGA,YAAAI,QAAA,SAAQC,EAAmCtb,QAAnC,IAAAsb,IAAAA,GDrGqB,QCqGc,IAAAtb,IAAAA,GAAA,GACzC,IAAMub,EAAQ,IAAIC,EAAMF,GAAS,EAAMtb,GAUvC,OATAf,KAAKgc,YAAY3a,SAAQ,SAACmb,GACxB,IAAMC,EAAaD,EAAQpB,SACrBsB,EAAWF,EAAQnB,OACnB,EAAwCmB,EAAQf,kBAA9CC,EAAU,aAAEH,EAAS,YAAEI,EAAU,aAErCD,IAAenB,GAAmB+B,EAAMtB,QAAQyB,EAAYf,GAC5DC,IAAepB,GAAmB+B,EAAMtB,QAAQ0B,EAAUf,GAC1DD,IAAenB,GAAqBoB,IAAeD,GAAaY,EAAM5B,aAAQxX,EAAWuZ,EAAYC,EAAUnB,EACrH,IACOe,CACT,EAGA,YAAAK,YAAA,WACE3c,KAAK+b,OAAS,GAGd,IAFA,IAAIa,OAAU1Z,EAEL3B,EADUvB,KAAKgc,YAAYjW,OACV,EAAGxE,GAAK,EAAGA,IAAK,CACxC,IAAMib,EAAUxc,KAAKgc,YAAYza,GAC3Bsb,EAAcL,EAAQpB,SACtB0B,EAAYN,EAAQnB,OAExBwB,EAAcC,SACD5Z,IAAZ0Z,GAAyBE,IAAcF,KAExC5c,KAAK+b,OAAOta,KAAKF,GACjBqb,EAAUC,E,CAGd,OAAO7c,KAAK+b,MACd,EAEA,YAAAhB,WAAA,WACE,IAAM5Z,EAAU,CAAC,EAKjB,OAJAnB,KAAKgc,YAAY3a,SAAQ,SAACmb,GACnBrb,EAAQqb,EAAQpB,YAAWja,EAAQqb,EAAQpB,WAAY,GACvDja,EAAQqb,EAAQnB,UAASla,EAAQqb,EAAQnB,SAAU,EAC1D,IACOhb,OAAOwK,KAAK1J,GAAS4E,MAC9B,EACF,EA1EA,GA4EA,cAME,WAAYgX,GAKV,GAJA/c,KAAKgd,IAAM,CAAC,EACZhd,KAAKid,UAAY,CAAC,EAClBjd,KAAKkd,UAAY,CAAC,EAClBld,KAAKiB,MAAQ,GACR8b,EAAL,CACA,KAAOA,GAAM,CACX,IAAMI,EAAIJ,EAAKrb,KACf1B,KAAKiB,MAAMQ,KAAK0b,GAChBnd,KAAKid,UAAUE,EAAEnS,MAAQ,EACzBhL,KAAKid,UAAUE,EAAEpS,IAAM,EACvB/K,KAAKkd,UAAUC,EAAE3b,IAAM,EACvBub,EAAOA,EAAKK,O,CAGdpd,KAAKiB,MAAQjB,KAAKiB,MAAMyC,SAVP,CAWnB,CASF,OAPE,YAAA2Z,QAAA,SAAQ/b,GACN,OAAmC,IAA5BtB,KAAKid,UAAU3b,EAAKE,GAC7B,EAEA,YAAA8b,QAAA,SAAQ5b,GACN,OAAmC,IAA5B1B,KAAKkd,UAAUxb,EAAKF,GAC7B,EACF,EA/BA,GA6DA,cAeE,WAAY,G,IACV+b,EAAM,SACN,IAAAC,WAAAA,OAAU,IAAG,IAAC,EACd,IAAAC,WAAAA,OAAU,IAAG,IAAC,EACd,IAAAC,WAAAA,OAAU,IAAG,IAAC,EACd,IAAAvE,IAAAA,OAAG,IAAG,KAAE,EACR,IAAApY,SAAAA,OAAQ,IAAG,GAAK,EAChB,IAAA4c,QAAAA,OAAO,IAAG,GAAK,EAGf3d,KAAKud,OAASA,EACdvd,KAAK4d,QAAU,IAAIC,GACnB7d,KAAK8d,QAAU,EACf9d,KAAK+d,uBAAyB,GAC9B/d,KAAKge,kBAAoB,GACzBhe,KAAKwd,WAAaA,EAClBxd,KAAKmZ,IAAMA,EACXnZ,KAAKe,SAAWA,EAChBf,KAAK8a,QAAU,EAEf9a,KAAK0d,WAAaA,EAClB1d,KAAKyd,WAAaA,EAClBzd,KAAK2d,QAAUA,EACX3d,KAAK0d,WAAa1d,KAAKyd,aAAYzd,KAAK0d,WAAa1d,KAAKyd,YAC9Dzd,KAAKie,SAAW,EAClB,CAilBF,OA9kBE,YAAAC,qBAAA,SAAqB5B,EAAclB,GAAnC,WACQ+C,EAAS,GACThd,EAAUmb,EAAMnb,QAMtB,OALAia,EAASna,MAAMI,SAAQ,SAACK,IAClB,EAAKX,UAAYqa,EAASZ,OAASrZ,EAAQO,EAAKqJ,IAAIyP,QACtD2D,EAAO1c,KAAKC,EAChB,IAEOyc,CACT,EAEA,YAAAC,iBAAA,SACE9B,EACA+B,EACAC,EACAC,GAEA,IAAKve,KAAKe,UAAYsd,IAAUC,EAAO,OAAO,KAK9C,IAJA,IAAMnd,EAAUmb,EAAMnb,QAEhBqd,EADUrd,EAAQmd,EAAMvT,IACD9J,MACvBwd,EAAaD,EAAazY,OACvBxE,EAAI,EAAGA,EAAIkd,EAAYld,IAAK,CACnC,IAAMG,EAAO8c,EAAajd,GAC1B,IAAIgd,EAAQjB,QAAQ5b,IAASA,EAAKqJ,KAAOsT,EAAMrT,KAC/C,GAAKhL,KAAKe,UASR,GACEI,EAAQkd,EAAMrT,MAAMwP,MAAQrZ,EAAQmd,EAAMvT,IAAIyP,OAC7CrZ,EAAQkd,EAAMrT,MAAMwP,QAAUrZ,EAAQmd,EAAMvT,IAAIyP,OAC/C6D,EAAM7D,OAAS9Y,EAAK8Y,MAEtB,OAAO9Y,OAbT,GACE2c,EAAM7D,MAAQ9Y,EAAK8Y,OAClB6D,EAAM7D,QAAU9Y,EAAK8Y,OACpBrZ,EAAQkd,EAAMtT,IAAIyP,OAASrZ,EAAQmd,EAAMvT,IAAIyP,MAE/C,OAAO9Y,C,CAYb,OAAO,IACT,EAEA,YAAAgd,qBAAA,SACEpC,EACAqC,EACAC,EACAL,GAMA,IAJA,IAAMJ,EAAS,GACTU,EAAoBF,EAAc5T,GAClC9J,EAAQqb,EAAMnb,QAAQ0d,GAAmB5d,MACzCwd,EAAaxd,EAAM8E,OAChBxE,EAAI,EAAGA,EAAIkd,EAAYld,IAAK,CACnC,IAAMG,EAAOT,EAAMM,GACb8Z,EAASiB,EAAMnb,QAAQO,EAAKqJ,IAC9B6T,GAAgBvD,EAAOb,QAAU+D,EAAQlB,QAAQhC,IACnD8C,EAAO1c,KAAKC,E,CAGhB,OAAOyc,CACT,EAEA,YAAAW,uBAAA,SACExC,EACAqC,EACAC,EACAL,GAQA,IANA,IAAMJ,EAAS,GACThd,EAAUmb,EAAMnb,QAChBqa,EAAcra,EAAQwd,EAAc5T,IAAIyP,MAExCvZ,EADWE,EAAQwd,EAAc3T,MAChB/J,MACjBwd,EAAaxd,EAAM8E,OAChBxE,EAAI,EAAGA,EAAIkd,EAAYld,IAAK,CACnC,IAAMG,EAAOT,EAAMM,GACbwd,EAAiB5d,EAAQO,EAAKqJ,IAAIyP,MAEtCmE,EAAc5T,KAAOrJ,EAAKqJ,IAC1B6T,EAAeG,GACfR,EAAQlB,QAAQlc,EAAQO,EAAKqJ,OAK7B4T,EAAcnE,MAAQ9Y,EAAK8Y,OAC1BmE,EAAcnE,QAAU9Y,EAAK8Y,OAASgB,GAAeuD,IAEtDZ,EAAO1c,KAAKC,E,CAGhB,OAAOyc,CACT,EAEA,YAAAa,WAAA,SAAWC,GACT,IAAMC,EAAW,CAAC,EAIlB,OAHAD,EAAU5d,SAAQ,SAAC8d,GACZD,EAASC,EAAI9C,WAAU6C,EAASC,EAAI9C,UAAW,EACtD,IACOhc,OAAOwK,KAAKqU,GAAUnZ,MAC/B,EAEA,YAAAqZ,aAAA,SACE3e,GAMA,IAAI4e,OAAWnc,EA0Bf,OAzBA7C,OAAOwK,KAAKpK,GAAKY,SAAQ,SAACoa,GAClB,MAAwChb,EAAIgb,GAA1CC,EAAU,aAAEH,EAAS,YAAEI,EAAU,aACpC0D,GASH3D,EAAa2D,EAAS3D,YACrBA,IAAe2D,EAAS3D,YACvBH,EAAY8D,EAAS9D,WACtBG,IAAe2D,EAAS3D,YACvBH,IAAc8D,EAAS9D,WACvBI,EAAa0D,EAAS1D,cAExB0D,EAAW,CACT3D,WAAU,EACVH,UAAS,EACTI,WAAU,IAlBZ0D,EAAW,CACT3D,WAAU,EACVH,UAAS,EACTI,WAAU,EAkBhB,IACO0D,CACT,EAEA,YAAAC,MAAA,sBACQ1B,EAAU5d,KAAK4d,QAErB,GADI5d,KAAK2d,SAAS9M,QAAQ0O,IAAI,iBAAkB3B,GACb,IAA/BA,EAAQ5B,YAAYjW,OAAc,OAAO,EAC7C,IAAMhF,EAAWf,KAAKe,SAChBub,EAAQsB,EAAQxB,SD5YK,EC4YoBrb,GACzCI,EAAUmb,EAAMnb,QAChBqe,EAAa,IAAI3B,GACjBne,EAAa,CAAC,EACpB4c,EAAMtb,MAAMK,SAAQ,SAACC,GACE,EAAK4c,qBAAqB5B,EAAOhb,GACzCD,SAAQ,SAACK,GACpB,IAAI+d,EAAYte,EAAQO,EAAKqJ,IACvB0Q,EAAoB,UAAGna,EAAKkZ,MAAK,YAAI9Y,EAAK8Y,MAAK,YAAIiF,EAAUjF,OAC9D9a,EAAK+b,KACR/b,EAAK+b,GAAqB,CACxBwD,UAAW,GACXvD,WAAYpa,EAAKkZ,MACjBe,UAAW7Z,EAAK8Y,MAChBmB,WAAY8D,EAAUjF,QAE1B,IAAMuC,EAAa,CACjBV,QAASC,EAAM9a,GACfE,KAAI,EACJ0b,QAAS,MAEX1d,EAAK+b,GAAmBwD,UAAUxd,KAAKsb,EACzC,GACF,IAGA,IAAIsC,EAAWrf,KAAKof,aAAa1f,GACjC,GAAK2f,EAAL,CACAG,EAAWxD,YAAYva,KACrB,IAAI0a,GACF,EACA,EACAkD,EAAS3D,WACT2D,EAAS9D,UACT8D,EAAS1D,aAKb,IAAM+D,EAAe,SAACT,GAWpB,IATA,IAAMlD,EAASyD,EAAW7C,cACpBiC,EACJY,EAAWxD,YAAY,GAAGP,kBAAkBC,WACxCiE,EAASH,EAAWxD,YAAYD,EAAO,IAAIV,OAE3CuE,EAAqB,CAAC,EACxBC,GAAO,EACTC,EAAQ,EACNC,EAAMhf,GAAY,EAAI,E,WACjBQ,GACP,GAAIse,E,cAEJZ,EAAU5d,SAAQ,SAACmH,GACjB,IAAM+V,EAAU,IAAIyB,GAAQxX,GACtByX,EAAe,EAAK7B,iBACxB9B,EACAiC,EAAQtd,MAAM8a,EAAOxa,IACrBgd,EAAQtd,MAAM8a,EAAO,IACrBwC,GAEE0B,IAEGL,EAAaK,EAAazF,SAC7BoF,EAAaK,EAAazF,OAAS,CACjCyE,UAAW,GACX1D,UAAW0E,EAAazF,QAG5BoF,EAAaK,EAAazF,OAAOyE,UAAUxd,KAAK,CAC9C4a,QAASC,EAAM9a,GACfE,KAAMke,EACNxC,QAAS5U,IAEXsX,EAAQN,EAAWxD,YAAYD,EAAOxa,IAAI6Z,SAC1CyE,GAAO,EAEX,G,EA3BOte,EAAIwa,EAAOhW,OAAS,EAAGxE,EAAIwe,G,YAA3Bxe,GAAgCA,KA8BzC,GAAIse,EAAM,CACR,IAAMK,EAAuB,EAAKd,aAAaQ,GAC/CJ,EAAWxD,YAAYva,KACrB,IAAI0a,GACFwD,EACAG,EACAvF,EACA2F,EAAqB3E,UACrBhB,IAGJ,IAAM,EAAMiF,EAAWxD,YAAYjW,OAAS,EAC5C,OAAI,EAAK6X,QAAQ5B,YAAY,KAASwD,EAAWxD,YAAY,IAEtD0D,EACLE,EAAaM,EAAqB3E,WAAW0D,U,CAGjD,IAAMkB,EAAoB,CAAC,EAC3BN,GAAO,EACP,IAAIO,EAAU,EACdnB,EAAU5d,SAAQ,SAACmH,GACjB,IAAM+V,EAAU,IAAIyB,GAAQxX,GACtB6X,EAAmB,EAAK3B,qBAC5BpC,EACAiC,EAAQtd,MAAM8a,EAAO,IACrB6C,EACAL,GAEE8B,EAAiBta,OAAS,IAC5B8Z,GAAO,EACPO,EAAUT,EACVU,EAAiBhf,SAAQ,SAACK,GACxB,IAAMvB,EAAM,UAAGuB,EAAK8Y,MAAK,YAAIrZ,EAAQO,EAAKqJ,IAAIyP,OACzC2F,EAAYhgB,KACfggB,EAAYhgB,GAAO,CACjB8e,UAAW,GACX1D,UAAW7Z,EAAK8Y,MAChBmB,WAAYxa,EAAQO,EAAKqJ,IAAIyP,QAEjC2F,EAAYhgB,GAAK8e,UAAUxd,KAAK,CAC9B4a,QAASC,EAAM9a,GACfE,KAAI,EACJ0b,QAAS5U,GAEb,IAEJ,IAEA,IAAM8X,EAAavE,EAAOhW,O,WACjBxE,GACP,GAAIse,E,cACJ,IAAM3d,EAAQ6Z,EAAOxa,GACrB0d,EAAU5d,SAAQ,SAACmH,GACjB,IAAM+V,EAAU,IAAIyB,GAAQxX,GACtB+X,EAAqB,EAAKzB,uBAC9BxC,EACAiC,EAAQtd,MAAMiB,GACd0c,EACAL,GAEEgC,EAAmBxa,OAAS,IAC9B8Z,GAAO,EACPO,EAAUZ,EAAWxD,YAAY9Z,GAAOkZ,SACxCmF,EAAmBlf,SAAQ,SAACK,GAC1B,IAAMvB,EAAM,UAAGuB,EAAK8Y,MAAK,YAAIrZ,EAAQO,EAAKqJ,IAAIyP,OACzC2F,EAAYhgB,KACfggB,EAAYhgB,GAAO,CACjB8e,UAAW,GACX1D,UAAW7Z,EAAK8Y,MAChBmB,WAAYxa,EAAQO,EAAKqJ,IAAIyP,QAEjC2F,EAAYhgB,GAAK8e,UAAUxd,KAAK,CAC9B4a,QAASC,EAAM9a,GACfE,KAAI,EACJ0b,QAAS5U,GAEb,IAEJ,G,EA7BF,IAASjH,EAAI,EAAGA,EAAI+e,G,YAAX/e,GAAuBA,KAgChC,IAAKse,EAAM,OAAO,EAElB,IAAMW,EAA0B,EAAKpB,aAAae,GAClDX,EAAWxD,YAAYva,KACrB,IAAI0a,GACFiE,EACAT,EAAS,EACTpF,EACAiG,EAAwBjF,UACxBiF,EAAwB7E,aAG5B,IAAMhH,EAAM6K,EAAWxD,YAAYjW,OAAS,EAC5C,OAAI6X,EAAQ5B,YAAYrH,KAAS6K,EAAWxD,YAAYrH,IAEjD+K,EACLS,EACE,UAAGK,EAAwBjF,UAAS,YAAIiF,EAAwB7E,aAChEsD,UAEN,EACM9e,EAAM,UAAGkf,EAAS3D,WAAU,YAAI2D,EAAS9D,UAAS,YAAI8D,EAAS1D,YACrE,OAAO+D,EAAahgB,EAAKS,GAAK8e,UA7JT,CA8JvB,EAEA,YAAAwB,OAAA,WACE,KAAIzgB,KAAK4d,QAAQ7C,aAAe/a,KAAKyd,YAArC,CACAzd,KAAK8a,UACL,IAAMwB,EAAQtc,KAAK4d,QAAQxB,QAAQpc,KAAK8a,QAAS9a,KAAKe,UACtDf,KAAKge,kBAAkBvc,KAAK,EAAM6a,GAHqB,CAIzD,EAEA,YAAAoE,eAAA,SAAezB,GAAf,WAEE,KADgBjf,KAAKgf,WAAWC,GAClBjf,KAAKwd,aACdxd,KAAKsf,QAAV,CACAtf,KAAKygB,SAEL,IAAM3R,EAAU9O,KAAK4d,QAAQ7C,aACvBgB,EAAS/b,KAAK4d,QAAQjB,cACtBgD,EAAS3f,KAAK4d,QAAQ5B,YAAYD,EAAO,IAAIV,OAC7CuD,EAAe5e,KAAK4d,QAAQ5B,YAAY,GAAGP,kBAC9CC,WAEGyE,EAAoB,CAAC,EACrBP,EAAqB,CAAC,EAE5BX,EAAU5d,SAAQ,SAACmH,GAKjB,IAJA,IAAM8T,EAAQ,EAAKiB,OAAO/U,EAAE6T,SACtBlb,EAAUmb,EAAMnb,QAChBod,EAAU,IAAIyB,GAAQxX,GAEnBjH,EAAIwa,EAAOhW,OAAS,EAAGxE,GAAK,EAAGA,IAAK,CAC3C,IAAM0e,EAAe,EAAK7B,iBACxB9B,EACAiC,EAAQtd,MAAM8a,EAAOxa,IACrBgd,EAAQtd,MAAM8a,EAAO,IACrBwC,GAEF,GAAI0B,EAAc,CAChB,IAAM9f,EAAM,UAAG,EAAKyd,QAAQ5B,YAAYD,EAAOxa,IAAI6Z,SAAQ,YACzD6E,EAAazF,OAEVoF,EAAazf,KAChByf,EAAazf,GAAO,CAClB8e,UAAW,GACXvC,SAAU,EAAKkB,QAAQ5B,YAAYD,EAAOxa,IAAI6Z,SAC9CG,UAAW0E,EAAazF,QAE5BoF,EAAazf,GAAK8e,UAAUxd,KAAK,CAC/B4a,QAAS7T,EAAE6T,QACX3a,KAAMue,EACN7C,QAAS5U,G,EAMf,KAAIsG,GAAW,EAAK4O,YAApB,CACyB,EAAKgB,qBAC5BpC,EACAiC,EAAQtd,MAAM8a,EAAO,IACrB6C,EACAL,GAEeld,SAAQ,SAACK,GACxB,IAAMvB,EAAM,UAAGwf,EAAM,YAAIje,EAAK8Y,MAAK,YAAIrZ,EAAQO,EAAKqJ,IAAIyP,OACnD2F,EAAYhgB,KACfggB,EAAYhgB,GAAO,CACjB8e,UAAW,GACXxC,WAAYkD,EACZpE,UAAW7Z,EAAK8Y,MAChBmB,WAAYxa,EAAQO,EAAKqJ,IAAIyP,QAEjC2F,EAAYhgB,GAAK8e,UAAUxd,KAAK,CAC9B4a,QAAS7T,EAAE6T,QACX3a,KAAI,EACJ0b,QAAS5U,GAEb,I,eAGSjH,GACoB,EAAKud,uBAC9BxC,EACAiC,EAAQtd,MAAM8a,EAAOxa,IACrBqd,EACAL,GAEiBld,SAAQ,SAACK,GAC1B,IAAMvB,EAAM,UAAG,EAAKyd,QAAQ5B,YAAYD,EAAOxa,IAAI6Z,SAAQ,YACzD1Z,EAAK8Y,MAAK,YACRrZ,EAAQO,EAAKqJ,IAAIyP,OAChB2F,EAAYhgB,KACfggB,EAAYhgB,GAAO,CACjB8e,UAAW,GACXxC,WAAY,EAAKmB,QAAQ5B,YAAYD,EAAOxa,IAAI6Z,SAChDG,UAAW7Z,EAAK8Y,MAChBmB,WAAYxa,EAAQO,EAAKqJ,IAAIyP,QAEjC2F,EAAYhgB,GAAK8e,UAAUxd,KAAK,CAC9B4a,QAAS7T,EAAE6T,QACX3a,KAAI,EACJ0b,QAAS5U,GAEb,G,EAvBF,IAASjH,EAAI,EAAGA,EAAIwa,EAAOhW,OAAQxE,I,EAA1BA,EAxB6B,CAiDxC,IAGAlB,OAAOwK,KAAK+U,GAAcve,SAAQ,SAAClB,GAC3B,MAA0Byf,EAAazf,GAArCuc,EAAQ,WAAEnB,EAAS,YAC3B,EAAKqC,QAAQ5B,YAAYva,KACvB,IAAI0a,GAAQwD,EAAQjD,EAAU,KAAMnB,EAAW,OAEjD,EAAKmF,eAAed,EAAazf,GAAK8e,WACtC,EAAKrB,QAAQ5B,YAAY9V,KAC3B,IAGA7F,OAAOwK,KAAKsV,GAAa9e,SAAQ,SAAClB,GAC1B,MAAwCggB,EAAYhgB,GAAlDsc,EAAU,aAAElB,EAAS,YAAEI,EAAU,aACzC,EAAKiC,QAAQ5B,YAAYva,KACvB,IAAI0a,GACFM,EACAkD,EAAS,EACTpF,EACAgB,EACAI,IAGJ,EAAK+E,eAAeP,EAAYhgB,GAAK8e,WACrC,EAAKrB,QAAQ5B,YAAY9V,KAC3B,GAtHyB,CAuH3B,EAEA,YAAAya,+BAAA,WACE,IAAMpD,EAASvd,KAAKud,OACdxc,EAAWf,KAAKe,SAChByc,EAAaxd,KAAKwd,WAClBO,EAAyB/d,KAAK+d,uBAChC6C,EAAmB,CAAC,EACtBC,EAAsB,CAAC,EAEnBC,EAAmB,CAAC,EAEpBC,EAA2B,CAAC,EA6DlC,OA5DA1gB,OAAOwK,KAAK0S,GAAQlc,SAAQ,SAAClB,GAE3B,IAAMmc,EAAQiB,EAAOpd,GACfgB,EAAUmb,EAAMnb,QAEtBmb,EAAMtb,MAAMK,SAAQ,SAACC,EAAMC,GAEzB,IAAMyf,EAAY1f,EAAKkZ,MACjByG,EAAe,UAAG9gB,EAAG,YAAI6gB,GAC/B,IAAKF,EAAiBG,GAAe,CACnC,IAAInG,EAAU8F,EAAiBI,IAAc,EAC7ClG,IACA8F,EAAiBI,GAAalG,C,CAEhCgG,EAAiBG,GAAgB,CAC/BC,SAAU/gB,EACVqa,MAAOwG,GAGT1f,EAAKL,MAAMI,SAAQ,SAACK,GAClB,IAAIga,EAAasF,EACbrF,EAAaxa,EAAQO,EAAKqJ,IAAIyP,MAClC,IAAKzZ,GAAY2a,EAAaC,EAAY,CACxC,IAAMlC,EAAMkC,EACZA,EAAaD,EACbA,EAAajC,C,CAEf,IAAM8B,EAAY7Z,EAAK8Y,MAEjB2G,EAAuB,UAAGhhB,EAAG,YAAIub,EAAU,YAAIH,EAAS,YAAII,GAC5DyF,EAAkB,UAAG1F,EAAU,YAAIH,EAAS,YAAII,GAEtD,IAAKkF,EAAoBO,GAAkB,CACzC,IAAItG,EAAU+F,EAAoBO,IAAoB,EACtDtG,IACA+F,EAAoBO,GAAmBtG,C,CAEzCiG,EAAyBI,GAAwB,CAC/C9E,QAASlc,EACTub,WAAU,EACVH,UAAS,EACTI,WAAU,EAEd,GACF,GACF,IAGAtb,OAAOwK,KAAK+V,GAAkBvf,SAAQ,SAACmZ,GAErC,KADcoG,EAAiBpG,GACnBgD,GAAZ,CACA,IAAM6D,EAAI,CAAErgB,MAAO,GAAIC,MAAO,IAC9BogB,EAAErgB,MAAMS,KAAK,CACXD,GAAI,IACJgZ,MAAK,IAEPuD,EAAuBtc,KAAK4f,EANE,CAQhC,IAEOtD,CACT,EAEA,YAAAuD,IAAA,sBAIE,GAFAthB,KAAK+d,uBAAyB/d,KAAK2gB,mCAE/B3gB,KAAK0d,WAAa,GAAtB,CAEA,IAAMH,EAASvd,KAAKud,OAId7d,GAHWM,KAAKe,SAGH,CAAC,GACpBV,OAAOwK,KAAK0S,GAAQlc,SAAQ,SAACgb,GAC3B,IAAMC,EAAQiB,EAAOlB,GACflb,EAAUmb,EAAMnb,QAEtBmb,EAAMtb,MAAMK,SAAQ,SAACC,GACM,EAAK4c,qBAAqB5B,EAAOhb,GAEzCD,SAAQ,SAACK,GACxB,IAAI2Z,EAASla,EAAQO,EAAKqJ,IACpB0Q,EAAoB,UAAGna,EAAKkZ,MAAK,YAAI9Y,EAAK8Y,MAAK,YAAIa,EAAOb,OAC3D9a,EAAK+b,KACR/b,EAAK+b,GAAqB,CACxBwD,UAAW,GACXvD,WAAYpa,EAAKkZ,MACjBe,UAAW7Z,EAAK8Y,MAChBmB,WAAYN,EAAOb,QAEvB,IAAMuC,EAAa,CACjBV,QAAO,EACP3a,KAAI,EACJ0b,QAAS,MAEX1d,EAAK+b,GAAmBwD,UAAUxd,KAAKsb,EACzC,GACF,GACF,IAGA1c,OAAOwK,KAAKnL,GAAM2B,SAAQ,SAACoa,GACnB,MAAmD/b,EACvD+b,GADMwD,EAAS,YAAEvD,EAAU,aAAEH,EAAS,YAAEI,EAAU,aAIpD,EAAKiC,QAAQ5B,YAAYva,KACvB,IAAI0a,GAAQ,EAAG,EAAGT,EAAYH,EAAWI,IAE3C,EAAK+E,eAAezB,GACpB,EAAKrB,QAAQ5B,YAAY9V,KAC3B,GA7C+B,CA8CjC,EACF,EAznBA,GA0rBMqb,GAAqB,UC/1B3B,IAAMC,GAAqB,SACzB1gB,EACA2gB,EACAC,EACA9U,QADA,IAAA8U,IAAAA,EAAA,gBACA,IAAA9U,IAAAA,EAAA,GAEA,IAAM+U,EAAwB,GACxB3gB,EAAQF,EAAUE,MAIxB,OAHAygB,EAAIpgB,SAAQ,SAACuT,EAAerT,GAC1BogB,EAAMlgB,KAAKmgB,GAAkB5gB,EAAO4T,EAAKrT,EAAGmgB,EAAe9U,GAC7D,IACO+U,CACT,EAEMC,GAAoB,SAAC5gB,EAAO4T,EAAKrT,EAAGmgB,EAAe9U,GACvD,IAAMiV,EAAe,CAACtgB,GAChBkE,EAAY,GACZqc,EAAgB,CAAC,EAiBvB,OAhBAlN,EAAIvT,SAAQ,SAACoN,EAAGzE,GACd,GAAIyE,GAAK7B,GAAKrL,IAAMyI,EAAG,CACrB6X,EAAapgB,KAAKuI,GAClBvE,EAAUhE,KAAKT,EAAMgJ,IACrB,IAAMwQ,EAAQxZ,EAAMgJ,GAAG0X,GAClBI,EAActH,IAEjBsH,EAActH,GAAO3C,QACrBiK,EAActH,GAAOuH,MAAMtgB,KAAKgN,IAHPqT,EAActH,GAAS,CAAE3C,MAAO,EAAGkK,MAAO,CAACtT,G,CAM1E,IAEApO,OAAOwK,KAAKiX,GAAezgB,SAAQ,SAAAmZ,GACjCsH,EAActH,GAAOuH,MAAQD,EAActH,GAAOuH,MAAM9Q,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,GACzE,IACO,CACLiI,QAAS3I,EACT8C,OAAQrD,EAAMO,GAAGC,GACjBwgB,SAAUH,EACVpc,UAAS,EACTwc,YAAaJ,EAAa9b,OAAS,EACnCmc,kBAAmBJ,EAEvB,EAsEMK,GAAmC,SACvCC,EACAC,EACAvhB,EACAwhB,GAEA,IAAMthB,EAAQF,EAAUE,MAyBxB,OAxBKshB,IAAuBA,EAAwB,CAAC,GACrDjiB,OAAOwK,KAAKuX,GAAa/gB,SAAQ,SAAAlB,G,QAC/B,IAAImiB,IAAyBA,EAAsBniB,GAAnD,CACAmiB,EAAsBniB,GAAO,CAAEa,MAAO,GAAIC,MAAO,IACjD,IAAMshB,EAAOH,EAAYjiB,GACnBqiB,EAA4C,QAAzB,EAAAH,EAAcE,EAAKnZ,cAAM,eAAE4Y,SAC9CS,EAAwC,QAAvB,EAAAJ,EAAcE,EAAKxC,YAAI,eAAEiC,SAChD,GAAKQ,GAAqBC,EAA1B,CACA,IAAMC,EAAS,IAAIza,IAAIwa,GACjBE,EAAYH,EAAiBhe,QAAO,SAAAoe,GAAK,OAAAF,EAAOra,IAAIua,EAAX,IAC/C,GAAKD,GAAcA,EAAU5c,OAA7B,CAGA,IAFA,IAAM8c,EAAiB,CAAC,EAClBC,EAAkBH,EAAU5c,OACzBxE,EAAI,EAAGA,EAAIuhB,EAAiBvhB,IAAK,CACxC,IAAMD,EAAON,EAAM2hB,EAAUphB,IAC7B+gB,EAAsBniB,GAAKa,MAAMS,KAAKH,GACtCuhB,EAAevhB,EAAKE,KAAM,C,CAG5BV,EAAUG,MAAMI,SAAQ,SAAAK,GAClBmhB,EAAenhB,EAAKC,SAAWkhB,EAAenhB,EAAKE,SACrD0gB,EAAsBniB,GAAKc,MAAMQ,KAAKC,EAC1C,GAZ2C,CAHK,CALe,CAqBjE,IACO4gB,CACT,EASMS,GAAkB,SAACzG,EAAO0G,EAAWtB,EAAeuB,G,QAClD9hB,EAAU,CAAC,EACjBmb,EAAMtb,MAAMK,SAAQ,SAAAC,GAClBH,EAAQG,EAAKE,IAAMF,CACrB,IACA,IAAIuW,EAAQ,EACZ,QAAqB,QAAhB,EAAAmL,aAAS,EAATA,EAAW/hB,aAAK,eAAE8E,UAA0B,QAAhB,EAAAid,aAAS,EAATA,EAAWhiB,aAAK,eAAE+E,QAAS,EAAU,GACtEuW,EAAMrb,MAAMI,SAAQ,SAAA8b,GAClB,IAAM+F,EAAc/hB,EAAQgc,EAAExb,QAAQ+f,GAChCyB,EAAchiB,EAAQgc,EAAEvb,QAAQ8f,GAChC0B,EAAgBJ,aAAS,EAATA,EAAWhiB,MAAM,GAAG0gB,GACpC2B,EAAgBL,aAAS,EAATA,EAAWhiB,MAAM,GAAG0gB,GACpC4B,EAAeN,aAAS,EAATA,EAAW/hB,MAAM,GAAGgiB,GAErC9F,EAAE8F,KAAmBK,IAEtBJ,IAAgBE,GAAiBD,IAAgBE,GACjDH,IAAgBG,GAAiBF,IAAgBC,IAElDvL,GAEJ,IACOA,EACT,EA6EM0L,GAAc,SAACviB,EAAO0gB,GAC1B,IAAMvgB,EAAmB,CAAC,EACxByZ,EAAyB,CAAC,EAO5B,OANA5Z,EAAMK,SAAQ,SAACC,EAAMC,GACnBJ,EAAQG,EAAKE,IAAM,CAAEmT,IAAKpT,EAAGD,KAAI,EAAEiF,OAAQ,EAAGE,SAAU,EAAGC,UAAW,GACtE,IAAM8T,EAAQlZ,EAAKogB,GACd9G,EAAaJ,KAAQI,EAAaJ,GAAS,IAChDI,EAAaJ,GAAO/Y,KAAKH,EAC3B,IACO,CAAEH,QAAO,EAAEyZ,aAAY,EAChC,EAEM4I,GAAc,SAClBviB,EACAgiB,EACA9hB,GAEA,IAAMsZ,EAAU,CAAC,EACfI,EAAe,CAAC,EAkBlB,OAjBA5Z,EAAMI,SAAQ,SAACK,EAAMH,GACnBkZ,EAAQ,UAAG/V,IAAc,CAAEiQ,IAAKpT,EAAGG,KAAI,GACvC,IAAM8Y,EAAQ9Y,EAAKuhB,GACdpI,EAAaL,KAAQK,EAAaL,GAAS,IAChDK,EAAaL,GAAO/Y,KAAKC,GAEzB,IAAM+hB,EAAatiB,EAAQO,EAAKC,QAC5B8hB,IACFA,EAAWld,SACXkd,EAAW/c,aAEb,IAAMZ,EAAa3E,EAAQO,EAAKE,QAC5BkE,IACFA,EAAWS,SACXT,EAAWW,WAEf,IACO,CAAEgU,QAAO,EAAEI,aAAY,EAChC,EAQM6I,GAAY,SAAC1iB,EAAOygB,EAAK1gB,GAC7B,IAAMgF,EAAS0b,EAAI1b,OACblC,EAAM,CAAC,EAYb,OAXA4d,EAAIpgB,SAAQ,SAACuT,EAAKrT,GAGhB,IAFA,IAAM6H,EAAQrI,EAAW,EAAIQ,EAAI,EAC3BoiB,EAAM3iB,EAAMO,GAAGC,GACZwI,EAAIZ,EAAOY,EAAIjE,EAAQiE,IAC9B,GAAIzI,IAAMyI,EAAV,CACA,IAAM4Z,EAAM5iB,EAAMgJ,GAAGxI,GACfwO,EAAO4E,EAAI5K,GACjBnG,EAAI,UAAG8f,EAAG,YAAIC,IAAS5T,EAClBjP,IAAU8C,EAAI,UAAG+f,EAAG,YAAID,IAAS3T,EAJjB,CAMzB,IACOnM,CACT,EAQMggB,GAAa,SACjBvH,EACAwH,EACAC,EACA5iB,EACA6iB,EACAC,EACAjB,EACAtB,EACAuB,EACAiB,EACAC,G,MAEMhkB,EAAM,UAAG2jB,EAAMtiB,GAAE,YAAIuiB,EAAMviB,IACjC,GAAI0iB,GAAgBA,EAAa/jB,GAAM,OAAO+jB,EAAa/jB,GAC3D,IAAIikB,EAAoBD,EAA0BA,EAAwBhkB,QAAO+C,EAEjF,IAAKkhB,EAAmB,CACtB,IAAMC,IAAO,MACVlkB,GAAM,CACLiJ,MAAOjI,EAAQ2iB,EAAMtiB,IAAImT,IACzBoL,IAAK5e,EAAQ4iB,EAAMviB,IAAImT,IACvB9B,SAAUmR,G,GAUdI,GANAD,EAA0BhC,GACxBkC,EACAJ,EACA3H,EACA6H,IAE0ChkB,E,CAG9C,OAAO4iB,GAAgBqB,EAAmBpB,EAAWtB,EAAeuB,EACtE,EAKMqB,GAAiC,SAACC,EAA8BC,EAAeC,EAAgBC,G,UAC/FC,EAAuE,QAA3C,EAAAJ,EAA6BC,UAAc,eAAEje,OACzEqe,EAAyE,QAA3C,EAAAL,EAA6BC,UAAc,eAAE/d,SAC3Eoe,EAA0E,QAA3C,EAAAN,EAA6BC,UAAc,eAAE9d,UAwBhF,YAtBoDxD,IAAhDqhB,EAA6BC,KAC/BG,EAA4B7a,IAC5B8a,EAA8B9a,IAC9B+a,EAA+B/a,IAC/B4a,EAAoBF,GAAenjB,SAAQ,SAAAyjB,GACzC,IAAMC,EAAoBN,EAAeK,EAAqBtjB,IAAI+E,OAC9Doe,EAA4BI,IAC9BJ,EAA4BI,GAC9B,IAAMC,EAAsBP,EAAeK,EAAqBtjB,IAAIiF,SAChEme,EAA8BI,IAChCJ,EAA8BI,GAChC,IAAMC,EAAuBR,EAAeK,EAAqBtjB,IAAIkF,UACjEme,EAA+BI,IACjCJ,EAA+BI,EACnC,IACAV,EAA6BC,GAAiB,CAC5Cje,OAAQoe,EACRle,SAAUme,EACVle,UAAWme,IAIR,CACLF,0BAAyB,EACzBC,4BAA2B,EAC3BC,6BAA4B,EAEhC,ECvbA,kBAME,WAAYK,QAAA,IAAAA,IAAAA,EAAA,IACVllB,KAAK8D,WAAa,IAAI,EACtB9D,KAAKklB,QAAUA,CACjB,CAqDF,OAnDE,sBAAI,qBAAM,C,IAAV,WACE,OAAOllB,KAAK8D,WAAWL,UAAUsC,MACnC,E,gCAKA,YAAAhC,QAAA,WACE,OAAQ/D,KAAK8D,WAAWvB,IAC1B,EAKA,YAAA4iB,WAAA,WACE,OAAOnlB,KAAKyD,UAAUsC,QAAU/F,KAAKklB,OACvC,EAKA,YAAAlhB,KAAA,WACE,OAAIhE,KAAK+D,UACA,KAIF/D,KAAK8D,WAAWvB,KAAKL,KAC9B,EAEA,YAAAT,KAAA,SAAKS,GACHlC,KAAK8D,WAAWpB,QAAQR,GACpBlC,KAAK+F,OAAS/F,KAAKklB,SACrBllB,KAAK8D,WAAWX,YAEpB,EAEA,YAAA+C,IAAA,WACE,IAAM/B,EAAanE,KAAK8D,WAAWT,aACnC,OAAOc,EAAaA,EAAWjC,MAAQ,IACzC,EAEA,YAAAuB,QAAA,WACE,OAAOzD,KAAK8D,WAAWL,UAAUI,KAAI,SAACvC,GAAS,OAAAA,EAAKY,KAAL,GACjD,EAEA,YAAAsH,MAAA,WACE,MAAQxJ,KAAK+D,WACX/D,KAAKkG,KAET,EACF,EA9DA,GCwDA,UACEkf,aAAY,EACZC,mBCnByB,SACzBvkB,EACAsG,EACAke,EACAvkB,QAAA,IAAAA,IAAAA,GAAA,GAEA,IAAM8F,EApCR,SAAuBA,QAAA,IAAAA,IAAAA,EAAiC,CAAC,GACvD,IAKQQ,EALFC,EAAoBT,EAEpBU,EAAe,WAAO,EAEtBC,GACEH,EAAO,CAAC,EACP,SAAC,G,IACA7F,EADM,OAEZ,OAAK6F,EAAK7F,KACR6F,EAAK7F,IAAM,GACJ,EAGX,GAOF,OAJA8F,EAAkBL,eAAiBJ,EAAUI,gBAAkBO,EAC/DF,EAAkBR,MAAQD,EAAUC,OAASS,EAC7CD,EAAkBJ,MAAQL,EAAUK,OAASK,EAEtCD,CACT,CAcoBie,CAAcD,GAC1BE,EAAY,IAAI,EAEd,EAAe1kB,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAGlBukB,EAAUvhB,QAAQmD,GAKlB,IAHA,IAAIR,EAAe,G,aAIjB,IAAM5D,EAAsBwiB,EAAUthB,UACtC2C,EAAUC,MAAM,CACdC,QAAS/D,EACTgE,SAAUJ,IAIZxC,EAAapB,EAAa/B,EAAOF,EAAW,cAAWmC,GAAW7B,SAAQ,SAACuC,GAEvEiD,EAAUI,eAAe,CACvBD,SAAUJ,EACVG,QAAS/D,EACTb,KAAMyB,KAGR4hB,EAAUvhB,QAAQL,EAEtB,IAEAiD,EAAUK,MAAM,CACdH,QAAS/D,EACTgE,SAAUJ,IAIZA,EAAe5D,C,GA1BTwiB,EAAUzhB,W,GA4BpB,EDzBE0hB,mBAAkB,EAClBC,UAAS,EACTC,Y7ChCyB,SAAC7kB,EAAsBuD,GAEhD,OADmBkC,EAAOzF,GACXuD,GACNkC,EAAOzF,GAAWuD,GAAQoC,SAE5B,CACT,E6C2BEmf,a7CpB0B,SAAC9kB,EAAsBuD,GAEjD,OADmBkC,EAAOzF,GACXuD,GACNkC,EAAOzF,GAAWuD,GAAQqC,UAE5B,CACT,E6CeEmf,YAAW,EACXC,oBA1C0B,EA2C1BC,gB3C4Q6B,SAC7BjlB,EACAC,EACA2G,EACAC,GAEA,YAFA,IAAAA,IAAAA,GAAA,GAEI5G,EAAiB8H,EAAuB/H,EAAW4G,EAASC,GACzDF,EAAyB3G,EAAW4G,EAASC,EACtD,E2CnREkB,uBAAsB,EACtBpB,yBAAwB,EACxBN,iBAAgB,EAChB6e,SAAQ,EACRC,YEpDyB,SACzBnlB,EACAsI,EACA2W,EACAhf,G,MAEA,GAAIqI,IAAU2W,EAAK,MAAO,CAAC,CAAC3W,IAEpB,MAAetI,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAEZmF,EAAU,CAACgD,GACX8c,IAAS,MAAM9c,IAAQ,EAAI,GAC3BtB,EAAoB,GACpB6H,EAAU,GACZlK,EAAY1E,EACZqD,EAAagF,EAAOnI,EAAO,UAC3BmD,EAAagF,EAAOnI,GAGxB,IAFA6G,EAAMrG,KAAKgE,GAEJW,EAAQL,OAAS,GAAK+B,EAAM/B,OAAS,GAAG,CAC7C,IAAMogB,EAAWre,EAAMA,EAAM/B,OAAS,GACtC,GAAIogB,EAASpgB,OAAb,CACE,IAAMqgB,EAAQD,EAAS7L,QAgBzB,GAfM8L,IACFhgB,EAAQ3E,KAAK2kB,GACbF,EAAUE,IAAS,EACnB3gB,EAAY1E,EACRqD,EAAagiB,EAAOnlB,EAAO,UAC3BmD,EAAagiB,EAAOnlB,GACxB6G,EAAMrG,KAAKgE,EAAUjB,QAAO,SAAA6B,GAAY,OAAC6f,EAAU7f,EAAX,MASxCD,EAAQA,EAAQL,OAAS,KAAOga,EAAK,CACvC,IAAMjX,EAAO1C,EAAQvC,KAAI,SAAAvC,GAAQ,OAAAA,CAAA,IACjCqO,EAAQlO,KAAKqH,GAEPxH,EAAO8E,EAAQF,MACrBggB,EAAU5kB,IAAQ,EAClBwG,EAAM5B,K,MAvBR,CAWE,IAAM5E,EAAO8E,EAAQF,MACrBggB,EAAU5kB,IAAQ,EAClBwG,EAAM5B,K,EAcV,OAAOyJ,CACT,EFIE0W,iBErE8B,SAC9BvlB,EACAsI,EACA2W,EACAhf,EACA2N,GAEM,MAA4B,EAChC5N,EACAsI,EACArI,EACA2N,GAJM3I,EAAM,SAAE+C,EAAI,OAAE6G,EAAO,UAM7B,MAAO,CAAE5J,OAAQA,EAAOga,GAAMjX,KAAMA,EAAKiX,GAAMpQ,QAASA,EAAQoQ,GAClE,EFwDEuG,cAAa,EACbC,iBG/DuB,SACvBzlB,EACAC,EACA2N,EACA8X,QAFA,IAAAzlB,IAAAA,GAAA,QACA,IAAA2N,IAAAA,EAAA,eACA,IAAA8X,IAAAA,EAAA,KAGQ,MAA2B1lB,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EAExBuT,EAAW,CAAC,EACZrT,EAAU,CAAC,EAEjBH,EAAMK,SAAQ,SAACC,EAAMC,GACnB,IAAMkT,EAAc/P,IACpBpD,EAAK+R,UAAYoB,EACjBD,EAASC,GAAO,CACdjT,GAAIiT,EACJzT,MAAO,CAACM,IAEVH,EAAQG,EAAKE,IAAM,CACjBF,KAAI,EACJqT,IAAKpT,EAET,IAGA,IAAMwR,EAAY,EAAajS,EAAWC,GAEpCiS,EAAK,GAQLvN,EAAY,CAAC,EACnBsN,EAAU1R,SAAQ,SAACuT,EAAKrT,GACtB,IAAIqL,EAAI,EACFiI,EAAM7T,EAAMO,GAAGC,GACrBiE,EAAUoP,GAAO,CAAC,EAClBD,EAAIvT,SAAQ,SAACyT,EAAO9K,GAClB,GAAK8K,EAAL,CACAlI,GAAKkI,EACL,IAAMC,EAAM/T,EAAMgJ,GAAGxI,GACrBiE,EAAUoP,GAAKE,GAAOD,CAHJ,CAIpB,IACA9B,EAAGvR,KAAKmL,EACV,IAIA,IAFA,IAAIsI,EAAO,E,aAGT,IAAIuR,GAAU,EAuCd,GAtCAzlB,EAAMK,SAAQ,SAAAC,GACZ,IAAMolB,EAAmB,CAAC,EAC1BrmB,OAAOwK,KAAKpF,EAAUnE,EAAKE,KAAKH,SAAQ,SAAA+G,GACtC,IAAMue,EAAiBlhB,EAAUnE,EAAKE,IAAI4G,GAEpCoO,EADerV,EAAQiH,GAAY9G,KACF+R,UAClCqT,EAAiBlQ,KAAoBkQ,EAAiBlQ,GAAqB,GAChFkQ,EAAiBlQ,IAAsBmQ,CACzC,IAEA,IAAIC,GAAY,IACZC,EAAiB,GASrB,GARAxmB,OAAOwK,KAAK6b,GAAkBrlB,SAAQ,SAAAgS,GAChCuT,EAAYF,EAAiBrT,IAC/BuT,EAAYF,EAAiBrT,GAC7BwT,EAAiB,CAACxT,IACTuT,IAAcF,EAAiBrT,IACxCwT,EAAeplB,KAAK4R,EAExB,IAC8B,IAA1BwT,EAAe9gB,QAAgB8gB,EAAe,KAAOvlB,EAAK+R,UAA9D,CACA,IAAMyT,EAAiBD,EAAelhB,QAAQrE,EAAK+R,WAEnD,GADIyT,GAAkB,GAAGD,EAAehb,OAAOib,EAAgB,GAC3DD,GAAkBA,EAAe9gB,OAAQ,CAC3C0gB,GAAU,EAGV,IAAM/Q,EAAclB,EAASlT,EAAK+R,WAC5B+D,EAAuB1B,EAAY1U,MAAM2E,QAAQrE,GACvDoU,EAAY1U,MAAM6K,OAAOuL,EAAsB,GAG/C,IAAM2P,EAAYliB,KAAKqU,MAAMrU,KAAKC,SAAW+hB,EAAe9gB,QACtD0P,EAAcjB,EAASqS,EAAeE,IAC5CtR,EAAYzU,MAAMS,KAAKH,GACvBA,EAAK+R,UAAYoC,EAAYjU,E,CAfgD,CAiBjF,KACKilB,E,cACLvR,G,EAzCKA,EAAOsR,G,gBA6CdnmB,OAAOwK,KAAK2J,GAAUnT,SAAQ,SAAAgS,GAC5B,IAAMkE,EAAU/C,EAASnB,GACpBkE,EAAQvW,OAAUuW,EAAQvW,MAAM+E,eAC5ByO,EAASnB,EAEpB,IAGA,IAAMqE,EAAe,GACfC,EAAiB,CAAC,EACxB1W,EAAMI,SAAQ,SAAAK,GACJ,IAAAC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzB8N,EAAS9N,EAAKgN,IAAuB,EACrC6G,EAAkBpU,EAAQQ,GAAQL,KAAK+R,UACvCmC,EAAkBrU,EAAQS,GAAQN,KAAK+R,UACvCuE,EAAY,UAAGrC,EAAe,cAAMC,GAC1C,GAAImC,EAAeC,GACjBD,EAAeC,GAAWpI,QAAUA,EACpCmI,EAAeC,GAAWC,YACrB,CACL,IAAMC,EAAU,CACdnW,OAAQ4T,EACR3T,OAAQ4T,EACRhG,OAAM,EACNqI,MAAO,GAETF,EAAeC,GAAaE,EAC5BJ,EAAajW,KAAKqW,E,CAEtB,IAEA,IAAMC,EAAgB,GAItB,OAHA1X,OAAOwK,KAAK2J,GAAUnT,SAAQ,SAAAgS,GAC5B0E,EAActW,KAAK+S,EAASnB,GAC9B,IACO,CACLmB,SAAUuD,EACVL,aAAY,EAEhB,EHxEEsP,QAAO,EACPC,SI/De,SACbnmB,EACAC,EACA2N,EACAwF,EACAC,EACAxC,EACAC,EACAwC,GAEF,YARE,IAAArT,IAAAA,GAAA,QACA,IAAA2N,IAAAA,EAAA,eACA,IAAAwF,IAAAA,EAAA,WACA,IAAAC,IAAAA,OAAA,QACA,IAAAxC,IAAAA,EAAA,SACA,IAAAC,IAAAA,EAAA,CAA4B,YAC5B,IAAAwC,IAAAA,EAAA,GAEK,EAAQtT,EAAWC,EAAU2N,EAAoBwF,GAAW,EAAMC,EAAaxC,EAAcC,EAAgBwC,EACtH,EJqDE8S,MKrEY,SACVpmB,EACA8L,G,WAAA,IAAAA,IAAAA,EAAA,GAKA,IAHA,IAAMkF,EAAO,EAAMhR,GACX,EAAegR,EAAI,MAAnB9Q,OAAK,IAAG,KAAE,EACZ,EAAe8Q,EAAI,MAAnB7Q,OAAK,IAAG,KAAE,E,aAGZ,IAAMuF,EAAU,EAAO,CAAExF,MAAK,EAAEC,MAAK,IAC/ByG,EAAUrH,OAAOwK,KAAKrE,GAE5BkB,EAAQuJ,MAAK,SAACjP,EAAGC,GAAC,QAAK,OAAU,QAAV,EAAAuE,EAAQxE,UAAE,eAAEuE,SAAmB,QAAV,EAAAC,EAAQvE,UAAE,eAAEsE,OAAM,IAC9D,IAAM4gB,EAAazf,EAAQ,GAC3B,IAAK1G,EAAM+E,SAA6B,QAAnB,EAAAS,EAAQ2gB,UAAW,eAAE5gB,SAAUqG,E,cAGpD,IAAM0H,EAActT,EAAM0H,WAAU,SAAApH,GAAQ,OAAAA,EAAKE,KAAO2lB,CAAZ,IAE5CnmB,EAAM6K,OAAOyI,EAAa,GAE1BrT,EAAQA,EAAMuD,QAAO,SAAA9C,GAAQ,QAAEA,EAAKC,SAAWwlB,GAAczlB,EAAKE,SAAWulB,EAAhD,G,kBAGjC,MAAO,CAAEnmB,MAAK,EAAEC,MAAK,EACzB,EL6CEmmB,OTlDa,SACbtV,EACAlF,EACAuH,EACAxC,EACAC,EACAe,QAJA,IAAA/F,IAAAA,EAAA,QACA,IAAAuH,IAAAA,OAAA,QACA,IAAAxC,IAAAA,EAAA,SACA,IAAAC,IAAAA,EAAA,CAA4B,YAC5B,IAAAe,IAAAA,EAA6BrB,EAAasB,mBAElC,MAA2Bd,EAAI,MAA/B9Q,OAAK,IAAG,KAAE,EAAE,EAAe8Q,EAAI,MAAnB7Q,OAAK,IAAG,KAAE,EAExBomB,EAAqB,CACzB7S,SAAU,CACR,CACEhT,GAAI,IACJR,MAAK,IAGT0W,aAAc,IAIhB,GAAI/E,IAAiBrB,EAAasB,oBAAsB5R,EAAMmR,OAAM,SAAA7Q,GAAQ,OAAAA,EAAKV,eAAeuT,EAApB,IAC1E,OAAOkT,EAIT,IAAIhT,EAAa,GAEbd,EAAsB,GAK1B,GAJIZ,IAAiBrB,EAAasB,oBAChCyB,EAAa9C,EAAiBvQ,EAAOmT,GACrCZ,EAAsB9B,EAAO4C,EAAY1C,EAAcC,KAEpD2B,EAAoBxN,OACvB,OAAOshB,EAOT,IALA,IAAMC,EAA0Bvb,EAAKwH,EAAoB1P,KAAI,SAAAwI,GAAQ,OAAAA,EAAK+B,KAAK,GAAV,KAE/DmZ,EAAS1iB,KAAKgB,IAAI+G,EAAG5L,EAAM+E,OAAQuhB,EAAwBvhB,QAGxDxE,EAAI,EAAGA,EAAIP,EAAM+E,OAAQxE,IAChCP,EAAMO,GAAG+S,YAAc/S,EAGzB,IAAMimB,EAAY,GACZC,EAAoB,GACpBjT,EAAW,GACjB,IAASjT,EAAI,EAAGA,EAAIgmB,EAAQhmB,IAC1B,GAAU,IAANA,EAAS,CAEX,IAAMmmB,EAAc7iB,KAAKqU,MAAMrU,KAAKC,SAAW9D,EAAM+E,QAC7C4M,IACDrB,EAAasB,kBAChB4U,EAAUjmB,GAAKgS,EAAoBmU,GAGnCF,EAAUjmB,GAAK,GAGnBkmB,EAAkBhmB,KAAKimB,GACvBlT,EAASjT,GAAK,CAACP,EAAM0mB,IACrB1mB,EAAM0mB,GAAarU,UAAYqB,OAAOnT,E,KACjC,CAIL,IAHA,IAAIomB,GAAc,IACdC,EAAuB,E,WAElB3U,GACP,IAAKwU,EAAkB1V,SAASkB,GAAI,CAElC,IADA,IAAI4U,EAAgB,EACX7d,EAAI,EAAGA,EAAIwd,EAAUzhB,OAAQiE,IAAK,CAEzC,IAAI6I,EAAW,EACPF,IACDrB,EAAasB,oBAChBC,EAAWJ,EAAYc,EAAoBvS,EAAMiS,GAAGqB,aAAckT,EAAUxd,GAAI2I,IAKpFkV,GAAiBhV,C,CAGnB,IAAMiV,EAAcD,EAAgBL,EAAUzhB,OAE1C+hB,EAAcH,IACfH,EAAUvkB,MAAK,SAAA8kB,GAAY,SAAQA,EAAU/P,EAAYrF,EAAcY,EAAqBvS,EAAMiS,GAAGqB,aAA1E,MAC5BqT,EAAcG,EACdF,EAAuB3U,E,GArBpBA,EAAI,EAAGA,EAAIjS,EAAM+E,OAAQkN,I,EAAzBA,GA0BTuU,EAAUjmB,GAAKyW,EAAYrF,EAAcY,EAAqBqU,GAC9DH,EAAkBhmB,KAAKmmB,GACvBpT,EAASjT,GAAK,CAACP,EAAM4mB,IACrB5mB,EAAM4mB,GAAsBvU,UAAYqB,OAAOnT,E,CAMnD,IADA,IAAIymB,EAAa,IACJ,CACX,IAASzmB,EAAI,EAAGA,EAAIP,EAAM+E,OAAQxE,IAAK,CACrC,IAAI0mB,EAAmB,EACnBC,EAAcpe,IAClB,GAAqB,IAAfke,IAAoBP,EAAkB1V,SAASxQ,GAAK,CACxD,IAAK,IAAIyI,EAAI,EAAGA,EAAIwd,EAAUzhB,OAAQiE,IAAK,CAEzC,IAAI6I,EAAW,EACPF,IACDrB,EAAasB,oBAChBC,EAAWJ,EAAYc,EAAoBhS,GAAIimB,EAAUxd,GAAI2I,IAM7DE,EAAWqV,IACbA,EAAcrV,EACdoV,EAAmBje,E,CAKvB,QAA2B9G,IAAvBlC,EAAMO,GAAG8R,UACX,IAAK,IAAI3N,EAAI8O,EAASjI,OAAOvL,EAAMO,GAAG8R,YAAYtN,OAAS,EAAGL,GAAK,EAAIA,IACjE8O,EAASjI,OAAOvL,EAAMO,GAAG8R,YAAY3N,GAAGlE,KAAOR,EAAMO,GAAGC,IAC1DgT,EAASjI,OAAOvL,EAAMO,GAAG8R,YAAYxH,OAAOnG,EAAG,GAKrD1E,EAAMO,GAAG8R,UAAYqB,OAAOuT,GAC5BzT,EAASyT,GAAkBxmB,KAAKT,EAAMO,G,EAK1C,IAAI4mB,GAAoB,EACxB,IAAS5mB,EAAI,EAAGA,EAAIiT,EAASzO,OAAQxE,IAAM,CACzC,IAAMmV,EAAelC,EAASjT,GAC1B6mB,EAAc,IAAI,EAAO,IAC7B,IAASpe,EAAI,EAAGA,EAAI0M,EAAa3Q,OAAQiE,IACvCoe,EAAcA,EAAYxf,IAAI,IAAI,EAAO2K,EAAoBmD,EAAa1M,GAAGsK,eAG/E,IAAM+T,EAAYD,EAAY7X,IAAImG,EAAa3Q,QAE1CsiB,EAAUhX,MAAM,IAAI,EAAOmW,EAAUjmB,OACxC4mB,GAAoB,EAEpBX,EAAUjmB,GAAK8mB,EAAUpY,S,CAK7B,GAFA+X,IAEIhnB,EAAMmR,OAAM,SAAA7Q,GAAQ,YAAmB4B,IAAnB5B,EAAK+R,SAAL,KAAiC8U,GAAqBH,GAAc,IAC1F,K,CAKJ,IAAMtQ,EAAe,GACfC,EAAiB,CAAC,EAmBxB,OAlBA1W,EAAMI,SAAQ,SAAAK,G,QACJC,EAAmBD,EAAI,OAAfE,EAAWF,EAAI,OACzB6T,EAAwD,QAAtC,EAAAvU,EAAMiC,MAAK,SAAA3B,GAAQ,OAAAA,EAAKE,KAAOG,CAAZ,WAAmB,eAAE0R,UAC1DmC,EAAwD,QAAtC,EAAAxU,EAAMiC,MAAK,SAAA3B,GAAQ,OAAAA,EAAKE,KAAOI,CAAZ,WAAmB,eAAEyR,UAC1DuE,EAAY,UAAGrC,EAAe,cAAMC,GAC1C,GAAImC,EAAeC,GACjBD,EAAeC,GAAWC,YACrB,CACL,IAAMC,EAAU,CACdnW,OAAQ4T,EACR3T,OAAQ4T,EACRqC,MAAO,GAETF,EAAeC,GAAaE,EAC5BJ,EAAajW,KAAKqW,E,CAEtB,IAEO,CAAEtD,SAAQ,EAAEkD,aAAY,EACjC,ESrIE4Q,iBAAgB,EAChBC,sBMpE4B,SAC5BvnB,EACAwnB,EACArU,EACAxC,EACAC,QAJA,IAAA5Q,IAAAA,EAAA,SAEA,IAAAmT,IAAAA,OAAA,QACA,IAAAxC,IAAAA,EAAA,SACA,IAAAC,IAAAA,EAAA,IAKA,IAAM6W,EAAe,EAAMznB,EAAMwD,QAAO,SAAAlD,GAAQ,OAAAA,EAAKE,KAAOgnB,EAAShnB,EAArB,KAC1CknB,EAAgB1nB,EAAM0H,WAAU,SAAApH,GAAQ,OAAAA,EAAKE,KAAOgnB,EAAShnB,EAArB,IAExC6S,EAAa9C,EAAiBvQ,EAAOmT,GAErCZ,EAAsB9B,EAAO4C,EAAY1C,EAAcC,GAEvD+W,EAAqBpV,EAAoBmV,GAEzCE,EAAgC,GActC,OAbAH,EAAapnB,SAAQ,SAACC,EAAMqD,GAC1B,GAAIrD,EAAKE,KAAOgnB,EAAShnB,GAAI,CAE3B,IAAMqnB,EAAiBtV,EAAoB5O,GAErCmkB,EAAwB,EAAiBD,EAAgBF,GAC/DC,EAAoBnnB,KAAKqnB,GACzBxnB,EAAKgnB,iBAAmBQ,C,CAE5B,IAGAL,EAAaxX,MAAK,SAACjP,EAAGC,GAAM,OAAAA,EAAEqmB,iBAAmBtmB,EAAEsmB,gBAAvB,IACrB,CAAEM,oBAAmB,EAAEH,aAAY,EAC5C,ENmCEM,oBLuB0B,SAACjoB,EAAsB0O,EAAiBwZ,GAKlE,OAAKA,EAJS,CACZC,KAAMpP,EACNqP,QAAShP,GAIE8O,GAAMloB,EAAW0O,GAFZ0K,EAAWpZ,EAAW0O,EAG1C,EK9BE2Z,SOxEe,SAACroB,EAAsBsoB,EAAkBC,GAGjC,iBAAZD,IAAsBA,EAAU,MACnB,iBAAbC,IAAuBA,EAAW,KAa7C,IAXA,IAMIC,EANAzW,EAAW,EACX0W,EAAa,EACbC,EAAgB,IAEZ,EAA2B1oB,EAAS,MAApCE,OAAK,IAAG,KAAE,EAAE,EAAeF,EAAS,MAAxBG,OAAK,IAAG,KAAE,EACxBwoB,EAAazoB,EAAM+E,OAEnB2jB,EAAW,CAAC,EACZC,EAAY,CAAC,EAGV3f,EAAI,EAAGA,EAAIyf,IAAczf,EAGhC0f,EADMrlB,GADA/C,EAAON,EAAMgJ,IACCxI,IACA,EAAIioB,EACxBE,EAAUtlB,GAAW,EAAIolB,EAI3B,IADA,IAAMG,EAAa,EAAO9oB,GACnB0oB,EAAgB,GAAK3W,EAAWuW,GAAS,CAE9C,IADAG,EAAa,EACJvf,EAAI,EAAGA,EAAIyf,IAAczf,EAAG,CACnC,IACM3F,GADA/C,EAAON,EAAMgJ,IACCxI,GAEpB,GADA8nB,EAAc,EACuB,IAAjCM,EAAWtoB,EAAKE,IAAIiF,SACtBijB,EAASrlB,GAAU,MACd,CAEL,IADA,IAAMoB,EAAYrB,EAAaC,EAAQpD,EAAO,UACrCM,EAAI,EAAGA,EAAIkE,EAAUM,SAAUxE,EAAG,CACzC,IAAM8E,EAAWZ,EAAUlE,GACrBmF,EAAoBkjB,EAAWvjB,GAAUK,UAC3CA,EAAY,IAAG4iB,GAAgBK,EAAUtjB,GAAYK,E,CAE3DgjB,EAASrlB,GAAUglB,EAAWC,EAC9BC,GAAcG,EAASrlB,E,EAM3B,IAFAklB,GAAc,EAAIA,GAAcE,EAChC5W,EAAW,EACF7I,EAAI,EAAGA,EAAIyf,IAAczf,EAAG,CACnC,IAAM1I,EAENgoB,EAAcI,EADRrlB,GADA/C,EAAON,EAAMgJ,IACCxI,IACa+nB,EACjC1W,GAAYhO,KAAKglB,IAAIP,EAAcK,EAAUtlB,IAC7CslB,EAAUtlB,GAAUilB,C,CAEtBE,GAAiB,C,CAGnB,OAAOG,CACT,EPeEvlB,aAAY,EACZ0lB,M,GACAC,MF+WY,SACZjpB,EACAkpB,EACAjpB,EACA6L,EACA7G,EACA2b,EACAuB,G,MAEA,QANA,IAAAliB,IAAAA,GAAA,QAGA,IAAA2gB,IAAAA,EAAA,gBACA,IAAAuB,IAAAA,EAAA,WAEKniB,GAAcA,EAAUE,MAA7B,CASA,IAAM8N,EAAUhO,EAAUE,MAAM+E,OAChC,GAAK+I,EAAL,CAEA,IAAM2S,EAAM,EAAc3gB,EAAWC,GAI/BkpB,EAAa,EAAcD,EAASjpB,GAIpCmpB,EAASxG,GAAU5iB,EAAUE,MAAOygB,EAAK1gB,GAIzCopB,EAAgBzG,GAAUsG,EAAQhpB,MAAOipB,EAAYlpB,GAIrD,EAA4BwiB,GAAYziB,EAAUE,MAAO0gB,GAAvDvgB,EAAO,UAAEyZ,EAAY,eACvB,EAAiE2I,GACrEyG,EAAQhpB,MACR0gB,GAFe+C,EAAc,UAAgBC,EAAmB,eAMlElB,GAAY1iB,EAAUG,MAAOgiB,EAAe9hB,GAEpC,IAAcipB,EAAwB5G,GAC5CwG,EAAQ/oB,MACRgiB,EACAwB,GACD,aAGG4F,EAAmB,GACvBJ,SAAAA,EAAY5oB,SAAQ,SAAAuT,GAClByV,EAAmBA,EAAiB9e,OAAOqJ,EAC7C,IACK7O,IAAQA,EAASlB,KAAKqM,IAAG,MAARrM,KAAI,OAAQwlB,GAAkB,GAAF,CAAE,IAAC,KAChDzd,IAAGA,EAAI7G,GAMZ,IAAMke,EAAiBzC,GAAmB1gB,EAAW2gB,EAAKC,EAAe9U,GACnE0d,EAAwB9I,GAAmBwI,EAASC,EAAYvI,EAAe9U,GAY/E2d,EAhbsB,SAC5B3d,EACAkC,EACA0b,EACAvG,EACAxC,GAGA,IAAIgJ,EAAsB5lB,KAAK6lB,KAAKF,EAAiB1b,GAC/CsT,EAAc,CAAC,EACjBuI,EAAqB,EAwCzB,OArCA1G,EAAe5iB,SAAQ,SAACupB,EAAMrpB,GAM5B,IAJA,IAAIspB,EAAoB,EACpBC,EAAiB,EACfrlB,EAAYmlB,EAAK5I,SACjBC,EAAc2I,EAAK3I,YAAc,EAChC4I,EAAoBJ,GAAqB,CAK9C,IAHA,IAAIM,EAAOtlB,EAAU,EAAIZ,KAAKqU,MAAMrU,KAAKC,SAAWmd,IAChD+I,EAAiB,GAEd5I,EAAY,UAAG7gB,EAAC,YAAIwpB,KAAW3I,EAAY,UAAG2I,EAAI,YAAIxpB,OAC3DwpB,EAAOlmB,KAAKqU,MAAMrU,KAAKC,SAAWgK,OAClCkc,EACqB,EAAIlc,MAE3B,GAAIkc,EAAiB,EAAIlc,IAEvBsT,EAAY,UAAG7gB,EAAC,YAAIwpB,IAAU,CAC5B3hB,MAAO7H,EACPwe,IAAKgL,EACLlY,SAAU4O,EAAIlgB,GAAGwpB,IAEnBF,MACAF,GAE0BH,GAAgB,OAAOpI,EAGnD,KADA0I,EACqB,EAAIhc,EAAS,K,CAGhC+b,EAAoBJ,IAEtBA,GAAuBA,GADXA,EAAsBI,KACmB/b,EAAUvN,EAAI,GAEvE,IACO6gB,CACT,CA6XuB6I,CACnBre,EACAkC,EAHqBjK,KAAKgB,IAAI,IAAMiJ,GAAWA,EAAU,GAAM,GAK/DmV,EACAxC,GAOEyJ,EAAU/I,GAAiCoI,EAActG,EAAgBnjB,GAqBvEqqB,EDiWM,SAACC,GAGX,IAAA7N,EAIE6N,EAAM,OAHR,EAGEA,EAAM,SAHRrqB,OAAQ,IAAG,GAAK,EAChB,EAEEqqB,EAAM,cAFR1J,OAAa,IAAG,EAAAH,GAAkB,EAClC,EACE6J,EAAM,cADRnI,OAAa,IAAG,EAAA1B,GAAkB,EAE9B8J,EA7Ea,SACnB9N,EACAxc,EACA2gB,EACAuB,GAEA,IAAM9E,EAAmC,CAAC,EAgB1C,OAfA9d,OAAOwK,KAAK0S,GAAQlc,SAAQ,SAAClB,EAAKoB,GAChC,IAAM+a,EAAQiB,EAAOpd,GACfmrB,EAAS,IAAI/O,EAAMhb,GAAG,EAAMR,GAC5BwqB,EAAa,CAAC,EACpBjP,EAAMtb,MAAMK,SAAQ,SAACC,EAAM0I,GACzBshB,EAAOtQ,QAAQhR,EAAG1I,EAAKogB,IACvB6J,EAAWjqB,EAAKE,IAAMwI,CACxB,IACAsS,EAAMrb,MAAMI,SAAQ,SAACK,EAAMkL,GACzB,IAAM4e,EAAYD,EAAW7pB,EAAKC,QAC5B8pB,EAAYF,EAAW7pB,EAAKE,QAClC0pB,EAAO5Q,SAAS,EAAG8Q,EAAWC,EAAW/pB,EAAKuhB,GAChD,IACIqI,GAAUA,EAAOvQ,eAAcoD,EAAOmN,EAAO9pB,IAAM8pB,EACzD,IACOnN,CACT,CAsD0BuN,CACtBnO,EACAxc,EACA2gB,EACAuB,GAEMzF,EAAqD4N,EAAM,WAA/C1N,EAAyC0N,EAAM,WAAnC3N,EAA6B2N,EAAM,WAAvBzN,EAAiByN,EAAM,QAAdjS,EAAQiS,EAAM,IAY7DO,EAAa,IAAIC,GATJ,CACjBrO,OAAQ8N,EACR7N,WAAU,EACVE,WAAU,EACVD,WAAU,EACVtE,IAAG,EACHwE,QAAO,EACP5c,SAAQ,IAGV4qB,EAAWrK,MAEX,IAAMnD,EAzEa,SACnBZ,EACAmE,EACAuB,GAEA,IAAM9E,EAAS,GAkBf,OAjBAZ,EAAOlc,SAAQ,SAACib,GACd,IAAMxb,EAAY,CAAEE,MAAO,GAAIC,MAAO,IACtCqb,EAAMtb,MAAMK,SAAQ,SAACC,G,MACnBR,EAAUE,MAAMS,OAAI,GAClBD,GAAI,UAAGF,EAAKE,MACXkgB,GAAgBpgB,EAAKkZ,M,GAE1B,IACA8B,EAAMrb,MAAMI,SAAQ,SAACK,G,MACnBZ,EAAUG,MAAMQ,OAAI,GAClBE,OAAQ,UAAGD,EAAKsJ,MAChBpJ,OAAQ,UAAGF,EAAKqJ,MACfkY,GAAgBvhB,EAAK8Y,M,GAE1B,IACA2D,EAAO1c,KAAKX,EACd,IACOqd,CACT,CAiDiB0N,CACbF,EAAW3N,kBACX0D,EACAuB,GAEF,OAAO9E,CACT,CCpYwB,CAfP,CACbZ,OAAQ2N,EACRxJ,cAAa,EACbuB,cAAa,EACbzF,WAPa,EAQbC,WAPa,EAQbC,WAPa,EAQb3c,SAAQ,IAQ0BuK,MAAM,EAnB9B,IAqBNwgB,EAAeX,EAAcplB,OAG7BgmB,EAAkB,GACxBZ,EAAc9pB,SAAQ,SAAC2hB,EAAWzhB,GAChCwqB,EAAgBxqB,GAAK,CAAC,EACtBlB,OAAOwK,KAAKqgB,GAAS7pB,SAAQ,SAAAlB,GAC3B,IAAMmc,EAAQ4O,EAAQ/qB,GAChB6rB,EAAoBjJ,GAAgBzG,EAAO0G,EAAWtB,EAAeuB,GAC3E8I,EAAgBxqB,GAAGpB,GAAO6rB,CAC5B,GACF,IAQM,MAjWuB,SAACD,EAAiBD,EAAcG,GAG7D,IAFA,IAAIC,EAAYpiB,IACdqiB,EAAuB,E,WAChB5qB,GAEP,IAAM6qB,EAAYL,EAAgBxqB,GAE5B8qB,EAAkBhsB,OAAOwK,KAAKuhB,GAAWnb,MAAK,SAACjP,EAAGC,GACtD,OAAOmqB,EAAUpqB,GAAKoqB,EAAUnqB,EAClC,IAIMuS,EAAW,GACjB6X,EAAgBhrB,SAAQ,SAAClB,EAAK6J,GACvBwK,EAASxK,EAHC,MAIbwK,EAASxK,EAJI,IAIY,CAAEuT,OAAQ,GAAI+O,WAAY,EAAGC,SAAU,IAClE/X,EAASxK,EALM,IAKQuT,OAAO9b,KAAKtB,GACnCqU,EAASxK,EANM,IAMQsiB,YAAcF,EAAUjsB,EACjD,IAGA,IAAIqsB,EAAe,EACbC,EAAY,GAClBjY,EAASnT,SAAQ,SAAAqrB,GAEf,IAAMH,EAAWG,EAAgBJ,WAAaI,EAAgBnP,OAAOxX,OACrE2mB,EAAgBH,SAAWA,EAC3BE,EAAUhrB,KAAK8qB,GAGf,IAAII,EAAqB,EACnBC,EAAYF,EAAgB3mB,OAClC2mB,EAAgBnP,OAAOlc,SAAQ,SAACwrB,EAAW7iB,GACzC,IAAM8iB,EAAcV,EAAUS,GAC9BH,EAAgBnP,OAAOlc,SAAQ,SAAC0rB,EAAWngB,GACrC5C,IAAM4C,IACV+f,GAAsB9nB,KAAKglB,IAAIiD,EAAcV,EAAUW,IACzD,GACF,IAEAP,GADAG,GAAuBC,GAAaA,EAAY,GAAM,CAExD,IAEAJ,GAAgBhY,EAASzO,OAGzB,IAAIinB,EAAe,EACnBP,EAAUprB,SAAQ,SAAC4rB,EAAWjjB,GAC5ByiB,EAAUprB,SAAQ,SAAC6rB,EAAWtgB,GACxB5C,IAAM4C,IACVogB,GAAgBnoB,KAAKglB,IAAIoD,EAAYC,GACvC,IACAF,GAAiBP,EAAU1mB,QAAU0mB,EAAU1mB,OAAS,GAAM,CAChE,IAGA,IAAMonB,EAASH,EAAeR,EAC1BN,EAAYiB,IACdjB,EAAYiB,EACZhB,EAAuB5qB,E,EAzDlBA,EAAI,EAAGA,EAAIuqB,EAAcvqB,I,EAAzBA,GA4DT,MAAO,CACLyhB,UAAWiJ,EAAWE,GACtBiB,kBAAmBrB,EAAgBI,GAEvC,CA8RyDkB,CACrDtB,EACAD,EACAX,GAHiBmC,EAAG,YAAqBC,EAAO,oBAQ9CC,EAAaxD,EAAQhpB,MAAM,GAC7BysB,EAAa,GACbjT,EAAwB,QAAhB,EAAAwP,EAAQhpB,MAAM,UAAE,eAAG0gB,GAC3BgM,GAA0B,IAC5B1D,EAAQhpB,MAAMK,SAAQ,SAAAC,GACpB,IAAMqsB,EAASrsB,EAAKogB,GACdkM,EAAqBhT,EAAa+S,IACpCC,aAAkB,EAAlBA,EAAoB7nB,QAAS2nB,IAC/BA,EAA0BE,EAAmB7nB,OAC7C0nB,EAAaG,EACbpT,EAAQmT,EACRH,EAAalsB,EAEjB,IAKA,IAAMijB,EAA+B,CAAC,EAClCsJ,EAAqB,CAAC,EACxBC,EAAiB,CAAC,EAClBC,EAAoB,CAAC,EAEjBC,EAAgB,CAAC,EACjBC,EAAoB,CAAC,EAC3B5tB,OAAOwK,KAAK6Z,GAAqBrjB,SAAQ,SAAC6sB,EAAQlkB,GAChDgkB,EAAcE,GAAU,GACpBntB,IACFktB,EAAkBC,GAAU,IAE9B,IAAIC,GAAU,IACRC,EAAyB1J,EAAoBwJ,GAC7CG,EAAqB,CAAC,EAC5BD,EAAuB/sB,SAAQ,SAAAitB,GAC7B,IAAMte,EAAOma,EAAc,UAAGqD,EAAWhsB,GAAE,YAAI8sB,EAAe9sB,KAQ9D,GAPAwO,GAAQge,EAAcE,GAAQzsB,KAAKuO,GAC/Bme,EAAUne,IAAMme,EAAUne,GAC9Bqe,EAAmB,UAAGb,EAAWhsB,GAAE,YAAI8sB,EAAe9sB,KAAQ,CAC5D4H,MAAO,EACP2W,IAAK0E,EAAe6J,EAAe9sB,IAAImT,IACvC9B,SAAU7C,GAERjP,EAAU,CACZ,IAAMwtB,EAAWpE,EAAc,UAAGmE,EAAe9sB,GAAE,YAAIgsB,EAAWhsB,KAClE+sB,GAAYN,EAAkBC,GAAQzsB,KAAK8sB,E,CAE/C,IAGAP,EAAcE,GAAUF,EAAcE,GAAQjd,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,IACzDlB,IAAUktB,EAAkBC,GAAUD,EAAkBC,GAAQjd,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,KAKnF4rB,EAAqB1L,GACnBkM,EACA/D,EACAN,EACA6D,GAGF,IAAIW,EAA6B,GAejC,GAdAnuB,OAAOwK,KAAKwjB,GAAoBhtB,SAAQ,SAAAlB,GACtC,GAAI2tB,EAAe3tB,GACjBquB,EAA2B/sB,KAAKqsB,EAAe3tB,QADjD,CAIA,IAAMsuB,EAAkBZ,EAAmB1tB,GAC3C2tB,EAAe3tB,GAAO4iB,GAAgB0L,EAAiBnB,EAAK5L,EAAeuB,GAC3EuL,EAA2B/sB,KAAKqsB,EAAe3tB,G,CACjD,IAGAquB,EAA6BA,EAA2Bvd,MAAK,SAACjP,EAAGC,GAAM,OAAAA,EAAID,CAAJ,IACvE+rB,EAAkB,UAAGP,EAAWhsB,GAAE,YAAI0sB,IAAYM,EAE9CN,IAAW1T,EAGf,IADA,I,WACSvH,GACP,IAAM2D,EAAQ6W,EAAWxa,GAGnByb,EAAoBzK,EAAe9iB,EAAQyV,EAAMpV,IAAImT,KACrDga,EAA4BD,EAAkBxM,kBAAkBgM,GAChEU,EAAmBlK,EAAoBwJ,GAAQnoB,OACrD,IAAK4oB,GAA6BA,EAA0B9W,MAAQ+W,E,OAClEnB,EAAW5hB,OAAOoH,EAAG,G,WAOvB,IADA,IAAI4b,GAAgB,EACXnpB,EAAI,EAAGA,EAAIkpB,EAAkBlpB,IACpC,GAAIipB,EAA0B5M,MAAMrc,GAAKsoB,EAAcE,GAAQxoB,GAAI,CACjEmpB,GAAgB,EAChB,K,CAGJ,GAAIA,E,OACFpB,EAAW5hB,OAAOoH,EAAG,G,WASvB,IAAM6b,EAAe,CAAC,EACtBJ,EAAkBjpB,UAAUpE,SAAQ,SAAA0tB,GAClC,IAAM/e,EAAOka,EAAO,UAAGtT,EAAMpV,GAAE,YAAIutB,EAAavtB,KAChDstB,EAAa,UAAGlY,EAAMpV,GAAE,YAAIutB,EAAavtB,KAAQ,CAC/C4H,MAAOjI,EAAQyV,EAAMpV,IAAImT,IACzBoL,IAAK5e,EAAQ4tB,EAAavtB,IAAImT,IAC9B9B,SAAU7C,EAEd,IAEAkb,EAAU/I,GAAiC2M,EAAc7K,EAAgBnjB,EAAWoqB,GAEpF,IAAI8D,EAAsB,GAC1B3uB,OAAOwK,KAAKikB,GAAcztB,SAAQ,SAAAlB,GAChC,GAAIotB,EAAQptB,GACV6uB,EAAoBvtB,KAAK8rB,EAAQptB,QADnC,CAIA,IAAM8uB,EAAW/D,EAAQ/qB,GACzBotB,EAAQptB,GAAO4iB,GAAgBkM,EAAU3B,EAAK5L,EAAeuB,GAC7D+L,EAAoBvtB,KAAK8rB,EAAQptB,G,CACnC,IAGA6uB,EAAsBA,EAAoB/d,MAAK,SAACjP,EAAGC,GAAM,OAAAA,EAAID,CAAJ,IAEzD,IAAIktB,GAAgB,EACpB,IAASxpB,EAAI,EAAGA,EAAIkpB,EAAkBlpB,IACpC,GAAIspB,EAAoBtpB,GAAK8oB,EAA2B9oB,GAAI,CAC1DwpB,GAAgB,EAChB,K,CAGJ,OAAIA,GACFzB,EAAW5hB,OAAOoH,EAAG,G,iBADvB,C,EAhEOA,IADawa,aAAU,EAAVA,EAAY1nB,SAAU,GACf,EAAGkN,GAAK,EAAGA,I,EAA/BA,EAqEX,IAEA,IAAMkc,EAAkB,GAQxB1B,SAAAA,EAAYpsB,SAAQ,SAAA+tB,GAelB,IAdA,IAAMllB,EAAU/I,EAAQiuB,EAAU5tB,IAAImT,IAShC0a,EARqBzN,GACzB9gB,EAAUE,MACVygB,EAAIvX,GACJA,EACAwX,EACA3b,GAGuCN,UAIrC6pB,GAAY,EACP/tB,EAFW8tB,EAActpB,OAEP,EAAGxE,GAAK,EAAGA,IAAK,CAEzC,GAAI8tB,EAActpB,OAAS,EAAIikB,EAAQhpB,MAAM+E,OAE3C,YADAupB,GAAY,GAGd,IAAMP,EAAeM,EAAc9tB,GAC7BijB,EAAgBuK,EAAarN,GAEnC,GAAKgD,EAAoBF,IAAmBE,EAAoBF,GAAeze,OAO/E,GAAKioB,EAAcxJ,IAAmBwJ,EAAcxJ,GAAeze,OAAnE,CAKA,IAAM5F,EAAM,UAAGivB,EAAU5tB,GAAE,YAAIutB,EAAavtB,IAGtC+tB,EAAkBrF,EAAO/pB,GAC3BwU,EAAMqZ,EAAcxJ,GAAeze,OAAS,EAEhD,GAAIwpB,EAD4BvB,EAAcxJ,GAAe7P,GAE3D0a,EAAcxjB,OAAOtK,EAAG,OAD1B,CAKF,GAAIR,EAAU,CACZ,IAAMyuB,EAAU,UAAGT,EAAavtB,GAAE,YAAI4tB,EAAU5tB,IAC1CiuB,EAAoBvF,EAAOsF,GAGjC,GAFA7a,EAAMsZ,EAAkBzJ,GAAeze,OAAS,EAE5C0pB,EADgCxB,EAAkBzJ,GAAe7P,GACd,CACrD0a,EAAcxjB,OAAOtK,EAAG,GACxB,Q,EAKF,IAAMmuB,EAAiBnC,EAAQptB,GAC3BotB,EAAQptB,GACR0jB,GACE/iB,EACAsuB,EACAL,EACA5tB,EACAouB,EACAtL,EACAqJ,EACA5L,EACAuB,EACAsK,EACArC,GAEAyE,EAAa,UAAGnC,EAAWhsB,GAAE,YAAIgjB,GAGvC,GAAIkL,EADF3B,EAAkB4B,GAAY5B,EAAkB4B,GAAY5pB,OAAS,GAErEspB,EAAcxjB,OAAOtK,EAAG,OAD1B,CAMM,MAIF+iB,GAA+BC,EAA8BC,EAAeC,EAAeC,GAH7FC,EAAyB,4BACE,8BACC,+BAG1BxjB,EAAQ4tB,EAAavtB,IAAI+E,OAASoe,GACpC0K,EAAcxjB,OAAOtK,EAAG,E,QA1DxB8tB,EAAcxjB,OAAOtK,EAAG,QAPxB8tB,EAAcxjB,OAAOtK,EAAG,E,CAuEvB+tB,GACHH,EAAgB1tB,KAAK,CACnBT,MAAO,CAACouB,GAAW7jB,OAAO8jB,IAGhC,IAMQ,IAAQO,EAAkC,EAAS5F,EAASwD,EAAWhsB,IAAI,GAAM,OAErFquB,EAAwC,CAAC,EACzC9uB,GACFV,OAAOwK,KAAK+kB,GAA+BvuB,SAAQ,SAAAgD,GACjD,IAAM2c,EAAYyD,EAAepgB,GAAQ/C,KAAKogB,GACzCmO,EAAsC7O,GAGzC6O,EAAsC7O,GAAWvf,KAC/CmuB,EAA8BvrB,IAHhCwrB,EAAsC7O,GAAa,CAAC4O,EAA8BvrB,GAKtF,IACAhE,OAAOwK,KAAKglB,GAAuCxuB,SAAQ,SAAAssB,GACzDkC,EAAsClC,GAAQ1c,MAAK,SAACjP,EAAGC,GAAM,OAAAD,EAAIC,CAAJ,GAC/D,KAEA4tB,EAAwC7B,EAK1C,IADA,I,WACSzsB,GACP,IAAMuuB,EAAiBX,EAAgB5tB,GACjC6tB,EAAYU,EAAe9uB,MAAM,GAEjC+uB,EAA6B,CAAC,EAC9BC,EAAmB,CAAC,EAC1BF,EAAe9uB,MAAMK,SAAQ,SAACC,EAAM2uB,GAClCD,EAAiB1uB,EAAKE,IAAM,CAC1BmT,IAAKsb,EACL3uB,KAAI,EACJiF,OAAQ,EACRE,SAAU,EACVC,UAAW,GAEb,IAAMwpB,EAAa5uB,EAAKogB,GACnBqO,EAA2BG,GAC3BH,EAA2BG,KADaH,EAA2BG,GAAc,CAExF,IAIA,IAAMC,EAAiB,GACjBC,EAAoB,CAAC,EAC3BtvB,EAAUG,MAAMI,SAAQ,SAAAK,GAClBsuB,EAAiBtuB,EAAKC,SAAWquB,EAAiBtuB,EAAKE,UACzDuuB,EAAe1uB,KAAKC,GACf0uB,EAAkB1uB,EAAKuhB,IACvBmN,EAAkB1uB,EAAKuhB,MADiBmN,EAAkB1uB,EAAKuhB,IAAkB,EAEtF+M,EAAiBtuB,EAAKC,QAAQ4E,SAC9BypB,EAAiBtuB,EAAKE,QAAQ2E,SAC9BypB,EAAiBtuB,EAAKC,QAAQ+E,YAC9BspB,EAAiBtuB,EAAKE,QAAQ6E,WAElC,IAKA,IAFA,IAAM4pB,EAAsBhwB,OAAOwK,KAAKuf,GAAqBrkB,OACzDuqB,GAAoB,EACfnT,EAAI,EAAGA,EAAIkT,EAAqBlT,IAAK,CAC5C,IAAM,EAAQ9c,OAAOwK,KAAKuf,GAAqBjN,GAC/C,IACGiT,EAAkB,IACnBA,EAAkB,GAAShG,EAAoB,GAAOrkB,OACtD,CACAuqB,GAAoB,EACpB,K,EAGJ,GAAIA,E,OACFnB,EAAgBtjB,OAAOtK,EAAG,G,WAK5B,IAAIgvB,EAAmBJ,EAAepqB,OAGtC,GAAIwqB,EAAmBvG,EAAQ/oB,MAAM8E,O,OACnCopB,EAAgBtjB,OAAOtK,EAAG,G,QAG5B,IAAIivB,GAAwB,E,WACnBrT,GACP,IAAMzb,EAAOyuB,EAAehT,GACtB5B,EAAY7Z,EAAKuhB,GACjBwN,EAAwBrG,EAAoB7O,GAGlD,IAAKkV,IAA0BA,EAAsB1qB,OAGnD,OAFAqqB,EAAkB7U,KAEdkV,GAAyBL,EAAkB7U,GAAakV,EAAsB1qB,QAChFyqB,GAAwB,E,UAG1BL,EAAetkB,OAAOsR,EAAG,GACzB6S,EAAiBtuB,EAAKC,QAAQ4E,SAC9BypB,EAAiBtuB,EAAKE,QAAQ2E,SAC9BypB,EAAiBtuB,EAAKC,QAAQ+E,YAC9BspB,EAAiBtuB,EAAKE,QAAQ6E,W,YAKhC,IAAMyc,EAAc8M,EAAiBtuB,EAAKC,QAAQL,KAAKogB,GACjDyB,EAAc6M,EAAiBtuB,EAAKE,QAAQN,KAAKogB,GAEnDgP,GAAc,EAgBlB,OAfAD,EAAsBpvB,SAAQ,SAAAsvB,GAC5B,IAAMC,EAAgBnM,EAAekM,EAAYhvB,QAAQL,KACnDuvB,EAAgBpM,EAAekM,EAAY/uB,QAAQN,KAEvDsvB,EAAclP,KAAmBwB,GACjC2N,EAAcnP,KAAmByB,IAEjCuN,GAAc,GAEb3vB,GACD6vB,EAAclP,KAAmByB,GACjC0N,EAAcnP,KAAmBwB,IAEjCwN,GAAc,EAClB,IACKA,OAAL,GACEN,EAAkB7U,KAEdkV,GAAyBL,EAAkB7U,GAAakV,EAAsB1qB,QAChFyqB,GAAwB,E,UAG1BL,EAAetkB,OAAOsR,EAAG,GACzB6S,EAAiBtuB,EAAKC,QAAQ4E,SAC9BypB,EAAiBtuB,EAAKE,QAAQ2E,SAC9BypB,EAAiBtuB,EAAKC,QAAQ+E,YAC9BspB,EAAiBtuB,EAAKE,QAAQ6E,W,cApDlC,IAAS0W,EAAIoT,EAAmB,EAAGpT,GAAK,G,YAA/BA,GAAkCA,KA0D3C,GAAIqT,E,OACFrB,EAAgBtjB,OAAOtK,EAAG,G,WAI5BuuB,EAAe7uB,MAAQkvB,EAEf,IAAQW,EAAuB,EACrChB,EACAA,EAAe9uB,MAAM,GAAGQ,IACxB,GACD,OA8CD,GA7CAnB,OAAOwK,KAAKimB,GACTptB,UACArC,SAAQ,SAAA0vB,GACP,GAAIA,IAAajB,EAAe9uB,MAAM,GAAGQ,KAAMgvB,EAA/C,CAEA,GAAIM,EAAmBC,KAAcjnB,IAAU,CAC7C,IAAMknB,EAAkBhB,EAAiBe,GAAUzvB,KAAKogB,GAExD,GADAqO,EAA2BiB,KAEzBjB,EAA2BiB,GAC3BtM,EAAoBsM,GAAiBjrB,OAGrC,YADAyqB,GAAwB,GAG1B,IAAM7b,EAAMmb,EAAe9uB,MAAM2E,QAAQqqB,EAAiBe,GAAUzvB,MAGpE,OAFAwuB,EAAe9uB,MAAM6K,OAAO8I,EAAK,QACjCqb,EAAiBe,QAAY7tB,E,CAI/B,IAAM+tB,EAAS9vB,EAAQ4vB,GAAUzvB,KAAKogB,GACtC,IACGmO,EAAsCoB,KACtCpB,EAAsCoB,GAAQlrB,QAC/C+qB,EAAmBC,GACjBlB,EAAsCoB,GACpCpB,EAAsCoB,GAAQlrB,OAAS,GAE3D,CAGA,GAFMirB,EAAkBhB,EAAiBe,GAAUzvB,KAAKogB,GACxDqO,EAA2BiB,KAEzBjB,EAA2BiB,GAC3BtM,EAAoBsM,GAAiBjrB,OAGrC,YADAyqB,GAAwB,GAGpB7b,EAAMmb,EAAe9uB,MAAM2E,QAAQqqB,EAAiBe,GAAUzvB,MACpEwuB,EAAe9uB,MAAM6K,OAAO8I,EAAK,GACjCqb,EAAiBe,QAAY7tB,C,CAtC6C,CAwC9E,IAEEstB,E,OACFrB,EAAgBtjB,OAAOtK,EAAG,G,WAM5B,IAFA,IAAI2vB,GAAgB,EAChBC,EAAY,EACTD,IAAkBV,GAAuB,CAQ9C,GAPAU,GAAgB,EAGEnwB,EAAYivB,EAAiBZ,EAAU5tB,IAAI+E,OAASke,EAAe+I,EAAWhsB,IAAI+E,QAClGypB,EAAiBZ,EAAU5tB,IAAIiF,SAAWge,EAAe+I,EAAWhsB,IAAIiF,UACxEupB,EAAiBZ,EAAU5tB,IAAIkF,UAAY+d,EAAe+I,EAAWhsB,IAAIkF,UACzEspB,EAAiBZ,EAAU5tB,IAAI+E,OAASke,EAAe+I,EAAWhsB,IAAI+E,OACzD,CACbiqB,GAAwB,EACxB,K,CAGF,GACET,EAA2BX,EAAU1N,IACrCgD,EAAoB0K,EAAU1N,IAAgB3b,OAC9C,CACAyqB,GAAwB,EACxB,K,CAKF,IADA,IACSpwB,EADuB0vB,EAAe9uB,MAAM+E,OACd,EAAG3F,GAAK,EAAGA,IAAK,CACrD,IAAMgxB,EAAStB,EAAe9uB,MAAMZ,GAC9BwpB,EAAaoG,EAAiBoB,EAAO5vB,IAAI+E,OACzC8qB,EAAerB,EAAiBoB,EAAO5vB,IAAIiF,SAC3C6qB,EAAgBtB,EAAiBoB,EAAO5vB,IAAIkF,UAC5CwpB,EAAakB,EAAO1P,GAEpB,EAIF4C,GAA+BC,EAA8B2L,EAAYzL,EAAeC,GAH1FC,EAAyB,4BACzBC,EAA2B,8BAC3BC,EAA4B,+BAO9B,GAJwB9jB,EAAY6oB,EAAajF,GAC/C0M,EAAezM,GACf0M,EAAgBzM,EAChB+E,EAAajF,EACM,CAGnB,GAFAoL,EAA2BqB,EAAO1P,MAGhCqO,EAA2BqB,EAAO1P,IAClCgD,EAAoB0M,EAAO1P,IAAgB3b,OAC3C,CACAyqB,GAAwB,EACxB,K,CAEFV,EAAe9uB,MAAM6K,OAAOzL,EAAG,GAC/B4vB,EAAiBoB,EAAO5vB,SAAM0B,EAC9BguB,GAAgB,C,EAGpB,GAAIV,IAA2BU,GAA+B,IAAdC,EAAkB,MAGlE,IAAK,IAAII,GADThB,EAAmBJ,EAAepqB,QACF,EAAGwrB,GAAK,EAAGA,IAAK,CAC9C,IAAMC,EAAQrB,EAAeoB,GAC7B,IAAKvB,EAAiBwB,EAAM7vB,UAAYquB,EAAiBwB,EAAM5vB,QAAS,CACtEuuB,EAAetkB,OAAO0lB,EAAG,GACzB,IAAMhW,EAAYiW,EAAMvO,GAWxB,GAVAmN,EAAkB7U,KACdyU,EAAiBwB,EAAM7vB,UACzBquB,EAAiBwB,EAAM7vB,QAAQ4E,SAC/BypB,EAAiBwB,EAAM7vB,QAAQ+E,aAE7BspB,EAAiBwB,EAAM5vB,UACzBouB,EAAiBwB,EAAM5vB,QAAQ2E,SAC/BypB,EAAiBwB,EAAM5vB,QAAQ6E,YAI/B2jB,EAAoB7O,IACpB6U,EAAkB7U,GAAa6O,EAAoB7O,GAAWxV,OAC9D,CACAyqB,GAAwB,EACxB,K,CAEFU,GAAgB,C,EAGpBC,G,CAGF,OAAIX,GAOFA,GACAV,EAAe9uB,MAAM+E,OAASikB,EAAQhpB,MAAM+E,QAC5CoqB,EAAepqB,OAASikB,EAAQ/oB,MAAM8E,QARtCopB,EAAgBtjB,OAAOtK,EAAG,G,iBAK5B,C,EApROA,EADe4tB,EAAgBppB,OACP,EAAGxE,GAAK,G,YAAhCA,GAAmCA,KAqS5C,IAAIkwB,EAAgBtC,EAAgBppB,O,WAC3BxE,GACP,IAAMmwB,EAAMvC,EAAgB5tB,GACtBowB,EAAa,CAAC,EACpBD,EAAIzwB,MAAMI,SAAQ,SAAAK,GAChB,IAAMvB,EAAM,UAAGuB,EAAKC,OAAM,YAAID,EAAKE,OAAM,YAAIF,EAAK8Y,OAC7CmX,EAAWxxB,GACXwxB,EAAWxxB,KADMwxB,EAAWxxB,GAAO,CAE1C,IAEA,I,eAAS6J,GACP,IAAM4nB,EAAMzC,EAAgBnlB,GACtB6nB,EAAa,CAAC,EACpBD,EAAI3wB,MAAMI,SAAQ,SAAAK,GAChB,IAAMvB,EAAM,UAAGuB,EAAKC,OAAM,YAAID,EAAKE,OAAM,YAAIF,EAAK8Y,OAC7CqX,EAAW1xB,GACX0xB,EAAW1xB,KADM0xB,EAAW1xB,GAAO,CAE1C,IAEA,IAAI2xB,GAAO,EACPzxB,OAAOwK,KAAKgnB,GAAY9rB,SAAW1F,OAAOwK,KAAK8mB,GAAY5rB,OAC7D+rB,GAAO,EAEPzxB,OAAOwK,KAAK8mB,GAAYtwB,SAAQ,SAAAlB,GAC1B0xB,EAAW1xB,KAASwxB,EAAWxxB,KAAM2xB,GAAO,EAClD,IAEEA,GACF3C,EAAgBtjB,OAAO7B,EAAG,E,EAlBrBA,EAAIynB,EAAgB,EAAGznB,EAAIzI,EAAGyI,I,EAA9BA,GAqBTynB,EAAgBtC,EAAgBppB,M,EA9BlC,IAASxE,EAAI,EAAGA,GAAKkwB,EAAgB,EAAGlwB,I,EAA/BA,GAiCT,OAAO4tB,CA/tBa,CAVsB,CA0uB5C,G", "sources": ["webpack://Algorithm/webpack/universalModuleDefinition", "webpack://Algorithm/webpack/bootstrap", "webpack://Algorithm/webpack/runtime/define property getters", "webpack://Algorithm/webpack/runtime/hasOwnProperty shorthand", "webpack://Algorithm/./src/adjacent-matrix.ts", "webpack://Algorithm/./src/structs/linked-list.ts", "webpack://Algorithm/./src/structs/queue.ts", "webpack://Algorithm/./src/util.ts", "webpack://Algorithm/./src/connected-component.ts", "webpack://Algorithm/./src/degree.ts", "webpack://Algorithm/./src/dfs.ts", "webpack://Algorithm/./src/detect-cycle.ts", "webpack://Algorithm/./node_modules/_tslib@2.6.1@tslib/tslib.es6.mjs", "webpack://Algorithm/../src/is-type.ts", "webpack://Algorithm/../src/is-function.ts", "webpack://Algorithm/../src/is-array.ts", "webpack://Algorithm/../src/keys.ts", "webpack://Algorithm/../src/pull.ts", "webpack://Algorithm/../src/pull-at.ts", "webpack://Algorithm/../src/remove.ts", "webpack://Algorithm/../src/is-string.ts", "webpack://Algorithm/../src/uniq.ts", "webpack://Algorithm/../src/group-by.ts", "webpack://Algorithm/../src/is-integer.ts", "webpack://Algorithm/../src/to-degree.ts", "webpack://Algorithm/../src/to-radian.ts", "webpack://Algorithm/../src/values.ts", "webpack://Algorithm/../src/is-prototype.ts", "webpack://Algorithm/../src/clone.ts", "webpack://Algorithm/../src/is-empty.ts", "webpack://Algorithm/../src/is-object-like.ts", "webpack://Algorithm/../src/is-array-like.ts", "webpack://Algorithm/../src/is-equal.ts", "webpack://Algorithm/../src/measure-text-width.ts", "webpack://Algorithm/../src/pick.ts", "webpack://Algorithm/../src/memoize.ts", "webpack://Algorithm/../src/cache.ts", "webpack://Algorithm/./src/dijkstra.ts", "webpack://Algorithm/./src/floydWarshall.ts", "webpack://Algorithm/./src/utils/vector.ts", "webpack://Algorithm/./src/constants/time.ts", "webpack://Algorithm/./src/types.ts", "webpack://Algorithm/./src/utils/node-properties.ts", "webpack://Algorithm/./src/utils/data-preprocessing.ts", "webpack://Algorithm/./src/louvain.ts", "webpack://Algorithm/./src/k-means.ts", "webpack://Algorithm/./src/cosine-similarity.ts", "webpack://Algorithm/./src/structs/union-find.ts", "webpack://Algorithm/./src/structs/binary-heap.ts", "webpack://Algorithm/./src/mts.ts", "webpack://Algorithm/./src/gSpan/struct.ts", "webpack://Algorithm/./src/gSpan/gSpan.ts", "webpack://Algorithm/./src/gaddi.ts", "webpack://Algorithm/./src/structs/stack.ts", "webpack://Algorithm/./src/index.ts", "webpack://Algorithm/./src/bfs.ts", "webpack://Algorithm/./src/find-path.ts", "webpack://Algorithm/./src/label-propagation.ts", "webpack://Algorithm/./src/i-louvain.ts", "webpack://Algorithm/./src/k-core.ts", "webpack://Algorithm/./src/nodes-cosine-similarity.ts", "webpack://Algorithm/./src/pageRank.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Algorithm\"] = factory();\n\telse\n\t\troot[\"Algorithm\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "import { GraphData, Matrix } from \"./types\";\n\nconst adjMatrix = (graphData: GraphData, directed?: boolean): Matrix[] => {\n  const { nodes, edges } = graphData;\n  const matrix: Matrix[] = [];\n  // map node with index in data.nodes\n  const nodeMap: {\n    [key: string]: number;\n  } = {};\n\n  if (!nodes) {\n    throw new Error(\"invalid nodes data!\");\n  }\n\n  if (nodes) {\n    nodes.forEach((node, i) => {\n      nodeMap[node.id] = i;\n      const row: number[] = [];\n      matrix.push(row);\n    });\n  }\n\n  if (edges) {\n    edges.forEach((edge) => {\n      const { source, target } = edge;\n      const sIndex = nodeMap[source as string];\n      const tIndex = nodeMap[target as string];\n      if ((!sIndex && sIndex !== 0) || (!tIndex && tIndex !== 0)) return;\n      matrix[sIndex][tIndex] = 1;\n      if (!directed) {\n        matrix[tIndex][sIndex] = 1;\n      }\n    });\n  }\n  return matrix;\n};\n\nexport default adjMatrix;\n", "const defaultComparator = (a, b) => {\n  if (a === b) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * 链表中单个元素节点\n */\nexport class LinkedListNode {\n  public value;\n\n  public next: LinkedListNode;\n\n  constructor(value, next: LinkedListNode = null) {\n    this.value = value;\n    this.next = next;\n  }\n\n  toString(callback?: any) {\n    return callback ? callback(this.value) : `${this.value}`;\n  }\n}\n\nexport default class LinkedList {\n  public head: LinkedListNode;\n\n  public tail: LinkedListNode;\n\n  public compare: Function;\n\n  constructor(comparator = defaultComparator) {\n    this.head = null;\n    this.tail = null;\n    this.compare = comparator;\n  }\n\n  /**\n   * 将指定元素添加到链表头部\n   * @param value\n   */\n  prepend(value) {\n    // 在头部添加一个节点\n    const newNode = new LinkedListNode(value, this.head);\n    this.head = newNode;\n\n    if (!this.tail) {\n      this.tail = newNode;\n    }\n\n    return this;\n  }\n\n  /**\n   * 将指定元素添加到链表中\n   * @param value\n   */\n  append(value) {\n    const newNode = new LinkedListNode(value);\n\n    // 如果不存在头节点，则将创建的新节点作为头节点\n    if (!this.head) {\n      this.head = newNode;\n      this.tail = newNode;\n\n      return this;\n    }\n\n    // 将新节点附加到链表末尾\n    this.tail.next = newNode;\n    this.tail = newNode;\n\n    return this;\n  }\n\n  /**\n   * 删除指定元素\n   * @param value 要删除的元素\n   */\n  delete(value): LinkedListNode {\n    if (!this.head) {\n      return null;\n    }\n\n    let deleteNode = null;\n\n    // 如果删除的是头部元素，则将next作为头元素\n    while (this.head && this.compare(this.head.value, value)) {\n      deleteNode = this.head;\n      this.head = this.head.next;\n    }\n\n    let currentNode = this.head;\n\n    if (currentNode !== null) {\n      // 如果删除了节点以后，将next节点前移\n      while (currentNode.next) {\n        if (this.compare(currentNode.next.value, value)) {\n          deleteNode = currentNode.next;\n          currentNode.next = currentNode.next.next;\n        } else {\n          currentNode = currentNode.next;\n        }\n      }\n    }\n\n    // 检查尾部节点是否被删除\n    if (this.compare(this.tail.value, value)) {\n      this.tail = currentNode;\n    }\n\n    return deleteNode;\n  }\n\n  /**\n   * 查找指定的元素\n   * @param param0\n   */\n  find({ value = undefined, callback = undefined }): LinkedListNode {\n    if (!this.head) {\n      return null;\n    }\n\n    let currentNode = this.head;\n\n    while (currentNode) {\n      // 如果指定了 callback，则按指定的 callback 查找\n      if (callback && callback(currentNode.value)) {\n        return currentNode;\n      }\n\n      // 如果指定了 value，则按 value 查找\n      if (value !== undefined && this.compare(currentNode.value, value)) {\n        return currentNode;\n      }\n\n      currentNode = currentNode.next;\n    }\n\n    return null;\n  }\n\n  /**\n   * 删除尾部节点\n   */\n  deleteTail() {\n    const deletedTail = this.tail;\n\n    if (this.head === this.tail) {\n      // 链表中只有一个元素\n      this.head = null;\n      this.tail = null;\n      return deletedTail;\n    }\n\n    let currentNode = this.head;\n    while (currentNode.next) {\n      if (!currentNode.next.next) {\n        currentNode.next = null;\n      } else {\n        currentNode = currentNode.next;\n      }\n    }\n\n    this.tail = currentNode;\n\n    return deletedTail;\n  }\n\n  /**\n   * 删除头部节点\n   */\n  deleteHead() {\n    if (!this.head) {\n      return null;\n    }\n\n    const deletedHead = this.head;\n\n    if (this.head.next) {\n      this.head = this.head.next;\n    } else {\n      this.head = null;\n      this.tail = null;\n    }\n\n    return deletedHead;\n  }\n\n  /**\n   * 将一组元素转成链表中的节点\n   * @param values 链表中的元素\n   */\n  fromArray(values) {\n    values.forEach((value) => this.append(value));\n    return this;\n  }\n\n  /**\n   * 将链表中的节点转成数组元素\n   */\n  toArray() {\n    const nodes = [];\n\n    let currentNode = this.head;\n\n    while (currentNode) {\n      nodes.push(currentNode);\n      currentNode = currentNode.next;\n    }\n\n    return nodes;\n  }\n\n  /**\n   * 反转链表中的元素节点\n   */\n  reverse() {\n    let currentNode = this.head;\n    let prevNode = null;\n    let nextNode = null;\n    while (currentNode) {\n      // 存储下一个元素节点\n      nextNode = currentNode.next;\n\n      // 更改当前节点的下一个节点，以便将它连接到上一个节点上\n      currentNode.next = prevNode;\n\n      // 将 prevNode 和 currentNode 向前移动一步\n      prevNode = currentNode;\n      currentNode = nextNode;\n    }\n\n    this.tail = this.head;\n    this.head = prevNode;\n  }\n\n  toString(callback = undefined) {\n    return this.toArray()\n      .map((node) => node.toString(callback))\n      .toString();\n  }\n}\n", "import LinkedList from './linked-list';\n\nexport default class Queue {\n  public linkedList: LinkedList;\n\n  constructor() {\n    this.linkedList = new LinkedList();\n  }\n\n  /**\n   * 队列是否为空\n   */\n  public isEmpty() {\n    return !this.linkedList.head;\n  }\n\n  /**\n   * 读取队列头部的元素， 不删除队列中的元素\n   */\n  public peek() {\n    if (!this.linkedList.head) {\n      return null;\n    }\n    return this.linkedList.head.value;\n  }\n\n  /**\n   * 在队列的尾部新增一个元素\n   * @param value\n   */\n  public enqueue(value) {\n    this.linkedList.append(value);\n  }\n\n  /**\n   * 删除队列中的头部元素，如果队列为空，则返回 null\n   */\n  public dequeue() {\n    const removeHead = this.linkedList.deleteHead();\n    return removeHead ? removeHead.value : null;\n  }\n\n  public toString(callback?: any) {\n    return this.linkedList.toString(callback);\n  }\n}\n", "import { EdgeConfig, GraphData, Matrix } from './types'\n\n/**\n * 获取指定节点的所有邻居\n * @param nodeId 节点 ID\n * @param edges 图中的所有边数据\n * @param type 邻居类型\n */\nexport const getNeighbors = (nodeId: string, edges: EdgeConfig[] = [], type?: 'target' | 'source' | undefined): string[] => {\n  const currentEdges = edges.filter(edge => edge.source === nodeId || edge.target === nodeId)\n  if (type === 'target') {\n    // 当前节点为 source，它所指向的目标节点\n    const neighhborsConverter = (edge: EdgeConfig) => {\n      return edge.source === nodeId;\n    };\n    return currentEdges.filter(neighhborsConverter).map((edge) => edge.target);\n  }\n  if (type === 'source') {\n    // 当前节点为 target，它所指向的源节点\n    const neighhborsConverter = (edge: EdgeConfig) => {\n      return edge.target === nodeId;\n    };\n    return currentEdges.filter(neighhborsConverter).map((edge) => edge.source);\n  }\n\n  // 若未指定 type ，则返回所有邻居\n  const neighhborsConverter = (edge: EdgeConfig) => {\n    return edge.source === nodeId ? edge.target : edge.source;\n  };\n  return currentEdges.map(neighhborsConverter);\n}\n\n/**\n * 获取指定节点的出边\n * @param nodeId 节点 ID\n * @param edges 图中的所有边数据\n */\nexport const getOutEdgesNodeId = (nodeId: string, edges: EdgeConfig[]) => {\n  return edges.filter(edge => edge.source === nodeId)\n}\n\n/**\n * 获取指定节点的边，包括出边和入边\n * @param nodeId 节点 ID\n * @param edges 图中的所有边数据\n */\nexport const getEdgesByNodeId = (nodeId: string, edges: EdgeConfig[]) => {\n  return edges.filter(edge => edge.source === nodeId || edge.target === nodeId)\n}\n\n/**\n * 生成唯一的 ID，规则是序号 + 时间戳\n * @param index 序号\n */\nexport const uniqueId = (index: number = 0) => {\n  const random1 = `${Math.random()}`.split('.')[1].substr(0, 5);\n  const random2 = `${Math.random()}`.split('.')[1].substr(0, 5);\n  return `${index}-${random1}${random2}`\n};\n", "import { GraphData, NodeConfig } from \"./types\";\nimport { getNeighbors } from \"./util\";\n\n/**\n * Generate all connected components for an undirected graph\n * @param graph\n */\nexport const detectConnectedComponents = (graphData: GraphData): NodeConfig[][] => {\n  const { nodes = [], edges = [] } = graphData\n  const allComponents: NodeConfig[][] = [];\n  const visited = {};\n  const nodeStack: NodeConfig[] = [];\n\n  const getComponent = (node: NodeConfig) => {\n    nodeStack.push(node);\n    visited[node.id] = true;\n    const neighbors = getNeighbors(node.id, edges);\n    for (let i = 0; i < neighbors.length; ++i) {\n      const neighbor = neighbors[i];\n      if (!visited[neighbor]) {\n        const targetNode = nodes.filter(node => node.id === neighbor)\n        if (targetNode.length > 0) {\n          getComponent(targetNode[0]);\n        }\n      }\n    }\n  };\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!visited[node.id]) {\n      // 对于无向图进行dfs遍历，每一次调用后都得到一个连通分量\n      getComponent(node);\n      const component = [];\n      while (nodeStack.length > 0) {\n        component.push(nodeStack.pop());\n      }\n      allComponents.push(component);\n    }\n  }\n  return allComponents;\n}\n\n/**\n * Tarjan's Algorithm 复杂度  O(|V|+|E|)\n * For directed graph only\n * a directed graph is said to be strongly connected if \"every vertex is reachable from every other vertex\".\n * refer: http://en.wikipedia.org/wiki/Tarjan%27s_strongly_connected_components_algorithm\n * @param graph\n * @return a list of strongly connected components\n */\nexport const detectStrongConnectComponents = (graphData: GraphData): NodeConfig[][] => {\n  const { nodes = [], edges = [] } = graphData\n  const nodeStack: NodeConfig[] = [];\n  const inStack = {}; // 辅助判断是否已经在stack中，减少查找开销\n  const indices = {};\n  const lowLink = {};\n  const allComponents: NodeConfig[][] = [];\n  let index = 0;\n\n  const getComponent = (node: NodeConfig) => {\n    // Set the depth index for v to the smallest unused index\n    indices[node.id] = index;\n    lowLink[node.id] = index;\n    index += 1;\n    nodeStack.push(node);\n    inStack[node.id] = true;\n\n    // 考虑每个邻接点\n    const neighbors = getNeighbors(node.id, edges, 'target').filter((n) => nodes.map(node => node.id).indexOf(n) > -1);\n    for (let i = 0; i < neighbors.length; i++) {\n      const targetNodeID = neighbors[i];\n      if (!indices[targetNodeID] && indices[targetNodeID] !== 0) {\n        const targetNode = nodes.filter(node => node.id === targetNodeID)\n        if (targetNode.length > 0) {\n          getComponent(targetNode[0]);\n        }\n        // tree edge\n        lowLink[node.id] = Math.min(lowLink[node.id], lowLink[targetNodeID]);\n      } else if (inStack[targetNodeID]) {\n        // back edge, target node is in the current SCC\n        lowLink[node.id] = Math.min(lowLink[node.id], indices[targetNodeID]);\n      }\n    }\n\n    // If node is a root node, generate an SCC\n    if (lowLink[node.id] === indices[node.id]) {\n      const component = [];\n      while (nodeStack.length > 0) {\n        const tmpNode = nodeStack.pop();\n        inStack[tmpNode.id] = false;\n        component.push(tmpNode);\n        if (tmpNode === node) break;\n      }\n      if (component.length > 0) {\n        allComponents.push(component);\n      }\n    }\n  };\n\n  for (const node of nodes) {\n    if (!indices[node.id] && indices[node.id] !== 0) {\n      getComponent(node);\n    }\n  }\n\n  return allComponents;\n}\n\nexport default function getConnectedComponents(graphData: GraphData, directed?: boolean): NodeConfig[][] {\n  if (directed) return detectStrongConnectComponents(graphData);\n  return detectConnectedComponents(graphData);\n}\n", "import { GraphData, DegreeType } from \"./types\";\n\nconst degree = (graphData: GraphData): DegreeType => {\n  const degrees: DegreeType = {};\n  const { nodes = [], edges = [] } = graphData\n\n  nodes.forEach((node) => {\n    degrees[node.id] = {\n      degree: 0,\n      inDegree: 0,\n      outDegree: 0,\n    };\n  });\n\n  edges.forEach((edge) => {\n    degrees[edge.source].degree++;\n    degrees[edge.source].outDegree++;\n    degrees[edge.target].degree++;\n    degrees[edge.target].inDegree++;\n  });\n\n  return degrees;\n};\n\nexport default degree;\n\n/**\n * 获取指定节点的入度\n * @param graphData 图数据\n * @param nodeId 节点ID\n */\nexport const getInDegree = (graphData: GraphData, nodeId: string): number => {\n  const nodeDegree = degree(graphData)\n  if (nodeDegree[nodeId]) {\n    return degree(graphData)[nodeId].inDegree\n  }\n  return 0\n}\n\n/**\n * 获取指定节点的出度\n * @param graphData 图数据\n * @param nodeId 节点ID\n */\nexport const getOutDegree = (graphData: GraphData, nodeId: string): number => {\n  const nodeDegree = degree(graphData)\n  if (nodeDegree[nodeId]) {\n    return degree(graphData)[nodeId].outDegree\n  }\n  return 0\n}\n", "import { IAlgorithmCallbacks, GraphData } from './types';\nimport { getNeighbors } from './util';\n\nfunction initCallbacks(callbacks: IAlgorithmCallbacks = {} as IAlgorithmCallbacks) {\n  const initiatedCallback = callbacks;\n\n  const stubCallback = () => {};\n\n  const allowTraversalCallback = (() => {\n    const seen = {};\n    return ({ next }) => {\n      if (!seen[next]) {\n        seen[next] = true;\n        return true;\n      }\n      return false;\n    };\n  })();\n\n  initiatedCallback.allowTraversal = callbacks.allowTraversal || allowTraversalCallback;\n  initiatedCallback.enter = callbacks.enter || stubCallback;\n  initiatedCallback.leave = callbacks.leave || stubCallback;\n\n  return initiatedCallback;\n}\n\n/**\n * @param {Graph} graph\n * @param {GraphNode} currentNode\n * @param {GraphNode} previousNode\n * @param {Callbacks} callbacks\n */\nfunction depthFirstSearchRecursive(\n  graphData: GraphData,\n  currentNode: string,\n  previousNode: string,\n  callbacks: IAlgorithmCallbacks,\n  directed: boolean = true,\n) {\n  callbacks.enter({\n    current: currentNode,\n    previous: previousNode,\n  });\n\n  const { edges = [] } = graphData;\n\n  getNeighbors(currentNode, edges, directed ? 'target' : undefined).forEach((nextNode) => {\n    if (\n      callbacks.allowTraversal({\n        previous: previousNode,\n        current: currentNode,\n        next: nextNode,\n      })\n    ) {\n      depthFirstSearchRecursive(graphData, nextNode, currentNode, callbacks, directed);\n    }\n  });\n\n  callbacks.leave({\n    current: currentNode,\n    previous: previousNode,\n  });\n}\n\n/**\n * 深度优先遍历图\n * @param data GraphData 图数据\n * @param startNodeId 开始遍历的节点的 ID\n * @param originalCallbacks 回调\n */\nexport default function depthFirstSearch(\n  graphData: GraphData,\n  startNodeId: string,\n  callbacks?: IAlgorithmCallbacks,\n  directed: boolean = true,\n) {\n  depthFirstSearchRecursive(graphData, startNodeId, '', initCallbacks(callbacks), directed);\n}\n", "import dfs from './dfs';\nimport getConnectedComponents, { detectStrongConnectComponents } from './connected-component';\nimport { GraphData, IAlgorithmCallbacks, NodeConfig } from './types';\nimport { getNeighbors } from './util';\n\nconst detectDirectedCycle = (graphData: GraphData): {\n  [key: string]: string;\n} => {\n  let cycle: {\n    [key: string]: string;\n  } = null;\n\n  const { nodes = [] } = graphData\n\n  const dfsParentMap = {};\n\n  // 所有没有被访问的节点集合\n  const unvisitedSet = {};\n\n  // 正在被访问的节点集合\n  const visitingSet = {};\n\n  // 所有已经被访问过的节点集合\n  const visitedSet = {};\n\n  // 初始化 unvisitedSet\n  nodes.forEach((node) => {\n    unvisitedSet[node.id] = node;\n  });\n\n  const callbacks: IAlgorithmCallbacks = {\n    enter: ({ current: currentNode, previous: previousNode }) => {\n      if (visitingSet[currentNode]) {\n        // 如果当前节点正在访问中，则说明检测到环路了\n        cycle = {};\n\n        let currentCycleNode = currentNode;\n        let previousCycleNode = previousNode;\n\n        while (previousCycleNode !== currentNode) {\n          cycle[currentCycleNode] = previousCycleNode;\n          currentCycleNode = previousCycleNode;\n          previousCycleNode = dfsParentMap[previousCycleNode];\n        }\n\n        cycle[currentCycleNode] = previousCycleNode;\n      } else {\n        // 如果不存在正在访问集合中，则将其放入正在访问集合，并从未访问集合中删除\n        visitingSet[currentNode] = currentNode;\n        delete unvisitedSet[currentNode];\n\n        // 更新 DSF parents 列表\n        dfsParentMap[currentNode] = previousNode;\n      }\n    },\n    leave: ({ current: currentNode }) => {\n      // 如果所有的节点的子节点都已经访问过了，则从正在访问集合中删除掉，并将其移入到已访问集合中，\n      // 同时也意味着当前节点的所有邻居节点都被访问过了\n      visitedSet[currentNode] = currentNode;\n      delete visitingSet[currentNode];\n    },\n    allowTraversal: ({ next: nextNode }) => {\n      // 如果检测到环路则需要终止所有进一步的遍历，否则会导致无限循环遍历\n      if (cycle) {\n        return false;\n      }\n\n      // 仅允许遍历没有访问的节点，visitedSet 中的都已经访问过了\n      return !visitedSet[nextNode];\n    },\n  };\n\n  // 开始遍历节点\n  while (Object.keys(unvisitedSet).length) {\n    // 从第一个节点开始进行 DFS 遍历\n    const firsetUnVisitedKey = Object.keys(unvisitedSet)[0];\n\n    dfs(graphData, firsetUnVisitedKey, callbacks);\n  }\n\n  return cycle;\n};\n\n/**\n * 检测无向图中的所有Base cycles\n * refer: https://www.codeproject.com/Articles/1158232/Enumerating-All-Cycles-in-an-Undirected-Graph\n * @param graph\n * @param nodeIds 节点 ID 的数组\n * @param include 包含或排除指定的节点\n * @return [{[key: string]: INode}] 返回一组base cycles\n */\nexport const detectAllUndirectedCycle = (graphData: GraphData, nodeIds?: string[], include = true) => {\n  const allCycles = [];\n  const components = getConnectedComponents(graphData, false);\n\n  // loop through all connected components\n  for (const component of components) {\n    if (!component.length) continue;\n    const root = component[0];\n    const rootId = root.id;\n\n    const stack = [root];\n    const parent = { [rootId]: root };\n    const used = { [rootId]: new Set() };\n\n    // walk a spanning tree to find cycles\n    while (stack.length > 0) {\n      const curNode = stack.pop();\n      const curNodeId = curNode.id;\n      const neighbors = getNeighbors(curNodeId, graphData.edges);\n      for (let i = 0; i < neighbors.length; i += 1) {\n        const neighborId = neighbors[i];\n        const neighbor = graphData.nodes.find(node => node.id === neighborId)\n        // const neighborId = neighbor.get('id');\n        if (neighborId === curNodeId) {\n          // 自环\n          allCycles.push({ [neighborId]: curNode });\n        } else if (!(neighborId in used)) {\n          // visit a new node\n          parent[neighborId] = curNode;\n          stack.push(neighbor);\n          used[neighborId] = new Set([curNode]);\n        } else if (!used[curNodeId].has(neighbor)) {\n          // a cycle found\n          let cycleValid = true;\n          const cyclePath = [neighbor, curNode];\n          let p = parent[curNodeId];\n          while (used[neighborId].size && !used[neighborId].has(p)) {\n            cyclePath.push(p);\n            if (p === parent[p.id]) break;\n            else p = parent[p.id];\n          }\n          cyclePath.push(p);\n\n          if (nodeIds && include) {\n            // 如果有指定包含的节点\n            cycleValid = false;\n            if (cyclePath.findIndex((node) => nodeIds.indexOf(node.id) > -1) > -1) {\n              cycleValid = true;\n            }\n          } else if (nodeIds && !include) {\n            // 如果有指定不包含的节点\n            if (cyclePath.findIndex((node) => nodeIds.indexOf(node.id) > -1) > -1) {\n              cycleValid = false;\n            }\n          }\n\n          // 把 node list 形式转换为 cycle 的格式\n          if (cycleValid) {\n            const cycle = {};\n            for (let index = 1; index < cyclePath.length; index += 1) {\n              cycle[cyclePath[index - 1].id] = cyclePath[index];\n            }\n            if (cyclePath.length) {\n              cycle[cyclePath[cyclePath.length - 1].id] = cyclePath[0];\n            }\n            allCycles.push(cycle);\n          }\n\n          used[neighborId].add(curNode);\n        }\n      }\n    }\n  }\n\n  return allCycles;\n};\n\n/**\n * Johnson's algorithm, 时间复杂度 O((V + E)(C + 1))$ and space bounded by O(V + E)\n * refer: https://www.cs.tufts.edu/comp/150GA/homeworks/hw1/Johnson%2075.PDF\n * refer: https://networkx.github.io/documentation/stable/_modules/networkx/algorithms/cycles.html#simple_cycles\n * @param graph\n * @param nodeIds 节点 ID 的数组\n * @param include 包含或排除指定的节点\n * @return [{[key: string]: INode}] 返回所有的 simple cycles\n */\nexport const detectAllDirectedCycle = (graphData: GraphData, nodeIds?: string[], include = true) => {\n  const path = []; // stack of nodes in current path\n  const blocked = new Set();\n  const B = []; // remember portions of the graph that yield no elementary circuit\n  const allCycles = [];\n  const idx2Node: {\n    [key: string]: NodeConfig;\n  } = {};\n  const node2Idx = {};\n\n  // 辅助函数： unblock all blocked nodes\n  const unblock = (thisNode: NodeConfig) => {\n    const stack = [thisNode];\n    while (stack.length > 0) {\n      const node = stack.pop();\n      if (blocked.has(node)) {\n        blocked.delete(node);\n        B[node.id].forEach((n) => {\n          stack.push(n);\n        });\n        B[node.id].clear();\n      }\n    }\n  };\n\n  const circuit = (node: NodeConfig, start: NodeConfig, adjList) => {\n    let closed = false; // whether a path is closed\n    if (nodeIds && include === false && nodeIds.indexOf(node.id) > -1) return closed;\n    path.push(node);\n    blocked.add(node);\n\n    const neighbors = adjList[node.id];\n    for (let i = 0; i < neighbors.length; i += 1) {\n      const neighbor = idx2Node[neighbors[i]];\n      if (neighbor === start) {\n        const cycle = {};\n        for (let index = 1; index < path.length; index += 1) {\n          cycle[path[index - 1].id] = path[index];\n        }\n        if (path.length) {\n          cycle[path[path.length - 1].id] = path[0];\n        }\n        allCycles.push(cycle);\n        closed = true;\n      } else if (!blocked.has(neighbor)) {\n        if (circuit(neighbor, start, adjList)) {\n          closed = true;\n        }\n      }\n    }\n\n    if (closed) {\n      unblock(node);\n    } else {\n      for (let i = 0; i < neighbors.length; i += 1) {\n        const neighbor = idx2Node[neighbors[i]];\n        if (!B[neighbor.id].has(node)) {\n          B[neighbor.id].add(node);\n        }\n      }\n    }\n    path.pop();\n    return closed;\n  };\n\n  const { nodes = [] } = graphData;\n\n  // Johnson's algorithm 要求给节点赋顺序，先按节点在数组中的顺序\n  for (let i = 0; i < nodes.length; i += 1) {\n    const node = nodes[i];\n    const nodeId = node.id;\n    node2Idx[nodeId] = i;\n    idx2Node[i] = node;\n  }\n  // 如果有指定包含的节点，则把指定节点排序在前，以便提早结束搜索\n  if (nodeIds && include) {\n    for (let i = 0; i < nodeIds.length; i++) {\n      const nodeId = nodeIds[i];\n      node2Idx[nodes[i].id] = node2Idx[nodeId];\n      node2Idx[nodeId] = 0;\n      idx2Node[0] = nodes.find(node => node.id === nodeId);\n      idx2Node[node2Idx[nodes[i].id]] = nodes[i];\n    }\n  }\n\n  // 返回 节点顺序 >= nodeOrder 的强连通分量的adjList\n  const getMinComponentAdj = (components: NodeConfig[][]) => {\n    let minCompIdx;\n    let minIdx = Infinity;\n\n    // Find least component and the lowest node\n    for (let i = 0; i < components.length; i += 1) {\n      const comp = components[i];\n      for (let j = 0; j < comp.length; j++) {\n        const nodeIdx = node2Idx[comp[j].id];\n        if (nodeIdx < minIdx) {\n          minIdx = nodeIdx;\n          minCompIdx = i;\n        }\n      }\n    }\n\n    const component = components[minCompIdx];\n    const adjList = [];\n    for (let i = 0; i < component.length; i += 1) {\n      const node = component[i];\n      adjList[node.id] = [];\n      for (const neighbor of getNeighbors(node.id, graphData.edges, 'target').filter((n) => component.map(c => c.id).indexOf(n) > -1)) {\n        // 对自环情况 (点连向自身) 特殊处理：记录自环，但不加入adjList\n        if (neighbor === node.id && !(include === false && nodeIds.indexOf(node.id) > -1)) {\n          allCycles.push({ [node.id]: node });\n        } else {\n          adjList[node.id].push(node2Idx[neighbor]);\n        }\n      }\n    }\n\n    return {\n      component,\n      adjList,\n      minIdx,\n    };\n  };\n\n  let nodeIdx = 0;\n  while (nodeIdx < nodes.length) {\n    const subgraphNodes = nodes.filter((n) => node2Idx[n.id] >= nodeIdx);\n    const sccs = detectStrongConnectComponents({ nodes: subgraphNodes, edges: graphData.edges }).filter(\n      (component) => component.length > 1,\n    );\n    if (sccs.length === 0) break;\n\n    const scc = getMinComponentAdj(sccs);\n    const { minIdx, adjList, component } = scc;\n    if (component.length > 1) {\n      component.forEach((node) => {\n        B[node.id] = new Set();\n      });\n      const startNode = idx2Node[minIdx];\n      // startNode 不在指定要包含的节点中，提前结束搜索\n      if (nodeIds && include && nodeIds.indexOf(startNode.id) === -1) return allCycles;\n      circuit(startNode, startNode, adjList);\n      nodeIdx = minIdx + 1;\n    } else {\n      break;\n    }\n  }\n  return allCycles;\n};\n\n/**\n * 查找图中所有满足要求的圈\n * @param graph\n * @param directed 是否为有向图\n * @param nodeIds 节点 ID 的数组，若不指定，则返回图中所有的圈\n * @param include 包含或排除指定的节点\n * @return [{[key: string]: Node}] 包含所有环的数组，每个环用一个Object表示，其中key为节点id，value为该节点在环中指向的下一个节点\n */\nexport const detectAllCycles = (\n  graphData: GraphData,\n  directed?: boolean,\n  nodeIds?: string[],\n  include = true,\n) => {\n  if (directed) return detectAllDirectedCycle(graphData, nodeIds, include);\n  return detectAllUndirectedCycle(graphData, nodeIds, include);\n};\n\nexport default detectDirectedCycle;\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n  return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose;\n    if (async) {\n        if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n        dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n        if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n        dispose = value[Symbol.dispose];\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  function next() {\n    while (env.stack.length) {\n      var rec = env.stack.pop();\n      try {\n        var result = rec.dispose && rec.dispose.call(rec.value);\n        if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n      }\n      catch (e) {\n          fail(e);\n      }\n    }\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n};\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "import { isArray } from '@antv/util';\nimport { GraphData, NodeConfig, EdgeConfig } from './types';\nimport { getOutEdgesNodeId, getEdgesByNodeId } from './util';\n\nconst minVertex = (\n  D: { [key: string]: number },\n  nodes: NodeConfig[],\n  marks: { [key: string]: boolean },\n): NodeConfig => {\n  // 找出最小的点\n  let minDis = Infinity;\n  let minNode;\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeId = nodes[i].id;\n    if (!marks[nodeId] && D[nodeId] <= minDis) {\n      minDis = D[nodeId];\n      minNode = nodes[i];\n    }\n  }\n  return minNode;\n};\n\nconst dijkstra = (\n  graphData: GraphData,\n  source: string,\n  directed?: boolean,\n  weightPropertyName?: string,\n) => {\n  const { nodes = [], edges = [] } = graphData;\n  const nodeIds = [];\n  const marks = {};\n  const D = {};\n  const prevs = {}; // key: 顶点, value: 顶点的前驱点数组（可能有多条等长的最短路径）\n  nodes.forEach((node, i) => {\n    const id = node.id;\n    nodeIds.push(id);\n    D[id] = Infinity;\n    if (id === source) D[id] = 0;\n  });\n\n  const nodeNum = nodes.length;\n  for (let i = 0; i < nodeNum; i++) {\n    // Process the vertices\n    const minNode = minVertex(D, nodes, marks);\n    const minNodeId = minNode.id;\n    marks[minNodeId] = true;\n\n    if (D[minNodeId] === Infinity) continue; // Unreachable vertices cannot be the intermediate point\n\n    let relatedEdges: EdgeConfig[] = [];\n    if (directed) relatedEdges = getOutEdgesNodeId(minNodeId, edges);\n    else relatedEdges = getEdgesByNodeId(minNodeId, edges);\n\n    relatedEdges.forEach(edge => {\n      const edgeTarget = edge.target;\n      const edgeSource = edge.source;\n      const w = edgeTarget === minNodeId ? edgeSource : edgeTarget;\n      const weight = weightPropertyName && edge[weightPropertyName] ? edge[weightPropertyName] : 1;\n      if (D[w] > D[minNode.id] + weight) {\n        D[w] = D[minNode.id] + weight;\n        prevs[w] = [minNode.id];\n      } else if (D[w] === D[minNode.id] + weight) {\n        prevs[w].push(minNode.id);\n      }\n    });\n  }\n\n  prevs[source] = [source];\n  // 每个节点存可能存在多条最短路径\n  const paths = {};\n  for (const target in D) {\n    if (D[target] !== Infinity) {\n      findAllPaths(source, target, prevs, paths);\n    }\n  }\n\n  // 兼容之前单路径\n  const path = {};\n  for (const target in paths) {\n    path[target] = paths[target][0];\n  }\n  return { length: D, path, allPath: paths };\n};\n\nexport default dijkstra;\n\nfunction findAllPaths(source, target, prevs, foundPaths) {\n  if (source === target) {\n    return [source];\n  }\n  if (foundPaths[target]) {\n    return foundPaths[target];\n  }\n  const paths = [];\n  for (let prev of prevs[target]) {\n    const prevPaths = findAllPaths(source, prev, prevs, foundPaths);\n    if (!prevPaths) return;\n    for (let prePath of prevPaths) {\n      if (isArray(prePath)) paths.push([...prePath, target]);\n      else paths.push([prePath, target]);\n    }\n  }\n  foundPaths[target] = paths;\n  return foundPaths[target];\n}\n", "import getAdjMatrix from \"./adjacent-matrix\";\nimport { GraphData, Matrix } from \"./types\";\n\nconst floydWarshall = (graphData: GraphData, directed?: boolean) => {\n  const adjacentMatrix = getAdjMatrix(graphData, directed);\n\n  const dist: Matrix[] = [];\n  const size = adjacentMatrix.length;\n  for (let i = 0; i < size; i += 1) {\n    dist[i] = [];\n    for (let j = 0; j < size; j += 1) {\n      if (i === j) {\n        dist[i][j] = 0;\n      } else if (adjacentMatrix[i][j] === 0 || !adjacentMatrix[i][j]) {\n        dist[i][j] = Infinity;\n      } else {\n        dist[i][j] = adjacentMatrix[i][j];\n      }\n    }\n  }\n  // floyd\n  for (let k = 0; k < size; k += 1) {\n    for (let i = 0; i < size; i += 1) {\n      for (let j = 0; j < size; j += 1) {\n        if (dist[i][j] > dist[i][k] + dist[k][j]) {\n          dist[i][j] = dist[i][k] + dist[k][j];\n        }\n      }\n    }\n  }\n  return dist;\n};\n\nexport default floydWarshall;\n", "\n/**\n * 向量运算\n */\nimport { clone } from '@antv/util';\n\nclass Vector {\n  arr: number[];\n\n  constructor(arr) {\n    this.arr = arr;\n  }\n\n  getArr() {\n    return this.arr || [];\n  }\n\n  add(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length) {\n      return new Vector(otherArr);\n    }\n    if (!otherArr?.length) {\n      return new Vector(this.arr);\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = [];\n      for (let index in this.arr) {\n        res[index] = this.arr[index] + otherArr[index];\n      }\n      return new Vector(res);\n    }\n  }\n\n  subtract(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length) {\n      return new Vector(otherArr);\n    }\n    if (!otherArr?.length) {\n      return new Vector(this.arr);\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = [];\n      for (let index in this.arr) {\n        res[index] = this.arr[index] - otherArr[index];\n      }\n      return new Vector(res);\n    }\n  }\n\n  avg(length) {\n    let res = [];\n    if (length !== 0) {\n      for (let index in this.arr) {\n        res[index] = this.arr[index] / length;\n      }\n    }\n    return new Vector(res);\n  }\n\n  negate() {\n    let res = [];\n    for (let index in this.arr) {\n      res[index] = - this.arr[index];\n    }\n    return new Vector(res);\n  }\n\n  // 平方欧式距离\n  squareEuclideanDistance(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length || !otherArr?.length) {\n      return 0;\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = 0;\n      for (let index in this.arr) {\n        res += Math.pow(this.arr[index] - otherVector.arr[index], 2);\n      }\n      return res;\n    }\n  }\n\n  // 欧式距离\n  euclideanDistance(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length || !otherArr?.length) {\n      return 0;\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = 0;\n      for (let index in this.arr) {\n        res += Math.pow(this.arr[index] - otherVector.arr[index], 2);\n      }\n      return Math.sqrt(res);\n    } else {\n      console.error('The two vectors are unequal in length.')\n    }\n  }\n\n  // 归一化处理\n  normalize() {\n    let res = [];\n    const cloneArr = clone(this.arr);\n    cloneArr.sort((a, b) => a - b);\n    const max = cloneArr[cloneArr.length - 1];\n    const min = cloneArr[0];\n    for (let index in this.arr) {\n      res[index] = (this.arr[index] - min) / (max - min);\n    }\n    return new Vector(res);\n  }\n\n  // 2范数 or 模长\n  norm2() {\n    if (!this.arr?.length) {\n      return 0;\n    }\n    let res = 0;\n      for (let index in this.arr) {\n        res += Math.pow(this.arr[index], 2);\n      }\n    return Math.sqrt(res);\n  }\n\n  // 两个向量的点积\n  dot(otherVector) {\n    const otherArr = otherVector.arr;\n    if (!this.arr?.length || !otherArr?.length) {\n      return 0;\n    }\n    if (this.arr.length === otherArr.length) {\n      let res = 0;\n      for (let index in this.arr) {\n        res += this.arr[index] * otherVector.arr[index];\n      }\n      return res;\n    } else {\n      console.error('The two vectors are unequal in length.')\n    }\n  }\n\n  // 两个向量比较\n  equal(otherVector) {\n    const otherArr = otherVector.arr;\n    if (this.arr?.length !== otherArr?.length) {\n      return false;\n    }\n    for (let index in this.arr) {\n      if (this.arr[index] !== otherArr[index]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n\nexport default Vector;\n", "export const secondReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2})$/;\nexport const dateReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2}) (\\d{1,2}):(\\d{1,2}):(\\d{1,2})$/;\n", "\nexport type Matrix = number[];\n\nexport interface NodeConfig {\n  id: string;\n  clusterId?: string;\n  [key: string]: any;\n}\n\nexport interface EdgeConfig {\n  source: string;\n  target: string;\n  weight?: number;\n  [key: string]: any;\n}\n\nexport interface GraphData {\n  nodes?: NodeConfig[];\n  edges?: EdgeConfig[];\n}\n\nexport interface Cluster {\n  id: string;\n  nodes: NodeConfig[];\n  sumTot?: number;\n}\n\nexport interface ClusterData {\n  clusters: Cluster[];\n  clusterEdges: EdgeConfig[];\n}\n\nexport interface ClusterMap {\n  [key: string]: Cluster\n}\n\n// 图算法回调方法接口定义\nexport interface IAlgorithmCallbacks {\n  enter?: (param: { current: string; previous: string }) => void;\n  leave?: (param: { current: string; previous?: string }) => void;\n  allowTraversal?: (param: { previous?: string; current?: string; next: string }) => boolean;\n}\n\nexport interface DegreeType {\n  [key: string]: {\n    degree: number;\n    inDegree: number;\n    outDegree: number;\n  }\n}\n\nexport enum DistanceType {\n  EuclideanDistance = 'euclideanDistance',\n}\n\nexport interface PlainObject {\n  [key: string]: any;\n}\n\n// 数据集中属性/特征值分布的map\nexport interface KeyValueMap {\n  [key:string]: any[];\n}\n\nexport interface IAlgorithm {\n  getAdjMatrix: (graphData: GraphData, directed?: boolean) => Matrix[],\n  breadthFirstSearch: (\n    graphData: GraphData,\n    startNodeId: string,\n    originalCallbacks?: IAlgorithmCallbacks,\n    directed?: boolean\n  ) => void,\n  connectedComponent: (graphData: GraphData, directed?: boolean) => NodeConfig[][],\n  getDegree: (graphData: GraphData) => DegreeType,\n  getInDegree: (graphData: GraphData, nodeId: string) => number,\n  getOutDegree: (graphData: GraphData, nodeId: string) => number,\n  detectCycle: (graphData: GraphData) => { [key: string]: string },\n  detectDirectedCycle: (graphData: GraphData) => { [key: string]: string },\n  detectAllCycles: (graphData: GraphData, directed?: boolean, nodeIds?: string[], include?: boolean) => any,\n  detectAllDirectedCycle: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  detectAllUndirectedCycle: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  depthFirstSearch: (graphData: GraphData, startNodeId: string, callbacks?: IAlgorithmCallbacks) => void,\n  dijkstra: (graphData: GraphData, source: string, directed?: boolean, weightPropertyName?: string) => { length: object, allPath: object, path: object},\n  findAllPath: (graphData: GraphData, start: string, end: string, directed?: boolean) => any,\n  findShortestPath: (graphData: GraphData, start: string, end: string, directed?: boolean, weightPropertyName?: string) => any,\n  floydWarshall: (graphData: GraphData, directed?: boolean) => Matrix[],\n  labelPropagation: (graphData: GraphData, directed?: boolean, weightPropertyName?: string, maxIteration?: number) => ClusterData,\n  louvain: (graphData: GraphData, directed: boolean, weightPropertyName: string, threshold: number) => ClusterData,\n  minimumSpanningTree: (graphData: GraphData, weight?: string, algo?: string) => EdgeConfig[],\n  pageRank: (graphData: GraphData, epsilon?: number, linkProb?: number) => { [key: string]: number},\n  getNeighbors: (nodeId: string, edges?: EdgeConfig[], type?: 'target' | 'source' | undefined) => string[],\n  Stack: any,\n  GADDI: (graphData: GraphData, pattern: GraphData, directed: boolean, k: number, length: number, nodeLabelProp: string, edgeLabelProp: string) => GraphData[],\n  getAdjMatrixAsync: (graphData: GraphData, directed?: boolean) => Matrix[],\n  connectedComponentAsync: (graphData: GraphData, directed?: boolean) => NodeConfig[][],\n  getDegreeAsync: (graphData: GraphData) => DegreeType,\n  getInDegreeAsync: (graphData: GraphData, nodeId: string) => number,\n  getOutDegreeAsync: (graphData: GraphData, nodeId: string) => number,\n  detectCycleAsync: (graphData: GraphData) => { [key: string]: string },\n  detectDirectedCycleAsync: (graphData: GraphData) => { [key: string]: string },\n  detectAllCyclesAsync: (graphData: GraphData, directed?: boolean, nodeIds?: string[], include?: boolean) => any,\n  detectAllDirectedCycleAsync: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  detectAllUndirectedCycleAsync: (graphData: GraphData, nodeIds?: string[], include?: boolean) => any,\n  dijkstraAsync: (graphData: GraphData, source: string, directed?: boolean, weightPropertyName?: string) => { length: object, allPath: object, path: object},\n  findAllPathAsync: (graphData: GraphData, start: string, end: string, directed?: boolean) => any,\n  findShortestPathAsync: (graphData: GraphData, start: string, end: string, directed?: boolean, weightPropertyName?: string) => any,\n  floydWarshallAsync: (graphData: GraphData, directed?: boolean) => Matrix[],\n  labelPropagationAsync: (graphData: GraphData, directed?: boolean, weightPropertyName?: string, maxIteration?: number) => ClusterData,\n  louvainAsync: (graphData: GraphData, directed: boolean, weightPropertyName: string, threshold: number) => ClusterData,\n  minimumSpanningTreeAsync: (graphData: GraphData, weight?: string, algo?: string) => EdgeConfig[],\n  pageRankAsync: (graphData: GraphData, epsilon?: number, linkProb?: number) => { [key: string]: number},\n  getNeighborsAsync: (nodeId: string, edges?: EdgeConfig[], type?: 'target' | 'source' | undefined) => string[],\n  GADDIAsync: (graphData: GraphData, pattern: GraphData, directed: boolean, k: number, length: number, nodeLabelProp: string, edgeLabelProp: string) => GraphData[],\n}", "import { NodeConfig } from '../types';\nimport { secondReg, dateReg } from '../constants/time';\n\n// 获取所有属性并排序\nexport const getAllSortProperties = (nodes: NodeConfig[] = [], n: number = 100) => {\n  const propertyKeyInfo = {};\n  nodes.forEach(node => {\n    if (!node.properties) {\n      return;\n    }\n    Object.keys(node.properties).forEach(propertyKey => {\n      // 目前过滤只保留可以转成数值型的或日期型的, todo: 统一转成one-hot特征向量或者embedding\n      if (propertyKey === 'id' || !`${node.properties[propertyKey]}`.match(secondReg) && \n        !`${node.properties[propertyKey]}`.match(dateReg) && \n        isNaN(Number(node.properties[propertyKey]))) {\n        if (propertyKeyInfo.hasOwnProperty(propertyKey)) {\n            delete propertyKeyInfo[propertyKey];\n        }\n        return;\n      }\n      if (propertyKeyInfo.hasOwnProperty(propertyKey)) {\n        propertyKeyInfo[propertyKey] += 1;\n      } else {\n        propertyKeyInfo[propertyKey] = 1;\n      }\n    })\n  })\n\n  // 取top50的属性\n  const sortKeys = Object.keys(propertyKeyInfo).sort((a,b) => propertyKeyInfo[b] - propertyKeyInfo[a]);\n  return sortKeys.length < n ? sortKeys : sortKeys.slice(0, n);\n}\n  \nconst processProperty = (properties, propertyKeys) => propertyKeys.map(key => {\n  if (properties.hasOwnProperty(key)) {\n    // // 可以转成数值的直接转成数值\n    // if (!isNaN(Number(properties[key]))) {\n    //   return Number(properties[key]);\n    // }\n    // // 时间型的转成时间戳\n    // if (properties[key].match(secondReg) || properties[key].match(dateReg)) {\n    //   // @ts-ignore\n    //   return Number(Date.parse(new Date(properties[key]))) / 1000;\n    // }\n    return properties[key];\n  }\n  return 0;\n})\n  \n// 获取属性特征权重\nexport const getPropertyWeight = (nodes: NodeConfig[]) => {\n  const propertyKeys = getAllSortProperties(nodes);\n  let allPropertiesWeight = [];\n  for (let i = 0; i < nodes.length; i++) {\n    allPropertiesWeight[i] = processProperty(nodes[i].properties, propertyKeys);\n  }\n  return allPropertiesWeight;\n}\n\n// 获取所有节点的属性集合\nexport const getAllProperties = (nodes, key = undefined) => {\n  const allProperties = [];\n  nodes.forEach(node => {\n    if (key === undefined) {\n      allProperties.push(node);\n    }\n    if (node[key] !== undefined) {\n      allProperties.push(node[key]);\n    }\n  })\n  return allProperties;\n}\n\nexport default {\n  getAllSortProperties,\n  getPropertyWeight,\n  getAllProperties\n}\n", "import { uniq } from '@antv/util';\nimport { PlainObject, DistanceType, GraphData, KeyValueMap } from '../types';\nimport Vector from './vector';\n\n/**\n * 获取数据中所有的属性及其对应的值\n * @param dataList 数据集\n * @param involvedKeys 参与计算的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n */\nexport const getAllKeyValueMap = (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => {\n  let keys = [];\n  // 指定了参与计算的keys时，使用指定的keys\n  if (involvedKeys?.length) {\n    keys = involvedKeys;\n  } else {\n    // 未指定抽取的keys时，提取数据中所有的key\n    dataList.forEach(data => {\n      keys = keys.concat(Object.keys(data));\n    })\n    keys = uniq(keys);\n  }\n  // 获取所有值非空的key的value数组\n  const allKeyValueMap: KeyValueMap = {};\n  keys.forEach(key => {\n    let value = [];\n    dataList.forEach(data => {\n      if (data[key] !== undefined && data[key] !== '') {\n        value.push(data[key]);\n      }\n    })\n    if (value.length && !uninvolvedKeys?.includes(key)) {\n      allKeyValueMap[key] = uniq(value);\n    }\n  })\n\n  return allKeyValueMap;\n}\n\n/**\n * one-hot编码：数据特征提取\n * @param dataList 数据集\n * @param involvedKeys 参与计算的的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n */\nexport const oneHot = (dataList: PlainObject[], involvedKeys?: string[], uninvolvedKeys?: string[]) => {\n  // 获取数据中所有的属性/特征及其对应的值\n  const allKeyValueMap = getAllKeyValueMap(dataList, involvedKeys, uninvolvedKeys);\n  const oneHotCode = [];\n  if (!Object.keys(allKeyValueMap).length) {\n    return oneHotCode;\n  }\n\n  // 获取所有的属性/特征值\n  const allValue = Object.values(allKeyValueMap);\n  // 是否所有属性/特征的值都是数值型\n  const isAllNumber = allValue.every(value => value.every(item => (typeof(item) === 'number')));\n\n  // 对数据进行one-hot编码\n  dataList.forEach((data, index) => {\n    let code = [];\n    Object.keys(allKeyValueMap).forEach(key => {\n      const keyValue = data[key];\n      const allKeyValue = allKeyValueMap[key];\n      const valueIndex = allKeyValue.findIndex(value => keyValue === value);\n      let subCode = [];\n      // 如果属性/特征所有的值都能转成数值型，不满足分箱，则直接用值（todo: 为了收敛更快，需做归一化处理）\n      if (isAllNumber) {\n        subCode.push(keyValue);\n      } else {\n        // 进行one-hot编码\n        for(let i = 0; i < allKeyValue.length; i++) {\n          if (i === valueIndex) {\n            subCode.push(1);\n          } else {\n            subCode.push(0);\n          }\n        }\n      }\n      code = code.concat(subCode);\n    })\n    oneHotCode[index] = code;\n  })\n  return oneHotCode;\n}\n\n/**\n * getDistance：获取两个元素之间的距离\n * @param item\n * @param otherItem\n * @param distanceType 距离类型\n * @param graphData 图数据\n */\nexport const getDistance = (item, otherItem, distanceType: DistanceType = DistanceType.EuclideanDistance, graphData?: GraphData) => {\n  let distance = 0;\n  switch (distanceType) {\n    case DistanceType.EuclideanDistance:\n      distance = new Vector(item).euclideanDistance(new Vector(otherItem));\n      break;\n    default:\n      break;\n  }\n  return distance;\n}\n\nexport default {\n  getAllKeyValueMap,\n  oneHot,\n  getDistance,\n}\n", "import { clone } from '@antv/util';\nimport getAdjMatrix from './adjacent-matrix';\nimport { NodeConfig, ClusterData, GraphData, ClusterMap } from './types';\nimport Vector from './utils/vector';\nimport { getAllProperties } from './utils/node-properties';\nimport { oneHot } from './utils/data-preprocessing';\n\nconst getModularity = (\n  nodes: NodeConfig[],\n  adjMatrix: number[][],\n  ks: number[],\n  m: number\n) => {\n  const length = adjMatrix.length;\n  const param = 2 * m;\n  let modularity = 0;\n  for (let i = 0; i < length; i++) {\n    const clusteri = nodes[i].clusterId;\n    for (let j = 0; j < length; j++) {\n      const clusterj = nodes[j].clusterId;\n      if (clusteri !== clusterj) continue;\n      const entry = adjMatrix[i][j] || 0;\n      const ki = ks[i] || 0;\n      const kj = ks[j] || 0;\n      modularity += (entry - ki * kj / param);\n    }\n  }\n  modularity *= (1 / param);\n  return modularity;\n}\n\n// 模块惯性度，衡量属性相似度\nconst getInertialModularity = (\n  nodes: NodeConfig[] = [],\n  allPropertiesWeight: number[][],\n) => {\n  const length = nodes.length;\n  let totalProperties = new Vector([]);\n  for (let i = 0; i < length; i++) {\n    totalProperties = totalProperties.add(new Vector(allPropertiesWeight[i]));\n  }\n  // 均值向量\n  const avgProperties = totalProperties.avg(length);\n\n  avgProperties.normalize();\n  // 节点集合的方差: 节点v与均值向量的平方欧式距离之和\n  let variance: number = 0;\n  for (let i = 0; i < length; i++) {\n    const propertiesi = new Vector(allPropertiesWeight[i]);\n    const squareEuclideanDistance = propertiesi.squareEuclideanDistance(avgProperties);\n    variance += squareEuclideanDistance;\n  }\n\n  // 任意两点间的欧式平方距离\n  let squareEuclideanDistanceInfo = [];\n  nodes.forEach(() => {\n    squareEuclideanDistanceInfo.push([]);\n  });\n  for (let i = 0; i < length; i++) {\n    const propertiesi = new Vector(allPropertiesWeight[i]);\n    nodes[i]['clusterInertial'] = 0;\n    for (let j = 0; j < length; j++) {\n      if ( i === j) {\n        squareEuclideanDistanceInfo[i][j] = 0;\n        continue;\n      }\n      const propertiesj = new Vector(allPropertiesWeight[j]);\n      squareEuclideanDistanceInfo[i][j] = propertiesi.squareEuclideanDistance(propertiesj);\n      nodes[i]['clusterInertial'] += squareEuclideanDistanceInfo[i][j];\n    }\n  }\n\n  // 计算模块惯性度\n  let inertialModularity: number = 0;\n  const param = 2 * length * variance;\n  for (let i = 0; i < length; i++) {\n    const clusteri = nodes[i].clusterId;\n    for (let j = 0; j < length; j++) {\n      const clusterj = nodes[j].clusterId;\n      if ( i === j || clusteri !== clusterj) continue;\n      const inertial = (nodes[i].clusterInertial * nodes[j].clusterInertial) / Math.pow(param, 2) - squareEuclideanDistanceInfo[i][j] / param;\n      inertialModularity += inertial;\n    }\n  }\n  return Number(inertialModularity.toFixed(4));\n}\n\n\n/**\n * 社区发现 louvain 算法\n * @param graphData 图数据\n * @param directed 是否有向图，默认为 false\n * @param weightPropertyName 权重的属性字段\n * @param threshold 差值阈值\n * @param inertialModularity 是否使用惯性模块度（即节点属性相似性）\n * @param propertyKey 属性的字段名\n * @param involvedKeys 参与计算的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n * @param inertialWeight 惯性模块度权重\n */\nconst louvain = (\n  graphData: GraphData,\n  directed: boolean = false,\n  weightPropertyName: string = 'weight',\n  threshold: number = 0.0001,\n  inertialModularity: boolean = false,\n  propertyKey: string = undefined,\n  involvedKeys: string[] = [],\n  uninvolvedKeys: string[] = ['id'],\n  inertialWeight: number = 1,\n): ClusterData => {\n  // the origin data\n  const { nodes = [], edges = [] } = graphData;\n\n  let allPropertiesWeight = [];\n  if (inertialModularity) {\n    nodes.forEach((node, index) => {\n      node.properties = node.properties || {};\n      node.originIndex = index;\n    })\n  \n    let nodeTypeInfo = [];\n    if (nodes.every(node => node.hasOwnProperty('nodeType'))) {\n      nodeTypeInfo = Array.from(new Set(nodes.map(node => node.nodeType)));\n      nodes.forEach(node => {\n        node.properties.nodeType = nodeTypeInfo.findIndex(nodeType => nodeType === node.nodeType);\n      })\n    }\n    // 所有节点属性集合\n    const properties = getAllProperties(nodes, propertyKey);\n    // 所有节点属性one-hot特征向量集合\n    allPropertiesWeight = oneHot(properties, involvedKeys, uninvolvedKeys);\n  }\n \n  let uniqueId = 1;\n\n  const clusters: ClusterMap = {};\n  const nodeMap = {};\n  // init the clusters and nodeMap\n  nodes.forEach((node, i) => {\n    const cid: string = String(uniqueId++);\n    node.clusterId = cid;\n    clusters[cid] = {\n      id: cid,\n      nodes: [node]\n    };\n    nodeMap[node.id] = {\n      node,\n      idx: i\n    };\n  });\n  // the adjacent matrix of calNodes inside clusters\n  const adjMatrix = getAdjMatrix(graphData, directed);\n  // the sum of each row in adjacent matrix\n  const ks = [];\n  /**\n   * neighbor nodes (id for key and weight for value) for each node\n   * neighbors = {\n   *  id(node_id): { id(neighbor_1_id): weight(weight of the edge), id(neighbor_2_id): weight(weight of the edge), ... },\n   *  ...\n   * }\n   */\n  const neighbors = {};\n  // the sum of the weights of all edges in the graph\n  let m = 0;\n  adjMatrix.forEach((row, i) => {\n    let k = 0;\n    const iid = nodes[i].id;\n    neighbors[iid] = {};\n    row.forEach((entry, j) => {\n      if (!entry) return;\n      k += entry;\n      const jid = nodes[j].id;\n      neighbors[iid][jid] = entry;\n      m += entry;\n    });\n    ks.push(k);\n  });\n\n  m /= 2;\n\n  let totalModularity = Infinity;\n  let previousModularity = Infinity;\n  let iter = 0;\n\n  let finalNodes = [];\n  let finalClusters = {};\n  while (true) {\n    if (inertialModularity && nodes.every(node => node.hasOwnProperty('properties'))) {\n      totalModularity = getModularity(nodes, adjMatrix, ks, m) + getInertialModularity(nodes, allPropertiesWeight) * inertialWeight;\n    } else {\n      totalModularity = getModularity(nodes, adjMatrix, ks, m);\n    }\n   \n    // 第一次迭代previousModularity直接赋值\n    if (iter === 0) {\n      previousModularity = totalModularity;\n      finalNodes = nodes;\n      finalClusters = clusters;\n    }\n\n    const increaseWithinThreshold = totalModularity > 0 && totalModularity > previousModularity && totalModularity - previousModularity < threshold;\n    // 总模块度增加才更新最优解\n    if (totalModularity > previousModularity) {\n      finalNodes = nodes.map(node => ({\n        node,\n        clusterId: node.clusterId\n      }));\n      finalClusters = clone(clusters);\n      previousModularity = totalModularity;\n    }\n\n    // whether to terminate the iterations\n    if ( increaseWithinThreshold || iter > 100) {\n      break;\n    };\n    iter++;\n    // pre compute some values for current clusters\n    Object.keys(clusters).forEach(clusterId => {\n      // sum of weights of edges to nodes in cluster\n      let sumTot = 0;\n      edges.forEach(edge => {\n        const { source, target } = edge;\n        const sourceClusterId = nodeMap[source].node.clusterId;\n        const targetClusterId = nodeMap[target].node.clusterId;\n        if ((sourceClusterId === clusterId && targetClusterId !== clusterId)\n          || (targetClusterId === clusterId && sourceClusterId !== clusterId)) {\n          sumTot = sumTot + (edge[weightPropertyName] as number || 1);\n        }\n      });\n      clusters[clusterId].sumTot = sumTot;\n    });\n\n\n    // move the nodes to increase the delta modularity\n    nodes.forEach((node, i) => {\n      const selfCluster = clusters[node.clusterId as string];\n      let bestIncrease = 0;\n      let bestCluster;\n\n      const commonParam = ks[i] / (2 * m);\n\n      // sum of weights of edges from node to nodes in cluster\n      let kiin = 0;\n      const selfClusterNodes = selfCluster.nodes;\n      selfClusterNodes.forEach(scNode => {\n        const scNodeIdx = nodeMap[scNode.id].idx;\n        kiin += adjMatrix[i][scNodeIdx] || 0;\n      });\n      // the modurarity for **removing** the node i from the origin cluster of node i\n      const removeModurarity = kiin - selfCluster.sumTot * commonParam;\n      // nodes for **removing** node i into this neighbor cluster\n      const selfClusterNodesAfterRemove = selfClusterNodes.filter(scNode => scNode.id !== node.id);\n      let propertiesWeightRemove = [];\n      selfClusterNodesAfterRemove.forEach((nodeRemove, index) => {\n        propertiesWeightRemove[index] = allPropertiesWeight[nodeRemove.originIndex];\n      })\n      // the inertialModularity for **removing** the node i from the origin cluster of node i\n      const removeInertialModularity = getInertialModularity(selfClusterNodesAfterRemove, allPropertiesWeight) * inertialWeight;\n\n      // the neightbors of the node\n      const nodeNeighborIds = neighbors[node.id];\n      Object.keys(nodeNeighborIds).forEach(neighborNodeId => {\n        const neighborNode = nodeMap[neighborNodeId].node;\n        const neighborClusterId = neighborNode.clusterId;\n\n        // if the node and the neighbor of node are in the same cluster, reutrn\n        if (neighborClusterId === node.clusterId) return;\n        const neighborCluster = clusters[neighborClusterId];\n        const clusterNodes = neighborCluster.nodes;\n\n        // if the cluster is empty, remove the cluster and return\n        if (!clusterNodes || !clusterNodes.length) return;\n\n        // sum of weights of edges from node to nodes in cluster\n        let neighborClusterKiin = 0;\n        clusterNodes.forEach(cNode => {\n          const cNodeIdx = nodeMap[cNode.id].idx;\n          neighborClusterKiin += adjMatrix[i][cNodeIdx] || 0;\n        });\n\n        // the modurarity for **adding** node i into this neighbor cluster\n        const addModurarity = neighborClusterKiin - neighborCluster.sumTot * commonParam;\n        // nodes for **adding** node i into this neighbor cluster\n        const clusterNodesAfterAdd= clusterNodes.concat([node]);\n        let propertiesWeightAdd = [];\n        clusterNodesAfterAdd.forEach((nodeAdd, index) => {\n          propertiesWeightAdd[index] = allPropertiesWeight[nodeAdd.originIndex];\n        })\n        // the inertialModularity for **adding** node i into this neighbor cluster\n        const addInertialModularity = getInertialModularity(clusterNodesAfterAdd, allPropertiesWeight) * inertialWeight;\n\n        // the increase modurarity is the difference between addModurarity and removeModurarity\n        let increase = addModurarity - removeModurarity;\n        if (inertialModularity) {\n          increase = (addModurarity + addInertialModularity) - (removeModurarity + removeInertialModularity);\n        }\n\n        // find the best cluster to move node i into\n        if (increase > bestIncrease) {\n          bestIncrease = increase;\n          bestCluster = neighborCluster;\n        }\n      });\n\n      // if found a best cluster to move into\n      if (bestIncrease > 0) {\n        bestCluster.nodes.push(node);\n        const previousClusterId = node.clusterId;\n        node.clusterId = bestCluster.id;\n        // move the node to the best cluster\n        const nodeInSelfClusterIdx = selfCluster.nodes.indexOf(node);\n        // remove from origin cluster\n        selfCluster.nodes.splice(nodeInSelfClusterIdx, 1);\n        // update sumTot for clusters\n        // sum of weights of edges to nodes in cluster\n        let neighborClusterSumTot = 0;\n        let selfClusterSumTot = 0;\n        edges.forEach(edge => {\n          const { source, target } = edge;\n          const sourceClusterId = nodeMap[source].node.clusterId;\n          const targetClusterId = nodeMap[target].node.clusterId;\n          if ((sourceClusterId === bestCluster.id && targetClusterId !== bestCluster.id)\n            || (targetClusterId === bestCluster.id && sourceClusterId !== bestCluster.id)) {\n            neighborClusterSumTot = neighborClusterSumTot + (edge[weightPropertyName] as number || 1);\n          }\n          if ((sourceClusterId === previousClusterId && targetClusterId !== previousClusterId)\n            || (targetClusterId === previousClusterId && sourceClusterId !== previousClusterId)) {\n            selfClusterSumTot = selfClusterSumTot + (edge[weightPropertyName] as number || 1);\n          }\n        });\n\n        // the nodes of the clusters to move into and remove are changed, update their sumTot\n        bestCluster.sumTot = neighborClusterSumTot;\n        selfCluster.sumTot = selfClusterSumTot;\n      }\n    });\n  }\n\n  // delete the empty clusters, assign increasing clusterId\n  const newClusterIdMap = {}\n  let clusterIdx = 0;\n  Object.keys(finalClusters).forEach((clusterId) => {\n    const cluster = finalClusters[clusterId];\n    if (!cluster.nodes || !cluster.nodes.length) {\n      delete finalClusters[clusterId];\n      return;\n    }\n    const newId = String(clusterIdx + 1);\n    if (newId === clusterId) {\n      return;\n    }\n    cluster.id = newId;\n    cluster.nodes = cluster.nodes.map(item => ({ id: item.id, clusterId: newId }));\n    finalClusters[newId] = cluster;\n    newClusterIdMap[clusterId] = newId;\n    delete finalClusters[clusterId];\n    clusterIdx ++;\n  });\n  // restore node clusterId\n  finalNodes.forEach(nodeInfo => {\n    const { node, clusterId } = nodeInfo;\n    if (!node) return;\n    node.clusterId = clusterId;\n    if (node.clusterId && newClusterIdMap[node.clusterId]) node.clusterId = newClusterIdMap[node.clusterId]\n  })\n  // get the cluster edges\n  const clusterEdges = [];\n  const clusterEdgeMap = {};\n  edges.forEach(edge => {\n    const { source, target } = edge;\n    const weight = edge[weightPropertyName] || 1;\n    const sourceClusterId = nodeMap[source].node.clusterId;\n    const targetClusterId = nodeMap[target].node.clusterId;\n    if (!sourceClusterId || !targetClusterId) return;\n    const newEdgeId = `${sourceClusterId}---${targetClusterId}`;\n    if (clusterEdgeMap[newEdgeId]) {\n      clusterEdgeMap[newEdgeId].weight += weight;\n      clusterEdgeMap[newEdgeId].count++;\n    } else {\n      const newEdge = {\n        source: sourceClusterId,\n        target: targetClusterId,\n        weight,\n        count: 1\n      };\n      clusterEdgeMap[newEdgeId] = newEdge;\n      clusterEdges.push(newEdge);\n    }\n  });\n  const clustersArray = [];\n  Object.keys(finalClusters).forEach(clusterId => {\n    clustersArray.push(finalClusters[clusterId]);\n  });\n  return {\n    clusters: clustersArray,\n    clusterEdges\n  }\n}\n\nexport default louvain;\n", "import { isEqual, uniq } from '@antv/util';\nimport { getAllProperties } from './utils/node-properties';\nimport { oneHot, getDistance } from './utils/data-preprocessing';\nimport Vector from './utils/vector';\nimport { GraphData, ClusterData, DistanceType } from './types';\n\n// 获取质心\nconst getCentroid = (distanceType, allPropertiesWeight, index) => {\n  let centroid = [];\n  switch (distanceType) {\n    case DistanceType.EuclideanDistance:\n      centroid = allPropertiesWeight[index];\n      break;\n    default:\n      centroid = [];\n      break;\n  }\n  return centroid;\n}\n\n/**\n *  k-means算法 根据节点之间的距离将节点聚类为K个簇\n * @param data 图数据 \n * @param k 质心（聚类中心）个数\n * @param propertyKey 属性的字段名\n * @param involvedKeys 参与计算的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n * @param distanceType 距离类型 默认节点属性的欧式距离\n */\nconst kMeans = (\n  data: GraphData,\n  k: number = 3,\n  propertyKey: string = undefined,\n  involvedKeys: string[] = [],\n  uninvolvedKeys: string[] = ['id'],\n  distanceType: DistanceType = DistanceType.EuclideanDistance,\n) : ClusterData => {\n  const { nodes = [], edges = [] } = data;\n\n  const defaultClusterInfo = {\n    clusters: [\n      {\n        id: \"0\",\n        nodes,\n      }\n    ],\n    clusterEdges: []\n  };\n\n  // 距离类型为欧式距离且没有属性时，直接return\n  if (distanceType === DistanceType.EuclideanDistance && !nodes.every(node => node.hasOwnProperty(propertyKey))){\n    return defaultClusterInfo;\n  }\n\n  // 所有节点属性集合\n  let properties = [];\n  // 所有节点属性one-hot特征向量集合\n  let allPropertiesWeight = [];\n  if (distanceType === DistanceType.EuclideanDistance) {\n    properties = getAllProperties(nodes, propertyKey);\n    allPropertiesWeight = oneHot(properties, involvedKeys, uninvolvedKeys);\n  }\n  if (!allPropertiesWeight.length) {\n    return defaultClusterInfo;\n  }\n  const allPropertiesWeightUniq = uniq(allPropertiesWeight.map(item => item.join('')));\n  // 当输入节点数量或者属性集合的长度小于k时，k调整为其中最小的值\n  const finalK = Math.min(k, nodes.length, allPropertiesWeightUniq.length);\n\n  // 记录节点的原始index，与allPropertiesWeight对应\n  for (let i = 0; i < nodes.length; i++) {\n    nodes[i].originIndex = i;\n  }\n  // 初始化质心（聚类中心）\n  const centroids = [];\n  const centroidIndexList = [];\n  const clusters = [];\n  for (let i = 0; i < finalK; i++) {\n    if (i === 0) {\n      // 随机选取质心（聚类中心）\n      const randomIndex = Math.floor(Math.random() * nodes.length);\n      switch (distanceType) {\n        case DistanceType.EuclideanDistance:\n          centroids[i] = allPropertiesWeight[randomIndex];\n          break;\n        default:\n          centroids[i] = [];\n          break;\n      }\n      centroidIndexList.push(randomIndex);\n      clusters[i] = [nodes[randomIndex]];\n      nodes[randomIndex].clusterId = String(i);\n    } else {\n      let maxDistance = -Infinity;\n      let maxDistanceNodeIndex = 0;\n      // 选取与已有质心平均距离最远的点做为新的质心\n      for (let m = 0; m < nodes.length; m++) {\n        if (!centroidIndexList.includes(m)) {\n          let totalDistance = 0;\n          for (let j = 0; j < centroids.length; j++) {\n            // 求节点到质心的距离（默认节点属性的欧式距离）\n            let distance = 0;\n            switch (distanceType) {\n              case DistanceType.EuclideanDistance:\n                distance = getDistance(allPropertiesWeight[nodes[m].originIndex], centroids[j], distanceType);\n                break;\n              default:\n                break;\n            }\n            totalDistance += distance;\n          }\n          // 节点到各质心的平均距离（默认欧式距离）\n          const avgDistance = totalDistance / centroids.length;\n          // 记录到已有质心最远的的距离和节点索引\n          if (avgDistance > maxDistance && \n            !centroids.find(centroid => isEqual(centroid, getCentroid(distanceType, allPropertiesWeight, nodes[m].originIndex)))) {\n            maxDistance = avgDistance;\n            maxDistanceNodeIndex = m;\n          }\n        }\n      }\n      \n      centroids[i] = getCentroid(distanceType, allPropertiesWeight, maxDistanceNodeIndex);\n      centroidIndexList.push(maxDistanceNodeIndex);\n      clusters[i] = [nodes[maxDistanceNodeIndex]];\n      nodes[maxDistanceNodeIndex].clusterId = String(i);\n    }\n  }\n\n\n  let iterations = 0;\n  while (true) {\n    for (let i = 0; i < nodes.length; i++) {\n      let minDistanceIndex = 0;\n      let minDistance = Infinity;\n      if (!(iterations === 0 && centroidIndexList.includes(i))) {\n        for (let j = 0; j < centroids.length; j++) {\n          // 求节点到质心的距离（默认节点属性的欧式距离）\n          let distance = 0;\n          switch (distanceType) {\n            case DistanceType.EuclideanDistance:\n              distance = getDistance(allPropertiesWeight[i], centroids[j], distanceType);\n              break;\n            default:\n              break;\n          }\n          // 记录节点最近的质心的索引\n          if (distance < minDistance) {\n            minDistance = distance;\n            minDistanceIndex = j;\n          }\n        }\n      \n        // 从原来的类别删除节点\n        if (nodes[i].clusterId !== undefined) {\n          for (let n = clusters[Number(nodes[i].clusterId)].length - 1; n >= 0 ; n--) {\n            if (clusters[Number(nodes[i].clusterId)][n].id === nodes[i].id) {\n              clusters[Number(nodes[i].clusterId)].splice(n, 1);\n            }\n          }\n        }\n        // 将节点划分到距离最小的质心（聚类中心）所对应的类中\n        nodes[i].clusterId = String(minDistanceIndex);\n        clusters[minDistanceIndex].push(nodes[i]);\n      }\n    }\n\n    // 是否存在质心（聚类中心）移动\n    let centroidsEqualAvg = false;\n    for (let i = 0; i < clusters.length; i ++) {\n      const clusterNodes = clusters[i];\n      let totalVector = new Vector([]);\n      for (let j = 0; j < clusterNodes.length; j++) {\n        totalVector = totalVector.add(new Vector(allPropertiesWeight[clusterNodes[j].originIndex]));\n      }\n      // 计算每个类别的均值向量\n      const avgVector = totalVector.avg(clusterNodes.length);\n      // 如果均值向量不等于质心向量\n      if (!avgVector.equal(new Vector(centroids[i]))) {\n        centroidsEqualAvg = true;\n        // 移动/更新每个类别的质心（聚类中心）到该均值向量\n        centroids[i] = avgVector.getArr();\n      }\n    }\n    iterations++;\n    // 如果每个节点都归属了类别，且不存在质心（聚类中心）移动或者迭代次数超过1000，则停止\n    if (nodes.every(node => node.clusterId !== undefined) && centroidsEqualAvg || iterations >= 1000) {\n      break;\n    }\n  }\n\n  // get the cluster edges\n  const clusterEdges = [];\n  const clusterEdgeMap = {};\n  edges.forEach(edge => {\n    const { source, target } = edge;\n    const sourceClusterId = nodes.find(node => node.id === source)?.clusterId;\n    const targetClusterId = nodes.find(node => node.id === target)?.clusterId;\n    const newEdgeId = `${sourceClusterId}---${targetClusterId}`;\n    if (clusterEdgeMap[newEdgeId]) {\n      clusterEdgeMap[newEdgeId].count++;\n    } else {\n      const newEdge = {\n        source: sourceClusterId,\n        target: targetClusterId,\n        count: 1\n      };\n      clusterEdgeMap[newEdgeId] = newEdge;\n      clusterEdges.push(newEdge);\n    }\n  });\n\n  return { clusters, clusterEdges };\n}\n\nexport default kMeans;\n", "import Vector from './utils/vector';\n/**\n * cosine-similarity算法 计算余弦相似度\n * @param item 元素\n * @param targetItem 目标元素\n */\nconst cosineSimilarity = (\n  item: number[],\n  targetItem: number[],\n): number => {\n  // 目标元素向量\n  const targetItemVector = new Vector(targetItem);\n  // 目标元素向量的模长\n  const targetNodeNorm2 = targetItemVector.norm2();\n  // 元素向量\n  const itemVector = new Vector(item);\n  // 元素向量的模长\n  const itemNorm2 = itemVector.norm2();\n  // 计算元素向量和目标元素向量的点积\n  const dot = targetItemVector.dot(itemVector);\n  const norm2Product = targetNodeNorm2 * itemNorm2;\n  // 计算元素向量和目标元素向量的余弦相似度\n  const cosineSimilarity = norm2Product ? dot / norm2Product : 0;\n  return cosineSimilarity;\n}\n\nexport default cosineSimilarity;\n", "/**\n * 并查集 Disjoint set to support quick union\n */\nexport default class UnionFind {\n  count: number;\n\n  parent: {};\n\n  constructor(items: (number | string)[]) {\n    this.count = items.length;\n    this.parent = {};\n    for (const i of items) {\n      this.parent[i] = i;\n    }\n  }\n\n  // find the root of the item\n  find(item) {\n    while (this.parent[item] !== item) {\n      item = this.parent[item];\n    }\n    return item;\n  }\n\n  union(a, b) {\n    const rootA = this.find(a);\n    const rootB = this.find(b);\n\n    if (rootA === rootB) return;\n\n    // make the element with smaller root the parent\n    if (rootA < rootB) {\n      if (this.parent[b] !== b) this.union(this.parent[b], a);\n      this.parent[b] = this.parent[a];\n    } else {\n      if (this.parent[a] !== a) this.union(this.parent[a], b);\n      this.parent[a] = this.parent[b];\n    }\n  }\n\n  // whether a and b are connected, i.e. a and b have the same root\n  connected(a, b) {\n    return this.find(a) === this.find(b);\n  }\n}\n", "const defaultCompare = (a, b) => {\n  return a - b;\n};\n\nexport default class MinBinaryHeap {\n  list: any[];\n\n  compareFn: (a: any, b: any) => number;\n\n  constructor(compareFn = defaultCompare) {\n    this.compareFn = compareFn;\n    this.list = [];\n  }\n\n  getLeft(index) {\n    return 2 * index + 1;\n  }\n\n  getRight(index) {\n    return 2 * index + 2;\n  }\n\n  getParent(index) {\n    if (index === 0) {\n      return null;\n    }\n    return Math.floor((index - 1) / 2);\n  }\n\n  isEmpty() {\n    return this.list.length <= 0;\n  }\n\n  top() {\n    return this.isEmpty() ? undefined : this.list[0];\n  }\n\n  delMin() {\n    const top = this.top();\n    const bottom = this.list.pop();\n    if (this.list.length > 0) {\n      this.list[0] = bottom;\n      this.moveDown(0);\n    }\n    return top;\n  }\n\n  insert(value) {\n    if (value !== null) {\n      this.list.push(value);\n      const index = this.list.length - 1;\n      this.moveUp(index);\n      return true;\n    }\n    return false;\n  }\n\n  moveUp(index) {\n    let parent = this.getParent(index);\n    while (index && index > 0 && this.compareFn(this.list[parent], this.list[index]) > 0) {\n      // swap\n      const tmp = this.list[parent];\n      this.list[parent] = this.list[index];\n      this.list[index] = tmp;\n      // [this.list[index], this.list[parent]] = [this.list[parent], this.list[index]]\n      index = parent;\n      parent = this.getParent(index);\n    }\n  }\n\n  moveDown(index) {\n    let element = index;\n    const left = this.getLeft(index);\n    const right = this.getRight(index);\n    const size = this.list.length;\n    if (left !== null && left < size && this.compareFn(this.list[element], this.list[left]) > 0) {\n      element = left;\n    } else if (\n      right !== null &&\n      right < size &&\n      this.compareFn(this.list[element], this.list[right]) > 0\n    ) {\n      element = right;\n    }\n    if (index !== element) {\n      [this.list[index], this.list[element]] = [this.list[element], this.list[index]];\n      this.moveDown(element);\n    }\n  }\n}\n", "import UnionFind from './structs/union-find';\nimport MinBinaryHeap from './structs/binary-heap';\nimport { GraphData, EdgeConfig } from './types';\nimport { getEdgesByNodeId } from './util';\n\n/**\n * Prim algorithm，use priority queue，复杂度 O(E+V*logV), V: 节点数量，E: 边的数量\n * refer: https://en.wikipedia.org/wiki/Prim%27s_algorithm\n * @param graph\n * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致\n */\nconst primMST = (graphData: GraphData, weight?: string) => {\n  const selectedEdges = [];\n  const { nodes = [], edges = [] } = graphData;\n  if (nodes.length === 0) {\n    return selectedEdges;\n  }\n\n  // 从nodes[0]开始\n  const currNode = nodes[0];\n  const visited = new Set();\n  visited.add(currNode);\n\n  // 用二叉堆维护距已加入节点的其他节点的边的权值\n  const compareWeight = (a: EdgeConfig, b: EdgeConfig) => {\n    if (weight) {\n      return a.weight - b.weight;\n    }\n    return 0;\n\n  };\n  const edgeQueue = new MinBinaryHeap(compareWeight);\n  getEdgesByNodeId(currNode.id, edges).forEach((edge) => {\n    edgeQueue.insert(edge);\n  });\n\n  while (!edgeQueue.isEmpty()) {\n    // 选取与已加入的结点之间边权最小的结点\n    const currEdge: EdgeConfig = edgeQueue.delMin();\n    const source = currEdge.source;\n    const target = currEdge.target;\n    if (visited.has(source) && visited.has(target)) continue;\n    selectedEdges.push(currEdge);\n\n    if (!visited.has(source)) {\n      visited.add(source);\n      getEdgesByNodeId(source, edges).forEach((edge) => {\n        edgeQueue.insert(edge);\n      });\n    }\n    if (!visited.has(target)) {\n      visited.add(target);\n      getEdgesByNodeId(target, edges).forEach((edge) => {\n        edgeQueue.insert(edge);\n      });\n    }\n  }\n  return selectedEdges;\n};\n\n/**\n * Kruskal algorithm，复杂度 O(E*logE), E: 边的数量\n * refer: https://en.wikipedia.org/wiki/Kruskal%27s_algorithm\n * @param graph\n * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致\n * @return IEdge[] 返回构成MST的边的数组\n */\nconst kruskalMST = (graphData: GraphData, weight?: string): EdgeConfig[] => {\n  const selectedEdges = [];\n  const { nodes = [], edges = [] } = graphData\n  if (nodes.length === 0) {\n    return selectedEdges;\n  }\n\n  // 若指定weight，则将所有的边按权值从小到大排序\n  const weightEdges = edges.map((edge) => edge);\n  if (weight) {\n    weightEdges.sort((a, b) => {\n      return a.weight - b.weight;\n    });\n  }\n  const disjointSet = new UnionFind(nodes.map((n) => n.id));\n\n  // 从权值最小的边开始，如果这条边连接的两个节点于图G中不在同一个连通分量中，则添加这条边\n  // 直到遍历完所有点或边\n  while (weightEdges.length > 0) {\n    const curEdge = weightEdges.shift();\n    const source = curEdge.source;\n    const target = curEdge.target;\n    if (!disjointSet.connected(source, target)) {\n      selectedEdges.push(curEdge);\n      disjointSet.union(source, target);\n    }\n  }\n  return selectedEdges;\n};\n\n/**\n * 最小生成树\n * refer: https://en.wikipedia.org/wiki/Kruskal%27s_algorithm\n * @param graph\n * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致\n * @param algo 'prim' | 'kruskal' 算法类型\n * @return EdgeConfig[] 返回构成MST的边的数组\n */\nconst minimumSpanningTree = (graphData: GraphData, weight?: string, algo?: string): EdgeConfig[] => {\n  const algos = {\n    prim: primMST,\n    kruskal: kruskalMST,\n  };\n  if (!algo) return kruskalMST(graphData, weight);\n\n  return algos[algo](graphData, weight);\n}\n\nexport default minimumSpanningTree\n", "import { indexOf } from \"@antv/util\";\n\nexport const VACANT_EDGE_ID = -1;\nexport const VACANT_NODE_ID = -1;\nexport const VACANT_EDGE_LABEL = \"-1\";\nexport const VACANT_NODE_LABEL = \"-1\";\nexport const VACANT_GRAPH_ID = -1;\nexport const AUTO_EDGE_ID = \"-1\";\n\nexport class Edge {\n  public id: number;\n  public from: number;\n  public to: number;\n  public label: string;\n\n  constructor(\n    id = VACANT_EDGE_ID,\n    from = VACANT_NODE_ID,\n    to = VACANT_NODE_ID,\n    label = VACANT_EDGE_LABEL\n  ) {\n    this.id = id;\n    this.from = from;\n    this.to = to;\n    this.label = label;\n  }\n}\n\nexport class Node {\n  public id: number;\n  public from: number;\n  public to: number;\n  public label: string;\n  public edges: Edge[];\n  public edgeMap: {};\n\n  constructor(id = VACANT_NODE_ID, label = VACANT_NODE_LABEL) {\n    this.id = id;\n    this.label = label;\n    this.edges = [];\n    this.edgeMap = {};\n  }\n\n  addEdge(edge) {\n    this.edges.push(edge);\n    this.edgeMap[edge.id] = edge;\n  }\n}\n\nexport class Graph {\n  public id: number;\n  public from: number;\n  public to: number;\n  public label: string;\n  public edgeIdAutoIncrease: boolean;\n  public nodes: Node[];\n  public edges: Edge[];\n  public nodeMap: {};\n  public edgeMap: {};\n  public nodeLabelMap: {}; // key 是 label，value 是节点 id 的数组\n  public edgeLabelMap: {};\n  private counter: number; // 自增用于自动生成边 id\n  public directed: boolean;\n\n  constructor(\n    id = VACANT_NODE_ID,\n    edgeIdAutoIncrease = true,\n    directed = false\n  ) {\n    this.id = id;\n    this.edgeIdAutoIncrease = edgeIdAutoIncrease;\n    this.edges = [];\n    this.nodes = [];\n    this.nodeMap = {};\n    this.edgeMap = {};\n    this.nodeLabelMap = {};\n    this.edgeLabelMap = {};\n    this.counter = 0;\n    this.directed = directed;\n  }\n\n  getNodeNum() {\n    return this.nodes.length;\n  }\n\n  addNode(id: number, label: string) {\n    if (this.nodeMap[id]) return;\n    const node = new Node(id, label);\n    this.nodes.push(node);\n    this.nodeMap[id] = node;\n    if (!this.nodeLabelMap[label]) this.nodeLabelMap[label] = [];\n    this.nodeLabelMap[label].push(id);\n  }\n\n  addEdge(id: number, from: number, to: number, label: string) {\n    if (this.edgeIdAutoIncrease || id === undefined) id = this.counter++;\n    if (this.nodeMap[from] && this.nodeMap[to] && this.nodeMap[to].edgeMap[id])\n      return;\n    const edge = new Edge(id, from, to, label);\n    this.edges.push(edge);\n    this.edgeMap[id] = edge;\n\n    this.nodeMap[from].addEdge(edge);\n\n    if (!this.edgeLabelMap[label]) this.edgeLabelMap[label] = [];\n    this.edgeLabelMap[label].push(edge);\n\n    if (!this.directed) {\n      const rEdge = new Edge(id, to, from, label);\n      this.nodeMap[to].addEdge(rEdge);\n      this.edgeLabelMap[label].push(rEdge);\n    }\n  }\n}\n", "import { GraphData } from \"../types\";\nimport { clone } from \"@antv/util\";\nimport {\n  Graph,\n  Edge,\n  VACANT_NODE_LABEL,\n  VACANT_GRAPH_ID,\n  Node,\n  VACANT_EDGE_LABEL,\n} from \"./struct\";\n\nexport interface EdgeMap {\n  [key: string]: {\n    // key 的格式为 source-target\n    idx: number; // 该边在原图 graphData.edges 的序号\n    edge: any;\n  };\n}\n\nexport interface NodeMap {\n  [key: string]: {\n    // key 格式为 node.id\n    idx: number; // 该j客店在原图 graphData.nodes 的序号\n    node: any;\n    degree: number;\n    inDegree: number;\n    outDegree: number;\n  };\n}\n\ninterface PDFS {\n  graphId: number;\n  edge: any;\n  preNode: any;\n}\n\nclass DFSedge {\n  public fromNode: number;\n  public toNode: number;\n  public nodeEdgeNodeLabel: {\n    nodeLabel1: string;\n    edgeLabel: string;\n    nodeLabel2: string;\n  };\n\n  constructor(\n    fromNode: number,\n    toNode: number,\n    fromNodeLabel: string,\n    edgeLabel: string,\n    toNodeLabel: string\n  ) {\n    this.fromNode = fromNode;\n    this.toNode = toNode;\n    this.nodeEdgeNodeLabel = {\n      nodeLabel1: fromNodeLabel || VACANT_NODE_LABEL,\n      edgeLabel: edgeLabel || VACANT_EDGE_LABEL,\n      nodeLabel2: toNodeLabel || VACANT_NODE_LABEL,\n    };\n  }\n\n  equalTo(other) {\n    return (\n      this.fromNode === other.formNode &&\n      this.toNode === other.toNode &&\n      this.nodeEdgeNodeLabel === other.nodeEdgeNodeLabel\n    );\n  }\n\n  notEqualTo(other) {\n    return !this.equalTo(other);\n  }\n}\n\n// DFScode 是 DESedge 的数组\nclass DFScode {\n  public dfsEdgeList: DFSedge[];\n  public rmpath: any;\n\n  constructor() {\n    this.rmpath = [];\n    this.dfsEdgeList = [];\n  }\n\n  equalTo(other) {\n    const aLength = this.dfsEdgeList.length;\n    const bLength = other.length;\n    if (aLength !== bLength) return false;\n    for (let i = 0; i < aLength; i++) {\n      if (this.dfsEdgeList[i] !== other[i]) return false;\n    }\n    return true;\n  }\n\n  notEqualTo(other) {\n    return !this.equalTo(other);\n  }\n\n  /** 增加一条 edge 到 DFScode */\n  pushBack(fromNode, toNode, fromNodeLabel, edgeLabel, toNodeLabel) {\n    this.dfsEdgeList.push(\n      new DFSedge(fromNode, toNode, fromNodeLabel, edgeLabel, toNodeLabel)\n    );\n    return this.dfsEdgeList;\n  }\n\n  /** 根据 dfs 构建图 */\n  toGraph(graphId: number = VACANT_GRAPH_ID, directed = false) {\n    const graph = new Graph(graphId, true, directed);\n    this.dfsEdgeList.forEach((dfsEdge) => {\n      const fromNodeId = dfsEdge.fromNode;\n      const toNodeId = dfsEdge.toNode;\n      const { nodeLabel1, edgeLabel, nodeLabel2 } = dfsEdge.nodeEdgeNodeLabel;\n\n      if (nodeLabel1 !== VACANT_NODE_LABEL) graph.addNode(fromNodeId, nodeLabel1);\n      if (nodeLabel2 !== VACANT_NODE_LABEL) graph.addNode(toNodeId, nodeLabel2);\n      if (nodeLabel1 !== VACANT_NODE_LABEL && nodeLabel2 !== nodeLabel1)  graph.addEdge(undefined, fromNodeId, toNodeId, edgeLabel);\n    });\n    return graph;\n  }\n\n  // 建立 rightmost path\n  buildRmpath() {\n    this.rmpath = [];\n    let oldFrom = undefined;\n    const selfLength = this.dfsEdgeList.length;\n    for (let i = selfLength - 1; i >= 0; i--) {\n      const dfsEdge = this.dfsEdgeList[i];\n      const fromNodeIdx = dfsEdge.fromNode;\n      const toNodeIdx = dfsEdge.toNode;\n      if (\n        fromNodeIdx < toNodeIdx &&\n        (oldFrom === undefined || toNodeIdx === oldFrom)\n      ) {\n        this.rmpath.push(i);\n        oldFrom = fromNodeIdx;\n      }\n    }\n    return this.rmpath;\n  }\n\n  getNodeNum() {\n    const nodeMap = {};\n    this.dfsEdgeList.forEach((dfsEdge) => {\n      if (!nodeMap[dfsEdge.fromNode]) nodeMap[dfsEdge.fromNode] = true;\n      if (!nodeMap[dfsEdge.toNode]) nodeMap[dfsEdge.toNode] = true;\n    });\n    return Object.keys(nodeMap).length;\n  }\n}\n\nclass History {\n  public his: object;\n  public edges: Edge[];\n  public nodesUsed: object;\n  public edgesUsed: object;\n\n  constructor(pdfs: PDFS) {\n    this.his = {};\n    this.nodesUsed = {};\n    this.edgesUsed = {};\n    this.edges = [];\n    if (!pdfs) return;\n    while (pdfs) {\n      const e = pdfs.edge;\n      this.edges.push(e);\n      this.nodesUsed[e.from] = 1;\n      this.nodesUsed[e.to] = 1;\n      this.edgesUsed[e.id] = 1;\n      pdfs = pdfs.preNode;\n    }\n    // 倒序\n    this.edges = this.edges.reverse();\n  }\n\n  hasNode(node: Node) {\n    return this.nodesUsed[node.id] === 1;\n  }\n\n  hasEdge(edge: Edge) {\n    return this.edgesUsed[edge.id] === 1;\n  }\n}\n\ninterface Root {\n  [key: string]: {\n    projected: PDFS[];\n    nodeLabel1?: string;\n    edgeLabel?: string;\n    nodeLabel2?: string;\n    fromNodeId?: number;\n    toNodeId?: number;\n  };\n}\n\ninterface GraphDataMap {\n  [key: string]: GraphData;\n}\ninterface GraphMap {\n  [key: number]: Graph;\n}\n\ninterface AlgorithmProps {\n  graphs: GraphMap; // 图数据\n  minSupport: number; // 算法参数，最小支持数量，根据 graphs 内图的数量指定\n  directed?: boolean; // 是否有向图，默认为 false\n  minNodeNum?: number; // 每个子图中边的最少个数，默认为 1\n  maxNodeNum?: number; // 每个子图中边的最多个数，默认为 4\n  top?: number; // 返回前 top 个频繁子图，默认为 10\n  verbose?: boolean;\n}\n\nclass GSpan {\n  public graphs: GraphMap;\n  public dfsCode: DFScode;\n  public support: number;\n  public frequentSize1Subgraphs: GraphData[];\n  public frequentSubgraphs: Graph[];\n  public reportDF: [];\n  public maxNodeNum: number;\n  public minNodeNum: number;\n  public minSupport: number;\n  public top: number;\n  public directed: boolean;\n  private counter: number; // 用于生成图的 id，自增\n  public verbose: boolean;\n\n  constructor({\n    graphs,\n    minSupport = 2,\n    minNodeNum = 1,\n    maxNodeNum = 4,\n    top = 10,\n    directed = false,\n    verbose = false,\n  }: AlgorithmProps) {\n    // -------- 第零步，初始化-------\n    this.graphs = graphs;\n    this.dfsCode = new DFScode();\n    this.support = 0;\n    this.frequentSize1Subgraphs = [];\n    this.frequentSubgraphs = [];\n    this.minSupport = minSupport;\n    this.top = top;\n    this.directed = directed;\n    this.counter = 0;\n    // TODO? timestamp = {}\n    this.maxNodeNum = maxNodeNum;\n    this.minNodeNum = minNodeNum;\n    this.verbose = verbose;\n    if (this.maxNodeNum < this.minNodeNum) this.maxNodeNum = this.minNodeNum;\n    this.reportDF = []; // matrix\n  }\n\n  // Line 352\n  findForwardRootEdges(graph: Graph, fromNode: Node): Edge[] {\n    const result = [];\n    const nodeMap = graph.nodeMap;\n    fromNode.edges.forEach((edge) => {\n      if (this.directed || fromNode.label <= nodeMap[edge.to].label)\n        result.push(edge);\n    });\n\n    return result;\n  }\n\n  findBackwardEdge(\n    graph: Graph,\n    edge1: Edge,\n    edge2: Edge,\n    history: History\n  ): Edge {\n    if (!this.directed && edge1 === edge2) return null;\n    const nodeMap = graph.nodeMap;\n    const edge2To = nodeMap[edge2.to];\n    const edge2ToEdges = edge2To.edges;\n    const edgeLength = edge2ToEdges.length;\n    for (let i = 0; i < edgeLength; i++) {\n      const edge = edge2ToEdges[i];\n      if (history.hasEdge(edge) || edge.to !== edge1.from) continue;\n      if (!this.directed) {\n        if (\n          edge1.label < edge.label ||\n          (edge1.label === edge.label &&\n            nodeMap[edge1.to].label <= nodeMap[edge2.to].label)\n        ) {\n          return edge;\n        }\n      } else {\n        if (\n          nodeMap[edge1.from].label < nodeMap[edge2.to].label ||\n          (nodeMap[edge1.from].label === nodeMap[edge2.to].label &&\n            edge1.label <= edge.label)\n        ) {\n          return edge;\n        }\n      }\n    }\n    return null;\n  }\n\n  findForwardPureEdges(\n    graph,\n    rightmostEdge,\n    minNodeLabel,\n    history: History\n  ): Edge[] {\n    const result = [];\n    const rightmostEdgeToId = rightmostEdge.to;\n    const edges = graph.nodeMap[rightmostEdgeToId].edges;\n    const edgeLength = edges.length;\n    for (let i = 0; i < edgeLength; i++) {\n      const edge = edges[i];\n      const toNode = graph.nodeMap[edge.to];\n      if (minNodeLabel <= toNode.label && !history.hasNode(toNode)) {\n        result.push(edge);\n      }\n    }\n    return result;\n  }\n\n  findForwardRmpathEdges(\n    graph: Graph,\n    rightmostEdge: Edge,\n    minNodeLabel: string,\n    history: History\n  ): Edge[] {\n    const result = [];\n    const nodeMap = graph.nodeMap;\n    const toNodeLabel = nodeMap[rightmostEdge.to].label;\n    const fromNode = nodeMap[rightmostEdge.from];\n    const edges = fromNode.edges;\n    const edgeLength = edges.length;\n    for (let i = 0; i < edgeLength; i++) {\n      const edge = edges[i];\n      const newToNodeLabel = nodeMap[edge.to].label;\n      if (\n        rightmostEdge.to === edge.to ||\n        minNodeLabel > newToNodeLabel ||\n        history.hasNode(nodeMap[edge.to])\n      ) {\n        continue;\n      }\n      if (\n        rightmostEdge.label < edge.label ||\n        (rightmostEdge.label === edge.label && toNodeLabel <= newToNodeLabel)\n      ) {\n        result.push(edge);\n      }\n    }\n    return result;\n  }\n\n  getSupport(projected: PDFS[]): number {\n    const graphMap = {};\n    projected.forEach((pro) => {\n      if (!graphMap[pro.graphId]) graphMap[pro.graphId] = true;\n    });\n    return Object.keys(graphMap).length;\n  }\n\n  findMinLabel(\n    obj: Root\n  ): {\n    nodeLabel1?: string;\n    edgeLabel: string;\n    nodeLabel2?: string;\n  } {\n    let minLabel = undefined;\n    Object.keys(obj).forEach((nodeEdgeNodeLabel) => {\n      const { nodeLabel1, edgeLabel, nodeLabel2 } = obj[nodeEdgeNodeLabel];\n      if (!minLabel) {\n        minLabel = {\n          nodeLabel1,\n          edgeLabel,\n          nodeLabel2,\n        };\n        return;\n      }\n      if (\n        nodeLabel1 < minLabel.nodeLabel1 ||\n        (nodeLabel1 === minLabel.nodeLabel1 &&\n          edgeLabel < minLabel.edgeLabel) ||\n        (nodeLabel1 === minLabel.nodeLabel1 &&\n          edgeLabel === minLabel.edgeLabel &&\n          nodeLabel2 < minLabel.nodeLabel2)\n      ) {\n        minLabel = {\n          nodeLabel1,\n          edgeLabel,\n          nodeLabel2,\n        };\n      }\n    });\n    return minLabel;\n  }\n\n  isMin() {\n    const dfsCode = this.dfsCode;\n    if (this.verbose) console.log(\"isMin checking\", dfsCode);\n    if (dfsCode.dfsEdgeList.length === 1) return true;\n    const directed = this.directed;\n    const graph = dfsCode.toGraph(VACANT_GRAPH_ID, directed);\n    const nodeMap = graph.nodeMap;\n    const dfsCodeMin = new DFScode();\n    const root: Root = {};\n    graph.nodes.forEach((node) => {\n      const forwardEdges = this.findForwardRootEdges(graph, node);\n      forwardEdges.forEach((edge) => {\n        let otherNode = nodeMap[edge.to];\n        const nodeEdgeNodeLabel = `${node.label}-${edge.label}-${otherNode.label}`;\n        if (!root[nodeEdgeNodeLabel])\n          root[nodeEdgeNodeLabel] = {\n            projected: [],\n            nodeLabel1: node.label,\n            edgeLabel: edge.label,\n            nodeLabel2: otherNode.label,\n          };\n        const pdfs: PDFS = {\n          graphId: graph.id,\n          edge,\n          preNode: null,\n        };\n        root[nodeEdgeNodeLabel].projected.push(pdfs);\n      });\n    });\n\n    // 比较 root 中每一项的 nodeEdgeNodeLabel 大小，按照 nodeLabel1、edgeLabe、nodeLabel2 的顺序比较\n    let minLabel = this.findMinLabel(root); // line 419\n    if (!minLabel) return;\n    dfsCodeMin.dfsEdgeList.push(\n      new DFSedge(\n        0,\n        1,\n        minLabel.nodeLabel1,\n        minLabel.edgeLabel,\n        minLabel.nodeLabel2\n      )\n    );\n\n    // line 423\n    const projectIsMin = (projected: PDFS[]) => {\n      // right most path\n      const rmpath = dfsCodeMin.buildRmpath();\n      const minNodeLabel =\n        dfsCodeMin.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1;\n      const maxToC = dfsCodeMin.dfsEdgeList[rmpath[0]].toNode; // node id\n\n      const backwardRoot: Root = {};\n      let flag = false,\n        newTo = 0;\n      let end = directed ? -1 : 0; // 遍历到 1 还是到 0\n      for (let i = rmpath.length - 1; i > end; i--) {\n        if (flag) break;\n        // line 435\n        projected.forEach((p) => {\n          const history = new History(p);\n          const backwardEdge = this.findBackwardEdge(\n            graph,\n            history.edges[rmpath[i]],\n            history.edges[rmpath[0]],\n            history\n          );\n          if (backwardEdge) {\n            // Line 441\n            if (!backwardRoot[backwardEdge.label]) {\n              backwardRoot[backwardEdge.label] = {\n                projected: [],\n                edgeLabel: backwardEdge.label,\n              };\n            }\n            backwardRoot[backwardEdge.label].projected.push({\n              graphId: graph.id,\n              edge: backwardRoot,\n              preNode: p,\n            });\n            newTo = dfsCodeMin.dfsEdgeList[rmpath[i]].fromNode;\n            flag = true;\n          }\n        });\n      }\n\n      if (flag) {\n        const minBackwardEdgeLabel = this.findMinLabel(backwardRoot);\n        dfsCodeMin.dfsEdgeList.push(\n          new DFSedge(\n            maxToC,\n            newTo,\n            VACANT_NODE_LABEL,\n            minBackwardEdgeLabel.edgeLabel,\n            VACANT_NODE_LABEL\n          )\n        );\n        const idx = dfsCodeMin.dfsEdgeList.length - 1;\n        if (this.dfsCode.dfsEdgeList[idx] !== dfsCodeMin.dfsEdgeList[idx])\n          return false;\n        return projectIsMin(\n          backwardRoot[minBackwardEdgeLabel.edgeLabel].projected\n        );\n      }\n      const forwardRoot: Root = {};\n      flag = false;\n      let newFrom = 0;\n      projected.forEach((p) => {\n        const history = new History(p);\n        const forwardPureEdges = this.findForwardPureEdges(\n          graph,\n          history.edges[rmpath[0]],\n          minNodeLabel,\n          history\n        );\n        if (forwardPureEdges.length > 0) {\n          flag = true;\n          newFrom = maxToC;\n          forwardPureEdges.forEach((edge) => {\n            const key = `${edge.label}-${nodeMap[edge.to].label}`;\n            if (!forwardRoot[key])\n              forwardRoot[key] = {\n                projected: [],\n                edgeLabel: edge.label,\n                nodeLabel2: nodeMap[edge.to].label,\n              };\n            forwardRoot[key].projected.push({\n              graphId: graph.id,\n              edge,\n              preNode: p,\n            });\n          });\n        }\n      });\n\n      const pathLength = rmpath.length;\n      for (let i = 0; i < pathLength; i++) {\n        if (flag) break;\n        const value = rmpath[i];\n        projected.forEach((p) => {\n          const history = new History(p);\n          const forwardRmpathEdges = this.findForwardRmpathEdges(\n            graph,\n            history.edges[value],\n            minNodeLabel,\n            history\n          );\n          if (forwardRmpathEdges.length > 0) {\n            flag = true;\n            newFrom = dfsCodeMin.dfsEdgeList[value].fromNode;\n            forwardRmpathEdges.forEach((edge) => {\n              const key = `${edge.label}-${nodeMap[edge.to].label}`;\n              if (!forwardRoot[key])\n                forwardRoot[key] = {\n                  projected: [],\n                  edgeLabel: edge.label,\n                  nodeLabel2: nodeMap[edge.to].label,\n                };\n              forwardRoot[key].projected.push({\n                graphId: graph.id,\n                edge,\n                preNode: p,\n              });\n            });\n          }\n        });\n      }\n\n      if (!flag) return true;\n\n      const forwardMinEdgeNodeLabel = this.findMinLabel(forwardRoot);\n      dfsCodeMin.dfsEdgeList.push(\n        new DFSedge(\n          newFrom,\n          maxToC + 1,\n          VACANT_NODE_LABEL,\n          forwardMinEdgeNodeLabel.edgeLabel,\n          forwardMinEdgeNodeLabel.nodeLabel2\n        )\n      );\n      const idx = dfsCodeMin.dfsEdgeList.length - 1;\n      if (dfsCode.dfsEdgeList[idx] !== dfsCodeMin.dfsEdgeList[idx])\n        return false;\n      return projectIsMin(\n        forwardRoot[\n          `${forwardMinEdgeNodeLabel.edgeLabel}-${forwardMinEdgeNodeLabel.nodeLabel2}`\n        ].projected\n      );\n    };\n    const key = `${minLabel.nodeLabel1}-${minLabel.edgeLabel}-${minLabel.nodeLabel2}`;\n    return projectIsMin(root[key].projected);\n  }\n\n  report() {\n    if (this.dfsCode.getNodeNum() < this.minNodeNum) return;\n    this.counter++;\n    const graph = this.dfsCode.toGraph(this.counter, this.directed);\n    this.frequentSubgraphs.push(clone(graph));\n  }\n\n  subGraphMining(projected) {\n    const support = this.getSupport(projected);\n    if (support < this.minSupport) return;\n    if (!this.isMin()) return;\n    this.report();\n\n    const nodeNum = this.dfsCode.getNodeNum();\n    const rmpath = this.dfsCode.buildRmpath();\n    const maxToC = this.dfsCode.dfsEdgeList[rmpath[0]].toNode;\n    const minNodeLabel = this.dfsCode.dfsEdgeList[0].nodeEdgeNodeLabel\n      .nodeLabel1;\n\n    const forwardRoot: Root = {};\n    const backwardRoot: Root = {};\n\n    projected.forEach((p) => {\n      const graph = this.graphs[p.graphId];\n      const nodeMap = graph.nodeMap;\n      const history = new History(p);\n      // backward Line 526\n      for (let i = rmpath.length - 1; i >= 0; i--) {\n        const backwardEdge = this.findBackwardEdge(\n          graph,\n          history.edges[rmpath[i]],\n          history.edges[rmpath[0]],\n          history\n        );\n        if (backwardEdge) {\n          const key = `${this.dfsCode.dfsEdgeList[rmpath[i]].fromNode}-${\n            backwardEdge.label\n          }`;\n          if (!backwardRoot[key])\n            backwardRoot[key] = {\n              projected: [],\n              toNodeId: this.dfsCode.dfsEdgeList[rmpath[i]].fromNode,\n              edgeLabel: backwardEdge.label,\n            };\n          backwardRoot[key].projected.push({\n            graphId: p.graphId,\n            edge: backwardEdge,\n            preNode: p,\n          });\n        }\n      }\n\n      // pure forward\n      if (nodeNum >= this.maxNodeNum) return;\n      const forwardPureEdges = this.findForwardPureEdges(\n        graph,\n        history.edges[rmpath[0]],\n        minNodeLabel,\n        history\n      );\n      forwardPureEdges.forEach((edge) => {\n        const key = `${maxToC}-${edge.label}-${nodeMap[edge.to].label}`;\n        if (!forwardRoot[key])\n          forwardRoot[key] = {\n            projected: [],\n            fromNodeId: maxToC,\n            edgeLabel: edge.label,\n            nodeLabel2: nodeMap[edge.to].label,\n          };\n        forwardRoot[key].projected.push({\n          graphId: p.graphId,\n          edge,\n          preNode: p,\n        });\n      });\n\n      // rmpath forward\n      for (let i = 0; i < rmpath.length; i++) {\n        const forwardRmpathEdges = this.findForwardRmpathEdges(\n          graph,\n          history.edges[rmpath[i]],\n          minNodeLabel,\n          history\n        );\n        forwardRmpathEdges.forEach((edge) => {\n          const key = `${this.dfsCode.dfsEdgeList[rmpath[i]].fromNode}-${\n            edge.label\n          }-${nodeMap[edge.to].label}`;\n          if (!forwardRoot[key])\n            forwardRoot[key] = {\n              projected: [],\n              fromNodeId: this.dfsCode.dfsEdgeList[rmpath[i]].fromNode,\n              edgeLabel: edge.label,\n              nodeLabel2: nodeMap[edge.to].label,\n            };\n          forwardRoot[key].projected.push({\n            graphId: p.graphId,\n            edge,\n            preNode: p,\n          });\n        });\n      }\n    });\n\n    // backward\n    Object.keys(backwardRoot).forEach((key) => {\n      const { toNodeId, edgeLabel } = backwardRoot[key];\n      this.dfsCode.dfsEdgeList.push(\n        new DFSedge(maxToC, toNodeId, \"-1\", edgeLabel, \"-1\")\n      );\n      this.subGraphMining(backwardRoot[key].projected);\n      this.dfsCode.dfsEdgeList.pop();\n    });\n\n    // forward\n    Object.keys(forwardRoot).forEach((key) => {\n      const { fromNodeId, edgeLabel, nodeLabel2 } = forwardRoot[key];\n      this.dfsCode.dfsEdgeList.push(\n        new DFSedge(\n          fromNodeId,\n          maxToC + 1,\n          VACANT_NODE_LABEL,\n          edgeLabel,\n          nodeLabel2\n        )\n      );\n      this.subGraphMining(forwardRoot[key].projected);\n      this.dfsCode.dfsEdgeList.pop();\n    });\n  }\n\n  generate1EdgeFrequentSubGraphs() {\n    const graphs = this.graphs;\n    const directed = this.directed;\n    const minSupport = this.minSupport;\n    const frequentSize1Subgraphs = this.frequentSize1Subgraphs;\n    let nodeLabelCounter = {},\n      nodeEdgeNodeCounter = {};\n    // 保存各个图和各自节点的关系 map，key 格式为 graphKey-node类型\n    const nodeLableCounted = {};\n    // 保存各个图和各自边的关系 map，key 格式为 graphKey-fromNode类型-edge类型-toNode类型\n    const nodeEdgeNodeLabelCounted = {};\n    Object.keys(graphs).forEach((key) => {\n      // Line 271\n      const graph = graphs[key];\n      const nodeMap = graph.nodeMap;\n      // 遍历节点，记录对应图 与 每个节点的 label 到 nodeLableCounted\n      graph.nodes.forEach((node, i) => {\n        // Line 272\n        const nodeLabel = node.label;\n        const graphNodeKey = `${key}-${nodeLabel}`;\n        if (!nodeLableCounted[graphNodeKey]) {\n          let counter = nodeLabelCounter[nodeLabel] || 0;\n          counter++;\n          nodeLabelCounter[nodeLabel] = counter;\n        }\n        nodeLableCounted[graphNodeKey] = {\n          graphKey: key,\n          label: nodeLabel,\n        };\n        // 遍历该节点的所有边，记录各个图和各自边的关系到 nodeEdgeNodeLabelCounted. Line 276\n        node.edges.forEach((edge) => {\n          let nodeLabel1 = nodeLabel;\n          let nodeLabel2 = nodeMap[edge.to].label;\n          if (!directed && nodeLabel1 > nodeLabel2) {\n            const tmp = nodeLabel2;\n            nodeLabel2 = nodeLabel1;\n            nodeLabel1 = tmp;\n          }\n          const edgeLabel = edge.label;\n\n          const graphNodeEdgeNodeKey = `${key}-${nodeLabel1}-${edgeLabel}-${nodeLabel2}`;\n          const nodeEdgeNodeKey = `${nodeLabel1}-${edgeLabel}-${nodeLabel2}`;\n\n          if (!nodeEdgeNodeCounter[nodeEdgeNodeKey]) {\n            let counter = nodeEdgeNodeCounter[nodeEdgeNodeKey] || 0;\n            counter++;\n            nodeEdgeNodeCounter[nodeEdgeNodeKey] = counter; // Line281\n          }\n          nodeEdgeNodeLabelCounted[graphNodeEdgeNodeKey] = {\n            graphId: key,\n            nodeLabel1,\n            edgeLabel,\n            nodeLabel2,\n          };\n        });\n      });\n    });\n\n    // 计算频繁的节点\n    Object.keys(nodeLabelCounter).forEach((label) => {\n      const count = nodeLabelCounter[label];\n      if (count < minSupport) return;\n      const g = { nodes: [], edges: [] };\n      g.nodes.push({\n        id: \"0\",\n        label,\n      });\n      frequentSize1Subgraphs.push(g);\n      // if (minNodeNum <= 1) reportSize1 TODO\n    });\n\n    return frequentSize1Subgraphs;\n  }\n\n  run() {\n    // -------- 第一步, _generate_1edge_frequent_subgraphs：频繁的单个节点-------\n    this.frequentSize1Subgraphs = this.generate1EdgeFrequentSubGraphs();\n\n    if (this.maxNodeNum < 2) return;\n\n    const graphs = this.graphs;\n    const directed = this.directed;\n\n    // PDFS 数组的 map Line 304\n    const root: Root = {};\n    Object.keys(graphs).forEach((graphId: any) => {\n      const graph = graphs[graphId];\n      const nodeMap = graph.nodeMap;\n      // Line 306\n      graph.nodes.forEach((node) => {\n        const forwardRootEdges = this.findForwardRootEdges(graph, node);\n        // Line 308\n        forwardRootEdges.forEach((edge) => {\n          let toNode = nodeMap[edge.to];\n          const nodeEdgeNodeLabel = `${node.label}-${edge.label}-${toNode.label}`;\n          if (!root[nodeEdgeNodeLabel])\n            root[nodeEdgeNodeLabel] = {\n              projected: [],\n              nodeLabel1: node.label as string,\n              edgeLabel: edge.label as string,\n              nodeLabel2: toNode.label as string,\n            };\n          const pdfs: PDFS = {\n            graphId,\n            edge,\n            preNode: null,\n          };\n          root[nodeEdgeNodeLabel].projected.push(pdfs);\n        });\n      });\n    });\n\n    // Line 313\n    Object.keys(root).forEach((nodeEdgeNodeLabel) => {\n      const { projected, nodeLabel1, edgeLabel, nodeLabel2 } = root[\n        nodeEdgeNodeLabel\n      ];\n\n      this.dfsCode.dfsEdgeList.push(\n        new DFSedge(0, 1, nodeLabel1, edgeLabel, nodeLabel2)\n      );\n      this.subGraphMining(projected);\n      this.dfsCode.dfsEdgeList.pop();\n    });\n  }\n}\n\nconst formatGraphs = (\n  graphs: GraphDataMap,\n  directed: boolean,\n  nodeLabelProp: string,\n  edgeLabelProp: string\n): GraphMap => {\n  const result: { [key: number]: Graph } = {};\n  Object.keys(graphs).forEach((key, i) => {\n    const graph = graphs[key];\n    const fGraph = new Graph(i, true, directed);\n    const nodeIdxMap = {};\n    graph.nodes.forEach((node, j) => {\n      fGraph.addNode(j, node[nodeLabelProp]);\n      nodeIdxMap[node.id] = j;\n    });\n    graph.edges.forEach((edge, k) => {\n      const sourceIdx = nodeIdxMap[edge.source];\n      const targetIdx = nodeIdxMap[edge.target];\n      fGraph.addEdge(-1, sourceIdx, targetIdx, edge[edgeLabelProp]);\n    });\n    if (fGraph && fGraph.getNodeNum()) result[fGraph.id] = fGraph;\n  });\n  return result;\n};\n\nconst toGraphDatas = (\n  graphs: Graph[],\n  nodeLabelProp: string,\n  edgeLabelProp: string\n) => {\n  const result = [];\n  graphs.forEach((graph) => {\n    const graphData = { nodes: [], edges: [] };\n    graph.nodes.forEach((node) => {\n      graphData.nodes.push({\n        id: `${node.id}`,\n        [nodeLabelProp]: node.label,\n      });\n    });\n    graph.edges.forEach((edge) => {\n      graphData.edges.push({\n        source: `${edge.from}`,\n        target: `${edge.to}`,\n        [edgeLabelProp]: edge.label,\n      });\n    });\n    result.push(graphData);\n  });\n  return result;\n};\n\ninterface Props {\n  graphs: GraphDataMap; // 图数据\n  minSupport: number; // 算法参数，最小支持数量，根据 graphs 内图的数量指定\n  directed?: boolean; // 是否有向图，默认为 false\n  nodeLabelProp?: string; // 节点类型的属性名\n  edgeLabelProp?: string; // 边类型的属性名\n  minNodeNum?: number; // 每个子图中节点的最少个数，默认为 1\n  maxNodeNum?: number; // 每个子图中节点的最多个数，默认为 4\n  top?: number; // 返回前 top 个频繁子图，默认为 10\n  verbose?: boolean;\n}\n\nconst DEFAULT_LABEL_NAME = \"cluster\";\n\n/**\n * gSpan 频繁子图计算算法（frequent graph mining）\n * @param params 参数\n */\nconst gSpan = (params: Props): GraphData[] => {\n  // ------- 将图数据 GraphData 的 map 转换为格式 -------\n  const {\n    graphs,\n    directed = false,\n    nodeLabelProp = DEFAULT_LABEL_NAME,\n    edgeLabelProp = DEFAULT_LABEL_NAME,\n  } = params;\n  const formattedGraphs = formatGraphs(\n    graphs,\n    directed,\n    nodeLabelProp,\n    edgeLabelProp\n  );\n  const { minSupport, maxNodeNum, minNodeNum, verbose, top } = params;\n\n  // ------- 初始化与执行算法 -------\n  const algoParams = {\n    graphs: formattedGraphs,\n    minSupport,\n    maxNodeNum,\n    minNodeNum,\n    top,\n    verbose,\n    directed,\n  };\n  const calculator = new GSpan(algoParams);\n  calculator.run();\n\n  const result = toGraphDatas(\n    calculator.frequentSubgraphs,\n    nodeLabelProp,\n    edgeLabelProp\n  );\n  return result;\n};\n\nexport default gSpan;\n", "import floydWarshall from './floydWarshall';\nimport { GraphData, Matrix } from './types';\nimport gSpan, { EdgeMap, NodeMap } from './gSpan/gSpan';\nimport dijkstra from './dijkstra';\nimport { uniqueId } from './util';\n\n/** 节点对 map */\ninterface NodePairMap {\n  [key: string]: {\n    // key 的格式为 startNodeIdx-endNodeIdx\n    start: number; // 第一个节点的 idx\n    end: number; // 第二个节点的 idx\n    distance: number; // 两节点最短路径长度\n  };\n}\n\ninterface LabelMap {\n  [label: string]: any;\n}\n\n/** 邻居单元类型 */\ninterface NeighborUnit {\n  nodeId: string;\n  nodeIdx: number;\n  nodeIdxs: number[]; // the first one is nodeIdx\n  neighbors: any[]; //\n  neighborNum: number;\n  nodeLabelCountMap: {\n    [label: string]: {\n      count: number;\n      dists: number[]; // 按照从小到大排序的距离数组\n    };\n  };\n}\n\n/** 节点对的邻居交集的诱导子图 map */\ninterface InterGraphMap {\n  [key: string]: GraphData; // key 格式由节点对的 idx 组成：beginIdx-endIdx，和 nodePairMap 对应\n}\n\n/**\n * 为 graphData 中每个节点生成邻居单元数组\n * @param graphData\n * @param spm\n * @param nodeLabelProp\n * @param k k-近邻\n */\nconst findKNeighborUnits = (\n  graphData: GraphData,\n  spm: Matrix[],\n  nodeLabelProp: string = 'cluster',\n  k: number = 2,\n): NeighborUnit[] => {\n  const units: NeighborUnit[] = [];\n  const nodes = graphData.nodes;\n  spm.forEach((row: number[], i) => {\n    units.push(findKNeighborUnit(nodes, row, i, nodeLabelProp, k));\n  });\n  return units;\n};\n\nconst findKNeighborUnit = (nodes, row, i, nodeLabelProp, k) => {\n  const unitNodeIdxs = [i];\n  const neighbors = [];\n  const labelCountMap = {};\n  row.forEach((v, j) => {\n    if (v <= k && i !== j) {\n      unitNodeIdxs.push(j);\n      neighbors.push(nodes[j]);\n      const label = nodes[j][nodeLabelProp];\n      if (!labelCountMap[label]) labelCountMap[label] = { count: 1, dists: [v] };\n      else {\n        labelCountMap[label].count++;\n        labelCountMap[label].dists.push(v);\n      }\n    }\n  });\n  // 将 labelCountMap 中的 dists 按照从小到大排序，方便后面使用\n  Object.keys(labelCountMap).forEach(label => {\n    labelCountMap[label].dists = labelCountMap[label].dists.sort((a, b) => a - b);\n  });\n  return {\n    nodeIdx: i,\n    nodeId: nodes[i].id,\n    nodeIdxs: unitNodeIdxs,\n    neighbors,\n    neighborNum: unitNodeIdxs.length - 1,\n    nodeLabelCountMap: labelCountMap,\n  };\n};\n\n/**\n * 随机寻找点对，满足距离小于 k\n * @param k 参数 k，表示 k-近邻\n * @param nodeNum 参数 length\n * @param maxNodePairNum 寻找点对的数量不超过 maxNodePairNum\n * @param spm 最短路径矩阵\n */\nconst findNodePairsRandomly = (\n  k: number,\n  nodeNum: number,\n  maxNodePairNum: number,\n  kNeighborUnits: NeighborUnit[],\n  spm: Matrix[],\n): NodePairMap => {\n  // 每个节点需要随机找出的点对数\n  let nodePairNumEachNode = Math.ceil(maxNodePairNum / nodeNum);\n  const nodePairMap = {};\n  let foundNodePairCount = 0;\n\n  // 遍历节点，为每个节点随机找出 nodePairNumEachNode 个点对，满足距离小于 k。找到的点对数量超过 maxNodePairNum 或所有节点遍历结束时终止\n  kNeighborUnits.forEach((unit, i) => {\n    // 若未达到 nodePairNumEachNode，或循环次数小于最大循环次数(2 * nodeNum)，继续循环\n    let nodePairForICount = 0;\n    let outerLoopCount = 0;\n    const neighbors = unit.nodeIdxs; // the first one is the center node\n    const neighborNum = unit.neighborNum - 1;\n    while (nodePairForICount < nodePairNumEachNode) {\n      // 另一端节点在节点数组中的的 index\n      let oidx = neighbors[1 + Math.floor(Math.random() * neighborNum)];\n      let innerLoopCount = 0;\n      // 若随机得到的另一端 idx 不符合条件，则继续 random。条件是不是同一个节点、这个点对没有被记录过、距离小于 k\n      while (nodePairMap[`${i}-${oidx}`] || nodePairMap[`${oidx}-${i}`]) {\n        oidx = Math.floor(Math.random() * nodeNum);\n        innerLoopCount++;\n        if (innerLoopCount > 2 * nodeNum) break; // 循环次数大于最大循环次数(2 * nodeNum)跳出循环，避免死循环\n      }\n      if (innerLoopCount < 2 * nodeNum) {\n        // 未达到最大循环次数，说明找到了合适的另一端\n        nodePairMap[`${i}-${oidx}`] = {\n          start: i,\n          end: oidx,\n          distance: spm[i][oidx],\n        };\n        nodePairForICount++;\n        foundNodePairCount++;\n        // 如果当前找到的点对数量达到了上限，返回结果\n        if (foundNodePairCount >= maxNodePairNum) return nodePairMap;\n      }\n      outerLoopCount++;\n      if (outerLoopCount > 2 * nodeNum) break; // 循环次数大于最大循环次数(2 * nodeNum)跳出循环，避免死循环\n    }\n    // 这个节点没有找到足够 nodePairNumEachNode 的点对。更新 nodePairNumEachNode，让后续节点找更多的点对\n    if (nodePairForICount < nodePairNumEachNode) {\n      const gap = nodePairNumEachNode - nodePairForICount;\n      nodePairNumEachNode = (nodePairNumEachNode + gap) / (nodeNum - i - 1);\n    }\n  });\n  return nodePairMap;\n};\n\n/**\n * 计算所有 nodePairMap 中节点对的相交邻居诱导子图\n * @param nodePairMap 节点对 map，key 为 node1.id-node2.id，value 为 { startNodeIdx, endNodeIdx, distance }\n * @param neighborUnits 每个节点的邻居元数组\n * @param graphData 原图数据\n * @param edgeMap 边的 map，方便检索\n * @param cachedInducedGraphMap 缓存的结果，下次进入该函数将继续更新该缓存，若 key 在缓存中存在则不需要重复计算\n */\nconst getIntersectNeighborInducedGraph = (\n  nodePairMap: NodePairMap,\n  neighborUnits: NeighborUnit[],\n  graphData: GraphData,\n  cachedInducedGraphMap?: InterGraphMap,\n): InterGraphMap => {\n  const nodes = graphData.nodes;\n  if (!cachedInducedGraphMap) cachedInducedGraphMap = {};\n  Object.keys(nodePairMap).forEach(key => {\n    if (cachedInducedGraphMap && cachedInducedGraphMap[key]) return;\n    cachedInducedGraphMap[key] = { nodes: [], edges: [] };\n    const pair = nodePairMap[key];\n    const startUnitNodeIds = neighborUnits[pair.start]?.nodeIdxs;\n    const endUnitNodeIds = neighborUnits[pair.end]?.nodeIdxs;\n    if (!startUnitNodeIds || !endUnitNodeIds) return; // 不存在邻元，返回空图\n    const endSet = new Set(endUnitNodeIds);\n    const intersect = startUnitNodeIds.filter(x => endSet.has(x)); // 可能会爆栈（在 1580 + 6 nodes full-connected 时出现）\n    if (!intersect || !intersect.length) return; // 没有交集，返回空图\n    const intersectIdMap = {};\n    const intersectLength = intersect.length;\n    for (let i = 0; i < intersectLength; i++) {\n      const node = nodes[intersect[i]];\n      cachedInducedGraphMap[key].nodes.push(node); // 将交集中的点加入诱导子图\n      intersectIdMap[node.id] = true;\n    }\n    // 遍历所有边数据，如果边的两端都在交集中，将该边加入诱导子图\n    graphData.edges.forEach(edge => {\n      if (intersectIdMap[edge.source] && intersectIdMap[edge.target])\n        cachedInducedGraphMap[key].edges.push(edge);\n    });\n  });\n  return cachedInducedGraphMap;\n};\n\n/**\n * 计算 strcutre 在 graph 上的匹配数量\n * @param graph 图数据\n * @param structure 目前支持只有两个节点一条边的最简单结构\n * @param nodeLabelProp 节点类型字段名\n * @param edgeLabelProp 边类型字段名\n */\nconst getMatchedCount = (graph, structure, nodeLabelProp, edgeLabelProp) => {\n  const nodeMap = {};\n  graph.nodes.forEach(node => {\n    nodeMap[node.id] = node;\n  });\n  let count = 0;\n  if (!structure?.edges?.length || structure?.nodes?.length < 2) return 0;\n  graph.edges.forEach(e => {\n    const sourceLabel = nodeMap[e.source][nodeLabelProp];\n    const targetLabel = nodeMap[e.target][nodeLabelProp];\n    const strNodeLabel1 = structure?.nodes[0][nodeLabelProp];\n    const strNodeLabel2 = structure?.nodes[1][nodeLabelProp];\n    const strEdgeLabel = structure?.edges[0][edgeLabelProp];\n\n    if (e[edgeLabelProp] !== strEdgeLabel) return;\n    if (\n      (sourceLabel === strNodeLabel1 && targetLabel === strNodeLabel2) ||\n      (sourceLabel === strNodeLabel2 && targetLabel === strNodeLabel1)\n    ) {\n      count++;\n    }\n  });\n  return count;\n};\n\n/**\n * structures 中寻找最具有代表性的一个。这个结构是使得 matchedCountMap 的分组方式类内间距最小，类间间距最大\n * @param matchedCountMap 每个 structure 分类后的各图匹配数量，格式 { [strcture.idx]: { [interInducedGraphKey]: count } }\n * @param structureNum strcuture 个数，与 matchedCountMap.length 对应\n * @param structures\n */\nconst findRepresentStructure = (matchedCountMap, structureNum, structures) => {\n  let maxOffset = Infinity,\n    representClusterType = 0;\n  for (let i = 0; i < structureNum; i++) {\n    // 一种分组的 map，key 是 intGraph 的 key，value 是 structures[i] 的匹配个数\n    const countMapI = matchedCountMap[i];\n    // 按照 value 为该组排序，生成 keys 的数组：\n    const sortedGraphKeys = Object.keys(countMapI).sort((a, b) => {\n      return countMapI[a] - countMapI[b];\n    });\n\n    // 共 100 个 graphKeys，将 graphKeys 按顺序分为 groupNum 组\n    const groupNum = 10;\n    const clusters = []; // 总共有 groupNum 个项\n    sortedGraphKeys.forEach((key, j) => {\n      if (!clusters[j % groupNum])\n        clusters[j % groupNum] = { graphs: [], totalCount: 0, aveCount: 0 };\n      clusters[j % groupNum].graphs.push(key);\n      clusters[j % groupNum].totalCount += countMapI[key];\n    });\n\n    // 计算 cluster 与 cluster 之间的距离 innerDist，每个 cluster 内部的距离 intraDist\n    let aveIntraDist = 0; // 该类的类内平均值\n    const aveCounts = []; // 类内平均匹配数量，将用于计算类间距离\n    clusters.forEach(graphsInCluster => {\n      // 类内均值\n      const aveCount = graphsInCluster.totalCount / graphsInCluster.graphs.length;\n      graphsInCluster.aveCount = aveCount;\n      aveCounts.push(aveCount);\n\n      // 对于每类，计算类内间距平均值\n      let aveIntraPerCluster = 0;\n      const graphsNum = graphsInCluster.length;\n      graphsInCluster.graphs.forEach((graphKey1, j) => {\n        const graph1Count = countMapI[graphKey1];\n        graphsInCluster.graphs.forEach((graphKey2, k) => {\n          if (j === k) return;\n          aveIntraPerCluster += Math.abs(graph1Count - countMapI[graphKey2]);\n        });\n      });\n      aveIntraPerCluster /= (graphsNum * (graphsNum - 1)) / 2;\n      aveIntraDist += aveIntraPerCluster;\n    });\n\n    aveIntraDist /= clusters.length;\n\n    // 用类内均值计算类间距\n    let aveInterDist = 0; // 类间间距平均值\n    aveCounts.forEach((aveCount1, j) => {\n      aveCounts.forEach((aveCount2, k) => {\n        if (j === k) return;\n        aveInterDist += Math.abs(aveCount1 - aveCount2);\n      });\n      aveInterDist /= (aveCounts.length * (aveCounts.length - 1)) / 2;\n    });\n\n    // 寻找 (类间间距均值-类内间距均值) 最大的一种分组方式（对应的 structure 就是最终要找的唯一 DS(G)）\n    const offset = aveInterDist - aveIntraDist;\n    if (maxOffset < offset) {\n      maxOffset = offset;\n      representClusterType = i;\n    }\n  }\n  return {\n    structure: structures[representClusterType],\n    structureCountMap: matchedCountMap[representClusterType],\n  };\n};\n\nconst getNodeMaps = (nodes, nodeLabelProp): { nodeMap: NodeMap; nodeLabelMap: LabelMap } => {\n  const nodeMap: NodeMap = {},\n    nodeLabelMap: LabelMap = {};\n  nodes.forEach((node, i) => {\n    nodeMap[node.id] = { idx: i, node, degree: 0, inDegree: 0, outDegree: 0 };\n    const label = node[nodeLabelProp];\n    if (!nodeLabelMap[label]) nodeLabelMap[label] = [];\n    nodeLabelMap[label].push(node);\n  });\n  return { nodeMap, nodeLabelMap };\n};\n\nconst getEdgeMaps = (\n  edges,\n  edgeLabelProp,\n  nodeMap: NodeMap,\n): { edgeMap: EdgeMap; edgeLabelMap: LabelMap } => {\n  const edgeMap = {},\n    edgeLabelMap = {};\n  edges.forEach((edge, i) => {\n    edgeMap[`${uniqueId}`] = { idx: i, edge };\n    const label = edge[edgeLabelProp];\n    if (!edgeLabelMap[label]) edgeLabelMap[label] = [];\n    edgeLabelMap[label].push(edge);\n\n    const sourceNode = nodeMap[edge.source];\n    if (sourceNode) {\n      sourceNode.degree++;\n      sourceNode.outDegree++;\n    }\n    const targetNode = nodeMap[edge.target];\n    if (targetNode) {\n      targetNode.degree++;\n      targetNode.inDegree++;\n    }\n  });\n  return { edgeMap, edgeLabelMap };\n};\n\n/**\n * 输出最短路径的 map，key 为 sourceNode.id-targetNode.id，value 为这两个节点的最短路径长度\n * @param nodes\n * @param spm\n * @param directed\n */\nconst getSpmMap = (nodes, spm, directed): { [key: string]: number } => {\n  const length = spm.length;\n  const map = {};\n  spm.forEach((row, i) => {\n    const start = directed ? 0 : i + 1;\n    const iId = nodes[i].id;\n    for (let j = start; j < length; j++) {\n      if (i === j) continue;\n      const jId = nodes[j].id;\n      const dist = row[j];\n      map[`${iId}-${jId}`] = dist;\n      if (!directed) map[`${jId}-${iId}`] = dist;\n    }\n  });\n  return map;\n};\n\n/**\n * 计算一对节点（node1，node2）的 NDS 距离\n * @param graph 原图数据\n * @param node1\n * @param node2\n */\nconst getNDSDist = (\n  graph,\n  node1,\n  node2,\n  nodeMap,\n  spDist,\n  kNeighborUnits,\n  structure,\n  nodeLabelProp,\n  edgeLabelProp,\n  cachedNDSMap,\n  cachedInterInducedGraph,\n) => {\n  const key = `${node1.id}-${node2.id}`;\n  if (cachedNDSMap && cachedNDSMap[key]) return cachedNDSMap[key];\n  let interInducedGraph = cachedInterInducedGraph ? cachedInterInducedGraph[key] : undefined;\n  // 若没有缓存相交邻居诱导子图，计算\n  if (!interInducedGraph) {\n    const pairMap: NodePairMap = {\n      [key]: {\n        start: nodeMap[node1.id].idx,\n        end: nodeMap[node2.id].idx,\n        distance: spDist,\n      },\n    };\n\n    cachedInterInducedGraph = getIntersectNeighborInducedGraph(\n      pairMap,\n      kNeighborUnits,\n      graph,\n      cachedInterInducedGraph,\n    );\n    interInducedGraph = cachedInterInducedGraph[key];\n  }\n\n  return getMatchedCount(interInducedGraph, structure, nodeLabelProp, edgeLabelProp);\n};\n\n/**\n * 计算 pattern 上绩点的度数并存储到 minPatternNodeLabelDegreeMap\n */\nconst stashPatternNodeLabelDegreeMap = (minPatternNodeLabelDegreeMap, neighborLabel, patternNodeMap, patternNodeLabelMap) => {\n  let minPatternNodeLabelDegree = minPatternNodeLabelDegreeMap[neighborLabel]?.degree;\n  let minPatternNodeLabelInDegree = minPatternNodeLabelDegreeMap[neighborLabel]?.inDegree;\n  let minPatternNodeLabelOutDegree = minPatternNodeLabelDegreeMap[neighborLabel]?.outDegree;\n\n  if (minPatternNodeLabelDegreeMap[neighborLabel] === undefined) {\n    minPatternNodeLabelDegree = Infinity;\n    minPatternNodeLabelInDegree = Infinity;\n    minPatternNodeLabelOutDegree = Infinity;\n    patternNodeLabelMap[neighborLabel].forEach(patternNodeWithLabel => {\n      const patternNodeDegree = patternNodeMap[patternNodeWithLabel.id].degree;\n      if (minPatternNodeLabelDegree > patternNodeDegree)\n        minPatternNodeLabelDegree = patternNodeDegree;\n      const patternNodeInDegree = patternNodeMap[patternNodeWithLabel.id].inDegree;\n      if (minPatternNodeLabelInDegree > patternNodeInDegree)\n        minPatternNodeLabelInDegree = patternNodeInDegree;\n      const patternNodeOutDegree = patternNodeMap[patternNodeWithLabel.id].outDegree;\n      if (minPatternNodeLabelOutDegree > patternNodeOutDegree)\n        minPatternNodeLabelOutDegree = patternNodeOutDegree;\n    });\n    minPatternNodeLabelDegreeMap[neighborLabel] = {\n      degree: minPatternNodeLabelDegree,\n      inDegree: minPatternNodeLabelInDegree,\n      outDegree: minPatternNodeLabelOutDegree\n    };\n  }\n\n  return {\n    minPatternNodeLabelDegree, \n    minPatternNodeLabelInDegree,\n    minPatternNodeLabelOutDegree\n  }\n}\n\n/**\n * GADDI 模式匹配\n * @param graphData 原图数据\n * @param pattern 搜索图（需要在原图上搜索的模式）数据\n * @param directed 是否计算有向图，默认 false\n * @param k 参数 k，表示 k-近邻\n * @param length 参数 length\n * @param nodeLabelProp 节点数据中代表节点标签（分类信息）的属性名。默认为 cluster\n * @param edgeLabelProp 边数据中代表边标签（分类信息）的属性名。默认为 cluster\n */\nconst GADDI = (\n  graphData: GraphData,\n  pattern: GraphData,\n  directed: boolean = false,\n  k: number,\n  length: number,\n  nodeLabelProp: string = 'cluster',\n  edgeLabelProp: string = 'cluster',\n): GraphData[] => {\n  if (!graphData || !graphData.nodes) return;\n  // 分为三步：\n  // 0. 预计算：节点/边数，邻接矩阵、最短路径矩阵\n  // 1. 处理原图 graphData。再分为 1~5 小步\n  // 2. 匹配\n\n  // console.log(\"----- stage-pre: preprocessing -------\");\n\n  // -------- 第零步，预计算：节点/边数，邻接矩阵、最短路径矩阵-------\n  const nodeNum = graphData.nodes.length;\n  if (!nodeNum) return;\n  // console.log(\"----- stage-pre.1: calc shortest path matrix for graph -------\");\n  const spm = floydWarshall(graphData, directed);\n  // console.log(\n  //   \"----- stage-pre.2: calc shortest path matrix for pattern -------\"\n  // );\n  const patternSpm = floydWarshall(pattern, directed);\n  // console.log(\n  //   \"----- stage-pre.3: calc shortest path matrix map for graph -------\"\n  // );\n  const spmMap = getSpmMap(graphData.nodes, spm, directed);\n  // console.log(\n  //   \"----- stage-pre.4: calc shortest path matrix map for pattern -------\"\n  // );\n  const patternSpmMap = getSpmMap(pattern.nodes, patternSpm, directed);\n\n  // console.log(\"----- stage-pre.5: establish maps -------\");\n  // 节点的 map，以 id 为 id 映射，方便后续快速检索\n  const { nodeMap, nodeLabelMap } = getNodeMaps(graphData.nodes, nodeLabelProp);\n  const { nodeMap: patternNodeMap, nodeLabelMap: patternNodeLabelMap } = getNodeMaps(\n    pattern.nodes,\n    nodeLabelProp,\n  );\n\n  // 计算节点度数\n  getEdgeMaps(graphData.edges, edgeLabelProp, nodeMap);\n\n  const { edgeLabelMap: patternEdgeLabelMap } = getEdgeMaps(\n    pattern.edges,\n    edgeLabelProp,\n    patternNodeMap,\n  );\n\n  // 若未指定 length，自动计算 pattern 半径（最短路径最大值）\n  let patternSpmSpread = [];\n  patternSpm?.forEach(row => {\n    patternSpmSpread = patternSpmSpread.concat(row);\n  })\n  if (!length) length = Math.max(...patternSpmSpread, 2);\n  if (!k) k = length;\n\n  // console.log(\"params\", directed, length, k);\n\n  // console.log(\"----- stage-pre.6: calc k neighbor units -------\");\n  // 计算每个节点的 k 邻元集合\n  const kNeighborUnits = findKNeighborUnits(graphData, spm, nodeLabelProp, k);\n  const patternKNeighborUnits = findKNeighborUnits(pattern, patternSpm, nodeLabelProp, k);\n\n  // console.log(\n  //   \"----- stage0: going to processing graph and find intersect neighbor induced graphs -------\"\n  // );\n\n  // console.log(\"----- stage0.1: going to select random node pairs -------\");\n  // -------- 第一步，处理原图 graphData-------\n\n  // 1.1. 随机选择最多 100 个点对，满足距离小于 Length 和 k\n  // 当 graphData 少于 20 个节点，则不能找出 100 个点对，只找出不多于 n(n-1)/2 个点对\n  const maxNodePairNum = Math.min(100, (nodeNum * (nodeNum - 1)) / 2);\n  const nodePairsMap = findNodePairsRandomly(\n    k,\n    nodeNum,\n    maxNodePairNum,\n    kNeighborUnits,\n    spm,\n  );\n\n  // console.log(\n  //   \"----- stage0.2: going to calculate intersect neighbor induced graphs -------\"\n  // );\n  // 1.2. 生成上面节点对的相应相交邻居诱导子图。格式为 {'beginNodeIdx-endNodeIdx': {nodes: [], edges: []}}\n  let intGMap = getIntersectNeighborInducedGraph(nodePairsMap, kNeighborUnits, graphData);\n  // 1.3. 使用 gSpan 算法（frequent graph mining）计算 ISIntG 的前 10 个频率最高的子结构（3-4条边）\n  const top = 10,\n    minSupport = 1,\n    minNodeNum = 1,\n    maxNodeNum = 4;\n  const params = {\n    graphs: intGMap,\n    nodeLabelProp,\n    edgeLabelProp,\n    minSupport,\n    minNodeNum,\n    maxNodeNum,\n    directed,\n  };\n\n  // console.log(\n  //   \"----- stage1: (gSpan) going to find frequent structure dsG -------\"\n  // );\n  // console.log(\"----- stage1.1: going to run gSpan -------\");\n  // 暂时假设生成的 sub structure 都只有一条边\n  const freStructures = gSpan(params).slice(0, top);\n  // structureNum 可能小于 top\n  const structureNum = freStructures.length;\n\n  // 1.4. 计算上述 10 个子结构在 intGMap 中每个诱导子图的匹配个数\n  const matchedCountMap = [];\n  freStructures.forEach((structure, i) => {\n    matchedCountMap[i] = {};\n    Object.keys(intGMap).forEach(key => {\n      const graph = intGMap[key];\n      const subStructureCount = getMatchedCount(graph, structure, nodeLabelProp, edgeLabelProp);\n      matchedCountMap[i][key] = subStructureCount;\n    });\n  });\n\n  // console.log(\n  //   \"----- stage1.1: going to find the most represent strucutre -------\"\n  // );\n\n  // 1.5. 对于每个子结构，根据匹配个数为 intGMap 中的诱导子图分组，生成 structureNum 种分组\n  // 计算每种分组的类间距和类内间距，找到类间距最大、类内间距最小的一种分组，这种分组对应的子结构被选为唯一代表性子结构 DS(G)\n  const { structure: dsG, structureCountMap: ndsDist } = findRepresentStructure(\n    matchedCountMap,\n    structureNum,\n    freStructures,\n  );\n\n  // -------- 第二步，匹配-------\n  // 2.1 找到从 Q 中的一个节点作为起始节点，寻找 G 中的匹配。这个其实节点的标签可以在 G 中找到最多的节点\n  let beginPNode = pattern.nodes[0],\n    candidates = [],\n    label = pattern.nodes[0]?.[nodeLabelProp],\n    maxNodeNumWithSameLabel = -Infinity;\n  pattern.nodes.forEach(node => {\n    const pLabel = node[nodeLabelProp];\n    const nodesWithSameLabel = nodeLabelMap[pLabel]\n    if (nodesWithSameLabel?.length > maxNodeNumWithSameLabel) {\n      maxNodeNumWithSameLabel = nodesWithSameLabel.length;\n      candidates = nodesWithSameLabel;\n      label = pLabel;\n      beginPNode = node;\n    }\n  });\n\n  // console.log(\"----- stage2: going to find candidates -------\");\n\n  // 全局缓存，避免重复计算\n  const minPatternNodeLabelDegreeMap = {}; // key 是 label，value 是该 label 节点的最小度数\n  let patternIntGraphMap = {},\n    patternNDSDist = {}, // key 为 node.id-node.id\n    patternNDSDistMap = {}; // key 为 node.id-label2，value nds距离值数组（按从大到小排序，无需关心具体对应哪个 node2）\n  // 2.2.2 对于 Q 中的另一个标签的 k 个节点，计算它们到 node 的最短路径以及 NDS 距离\n  const patternSpDist = {};\n  const patternSpDistBack = {};\n  Object.keys(patternNodeLabelMap).forEach((label2, j) => {\n    patternSpDist[label2] = [];\n    if (directed) {\n      patternSpDistBack[label2] = [];\n    }\n    let maxDist = -Infinity;\n    const patternNodesWithLabel2 = patternNodeLabelMap[label2];\n    const patternNodePairMap = {};\n    patternNodesWithLabel2.forEach(nodeWithLabel2 => {\n      const dist = patternSpmMap[`${beginPNode.id}-${nodeWithLabel2.id}`];\n      dist && patternSpDist[label2].push(dist);\n      if (maxDist < dist) maxDist = dist;\n      patternNodePairMap[`${beginPNode.id}-${nodeWithLabel2.id}`] = {\n        start: 0,\n        end: patternNodeMap[nodeWithLabel2.id].idx,\n        distance: dist,\n      };\n      if (directed) {\n        const distBack = patternSpmMap[`${nodeWithLabel2.id}-${beginPNode.id}`];\n        distBack && patternSpDistBack[label2].push(distBack);\n      }\n    });\n\n    // spDist[label2] 按照从小到大排序\n    patternSpDist[label2] = patternSpDist[label2].sort((a, b) => a - b);\n    if (directed) patternSpDistBack[label2] = patternSpDistBack[label2].sort((a, b) => a - b);\n\n    // 计算 Q 中所有 label2 节点到 beginPNode 的 NDS 距离\n    // 所有 label2 节点到 beginPNode 的邻居相交诱导子图：\n    // key: node1.id-node2.id\n    patternIntGraphMap = getIntersectNeighborInducedGraph(\n      patternNodePairMap,\n      patternKNeighborUnits,\n      pattern,\n      patternIntGraphMap,\n    );\n    // pattern 中 beginNode 到当前 label2 节点 的 NDS 距离（数组，无需关心具体对应到哪个节点）\n    let currentPatternNDSDistArray = [];\n    Object.keys(patternNodePairMap).forEach(key => {\n      if (patternNDSDist[key]) {\n        currentPatternNDSDistArray.push(patternNDSDist[key]);\n        return; // 缓存过则不需要再次计算\n      }\n      const patternIntGraph = patternIntGraphMap[key];\n      patternNDSDist[key] = getMatchedCount(patternIntGraph, dsG, nodeLabelProp, edgeLabelProp);\n      currentPatternNDSDistArray.push(patternNDSDist[key]);\n    });\n\n    // 根据值为 currentPatternNDSDist 从大到小排序\n    currentPatternNDSDistArray = currentPatternNDSDistArray.sort((a, b) => b - a);\n    patternNDSDistMap[`${beginPNode.id}-${label2}`] = currentPatternNDSDistArray;\n\n    if (label2 === label) return;\n\n    const candidatesNum = candidates?.length || 0;\n    for (let m = candidatesNum - 1; m >= 0; m--) {\n      const cNode = candidates[m];\n\n      // prune1：若 candidates 中节点 cNode 的 kNeighborUnits 中标签为 label2 的节点个数少于 pattern 中 label2 个数，删去它\n      const graphNeighborUnit = kNeighborUnits[nodeMap[cNode.id].idx];\n      const graphNeighborUnitCountMap = graphNeighborUnit.nodeLabelCountMap[label2];\n      const patternLabel2Num = patternNodeLabelMap[label2].length;\n      if (!graphNeighborUnitCountMap || graphNeighborUnitCountMap.count < patternLabel2Num) {\n        candidates.splice(m, 1);\n        continue;\n      }\n\n      // prune2：若 candidates 中节点 cNode 到 kNeighborUnits 中标签为 label2 的节点最短路径大于 patternSpDist[label2]，删去它\n      // (prune2 规则即：candidate 相关的最短路径的最大 spDist[label2].length 个，按照大小顺序依次和 patternSpDist[label2] 中的值比较，只要遇到一个是 G > Q 的，就删去这个 candidate)\n      let prune2Invalid = false;\n      for (let n = 0; n < patternLabel2Num; n++) {\n        if (graphNeighborUnitCountMap.dists[n] > patternSpDist[label2][n]) {\n          prune2Invalid = true;\n          break;\n        }\n      }\n      if (prune2Invalid) {\n        candidates.splice(m, 1);\n        continue;\n      }\n\n      // prune3：若 candidates 中节点 cNode 到 kNeighborUnits 中标签为 label2 的节点 NDS 距离小于 patternNDSDist[beginNode.id-label2]，删去它\n      // TODO：prune3，currentPatternNDSDistArray 与 currentNDSDist 的比较\n\n      // 计算 G 中所有 label2 节点到 cNode 的 NDS 距离\n      // 所有 label2 节点到 cNode 的邻居相交诱导子图：\n      const cNodePairMap = {};\n      graphNeighborUnit.neighbors.forEach(neighborNode => {\n        const dist = spmMap[`${cNode.id}-${neighborNode.id}`];\n        cNodePairMap[`${cNode.id}-${neighborNode.id}`] = {\n          start: nodeMap[cNode.id].idx,\n          end: nodeMap[neighborNode.id].idx,\n          distance: dist,\n        };\n      });\n      // 更新 intGMap\n      intGMap = getIntersectNeighborInducedGraph(cNodePairMap, kNeighborUnits, graphData, intGMap);\n      // candidate 到它周围 label2 节点的 NDS 距离, key 是 node.id-node.id\n      let currentNDSDistArray = [];\n      Object.keys(cNodePairMap).forEach(key => {\n        if (ndsDist[key]) {\n          currentNDSDistArray.push(ndsDist[key]);\n          return; // 缓存过则不需要再次计算\n        }\n        const intGraph = intGMap[key];\n        ndsDist[key] = getMatchedCount(intGraph, dsG, nodeLabelProp, edgeLabelProp);\n        currentNDSDistArray.push(ndsDist[key]);\n      });\n\n      // 根据值为 currentNDSDistArray 从大到小排序\n      currentNDSDistArray = currentNDSDistArray.sort((a, b) => b - a);\n\n      let prune3Invalid = false;\n      for (let n = 0; n < patternLabel2Num; n++) {\n        if (currentNDSDistArray[n] < currentPatternNDSDistArray[n]) {\n          prune3Invalid = true;\n          break;\n        }\n      }\n      if (prune3Invalid) {\n        candidates.splice(m, 1);\n        continue;\n      }\n    }\n  });\n\n  const candidateGraphs = [];\n\n  // console.log(\n  //   \"----- stage3: going to splice neighbors for each candidate graph -------\"\n  // );\n\n  // candidates 经过筛选后，以每个 candidate 为中心，生成 Length-neighbor 的邻居诱导子图\n  // 并在诱导子图中去除不可能在 Q 上找到匹配的点：在 Q 上不存在的 label，其他 label 到 candidate 的最大最短距离符合 Q、NDS 距离符合 Q\n  candidates?.forEach(candidate => {\n    const nodeIdx = nodeMap[candidate.id].idx;\n    const lengthNeighborUnit = findKNeighborUnit(\n      graphData.nodes,\n      spm[nodeIdx],\n      nodeIdx,\n      nodeLabelProp,\n      length,\n    );\n\n    const neighborNodes = lengthNeighborUnit.neighbors;\n\n    // 删除不可能找到匹配的邻居点\n    const neighborNum = neighborNodes.length;\n    let unmatched = false;\n    for (let i = neighborNum - 1; i >= 0; i--) {\n      // 如果通过裁剪，符合条件的节点数量已过少，说明不能匹配这个 candidate 相关的图\n      if (neighborNodes.length + 1 < pattern.nodes.length) {\n        unmatched = true;\n        return;\n      }\n      const neighborNode = neighborNodes[i];\n      const neighborLabel = neighborNode[nodeLabelProp];\n      // prune1: 若该邻居点的 label 不存在于 pattern 中，移除这个点\n      if (!patternNodeLabelMap[neighborLabel] || !patternNodeLabelMap[neighborLabel].length) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n      // prune2: 若该邻居点到 candidate 的最短路径比和它有相同 label 的节点到 beginPNode 的最大最短路径长度长，移除这个点\n      // prune2.1: 如果没有这个标签到 beginPNode 的距离记录，说明 pattern 上（可能 beginPNode 是这个 label）没有其他这个 label 的节点\n      if (!patternSpDist[neighborLabel] || !patternSpDist[neighborLabel].length) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n      const key = `${candidate.id}-${neighborNode.id}`;\n\n      // prune2.2\n      const distToCandidate = spmMap[key];\n      let idx = patternSpDist[neighborLabel].length - 1;\n      let maxDistWithLabelInPattern = patternSpDist[neighborLabel][idx]; // patternSpDist[neighborLabel] 已经按照从小到大排序\n      if (distToCandidate > maxDistWithLabelInPattern) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n    if (directed) {\n      const keyBack = `${neighborNode.id}-${candidate.id}`;\n      const distFromCandidate = spmMap[keyBack];\n      idx = patternSpDistBack[neighborLabel].length - 1;\n      let maxBackDistWithLabelInPattern = patternSpDistBack[neighborLabel][idx];\n      if (distFromCandidate > maxBackDistWithLabelInPattern) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n    }\n\n      // prune3: 若该邻居点到 candidate 的 NDS 距离比和它有相同 label 的节点到 beginPNode 的最小 NDS 距离小，移除这个点\n      const ndsToCandidate = ndsDist[key]\n        ? ndsDist[key]\n        : getNDSDist(\n            graphData,\n            candidate,\n            neighborNode,\n            nodeMap,\n            distToCandidate,\n            kNeighborUnits,\n            dsG,\n            nodeLabelProp,\n            edgeLabelProp,\n            ndsDist,\n            intGMap,\n          );\n      const patternKey = `${beginPNode.id}-${neighborLabel}`;\n      const minNdsWithLabelInPattern =\n        patternNDSDistMap[patternKey][patternNDSDistMap[patternKey].length - 1]; // patternNDSDist[key] 一定存在\n      if (ndsToCandidate < minNdsWithLabelInPattern) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n\n      // prune4: 若该邻居点的度数小于 pattern 同 label 节点最小度数，删去该点\n      const {\n        minPatternNodeLabelDegree,\n        minPatternNodeLabelInDegree,\n        minPatternNodeLabelOutDegree\n      } = stashPatternNodeLabelDegreeMap(minPatternNodeLabelDegreeMap, neighborLabel, patternNodeMap,patternNodeLabelMap);\n\n      if (nodeMap[neighborNode.id].degree < minPatternNodeLabelDegree) {\n        neighborNodes.splice(i, 1);\n        continue;\n      }\n    }\n\n    // 节点在个数上符合匹配（不少于 pattern 的节点个数），现在筛选相关边\n    if (!unmatched) {\n      candidateGraphs.push({\n        nodes: [candidate].concat(neighborNodes),\n      });\n    }\n  });\n\n  // console.log(\n  //   \"----- stage4: going to splice edges and neighbors for each candidate graph -------\"\n  // );\n\n  const { length: undirectedLengthsToBeginPNode } = dijkstra(pattern, beginPNode.id, false);\n\n  let undirectedLengthsToBeginPNodeLabelMap = {};\n  if (directed) {\n    Object.keys(undirectedLengthsToBeginPNode).forEach(nodeId => {\n      const nodeLabel = patternNodeMap[nodeId].node[nodeLabelProp];\n      if (!undirectedLengthsToBeginPNodeLabelMap[nodeLabel])\n        undirectedLengthsToBeginPNodeLabelMap[nodeLabel] = [undirectedLengthsToBeginPNode[nodeId]];\n      else\n        undirectedLengthsToBeginPNodeLabelMap[nodeLabel].push(\n          undirectedLengthsToBeginPNode[nodeId],\n        );\n    });\n    Object.keys(undirectedLengthsToBeginPNodeLabelMap).forEach(pLabel => {\n      undirectedLengthsToBeginPNodeLabelMap[pLabel].sort((a, b) => a - b);\n    });\n  } else {\n    undirectedLengthsToBeginPNodeLabelMap = patternSpDist;\n  }\n\n  // 现在 candidateGraphs 里面只有节点，进行边的筛选\n  let candidateGraphNum = candidateGraphs.length;\n  for (let i = candidateGraphNum - 1; i >= 0; i--) {\n    const candidateGraph = candidateGraphs[i];\n    const candidate = candidateGraph.nodes[0];\n\n    const candidateNodeLabelCountMap = {};\n    const candidateNodeMap = {};\n    candidateGraph.nodes.forEach((node, q) => {\n      candidateNodeMap[node.id] = {\n        idx: q,\n        node,\n        degree: 0,\n        inDegree: 0,\n        outDegree: 0\n      };\n      const cNodeLabel = node[nodeLabelProp];\n      if (!candidateNodeLabelCountMap[cNodeLabel]) candidateNodeLabelCountMap[cNodeLabel] = 1;\n      else candidateNodeLabelCountMap[cNodeLabel]++;\n    });\n\n    // 根据 candidate 和 neighborNodes 中的节点生成 G 的诱导子图\n    // 即，将 graphData 上两端都在 candidateGraph.nodes 中的边放入 candidateEdges\n    const candidateEdges = [];\n    const edgeLabelCountMap = {};\n    graphData.edges.forEach(edge => {\n      if (candidateNodeMap[edge.source] && candidateNodeMap[edge.target]) {\n        candidateEdges.push(edge);\n        if (!edgeLabelCountMap[edge[edgeLabelProp]]) edgeLabelCountMap[edge[edgeLabelProp]] = 1;\n        else edgeLabelCountMap[edge[edgeLabelProp]]++;\n        candidateNodeMap[edge.source].degree++;\n        candidateNodeMap[edge.target].degree++;\n        candidateNodeMap[edge.source].outDegree++;\n        candidateNodeMap[edge.target].inDegree++;\n      }\n    });\n\n    // prune：若有一个 edgeLabel 在 candidateGraph 上的个数少于 pattern，去除该图\n    const pattenrEdgeLabelNum = Object.keys(patternEdgeLabelMap).length;\n    let prunedByEdgeLabel = false;\n    for (let e = 0; e < pattenrEdgeLabelNum; e++) {\n      const label = Object.keys(patternEdgeLabelMap)[e];\n      if (\n        !edgeLabelCountMap[label] ||\n        edgeLabelCountMap[label] < patternEdgeLabelMap[label].length\n      ) {\n        prunedByEdgeLabel = true;\n        break;\n      }\n    }\n    if (prunedByEdgeLabel) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    // 遍历 candidateEdges，进行边的筛选\n    let candidateEdgeNum = candidateEdges.length;\n\n    // prune：若边数过少，去除该图\n    if (candidateEdgeNum < pattern.edges.length) {\n      candidateGraphs.splice(i, 1);\n      break;\n    }\n    let candidateGraphInvalid = false;\n    for (let e = candidateEdgeNum - 1; e >= 0; e--) {\n      const edge = candidateEdges[e];\n      const edgeLabel = edge[edgeLabelProp];\n      const patternEdgesWithLabel = patternEdgeLabelMap[edgeLabel];\n\n      // prune 1: 若边的 label 不存在于 pattern 边 label 中，去除该边\n      if (!patternEdgesWithLabel || !patternEdgesWithLabel.length) {\n        edgeLabelCountMap[edgeLabel]--;\n        // 若这个 label 的 count 减少之后，该 label 的边数不足，去除该图\n        if (patternEdgesWithLabel && edgeLabelCountMap[edgeLabel] < patternEdgesWithLabel.length) {\n          candidateGraphInvalid = true;\n          break;\n        }\n        candidateEdges.splice(e, 1);\n        candidateNodeMap[edge.source].degree--;\n        candidateNodeMap[edge.target].degree--;\n        candidateNodeMap[edge.source].outDegree--;\n        candidateNodeMap[edge.target].inDegree--;\n        continue;\n      }\n\n      // prune 2: 若边的 label +两端 label 的三元组关系不能在 pattern 中找到，去除该边\n      const sourceLabel = candidateNodeMap[edge.source].node[nodeLabelProp];\n      const targetLabel = candidateNodeMap[edge.target].node[nodeLabelProp];\n\n      let edgeMatched = false;\n      patternEdgesWithLabel.forEach(patternEdge => {\n        const patternSource = patternNodeMap[patternEdge.source].node;\n        const patternTarget = patternNodeMap[patternEdge.target].node;\n        if (\n          patternSource[nodeLabelProp] === sourceLabel &&\n          patternTarget[nodeLabelProp] === targetLabel\n        )\n          edgeMatched = true;\n        if (\n          !directed &&\n          patternSource[nodeLabelProp] === targetLabel &&\n          patternTarget[nodeLabelProp] === sourceLabel\n        )\n          edgeMatched = true;\n      });\n      if (!edgeMatched) {\n        edgeLabelCountMap[edgeLabel]--;\n        // 若这个 label 的 count 减少之后，该 label 的边数不足，去除该图\n        if (patternEdgesWithLabel && edgeLabelCountMap[edgeLabel] < patternEdgesWithLabel.length) {\n          candidateGraphInvalid = true;\n          break;\n        }\n        candidateEdges.splice(e, 1);\n        candidateNodeMap[edge.source].degree--;\n        candidateNodeMap[edge.target].degree--;\n        candidateNodeMap[edge.source].outDegree--;\n        candidateNodeMap[edge.target].inDegree--;\n        continue;\n      }\n    }\n\n    // prune2: 删除边的过程中，发现边数过少/边 label 数过少时，去除该图\n    if (candidateGraphInvalid) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    candidateGraph.edges = candidateEdges;\n\n    const { length: lengthsToCandidate } = dijkstra(\n      candidateGraph,\n      candidateGraph.nodes[0].id,\n      false, // 此处计算路径长度用于判断是否连通，因此使用无向图\n    );\n    Object.keys(lengthsToCandidate)\n      .reverse()\n      .forEach(targetId => {\n        if (targetId === candidateGraph.nodes[0].id || candidateGraphInvalid) return;\n        // prune4: 通过上述裁剪，可能导致该邻居子图变为不连通。裁剪掉目前在这个邻居子图中和 candidate（第一个节点）不连通的节点\n        if (lengthsToCandidate[targetId] === Infinity) {\n          const targetNodeLabel = candidateNodeMap[targetId].node[nodeLabelProp];\n          candidateNodeLabelCountMap[targetNodeLabel]--;\n          if (\n            candidateNodeLabelCountMap[targetNodeLabel] <\n            patternNodeLabelMap[targetNodeLabel].length\n          ) {\n            candidateGraphInvalid = true;\n            return;\n          }\n          const idx = candidateGraph.nodes.indexOf(candidateNodeMap[targetId].node);\n          candidateGraph.nodes.splice(idx, 1);\n          candidateNodeMap[targetId] = undefined;\n          return;\n        }\n        // prune5: 经过边裁剪后，可能又出现了最短路径过长的节点 （比 pattern 中同 label 的节点到 beginNode 最大最短距离远），删去这些节点\n        const nLabel = nodeMap[targetId].node[nodeLabelProp];\n        if (\n          !undirectedLengthsToBeginPNodeLabelMap[nLabel] ||\n          !undirectedLengthsToBeginPNodeLabelMap[nLabel].length ||\n          lengthsToCandidate[targetId] >\n            undirectedLengthsToBeginPNodeLabelMap[nLabel][\n              undirectedLengthsToBeginPNodeLabelMap[nLabel].length - 1\n            ]\n        ) {\n          const targetNodeLabel = candidateNodeMap[targetId].node[nodeLabelProp];\n          candidateNodeLabelCountMap[targetNodeLabel]--;\n          if (\n            candidateNodeLabelCountMap[targetNodeLabel] <\n            patternNodeLabelMap[targetNodeLabel].length\n          ) {\n            candidateGraphInvalid = true;\n            return;\n          }\n          const idx = candidateGraph.nodes.indexOf(candidateNodeMap[targetId].node);\n          candidateGraph.nodes.splice(idx, 1);\n          candidateNodeMap[targetId] = undefined;\n        }\n      });\n\n    if (candidateGraphInvalid) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    let degreeChanged = true;\n    let loopCount = 0;\n    while (degreeChanged && !candidateGraphInvalid) {\n      degreeChanged = false;\n\n      // candidate 度数不足，删去该图\n      const condition = directed ? (candidateNodeMap[candidate.id].degree < patternNodeMap[beginPNode.id].degree || \n        candidateNodeMap[candidate.id].inDegree < patternNodeMap[beginPNode.id].inDegree ||\n        candidateNodeMap[candidate.id].outDegree < patternNodeMap[beginPNode.id].outDegree) :\n        candidateNodeMap[candidate.id].degree < patternNodeMap[beginPNode.id].degree;\n      if (condition) {\n        candidateGraphInvalid = true;\n        break;\n      }\n      // candidate label 个数不足，删去该图\n      if (\n        candidateNodeLabelCountMap[candidate[nodeLabelProp]] <\n        patternNodeLabelMap[candidate[nodeLabelProp]].length\n      ) {\n        candidateGraphInvalid = true;\n        break;\n      }\n\n      // prune6：去除度数过小的节点\n      const currentCandidateNodeNum = candidateGraph.nodes.length;\n      for (let o = currentCandidateNodeNum - 1; o >= 0; o--) {\n        const cgNode = candidateGraph.nodes[o];\n        const nodeDegree = candidateNodeMap[cgNode.id].degree;\n        const nodeInDegree = candidateNodeMap[cgNode.id].inDegree;\n        const nodeOutDegree = candidateNodeMap[cgNode.id].outDegree;\n        const cNodeLabel = cgNode[nodeLabelProp];\n        \n        const {\n          minPatternNodeLabelDegree,\n          minPatternNodeLabelInDegree,\n          minPatternNodeLabelOutDegree\n        } = stashPatternNodeLabelDegreeMap(minPatternNodeLabelDegreeMap, cNodeLabel, patternNodeMap,patternNodeLabelMap);\n        \n        const deleteCondition = directed ? (nodeDegree < minPatternNodeLabelDegree || \n          nodeInDegree < minPatternNodeLabelInDegree ||\n          nodeOutDegree < minPatternNodeLabelOutDegree) :\n          nodeDegree < minPatternNodeLabelDegree;\n        if (deleteCondition) {\n          candidateNodeLabelCountMap[cgNode[nodeLabelProp]]--;\n          // 节点 label 个数不足\n          if (\n            candidateNodeLabelCountMap[cgNode[nodeLabelProp]] <\n            patternNodeLabelMap[cgNode[nodeLabelProp]].length\n          ) {\n            candidateGraphInvalid = true;\n            break;\n          }\n          candidateGraph.nodes.splice(o, 1);\n          candidateNodeMap[cgNode.id] = undefined;\n          degreeChanged = true;\n        }\n      }\n      if (candidateGraphInvalid || (!degreeChanged && loopCount !== 0)) break;\n      // 经过 prune5 节点裁剪，删去端点已经不在 candidateGraph 中的边\n      candidateEdgeNum = candidateEdges.length;\n      for (let y = candidateEdgeNum - 1; y >= 0; y--) {\n        const cedge = candidateEdges[y];\n        if (!candidateNodeMap[cedge.source] || !candidateNodeMap[cedge.target]) {\n          candidateEdges.splice(y, 1);\n          const edgeLabel = cedge[edgeLabelProp];\n          edgeLabelCountMap[edgeLabel]--;\n          if (candidateNodeMap[cedge.source]) {\n            candidateNodeMap[cedge.source].degree--;\n            candidateNodeMap[cedge.source].outDegree--;\n          }\n          if (candidateNodeMap[cedge.target]) {\n            candidateNodeMap[cedge.target].degree--;\n            candidateNodeMap[cedge.target].inDegree--;\n          }\n          // 边 label 数量不足\n          if (\n            patternEdgeLabelMap[edgeLabel] &&\n            edgeLabelCountMap[edgeLabel] < patternEdgeLabelMap[edgeLabel].length\n          ) {\n            candidateGraphInvalid = true;\n            break;\n          }\n          degreeChanged = true;\n        }\n      }\n      loopCount++;\n    }\n\n    if (candidateGraphInvalid) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n\n    // prune: 若节点/边数过少，节点/边 label 过少，去掉这个图\n    if (\n      candidateGraphInvalid ||\n      candidateGraph.nodes.length < pattern.nodes.length ||\n      candidateEdges.length < pattern.edges.length\n    ) {\n      candidateGraphs.splice(i, 1);\n      continue;\n    }\n  }\n\n  // 此时已经生成的多个 candidateGraphs，可能有重复\n\n  // console.log(\n  //   \"----- stage5: going to splice dulplicated candidate graphs -------\"\n  // );\n\n  // 删去 candidateGraphs 中一模一样的子图，通过边的 node-node-edgeLabel 作为 key，这类边个数作为 value，进行匹配\n  let currentLength = candidateGraphs.length;\n  for (let i = 0; i <= currentLength - 1; i++) {\n    const cg1 = candidateGraphs[i];\n    const cg1EdgeMap = {}; // [node1.id-node2.id-edge.label]: count\n    cg1.edges.forEach(edge => {\n      const key = `${edge.source}-${edge.target}-${edge.label}`;\n      if (!cg1EdgeMap[key]) cg1EdgeMap[key] = 1;\n      else cg1EdgeMap[key]++;\n    });\n\n    for (let j = currentLength - 1; j > i; j--) {\n      const cg2 = candidateGraphs[j];\n      const cg2EdgeMap = {}; // [node1.id-node2.id-edge.label]: count\n      cg2.edges.forEach(edge => {\n        const key = `${edge.source}-${edge.target}-${edge.label}`;\n        if (!cg2EdgeMap[key]) cg2EdgeMap[key] = 1;\n        else cg2EdgeMap[key]++;\n      });\n\n      let same = true;\n      if (Object.keys(cg2EdgeMap).length !== Object.keys(cg1EdgeMap).length) {\n        same = false;\n      } else {\n        Object.keys(cg1EdgeMap).forEach(key => {\n          if (cg2EdgeMap[key] !== cg1EdgeMap[key]) same = false;\n        });\n      }\n      if (same) {\n        candidateGraphs.splice(j, 1);\n      }\n    }\n    currentLength = candidateGraphs.length;\n  }\n\n  return candidateGraphs;\n};\n\nexport default GADDI;\n", "import LinkedList from './linked-list';\n\nexport default class Stack {\n\n  private linkedList: LinkedList;\n\n  private maxStep: number;\n\n  constructor(maxStep: number = 10) {\n    this.linkedList = new LinkedList();\n    this.maxStep = maxStep;\n  }\n\n  get length() {\n    return this.linkedList.toArray().length;\n  }\n\n  /**\n   * 判断栈是否为空，如果链表中没有头部元素，则栈为空\n   */\n  isEmpty() {\n    return !this.linkedList.head;\n  }\n\n  /**\n   * 是否到定义的栈的最大长度，如果达到最大长度后，不再允许入栈\n   */\n  isMaxStack() {\n    return this.toArray().length >= this.maxStep;\n  }\n\n  /**\n   * 访问顶端元素\n   */\n  peek() {\n    if (this.isEmpty()) {\n      return null;\n    }\n\n    // 返回头部元素，不删除元素\n    return this.linkedList.head.value;\n  }\n\n  push(value) {\n    this.linkedList.prepend(value);\n    if (this.length > this.maxStep) {\n      this.linkedList.deleteTail();\n    }\n  }\n\n  pop() {\n    const removeHead = this.linkedList.deleteHead();\n    return removeHead ? removeHead.value : null;\n  }\n\n  toArray() {\n    return this.linkedList.toArray().map((node) => node.value);\n  }\n\n  clear() {\n    while (!this.isEmpty()) {\n      this.pop();\n    }\n  }\n}\n", "import getAdjMatrix from './adjacent-matrix';\nimport breadthFirstSearch from './bfs';\nimport connectedComponent from './connected-component';\nimport getDegree from './degree';\nimport { getInDegree, getOutDegree } from './degree';\nimport detectCycle, { detectAllCycles, detectAllDirectedCycle, detectAllUndirectedCycle } from './detect-cycle';\nimport depthFirstSearch from './dfs';\nimport dijkstra from './dijkstra';\nimport { findAllPath, findShortestPath } from './find-path';\nimport floydWarshall from './floydWarshall';\nimport labelPropagation from './label-propagation';\nimport louvain from './louvain';\nimport iLouvain from './i-louvain';\nimport kCore from './k-core';\nimport kMeans from './k-means';\nimport cosineSimilarity from './cosine-similarity';\nimport nodesCosineSimilarity from './nodes-cosine-similarity';\nimport minimumSpanningTree from './mts';\nimport pageRank from './pageRank';\nimport GADDI from './gaddi';\nimport Stack from './structs/stack';\nimport { getNeighbors } from './util';\nimport { IAlgorithm } from './types';\n\nconst detectDirectedCycle = detectCycle;\n\nexport {\n  getAdjMatrix,\n  breadthFirstSearch,\n  connectedComponent,\n  getDegree,\n  getInDegree,\n  getOutDegree,\n  detectCycle,\n  detectDirectedCycle,\n  detectAllCycles,\n  detectAllDirectedCycle,\n  detectAllUndirectedCycle,\n  depthFirstSearch,\n  dijkstra,\n  findAllPath,\n  findShortestPath,\n  floydWarshall,\n  labelPropagation,\n  louvain,\n  iLouvain,\n  kCore,\n  kMeans,\n  cosineSimilarity,\n  nodesCosineSimilarity,\n  minimumSpanningTree,\n  pageRank,\n  getNeighbors,\n  Stack,\n  GADDI,\n  IAlgorithm\n};\n\nexport default {\n  getAdjMatrix,\n  breadthFirstSearch,\n  connectedComponent,\n  getDegree,\n  getInDegree,\n  getOutDegree,\n  detectCycle,\n  detectDirectedCycle,\n  detectAllCycles,\n  detectAllDirectedCycle,\n  detectAllUndirectedCycle,\n  depthFirstSearch,\n  dijkstra,\n  findAllPath,\n  findShortestPath,\n  floydWarshall,\n  labelPropagation,\n  louvain,\n  iLouvain,\n  kCore,\n  kMeans,\n  cosineSimilarity,\n  nodesCosineSimilarity,\n  minimumSpanningTree,\n  pageRank,\n  getNeighbors,\n  Stack,\n  GADDI,\n};", "import Queue from './structs/queue'\nimport { GraphData, IAlgorithmCallbacks } from './types';\nimport { getNeighbors } from './util';\n\n/**\n *\n * @param callbacks\n * allowTraversal: 确定 BFS 是否从顶点沿着边遍历到其邻居，默认情况下，同一个节点只能遍历一次\n * enterNode: 当 BFS 访问某个节点时调用\n * leaveNode: 当 BFS 访问访问结束某个节点时调用\n */\nfunction initCallbacks(callbacks: IAlgorithmCallbacks = {} as IAlgorithmCallbacks) {\n  const initiatedCallback = callbacks;\n\n  const stubCallback = () => {};\n\n  const allowTraversalCallback = (() => {\n    const seen = {};\n    return ({ next }) => {\n      const id = next;\n      if (!seen[id]) {\n        seen[id] = true;\n        return true;\n      }\n      return false;\n    };\n  })();\n\n  initiatedCallback.allowTraversal = callbacks.allowTraversal || allowTraversalCallback;\n  initiatedCallback.enter = callbacks.enter || stubCallback;\n  initiatedCallback.leave = callbacks.leave || stubCallback;\n\n  return initiatedCallback;\n}\n\n/**\n * 广度优先遍历图\n * @param graph Graph 图实例\n * @param startNode 开始遍历的节点\n * @param originalCallbacks 回调\n */\nconst breadthFirstSearch = (\n  graphData: GraphData,\n  startNodeId: string,\n  originalCallbacks?: IAlgorithmCallbacks,\n  directed: boolean = true\n) => {\n  const callbacks = initCallbacks(originalCallbacks);\n  const nodeQueue = new Queue();\n\n  const { edges = [] } = graphData\n\n  // 初始化队列元素\n  nodeQueue.enqueue(startNodeId);\n\n  let previousNode = '';\n\n  // 遍历队列中的所有顶点\n  while (!nodeQueue.isEmpty()) {\n    const currentNode: string = nodeQueue.dequeue();\n    callbacks.enter({\n      current: currentNode,\n      previous: previousNode,\n    });\n\n    // 将所有邻居添加到队列中以便遍历\n    getNeighbors(currentNode, edges, directed ? 'target' : undefined).forEach((nextNode) => {\n      if (\n        callbacks.allowTraversal({\n          previous: previousNode,\n          current: currentNode,\n          next: nextNode,\n        })\n      ) {\n        nodeQueue.enqueue(nextNode);\n      }\n    });\n\n    callbacks.leave({\n      current: currentNode,\n      previous: previousNode,\n    });\n\n    // 下一次循环之前存储当前顶点\n    previousNode = currentNode;\n  }\n};\n\nexport default breadthFirstSearch;\n", "import dijkstra from './dijkstra';\nimport { GraphData } from './types';\nimport { getNeighbors } from './util';\n\nexport const findShortestPath = (\n  graphData: GraphData,\n  start: string,\n  end: string,\n  directed?: boolean,\n  weightPropertyName?: string\n) => {\n  const { length, path, allPath } = dijkstra(\n    graphData,\n    start,\n    directed,\n    weightPropertyName\n  );\n  return { length: length[end], path: path[end], allPath: allPath[end] };\n};\n\nexport const findAllPath = (\n  graphData: GraphData,\n  start: string,\n  end: string,\n  directed?: boolean\n) => {\n  if (start === end) return [[start]];\n\n  const { edges = [] } = graphData;\n\n  const visited = [start];\n  const isVisited = { [start]: true };\n  const stack: string[][] = []; // 辅助栈，用于存储访问过的节点的邻居节点\n  const allPath = [];\n  let neighbors = directed\n    ? getNeighbors(start, edges, 'target')\n    : getNeighbors(start, edges);\n  stack.push(neighbors);\n\n  while (visited.length > 0 && stack.length > 0) {\n    const children = stack[stack.length - 1];\n    if (children.length) {\n      const child = children.shift();\n      if (child) {\n        visited.push(child);\n        isVisited[child] = true;\n        neighbors = directed\n          ? getNeighbors(child, edges, 'target')\n          : getNeighbors(child, edges);\n        stack.push(neighbors.filter(neighbor => !isVisited[neighbor]));\n      }\n    } else {\n      const node = visited.pop();\n      isVisited[node] = false;\n      stack.pop();\n      continue;\n    }\n\n    if (visited[visited.length - 1] === end) {\n      const path = visited.map(node => node);\n      allPath.push(path);\n\n      const node = visited.pop();\n      isVisited[node] = false;\n      stack.pop();\n    }\n  }\n\n  return allPath;\n};\n", "\nimport getAdjMatrix from './adjacent-matrix'\nimport { uniqueId } from './util';\nimport { GraphData, ClusterData } from './types';\n\n/**\n * 标签传播算法\n * @param graphData 图数据\n * @param directed 是否有向图，默认为 false\n * @param weightPropertyName 权重的属性字段\n * @param maxIteration 最大迭代次数\n */\nconst labelPropagation = (\n  graphData: GraphData,\n  directed: boolean = false,\n  weightPropertyName: string = 'weight',\n  maxIteration: number = 1000\n): ClusterData => {\n  // the origin data\n  const { nodes = [], edges = [] } = graphData;\n\n  const clusters = {};\n  const nodeMap = {};\n  // init the clusters and nodeMap\n  nodes.forEach((node, i) => {\n    const cid: string = uniqueId();\n    node.clusterId = cid;\n    clusters[cid] = {\n      id: cid,\n      nodes: [node]\n    };\n    nodeMap[node.id] = {\n      node,\n      idx: i\n    };\n  });\n\n  // the adjacent matrix of calNodes inside clusters\n  const adjMatrix = getAdjMatrix(graphData, directed);\n  // the sum of each row in adjacent matrix\n  const ks = [];\n  /**\n   * neighbor nodes (id for key and weight for value) for each node\n   * neighbors = {\n   *  id(node_id): { id(neighbor_1_id): weight(weight of the edge), id(neighbor_2_id): weight(weight of the edge), ... },\n   *  ...\n   * }\n   */\n  const neighbors = {};\n  adjMatrix.forEach((row, i) => {\n    let k = 0;\n    const iid = nodes[i].id;\n    neighbors[iid] = {};\n    row.forEach((entry, j) => {\n      if (!entry) return;\n      k += entry;\n      const jid = nodes[j].id;\n      neighbors[iid][jid] = entry;\n    });\n    ks.push(k);\n  });\n\n  let iter = 0;\n\n  while (iter < maxIteration) {\n    let changed = false;\n    nodes.forEach(node => {\n      const neighborClusters = {};\n      Object.keys(neighbors[node.id]).forEach(neighborId => {\n        const neighborWeight = neighbors[node.id][neighborId];\n        const neighborNode = nodeMap[neighborId].node;\n        const neighborClusterId = neighborNode.clusterId;\n        if (!neighborClusters[neighborClusterId]) neighborClusters[neighborClusterId] = 0;\n        neighborClusters[neighborClusterId] += neighborWeight;\n      });\n      // find the cluster with max weight\n      let maxWeight = -Infinity;\n      let bestClusterIds = [];\n      Object.keys(neighborClusters).forEach(clusterId => {\n        if (maxWeight < neighborClusters[clusterId]) {\n          maxWeight = neighborClusters[clusterId];\n          bestClusterIds = [clusterId];\n        } else if (maxWeight === neighborClusters[clusterId]) {\n          bestClusterIds.push(clusterId);\n        }\n      });\n      if (bestClusterIds.length === 1 && bestClusterIds[0] === node.clusterId) return;\n      const selfClusterIdx = bestClusterIds.indexOf(node.clusterId);\n      if (selfClusterIdx >= 0) bestClusterIds.splice(selfClusterIdx, 1);\n      if (bestClusterIds && bestClusterIds.length) {\n        changed = true;\n\n        // remove from origin cluster\n        const selfCluster = clusters[node.clusterId as string];\n        const nodeInSelfClusterIdx = selfCluster.nodes.indexOf(node);\n        selfCluster.nodes.splice(nodeInSelfClusterIdx, 1);\n\n        // move the node to the best cluster\n        const randomIdx = Math.floor(Math.random() * bestClusterIds.length);\n        const bestCluster = clusters[bestClusterIds[randomIdx]];\n        bestCluster.nodes.push(node);\n        node.clusterId = bestCluster.id;\n      }\n    });\n    if (!changed) break;\n    iter++;\n  }\n\n  // delete the empty clusters\n  Object.keys(clusters).forEach(clusterId => {\n    const cluster = clusters[clusterId];\n    if (!cluster.nodes || !cluster.nodes.length) {\n      delete clusters[clusterId];\n    }\n  });\n\n  // get the cluster edges\n  const clusterEdges = [];\n  const clusterEdgeMap = {};\n  edges.forEach(edge => {\n    const { source, target } = edge;\n    const weight = edge[weightPropertyName] || 1;\n    const sourceClusterId = nodeMap[source].node.clusterId;\n    const targetClusterId = nodeMap[target].node.clusterId;\n    const newEdgeId = `${sourceClusterId}---${targetClusterId}`;\n    if (clusterEdgeMap[newEdgeId]) {\n      clusterEdgeMap[newEdgeId].weight += weight;\n      clusterEdgeMap[newEdgeId].count++;\n    } else {\n      const newEdge = {\n        source: sourceClusterId,\n        target: targetClusterId,\n        weight,\n        count: 1\n      };\n      clusterEdgeMap[newEdgeId] = newEdge;\n      clusterEdges.push(newEdge);\n    }\n  });\n\n  const clustersArray = [];\n  Object.keys(clusters).forEach(clusterId => {\n    clustersArray.push(clusters[clusterId]);\n  });\n  return {\n    clusters: clustersArray,\n    clusterEdges\n  }\n}\n\nexport default labelPropagation;\n", "import louvain from './louvain';\nimport type { ClusterData, GraphData } from './types';\n\n/**\n * 社区发现 i-louvain 算法：模块度 + 惯性模块度（即节点属性相似性）\n * @param graphData 图数据\n * @param directed 是否有向图，默认为 false\n * @param weightPropertyName 权重的属性字段\n * @param threshold 差值阈值\n * @param propertyKey 属性的字段名\n * @param involvedKeys 参与计算的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n * @param inertialWeight 惯性模块度权重\n */\nconst iLouvain = (\n    graphData: GraphData,\n    directed: boolean = false,\n    weightPropertyName: string = 'weight',\n    threshold: number = 0.0001,\n    propertyKey: string = undefined,\n    involvedKeys: string[] = [],\n    uninvolvedKeys: string[] = ['id'],\n    inertialWeight: number = 1,\n  ): ClusterData => {\n  return louvain(graphData, directed, weightPropertyName, threshold, true, propertyKey, involvedKeys, uninvolvedKeys, inertialWeight);\n}\n\nexport default iLouvain;\n", "\nimport { clone } from '@antv/util';\nimport degree from './degree';\nimport { GraphData } from './types';\n/**\n *  k-core算法 找出符合指定核心度的紧密关联的子图结构\n * @param graphData 图数据\n * @param k 核心度数\n */\nconst kCore = (\n    graphData: GraphData,\n    k: number = 1,\n  ): GraphData => {\n    const data = clone(graphData);\n    const { nodes = [] } = data;\n    let { edges = [] } = data;\n    while (true) {\n        // 获取图中节点的度数\n        const degrees = degree({ nodes, edges});\n        const nodeIds = Object.keys(degrees);\n        // 按照度数进行排序\n        nodeIds.sort((a, b) => degrees[a]?.degree - degrees[b]?.degree);\n        const minIndexId = nodeIds[0];\n        if (!nodes.length || degrees[minIndexId]?.degree >= k) {\n            break;\n        }\n        const originIndex = nodes.findIndex(node => node.id === minIndexId);\n        // 移除度数小于k的节点\n        nodes.splice(originIndex, 1);\n        // 移除度数小于k的节点相关的边\n        edges = edges.filter(edge => !(edge.source === minIndexId || edge.target === minIndexId));\n    }\n    \n    return { nodes, edges };\n}\n\nexport default kCore;\n", "import { clone } from '@antv/util';\nimport { NodeConfig } from './types';\nimport { getAllProperties } from './utils/node-properties';\nimport { oneHot } from './utils/data-preprocessing';\nimport cosineSimilarity from './cosine-similarity';\n/**\n *  nodes-cosine-similarity算法 基于节点属性计算余弦相似度(基于种子节点寻找相似节点)\n * @param nodes 图节点数据\n * @param seedNode 种子节点\n * @param propertyKey 属性的字段名\n * @param involvedKeys 参与计算的key集合\n * @param uninvolvedKeys 不参与计算的key集合\n */\nconst nodesCosineSimilarity = (\n  nodes: NodeConfig[] = [],\n  seedNode: NodeConfig,\n  propertyKey: string = undefined,\n  involvedKeys: string[] = [],\n  uninvolvedKeys: string[] = [],\n): {\n  allCosineSimilarity: number[],\n  similarNodes: NodeConfig[],\n} => {\n  const similarNodes = clone(nodes.filter(node => node.id !== seedNode.id));\n  const seedNodeIndex = nodes.findIndex(node => node.id === seedNode.id);\n  // 所有节点属性集合\n  const properties = getAllProperties(nodes, propertyKey);\n  // 所有节点属性one-hot特征向量集合\n  const allPropertiesWeight = oneHot(properties, involvedKeys, uninvolvedKeys);\n  // 种子节点属性\n  const seedNodeProperties = allPropertiesWeight[seedNodeIndex];\n\n  const allCosineSimilarity: number[] = [];\n  similarNodes.forEach((node, index) => {\n    if (node.id !== seedNode.id) {\n      // 节点属性\n      const nodeProperties = allPropertiesWeight[index];\n      // 计算节点向量和种子节点向量的余弦相似度\n      const cosineSimilarityValue = cosineSimilarity(nodeProperties, seedNodeProperties);\n      allCosineSimilarity.push(cosineSimilarityValue);\n      node.cosineSimilarity = cosineSimilarityValue;\n    }\n  });\n\n  // 将返回的节点按照余弦相似度大小排序\n  similarNodes.sort((a, b) => b.cosineSimilarity - a.cosineSimilarity);\n  return { allCosineSimilarity, similarNodes };\n}\n\nexport default nodesCosineSimilarity;\n", "import { GraphData } from \"./types\";\nimport degree from './degree'\nimport { getNeighbors } from \"./util\";\n\n/**\n * PageRank https://en.wikipedia.org/wiki/PageRank\n * refer: https://github.com/anvaka/ngraph.pagerank\n * @param graph \n * @param epsilon 判断是否收敛的精度值，默认 0.000001\n * @param linkProb 阻尼系数（dumping factor），指任意时刻，用户访问到某节点后继续访问该节点链接的下一个节点的概率，经验值 0.85\n */\nconst pageRank = (graphData: GraphData, epsilon?: number, linkProb?: number): {\n  [key: string]: number;\n} => {\n  if (typeof epsilon !== 'number') epsilon = 0.000001;\n  if (typeof linkProb !== 'number') linkProb = 0.85;\n\n  let distance = 1;\n  let leakedRank = 0;\n  let maxIterations = 1000;\n\n  const { nodes = [], edges = [] } = graphData;\n  const nodesCount = nodes.length;\n  let currentRank;\n  const curRanks = {};\n  const prevRanks = {}\n\n  // Initialize pageranks 初始化\n  for (let j = 0; j < nodesCount; ++j) {\n    const node = nodes[j];\n    const nodeId = node.id;\n    curRanks[nodeId] = (1 / nodesCount)\n    prevRanks[nodeId] = (1 / nodesCount)\n  }\n\n  const nodeDegree = degree(graphData)\n  while (maxIterations > 0 && distance > epsilon) {\n    leakedRank = 0;\n    for (let j = 0; j < nodesCount; ++j) {\n      const node = nodes[j];\n      const nodeId = node.id;\n      currentRank = 0;\n      if (nodeDegree[node.id].inDegree === 0) {\n        curRanks[nodeId] = 0;\n      } else {\n        const neighbors = getNeighbors(nodeId, edges, 'source');\n        for (let i = 0; i < neighbors.length; ++i) {\n          const neighbor = neighbors[i];\n          const outDegree: number = nodeDegree[neighbor].outDegree;\n          if (outDegree > 0) currentRank += (prevRanks[neighbor] / outDegree);\n        }\n        curRanks[nodeId] = linkProb * currentRank;\n        leakedRank += curRanks[nodeId];\n      }\n    }\n\n    leakedRank = (1 - leakedRank) / nodesCount;\n    distance = 0;\n    for (let j = 0; j < nodesCount; ++j) {\n      const node = nodes[j];\n      const nodeId = node.id;\n      currentRank = curRanks[nodeId] + leakedRank;\n      distance += Math.abs(currentRank - prevRanks[nodeId]);\n      prevRanks[nodeId] = currentRank;\n    }\n    maxIterations -= 1\n  }\n\n  return prevRanks;\n}\n\nexport default pageRank\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "graphData", "directed", "nodes", "edges", "matrix", "nodeMap", "Error", "for<PERSON>ach", "node", "i", "id", "push", "edge", "source", "target", "sIndex", "tIndex", "defaultComparator", "a", "b", "value", "next", "toString", "callback", "comparator", "head", "tail", "compare", "prepend", "newNode", "LinkedListNode", "append", "delete", "deleteNode", "currentNode", "find", "undefined", "deleteTail", "deletedTail", "deleteHead", "deletedHead", "fromArray", "values", "toArray", "reverse", "prevNode", "nextNode", "map", "linkedList", "isEmpty", "peek", "enqueue", "dequeue", "removeHead", "getNeighbors", "nodeId", "type", "currentEdges", "filter", "getEdgesByNodeId", "uniqueId", "index", "random1", "Math", "random", "split", "substr", "random2", "detectStrongConnectComponents", "nodeStack", "inStack", "indices", "lowLink", "allComponents", "getComponent", "neighbors", "n", "indexOf", "targetNodeID", "min", "targetNode", "length", "component", "tmpNode", "pop", "getConnectedComponents", "visited", "neighbor", "detectConnectedComponents", "degree", "degrees", "inDegree", "outDegree", "depthFirstSearchRecursive", "previousNode", "callbacks", "enter", "current", "previous", "allowTraversal", "leave", "depthFirstSearch", "startNodeId", "seen", "initiatedCallback", "stub<PERSON><PERSON><PERSON>", "allowTraversalCallback", "detectAllUndirectedCycle", "nodeIds", "include", "allCycles", "rootId", "stack", "parent", "used", "Set", "curNode", "curNodeId", "neighborId", "has", "cycleValid", "cyclePath", "p", "size", "findIndex", "cycle", "add", "detectAllDirectedCycle", "path", "blocked", "B", "idx2Node", "node2Idx", "circuit", "start", "adjList", "closed", "thisNode", "clear", "unblock", "getMinComponentAdj", "components", "minCompIdx", "minIdx", "Infinity", "comp", "j", "c", "nodeIdx", "subgraphNodes", "sccs", "scc", "startNode", "dfsParentMap", "unvisitedSet", "visitingSet", "visitedSet", "currentCycleNode", "previousCycleNode", "keys", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "ar", "l", "Array", "slice", "concat", "create", "SuppressedError", "isType", "isArray", "arrPrototype", "splice", "str", "uniq", "arr", "cache", "Map", "r", "len", "item", "set", "Number", "isInteger", "PI", "clone", "rst", "k", "isFinite", "isEqual", "other", "isString", "isArrayLike", "isObjectLike", "valueKeys", "otherKeys", "ctx", "f", "resolver", "isFunction", "TypeError", "text", "font", "fontSize", "fontFamily", "fontWeight", "fontStyle", "fontVariant", "document", "createElement", "getContext", "join", "measureText", "width", "default_1", "def", "v", "weightPropertyName", "marks", "D", "prevs", "nodeNum", "minNode", "minDis", "minVertex", "minNodeId", "relatedEdges", "getOutEdgesNodeId", "edgeTarget", "edgeSource", "w", "weight", "paths", "find<PERSON>llP<PERSON>s", "allPath", "foundP<PERSON>s", "prevPaths", "prePath", "adjacentMatrix", "dist", "getArr", "otherVector", "otherArr", "Vector", "res", "subtract", "avg", "negate", "squareEuclideanDistance", "pow", "euclideanDistance", "sqrt", "console", "error", "normalize", "cloneArr", "sort", "max", "norm2", "dot", "equal", "DistanceType", "getAllProperties", "allProperties", "oneHot", "dataList", "<PERSON><PERSON><PERSON><PERSON>", "uninvolvedKeys", "allKeyValueMap", "data", "includes", "getAllKeyValueMap", "oneHotCode", "isAllNumber", "every", "code", "keyValue", "allKeyValue", "valueIndex", "subCode", "getDistance", "otherItem", "distanceType", "EuclideanDistance", "distance", "getModularity", "adjMatrix", "ks", "m", "param", "modularity", "clusteri", "clusterId", "getInertialModularity", "allPropertiesWeight", "totalProperties", "avgProperties", "variance", "propertiesi", "squareEuclideanDistanceInfo", "propertiesj", "inertialModularity", "clusterj", "clusterInertial", "toFixed", "threshold", "propertyKey", "inertialWeight", "properties", "originIndex", "nodeType", "clusters", "cid", "String", "idx", "row", "iid", "entry", "jid", "totalModularity", "previousModularity", "iter", "finalNodes", "finalClusters", "increaseWithinThreshold", "sumTot", "sourceClusterId", "targetClusterId", "bestCluster", "selfCluster", "bestIncrease", "commonParam", "kiin", "selfClusterNodes", "scNode", "scNodeIdx", "removeModurarity", "selfClusterNodesAfterRemove", "propertiesWeightRemove", "nodeRemove", "removeInertialModularity", "nodeNeighborIds", "neighborNodeId", "neighborClusterId", "neighborCluster", "clusterNodes", "neighborClusterKiin", "cNode", "cNodeIdx", "addModurarity", "clusterNodesAfterAdd", "propertiesWeightAdd", "nodeAdd", "addInertialModularity", "increase", "nodeInSelfClusterIdx", "newClusterIdMap", "clusterIdx", "cluster", "newId", "nodeInfo", "clusterEdges", "clusterEdgeMap", "newEdgeId", "count", "newEdge", "clustersArray", "getCentroid", "targetItem", "targetItemVector", "targetNodeNorm2", "itemVector", "itemNorm2", "norm2Product", "items", "union", "rootA", "rootB", "connected", "defaultCompare", "compareFn", "list", "getLeft", "getRight", "getParent", "floor", "top", "del<PERSON>in", "bottom", "moveDown", "insert", "moveUp", "tmp", "element", "left", "right", "primMST", "<PERSON><PERSON><PERSON>", "currNode", "edgeQueue", "currEdge", "kruskalMST", "weightEdges", "disjointSet", "curEdge", "shift", "VACANT_NODE_LABEL", "label", "edgeMap", "addEdge", "edgeIdAutoIncrease", "nodeLabelMap", "edgeLabelMap", "counter", "getNodeNum", "addNode", "Node", "Edge", "rEdge", "fromNode", "toNode", "fromNodeLabel", "edgeLabel", "toNodeLabel", "nodeEdgeNodeLabel", "nodeLabel1", "nodeLabel2", "equalTo", "formNode", "notEqualTo", "rmpath", "dfsEdgeList", "a<PERSON><PERSON><PERSON>", "pushBack", "DFSedge", "toGraph", "graphId", "graph", "Graph", "dfsEdge", "fromNodeId", "toNodeId", "buildRmpath", "oldFrom", "fromNodeIdx", "toNodeIdx", "pdfs", "his", "nodesUsed", "edgesUsed", "e", "preNode", "hasNode", "hasEdge", "graphs", "minSupport", "minNodeNum", "maxNodeNum", "verbose", "dfsCode", "DFScode", "support", "frequentSize1Subgraphs", "frequentSubgraphs", "reportDF", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "findBackwardEdge", "edge1", "edge2", "history", "edge2ToEdges", "edge<PERSON><PERSON><PERSON>", "findForwardPureEdges", "rightmostEdge", "minNodeLabel", "rightmostEdgeToId", "find<PERSON>orwardRmpath<PERSON>dges", "newToNodeLabel", "getSupport", "projected", "graphMap", "pro", "findMinLabel", "minLabel", "isMin", "log", "dfsCodeMin", "otherNode", "projectIsMin", "maxToC", "backwardRoot", "flag", "newTo", "end", "History", "backwardEdge", "minBackwardEdgeLabel", "forwardRoot", "newFrom", "forwardPureEdges", "<PERSON><PERSON><PERSON><PERSON>", "forwardRmpath<PERSON><PERSON>", "forwardMinEdgeNodeLabel", "report", "subGraphMining", "generate1EdgeFrequentSubGraphs", "nodeLabelCounter", "nodeEdgeNodeCounter", "nodeLableCounted", "nodeEdgeNodeLabelCounted", "nodeLabel", "graphNodeKey", "graph<PERSON>ey", "graphNodeEdgeNodeKey", "nodeEdgeNodeKey", "g", "run", "DEFAULT_LABEL_NAME", "findKNeighborUnits", "spm", "nodeLabelProp", "units", "findKNeighborUnit", "unitNodeIdxs", "labelCountMap", "dists", "nodeIdxs", "neighborNum", "nodeLabelCountMap", "getIntersectNeighborInducedGraph", "nodePairMap", "neighborUnits", "cachedInducedGraphMap", "pair", "startUnitNodeIds", "endUnitNodeIds", "endSet", "intersect", "x", "intersectIdMap", "intersectLength", "getMatchedCount", "structure", "edgeLabelProp", "sourceLabel", "targetLabel", "strNodeLabel1", "strNodeLabel2", "strEdge<PERSON>abel", "getNodeMaps", "getEdgeMaps", "sourceNode", "getSpmMap", "iId", "jId", "getNDSDist", "node1", "node2", "spDist", "kNeighborUnits", "cachedNDSMap", "cachedInterInducedGraph", "interInducedGraph", "pairMap", "stashPatternNodeLabelDegreeMap", "minPatternNodeLabelDegreeMap", "neighborLabel", "patternNodeMap", "patternNodeLabelMap", "minPatternNodeLabelDegree", "minPatternNodeLabelInDegree", "minPatternNodeLabelOutDegree", "patternNodeWithLabel", "patternNodeDegree", "patternNodeInDegree", "patternNodeOutDegree", "maxStep", "isMaxStack", "getAdjMatrix", "breadthFirstSearch", "originalCallbacks", "initCallbacks", "nodeQueue", "connectedComponent", "getDegree", "getInDegree", "getOutDegree", "detectCycle", "detectDirectedCycle", "detectAllCycles", "<PERSON><PERSON><PERSON>", "find<PERSON>ll<PERSON>ath", "isVisited", "children", "child", "findShortestPath", "<PERSON>oyd<PERSON><PERSON><PERSON>", "labelPropagation", "maxIteration", "changed", "neighborClusters", "neighborWeight", "maxWeight", "bestClusterIds", "selfClusterIdx", "randomIdx", "louvain", "iLouvain", "kCore", "minIndexId", "kMeans", "defaultClusterInfo", "allPropertiesWeightUniq", "finalK", "centroids", "centroidIndexList", "randomIndex", "maxDistance", "maxDistanceNodeIndex", "totalDistance", "avgDistance", "centroid", "iterations", "minDistanceIndex", "minDistance", "centroidsEqualAvg", "totalVector", "avgVector", "cosineSimilarity", "nodesCosineSimilarity", "seedNode", "similarNodes", "seedNodeIndex", "seedNodeProperties", "allCosineSimilarity", "nodeProperties", "cosineSimilarityValue", "minimumSpanningTree", "algo", "prim", "kruskal", "pageRank", "epsilon", "linkProb", "currentRank", "leakedRank", "maxIterations", "nodesCount", "curRanks", "prevRanks", "nodeDegree", "abs", "<PERSON><PERSON>", "GADDI", "pattern", "patternSpm", "spmMap", "patternSpmMap", "patternEdgeLabelMap", "patternSpmSpread", "patternKNeighborUnits", "nodePairsMap", "maxNodePairNum", "nodePairNumEachNode", "ceil", "foundNodePairCount", "unit", "nodePairForICount", "outerLoopCount", "oidx", "innerLoopCount", "findNodePairsRandomly", "intGMap", "freStructures", "params", "formattedGraphs", "fGraph", "nodeIdxMap", "sourceIdx", "targetIdx", "formatGraphs", "calculator", "GSpan", "toGraphDatas", "structureNum", "matchedCountMap", "subStructureCount", "structures", "maxOffset", "representClusterType", "countMapI", "sortedGraphKeys", "totalCount", "aveCount", "aveIntraDist", "aveCounts", "graphsInCluster", "aveIntraPerCluster", "graphsNum", "graphKey1", "graph1Count", "graphKey2", "aveInterDist", "aveCount1", "aveCount2", "offset", "structureCountMap", "findRepresentStructure", "dsG", "ndsDist", "beginPNode", "candidates", "maxNodeNumWithSameLabel", "p<PERSON><PERSON><PERSON>", "nodesWithSameLabel", "patternIntGraphMap", "patternNDSDist", "patternNDSDistMap", "patternSpDist", "patternSpDistBack", "label2", "maxDist", "patternNodesWithLabel2", "patternNodePairMap", "nodeWithLabel2", "distBack", "currentPatternNDSDistArray", "patternIntGraph", "graphNeighborUnit", "graphNeighborUnitCountMap", "patternLabel2Num", "prune2Invalid", "cNodePairMap", "neighborNode", "currentNDSDistArray", "intGraph", "prune3Invalid", "candidateGraphs", "candidate", "neighborNodes", "unmatched", "distToCandidate", "keyBack", "distFromCandidate", "ndsToCandidate", "pattern<PERSON>ey", "undirectedLengthsToBeginPNode", "undirectedLengthsToBeginPNodeLabelMap", "<PERSON><PERSON><PERSON><PERSON>", "candidateNodeLabelCountMap", "candidateNodeMap", "q", "cNodeLabel", "candidate<PERSON><PERSON>", "edgeLabelCountMap", "pattenr<PERSON>dge<PERSON>abel<PERSON>um", "pruned<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "candidateGraphInvalid", "patternEdgesWithLabel", "edgeMatched", "patternEdge", "patternSource", "patternTarget", "lengthsToCandidate", "targetId", "targetNodeLabel", "nLabel", "degreeChanged", "loopCount", "cgNode", "nodeInDegree", "nodeOutDegree", "y", "cedge", "<PERSON><PERSON><PERSON><PERSON>", "cg1", "cg1EdgeMap", "cg2", "cg2EdgeMap", "same"], "sourceRoot": ""}