import type { ComputePipeline, ComputePipelineDescriptor } from '../api';
import { ResourceType } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export declare class ComputePipeline_GL extends ResourceBase_GL implements ComputePipeline {
    type: ResourceType.ComputePipeline;
    descriptor: ComputePipelineDescriptor;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: ComputePipelineDescriptor;
    });
}
