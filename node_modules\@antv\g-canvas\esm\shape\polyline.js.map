{"version": 3, "file": "polyline.js", "sourceRoot": "", "sources": ["../../src/shape/polyline.ts"], "names": [], "mappings": ";AAKA,OAAO,EAAE,IAAI,IAAI,QAAQ,EAAE,MAAM,cAAc,CAAC;AAChD,OAAO,EAAE,QAAQ,IAAI,YAAY,EAAE,MAAM,cAAc,CAAC;AACxD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,SAAS,MAAM,QAAQ,CAAC;AAC/B,OAAO,UAAU,MAAM,4BAA4B,CAAC;AACpD,OAAO,KAAK,SAAS,MAAM,eAAe,CAAC;AAE3C;IAAuB,4BAAS;IAAhC;;IAuMA,CAAC;IAtMC,kCAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6BACK,KAAK,KACR,UAAU,EAAE,KAAK,EACjB,QAAQ,EAAE,KAAK,IACf;IACJ,CAAC;IAED,4BAAS,GAAT,UAAU,KAAK;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,uBAAuB;IACvB,+BAAY,GAAZ,UAAa,IAAY,EAAE,KAAU,EAAE,WAAgB;QACrD,iBAAM,YAAY,YAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACnC,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;IACH,CAAC;IAED,8BAAW,GAAX;QACE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,2BAAQ,GAAR;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,IAAA,KAAmC,IAAI,CAAC,KAAK,EAA3C,MAAM,YAAA,EAAE,UAAU,gBAAA,EAAE,QAAQ,cAAe,CAAC;QACpD,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,IAAM,EAAE,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,IAAM,EAAE,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjC,IAAI,UAAU,EAAE;YACd,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SAC1E;QACD,IAAI,QAAQ,EAAE;YACZ,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SAC1F;IACH,CAAC;IAED,WAAW;IACX,yBAAM,GAAN;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,mCAAgB,GAAhB,UAAiB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;QAChD,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QACO,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,EAAE,OAAhB,CAAiB;QAC/B,OAAO,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,OAAO;IACP,2BAAQ,GAAR;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6BAAU,GAAV,UAAW,OAAO;QACV,IAAA,KAAmC,IAAI,CAAC,IAAI,EAAE,EAA5C,MAAM,YAAA,EAAE,UAAU,gBAAA,EAAE,QAAQ,cAAgB,CAAC;QACrD,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO;SACR;QACD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,0BAA0B;QAC1B,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,EAAE;YAC9B,IAAM,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;YAC9F,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC;YAClB,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC;SACnB;QACD,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,EAAE;YAC1B,IAAM,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC9G,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC;YAClB,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC;SACnB;QAED,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACpC;QACD,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACzB,CAAC;IAED,gCAAa,GAAb,UAAc,OAAiC;QAC7C,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAChD,IAAI,eAAe,EAAE;YACnB,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/B;QACD,IAAI,aAAa,EAAE;YACjB,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC;IAED;;;OAGG;IACH,iCAAc,GAAd;QACU,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,EAAE,OAAhB,CAAiB;QAC/B,6BAA6B;QAC7B,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YACvB,OAAO,WAAW,CAAC;SACpB;QACD,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,2BAAQ,GAAR,UAAS,KAAa;QACZ,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,EAAE,OAAhB,CAAiB;QAC/B,wBAAwB;QACxB,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC;QACT,IAAI,KAAK,CAAC;QACV,IAAI,CAAC,MAAM,EAAE,UAAC,CAAC,EAAE,CAAC;YAChB,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;gBAClC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,KAAK,GAAG,CAAC,CAAC;aACX;QACH,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAChH,CAAC;IAED,6BAAU,GAAV;QACU,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,EAAE,OAAhB,CAAiB;QAC/B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,OAAO;SACR;QAED,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,WAAW,IAAI,CAAC,EAAE;YACpB,OAAO;SACR;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,QAAQ,CAAC;QACb,IAAI,QAAQ,CAAC;QAEb,IAAI,CAAC,MAAM,EAAE,UAAC,CAAC,EAAE,CAAC;YAChB,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;gBACjB,QAAQ,GAAG,EAAE,CAAC;gBACd,QAAQ,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,WAAW,CAAC;gBACvC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3E,UAAU,IAAI,QAAQ,CAAC;gBACvB,QAAQ,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,WAAW,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,kCAAe,GAAf;QACU,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,EAAE,OAAhB,CAAiB;QAC/B,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,gCAAa,GAAb;QACU,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,EAAE,OAAhB,CAAiB;QAC/B,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC;IAChB,CAAC;IACH,eAAC;AAAD,CAAC,AAvMD,CAAuB,SAAS,GAuM/B;AAED,eAAe,QAAQ,CAAC"}