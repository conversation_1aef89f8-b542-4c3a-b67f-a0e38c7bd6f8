{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nvar PolarAxisModel = /** @class */function (_super) {\n  __extends(PolarAxisModel, _super);\n  function PolarAxisModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PolarAxisModel.prototype.getCoordSysModel = function () {\n    return this.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n  };\n  PolarAxisModel.type = 'polarAxis';\n  return PolarAxisModel;\n}(ComponentModel);\nzrUtil.mixin(PolarAxisModel, AxisModelCommonMixin);\nexport { PolarAxisModel };\nvar AngleAxisModel = /** @class */function (_super) {\n  __extends(AngleAxisModel, _super);\n  function AngleAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AngleAxisModel.type;\n    return _this;\n  }\n  AngleAxisModel.type = 'angleAxis';\n  return AngleAxisModel;\n}(PolarAxisModel);\nexport { AngleAxisModel };\nvar RadiusAxisModel = /** @class */function (_super) {\n  __extends(RadiusAxisModel, _super);\n  function RadiusAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadiusAxisModel.type;\n    return _this;\n  }\n  RadiusAxisModel.type = 'radiusAxis';\n  return RadiusAxisModel;\n}(PolarAxisModel);\nexport { RadiusAxisModel };", "map": {"version": 3, "names": ["__extends", "zrUtil", "ComponentModel", "AxisModelCommonMixin", "SINGLE_REFERRING", "PolarAxisModel", "_super", "apply", "arguments", "prototype", "getCoordSysModel", "getReferringComponents", "models", "type", "mixin", "AngleAxisModel", "_this", "RadiusAxisModel"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/coord/polar/AxisModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nvar PolarAxisModel = /** @class */function (_super) {\n  __extends(PolarAxisModel, _super);\n  function PolarAxisModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PolarAxisModel.prototype.getCoordSysModel = function () {\n    return this.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n  };\n  PolarAxisModel.type = 'polarAxis';\n  return PolarAxisModel;\n}(ComponentModel);\nzrUtil.mixin(PolarAxisModel, AxisModelCommonMixin);\nexport { PolarAxisModel };\nvar AngleAxisModel = /** @class */function (_super) {\n  __extends(AngleAxisModel, _super);\n  function AngleAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AngleAxisModel.type;\n    return _this;\n  }\n  AngleAxisModel.type = 'angleAxis';\n  return AngleAxisModel;\n}(PolarAxisModel);\nexport { AngleAxisModel };\nvar RadiusAxisModel = /** @class */function (_super) {\n  __extends(RadiusAxisModel, _super);\n  function RadiusAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadiusAxisModel.type;\n    return _this;\n  }\n  RadiusAxisModel.type = 'radiusAxis';\n  return RadiusAxisModel;\n}(PolarAxisModel);\nexport { RadiusAxisModel };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDN,SAAS,CAACK,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,cAAc,CAACI,SAAS,CAACC,gBAAgB,GAAG,YAAY;IACtD,OAAO,IAAI,CAACC,sBAAsB,CAAC,OAAO,EAAEP,gBAAgB,CAAC,CAACQ,MAAM,CAAC,CAAC,CAAC;EACzE,CAAC;EACDP,cAAc,CAACQ,IAAI,GAAG,WAAW;EACjC,OAAOR,cAAc;AACvB,CAAC,CAACH,cAAc,CAAC;AACjBD,MAAM,CAACa,KAAK,CAACT,cAAc,EAAEF,oBAAoB,CAAC;AAClD,SAASE,cAAc;AACvB,IAAIU,cAAc,GAAG,aAAa,UAAUT,MAAM,EAAE;EAClDN,SAAS,CAACe,cAAc,EAAET,MAAM,CAAC;EACjC,SAASS,cAAcA,CAAA,EAAG;IACxB,IAAIC,KAAK,GAAGV,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEQ,KAAK,CAACH,IAAI,GAAGE,cAAc,CAACF,IAAI;IAChC,OAAOG,KAAK;EACd;EACAD,cAAc,CAACF,IAAI,GAAG,WAAW;EACjC,OAAOE,cAAc;AACvB,CAAC,CAACV,cAAc,CAAC;AACjB,SAASU,cAAc;AACvB,IAAIE,eAAe,GAAG,aAAa,UAAUX,MAAM,EAAE;EACnDN,SAAS,CAACiB,eAAe,EAAEX,MAAM,CAAC;EAClC,SAASW,eAAeA,CAAA,EAAG;IACzB,IAAID,KAAK,GAAGV,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEQ,KAAK,CAACH,IAAI,GAAGI,eAAe,CAACJ,IAAI;IACjC,OAAOG,KAAK;EACd;EACAC,eAAe,CAACJ,IAAI,GAAG,YAAY;EACnC,OAAOI,eAAe;AACxB,CAAC,CAACZ,cAAc,CAAC;AACjB,SAASY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}