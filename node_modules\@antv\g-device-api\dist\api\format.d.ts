import { SamplerFormatKind } from './interfaces';
export declare enum FormatTypeFlags {
    U8 = 1,
    U16 = 2,
    U32 = 3,
    S8 = 4,
    S16 = 5,
    S32 = 6,
    F16 = 7,
    F32 = 8,
    BC1 = 65,
    BC2 = 66,
    BC3 = 67,
    BC4_UNORM = 68,
    BC4_SNORM = 69,
    BC5_UNORM = 70,
    BC5_SNORM = 71,
    U16_PACKED_5551 = 97,
    U16_PACKED_565 = 98,
    D24 = 129,
    D32F = 130,
    D24S8 = 131,
    D32FS8 = 132
}
export declare enum FormatCompFlags {
    R = 1,
    RG = 2,
    RGB = 3,
    RGBA = 4,
    A = 5
}
export declare function getFormatCompFlagsComponentCount(n: FormatCompFlags): number;
export declare enum FormatFlags {
    None = 0,
    Normalized = 1,
    sRGB = 2,
    Depth = 4,
    Stencil = 8,
    RenderTarget = 16,
    Luminance = 32
}
export declare function makeFormat(type: FormatTypeFlags, comp: FormatCompFlags, flags: FormatFlags): Format;
export declare enum Format {
    ALPHA,
    U8_LUMINANCE,
    F16_LUMINANCE,
    F32_LUMINANCE,
    F16_R,
    F16_RG,
    F16_RGB,
    F16_RGBA,
    F32_R,
    F32_RG,
    F32_RGB,
    F32_RGBA,
    U8_R,
    U8_R_NORM,
    U8_RG,
    U8_RG_NORM,
    U8_RGB,
    U8_RGB_NORM,
    U8_RGB_SRGB,
    U8_RGBA,
    U8_RGBA_NORM,
    U8_RGBA_SRGB,
    U16_R,
    U16_R_NORM,
    U16_RG_NORM,
    U16_RGBA_NORM,
    U16_RGBA,
    U16_RGB,
    U16_RG,
    U32_R,
    U32_RG,
    U32_RGB,
    U32_RGBA,
    S8_R,
    S8_R_NORM,
    S8_RG_NORM,
    S8_RGB_NORM,
    S8_RGBA_NORM,
    S16_R,
    S16_RG,
    S16_RG_NORM,
    S16_RGB_NORM,
    S16_RGBA,
    S16_RGBA_NORM,
    S32_R,
    S32_RG,
    S32_RGB,
    S32_RGBA,
    U16_RGBA_5551,
    U16_RGB_565,
    BC1,
    BC1_SRGB,
    BC2,
    BC2_SRGB,
    BC3,
    BC3_SRGB,
    BC4_UNORM,
    BC4_SNORM,
    BC5_UNORM,
    BC5_SNORM,
    D24,
    D24_S8,
    D32F,
    D32F_S8,
    U8_RGB_RT,
    U8_RGBA_RT,
    U8_RGBA_RT_SRGB
}
export declare function getFormatCompFlags(fmt: Format): FormatCompFlags;
export declare function getFormatTypeFlags(fmt: Format): FormatTypeFlags;
export declare function getFormatFlags(fmt: Format): FormatFlags;
export declare function getFormatTypeFlagsByteSize(typeFlags: FormatTypeFlags): 1 | 2 | 4;
/**
 * Gets the byte size for an individual component.
 * e.g. for F32_RGB, this will return "4", since F32 has 4 bytes.
 */
export declare function getFormatCompByteSize(fmt: Format): 1 | 2 | 4;
export declare function getFormatComponentCount(fmt: Format): number;
export declare function getFormatByteSize(fmt: Format): number;
export declare function setFormatFlags(fmt: Format, flags: FormatFlags): Format;
export declare function setFormatComponentCount(fmt: Format, compFlags: FormatCompFlags): Format;
export declare function getFormatSamplerKind(fmt: Format): SamplerFormatKind;
