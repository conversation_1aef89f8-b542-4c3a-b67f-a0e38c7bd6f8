{"ast": null, "code": "import _objectSpread from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport request from '@/utils/request';\nexport default {\n  name: 'OptionsOrder',\n  data: function data() {\n    return {\n      loading: false,\n      total: 0,\n      orderList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        symbol: '',\n        winOrLose: '',\n        leverType: '',\n        positionStatus: '',\n        status: '',\n        username: '',\n        email: '',\n        userNo: ''\n      },\n      detailDialogVisible: false,\n      detailRow: {}\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      var params = _objectSpread({}, this.queryParams);\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n      request({\n        url: '/api/futuresOptionOrder/list',\n        method: 'get',\n        params: params\n      }).then(function (res) {\n        _this.orderList = res.records || [];\n        _this.total = res.total || 0;\n      })[\"finally\"](function () {\n        _this.loading = false;\n      });\n    },\n    handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery: function resetQuery() {\n      this.dateRange = [];\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        symbol: '',\n        winOrLose: '',\n        leverType: '',\n        positionStatus: '',\n        status: '',\n        username: '',\n        email: '',\n        userNo: ''\n      };\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.queryParams.pageSize = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.getList();\n    },\n    formatDateTime: function formatDateTime(val) {\n      if (!val) return '';\n      // 兼容带T的时间字符串，转为 'YYYY-MM-DD HH:mm:ss'\n      return val.replace('T', ' ').slice(0, 19);\n    },\n    showDetail: function showDetail(row) {\n      this.detailRow = row;\n      this.detailDialogVisible = true;\n    },\n    getStatusText: function getStatusText(status) {\n      var map = {\n        0: '持仓中',\n        1: '已结算',\n        2: '结算中'\n      };\n      return map[status] || '未知';\n    },\n    getStatusType: function getStatusType(status) {\n      var map = {\n        0: 'warning',\n        1: 'success',\n        2: 'info'\n      };\n      return map[status] || 'info';\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "loading", "total", "orderList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "symbol", "winOr<PERSON>ose", "leverType", "positionStatus", "status", "username", "email", "userNo", "detailDialogVisible", "detailRow", "created", "getList", "methods", "_this", "params", "_objectSpread", "length", "startTime", "endTime", "url", "method", "then", "res", "records", "handleQuery", "reset<PERSON><PERSON>y", "handleSizeChange", "val", "handleCurrentChange", "formatDateTime", "replace", "slice", "showDetail", "row", "getStatusText", "map", "getStatusType"], "sources": ["src/views/exchange/options/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"8\" class=\"filter-row\" type=\"flex\" align=\"middle\">\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"queryParams.username\"\r\n              placeholder=\"请输入用户名（精确查询）\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 100%;\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"queryParams.email\"\r\n              placeholder=\"请输入邮箱（精确查询）\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 100%;\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"queryParams.userNo\"\r\n              placeholder=\"请输入UID（精确查询）\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 100%;\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"请选择订单状态\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 100%;\"\r\n            >\r\n              <el-option label=\"持仓中\" :value=\"0\" />\r\n              <el-option label=\"已结算\" :value=\"1\" />\r\n              <el-option label=\"结算中\" :value=\"2\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-select\r\n              v-model=\"queryParams.direction\"\r\n              placeholder=\"请选择方向\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 100%;\"\r\n            >\r\n              <el-option label=\"全部\" :value=\"''\" />\r\n              <el-option label=\"买涨\" value=\"up\" />\r\n              <el-option label=\"买跌\" value=\"down\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              class=\"filter-item\"\r\n              style=\"width: 100%;\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"2\" style=\"display: flex; gap: 8px;\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"orderList\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"UID\" align=\"center\" prop=\"user_no\" min-width=\"100\" />\r\n        <el-table-column label=\"用户名\" align=\"center\" prop=\"username\" min-width=\"120\" />\r\n        <el-table-column label=\"邮箱\" align=\"center\" prop=\"email\" min-width=\"180\" />\r\n        <!-- <el-table-column label=\"订单号\" align=\"center\" prop=\"id\" min-width=\"80\" /> -->\r\n        <el-table-column label=\"用户ID\" align=\"center\" prop=\"user_id\" min-width=\"80\" />\r\n        <el-table-column label=\"交易对\" align=\"center\" prop=\"symbol\" min-width=\"100\" />\r\n        <el-table-column label=\"做多/做空\" align=\"center\" prop=\"win_or_lose\" min-width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.win_or_lose === 0 ? 'success' : 'danger'\">\r\n              {{ scope.row.win_or_lose === 0 ? '做多' : '做空' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"杠杆类型\" align=\"center\" prop=\"lever_type\" min-width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.lever_type === 0 ? 'x5' : 'x10' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"保证金额\" align=\"center\" prop=\"amount\" min-width=\"100\" />\r\n        <el-table-column label=\"持仓价格\" align=\"center\" prop=\"open_price\" min-width=\"100\" />\r\n        <el-table-column label=\"持仓时间\" align=\"center\" prop=\"position_time\" min-width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.position_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"平仓价格\" align=\"center\" prop=\"close_price\" min-width=\"100\" />\r\n        <el-table-column label=\"平仓时间\" align=\"center\" prop=\"position_end_time\" min-width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.position_end_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"持仓个数\" align=\"center\" prop=\"position_count\" min-width=\"80\" />\r\n        <el-table-column label=\"持仓状态\" align=\"center\" prop=\"position_status\" min-width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.position_status === 0\" type=\"info\">未平仓</el-tag>\r\n            <el-tag v-else-if=\"scope.row.position_status === 1\" type=\"success\">已平仓</el-tag>\r\n            <el-tag v-else-if=\"scope.row.position_status === 2\" type=\"danger\">已止损</el-tag>\r\n            <el-tag v-else-if=\"scope.row.position_status === 3\" type=\"warning\">已止盈</el-tag>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"止盈\" align=\"center\" prop=\"take_profit\" min-width=\"100\" />\r\n        <el-table-column label=\"止损\" align=\"center\" prop=\"stop_loss\" min-width=\"100\" />\r\n        <el-table-column label=\"是否止盈止损\" align=\"center\" prop=\"is_stop_profit\" min-width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.is_stop_profit === 1 ? '是' : '否' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"盈亏金额\" align=\"center\" prop=\"profit\" min-width=\"100\" />\r\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"status\" min-width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.status === 0\" type=\"info\">持仓中</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status === 1\" type=\"success\">已结算</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status === 2\" type=\"warning\">结算中</el-tag>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"120\" />\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"create_time\" min-width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"更新时间\" align=\"center\" prop=\"update_time\" min-width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.update_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"primary\" size=\"mini\" @click=\"showDetail(scope.row)\">详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <!-- 详情弹窗 -->\r\n      <el-dialog :visible.sync=\"detailDialogVisible\" title=\"订单详情\" width=\"800px\">\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"UID\">{{ detailRow.user_no }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户名\">{{ detailRow.username }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"邮箱\">{{ detailRow.email }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"订单号\">{{ detailRow.id }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户ID\">{{ detailRow.user_id }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"交易对\">{{ detailRow.symbol }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"做多/做空\">\r\n            <el-tag :type=\"detailRow.win_or_lose === 0 ? 'success' : 'danger'\">\r\n              {{ detailRow.win_or_lose === 0 ? '做多' : '做空' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"杠杆类型\">{{ detailRow.lever_type === 0 ? 'x5' : 'x10' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"保证金额\">{{ detailRow.amount }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"持仓价格\">{{ detailRow.open_price }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"持仓时间\">{{ formatDateTime(detailRow.position_time) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"平仓价格\">{{ detailRow.close_price }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"平仓时间\">{{ formatDateTime(detailRow.position_end_time) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"持仓个数\">{{ detailRow.position_count }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"持仓状态\">\r\n            <el-tag v-if=\"detailRow.position_status === 0\" type=\"info\">未平仓</el-tag>\r\n            <el-tag v-else-if=\"detailRow.position_status === 1\" type=\"success\">已平仓</el-tag>\r\n            <el-tag v-else-if=\"detailRow.position_status === 2\" type=\"danger\">已止损</el-tag>\r\n            <el-tag v-else-if=\"detailRow.position_status === 3\" type=\"warning\">已止盈</el-tag>\r\n            <span v-else>-</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"止盈\">{{ detailRow.take_profit }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"止损\">{{ detailRow.stop_loss }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"是否止盈止损\">{{ detailRow.is_stop_profit === 1 ? '是' : '否' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"盈亏金额\">{{ detailRow.profit }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"订单状态\">\r\n            <el-tag v-if=\"detailRow.status === 0\" type=\"info\">持仓中</el-tag>\r\n            <el-tag v-else-if=\"detailRow.status === 1\" type=\"success\">已结算</el-tag>\r\n            <el-tag v-else-if=\"detailRow.status === 2\" type=\"warning\">结算中</el-tag>\r\n            <span v-else>-</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"备注\">{{ detailRow.remark }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\">{{ formatDateTime(detailRow.create_time) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"更新时间\">{{ formatDateTime(detailRow.update_time) }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </el-dialog>\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"queryParams.pageNum\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"queryParams.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request'\r\nexport default {\r\n  name: 'OptionsOrder',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      total: 0,\r\n      orderList: [],\r\n      dateRange: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        symbol: '',\r\n        winOrLose: '',\r\n        leverType: '',\r\n        positionStatus: '',\r\n        status: '',\r\n        username: '',\r\n        email: '',\r\n        userNo: '',\r\n      },\r\n      detailDialogVisible: false,\r\n      detailRow: {},\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true\r\n      const params = {\r\n        ...this.queryParams\r\n      }\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.startTime = this.dateRange[0]\r\n        params.endTime = this.dateRange[1]\r\n      }\r\n      request({\r\n        url: '/api/futuresOptionOrder/list',\r\n        method: 'get',\r\n        params\r\n      }).then(res => {\r\n        this.orderList = res.records || []\r\n        this.total = res.total || 0\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        symbol: '',\r\n        winOrLose: '',\r\n        leverType: '',\r\n        positionStatus: '',\r\n        status: '',\r\n        username: '',\r\n        email: '',\r\n        userNo: '',\r\n      }\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.queryParams.pageSize = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.queryParams.pageNum = val\r\n      this.getList()\r\n    },\r\n    formatDateTime(val) {\r\n      if (!val) return '';\r\n      // 兼容带T的时间字符串，转为 'YYYY-MM-DD HH:mm:ss'\r\n      return val.replace('T', ' ').slice(0, 19);\r\n    },\r\n    showDetail(row) {\r\n      this.detailRow = row;\r\n      this.detailDialogVisible = true;\r\n    },\r\n    getStatusText(status) {\r\n      const map = {0: '持仓中', 1: '已结算', 2: '结算中'}\r\n      return map[status] || '未知'\r\n    },\r\n    getStatusType(status) {\r\n      const map = {0: 'warning', 1: 'success', 2: 'info'}\r\n      return map[status] || 'info'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n}\r\n.filter-item {\r\n  width: 100%;\r\n}\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n.text-success {\r\n  color: #67c23a;\r\n}\r\n.text-danger {\r\n  color: #f56c6c;\r\n}\r\n.table-wrapper {\r\n  overflow-x: auto;\r\n}\r\n</style> "], "mappings": ";;;;;AAgOA,OAAAA,OAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;QACAC,SAAA;QACAC,cAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,mBAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApB,OAAA;MACA,IAAAqB,MAAA,GAAAC,aAAA,KACA,KAAAlB,WAAA,CACA;MACA,SAAAD,SAAA,SAAAA,SAAA,CAAAoB,MAAA;QACAF,MAAA,CAAAG,SAAA,QAAArB,SAAA;QACAkB,MAAA,CAAAI,OAAA,QAAAtB,SAAA;MACA;MACAN,OAAA;QACA6B,GAAA;QACAC,MAAA;QACAN,MAAA,EAAAA;MACA,GAAAO,IAAA,WAAAC,GAAA;QACAT,KAAA,CAAAlB,SAAA,GAAA2B,GAAA,CAAAC,OAAA;QACAV,KAAA,CAAAnB,KAAA,GAAA4B,GAAA,CAAA5B,KAAA;MACA;QACAmB,KAAA,CAAApB,OAAA;MACA;IACA;IACA+B,WAAA,WAAAA,YAAA;MACA,KAAA3B,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACAc,UAAA,WAAAA,WAAA;MACA,KAAA7B,SAAA;MACA,KAAAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;QACAC,SAAA;QACAC,cAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACA,KAAAI,OAAA;IACA;IACAe,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAA9B,WAAA,CAAAE,QAAA,GAAA4B,GAAA;MACA,KAAAhB,OAAA;IACA;IACAiB,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAA9B,WAAA,CAAAC,OAAA,GAAA6B,GAAA;MACA,KAAAhB,OAAA;IACA;IACAkB,cAAA,WAAAA,eAAAF,GAAA;MACA,KAAAA,GAAA;MACA;MACA,OAAAA,GAAA,CAAAG,OAAA,WAAAC,KAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAxB,SAAA,GAAAwB,GAAA;MACA,KAAAzB,mBAAA;IACA;IACA0B,aAAA,WAAAA,cAAA9B,MAAA;MACA,IAAA+B,GAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAA/B,MAAA;IACA;IACAgC,aAAA,WAAAA,cAAAhC,MAAA;MACA,IAAA+B,GAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAA/B,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}