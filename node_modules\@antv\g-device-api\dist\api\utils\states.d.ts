import type { AttachmentState, MegaStateDescriptor, SamplerBinding, StencilFaceState } from '../interfaces';
import { BlendFactor, BlendMode, ChannelWriteMask } from '../interfaces';
export declare function isPowerOfTwo(n: number): boolean;
export declare function fallbackUndefined<T>(v: T | null | undefined, fallback: T): T;
export declare function nullify<T>(v: T | undefined | null): T | null;
export declare function fillArray<T>(L: T[], n: number, v: T): void;
export declare function align(n: number, multiple: number): number;
export declare function alignNonPowerOfTwo(n: number, multiple: number): number;
export declare function bisectRight<T>(L: T[], e: T, compare: (a: T, b: T) => number): number;
export declare function spliceBisectRight<T>(L: T[], e: T, compare: (a: T, b: T) => number): void;
export declare function setBitFlagEnabled(v: number, mask: number, enabled: boolean): number;
export declare function nArray<T>(n: number, c: () => T): T[];
export declare function prependLineNo(str: string, lineStart?: number): string;
export declare function leftPad(S: string, spaces: number, ch?: string): string;
export declare function range(start: number, count: number): number[];
export declare function copyStencilFaceState(dst: Partial<StencilFaceState> | undefined, src: Partial<StencilFaceState>): Partial<StencilFaceState>;
export declare function copyAttachmentState(dst: AttachmentState | undefined, src: AttachmentState): AttachmentState;
export declare function setMegaStateFlags(dst: MegaStateDescriptor, src: Partial<MegaStateDescriptor>): void;
export declare function copyMegaState(src: MegaStateDescriptor): MegaStateDescriptor;
export interface AttachmentStateSimple {
    channelWriteMask: ChannelWriteMask;
    rgbBlendMode?: BlendMode;
    alphaBlendMode?: BlendMode;
    rgbBlendSrcFactor?: BlendFactor;
    alphaBlendSrcFactor?: BlendFactor;
    rgbBlendDstFactor?: BlendFactor;
    alphaBlendDstFactor?: BlendFactor;
}
export declare function copyAttachmentStateFromSimple(dst: AttachmentState, src: Partial<AttachmentStateSimple>): void;
export declare const defaultMegaState: MegaStateDescriptor;
export declare function makeMegaState(other?: Partial<MegaStateDescriptor> | null, src?: MegaStateDescriptor): MegaStateDescriptor;
export declare const fullscreenMegaState: MegaStateDescriptor;
export declare function setAttachmentStateSimple(dst: Partial<MegaStateDescriptor>, simple: Partial<AttachmentStateSimple>): Partial<MegaStateDescriptor>;
export declare const defaultBindingLayoutSamplerDescriptor: SamplerBinding;
