{"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../src/util/text.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AAGlD;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,IAAY,EAAE,QAAgB,EAAE,UAAmB;IAC/E,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;KACrC;IACD,IAAI,SAAS,GAAG,CAAC,EAAE;QACjB,IAAM,SAAS,GAAG,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACxD,OAAO,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;KAC3D;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,QAAgB,EAAE,UAAmB;IACnE,OAAO,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9D,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,IAAY,EAAE,IAAY;IACrD,IAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC,CAAC,iBAAiB;IACxD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,4BAA4B;IAC5B,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,EAAE;QAC9B,OAAO,KAAK,CAAC;KACd;IACD,OAAO,CAAC,IAAI,EAAE,CAAC;IACf,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACzC,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,EAAE,UAAC,OAAO;YACpB,IAAM,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;YACxD,IAAI,KAAK,GAAG,YAAY,EAAE;gBACxB,KAAK,GAAG,YAAY,CAAC;aACtB;QACH,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;KACzC;IACD,OAAO,CAAC,OAAO,EAAE,CAAC;IAClB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,KAAiB;IACpC,IAAA,QAAQ,GAAqD,KAAK,SAA1D,EAAE,UAAU,GAAyC,KAAK,WAA9C,EAAE,UAAU,GAA6B,KAAK,WAAlC,EAAE,SAAS,GAAkB,KAAK,UAAvB,EAAE,WAAW,GAAK,KAAK,YAAV,CAAW;IAC3E,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAK,QAAQ,OAAI,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5F,CAAC"}