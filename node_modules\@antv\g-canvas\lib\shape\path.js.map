{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../../src/shape/path.ts"], "names": [], "mappings": ";;;AAKA,uCAAkD;AAClD,mCAAyC;AACzC,+BAA+B;AAC/B,6CAA+D;AAC/D,qCAAwC;AACxC,+DAA0D;AAC1D,mDAAkD;AAClD,qCAAoC;AACpC,yCAA2C;AAE3C,aAAa;AACb,SAAS,YAAY,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;IAClC,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3B,KAAK,GAAG,iBAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,IAAI,KAAK,EAAE;YACT,MAAM;SACP;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;IAAmB,gCAAS;IAA5B;;IA2RA,CAAC;IA1RC,8BAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6CACK,KAAK,KACR,UAAU,EAAE,KAAK,EACjB,QAAQ,EAAE,KAAK,IACf;IACJ,CAAC;IAED,wBAAS,GAAT,UAAU,KAAK;QACb,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,qBAAqB;IACrB,2BAAY,GAAZ,UAAa,IAAY,EAAE,KAAU,EAAE,WAAgB;QACrD,iBAAM,YAAY,YAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAC7C,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACzB;QACD,mEAAmE;QACnE,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,iBAAiB;IACjB,0BAAW,GAAX,UAAY,IAAI;QACd,cAAc;QACd,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,yBAAa,CAAC,IAAI,CAAC,CAAC;QACtC,IAAM,MAAM,GAAG,cAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrC,mCAAmC;QACnC,+CAA+C;QAC/C,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO;QACpC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,0BAA0B;QACtD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,0BAAW,GAAX;QACE,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,yBAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;SAChC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,uBAAQ,GAAR;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAA,UAAU,GAAe,KAAK,WAApB,EAAE,QAAQ,GAAK,KAAK,SAAV,CAAW;QAEvC,IAAI,UAAU,EAAE;YACd,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACvC,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAClG;QACD,IAAI,QAAQ,EAAE;YACZ,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChG;IACH,CAAC;IAED,+BAAgB,GAAhB,UAAiB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;QAChD,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,IAAI,QAAQ,EAAE;YACZ,IAAM,QAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACrC,KAAK,GAAG,cAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,QAAM,CAAC,CAAC;SACrE;QACD,IAAI,CAAC,KAAK,IAAI,MAAM,EAAE;YACpB,IAAI,MAAM,EAAE;gBACV,yCAAyC;gBACzC,KAAK,GAAG,uBAAa,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACnC;iBAAM;gBACL,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,IAAM,aAAa,GAAG,cAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACrD,kCAAkC;gBAClC,KAAK,GAAG,YAAY,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,YAAY,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACnG;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,yBAAU,GAAV,UAAW,OAAO;QAChB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,sBAAsB;QACnE,eAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAC9C,CAAC;IAED,4BAAa,GAAb,UAAc,OAAiC;QAC7C,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAChD,IAAI,eAAe,EAAE;YACnB,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/B;QACD,IAAI,aAAa,EAAE;YACjB,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC;IAED;;;OAGG;IACH,6BAAc,GAAd;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAK,CAAC,WAAW,CAAC,EAAE;YACvB,OAAO,WAAW,CAAC;SACpB;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,uBAAQ,GAAR,UAAS,KAAa;QACpB,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC;QACT,IAAI,KAAK,CAAC;QAEV,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,IAAI,KAAK,EAAE;gBACT,OAAO;oBACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACf,CAAC;aACH;YACD,OAAO,IAAI,CAAC;SACb;QACD,WAAI,CAAC,MAAM,EAAE,UAAC,CAAC,EAAE,CAAC;YAChB,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;gBAClC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,KAAK,GAAG,CAAC,CAAC;aACX;QACH,CAAC,CAAC,CAAC;QAEH,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,YAAK,CAAC,GAAG,CAAC,IAAI,YAAK,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC;SACb;QACD,IAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;QACrB,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACjC,OAAO,cAAS,CAAC,OAAO,CACtB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EACV,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,IAAI,CACL,CAAC;IACJ,CAAC;IAED,8BAAe,GAAf;QACU,IAAA,IAAI,GAAK,IAAI,CAAC,IAAI,EAAE,KAAhB,CAAiB;QAC7B,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,cAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,yBAAU,GAAV;QACE,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,2EAA2E;QAC3E,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,QAAQ,CAAC;QACb,IAAI,QAAQ,CAAC;QACb,IAAI,QAAQ,CAAC;QACb,IAAI,CAAC,CAAC;QACN,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QAED,WAAI,CAAC,KAAK,EAAE,UAAC,OAAO,EAAE,CAAC;YACrB,QAAQ,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YACnB,IAAI,QAAQ,EAAE;gBACZ,WAAW;oBACT,cAAS,CAAC,MAAM,CACd,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EACd,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EACd,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,CACZ,IAAI,CAAC,CAAC;aACV;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAErC,IAAI,WAAW,KAAK,CAAC,EAAE;YACrB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACvB,OAAO;SACR;QAED,WAAI,CAAC,KAAK,EAAE,UAAC,OAAO,EAAE,CAAC;YACrB,QAAQ,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YACnB,IAAI,QAAQ,EAAE;gBACZ,QAAQ,GAAG,EAAE,CAAC;gBACd,QAAQ,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,WAAW,CAAC;gBACvC,QAAQ,GAAG,cAAS,CAAC,MAAM,CACzB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EACd,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EACd,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,EACX,QAAQ,CAAC,CAAC,CAAC,CACZ,CAAC;gBACF,yCAAyC;gBACzC,UAAU,IAAI,QAAQ,IAAI,CAAC,CAAC;gBAC5B,QAAQ,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,WAAW,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,8BAAe,GAAf;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,MAAM,CAAC;QACX,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,IAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC5C,IAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC1C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YACzC,MAAM,GAAG,EAAE,CAAC;YACZ,IAAI,OAAO,EAAE;gBACX,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7C;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7C;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,4BAAa,GAAb;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAI,MAAM,CAAC;QACX,IAAI,MAAM,GAAG,CAAC,EAAE;YACd,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC;YACrD,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC;YACnD,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC;YAChD,MAAM,GAAG,EAAE,CAAC;YACZ,IAAI,OAAO,EAAE;gBACX,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClE,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACH,WAAC;AAAD,CAAC,AA3RD,CAAmB,cAAS,GA2R3B;AAED,kBAAe,IAAI,CAAC"}