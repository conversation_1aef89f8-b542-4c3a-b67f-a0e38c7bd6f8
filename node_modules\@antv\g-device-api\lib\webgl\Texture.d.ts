import { Format, ResourceType, SamplerFormatKind, Texture, TextureDescriptor, TextureDimension } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export declare class Texture_GL extends ResourceBase_GL implements Texture {
    type: ResourceType.Texture;
    gl_texture: WebGLTexture;
    gl_target: GLenum;
    format: Format;
    dimension: TextureDimension;
    width: number;
    height: number;
    depthOrArrayLayers: number;
    mipLevelCount: number;
    immutable: boolean;
    pixelStore: Partial<{
        packAlignment: number;
        unpackAlignment: number;
        unpackFlipY: boolean;
    }>;
    mipmaps: boolean;
    formatKind: SamplerFormatKind;
    textureIndex: number;
    constructor({ id, device, descriptor, fake, }: {
        id: number;
        device: Device_GL;
        descriptor: TextureDescriptor;
        fake?: boolean;
    });
    setImageData(levelDatas: (TexImageSource | ArrayBufferView)[], lod?: number): void;
    destroy(): void;
    private clampmipLevelCount;
    private preprocessImage;
    private generateMipmap;
    private isNPOT;
}
