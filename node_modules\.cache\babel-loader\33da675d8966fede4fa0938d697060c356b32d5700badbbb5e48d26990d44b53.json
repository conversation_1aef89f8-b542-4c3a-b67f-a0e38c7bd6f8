{"ast": null, "code": "var _typeof = require(\"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\n//! moment.js locale configuration\n//! locale : Konkani Latin script [gom-latn]\n//! author : The Discoverer : https://github.com/WikiDiscoverer\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['thoddea sekondamni', 'thodde sekond'],\n      ss: [number + ' sekondamni', number + ' sekond'],\n      m: ['eka mintan', 'ek minut'],\n      mm: [number + ' mintamni', number + ' mintam'],\n      h: ['eka voran', 'ek vor'],\n      hh: [number + ' voramni', number + ' voram'],\n      d: ['eka disan', 'ek dis'],\n      dd: [number + ' disamni', number + ' dis'],\n      M: ['eka mhoinean', 'ek mhoino'],\n      MM: [number + ' mhoineamni', number + ' mhoine'],\n      y: ['eka vorsan', 'ek voros'],\n      yy: [number + ' vorsamni', number + ' vorsam']\n    };\n    return isFuture ? format[key][0] : format[key][1];\n  }\n  var gomLatn = moment.defineLocale('gom-latn', {\n    months: {\n      standalone: 'Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr'.split('_'),\n      format: 'Janerachea_Febrerachea_Marsachea_Abrilachea_Maiachea_Junachea_Julaiachea_Agostachea_Setembrachea_Otubrachea_Novembrachea_Dezembrachea'.split('_'),\n      isFormat: /MMMM(\\s)+D[oD]?/\n    },\n    monthsShort: 'Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.'.split('_'),\n    monthsParseExact: true,\n    weekdays: \"Aitar_Somar_Mongllar_Budhvar_Birestar_Sukrar_Son'var\".split('_'),\n    weekdaysShort: 'Ait._Som._Mon._Bud._Bre._Suk._Son.'.split('_'),\n    weekdaysMin: 'Ai_Sm_Mo_Bu_Br_Su_Sn'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'A h:mm [vazta]',\n      LTS: 'A h:mm:ss [vazta]',\n      L: 'DD-MM-YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY A h:mm [vazta]',\n      LLLL: 'dddd, MMMM Do, YYYY, A h:mm [vazta]',\n      llll: 'ddd, D MMM YYYY, A h:mm [vazta]'\n    },\n    calendar: {\n      sameDay: '[Aiz] LT',\n      nextDay: '[Faleam] LT',\n      nextWeek: '[Fuddlo] dddd[,] LT',\n      lastDay: '[Kal] LT',\n      lastWeek: '[Fattlo] dddd[,] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s',\n      past: '%s adim',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(er)/,\n    ordinal: function ordinal(number, period) {\n      switch (period) {\n        // the ordinal 'er' only applies to day of the month\n        case 'D':\n          return number + 'er';\n        default:\n        case 'M':\n        case 'Q':\n        case 'DDD':\n        case 'd':\n        case 'w':\n        case 'W':\n          return number;\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week\n      doy: 3 // The week that contains Jan 4th is the first week of the year (7 + 0 - 4)\n    },\n    meridiemParse: /rati|sokallim|donparam|sanje/,\n    meridiemHour: function meridiemHour(hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'rati') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'sokallim') {\n        return hour;\n      } else if (meridiem === 'donparam') {\n        return hour > 12 ? hour : hour + 12;\n      } else if (meridiem === 'sanje') {\n        return hour + 12;\n      }\n    },\n    meridiem: function meridiem(hour, minute, isLower) {\n      if (hour < 4) {\n        return 'rati';\n      } else if (hour < 12) {\n        return 'sokallim';\n      } else if (hour < 16) {\n        return 'donparam';\n      } else if (hour < 20) {\n        return 'sanje';\n      } else {\n        return 'rati';\n      }\n    }\n  });\n  return gomLatn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "processRelativeTime", "number", "withoutSuffix", "key", "isFuture", "format", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "gomLatn", "defineLocale", "months", "standalone", "split", "isFormat", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "dayOfMonthOrdinalParse", "ordinal", "period", "week", "dow", "doy", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/moment/locale/gom-latn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Konkani Latin script [gom-latn]\n//! author : The Discoverer : https://github.com/WikiDiscoverer\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            s: ['thoddea sekondamni', 'thodde sekond'],\n            ss: [number + ' sekondamni', number + ' sekond'],\n            m: ['eka mintan', 'ek minut'],\n            mm: [number + ' mintamni', number + ' mintam'],\n            h: ['eka voran', 'ek vor'],\n            hh: [number + ' voramni', number + ' voram'],\n            d: ['eka disan', 'ek dis'],\n            dd: [number + ' disamni', number + ' dis'],\n            M: ['eka mhoinean', 'ek mhoino'],\n            MM: [number + ' mhoineamni', number + ' mhoine'],\n            y: ['eka vorsan', 'ek voros'],\n            yy: [number + ' vorsamni', number + ' vorsam'],\n        };\n        return isFuture ? format[key][0] : format[key][1];\n    }\n\n    var gomLatn = moment.defineLocale('gom-latn', {\n        months: {\n            standalone:\n                'Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr'.split(\n                    '_'\n                ),\n            format: 'Janerachea_Febrerachea_Marsachea_Abrilachea_Maiachea_Junachea_Julaiachea_Agostachea_Setembrachea_Otubrachea_Novembrachea_Dezembrachea'.split(\n                '_'\n            ),\n            isFormat: /MMMM(\\s)+D[oD]?/,\n        },\n        monthsShort:\n            'Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays: \"Aitar_Somar_Mongllar_Budhvar_Birestar_Sukrar_Son'var\".split('_'),\n        weekdaysShort: 'Ait._Som._Mon._Bud._Bre._Suk._Son.'.split('_'),\n        weekdaysMin: 'Ai_Sm_Mo_Bu_Br_Su_Sn'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'A h:mm [vazta]',\n            LTS: 'A h:mm:ss [vazta]',\n            L: 'DD-MM-YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY A h:mm [vazta]',\n            LLLL: 'dddd, MMMM Do, YYYY, A h:mm [vazta]',\n            llll: 'ddd, D MMM YYYY, A h:mm [vazta]',\n        },\n        calendar: {\n            sameDay: '[Aiz] LT',\n            nextDay: '[Faleam] LT',\n            nextWeek: '[Fuddlo] dddd[,] LT',\n            lastDay: '[Kal] LT',\n            lastWeek: '[Fattlo] dddd[,] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s',\n            past: '%s adim',\n            s: processRelativeTime,\n            ss: processRelativeTime,\n            m: processRelativeTime,\n            mm: processRelativeTime,\n            h: processRelativeTime,\n            hh: processRelativeTime,\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(er)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                // the ordinal 'er' only applies to day of the month\n                case 'D':\n                    return number + 'er';\n                default:\n                case 'M':\n                case 'Q':\n                case 'DDD':\n                case 'd':\n                case 'w':\n                case 'W':\n                    return number;\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week\n            doy: 3, // The week that contains Jan 4th is the first week of the year (7 + 0 - 4)\n        },\n        meridiemParse: /rati|sokallim|donparam|sanje/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'rati') {\n                return hour < 4 ? hour : hour + 12;\n            } else if (meridiem === 'sokallim') {\n                return hour;\n            } else if (meridiem === 'donparam') {\n                return hour > 12 ? hour : hour + 12;\n            } else if (meridiem === 'sanje') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'rati';\n            } else if (hour < 12) {\n                return 'sokallim';\n            } else if (hour < 16) {\n                return 'donparam';\n            } else if (hour < 20) {\n                return 'sanje';\n            } else {\n                return 'rati';\n            }\n        },\n    });\n\n    return gomLatn;\n\n})));\n"], "mappings": ";AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC/D,IAAIC,MAAM,GAAG;MACTC,CAAC,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC;MAC1CC,EAAE,EAAE,CAACN,MAAM,GAAG,aAAa,EAAEA,MAAM,GAAG,SAAS,CAAC;MAChDO,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;MAC7BC,EAAE,EAAE,CAACR,MAAM,GAAG,WAAW,EAAEA,MAAM,GAAG,SAAS,CAAC;MAC9CS,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;MAC1BC,EAAE,EAAE,CAACV,MAAM,GAAG,UAAU,EAAEA,MAAM,GAAG,QAAQ,CAAC;MAC5CW,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;MAC1BC,EAAE,EAAE,CAACZ,MAAM,GAAG,UAAU,EAAEA,MAAM,GAAG,MAAM,CAAC;MAC1Ca,CAAC,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;MAChCC,EAAE,EAAE,CAACd,MAAM,GAAG,aAAa,EAAEA,MAAM,GAAG,SAAS,CAAC;MAChDe,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;MAC7BC,EAAE,EAAE,CAAChB,MAAM,GAAG,WAAW,EAAEA,MAAM,GAAG,SAAS;IACjD,CAAC;IACD,OAAOG,QAAQ,GAAGC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EACrD;EAEA,IAAIe,OAAO,GAAGnB,MAAM,CAACoB,YAAY,CAAC,UAAU,EAAE;IAC1CC,MAAM,EAAE;MACJC,UAAU,EACN,2EAA2E,CAACC,KAAK,CAC7E,GACJ,CAAC;MACLjB,MAAM,EAAE,uIAAuI,CAACiB,KAAK,CACjJ,GACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,WAAW,EACP,2DAA2D,CAACF,KAAK,CAAC,GAAG,CAAC;IAC1EG,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,sDAAsD,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC3EK,aAAa,EAAE,oCAAoC,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9DM,WAAW,EAAE,sBAAsB,CAACN,KAAK,CAAC,GAAG,CAAC;IAC9CO,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,gBAAgB;MACpBC,GAAG,EAAE,mBAAmB;MACxBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,4BAA4B;MACjCC,IAAI,EAAE,qCAAqC;MAC3CC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,UAAU;MACnBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,qBAAqB;MAC/BC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,SAAS;MACfzC,CAAC,EAAEN,mBAAmB;MACtBO,EAAE,EAAEP,mBAAmB;MACvBQ,CAAC,EAAER,mBAAmB;MACtBS,EAAE,EAAET,mBAAmB;MACvBU,CAAC,EAAEV,mBAAmB;MACtBW,EAAE,EAAEX,mBAAmB;MACvBY,CAAC,EAAEZ,mBAAmB;MACtBa,EAAE,EAAEb,mBAAmB;MACvBc,CAAC,EAAEd,mBAAmB;MACtBe,EAAE,EAAEf,mBAAmB;MACvBgB,CAAC,EAAEhB,mBAAmB;MACtBiB,EAAE,EAAEjB;IACR,CAAC;IACDgD,sBAAsB,EAAE,aAAa;IACrCC,OAAO,EAAE,SAATA,OAAOA,CAAYhD,MAAM,EAAEiD,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV;QACA,KAAK,GAAG;UACJ,OAAOjD,MAAM,GAAG,IAAI;QACxB;QACA,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDkD,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ,CAAC;IACDC,aAAa,EAAE,8BAA8B;IAC7CC,YAAY,EAAE,SAAdA,YAAYA,CAAYC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,MAAM,EAAE;QACrB,OAAOD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAIC,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAOD,IAAI,GAAG,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACvC,CAAC,MAAM,IAAIC,QAAQ,KAAK,OAAO,EAAE;QAC7B,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAVA,QAAQA,CAAYD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU;MACrB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU;MACrB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM;QACH,OAAO,MAAM;MACjB;IACJ;EACJ,CAAC,CAAC;EAEF,OAAOtC,OAAO;AAElB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}