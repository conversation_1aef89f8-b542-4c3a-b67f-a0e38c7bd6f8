{"version": 3, "file": "ellipse.js", "sourceRoot": "", "sources": ["../../src/shape/ellipse.ts"], "names": [], "mappings": "AAAA;;;GAGG;;AAEH,OAAO,SAAS,MAAM,QAAQ,CAAC;AAE/B,kCAAkC;AAClC,SAAS,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;IAC/C,OAAO,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACnD,CAAC;AAED;IAAsB,2BAAS;IAA/B;;IA2DA,CAAC;IA1DC,iCAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6BACK,KAAK,KACR,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,IACL;IACJ,CAAC;IAED,kCAAgB,GAAhB,UAAiB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;QAChD,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,YAAY,GAAG,SAAS,GAAG,CAAC,CAAC;QACnC,IAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;QACnB,IAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;QACX,IAAA,EAAE,GAAS,KAAK,GAAd,EAAE,EAAE,GAAK,KAAK,GAAV,CAAW;QACzB,IAAM,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACpC,IAAM,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACpC,sCAAsC;QACtC,IAAI,MAAM,IAAI,QAAQ,EAAE;YACtB,OAAO,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,GAAG,YAAY,EAAE,EAAE,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;SACrF;QACD,IAAI,MAAM,EAAE;YACV,OAAO,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;SACvD;QACD,IAAI,QAAQ,EAAE;YACZ,OAAO,CACL,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,GAAG,YAAY,EAAE,EAAE,GAAG,YAAY,CAAC,IAAI,CAAC;gBAC5E,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,GAAG,YAAY,EAAE,EAAE,GAAG,YAAY,CAAC,IAAI,CAAC,CAC7E,CAAC;SACH;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,4BAAU,GAAV,UAAW,OAAO;QAChB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;QACnB,IAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;QACnB,IAAM,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;QACpB,IAAM,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;QACpB,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO;QACP,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;SAC3D;aAAM;YACL,qBAAqB;YACrB,IAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5B,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;YACrC,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC1B,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACrC,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,SAAS,EAAE,CAAC;SACrB;IACH,CAAC;IACH,cAAC;AAAD,CAAC,AA3DD,CAAsB,SAAS,GA2D9B;AAED,eAAe,OAAO,CAAC"}