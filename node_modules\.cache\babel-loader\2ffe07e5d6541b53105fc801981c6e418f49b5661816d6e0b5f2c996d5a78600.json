{"ast": null, "code": "var _typeof = require(\"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\n//! moment.js locale configuration\n//! locale : Icelandic [is]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/hinrik\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function plural(n) {\n    if (n % 100 === 11) {\n      return true;\n    } else if (n % 10 === 1) {\n      return false;\n    }\n    return true;\n  }\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    switch (key) {\n      case 's':\n        return withoutSuffix || isFuture ? 'nokkrar sekúndur' : 'nokkrum sekúndum';\n      case 'ss':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'sekúndur' : 'sekúndum');\n        }\n        return result + 'sekúnda';\n      case 'm':\n        return withoutSuffix ? 'mínúta' : 'mínútu';\n      case 'mm':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'mínútur' : 'mínútum');\n        } else if (withoutSuffix) {\n          return result + 'mínúta';\n        }\n        return result + 'mínútu';\n      case 'hh':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'klukkustundir' : 'klukkustundum');\n        }\n        return result + 'klukkustund';\n      case 'd':\n        if (withoutSuffix) {\n          return 'dagur';\n        }\n        return isFuture ? 'dag' : 'degi';\n      case 'dd':\n        if (plural(number)) {\n          if (withoutSuffix) {\n            return result + 'dagar';\n          }\n          return result + (isFuture ? 'daga' : 'dögum');\n        } else if (withoutSuffix) {\n          return result + 'dagur';\n        }\n        return result + (isFuture ? 'dag' : 'degi');\n      case 'M':\n        if (withoutSuffix) {\n          return 'mánuður';\n        }\n        return isFuture ? 'mánuð' : 'mánuði';\n      case 'MM':\n        if (plural(number)) {\n          if (withoutSuffix) {\n            return result + 'mánuðir';\n          }\n          return result + (isFuture ? 'mánuði' : 'mánuðum');\n        } else if (withoutSuffix) {\n          return result + 'mánuður';\n        }\n        return result + (isFuture ? 'mánuð' : 'mánuði');\n      case 'y':\n        return withoutSuffix || isFuture ? 'ár' : 'ári';\n      case 'yy':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'ár' : 'árum');\n        }\n        return result + (withoutSuffix || isFuture ? 'ár' : 'ári');\n    }\n  }\n  var is = moment.defineLocale('is', {\n    months: 'janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des'.split('_'),\n    weekdays: 'sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur'.split('_'),\n    weekdaysShort: 'sun_mán_þri_mið_fim_fös_lau'.split('_'),\n    weekdaysMin: 'Su_Má_Þr_Mi_Fi_Fö_La'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY [kl.] H:mm',\n      LLLL: 'dddd, D. MMMM YYYY [kl.] H:mm'\n    },\n    calendar: {\n      sameDay: '[í dag kl.] LT',\n      nextDay: '[á morgun kl.] LT',\n      nextWeek: 'dddd [kl.] LT',\n      lastDay: '[í gær kl.] LT',\n      lastWeek: '[síðasta] dddd [kl.] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'eftir %s',\n      past: 'fyrir %s síðan',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: 'klukkustund',\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return is;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "plural", "n", "translate", "number", "withoutSuffix", "key", "isFuture", "result", "is", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["G:/备份9/adminweb/node_modules/moment/locale/is.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Icelandic [is]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/hinrik\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function plural(n) {\n        if (n % 100 === 11) {\n            return true;\n        } else if (n % 10 === 1) {\n            return false;\n        }\n        return true;\n    }\n    function translate(number, withoutSuffix, key, isFuture) {\n        var result = number + ' ';\n        switch (key) {\n            case 's':\n                return withoutSuffix || isFuture\n                    ? 'nokkrar sekúndur'\n                    : 'nokkrum sekúndum';\n            case 'ss':\n                if (plural(number)) {\n                    return (\n                        result +\n                        (withoutSuffix || isFuture ? 'sekúndur' : 'sekúndum')\n                    );\n                }\n                return result + 'sekúnda';\n            case 'm':\n                return withoutSuffix ? 'mínúta' : 'mínútu';\n            case 'mm':\n                if (plural(number)) {\n                    return (\n                        result + (withoutSuffix || isFuture ? 'mínútur' : 'mínútum')\n                    );\n                } else if (withoutSuffix) {\n                    return result + 'mínúta';\n                }\n                return result + 'mínútu';\n            case 'hh':\n                if (plural(number)) {\n                    return (\n                        result +\n                        (withoutSuffix || isFuture\n                            ? 'klukkustundir'\n                            : 'klukkustundum')\n                    );\n                }\n                return result + 'klukkustund';\n            case 'd':\n                if (withoutSuffix) {\n                    return 'dagur';\n                }\n                return isFuture ? 'dag' : 'degi';\n            case 'dd':\n                if (plural(number)) {\n                    if (withoutSuffix) {\n                        return result + 'dagar';\n                    }\n                    return result + (isFuture ? 'daga' : 'dögum');\n                } else if (withoutSuffix) {\n                    return result + 'dagur';\n                }\n                return result + (isFuture ? 'dag' : 'degi');\n            case 'M':\n                if (withoutSuffix) {\n                    return 'mánuður';\n                }\n                return isFuture ? 'mánuð' : 'mánuði';\n            case 'MM':\n                if (plural(number)) {\n                    if (withoutSuffix) {\n                        return result + 'mánuðir';\n                    }\n                    return result + (isFuture ? 'mánuði' : 'mánuðum');\n                } else if (withoutSuffix) {\n                    return result + 'mánuður';\n                }\n                return result + (isFuture ? 'mánuð' : 'mánuði');\n            case 'y':\n                return withoutSuffix || isFuture ? 'ár' : 'ári';\n            case 'yy':\n                if (plural(number)) {\n                    return result + (withoutSuffix || isFuture ? 'ár' : 'árum');\n                }\n                return result + (withoutSuffix || isFuture ? 'ár' : 'ári');\n        }\n    }\n\n    var is = moment.defineLocale('is', {\n        months: 'janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember'.split(\n            '_'\n        ),\n        monthsShort: 'jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des'.split('_'),\n        weekdays:\n            'sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur'.split(\n                '_'\n            ),\n        weekdaysShort: 'sun_mán_þri_mið_fim_fös_lau'.split('_'),\n        weekdaysMin: 'Su_Má_Þr_Mi_Fi_Fö_La'.split('_'),\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY [kl.] H:mm',\n            LLLL: 'dddd, D. MMMM YYYY [kl.] H:mm',\n        },\n        calendar: {\n            sameDay: '[í dag kl.] LT',\n            nextDay: '[á morgun kl.] LT',\n            nextWeek: 'dddd [kl.] LT',\n            lastDay: '[í gær kl.] LT',\n            lastWeek: '[síðasta] dddd [kl.] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'eftir %s',\n            past: 'fyrir %s síðan',\n            s: translate,\n            ss: translate,\n            m: translate,\n            mm: translate,\n            h: 'klukkustund',\n            hh: translate,\n            d: translate,\n            dd: translate,\n            M: translate,\n            MM: translate,\n            y: translate,\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return is;\n\n})));\n"], "mappings": ";AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,GAAG,GAAG,KAAK,EAAE,EAAE;MAChB,OAAO,IAAI;IACf,CAAC,MAAM,IAAIA,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;MACrB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA,SAASC,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IACrD,IAAIC,MAAM,GAAGJ,MAAM,GAAG,GAAG;IACzB,QAAQE,GAAG;MACP,KAAK,GAAG;QACJ,OAAOD,aAAa,IAAIE,QAAQ,GAC1B,kBAAkB,GAClB,kBAAkB;MAC5B,KAAK,IAAI;QACL,IAAIN,MAAM,CAACG,MAAM,CAAC,EAAE;UAChB,OACII,MAAM,IACLH,aAAa,IAAIE,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC;QAE7D;QACA,OAAOC,MAAM,GAAG,SAAS;MAC7B,KAAK,GAAG;QACJ,OAAOH,aAAa,GAAG,QAAQ,GAAG,QAAQ;MAC9C,KAAK,IAAI;QACL,IAAIJ,MAAM,CAACG,MAAM,CAAC,EAAE;UAChB,OACII,MAAM,IAAIH,aAAa,IAAIE,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;QAEpE,CAAC,MAAM,IAAIF,aAAa,EAAE;UACtB,OAAOG,MAAM,GAAG,QAAQ;QAC5B;QACA,OAAOA,MAAM,GAAG,QAAQ;MAC5B,KAAK,IAAI;QACL,IAAIP,MAAM,CAACG,MAAM,CAAC,EAAE;UAChB,OACII,MAAM,IACLH,aAAa,IAAIE,QAAQ,GACpB,eAAe,GACf,eAAe,CAAC;QAE9B;QACA,OAAOC,MAAM,GAAG,aAAa;MACjC,KAAK,GAAG;QACJ,IAAIH,aAAa,EAAE;UACf,OAAO,OAAO;QAClB;QACA,OAAOE,QAAQ,GAAG,KAAK,GAAG,MAAM;MACpC,KAAK,IAAI;QACL,IAAIN,MAAM,CAACG,MAAM,CAAC,EAAE;UAChB,IAAIC,aAAa,EAAE;YACf,OAAOG,MAAM,GAAG,OAAO;UAC3B;UACA,OAAOA,MAAM,IAAID,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;QACjD,CAAC,MAAM,IAAIF,aAAa,EAAE;UACtB,OAAOG,MAAM,GAAG,OAAO;QAC3B;QACA,OAAOA,MAAM,IAAID,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC;MAC/C,KAAK,GAAG;QACJ,IAAIF,aAAa,EAAE;UACf,OAAO,SAAS;QACpB;QACA,OAAOE,QAAQ,GAAG,OAAO,GAAG,QAAQ;MACxC,KAAK,IAAI;QACL,IAAIN,MAAM,CAACG,MAAM,CAAC,EAAE;UAChB,IAAIC,aAAa,EAAE;YACf,OAAOG,MAAM,GAAG,SAAS;UAC7B;UACA,OAAOA,MAAM,IAAID,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;QACrD,CAAC,MAAM,IAAIF,aAAa,EAAE;UACtB,OAAOG,MAAM,GAAG,SAAS;QAC7B;QACA,OAAOA,MAAM,IAAID,QAAQ,GAAG,OAAO,GAAG,QAAQ,CAAC;MACnD,KAAK,GAAG;QACJ,OAAOF,aAAa,IAAIE,QAAQ,GAAG,IAAI,GAAG,KAAK;MACnD,KAAK,IAAI;QACL,IAAIN,MAAM,CAACG,MAAM,CAAC,EAAE;UAChB,OAAOI,MAAM,IAAIH,aAAa,IAAIE,QAAQ,GAAG,IAAI,GAAG,MAAM,CAAC;QAC/D;QACA,OAAOC,MAAM,IAAIH,aAAa,IAAIE,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;IAClE;EACJ;EAEA,IAAIE,EAAE,GAAGT,MAAM,CAACU,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,mFAAmF,CAACC,KAAK,CAC7F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EACJ,kFAAkF,CAACF,KAAK,CACpF,GACJ,CAAC;IACLG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,yBAAyB;MACnCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,gBAAgB;MACtBC,CAAC,EAAE/B,SAAS;MACZgC,EAAE,EAAEhC,SAAS;MACbiC,CAAC,EAAEjC,SAAS;MACZkC,EAAE,EAAElC,SAAS;MACbmC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAEpC,SAAS;MACbqC,CAAC,EAAErC,SAAS;MACZsC,EAAE,EAAEtC,SAAS;MACbuC,CAAC,EAAEvC,SAAS;MACZwC,EAAE,EAAExC,SAAS;MACbyC,CAAC,EAAEzC,SAAS;MACZ0C,EAAE,EAAE1C;IACR,CAAC;IACD2C,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOzC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}