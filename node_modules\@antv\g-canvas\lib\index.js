"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.version = exports.Shape = void 0;
var tslib_1 = require("tslib");
var Shape = require("./shape");
exports.Shape = Shape;
tslib_1.__exportStar(require("@antv/g-base"), exports);
var canvas_1 = require("./canvas");
Object.defineProperty(exports, "Canvas", { enumerable: true, get: function () { return canvas_1.default; } });
var group_1 = require("./group");
Object.defineProperty(exports, "Group", { enumerable: true, get: function () { return group_1.default; } });
var arc_params_1 = require("./util/arc-params");
Object.defineProperty(exports, "getArcParams", { enumerable: true, get: function () { return arc_params_1.default; } });
exports.version = '0.5.12';
//# sourceMappingURL=index.js.map