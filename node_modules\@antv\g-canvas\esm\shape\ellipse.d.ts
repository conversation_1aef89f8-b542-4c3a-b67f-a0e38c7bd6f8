/**
 * @fileoverview 椭圆
 * <AUTHOR>
 */
import ShapeBase from './base';
declare class Ellipse extends ShapeBase {
    getDefaultAttrs(): {
        x: number;
        y: number;
        rx: number;
        ry: number;
        lineWidth: number;
        lineAppendWidth: number;
        strokeOpacity: number;
        fillOpacity: number;
        matrix: any;
        opacity: number;
    };
    isInStrokeOrPath(x: any, y: any, isStroke: any, isFill: any, lineWidth: any): boolean;
    createPath(context: any): void;
}
export default Ellipse;
