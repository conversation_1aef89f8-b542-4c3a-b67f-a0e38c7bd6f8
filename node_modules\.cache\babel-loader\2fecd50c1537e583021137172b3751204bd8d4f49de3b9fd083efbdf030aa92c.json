{"ast": null, "code": "import \"E:\\\\\\u65B0\\u9879\\u76EE\\\\\\u6574\\u74066\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.array.iterator.js\";\nimport \"E:\\\\\\u65B0\\u9879\\u76EE\\\\\\u6574\\u74066\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.js\";\nimport \"E:\\\\\\u65B0\\u9879\\u76EE\\\\\\u6574\\u74066\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.object.assign.js\";\nimport \"E:\\\\\\u65B0\\u9879\\u76EE\\\\\\u6574\\u74066\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.finally.js\";\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nVue.use(ElementUI);\nVue.config.productionTip = false;\nnew Vue({\n  router: router,\n  store: store,\n  render: function render(h) {\n    return h(App);\n  }\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "ElementUI", "use", "config", "productionTip", "render", "h", "$mount"], "sources": ["E:/新项目/整理6/adminweb/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport ElementUI from 'element-ui'\r\nimport 'element-ui/lib/theme-chalk/index.css'\r\n\r\nVue.use(ElementUI)\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app') "], "mappings": ";;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAE7CJ,GAAG,CAACK,GAAG,CAACD,SAAS,CAAC;AAClBJ,GAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIP,GAAG,CAAC;EACNE,MAAM,EAANA,MAAM;EACNC,KAAK,EAALA,KAAK;EACLK,MAAM,EAAE,SAARA,MAAMA,CAAEC,CAAC;IAAA,OAAIA,CAAC,CAACR,GAAG,CAAC;EAAA;AACrB,CAAC,CAAC,CAACS,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}