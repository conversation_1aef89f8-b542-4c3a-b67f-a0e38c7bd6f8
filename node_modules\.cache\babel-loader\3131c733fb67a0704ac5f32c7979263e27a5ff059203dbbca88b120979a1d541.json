{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取任务日志列表\nexport function getJobLogList(params) {\n  return request({\n    url: '/joblog/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 清空任务日志\nexport function cleanJobLog() {\n  return request({\n    url: '/joblog/clean',\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["request", "getJobLogList", "params", "url", "method", "cleanJobLog"], "sources": ["F:/常规项目/华通宝/adminweb/src/api/log/task.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取任务日志列表\r\nexport function getJobLogList(params) {\r\n  return request({\r\n    url: '/joblog/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 清空任务日志\r\nexport function cleanJobLog() {\r\n  return request({\r\n    url: '/joblog/clean',\r\n    method: 'delete'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,WAAWA,CAAA,EAAG;EAC5B,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}