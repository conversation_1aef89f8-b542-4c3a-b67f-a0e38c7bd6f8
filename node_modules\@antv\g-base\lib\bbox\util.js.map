{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/bbox/util.ts"], "names": [], "mappings": ";;;AAEA,QAAQ;AACR,SAAgB,SAAS,CAAC,KAAK,EAAE,KAAK;IACpC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;QACpB,OAAO,KAAK,IAAI,KAAK,CAAC;KACvB;IACD,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;QACtC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;QACtC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;QACtC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;KACvC,CAAC;AACJ,CAAC;AAVD,8BAUC;AAED,WAAW;AACX,SAAgB,cAAc,CAAC,KAAa,EAAE,IAAI;IAChD,IAAM,eAAe,GAAG,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACrD,IAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACjD,IAAI,cAAc,GAAG,IAAI,CAAC;IAC1B,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,IAAI,eAAe,EAAE;QACnB,cAAc,GAAG,eAAe,CAAC,aAAa,EAAE,CAAC;QACjD,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KACxC;IACD,IAAI,aAAa,EAAE;QACjB,YAAY,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAC7C,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KACtC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAdD,wCAcC"}