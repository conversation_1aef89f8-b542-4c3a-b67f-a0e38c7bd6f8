{"ast": null, "code": "import \"G:\\\\\\u5907\\u4EFD9\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.array.iterator.js\";\nimport \"G:\\\\\\u5907\\u4EFD9\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.js\";\nimport \"G:\\\\\\u5907\\u4EFD9\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.object.assign.js\";\nimport \"G:\\\\\\u5907\\u4EFD9\\\\adminweb\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.finally.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nimport { parseTime, formatDate } from '@/utils/date';\n\n// 屏蔽所有控制台输出\nif (process.env.NODE_ENV === 'production') {\n  console.log = function () {};\n  console.info = function () {};\n  console.warn = function () {};\n  console.error = function () {};\n  console.debug = function () {};\n}\nVue.use(ElementUI);\nVue.config.productionTip = false;\n\n// 全局过滤器\nVue.filter('formatDateTime', function (time) {\n  return parseTime(time);\n});\nVue.filter('formatDate', function (time) {\n  return formatDate(time);\n});\nnew Vue({\n  router: router,\n  store: store,\n  render: function render(h) {\n    return h(App);\n  }\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "ElementUI", "parseTime", "formatDate", "process", "env", "NODE_ENV", "console", "log", "info", "warn", "error", "debug", "use", "config", "productionTip", "filter", "time", "render", "h", "$mount"], "sources": ["G:/备份9/adminweb/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport ElementUI from 'element-ui'\r\nimport 'element-ui/lib/theme-chalk/index.css'\r\nimport { parseTime, formatDate } from '@/utils/date'\r\n\r\n// 屏蔽所有控制台输出\r\nif (process.env.NODE_ENV === 'production') {\r\n  console.log = () => {}\r\n  console.info = () => {}\r\n  console.warn = () => {}\r\n  console.error = () => {}\r\n  console.debug = () => {}\r\n}\r\n\r\nVue.use(ElementUI)\r\nVue.config.productionTip = false\r\n\r\n// 全局过滤器\r\nVue.filter('formatDateTime', (time) => parseTime(time))\r\nVue.filter('formatDate', (time) => formatDate(time))\r\n\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app') "], "mappings": ";;;;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,SAASC,SAAS,EAAEC,UAAU,QAAQ,cAAc;;AAEpD;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,OAAO,CAACC,GAAG,GAAG,YAAM,CAAC,CAAC;EACtBD,OAAO,CAACE,IAAI,GAAG,YAAM,CAAC,CAAC;EACvBF,OAAO,CAACG,IAAI,GAAG,YAAM,CAAC,CAAC;EACvBH,OAAO,CAACI,KAAK,GAAG,YAAM,CAAC,CAAC;EACxBJ,OAAO,CAACK,KAAK,GAAG,YAAM,CAAC,CAAC;AAC1B;AAEAf,GAAG,CAACgB,GAAG,CAACZ,SAAS,CAAC;AAClBJ,GAAG,CAACiB,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACAlB,GAAG,CAACmB,MAAM,CAAC,gBAAgB,EAAE,UAACC,IAAI;EAAA,OAAKf,SAAS,CAACe,IAAI,CAAC;AAAA,EAAC;AACvDpB,GAAG,CAACmB,MAAM,CAAC,YAAY,EAAE,UAACC,IAAI;EAAA,OAAKd,UAAU,CAACc,IAAI,CAAC;AAAA,EAAC;AAEpD,IAAIpB,GAAG,CAAC;EACNE,MAAM,EAANA,MAAM;EACNC,KAAK,EAALA,KAAK;EACLkB,MAAM,EAAE,SAARA,MAAMA,CAAEC,CAAC;IAAA,OAAIA,CAAC,CAACrB,GAAG,CAAC;EAAA;AACrB,CAAC,CAAC,CAACsB,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}