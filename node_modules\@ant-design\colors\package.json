{"name": "@ant-design/colors", "version": "4.0.5", "description": "Color palettes calculator of Ant Design", "main": "dist/index.js", "module": "dist/index.esm.js", "files": ["lib", "dist/index.js", "dist/index.esm.js", "dist/src/index.d.ts", "dist/src/generate.d.ts"], "typings": "dist/src/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/ant-design/ant-design-colors.git"}, "bugs": {"url": "https://github.com/ant-design/ant-design-colors/issues"}, "scripts": {"tsc": "tsc --noEmit", "compile": "father build --esm --cjs", "prepublishOnly": "npm run compile && np --no-cleanup --no-publish", "lint": "eslint src --ext .ts", "lint:fix": "prettier --write '{src,tests}/**/*.ts'", "jest": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls", "test": "npm run tsc && npm run lint && npm run jest"}, "author": "afc163 <<EMAIL>>", "license": "MIT", "dependencies": {"tinycolor2": "^1.4.1"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/tinycolor2": "^1.4.1", "@typescript-eslint/eslint-plugin": "^3.0.0", "@typescript-eslint/parser": "^3.0.0", "coveralls": "^3.0.3", "eslint": "^7.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1", "father": "^2.29.5", "jest": "^26.0.1", "np": "^6.3.2", "prettier": "^2.0.0", "ts-jest": "^26.0.0", "typescript": "^3.4.2"}, "homepage": "https://github.com/ant-design/ant-design-colors#readme"}