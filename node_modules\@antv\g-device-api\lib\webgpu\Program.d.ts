/// <reference types="@webgpu/types" />
import type { Program, ProgramDescriptor } from '../api';
import { ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class Program_WebGPU extends ResourceBase_WebGPU implements Program {
    type: ResourceType.Program;
    descriptor: ProgramDescriptor;
    vertexStage: GPUProgrammableStage | null;
    fragmentStage: GPUProgrammableStage | null;
    computeStage: GPUProgrammableStage | null;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: ProgramDescriptor;
    });
    setUniformsLegacy(uniforms?: Record<string, any>): void;
    private createShaderStage;
}
