/// <reference types="@webgpu/types" />
import type { RenderPipeline, RenderPipelineDescriptor } from '../api';
import { ResourceType } from '../api';
import type { IDevice_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class RenderPipeline_WebGPU extends ResourceBase_WebGPU implements RenderPipeline {
    type: ResourceType.RenderPipeline;
    descriptor: RenderPipelineDescriptor;
    isCreatingAsync: boolean;
    gpuRenderPipeline: GPURenderPipeline | null;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: RenderPipelineDescriptor;
    });
    getBindGroupLayout(index: number): GPUBindGroupLayout;
}
