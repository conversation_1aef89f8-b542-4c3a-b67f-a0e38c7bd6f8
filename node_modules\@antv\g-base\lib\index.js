"use strict";
/**
 * @fileoverview G 的基础接口定义和所有的抽象类
 * <AUTHOR>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.version = exports.PathUtil = void 0;
var tslib_1 = require("tslib");
var PathUtil = require("./util/path");
exports.PathUtil = PathUtil;
tslib_1.__exportStar(require("./types"), exports);
tslib_1.__exportStar(require("./interfaces"), exports);
var graph_event_1 = require("./event/graph-event");
Object.defineProperty(exports, "Event", { enumerable: true, get: function () { return graph_event_1.default; } });
var base_1 = require("./abstract/base");
Object.defineProperty(exports, "Base", { enumerable: true, get: function () { return base_1.default; } });
var canvas_1 = require("./abstract/canvas");
Object.defineProperty(exports, "AbstractCanvas", { enumerable: true, get: function () { return canvas_1.default; } });
var group_1 = require("./abstract/group");
Object.defineProperty(exports, "AbstractGroup", { enumerable: true, get: function () { return group_1.default; } });
var shape_1 = require("./abstract/shape");
Object.defineProperty(exports, "AbstractShape", { enumerable: true, get: function () { return shape_1.default; } });
var bbox_1 = require("./bbox");
Object.defineProperty(exports, "getBBoxMethod", { enumerable: true, get: function () { return bbox_1.getBBoxMethod; } });
Object.defineProperty(exports, "registerBBox", { enumerable: true, get: function () { return bbox_1.registerBBox; } });
var text_1 = require("./util/text");
Object.defineProperty(exports, "getTextHeight", { enumerable: true, get: function () { return text_1.getTextHeight; } });
Object.defineProperty(exports, "assembleFont", { enumerable: true, get: function () { return text_1.assembleFont; } });
var util_1 = require("./util/util");
Object.defineProperty(exports, "isAllowCapture", { enumerable: true, get: function () { return util_1.isAllowCapture; } });
var matrix_1 = require("./util/matrix");
Object.defineProperty(exports, "multiplyVec2", { enumerable: true, get: function () { return matrix_1.multiplyVec2; } });
Object.defineProperty(exports, "invert", { enumerable: true, get: function () { return matrix_1.invert; } });
var offscreen_1 = require("./util/offscreen");
Object.defineProperty(exports, "getOffScreenContext", { enumerable: true, get: function () { return offscreen_1.getOffScreenContext; } });
var register_1 = require("./animate/register");
Object.defineProperty(exports, "registerEasing", { enumerable: true, get: function () { return register_1.registerEasing; } });
exports.version = '0.5.11';
//# sourceMappingURL=index.js.map