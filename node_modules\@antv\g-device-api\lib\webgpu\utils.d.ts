/// <reference types="@webgpu/types" />
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MegaStateDescriptor, AttachmentState, QueryPool, SamplerBinding, StencilOp } from '../api';
import { BufferUsage, AddressMode, FilterMode, MipmapFilterMode, TextureDimension, PrimitiveTopology, CullMode, FrontFace, BlendFactor, BlendMode, CompareFunction, VertexStepMode, TextureUsage, QueryPoolType, Format } from '../api';
/**
 * @see https://developer.mozilla.org/en-US/docs/Web/API/GPUTexture/usage#value
 */
export declare function translateTextureUsage(usage: TextureUsage): GPUTextureUsageFlags;
/**
 * @see https://www.w3.org/TR/webgpu/#enumdef-gputextureformat
 */
export declare function translateTextureFormat(format: Format): GPUTextureFormat;
export declare function translateTextureDimension(dimension: TextureDimension): GPUTextureDimension;
/**
 * @see https://www.w3.org/TR/webgpu/#enumdef-gputextureviewdimension
 */
export declare function translateTextureViewDimension(dimension: TextureDimension): GPUTextureViewDimension;
export declare function translateBufferUsage(usage_: BufferUsage): GPUBufferUsageFlags;
export declare function translateAddressMode(wrapMode: AddressMode): GPUAddressMode;
export declare function translateMinMagFilter(texFilter: FilterMode): GPUFilterMode;
export declare function translateMipFilter(mipmapFilter: MipmapFilterMode): GPUFilterMode;
export declare function translateBindGroupSamplerBinding(sampler: SamplerBinding): GPUSamplerBindingLayout;
export declare function translateBindGroupTextureBinding(sampler: SamplerBinding): GPUTextureBindingLayout;
export declare function getPlatformBuffer(buffer_: Buffer): GPUBuffer;
export declare function getPlatformSampler(sampler_: Sampler): GPUSampler;
export declare function getPlatformQuerySet(queryPool_: QueryPool): GPUQuerySet;
export declare function translateQueryPoolType(type: QueryPoolType): GPUQueryType;
/**
 * @see https://www.w3.org/TR/webgpu/#primitive-state
 */
export declare function translateTopology(topology: PrimitiveTopology): GPUPrimitiveTopology;
/**
 * @see https://www.w3.org/TR/webgpu/#enumdef-gpucullmode
 */
export declare function translateCullMode(cullMode: CullMode): GPUCullMode;
/**
 * @see https://www.w3.org/TR/webgpu/#enumdef-gpufrontface
 */
export declare function translateFrontFace(frontFaceMode: FrontFace): GPUFrontFace;
export declare function translatePrimitiveState(topology: PrimitiveTopology, megaStateDescriptor: MegaStateDescriptor): GPUPrimitiveState;
/**
 * @see https://www.w3.org/TR/webgpu/#enumdef-gpublendfactor
 */
export declare function translateBlendFactor(factor: BlendFactor): GPUBlendFactor;
/**
 * @see https://www.w3.org/TR/webgpu/#enumdef-gpublendoperation
 */
export declare function translateBlendMode(mode: BlendMode): GPUBlendOperation;
export declare function translateColorState(attachmentState: AttachmentState, format: Format): GPUColorTargetState;
export declare function translateTargets(colorAttachmentFormats: (Format | null)[], megaStateDescriptor: MegaStateDescriptor): GPUColorTargetState[];
export declare function translateCompareFunction(compareFunction: CompareFunction): GPUCompareFunction;
export declare function translateStencilOperation(stencilOp: StencilOp): GPUStencilOperation;
/**
 * @see https://www.w3.org/TR/webgpu/#dictdef-gpudepthstencilstate
 */
export declare function translateDepthStencilState(format: Format | null, megaStateDescriptor: MegaStateDescriptor): GPUDepthStencilState | undefined;
export declare function translateIndexFormat(format: Format | null): GPUIndexFormat | undefined;
export declare function translateVertexStepMode(stepMode: VertexStepMode): GPUVertexStepMode;
export declare function translateVertexFormat(format: Format): GPUVertexFormat;
export declare function isFormatTextureCompressionBC(format: Format): boolean;
export declare function getFormatByteSizePerBlock(format: Format): number;
export declare function getFormatBlockSize(format: Format): number;
export declare function translateImageLayout(layout: GPUImageDataLayout, format: Format, mipWidth: number, mipHeight: number): void;
export declare function allocateAndCopyTypedBuffer(type: Format, sizeOrDstBuffer: number | ArrayBuffer, sizeInBytes?: boolean, copyBuffer?: ArrayBuffer): ArrayBufferView;
/**
 * Converts a half float to a number
 * @param value half float to convert
 * @returns converted half float
 */
export declare function halfFloat2Number(value: number): number;
export declare function getBlockInformationFromFormat(format: GPUTextureFormat): {
    width: number;
    height: number;
    length: number;
};
