{"ast": null, "code": "var _typeof = require(\"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\n//! moment.js locale configuration\n//! locale : French [fr]\n//! author : <PERSON> : https://github.com/jfroffice\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var monthsStrictRegex = /^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,\n    monthsShortStrictRegex = /(janv\\.?|févr\\.?|mars|avr\\.?|mai|juin|juil\\.?|août|sept\\.?|oct\\.?|nov\\.?|déc\\.?)/i,\n    monthsRegex = /(janv\\.?|févr\\.?|mars|avr\\.?|mai|juin|juil\\.?|août|sept\\.?|oct\\.?|nov\\.?|déc\\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,\n    monthsParse = [/^janv/i, /^févr/i, /^mars/i, /^avr/i, /^mai/i, /^juin/i, /^juil/i, /^août/i, /^sept/i, /^oct/i, /^nov/i, /^déc/i];\n  var fr = moment.defineLocale('fr', {\n    months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),\n    monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: monthsStrictRegex,\n    monthsShortStrictRegex: monthsShortStrictRegex,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),\n    weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),\n    weekdaysMin: 'di_lu_ma_me_je_ve_sa'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Aujourd’hui à] LT',\n      nextDay: '[Demain à] LT',\n      nextWeek: 'dddd [à] LT',\n      lastDay: '[Hier à] LT',\n      lastWeek: 'dddd [dernier à] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dans %s',\n      past: 'il y a %s',\n      s: 'quelques secondes',\n      ss: '%d secondes',\n      m: 'une minute',\n      mm: '%d minutes',\n      h: 'une heure',\n      hh: '%d heures',\n      d: 'un jour',\n      dd: '%d jours',\n      w: 'une semaine',\n      ww: '%d semaines',\n      M: 'un mois',\n      MM: '%d mois',\n      y: 'un an',\n      yy: '%d ans'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(er|)/,\n    ordinal: function ordinal(number, period) {\n      switch (period) {\n        // TODO: Return 'e' when day of month > 1. Move this case inside\n        // block for masculine words below.\n        // See https://github.com/moment/moment/issues/3375\n        case 'D':\n          return number + (number === 1 ? 'er' : '');\n\n        // Words with masculine grammatical gender: mois, trimestre, jour\n        default:\n        case 'M':\n        case 'Q':\n        case 'DDD':\n        case 'd':\n          return number + (number === 1 ? 'er' : 'e');\n\n        // Words with feminine grammatical gender: semaine\n        case 'w':\n        case 'W':\n          return number + (number === 1 ? 're' : 'e');\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return fr;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "monthsStrictRegex", "monthsShortStrictRegex", "monthsRegex", "<PERSON><PERSON><PERSON>e", "fr", "defineLocale", "months", "split", "monthsShort", "monthsShortRegex", "longMonthsParse", "shortMonthsParse", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "week", "dow", "doy"], "sources": ["G:/备份9/adminweb/node_modules/moment/locale/fr.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : French [fr]\n//! author : <PERSON> : https://github.com/jfroffice\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var monthsStrictRegex =\n            /^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,\n        monthsShortStrictRegex =\n            /(janv\\.?|févr\\.?|mars|avr\\.?|mai|juin|juil\\.?|août|sept\\.?|oct\\.?|nov\\.?|déc\\.?)/i,\n        monthsRegex =\n            /(janv\\.?|févr\\.?|mars|avr\\.?|mai|juin|juil\\.?|août|sept\\.?|oct\\.?|nov\\.?|déc\\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,\n        monthsParse = [\n            /^janv/i,\n            /^févr/i,\n            /^mars/i,\n            /^avr/i,\n            /^mai/i,\n            /^juin/i,\n            /^juil/i,\n            /^août/i,\n            /^sept/i,\n            /^oct/i,\n            /^nov/i,\n            /^déc/i,\n        ];\n\n    var fr = moment.defineLocale('fr', {\n        months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split(\n            '_'\n        ),\n        monthsShort:\n            'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split(\n                '_'\n            ),\n        monthsRegex: monthsRegex,\n        monthsShortRegex: monthsRegex,\n        monthsStrictRegex: monthsStrictRegex,\n        monthsShortStrictRegex: monthsShortStrictRegex,\n        monthsParse: monthsParse,\n        longMonthsParse: monthsParse,\n        shortMonthsParse: monthsParse,\n        weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),\n        weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),\n        weekdaysMin: 'di_lu_ma_me_je_ve_sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Aujourd’hui à] LT',\n            nextDay: '[Demain à] LT',\n            nextWeek: 'dddd [à] LT',\n            lastDay: '[Hier à] LT',\n            lastWeek: 'dddd [dernier à] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'dans %s',\n            past: 'il y a %s',\n            s: 'quelques secondes',\n            ss: '%d secondes',\n            m: 'une minute',\n            mm: '%d minutes',\n            h: 'une heure',\n            hh: '%d heures',\n            d: 'un jour',\n            dd: '%d jours',\n            w: 'une semaine',\n            ww: '%d semaines',\n            M: 'un mois',\n            MM: '%d mois',\n            y: 'un an',\n            yy: '%d ans',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(er|)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                // TODO: Return 'e' when day of month > 1. Move this case inside\n                // block for masculine words below.\n                // See https://github.com/moment/moment/issues/3375\n                case 'D':\n                    return number + (number === 1 ? 'er' : '');\n\n                // Words with masculine grammatical gender: mois, trimestre, jour\n                default:\n                case 'M':\n                case 'Q':\n                case 'DDD':\n                case 'd':\n                    return number + (number === 1 ? 'er' : 'e');\n\n                // Words with feminine grammatical gender: semaine\n                case 'w':\n                case 'W':\n                    return number + (number === 1 ? 're' : 'e');\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return fr;\n\n})));\n"], "mappings": ";AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,iBAAiB,GACb,0FAA0F;IAC9FC,sBAAsB,GAClB,mFAAmF;IACvFC,WAAW,GACP,wKAAwK;IAC5KC,WAAW,GAAG,CACV,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,CACV;EAEL,IAAIC,EAAE,GAAGL,MAAM,CAACM,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,sFAAsF,CAACC,KAAK,CAChG,GACJ,CAAC;IACDC,WAAW,EACP,gEAAgE,CAACD,KAAK,CAClE,GACJ,CAAC;IACLL,WAAW,EAAEA,WAAW;IACxBO,gBAAgB,EAAEP,WAAW;IAC7BF,iBAAiB,EAAEA,iBAAiB;IACpCC,sBAAsB,EAAEA,sBAAsB;IAC9CE,WAAW,EAAEA,WAAW;IACxBO,eAAe,EAAEP,WAAW;IAC5BQ,gBAAgB,EAAER,WAAW;IAC7BS,QAAQ,EAAE,qDAAqD,CAACL,KAAK,CAAC,GAAG,CAAC;IAC1EM,aAAa,EAAE,oCAAoC,CAACN,KAAK,CAAC,GAAG,CAAC;IAC9DO,WAAW,EAAE,sBAAsB,CAACP,KAAK,CAAC,GAAG,CAAC;IAC9CQ,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,oBAAoB;MAC7BC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,mBAAmB;MACtBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,cAAc;IACtCC,OAAO,EAAE,SAATA,OAAOA,CAAYC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV;QACA;QACA;QACA,KAAK,GAAG;UACJ,OAAOD,MAAM,IAAIA,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;;QAE9C;QACA;QACA,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;QACV,KAAK,GAAG;UACJ,OAAOA,MAAM,IAAIA,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;;QAE/C;QACA,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAOA,MAAM,IAAIA,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;MACnD;IACJ,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOjD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}