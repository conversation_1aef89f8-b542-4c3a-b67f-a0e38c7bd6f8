{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport { getUserList, getUserDetail, updateUserStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels as _getAgentLevels, updateUserLevel, updateUserBalance, updateUserGbDividend, deleteUser } from '@/api/user/user';\nimport { listUserWallet } from '@/api/user/wallet';\nimport { parseTime, formatDate } from '@/utils/date';\nimport axios from 'axios';\nexport default {\n  name: 'UserList',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        // 用户名/手机号\n        status: '',\n        // 状态\n        isManager: '',\n        // 用户等级\n        shareCode: '',\n        // 邀请码\n        referrerPhone: '',\n        // 邀请人手机号\n        email: '',\n        // 邮箱\n        dateRange: [],\n        startDate: '',\n        endDate: '',\n        contractAgreement: '',\n        // 合同状态\n        isGbDividend: '' // GB分红状态\n      },\n      // 代理级别选项\n      agentLevelOptions: [{\n        label: '普通会员',\n        value: '0'\n      }, {\n        label: '合伙人',\n        value: '1'\n      }, {\n        label: '联创',\n        value: '2'\n      }],\n      loading: false,\n      total: 0,\n      tableData: [],\n      // 充值相关\n      rechargeVisible: false,\n      rechargeUser: {},\n      rechargeForm: {\n        amount: 100,\n        remark: ''\n      },\n      rechargeRules: {\n        amount: [{\n          required: true,\n          message: '请输入充值金额',\n          trigger: 'blur'\n        }]\n      },\n      // 详情相关\n      detailVisible: false,\n      detailUser: {\n        username: '',\n        phone: '',\n        realName: '',\n        agentLevel: '',\n        teamCount: 0,\n        teamPerformance: 0,\n        createTime: '',\n        lastLoginTime: '',\n        balance: 0,\n        status: '1',\n        referrer: '',\n        inviteCode: '',\n        totalRecharge: 0,\n        totalWithdraw: 0,\n        commission: 0\n      },\n      // 银行卡相关\n      bankCardsVisible: false,\n      bankCardsLoading: false,\n      bankCards: [],\n      // 修改等级相关\n      changeLevelVisible: false,\n      currentUser: {},\n      levelForm: {\n        isManager: ''\n      },\n      levelRules: {\n        isManager: [{\n          required: true,\n          message: '请选择等级',\n          trigger: 'change'\n        }]\n      },\n      agentLevels: [],\n      // 存储代理等级列表\n      // 修改余额相关\n      modifyBalanceVisible: false,\n      modifyBalanceForm: {\n        newBalance: 0\n      },\n      modifyBalanceRules: {\n        newBalance: [{\n          required: true,\n          message: '请输入新余额',\n          trigger: 'blur'\n        }]\n      },\n      walletDialogVisible: false,\n      walletList: [],\n      walletLoading: false\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getAgentLevels(); // 获取代理等级列表\n  },\n  watch: {\n    // 监听日期范围变化\n    'listQuery.dateRange': function listQueryDateRange(val) {\n      if (val && val.length === 2) {\n        this.listQuery.startDate = formatDate(val[0]);\n        this.listQuery.endDate = formatDate(val[1]);\n      } else {\n        this.listQuery.startDate = '';\n        this.listQuery.endDate = '';\n      }\n    }\n  },\n  methods: {\n    // 获取列表数据\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              _context.next = 4;\n              return getUserList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0 || res.code === 200) {\n                // 确保数据存在\n                if (res.data) {\n                  _this.tableData = res.data.records || [];\n                  _this.total = res.data.total || 0;\n                } else {\n                  _this.tableData = [];\n                  _this.total = 0;\n                }\n              } else {\n                _this.$message.error(res.msg || '获取用户列表失败');\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取用户列表失败:', _context.t0);\n              _this.$message.error('获取用户列表失败');\n            case 12:\n              _context.prev = 12;\n              _this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 8, 12, 15]]);\n      }))();\n    },\n    // 搜索\n    handleSearch: function handleSearch() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    // 重置查询\n    resetQuery: function resetQuery() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        status: '',\n        isManager: '',\n        // 重置用户等级\n        shareCode: '',\n        // 重置邀请码\n        referrerPhone: '',\n        // 重置邀请人手机号\n        email: '',\n        // 重置邮箱\n        dateRange: [],\n        startDate: '',\n        endDate: '',\n        contractAgreement: '',\n        // 重置合同状态\n        isGbDividend: '' // 重置GB分红状态\n      };\n      this.getList();\n    },\n    // 格式化数字\n    formatNumber: function formatNumber(num) {\n      return num ? num.toLocaleString() : '0';\n    },\n    // 获取等级名称\n    getLevelName: function getLevelName(level) {\n      var levelMap = {\n        0: '普通会员',\n        1: '合伙人',\n        2: '联创'\n      };\n      return levelMap[level] || '未知等级';\n    },\n    // 获取等级标签类型\n    getLevelType: function getLevelType(level) {\n      var typeMap = {\n        0: 'info',\n        1: 'success',\n        2: 'warning'\n      };\n      return typeMap[level] || 'info';\n    },\n    // 处理状态变更\n    handleStatusChange: function handleStatusChange(row) {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return updateUserStatus(row.id, row.status);\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this2.$message.success(\"\".concat(row.status === 1 ? '启用' : '禁用', \"\\u6210\\u529F\"));\n              } else {\n                row.status = row.status === 1 ? 0 : 1;\n                _this2.$message.error(res.msg || '操作失败');\n              }\n              _context2.next = 11;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              row.status = row.status === 1 ? 0 : 1;\n              _this2.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    // 查看详情\n    handleDetail: function handleDetail(row) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return getUserDetail(row.id);\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this3.detailUser = res.data;\n                _this3.detailVisible = true;\n              } else {\n                _this3.$message.error(res.msg || '获取详情失败');\n              }\n              _context3.next = 10;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              _this3.$message.error('获取详情失败');\n            case 10:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    // 打开充值对话框\n    handleRecharge: function handleRecharge(row) {\n      this.rechargeUser = row;\n      this.rechargeForm = {\n        amount: 100,\n        remark: ''\n      };\n      this.rechargeVisible = true;\n    },\n    // 提交充值\n    submitRecharge: function submitRecharge() {\n      var _this4 = this;\n      this.$refs.rechargeForm.validate(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n            while (1) switch (_context4.prev = _context4.next) {\n              case 0:\n                if (!valid) {\n                  _context4.next = 11;\n                  break;\n                }\n                _context4.prev = 1;\n                _context4.next = 4;\n                return rechargeUser(_this4.rechargeUser.id, _this4.rechargeForm);\n              case 4:\n                res = _context4.sent;\n                if (res.code === 0 || res.code === 200) {\n                  _this4.$message.success('充值成功');\n                  _this4.rechargeVisible = false;\n                  _this4.getList(); // 刷新列表\n                } else {\n                  _this4.$message.error(res.msg || '充值失败');\n                }\n                _context4.next = 11;\n                break;\n              case 8:\n                _context4.prev = 8;\n                _context4.t0 = _context4[\"catch\"](1);\n                _this4.$message.error('充值失败');\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }, _callee4, null, [[1, 8]]);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    // 分页相关\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');\n    },\n    // 格式化日期\n    formatDate: function formatDate(time) {\n      if (!time) return '';\n      return parseTime(time, 'yyyy-MM-dd');\n    },\n    // 重置密码\n    handleReset: function handleReset(row) {\n      var _this5 = this;\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return resetUserPassword(row.id);\n            case 3:\n              res = _context5.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this5.$message.success('密码重置成功');\n              } else {\n                _this5.$message.error(res.msg || '密码重置失败');\n              }\n              _context5.next = 10;\n              break;\n            case 7:\n              _context5.prev = 7;\n              _context5.t0 = _context5[\"catch\"](0);\n              _this5.$message.error('密码重置失败');\n            case 10:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消重置，不做任何操作\n      });\n    },\n    // 查看银行\n    handleBankCards: function handleBankCards(row) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _this6.bankCardsVisible = true;\n              _this6.bankCardsLoading = true;\n              _context6.prev = 2;\n              _context6.next = 5;\n              return getUserBankCards(row.id);\n            case 5:\n              res = _context6.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this6.bankCards = res.data || [];\n              } else {\n                _this6.$message.error(res.msg || '获取银行卡列表失败');\n              }\n              _context6.next = 13;\n              break;\n            case 9:\n              _context6.prev = 9;\n              _context6.t0 = _context6[\"catch\"](2);\n              console.error('获取银行卡失败:', _context6.t0); // 添加错误日志\n              _this6.$message.error('获取银行卡列表失败');\n            case 13:\n              _context6.prev = 13;\n              _this6.bankCardsLoading = false;\n              return _context6.finish(13);\n            case 16:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6, null, [[2, 9, 13, 16]]);\n      }))();\n    },\n    // 获取代理等级列表\n    getAgentLevels: function getAgentLevels() {\n      var _this7 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.prev = 0;\n              _context7.next = 3;\n              return _getAgentLevels();\n            case 3:\n              res = _context7.sent;\n              // 需要添加这个API\n              if (res.code === 0 || res.code === 200) {\n                _this7.agentLevels = res.data || [];\n              }\n              _context7.next = 10;\n              break;\n            case 7:\n              _context7.prev = 7;\n              _context7.t0 = _context7[\"catch\"](0);\n              console.error('获取代理等级列表失败:', _context7.t0);\n            case 10:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7, null, [[0, 7]]);\n      }))();\n    },\n    // 打开修改等级对话框\n    handleChangeLevel: function handleChangeLevel(row) {\n      this.currentUser = row;\n      this.levelForm.isManager = row.isManager;\n      this.changeLevelVisible = true;\n    },\n    // 提交修改等级\n    submitChangeLevel: function submitChangeLevel() {\n      var _this8 = this;\n      this.$refs.levelForm.validate(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n            while (1) switch (_context8.prev = _context8.next) {\n              case 0:\n                if (!valid) {\n                  _context8.next = 12;\n                  break;\n                }\n                _context8.prev = 1;\n                _context8.next = 4;\n                return updateUserLevel(_this8.currentUser.id, _this8.levelForm.isManager);\n              case 4:\n                res = _context8.sent;\n                if (res.code === 0) {\n                  _this8.$message.success('修改等级成功');\n                  _this8.changeLevelVisible = false;\n                  _this8.getList(); // 刷新列表\n                } else {\n                  _this8.$message.error(res.msg || '修改等级失败');\n                }\n                _context8.next = 12;\n                break;\n              case 8:\n                _context8.prev = 8;\n                _context8.t0 = _context8[\"catch\"](1);\n                console.error('修改等级失败:', _context8.t0);\n                _this8.$message.error('修改等级失败');\n              case 12:\n              case \"end\":\n                return _context8.stop();\n            }\n          }, _callee8, null, [[1, 8]]);\n        }));\n        return function (_x2) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    },\n    // 打开修改余额对话框\n    handleModifyBalance: function handleModifyBalance(row) {\n      this.currentUser = row;\n      this.modifyBalanceForm.newBalance = row.availableBalance;\n      this.modifyBalanceVisible = true;\n    },\n    // 提交修改余额\n    submitModifyBalance: function submitModifyBalance() {\n      var _this9 = this;\n      this.$refs.modifyBalanceForm.validate(/*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n            while (1) switch (_context9.prev = _context9.next) {\n              case 0:\n                if (!valid) {\n                  _context9.next = 14;\n                  break;\n                }\n                _context9.prev = 1;\n                console.log('准备修改余额:', {\n                  userId: _this9.currentUser.id,\n                  newBalance: _this9.modifyBalanceForm.newBalance\n                });\n                _context9.next = 5;\n                return updateUserBalance(_this9.currentUser.id, _this9.modifyBalanceForm.newBalance);\n              case 5:\n                res = _context9.sent;\n                console.log('修改余额响应:', res);\n                if (res.code === 0 || res.code === 200) {\n                  _this9.$message.success('修改余额成功');\n                  _this9.modifyBalanceVisible = false;\n                  _this9.getList(); // 刷新列表\n                } else {\n                  _this9.$message.error(res.msg || '修改余额失败');\n                }\n                _context9.next = 14;\n                break;\n              case 10:\n                _context9.prev = 10;\n                _context9.t0 = _context9[\"catch\"](1);\n                console.error('修改余额失败:', _context9.t0);\n                _this9.$message.error('修改余额失败');\n              case 14:\n              case \"end\":\n                return _context9.stop();\n            }\n          }, _callee9, null, [[1, 10]]);\n        }));\n        return function (_x3) {\n          return _ref4.apply(this, arguments);\n        };\n      }());\n    },\n    // 处理GB分红状态变更\n    handleGbDividendChange: function handleGbDividendChange(row) {\n      var _this10 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              _context10.prev = 0;\n              _context10.next = 3;\n              return updateUserGbDividend(row.id, row.isGbDividend);\n            case 3:\n              res = _context10.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this10.$message.success(\"\".concat(row.isGbDividend === 1 ? '开启' : '关闭', \"GB\\u5206\\u7EA2\\u6210\\u529F\"));\n              } else {\n                row.isGbDividend = row.isGbDividend === 1 ? 0 : 1;\n                _this10.$message.error(res.msg || '操作失败');\n              }\n              _context10.next = 11;\n              break;\n            case 7:\n              _context10.prev = 7;\n              _context10.t0 = _context10[\"catch\"](0);\n              row.isGbDividend = row.isGbDividend === 1 ? 0 : 1;\n              _this10.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee10, null, [[0, 7]]);\n      }))();\n    },\n    // 处理删除用户\n    handleDelete: function handleDelete(row) {\n      var _this11 = this;\n      this.$confirm('确认要删除该用户吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              _context11.prev = 0;\n              _context11.next = 3;\n              return deleteUser(row.id);\n            case 3:\n              res = _context11.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this11.$message.success('删除成功');\n                _this11.getList(); // 刷新列表\n              } else {\n                _this11.$message.error(res.msg || '删除失败');\n              }\n              _context11.next = 10;\n              break;\n            case 7:\n              _context11.prev = 7;\n              _context11.t0 = _context11[\"catch\"](0);\n              _this11.$message.error('删除失败');\n            case 10:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消删除，不做任何操作\n      });\n    },\n    handleWalletList: function handleWalletList(row) {\n      var _this12 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee12() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              _this12.walletDialogVisible = true;\n              _this12.walletLoading = true;\n              _context12.prev = 2;\n              _context12.next = 5;\n              return listUserWallet({\n                userId: row.id,\n                pageNum: 1,\n                pageSize: 100\n              });\n            case 5:\n              res = _context12.sent;\n              if (res.code === 0 && res.data && res.data.records) {\n                _this12.walletList = res.data.records;\n              } else {\n                _this12.walletList = [];\n              }\n              _context12.next = 13;\n              break;\n            case 9:\n              _context12.prev = 9;\n              _context12.t0 = _context12[\"catch\"](2);\n              _this12.walletList = [];\n              _this12.$message.error('获取钱包列表失败');\n            case 13:\n              _this12.walletLoading = false;\n            case 14:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee12, null, [[2, 9]]);\n      }))();\n    },\n    formatAddress: function formatAddress(addr) {\n      if (!addr) return '';\n      if (addr.length <= 16) return addr;\n      return addr.slice(0, 6) + '...' + addr.slice(-6);\n    }\n  }\n};", "map": {"version": 3, "names": ["getUserList", "getUserDetail", "updateUserStatus", "resetUserPassword", "rechargeUser", "getUserBankCards", "getAgentLevels", "updateUserLevel", "updateUserBalance", "updateUserGbDividend", "deleteUser", "listUserWallet", "parseTime", "formatDate", "axios", "name", "data", "list<PERSON>uery", "page", "limit", "username", "status", "is<PERSON>anager", "shareCode", "referrerPhone", "email", "date<PERSON><PERSON><PERSON>", "startDate", "endDate", "contractAgreement", "isGbDividend", "agentLevelOptions", "label", "value", "loading", "total", "tableData", "rechargeVisible", "rechargeForm", "amount", "remark", "rechargeRules", "required", "message", "trigger", "detailVisible", "detailUser", "phone", "realName", "agentLevel", "teamCount", "teamPerformance", "createTime", "lastLoginTime", "balance", "referrer", "inviteCode", "totalRecharge", "totalWithdraw", "commission", "bankCardsVisible", "bankCardsLoading", "bankCards", "changeLevelVisible", "currentUser", "levelForm", "levelRules", "agentLevels", "modifyBalanceVisible", "modifyBalanceForm", "newBalance", "modifyBalanceRules", "walletDialogVisible", "walletList", "walletLoading", "created", "getList", "watch", "listQueryDateRange", "val", "length", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "records", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleSearch", "reset<PERSON><PERSON>y", "formatNumber", "num", "toLocaleString", "getLevelName", "level", "levelMap", "getLevelType", "typeMap", "handleStatusChange", "row", "_this2", "_callee2", "_callee2$", "_context2", "id", "success", "concat", "handleDetail", "_this3", "_callee3", "_callee3$", "_context3", "handleRecharge", "submit<PERSON>echarge", "_this4", "$refs", "validate", "_ref", "_callee4", "valid", "_callee4$", "_context4", "_x", "apply", "arguments", "handleSizeChange", "handleCurrentChange", "formatDateTime", "time", "handleReset", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_callee5", "_callee5$", "_context5", "handleBankCards", "_this6", "_callee6", "_callee6$", "_context6", "_this7", "_callee7", "_callee7$", "_context7", "handleChangeLevel", "submitChangeLevel", "_this8", "_ref3", "_callee8", "_callee8$", "_context8", "_x2", "handleModifyBalance", "availableBalance", "submitModifyBalance", "_this9", "_ref4", "_callee9", "_callee9$", "_context9", "log", "userId", "_x3", "handleGbDividendChange", "_this10", "_callee10", "_callee10$", "_context10", "handleDelete", "_this11", "_callee11", "_callee11$", "_context11", "handleWalletList", "_this12", "_callee12", "_callee12$", "_context12", "pageNum", "pageSize", "formatAddress", "addr", "slice"], "sources": ["src/views/user/list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.username\"\r\n              placeholder=\"用户名/手机号\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 180px\"\r\n            />\r\n          </el-col>         \r\n\r\n          <el-col :span=\"3\">\r\n            <el-select\r\n              v-model=\"listQuery.status\"\r\n              placeholder=\"账户状态\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            >\r\n              <el-option label=\"正常\" value=\"1\" />\r\n              <el-option label=\"禁用\" value=\"0\" />\r\n            </el-select>\r\n          </el-col>\r\n        \r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.shareCode\"\r\n              placeholder=\"邀请码\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.referrerPhone\"\r\n              placeholder=\"邀请人手机号\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.email\"\r\n              placeholder=\"邮箱\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"listQuery.dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              class=\"filter-item date-range-picker\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"18\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n            <!-- <el-button type=\"warning\" icon=\"el-icon-download\">导出</el-button> -->\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"UUID\" prop=\"userNo\" width=\"130\" align=\"center\" />\r\n        <el-table-column label=\"用户名称\" prop=\"username\" align=\"center\"  width=\"120\"/>\r\n        <el-table-column label=\"手机号码\" prop=\"phone\" align=\"center\" width=\"120\" />\r\n        <el-table-column label=\"邮箱\" prop=\"email\" align=\"center\" width=\"180\" />\r\n        <el-table-column label=\"推荐人\" align=\"center\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.referrerPhone\">\r\n              {{ scope.row.referrerPhone }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ scope.row.referrerShareCode }}</el-tag>\r\n            </div>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"分享码\" prop=\"shareCode\" align=\"center\" width=\"120\" />\r\n        \r\n        <el-table-column label=\"账户状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              :active-value=\"0\"\r\n              :inactive-value=\"1\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"资金账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.availableBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"跟单账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.copyTradeBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"佣金账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.commissionBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"跟单冻结账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.usageFrozenBlance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"提现冻结余额\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.frozenBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n      \r\n      \r\n        <el-table-column label=\"操作\" align=\"center\" width=\"400\" fixed=\"right\">\r\n          <template v-slot=\"{ row }\">\r\n            <el-button type=\"text\" @click=\"handleDetail(row)\">详情</el-button>\r\n            <el-button type=\"text\" @click=\"handleRecharge(row)\">充值</el-button>\r\n            <el-button type=\"text\" @click=\"handleWalletList(row)\">充值地址列表</el-button>\r\n            <el-button type=\"text\" @click=\"handleBankCards(row)\">提现地址列表</el-button>\r\n            <el-button type=\"text\" style=\"color: #f56c6c\" @click=\"handleReset(row)\">重置密码</el-button>\r\n           \r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        />\r\n      </div>\r\n\r\n      <!-- 用户详情对话框 -->\r\n      <el-dialog\r\n        title=\"用户详情\"\r\n        :visible.sync=\"detailVisible\"\r\n        width=\"800px\"\r\n        :close-on-click-modal=\"false\"\r\n        custom-class=\"user-detail-dialog\"\r\n      >\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"UUID\">{{ detailUser.userNo }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">{{ detailUser.phone }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"邮箱\">{{ detailUser.email }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户名称\">{{ detailUser.username }}</el-descriptions-item> \r\n          <el-descriptions-item label=\"团队新增人数\">{{ formatNumber(detailUser.teamTodayCount) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"注册时间\">{{ formatDateTime(detailUser.createTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"最后登录\">{{ formatDateTime(detailUser.updateTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"资金账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.availableBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"跟单账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.copyTradeBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n            <el-descriptions-item label=\"佣金账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.commissionBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"跟单冻结账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.usageFrozenBlance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"提现冻结余额\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.frozenBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"账户状态\">\r\n            <el-tag :type=\"detailUser.status === 1 ? 'success' : 'danger'\">\r\n              {{ detailUser.status === 1 ? '正常' : '禁用' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n         \r\n          <el-descriptions-item label=\"推荐人\">\r\n            <template v-if=\"detailUser.referrerPhone\">\r\n              {{ detailUser.referrerPhone }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ detailUser.referrerShareCode }}</el-tag>\r\n            </template>\r\n            <span v-else>-</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"邀请码\">{{ detailUser.shareCode || '-' }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </el-dialog>\r\n\r\n      <!-- 用户充值对话框 -->\r\n      <el-dialog\r\n        title=\"用户充值\"\r\n        :visible.sync=\"rechargeVisible\"\r\n        width=\"500px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"rechargeForm\"\r\n          :model=\"rechargeForm\"\r\n          :rules=\"rechargeRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"用户手机号\">\r\n            <span>{{ rechargeUser.phone }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"账户资金余额`\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(rechargeUser.availableBalance) || '0' }}USDT</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"充值金额\" prop=\"amount\">\r\n            <el-input-number\r\n              v-model=\"rechargeForm.amount\"\r\n              :precision=\"2\"\r\n              :step=\"100\"\r\n              style=\"width: 200px\"\r\n            />\r\n            <div style=\"color:#999;font-size:12px;\">可输入负数进行扣款</div>\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"remark\">\r\n            <el-input\r\n              v-model=\"rechargeForm.remark\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"请输入充值备注\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"rechargeVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitRecharge\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 银行卡列表对话框 -->\r\n      <el-dialog\r\n        title=\"提现钱包地址列表\"\r\n        :visible.sync=\"bankCardsVisible\"\r\n        width=\"1000px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-table\r\n          :data=\"bankCards\"\r\n          border\r\n          style=\"width: 100%\"\r\n          v-loading=\"bankCardsLoading\"\r\n        >\r\n          <el-table-column label=\"钱包地址\" prop=\"chainAddress\" align=\"center\" width=\"360\" />   \r\n          <el-table-column label=\"链名称\" prop=\"chainName\" align=\"center\" width=\"150\" />   \r\n          <el-table-column label=\"是否默认\" prop=\"isDefault\" align=\"center\" width=\"120\" />\r\n          <el-table-column label=\"创建时间\" prop=\"createTime\" align=\"center\" width=\"160\" />\r\n          <el-table-column label=\"更新时间\" prop=\"updateTime\" align=\"center\" width=\"160\" />\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"bankCardsVisible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 修改等级对话框 -->\r\n      <el-dialog\r\n        title=\"修改代理等级\"\r\n        :visible.sync=\"changeLevelVisible\"\r\n        width=\"400px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"levelForm\"\r\n          :model=\"levelForm\"\r\n          :rules=\"levelRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"当前等级\">\r\n            <el-tag :type=\"getLevelType(currentUser.isManager)\">\r\n              {{ getLevelName(currentUser.isManager) }}\r\n            </el-tag>\r\n          </el-form-item>\r\n          <el-form-item label=\"新等级\" prop=\"isManager\">\r\n            <el-select v-model=\"levelForm.isManager\" placeholder=\"请选择等级\">\r\n              <el-option label=\"普通会员\" :value=\"0\" />\r\n              <el-option label=\"合伙人\" :value=\"1\" />\r\n              <el-option label=\"联创\" :value=\"2\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"changeLevelVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitChangeLevel\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 修改余额对话框 -->\r\n      <el-dialog\r\n        title=\"修改账户余额\"\r\n        :visible.sync=\"modifyBalanceVisible\"\r\n        width=\"400px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"modifyBalanceForm\"\r\n          :model=\"modifyBalanceForm\"\r\n          :rules=\"modifyBalanceRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"用户名\">\r\n            <span>{{ currentUser.username }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号\">\r\n            <span>{{ currentUser.phone }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"当前余额\">\r\n            <span style=\"color: #67C23A\">¥{{ formatNumber(currentUser.availableBalance) || '0' }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"新余额\" prop=\"newBalance\">\r\n            <el-input-number\r\n              v-model=\"modifyBalanceForm.newBalance\"\r\n              :precision=\"2\"\r\n              :step=\"100\"\r\n              :controls-position=\"'right'\"\r\n              :min=\"-999999999\"\r\n              style=\"width: 200px\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"modifyBalanceVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitModifyBalance\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 钱包地址列表对话框 -->\r\n      <el-dialog\r\n        title=\"充值钱包地址列表\"\r\n        :visible.sync=\"walletDialogVisible\"\r\n        width=\"1000px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-table :data=\"walletList\" v-loading=\"walletLoading\" border>\r\n          <el-table-column label=\"链名称\" prop=\"chainName\" align=\"center\" />\r\n          <el-table-column label=\"链地址\" prop=\"chainAddress\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" :content=\"scope.row.chainAddress\" placement=\"top\">\r\n                <span style=\"cursor:pointer;\">\r\n                  {{ formatAddress(scope.row.chainAddress) }}\r\n                </span>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"BNB余额\" prop=\"bnbBalance\" align=\"center\" />\r\n          <el-table-column label=\"USDT余额\" prop=\"usdtBalance\" align=\"center\" />\r\n          <el-table-column label=\"创建时间\" prop=\"createTime\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.createTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"更新时间\" prop=\"updateTime\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.updateTime) }}\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"walletDialogVisible = false\">关闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getUserList, getUserDetail, updateUserStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels, updateUserLevel, updateUserBalance, updateUserGbDividend, deleteUser } from '@/api/user/user'\r\nimport { listUserWallet } from '@/api/user/wallet'\r\nimport { parseTime, formatDate } from '@/utils/date'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'UserList',\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',      // 用户名/手机号\r\n        status: '',        // 状态\r\n        isManager: '',     // 用户等级\r\n        shareCode: '',     // 邀请码\r\n        referrerPhone: '', // 邀请人手机号\r\n        email: '',         // 邮箱\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: '',\r\n        contractAgreement: '', // 合同状态\r\n        isGbDividend: ''      // GB分红状态\r\n      },\r\n      // 代理级别选项\r\n      agentLevelOptions: [\r\n        { label: '普通会员', value: '0' },\r\n        { label: '合伙人', value: '1' },\r\n        { label: '联创', value: '2' },\r\n      \r\n      ],\r\n      loading: false,\r\n      total: 0,\r\n      tableData: [],\r\n      // 充值相关\r\n      rechargeVisible: false,\r\n      rechargeUser: {},\r\n      rechargeForm: {\r\n        amount: 100,\r\n        remark: ''\r\n      },\r\n      rechargeRules: {\r\n        amount: [\r\n          { required: true, message: '请输入充值金额', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 详情相关\r\n      detailVisible: false,\r\n      detailUser: {\r\n        username: '',\r\n        phone: '',\r\n        realName: '',\r\n        agentLevel: '',\r\n        teamCount: 0,\r\n        teamPerformance: 0,\r\n        createTime: '',\r\n        lastLoginTime: '',\r\n        balance: 0,\r\n        status: '1',\r\n        referrer: '',\r\n        inviteCode: '',\r\n        totalRecharge: 0,\r\n        totalWithdraw: 0,\r\n        commission: 0\r\n      },\r\n      // 银行卡相关\r\n      bankCardsVisible: false,\r\n      bankCardsLoading: false,\r\n      bankCards: [],\r\n      // 修改等级相关\r\n      changeLevelVisible: false,\r\n      currentUser: {},\r\n      levelForm: {\r\n        isManager: ''\r\n      },\r\n      levelRules: {\r\n        isManager: [\r\n          { required: true, message: '请选择等级', trigger: 'change' }\r\n        ]\r\n      },\r\n      agentLevels: [], // 存储代理等级列表\r\n      // 修改余额相关\r\n      modifyBalanceVisible: false,\r\n      modifyBalanceForm: {\r\n        newBalance: 0\r\n      },\r\n      modifyBalanceRules: {\r\n        newBalance: [\r\n          { required: true, message: '请输入新余额', trigger: 'blur' }\r\n        ]\r\n      },\r\n      walletDialogVisible: false,\r\n      walletList: [],\r\n      walletLoading: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getAgentLevels() // 获取代理等级列表\r\n  },\r\n  watch: {\r\n    // 监听日期范围变化\r\n    'listQuery.dateRange'(val) {\r\n      if (val && val.length === 2) {\r\n        this.listQuery.startDate = formatDate(val[0])\r\n        this.listQuery.endDate = formatDate(val[1])\r\n      } else {\r\n        this.listQuery.startDate = ''\r\n        this.listQuery.endDate = ''\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取列表数据\r\n    async getList() {\r\n      this.loading = true\r\n      try { \r\n        const res = await getUserList(this.listQuery)  \r\n        if (res.code === 0 || res.code === 200) {\r\n          // 确保数据存在\r\n          if (res.data) {\r\n            this.tableData = res.data.records || []\r\n            this.total = res.data.total || 0 \r\n          } else {\r\n            this.tableData = []\r\n            this.total = 0\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '获取用户列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取用户列表失败:', error)\r\n        this.$message.error('获取用户列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch() {\r\n      this.listQuery.page = 1\r\n      this.getList()\r\n    },\r\n\r\n    // 重置查询\r\n    resetQuery() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        status: '',\r\n        isManager: '',     // 重置用户等级\r\n        shareCode: '',     // 重置邀请码\r\n        referrerPhone: '', // 重置邀请人手机号\r\n        email: '',         // 重置邮箱\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: '',\r\n        contractAgreement: '', // 重置合同状态\r\n        isGbDividend: ''      // 重置GB分红状态\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化数字\r\n    formatNumber(num) {\r\n      return num ? num.toLocaleString() : '0'\r\n    },\r\n\r\n    // 获取等级名称\r\n    getLevelName(level) {\r\n      const levelMap = {\r\n        0: '普通会员',\r\n        1: '合伙人',\r\n        2: '联创'\r\n      }\r\n      return levelMap[level] || '未知等级'\r\n    },\r\n\r\n    // 获取等级标签类型\r\n    getLevelType(level) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'warning'\r\n      }\r\n      return typeMap[level] || 'info'\r\n    },\r\n\r\n    // 处理状态变更\r\n    async handleStatusChange(row) {\r\n      try {\r\n        const res = await updateUserStatus(row.id, row.status)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.status === 1 ? '启用' : '禁用'}成功`)\r\n        } else {\r\n          row.status = row.status === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.status = row.status === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 查看详情\r\n    async handleDetail(row) {\r\n      try {\r\n        const res = await getUserDetail(row.id)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.detailUser = res.data\r\n          this.detailVisible = true\r\n        } else {\r\n          this.$message.error(res.msg || '获取详情失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取详情失败')\r\n      }\r\n    },\r\n\r\n    // 打开充值对话框\r\n    handleRecharge(row) {\r\n      this.rechargeUser = row\r\n      this.rechargeForm = {\r\n        amount: 100,\r\n        remark: ''\r\n      }\r\n      this.rechargeVisible = true\r\n    },\r\n\r\n    // 提交充值\r\n    submitRecharge() {\r\n      this.$refs.rechargeForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await rechargeUser(this.rechargeUser.id, this.rechargeForm)\r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('充值成功')\r\n              this.rechargeVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '充值失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('充值失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 分页相关\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(time) {\r\n      if (!time) return ''\r\n      return parseTime(time, 'yyyy-MM-dd')\r\n    },\r\n\r\n    // 重置密码\r\n    handleReset(row) {\r\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await resetUserPassword(row.id)\r\n          if (res.code === 0 || res.code === 200) {\r\n            this.$message.success('密码重置成功')\r\n          } else {\r\n            this.$message.error(res.msg || '密码重置失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('密码重置失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消重置，不做任何操作\r\n      })\r\n    },\r\n\r\n    // 查看银行\r\n    async handleBankCards(row) {\r\n      \r\n      this.bankCardsVisible = true\r\n      this.bankCardsLoading = true\r\n      try {\r\n        const res = await getUserBankCards(row.id)\r\n       \r\n        if (res.code === 0 || res.code === 200) {\r\n          this.bankCards = res.data || []\r\n        } else {\r\n          this.$message.error(res.msg || '获取银行卡列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取银行卡失败:', error)  // 添加错误日志\r\n        this.$message.error('获取银行卡列表失败')\r\n      } finally {\r\n        this.bankCardsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取代理等级列表\r\n    async getAgentLevels() {\r\n      try {\r\n        const res = await getAgentLevels() // 需要添加这个API\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.agentLevels = res.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取代理等级列表失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 打开修改等级对话框\r\n    handleChangeLevel(row) {\r\n      this.currentUser = row\r\n      this.levelForm.isManager = row.isManager\r\n      this.changeLevelVisible = true\r\n    },\r\n    \r\n    // 提交修改等级\r\n    submitChangeLevel() {\r\n      this.$refs.levelForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await updateUserLevel(\r\n              this.currentUser.id,\r\n              this.levelForm.isManager\r\n            )\r\n            if (res.code === 0) {\r\n              this.$message.success('修改等级成功')\r\n              this.changeLevelVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '修改等级失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改等级失败:', error)\r\n            this.$message.error('修改等级失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 打开修改余额对话框\r\n    handleModifyBalance(row) {\r\n      this.currentUser = row\r\n      this.modifyBalanceForm.newBalance = row.availableBalance\r\n      this.modifyBalanceVisible = true\r\n    },\r\n\r\n    // 提交修改余额\r\n    submitModifyBalance() {\r\n      this.$refs.modifyBalanceForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            console.log('准备修改余额:', {\r\n              userId: this.currentUser.id,\r\n              newBalance: this.modifyBalanceForm.newBalance\r\n            })\r\n            \r\n            const res = await updateUserBalance(\r\n              this.currentUser.id, \r\n              this.modifyBalanceForm.newBalance\r\n            )\r\n            \r\n            console.log('修改余额响应:', res)\r\n            \r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('修改余额成功')\r\n              this.modifyBalanceVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '修改余额失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改余额失败:', error)\r\n            this.$message.error('修改余额失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理GB分红状态变更\r\n    async handleGbDividendChange(row) {\r\n      try {\r\n        const res = await updateUserGbDividend(row.id, row.isGbDividend)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.isGbDividend === 1 ? '开启' : '关闭'}GB分红成功`)\r\n        } else {\r\n          row.isGbDividend = row.isGbDividend === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.isGbDividend = row.isGbDividend === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 处理删除用户\r\n    handleDelete(row) {\r\n      this.$confirm('确认要删除该用户吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await deleteUser(row.id)\r\n          if (res.code === 0 || res.code === 200) {\r\n            this.$message.success('删除成功')\r\n            this.getList() // 刷新列表\r\n          } else {\r\n            this.$message.error(res.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消删除，不做任何操作\r\n      })\r\n    },\r\n\r\n    async handleWalletList(row) {\r\n      this.walletDialogVisible = true;\r\n      this.walletLoading = true;\r\n      try {\r\n        const res = await listUserWallet({ userId: row.id, pageNum: 1, pageSize: 100 });\r\n        if (res.code === 0 && res.data && res.data.records) {\r\n          this.walletList = res.data.records;\r\n        } else {\r\n          this.walletList = [];\r\n        }\r\n      } catch (e) {\r\n        this.walletList = [];\r\n        this.$message.error('获取钱包列表失败');\r\n      }\r\n      this.walletLoading = false;\r\n    },\r\n\r\n    formatAddress(addr) {\r\n      if (!addr) return '';\r\n      if (addr.length <= 16) return addr;\r\n      return addr.slice(0, 6) + '...' + addr.slice(-6);\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n  \r\n  .filter-row {\r\n    margin-bottom: 20px;\r\n    \r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  \r\n  .filter-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .date-range-picker {\r\n    width: 100%;\r\n  }\r\n  \r\n  .el-button {\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .el-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n// 修改对话框样式\r\n.user-detail-dialog {\r\n  ::v-deep .el-dialog__body {\r\n    padding: 10px 20px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__header {\r\n    padding: 15px 20px 10px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__footer {\r\n    padding: 10px 20px 15px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  margin: 0;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding-top: 0;\r\n}\r\n</style>"], "mappings": ";;;AAiYA,SAAAA,WAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,cAAA,IAAAA,eAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,oBAAA,EAAAC,UAAA;AACA,SAAAC,cAAA;AACA,SAAAC,SAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QAAA;QACAC,MAAA;QAAA;QACAC,SAAA;QAAA;QACAC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,iBAAA;QAAA;QACAC,YAAA;MACA;MACA;MACAC,iBAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EAEA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACAjC,YAAA;MACAkC,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,aAAA;QACAF,MAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,aAAA;MACAC,UAAA;QACA1B,QAAA;QACA2B,KAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,eAAA;QACAC,UAAA;QACAC,aAAA;QACAC,OAAA;QACAjC,MAAA;QACAkC,QAAA;QACAC,UAAA;QACAC,aAAA;QACAC,aAAA;QACAC,UAAA;MACA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,kBAAA;MACAC,WAAA;MACAC,SAAA;QACA3C,SAAA;MACA;MACA4C,UAAA;QACA5C,SAAA,GACA;UAAAoB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAuB,WAAA;MAAA;MACA;MACAC,oBAAA;MACAC,iBAAA;QACAC,UAAA;MACA;MACAC,kBAAA;QACAD,UAAA,GACA;UAAA5B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA4B,mBAAA;MACAC,UAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAtE,cAAA;EACA;EACAuE,KAAA;IACA;IACA,gCAAAC,mBAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA,CAAAC,MAAA;QACA,KAAA/D,SAAA,CAAAU,SAAA,GAAAd,UAAA,CAAAkE,GAAA;QACA,KAAA9D,SAAA,CAAAW,OAAA,GAAAf,UAAA,CAAAkE,GAAA;MACA;QACA,KAAA9D,SAAA,CAAAU,SAAA;QACA,KAAAV,SAAA,CAAAW,OAAA;MACA;IACA;EACA;EACAqD,OAAA;IACA;IACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAhD,OAAA;cAAAwD,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEA5F,WAAA,CAAAkF,KAAA,CAAAjE,SAAA;YAAA;cAAAsE,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA;gBACA,IAAAP,GAAA,CAAAvE,IAAA;kBACAkE,KAAA,CAAA9C,SAAA,GAAAmD,GAAA,CAAAvE,IAAA,CAAA+E,OAAA;kBACAb,KAAA,CAAA/C,KAAA,GAAAoD,GAAA,CAAAvE,IAAA,CAAAmB,KAAA;gBACA;kBACA+C,KAAA,CAAA9C,SAAA;kBACA8C,KAAA,CAAA/C,KAAA;gBACA;cACA;gBACA+C,KAAA,CAAAc,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,cAAAP,QAAA,CAAAS,EAAA;cACAjB,KAAA,CAAAc,QAAA,CAAAC,KAAA;YAAA;cAAAP,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAAhD,OAAA;cAAA,OAAAwD,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IAEA;IAEA;IACAiB,YAAA,WAAAA,aAAA;MACA,KAAAtF,SAAA,CAAAC,IAAA;MACA,KAAA0D,OAAA;IACA;IAEA;IACA4B,UAAA,WAAAA,WAAA;MACA,KAAAvF,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;QAAA;QACAC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,iBAAA;QAAA;QACAC,YAAA;MACA;MACA,KAAA8C,OAAA;IACA;IAEA;IACA6B,YAAA,WAAAA,aAAAC,GAAA;MACA,OAAAA,GAAA,GAAAA,GAAA,CAAAC,cAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAC,KAAA;MACA,IAAAC,QAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,KAAA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAAF,KAAA;MACA,IAAAG,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAH,KAAA;IACA;IAEA;IACAI,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAAhC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+B,SAAA;QAAA,IAAA7B,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA6B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3B,IAAA,GAAA2B,SAAA,CAAA1B,IAAA;YAAA;cAAA0B,SAAA,CAAA3B,IAAA;cAAA2B,SAAA,CAAA1B,IAAA;cAAA,OAEA1F,gBAAA,CAAAgH,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAA7F,MAAA;YAAA;cAAAkE,GAAA,GAAA+B,SAAA,CAAAzB,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAqB,MAAA,CAAAnB,QAAA,CAAAwB,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAA7F,MAAA;cACA;gBACA6F,GAAA,CAAA7F,MAAA,GAAA6F,GAAA,CAAA7F,MAAA;gBACA8F,MAAA,CAAAnB,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAoB,SAAA,CAAA1B,IAAA;cAAA;YAAA;cAAA0B,SAAA,CAAA3B,IAAA;cAAA2B,SAAA,CAAAnB,EAAA,GAAAmB,SAAA;cAEAJ,GAAA,CAAA7F,MAAA,GAAA6F,GAAA,CAAA7F,MAAA;cACA8F,MAAA,CAAAnB,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAqB,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAc,QAAA;MAAA;IAEA;IAEA;IACAM,YAAA,WAAAA,aAAAR,GAAA;MAAA,IAAAS,MAAA;MAAA,OAAAxC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuC,SAAA;QAAA,IAAArC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAqC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;YAAA;cAAAkC,SAAA,CAAAnC,IAAA;cAAAmC,SAAA,CAAAlC,IAAA;cAAA,OAEA3F,aAAA,CAAAiH,GAAA,CAAAK,EAAA;YAAA;cAAAhC,GAAA,GAAAuC,SAAA,CAAAjC,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA6B,MAAA,CAAA7E,UAAA,GAAAyC,GAAA,CAAAvE,IAAA;gBACA2G,MAAA,CAAA9E,aAAA;cACA;gBACA8E,MAAA,CAAA3B,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA4B,SAAA,CAAAlC,IAAA;cAAA;YAAA;cAAAkC,SAAA,CAAAnC,IAAA;cAAAmC,SAAA,CAAA3B,EAAA,GAAA2B,SAAA;cAEAH,MAAA,CAAA3B,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA6B,SAAA,CAAAxB,IAAA;UAAA;QAAA,GAAAsB,QAAA;MAAA;IAEA;IAEA;IACAG,cAAA,WAAAA,eAAAb,GAAA;MACA,KAAA9G,YAAA,GAAA8G,GAAA;MACA,KAAA5E,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,KAAAH,eAAA;IACA;IAEA;IACA2F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA5F,YAAA,CAAA6F,QAAA;QAAA,IAAAC,IAAA,GAAAjD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgD,SAAAC,KAAA;UAAA,IAAA/C,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAA5C,IAAA;cAAA;gBAAA,KACA0C,KAAA;kBAAAE,SAAA,CAAA5C,IAAA;kBAAA;gBAAA;gBAAA4C,SAAA,CAAA7C,IAAA;gBAAA6C,SAAA,CAAA5C,IAAA;gBAAA,OAEAxF,YAAA,CAAA6H,MAAA,CAAA7H,YAAA,CAAAmH,EAAA,EAAAU,MAAA,CAAA3F,YAAA;cAAA;gBAAAiD,GAAA,GAAAiD,SAAA,CAAA3C,IAAA;gBACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACAmC,MAAA,CAAAjC,QAAA,CAAAwB,OAAA;kBACAS,MAAA,CAAA5F,eAAA;kBACA4F,MAAA,CAAArD,OAAA;gBACA;kBACAqD,MAAA,CAAAjC,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAsC,SAAA,CAAA5C,IAAA;gBAAA;cAAA;gBAAA4C,SAAA,CAAA7C,IAAA;gBAAA6C,SAAA,CAAArC,EAAA,GAAAqC,SAAA;gBAEAP,MAAA,CAAAjC,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAuC,SAAA,CAAAlC,IAAA;YAAA;UAAA,GAAA+B,QAAA;QAAA,CAGA;QAAA,iBAAAI,EAAA;UAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA7D,GAAA;MACA,KAAA9D,SAAA,CAAAE,KAAA,GAAA4D,GAAA;MACA,KAAAH,OAAA;IACA;IACAiE,mBAAA,WAAAA,oBAAA9D,GAAA;MACA,KAAA9D,SAAA,CAAAC,IAAA,GAAA6D,GAAA;MACA,KAAAH,OAAA;IACA;IAEA;IACAkE,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,OAAAnI,SAAA,CAAAmI,IAAA;IACA;IAEA;IACAlI,UAAA,WAAAA,WAAAkI,IAAA;MACA,KAAAA,IAAA;MACA,OAAAnI,SAAA,CAAAmI,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAAnE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkE,SAAA;QAAA,IAAAhE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAgE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9D,IAAA,GAAA8D,SAAA,CAAA7D,IAAA;YAAA;cAAA6D,SAAA,CAAA9D,IAAA;cAAA8D,SAAA,CAAA7D,IAAA;cAAA,OAEAzF,iBAAA,CAAA+G,GAAA,CAAAK,EAAA;YAAA;cAAAhC,GAAA,GAAAkE,SAAA,CAAA5D,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAmD,MAAA,CAAAjD,QAAA,CAAAwB,OAAA;cACA;gBACAyB,MAAA,CAAAjD,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAuD,SAAA,CAAA7D,IAAA;cAAA;YAAA;cAAA6D,SAAA,CAAA9D,IAAA;cAAA8D,SAAA,CAAAtD,EAAA,GAAAsD,SAAA;cAEAR,MAAA,CAAAjD,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAwD,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAAiD,QAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IAEA;IACAG,eAAA,WAAAA,gBAAAxC,GAAA;MAAA,IAAAyC,MAAA;MAAA,OAAAxE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuE,SAAA;QAAA,IAAArE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAqE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnE,IAAA,GAAAmE,SAAA,CAAAlE,IAAA;YAAA;cAEA+D,MAAA,CAAA/F,gBAAA;cACA+F,MAAA,CAAA9F,gBAAA;cAAAiG,SAAA,CAAAnE,IAAA;cAAAmE,SAAA,CAAAlE,IAAA;cAAA,OAEAvF,gBAAA,CAAA6G,GAAA,CAAAK,EAAA;YAAA;cAAAhC,GAAA,GAAAuE,SAAA,CAAAjE,IAAA;cAEA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA6D,MAAA,CAAA7F,SAAA,GAAAyB,GAAA,CAAAvE,IAAA;cACA;gBACA2I,MAAA,CAAA3D,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA4D,SAAA,CAAAlE,IAAA;cAAA;YAAA;cAAAkE,SAAA,CAAAnE,IAAA;cAAAmE,SAAA,CAAA3D,EAAA,GAAA2D,SAAA;cAEA1D,OAAA,CAAAH,KAAA,aAAA6D,SAAA,CAAA3D,EAAA;cACAwD,MAAA,CAAA3D,QAAA,CAAAC,KAAA;YAAA;cAAA6D,SAAA,CAAAnE,IAAA;cAEAgE,MAAA,CAAA9F,gBAAA;cAAA,OAAAiG,SAAA,CAAAzD,MAAA;YAAA;YAAA;cAAA,OAAAyD,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA;IAEA;IAEA;IACAtJ,cAAA,WAAAA,eAAA;MAAA,IAAAyJ,MAAA;MAAA,OAAA5E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2E,SAAA;QAAA,IAAAzE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAyE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAtE,IAAA;cAAA,OAEAtF,eAAA;YAAA;cAAAiF,GAAA,GAAA2E,SAAA,CAAArE,IAAA;cAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAiE,MAAA,CAAA5F,WAAA,GAAAoB,GAAA,CAAAvE,IAAA;cACA;cAAAkJ,SAAA,CAAAtE,IAAA;cAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAA/D,EAAA,GAAA+D,SAAA;cAEA9D,OAAA,CAAAH,KAAA,gBAAAiE,SAAA,CAAA/D,EAAA;YAAA;YAAA;cAAA,OAAA+D,SAAA,CAAA5D,IAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA;IAEA;IAEA;IACAG,iBAAA,WAAAA,kBAAAjD,GAAA;MACA,KAAAlD,WAAA,GAAAkD,GAAA;MACA,KAAAjD,SAAA,CAAA3C,SAAA,GAAA4F,GAAA,CAAA5F,SAAA;MACA,KAAAyC,kBAAA;IACA;IAEA;IACAqG,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAnC,KAAA,CAAAjE,SAAA,CAAAkE,QAAA;QAAA,IAAAmC,KAAA,GAAAnF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkF,SAAAjC,KAAA;UAAA,IAAA/C,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAgF,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA9E,IAAA,GAAA8E,SAAA,CAAA7E,IAAA;cAAA;gBAAA,KACA0C,KAAA;kBAAAmC,SAAA,CAAA7E,IAAA;kBAAA;gBAAA;gBAAA6E,SAAA,CAAA9E,IAAA;gBAAA8E,SAAA,CAAA7E,IAAA;gBAAA,OAEArF,eAAA,CACA8J,MAAA,CAAArG,WAAA,CAAAuD,EAAA,EACA8C,MAAA,CAAApG,SAAA,CAAA3C,SACA;cAAA;gBAHAiE,GAAA,GAAAkF,SAAA,CAAA5E,IAAA;gBAIA,IAAAN,GAAA,CAAAO,IAAA;kBACAuE,MAAA,CAAArE,QAAA,CAAAwB,OAAA;kBACA6C,MAAA,CAAAtG,kBAAA;kBACAsG,MAAA,CAAAzF,OAAA;gBACA;kBACAyF,MAAA,CAAArE,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAuE,SAAA,CAAA7E,IAAA;gBAAA;cAAA;gBAAA6E,SAAA,CAAA9E,IAAA;gBAAA8E,SAAA,CAAAtE,EAAA,GAAAsE,SAAA;gBAEArE,OAAA,CAAAH,KAAA,YAAAwE,SAAA,CAAAtE,EAAA;gBACAkE,MAAA,CAAArE,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAwE,SAAA,CAAAnE,IAAA;YAAA;UAAA,GAAAiE,QAAA;QAAA,CAGA;QAAA,iBAAAG,GAAA;UAAA,OAAAJ,KAAA,CAAA5B,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAgC,mBAAA,WAAAA,oBAAAzD,GAAA;MACA,KAAAlD,WAAA,GAAAkD,GAAA;MACA,KAAA7C,iBAAA,CAAAC,UAAA,GAAA4C,GAAA,CAAA0D,gBAAA;MACA,KAAAxG,oBAAA;IACA;IAEA;IACAyG,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAA5C,KAAA,CAAA7D,iBAAA,CAAA8D,QAAA;QAAA,IAAA4C,KAAA,GAAA5F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2F,SAAA1C,KAAA;UAAA,IAAA/C,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAyF,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAvF,IAAA,GAAAuF,SAAA,CAAAtF,IAAA;cAAA;gBAAA,KACA0C,KAAA;kBAAA4C,SAAA,CAAAtF,IAAA;kBAAA;gBAAA;gBAAAsF,SAAA,CAAAvF,IAAA;gBAEAS,OAAA,CAAA+E,GAAA;kBACAC,MAAA,EAAAN,MAAA,CAAA9G,WAAA,CAAAuD,EAAA;kBACAjD,UAAA,EAAAwG,MAAA,CAAAzG,iBAAA,CAAAC;gBACA;gBAAA4G,SAAA,CAAAtF,IAAA;gBAAA,OAEApF,iBAAA,CACAsK,MAAA,CAAA9G,WAAA,CAAAuD,EAAA,EACAuD,MAAA,CAAAzG,iBAAA,CAAAC,UACA;cAAA;gBAHAiB,GAAA,GAAA2F,SAAA,CAAArF,IAAA;gBAKAO,OAAA,CAAA+E,GAAA,YAAA5F,GAAA;gBAEA,IAAAA,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACAgF,MAAA,CAAA9E,QAAA,CAAAwB,OAAA;kBACAsD,MAAA,CAAA1G,oBAAA;kBACA0G,MAAA,CAAAlG,OAAA;gBACA;kBACAkG,MAAA,CAAA9E,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAgF,SAAA,CAAAtF,IAAA;gBAAA;cAAA;gBAAAsF,SAAA,CAAAvF,IAAA;gBAAAuF,SAAA,CAAA/E,EAAA,GAAA+E,SAAA;gBAEA9E,OAAA,CAAAH,KAAA,YAAAiF,SAAA,CAAA/E,EAAA;gBACA2E,MAAA,CAAA9E,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAiF,SAAA,CAAA5E,IAAA;YAAA;UAAA,GAAA0E,QAAA;QAAA,CAGA;QAAA,iBAAAK,GAAA;UAAA,OAAAN,KAAA,CAAArC,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACA2C,sBAAA,WAAAA,uBAAApE,GAAA;MAAA,IAAAqE,OAAA;MAAA,OAAApG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmG,UAAA;QAAA,IAAAjG,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiG,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/F,IAAA,GAAA+F,UAAA,CAAA9F,IAAA;YAAA;cAAA8F,UAAA,CAAA/F,IAAA;cAAA+F,UAAA,CAAA9F,IAAA;cAAA,OAEAnF,oBAAA,CAAAyG,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAApF,YAAA;YAAA;cAAAyD,GAAA,GAAAmG,UAAA,CAAA7F,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAyF,OAAA,CAAAvF,QAAA,CAAAwB,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAApF,YAAA;cACA;gBACAoF,GAAA,CAAApF,YAAA,GAAAoF,GAAA,CAAApF,YAAA;gBACAyJ,OAAA,CAAAvF,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAwF,UAAA,CAAA9F,IAAA;cAAA;YAAA;cAAA8F,UAAA,CAAA/F,IAAA;cAAA+F,UAAA,CAAAvF,EAAA,GAAAuF,UAAA;cAEAxE,GAAA,CAAApF,YAAA,GAAAoF,GAAA,CAAApF,YAAA;cACAyJ,OAAA,CAAAvF,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAyF,UAAA,CAAApF,IAAA;UAAA;QAAA,GAAAkF,SAAA;MAAA;IAEA;IAEA;IACAG,YAAA,WAAAA,aAAAzE,GAAA;MAAA,IAAA0E,OAAA;MACA,KAAA1C,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAAnE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwG,UAAA;QAAA,IAAAtG,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAsG,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApG,IAAA,GAAAoG,UAAA,CAAAnG,IAAA;YAAA;cAAAmG,UAAA,CAAApG,IAAA;cAAAoG,UAAA,CAAAnG,IAAA;cAAA,OAEAlF,UAAA,CAAAwG,GAAA,CAAAK,EAAA;YAAA;cAAAhC,GAAA,GAAAwG,UAAA,CAAAlG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA8F,OAAA,CAAA5F,QAAA,CAAAwB,OAAA;gBACAoE,OAAA,CAAAhH,OAAA;cACA;gBACAgH,OAAA,CAAA5F,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA6F,UAAA,CAAAnG,IAAA;cAAA;YAAA;cAAAmG,UAAA,CAAApG,IAAA;cAAAoG,UAAA,CAAA5F,EAAA,GAAA4F,UAAA;cAEAH,OAAA,CAAA5F,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA8F,UAAA,CAAAzF,IAAA;UAAA;QAAA,GAAAuF,SAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IAEAG,gBAAA,WAAAA,iBAAA9E,GAAA;MAAA,IAAA+E,OAAA;MAAA,OAAA9G,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6G,UAAA;QAAA,IAAA3G,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA2G,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAzG,IAAA,GAAAyG,UAAA,CAAAxG,IAAA;YAAA;cACAqG,OAAA,CAAAzH,mBAAA;cACAyH,OAAA,CAAAvH,aAAA;cAAA0H,UAAA,CAAAzG,IAAA;cAAAyG,UAAA,CAAAxG,IAAA;cAAA,OAEAjF,cAAA;gBAAAyK,MAAA,EAAAlE,GAAA,CAAAK,EAAA;gBAAA8E,OAAA;gBAAAC,QAAA;cAAA;YAAA;cAAA/G,GAAA,GAAA6G,UAAA,CAAAvG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAvE,IAAA,IAAAuE,GAAA,CAAAvE,IAAA,CAAA+E,OAAA;gBACAkG,OAAA,CAAAxH,UAAA,GAAAc,GAAA,CAAAvE,IAAA,CAAA+E,OAAA;cACA;gBACAkG,OAAA,CAAAxH,UAAA;cACA;cAAA2H,UAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,UAAA,CAAAzG,IAAA;cAAAyG,UAAA,CAAAjG,EAAA,GAAAiG,UAAA;cAEAH,OAAA,CAAAxH,UAAA;cACAwH,OAAA,CAAAjG,QAAA,CAAAC,KAAA;YAAA;cAEAgG,OAAA,CAAAvH,aAAA;YAAA;YAAA;cAAA,OAAA0H,UAAA,CAAA9F,IAAA;UAAA;QAAA,GAAA4F,SAAA;MAAA;IACA;IAEAK,aAAA,WAAAA,cAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAA,IAAA,CAAAxH,MAAA,eAAAwH,IAAA;MACA,OAAAA,IAAA,CAAAC,KAAA,iBAAAD,IAAA,CAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}