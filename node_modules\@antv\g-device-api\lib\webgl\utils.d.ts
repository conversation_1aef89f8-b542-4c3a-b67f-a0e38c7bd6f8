import { Format, BufferFrequencyHint, BufferUsage, MipmapFilterMode, PrimitiveTopology, QueryPoolType, FilterMode, TextureDimension, AddressMode } from '../api';
import type { Buffer, ChannelBlendState, Sampler, Texture } from '../api';
export declare function isWebGL2(gl: WebGL2RenderingContext | WebGLRenderingContext): gl is WebGL2RenderingContext;
export declare function isTextureFormatCompressed(fmt: Format): boolean;
export declare function isFormatSizedInteger(fmt: Format): boolean;
export declare function translateBufferHint(hint: BufferFrequencyHint): GLenum;
export declare function translateBufferUsageToTarget(usage: BufferUsage): GLenum;
export declare function translatePrimitiveTopology(topology: PrimitiveTopology): GLenum;
export declare function translateVertexFormat(fmt: Format): {
    size: number;
    type: GLenum;
    normalized: boolean;
};
export declare function translateIndexFormat(format: Format): GLenum;
export declare function translateAddressMode(wrapMode: AddressMode): GLenum;
export declare function translateFilterMode(filter: FilterMode, mipmapFilter: MipmapFilterMode): GLenum;
export declare function getPlatformBuffer(buffer_: Buffer, byteOffset?: number): WebGLBuffer;
export declare function getPlatformTexture(texture_: Texture): WebGLTexture;
export declare function getPlatformSampler(sampler_: Sampler): WebGLSampler;
export declare function assignPlatformName(o: any, name: string): void;
export declare function findall(haystack: string, needle: RegExp): RegExpExecArray[];
export declare function isBlendStateNone(blendState: ChannelBlendState): boolean;
export declare function translateQueryPoolType(type: QueryPoolType): GLenum;
export declare function translateTextureDimension(dimension: TextureDimension): GLenum;
export declare function isBlockCompressSized(w: number, h: number, bw: number, bh: number): boolean;
