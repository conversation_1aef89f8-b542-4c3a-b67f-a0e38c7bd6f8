import { Buffer, Readback, Texture } from '../api';
import { ResourceType } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export declare class Readback_GL extends ResourceBase_GL implements Readback {
    type: ResourceType.Readback;
    gl_pbo: WebGLBuffer | null;
    gl_sync: WebGLSync | null;
    constructor({ id, device }: {
        id: number;
        device: Device_GL;
    });
    private clientWaitAsync;
    private getBufferSubDataAsync;
    /**
     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/WebGL_best_practices#use_non-blocking_async_data_readback
     */
    readTexture(t: Texture, x: number, y: number, width: number, height: number, dstBuffer: ArrayBufferView, dstOffset?: number, length?: number): Promise<ArrayBufferView>;
    readTextureSync(t: Texture, x: number, y: number, width: number, height: number, dstBuffer: ArrayBufferView, dstOffset?: number, length?: number): ArrayBufferView;
    readBuffer(b: Buffer, srcByteOffset: number, dstBuffer: ArrayBufferView, dstOffset?: number, length?: number): Promise<ArrayBufferView>;
    destroy(): void;
}
