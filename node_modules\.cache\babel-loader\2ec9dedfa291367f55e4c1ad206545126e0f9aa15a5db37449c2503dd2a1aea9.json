{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport request from '@/utils/request';\n\n// 获取任务列表\nexport function getJobList(params) {\n  return request({\n    url: '/job/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 新增任务\nexport function addJob(data) {\n  return request({\n    url: '/job/add',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改任务\nexport function updateJob(data) {\n  return request({\n    url: '/job/update',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改状态\nexport function toggleJobStatus(id, status) {\n  return request({\n    url: \"/job/status/\".concat(id, \"/\").concat(status),\n    method: 'post'\n  });\n}\n\n// 删除任务\nexport function deleteJob(id) {\n  return request({\n    url: \"/job/delete/\".concat(id),\n    method: 'post'\n  });\n}\n\n// 执行任务\nexport function executeJob(jobId) {\n  return request({\n    url: \"/job/execute/\".concat(jobId),\n    method: 'post'\n  });\n}\n\n// 获取任务日志列表\nexport function getJobLogList(params) {\n  return request({\n    url: '/job/log/list',\n    method: 'get',\n    params: params\n  });\n}", "map": {"version": 3, "names": ["request", "getJobList", "params", "url", "method", "addJob", "data", "updateJob", "toggleJobStatus", "id", "status", "concat", "deleteJob", "<PERSON><PERSON>ob", "jobId", "getJobLogList"], "sources": ["F:/常规项目/adminweb/src/api/task/job.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取任务列表\r\nexport function getJobList(params) {\r\n  return request({\r\n    url: '/job/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 新增任务\r\nexport function addJob(data) {\r\n  return request({\r\n    url: '/job/add',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 修改任务\r\nexport function updateJob(data) {\r\n  return request({\r\n    url: '/job/update',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 修改状态\r\nexport function toggleJobStatus(id, status) {\r\n  return request({\r\n    url: `/job/status/${id}/${status}`,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 删除任务\r\nexport function deleteJob(id) {\r\n  return request({\r\n    url: `/job/delete/${id}`,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 执行任务\r\nexport function executeJob(jobId) {\r\n  return request({\r\n    url: `/job/execute/${jobId}`,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 获取任务日志列表\r\nexport function getJobLogList(params) {\r\n  return request({\r\n    url: '/job/log/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n} "], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,SAASA,CAACD,IAAI,EAAE;EAC9B,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,eAAeA,CAACC,EAAE,EAAEC,MAAM,EAAE;EAC1C,OAAOV,OAAO,CAAC;IACbG,GAAG,iBAAAQ,MAAA,CAAiBF,EAAE,OAAAE,MAAA,CAAID,MAAM,CAAE;IAClCN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,SAASA,CAACH,EAAE,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,iBAAAQ,MAAA,CAAiBF,EAAE,CAAE;IACxBL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOd,OAAO,CAAC;IACbG,GAAG,kBAAAQ,MAAA,CAAkBG,KAAK,CAAE;IAC5BV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASW,aAAaA,CAACb,MAAM,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}