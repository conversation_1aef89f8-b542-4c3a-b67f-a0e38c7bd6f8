import LinkedList from './linked-list';
export default class Queue {
    linkedList: LinkedList;
    constructor();
    /**
     * 队列是否为空
     */
    isEmpty(): boolean;
    /**
     * 读取队列头部的元素， 不删除队列中的元素
     */
    peek(): any;
    /**
     * 在队列的尾部新增一个元素
     * @param value
     */
    enqueue(value: any): void;
    /**
     * 删除队列中的头部元素，如果队列为空，则返回 null
     */
    dequeue(): any;
    toString(callback?: any): string;
}
