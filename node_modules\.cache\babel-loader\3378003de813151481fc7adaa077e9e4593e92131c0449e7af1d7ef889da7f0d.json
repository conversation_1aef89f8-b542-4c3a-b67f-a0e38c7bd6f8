{"ast": null, "code": "import _objectSpread from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport request from '@/utils/request';\nexport default {\n  name: 'OptionsOrder',\n  data: function data() {\n    return {\n      loading: false,\n      total: 0,\n      orderList: [],\n      dateRange: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        username: '',\n        email: '',\n        status: ''\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      var params = _objectSpread({}, this.queryParams);\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.startTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n      request({\n        url: '/api/futuresOptionOrder/list',\n        method: 'get',\n        params: params\n      }).then(function (res) {\n        _this.orderList = res.records || [];\n        _this.total = res.total || 0;\n      })[\"finally\"](function () {\n        _this.loading = false;\n      });\n    },\n    handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery: function resetQuery() {\n      this.dateRange = [];\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        username: '',\n        email: '',\n        status: ''\n      };\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.queryParams.pageSize = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.getList();\n    },\n    formatDateTime: function formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm:ss');\n    },\n    getStatusText: function getStatusText(status) {\n      var map = {\n        0: '持仓中',\n        1: '已结算',\n        2: '结算中'\n      };\n      return map[status] || '未知';\n    },\n    getStatusType: function getStatusType(status) {\n      var map = {\n        0: 'warning',\n        1: 'success',\n        2: 'info'\n      };\n      return map[status] || 'info';\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "loading", "total", "orderList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "username", "email", "status", "created", "getList", "methods", "_this", "params", "_objectSpread", "length", "startTime", "endTime", "url", "method", "then", "res", "records", "handleQuery", "reset<PERSON><PERSON>y", "handleSizeChange", "val", "handleCurrentChange", "formatDateTime", "dateTime", "$moment", "format", "getStatusText", "map", "getStatusType"], "sources": ["src/views/exchange/options/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n          <el-col :span=\"6\">\r\n            <el-input\r\n              v-model.trim=\"queryParams.username\"\r\n              placeholder=\"请输入用户名\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-input\r\n              v-model.trim=\"queryParams.email\"\r\n              placeholder=\"请输入邮箱\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"请选择订单状态\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            >\r\n              <el-option label=\"持仓中\" :value=\"0\" />\r\n              <el-option label=\"已结算\" :value=\"1\" />\r\n              <el-option label=\"结算中\" :value=\"2\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n          <el-col :span=\"6\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"orderList\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"用户名\" align=\"center\" prop=\"username\" min-width=\"100\" />\r\n        <el-table-column label=\"邮箱\" align=\"center\" prop=\"email\" min-width=\"150\" />\r\n        <el-table-column label=\"订单号\" align=\"center\" prop=\"id\" min-width=\"80\" />\r\n        <el-table-column label=\"交易对\" align=\"center\" prop=\"symbol\" min-width=\"100\" />\r\n        <el-table-column label=\"方向\" align=\"center\" prop=\"direction\" min-width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.direction === 'up' ? 'success' : 'danger'\">\r\n              {{ scope.row.direction === 'up' ? '买涨' : '买跌' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"下单金额\" align=\"center\" prop=\"amount\" min-width=\"100\" />\r\n        <el-table-column label=\"周期(秒)\" align=\"center\" prop=\"period\" min-width=\"80\" />\r\n        <el-table-column label=\"下单时间\" align=\"center\" prop=\"orderTime\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.orderTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"开仓价\" align=\"center\" prop=\"open_price\" min-width=\"100\" />\r\n        <el-table-column label=\"结算时间\" align=\"center\" prop=\"settleTime\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.settleTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"结算价\" align=\"center\" prop=\"close_price\" min-width=\"100\" />\r\n        <el-table-column label=\"盈亏\" align=\"center\" prop=\"profit\" min-width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span :class=\"scope.row.profit >= 0 ? 'text-success' : 'text-danger'\">\r\n              {{ scope.row.profit }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" min-width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getStatusType(scope.row.status)\">\r\n              {{ getStatusText(scope.row.status) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"120\" />\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"queryParams.pageNum\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"queryParams.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request'\r\nexport default {\r\n  name: 'OptionsOrder',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      total: 0,\r\n      orderList: [],\r\n      dateRange: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        username: '',\r\n        email: '',\r\n        status: '',\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true\r\n      const params = {\r\n        ...this.queryParams\r\n      }\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.startTime = this.dateRange[0]\r\n        params.endTime = this.dateRange[1]\r\n      }\r\n      request({\r\n        url: '/api/futuresOptionOrder/list',\r\n        method: 'get',\r\n        params\r\n      }).then(res => {\r\n        this.orderList = res.records || []\r\n        this.total = res.total || 0\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        username: '',\r\n        email: '',\r\n        status: '',\r\n      }\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.queryParams.pageSize = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.queryParams.pageNum = val\r\n      this.getList()\r\n    },\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm:ss')\r\n    },\r\n    getStatusText(status) {\r\n      const map = {0: '持仓中', 1: '已结算', 2: '结算中'}\r\n      return map[status] || '未知'\r\n    },\r\n    getStatusType(status) {\r\n      const map = {0: 'warning', 1: 'success', 2: 'info'}\r\n      return map[status] || 'info'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n}\r\n.filter-item {\r\n  width: 100%;\r\n}\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n.text-success {\r\n  color: #67c23a;\r\n}\r\n.text-danger {\r\n  color: #f56c6c;\r\n}\r\n.table-wrapper {\r\n  overflow-x: auto;\r\n}\r\n</style> "], "mappings": ";;AA6HA,OAAAA,OAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAb,OAAA;MACA,IAAAc,MAAA,GAAAC,aAAA,KACA,KAAAX,WAAA,CACA;MACA,SAAAD,SAAA,SAAAA,SAAA,CAAAa,MAAA;QACAF,MAAA,CAAAG,SAAA,QAAAd,SAAA;QACAW,MAAA,CAAAI,OAAA,QAAAf,SAAA;MACA;MACAN,OAAA;QACAsB,GAAA;QACAC,MAAA;QACAN,MAAA,EAAAA;MACA,GAAAO,IAAA,WAAAC,GAAA;QACAT,KAAA,CAAAX,SAAA,GAAAoB,GAAA,CAAAC,OAAA;QACAV,KAAA,CAAAZ,KAAA,GAAAqB,GAAA,CAAArB,KAAA;MACA;QACAY,KAAA,CAAAb,OAAA;MACA;IACA;IACAwB,WAAA,WAAAA,YAAA;MACA,KAAApB,WAAA,CAAAC,OAAA;MACA,KAAAM,OAAA;IACA;IACAc,UAAA,WAAAA,WAAA;MACA,KAAAtB,SAAA;MACA,KAAAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACA,KAAAE,OAAA;IACA;IACAe,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAvB,WAAA,CAAAE,QAAA,GAAAqB,GAAA;MACA,KAAAhB,OAAA;IACA;IACAiB,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAvB,WAAA,CAAAC,OAAA,GAAAsB,GAAA;MACA,KAAAhB,OAAA;IACA;IACAkB,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,YAAAC,OAAA,CAAAD,QAAA,EAAAE,MAAA;IACA;IACAC,aAAA,WAAAA,cAAAxB,MAAA;MACA,IAAAyB,GAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAAzB,MAAA;IACA;IACA0B,aAAA,WAAAA,cAAA1B,MAAA;MACA,IAAAyB,GAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAAzB,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}