{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Gradient from './Gradient.js';\nvar RadialGradient = function (_super) {\n  __extends(RadialGradient, _super);\n  function RadialGradient(x, y, r, colorStops, globalCoord) {\n    var _this = _super.call(this, colorStops) || this;\n    _this.x = x == null ? 0.5 : x;\n    _this.y = y == null ? 0.5 : y;\n    _this.r = r == null ? 0.5 : r;\n    _this.type = 'radial';\n    _this.global = globalCoord || false;\n    return _this;\n  }\n  return RadialGradient;\n}(Gradient);\nexport default RadialGradient;", "map": {"version": 3, "names": ["__extends", "Gradient", "RadialGrad<PERSON>", "_super", "x", "y", "r", "colorStops", "globalCoord", "_this", "call", "type", "global"], "sources": ["E:/新项目/adminweb/node_modules/zrender/lib/graphic/RadialGradient.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Gradient from './Gradient.js';\nvar RadialGradient = (function (_super) {\n    __extends(RadialGradient, _super);\n    function RadialGradient(x, y, r, colorStops, globalCoord) {\n        var _this = _super.call(this, colorStops) || this;\n        _this.x = x == null ? 0.5 : x;\n        _this.y = y == null ? 0.5 : y;\n        _this.r = r == null ? 0.5 : r;\n        _this.type = 'radial';\n        _this.global = globalCoord || false;\n        return _this;\n    }\n    return RadialGradient;\n}(Gradient));\nexport default RadialGradient;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,eAAe;AACpC,IAAIC,cAAc,GAAI,UAAUC,MAAM,EAAE;EACpCH,SAAS,CAACE,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,UAAU,EAAEC,WAAW,EAAE;IACtD,IAAIC,KAAK,GAAGN,MAAM,CAACO,IAAI,CAAC,IAAI,EAAEH,UAAU,CAAC,IAAI,IAAI;IACjDE,KAAK,CAACL,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,GAAG,GAAGA,CAAC;IAC7BK,KAAK,CAACJ,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,GAAG,GAAGA,CAAC;IAC7BI,KAAK,CAACH,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,GAAG,GAAGA,CAAC;IAC7BG,KAAK,CAACE,IAAI,GAAG,QAAQ;IACrBF,KAAK,CAACG,MAAM,GAAGJ,WAAW,IAAI,KAAK;IACnC,OAAOC,KAAK;EAChB;EACA,OAAOP,cAAc;AACzB,CAAC,CAACD,QAAQ,CAAE;AACZ,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}