import { DeviceContribution } from '../api';
import { Device_GL } from './Device';
export interface WebGLRendererPluginOptions {
    targets: ('webgl1' | 'webgl2')[];
    xrCompatible: boolean;
    antialias: boolean;
    preserveDrawingBuffer: boolean;
    premultipliedAlpha: boolean;
    shaderDebug: boolean;
    trackResources: boolean;
    onContextCreationError: (e: Event) => void;
    onContextLost: (e: Event) => void;
    onContextRestored: (e: Event) => void;
}
export declare class WebGLDeviceContribution implements DeviceContribution {
    private pluginOptions;
    constructor(pluginOptions: Partial<WebGLRendererPluginOptions>);
    createSwapChain($canvas: HTMLCanvasElement): Promise<Device_GL>;
    private handleContextEvents;
}
