{"version": 3, "file": "line.js", "sourceRoot": "", "sources": ["../../src/shape/line.ts"], "names": [], "mappings": ";AAAA;;;GAGG;AACH,OAAO,EAAE,IAAI,IAAI,QAAQ,EAAE,MAAM,cAAc,CAAC;AAChD,OAAO,SAAS,MAAM,QAAQ,CAAC;AAC/B,OAAO,MAAM,MAAM,wBAAwB,CAAC;AAC5C,OAAO,KAAK,SAAS,MAAM,eAAe,CAAC;AAE3C;IAAmB,wBAAS;IAA5B;;IAkGA,CAAC;IAjGC,8BAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6BACK,KAAK,KACR,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,EACL,UAAU,EAAE,KAAK,EACjB,QAAQ,EAAE,KAAK,IACf;IACJ,CAAC;IAED,wBAAS,GAAT,UAAU,KAAK;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,kBAAkB;IAClB,2BAAY,GAAZ,UAAa,IAAY,EAAE,KAAU,EAAE,WAAgB;QACrD,iBAAM,YAAY,YAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAC7C,mEAAmE;QACnE,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,uBAAQ,GAAR;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAA,EAAE,GAAuC,KAAK,GAA5C,EAAE,EAAE,GAAmC,KAAK,GAAxC,EAAE,EAAE,GAA+B,KAAK,GAApC,EAAE,EAAE,GAA2B,KAAK,GAAhC,EAAE,UAAU,GAAe,KAAK,WAApB,EAAE,QAAQ,GAAK,KAAK,SAAV,CAAW;QACvD,IAAI,UAAU,EAAE;YACd,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACtD;QACD,IAAI,QAAQ,EAAE;YACZ,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACpD;IACH,CAAC;IAED,+BAAgB,GAAhB,UAAiB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;QAChD,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QACK,IAAA,KAAqB,IAAI,CAAC,IAAI,EAAE,EAA9B,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAgB,CAAC;QACvC,OAAO,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,yBAAU,GAAV,UAAW,OAAO;QAChB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAA,EAAE,GAAuC,KAAK,GAA5C,EAAE,EAAE,GAAmC,KAAK,GAAxC,EAAE,EAAE,GAA+B,KAAK,GAApC,EAAE,EAAE,GAA2B,KAAK,GAAhC,EAAE,UAAU,GAAe,KAAK,WAApB,EAAE,QAAQ,GAAK,KAAK,SAAV,CAAW;QACvD,IAAI,kBAAkB,GAAG;YACvB,EAAE,EAAE,CAAC;YACL,EAAE,EAAE,CAAC;SACN,CAAC;QACF,IAAI,gBAAgB,GAAG;YACrB,EAAE,EAAE,CAAC;YACL,EAAE,EAAE,CAAC;SACN,CAAC;QAEF,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,EAAE;YAC9B,kBAAkB,GAAG,SAAS,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACrF;QACD,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,EAAE;YAC1B,gBAAgB,GAAG,SAAS,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SACjF;QAED,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,iBAAiB;QACjB,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,kBAAkB,CAAC,EAAE,EAAE,EAAE,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAC;QACvE,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC;IAED,4BAAa,GAAb,UAAc,OAAO;QACnB,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAChD,IAAI,eAAe,EAAE;YACnB,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/B;QACD,IAAI,aAAa,EAAE;YACjB,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC;IAED;;;OAGG;IACH,6BAAc,GAAd;QACQ,IAAA,KAAqB,IAAI,CAAC,IAAI,EAAE,EAA9B,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAgB,CAAC;QACvC,OAAO,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,uBAAQ,GAAR,UAAS,KAAa;QACd,IAAA,KAAqB,IAAI,CAAC,IAAI,EAAE,EAA9B,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAgB,CAAC;QACvC,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IACH,WAAC;AAAD,CAAC,AAlGD,CAAmB,SAAS,GAkG3B;AAED,eAAe,IAAI,CAAC"}