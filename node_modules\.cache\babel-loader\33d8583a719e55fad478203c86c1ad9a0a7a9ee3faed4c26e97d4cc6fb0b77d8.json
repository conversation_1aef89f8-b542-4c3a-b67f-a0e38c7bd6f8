{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.is-array.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport * as echarts from 'echarts';\nimport { getLoginLogList } from '@/api/log/login';\nimport { getOperLogList } from '@/api/log/operation';\nimport { getDashboardStats, getProvinceDeviceStats } from '@/api/dashboard';\nexport default {\n  name: 'Dashboard',\n  data: function data() {\n    return {\n      // 登录日志\n      loginLogs: [],\n      // 操作日志\n      operationLogs: [],\n      // 添加新的地图关数据\n      mapChart: null,\n      // 修改查询参数\n      logQuery: {\n        pageNum: 1,\n        pageSize: 3,\n        // 只取3条记录\n        orderByColumn: 'createTime',\n        // 按创建时间排序\n        isAsc: 'desc' // 降序排序，最新的在前\n      },\n      // 添加统计数据\n      stats: {\n        todayUsers: 0,\n        todayUsersRate: 0,\n        todayOrders: 0,\n        todayOrdersRate: 0,\n        todayWithdraw: 0,\n        todayWithdrawRate: 0,\n        todayDevices: 0,\n        todayDevicesRate: 0\n      }\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return _this.initEchartsMap();\n          case 2:\n            _this.getLoginLogs();\n            _this.getOperationLogs();\n            _this.getStats();\n          case 5:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }))();\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.mapChart) {\n      this.mapChart.dispose();\n    }\n  },\n  methods: {\n    initEchartsMap: function initEchartsMap() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var chartDom, res, validData, option;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              chartDom = document.getElementById('deviceMap');\n              _this2.mapChart = echarts.init(chartDom);\n              _context2.next = 5;\n              return getProvinceDeviceStats();\n            case 5:\n              res = _context2.sent;\n              if (!((res.code === 0 || res.code === 200) && Array.isArray(res.data))) {\n                _context2.next = 13;\n                break;\n              }\n              // 过滤掉 name 为空的数据\n              validData = res.data.filter(function (item) {\n                return item.name;\n              }); // 如果没有有效数据，不展示图表\n              if (!(validData.length === 0)) {\n                _context2.next = 11;\n                break;\n              }\n              _this2.mapChart.setOption({\n                title: {\n                  text: '暂无设备分布数据',\n                  left: 'center',\n                  top: 'center',\n                  textStyle: {\n                    color: '#909399',\n                    fontSize: 16\n                  }\n                }\n              });\n              return _context2.abrupt(\"return\");\n            case 11:\n              option = {\n                backgroundColor: '#fff',\n                grid: {\n                  top: '10%',\n                  left: '5%',\n                  right: '5%',\n                  bottom: '15%',\n                  containLabel: true\n                },\n                tooltip: {\n                  trigger: 'axis',\n                  backgroundColor: 'rgba(255, 255, 255, 0.9)',\n                  borderColor: '#eee',\n                  borderWidth: 1,\n                  textStyle: {\n                    color: '#333'\n                  },\n                  formatter: '{b}<br/>设备数量：{c}'\n                },\n                xAxis: {\n                  type: 'category',\n                  data: validData.map(function (item) {\n                    return item.name;\n                  }),\n                  axisLabel: {\n                    interval: 0,\n                    rotate: 45,\n                    color: '#666',\n                    fontSize: 12\n                  },\n                  axisLine: {\n                    lineStyle: {\n                      color: '#ddd'\n                    }\n                  },\n                  axisTick: {\n                    show: false\n                  }\n                },\n                yAxis: {\n                  type: 'value',\n                  name: '设备数量',\n                  nameTextStyle: {\n                    color: '#666',\n                    fontSize: 12,\n                    padding: [0, 0, 0, 40]\n                  },\n                  axisLabel: {\n                    color: '#666',\n                    fontSize: 12\n                  },\n                  axisLine: {\n                    show: false\n                  },\n                  axisTick: {\n                    show: false\n                  },\n                  splitLine: {\n                    lineStyle: {\n                      color: '#eee',\n                      type: 'dashed'\n                    }\n                  }\n                },\n                series: [{\n                  type: 'bar',\n                  data: validData.map(function (item) {\n                    return {\n                      value: item.value,\n                      itemStyle: {\n                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n                          offset: 0,\n                          color: '#1890ff'\n                        }, {\n                          offset: 1,\n                          color: '#bae7ff'\n                        }])\n                      }\n                    };\n                  }),\n                  barWidth: '35%',\n                  label: {\n                    show: true,\n                    position: 'top',\n                    color: '#666',\n                    fontSize: 12,\n                    formatter: '{c}',\n                    distance: 10\n                  },\n                  itemStyle: {\n                    borderRadius: [4, 4, 0, 0]\n                  },\n                  emphasis: {\n                    itemStyle: {\n                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n                        offset: 0,\n                        color: '#40a9ff'\n                      }, {\n                        offset: 1,\n                        color: '#91d5ff'\n                      }])\n                    }\n                  }\n                }]\n              };\n              _this2.mapChart.setOption(option);\n            case 13:\n              _context2.next = 18;\n              break;\n            case 15:\n              _context2.prev = 15;\n              _context2.t0 = _context2[\"catch\"](0);\n              _this2.$message.error('初始化图表失败');\n            case 18:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 15]]);\n      }))();\n    },\n    // 获取登录日志\n    getLoginLogs: function getLoginLogs() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res, latestLogs;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return getLoginLogList(_this3.logQuery);\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0 || res.code === 200) {\n                // 确保只取最新的3条记录\n                latestLogs = Array.isArray(res.data) ? res.data.slice(0, 3) : [];\n                _this3.loginLogs = latestLogs.map(function (item) {\n                  return {\n                    loginTime: _this3.formatDateTime(item.loginTime),\n                    ip: item.ipaddr,\n                    location: \"\".concat(item.loginLocation),\n                    status: item.status\n                  };\n                });\n              } else {\n                _this3.$message.error(res.msg || '获取登录日志失败');\n              }\n              _context3.next = 10;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              _this3.$message.error('获取登录日志失败');\n            case 10:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    // 获取操作日志\n    getOperationLogs: function getOperationLogs() {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var res, latestLogs;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.prev = 0;\n              _context4.next = 3;\n              return getOperLogList(_this4.logQuery);\n            case 3:\n              res = _context4.sent;\n              if (res.code === 0 || res.code === 200) {\n                // 确保只取最新的3条记录\n                latestLogs = Array.isArray(res.data) ? res.data.slice(0, 3) : [];\n                _this4.operationLogs = latestLogs.map(function (item) {\n                  return {\n                    operateTime: _this4.formatDateTime(item.operTime),\n                    module: item.title,\n                    operation: item.operType,\n                    status: item.status\n                  };\n                });\n              } else {\n                _this4.$message.error(res.msg || '获取操作日志失败');\n              }\n              _context4.next = 10;\n              break;\n            case 7:\n              _context4.prev = 7;\n              _context4.t0 = _context4[\"catch\"](0);\n              _this4.$message.error('获取操作日志失败');\n            case 10:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[0, 7]]);\n      }))();\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    // 获取统计数据\n    getStats: function getStats() {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return getDashboardStats();\n            case 3:\n              res = _context5.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this5.stats = {\n                  todayUsers: res.data.todayUsers || 0,\n                  todayUsersRate: res.data.todayUsersRate || 0,\n                  todayOrders: res.data.todayOrders || 0,\n                  todayOrdersRate: res.data.todayOrdersRate || 0,\n                  todayWithdraw: res.data.todayWithdraw || 0,\n                  todayWithdrawRate: res.data.todayWithdrawRate || 0,\n                  todayDevices: res.data.todayDevices || 0,\n                  todayDevicesRate: res.data.todayDevicesRate || 0\n                };\n              }\n              _context5.next = 10;\n              break;\n            case 7:\n              _context5.prev = 7;\n              _context5.t0 = _context5[\"catch\"](0);\n              _this5.$message.error('获取统计数据失败');\n            case 10:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 7]]);\n      }))();\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getLoginLogList", "getOperLogList", "getDashboardStats", "getProvinceDeviceStats", "name", "data", "loginLogs", "operationLogs", "mapChart", "log<PERSON><PERSON>y", "pageNum", "pageSize", "orderByColumn", "isAsc", "stats", "todayUsers", "todayUsersRate", "todayOrders", "todayOrdersRate", "todayWithdraw", "todayWithdrawRate", "todayDevices", "todayDevicesRate", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "initEchartsMap", "getLoginLogs", "getOperationLogs", "getStats", "stop", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "_this2", "_callee2", "chartDom", "res", "validData", "option", "_callee2$", "_context2", "document", "getElementById", "init", "sent", "code", "Array", "isArray", "filter", "item", "length", "setOption", "title", "text", "left", "top", "textStyle", "color", "fontSize", "abrupt", "backgroundColor", "grid", "right", "bottom", "containLabel", "tooltip", "trigger", "borderColor", "borderWidth", "formatter", "xAxis", "type", "map", "axisLabel", "interval", "rotate", "axisLine", "lineStyle", "axisTick", "show", "yAxis", "nameTextStyle", "padding", "splitLine", "series", "value", "itemStyle", "graphic", "LinearGradient", "offset", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "distance", "borderRadius", "emphasis", "t0", "$message", "error", "_this3", "_callee3", "latestLogs", "_callee3$", "_context3", "slice", "loginTime", "formatDateTime", "ip", "ipaddr", "location", "concat", "loginLocation", "status", "msg", "_this4", "_callee4", "_callee4$", "_context4", "operateTime", "operTime", "module", "operation", "operType", "time", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "_this5", "_callee5", "_callee5$", "_context5"], "sources": ["src/views/dashboard/home/<USER>"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日新增用户</div>\r\n            <div class=\"value\">{{ stats.todayUsers }}人</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayUsersRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayUsersRate >= 0 ? '+' : '' }}{{ stats.todayUsersRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-user\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日订单金额</div>\r\n            <div class=\"value\">{{ stats.todayOrders }}</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayOrdersRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayOrdersRate >= 0 ? '+' : '' }}{{ stats.todayOrdersRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-s-order\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日提现额</div>\r\n            <div class=\"value\">￥{{ stats.todayWithdraw }}</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayWithdrawRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayWithdrawRate >= 0 ? '+' : '' }}{{ stats.todayWithdrawRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-money\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日新增广告设备</div>\r\n            <div class=\"value\">{{ stats.todayDevices }}台</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span :class=\"stats.todayDevicesRate >= 0 ? 'up' : 'down'\">\r\n                {{ stats.todayDevicesRate >= 0 ? '+' : '' }}{{ stats.todayDevicesRate }}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-monitor\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 设备分布部分 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n      <el-col :span=\"24\">\r\n        <el-card class=\"device-distribution-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>广告设备分布</span>\r\n          </div>\r\n          <div id=\"deviceMap\" style=\"height: 500px; width: 100%; padding: 20px; background-color: #fff; border-radius: 4px;\"></div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n      <el-col :span=\"12\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>登录日志</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$router.push('/dashboard/log/login')\">\r\n              查看更多\r\n            </el-button>\r\n          </div>\r\n          <el-table :data=\"loginLogs\" stripe style=\"width: 100%\">\r\n            <el-table-column prop=\"loginTime\" label=\"登录时间\" width=\"180\" />\r\n            <el-table-column prop=\"ip\" label=\"登录IP\" width=\"140\" />\r\n            <el-table-column prop=\"location\" label=\"登录地点\" min-width=\"140\" />\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"scope.row.status === 1 ? 'success' : 'danger'\">\r\n                  {{ scope.row.status === 1 ? '成功' : '失败' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>操作日志</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$router.push('/dashboard/log/operation')\">\r\n              查看更多\r\n            </el-button>\r\n          </div>\r\n          <el-table :data=\"operationLogs\" stripe style=\"width: 100%\">\r\n            <el-table-column prop=\"operateTime\" label=\"操作时间\" width=\"180\" />\r\n            <el-table-column prop=\"module\" label=\"操作模块\" width=\"140\" />\r\n            <el-table-column prop=\"operation\" label=\"操作内容\" min-width=\"140\" />\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"scope.row.status === 1 ? 'success' : 'danger'\">\r\n                  {{ scope.row.status === 1 ? '成功' : '失败' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { getLoginLogList } from '@/api/log/login'\r\nimport { getOperLogList } from '@/api/log/operation'\r\nimport { getDashboardStats, getProvinceDeviceStats } from '@/api/dashboard'\r\n \r\nexport default {\r\n  name: 'Dashboard',\r\n  data() {\r\n    return {\r\n      // 登录日志\r\n      loginLogs: [],\r\n      // 操作日志\r\n      operationLogs: [],\r\n      // 添加新的地图关数据\r\n      mapChart: null,\r\n      \r\n      // 修改查询参数\r\n      logQuery: {\r\n        pageNum: 1,\r\n        pageSize: 3,  // 只取3条记录\r\n        orderByColumn: 'createTime',  // 按创建时间排序\r\n        isAsc: 'desc'  // 降序排序，最新的在前\r\n      },\r\n      // 添加统计数据\r\n      stats: {\r\n        todayUsers: 0,\r\n        todayUsersRate: 0,\r\n        todayOrders: 0,\r\n        todayOrdersRate: 0,\r\n        todayWithdraw: 0,\r\n        todayWithdrawRate: 0,\r\n        todayDevices: 0,\r\n        todayDevicesRate: 0\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.initEchartsMap()\r\n    this.getLoginLogs()\r\n    this.getOperationLogs()\r\n    this.getStats()\r\n  },\r\n  beforeDestroy() {\r\n    if (this.mapChart) {\r\n      this.mapChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    async initEchartsMap() {\r\n      try {\r\n        const chartDom = document.getElementById('deviceMap')\r\n        this.mapChart = echarts.init(chartDom)\r\n        \r\n        const res = await getProvinceDeviceStats()\r\n\r\n        if ((res.code === 0 || res.code === 200) && Array.isArray(res.data)) {\r\n          // 过滤掉 name 为空的数据\r\n          const validData = res.data.filter(item => item.name)\r\n          \r\n          // 如果没有有效数据，不展示图表\r\n          if (validData.length === 0) {\r\n            this.mapChart.setOption({\r\n              title: {\r\n                text: '暂无设备分布数据',\r\n                left: 'center',\r\n                top: 'center',\r\n                textStyle: {\r\n                  color: '#909399',\r\n                  fontSize: 16\r\n                }\r\n              }\r\n            })\r\n            return\r\n          }\r\n\r\n          const option = {\r\n            backgroundColor: '#fff',\r\n            grid: {\r\n              top: '10%',\r\n              left: '5%',\r\n              right: '5%',\r\n              bottom: '15%',\r\n              containLabel: true\r\n            },\r\n            tooltip: {\r\n              trigger: 'axis',\r\n              backgroundColor: 'rgba(255, 255, 255, 0.9)',\r\n              borderColor: '#eee',\r\n              borderWidth: 1,\r\n              textStyle: {\r\n                color: '#333'\r\n              },\r\n              formatter: '{b}<br/>设备数量：{c}'\r\n            },\r\n            xAxis: {\r\n              type: 'category',\r\n              data: validData.map(item => item.name),\r\n              axisLabel: {\r\n                interval: 0,\r\n                rotate: 45,\r\n                color: '#666',\r\n                fontSize: 12\r\n              },\r\n              axisLine: {\r\n                lineStyle: {\r\n                  color: '#ddd'\r\n                }\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              }\r\n            },\r\n            yAxis: {\r\n              type: 'value',\r\n              name: '设备数量',\r\n              nameTextStyle: {\r\n                color: '#666',\r\n                fontSize: 12,\r\n                padding: [0, 0, 0, 40]\r\n              },\r\n              axisLabel: {\r\n                color: '#666',\r\n                fontSize: 12\r\n              },\r\n              axisLine: {\r\n                show: false\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              },\r\n              splitLine: {\r\n                lineStyle: {\r\n                  color: '#eee',\r\n                  type: 'dashed'\r\n                }\r\n              }\r\n            },\r\n            series: [\r\n              {\r\n                type: 'bar',\r\n                data: validData.map(item => ({\r\n                  value: item.value,\r\n                  itemStyle: {\r\n                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                      { offset: 0, color: '#1890ff' },\r\n                      { offset: 1, color: '#bae7ff' }\r\n                    ])\r\n                  }\r\n                })),\r\n                barWidth: '35%',\r\n                label: {\r\n                  show: true,\r\n                  position: 'top',\r\n                  color: '#666',\r\n                  fontSize: 12,\r\n                  formatter: '{c}',\r\n                  distance: 10\r\n                },\r\n                itemStyle: {\r\n                  borderRadius: [4, 4, 0, 0]\r\n                },\r\n                emphasis: {\r\n                  itemStyle: {\r\n                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                      { offset: 0, color: '#40a9ff' },\r\n                      { offset: 1, color: '#91d5ff' }\r\n                    ])\r\n                  }\r\n                }\r\n              }\r\n            ]\r\n          }\r\n\r\n          this.mapChart.setOption(option)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('初始化图表失败')\r\n      }\r\n    },\r\n\r\n    \r\n\r\n    // 获取登录日志\r\n    async getLoginLogs() {\r\n      try {\r\n        const res = await getLoginLogList(this.logQuery)\r\n        if (res.code === 0 || res.code === 200) {\r\n          // 确保只取最新的3条记录\r\n          const latestLogs = Array.isArray(res.data) ? res.data.slice(0, 3) : []\r\n          this.loginLogs = latestLogs.map(item => ({ \r\n            loginTime: this.formatDateTime(item.loginTime),\r\n            ip: item.ipaddr,\r\n            location: `${item.loginLocation}`,\r\n            status: item.status\r\n          }))\r\n        } else {\r\n          this.$message.error(res.msg || '获取登录日志失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取登录日志失败')\r\n      }\r\n    },\r\n\r\n    // 获取操作日志\r\n    async getOperationLogs() {\r\n      try {\r\n        const res = await getOperLogList(this.logQuery)\r\n        if (res.code === 0 || res.code === 200) {\r\n          // 确保只取最新的3条记录\r\n          const latestLogs = Array.isArray(res.data) ? res.data.slice(0, 3) : []\r\n          this.operationLogs = latestLogs.map(item => ({\r\n            operateTime: this.formatDateTime(item.operTime),\r\n            module: item.title,\r\n            operation: item.operType,\r\n            status: item.status\r\n          }))\r\n        } else {\r\n          this.$message.error(res.msg || '获取操作日志失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取操作日志失败')\r\n      }\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      const date = new Date(time)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n\r\n    // 获取统计数据\r\n    async getStats() {\r\n      try {\r\n        const res = await getDashboardStats()\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.stats = {\r\n            todayUsers: res.data.todayUsers || 0,\r\n            todayUsersRate: res.data.todayUsersRate || 0,\r\n            todayOrders: res.data.todayOrders || 0,\r\n            todayOrdersRate: res.data.todayOrdersRate || 0,\r\n            todayWithdraw: res.data.todayWithdraw || 0,\r\n            todayWithdrawRate: res.data.todayWithdrawRate || 0,\r\n            todayDevices: res.data.todayDevices || 0,\r\n            todayDevicesRate: res.data.todayDevicesRate || 0\r\n          }\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取统计数据失败')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  height: calc(100vh - 84px);  // 修复滚动条问题\r\n  overflow-y: auto;  // 添加垂直滚动\r\n\r\n  .dashboard-card {\r\n    position: relative;\r\n    height: 120px;\r\n    overflow: hidden;\r\n\r\n    .card-header {\r\n      position: relative;\r\n      z-index: 1;\r\n\r\n      .title {\r\n        font-size: 14px;\r\n        color: #909399;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 24px;\r\n        font-weight: bold;\r\n        color: #303133;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .trend {\r\n        font-size: 13px;\r\n        color: #909399;\r\n\r\n        .up {\r\n          color: #67C23A;\r\n        }\r\n\r\n        .down {\r\n          color: #F56C6C;\r\n        }\r\n      }\r\n    }\r\n\r\n    .icon {\r\n      position: absolute;\r\n      right: 20px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      font-size: 48px;\r\n      opacity: 0.1;\r\n      transition: all 0.3s;\r\n\r\n      i {\r\n        color: #1890ff;\r\n      }\r\n    }\r\n\r\n    &:hover {\r\n      .icon {\r\n        opacity: 0.2;\r\n        transform: translateY(-50%) scale(1.1);\r\n      }\r\n    }\r\n  }\r\n\r\n  #userMap,\r\n  #deviceMap {\r\n    width: 100%;\r\n    height: 500px;\r\n  }\r\n}\r\n\r\n.el-breadcrumb {\r\n  display: inline-block;\r\n  margin-left: 15px;\r\n  \r\n  :deep(.el-breadcrumb__item) {\r\n    cursor: pointer;\r\n    \r\n    &:hover {\r\n      color: #409EFF;\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;AAuIA,YAAAA,OAAA;AACA,SAAAC,eAAA;AACA,SAAAC,cAAA;AACA,SAAAC,iBAAA,EAAAC,sBAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MACA;MACAC,aAAA;MACA;MACAC,QAAA;MAEA;MACAC,QAAA;QACAC,OAAA;QACAC,QAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;MACA;MACA;MACAC,KAAA;QACAC,UAAA;QACAC,cAAA;QACAC,WAAA;QACAC,eAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,YAAA;QACAC,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,cAAA;UAAA;YACAV,KAAA,CAAAW,YAAA;YACAX,KAAA,CAAAY,gBAAA;YACAZ,KAAA,CAAAa,QAAA;UAAA;UAAA;YAAA,OAAAN,QAAA,CAAAO,IAAA;QAAA;MAAA,GAAAV,OAAA;IAAA;EACA;EACAW,aAAA,WAAAA,cAAA;IACA,SAAA/B,QAAA;MACA,KAAAA,QAAA,CAAAgC,OAAA;IACA;EACA;EACAC,OAAA;IACAP,cAAA,WAAAA,eAAA;MAAA,IAAAQ,MAAA;MAAA,OAAAjB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgB,SAAA;QAAA,IAAAC,QAAA,EAAAC,GAAA,EAAAC,SAAA,EAAAC,MAAA;QAAA,OAAArB,mBAAA,GAAAG,IAAA,UAAAmB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjB,IAAA,GAAAiB,SAAA,CAAAhB,IAAA;YAAA;cAAAgB,SAAA,CAAAjB,IAAA;cAEAY,QAAA,GAAAM,QAAA,CAAAC,cAAA;cACAT,MAAA,CAAAlC,QAAA,GAAAT,OAAA,CAAAqD,IAAA,CAAAR,QAAA;cAAAK,SAAA,CAAAhB,IAAA;cAAA,OAEA9B,sBAAA;YAAA;cAAA0C,GAAA,GAAAI,SAAA,CAAAI,IAAA;cAAA,MAEA,CAAAR,GAAA,CAAAS,IAAA,UAAAT,GAAA,CAAAS,IAAA,aAAAC,KAAA,CAAAC,OAAA,CAAAX,GAAA,CAAAxC,IAAA;gBAAA4C,SAAA,CAAAhB,IAAA;gBAAA;cAAA;cACA;cACAa,SAAA,GAAAD,GAAA,CAAAxC,IAAA,CAAAoD,MAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAtD,IAAA;cAAA,IAEA;cAAA,MACA0C,SAAA,CAAAa,MAAA;gBAAAV,SAAA,CAAAhB,IAAA;gBAAA;cAAA;cACAS,MAAA,CAAAlC,QAAA,CAAAoD,SAAA;gBACAC,KAAA;kBACAC,IAAA;kBACAC,IAAA;kBACAC,GAAA;kBACAC,SAAA;oBACAC,KAAA;oBACAC,QAAA;kBACA;gBACA;cACA;cAAA,OAAAlB,SAAA,CAAAmB,MAAA;YAAA;cAIArB,MAAA;gBACAsB,eAAA;gBACAC,IAAA;kBACAN,GAAA;kBACAD,IAAA;kBACAQ,KAAA;kBACAC,MAAA;kBACAC,YAAA;gBACA;gBACAC,OAAA;kBACAC,OAAA;kBACAN,eAAA;kBACAO,WAAA;kBACAC,WAAA;kBACAZ,SAAA;oBACAC,KAAA;kBACA;kBACAY,SAAA;gBACA;gBACAC,KAAA;kBACAC,IAAA;kBACA3E,IAAA,EAAAyC,SAAA,CAAAmC,GAAA,WAAAvB,IAAA;oBAAA,OAAAA,IAAA,CAAAtD,IAAA;kBAAA;kBACA8E,SAAA;oBACAC,QAAA;oBACAC,MAAA;oBACAlB,KAAA;oBACAC,QAAA;kBACA;kBACAkB,QAAA;oBACAC,SAAA;sBACApB,KAAA;oBACA;kBACA;kBACAqB,QAAA;oBACAC,IAAA;kBACA;gBACA;gBACAC,KAAA;kBACAT,IAAA;kBACA5E,IAAA;kBACAsF,aAAA;oBACAxB,KAAA;oBACAC,QAAA;oBACAwB,OAAA;kBACA;kBACAT,SAAA;oBACAhB,KAAA;oBACAC,QAAA;kBACA;kBACAkB,QAAA;oBACAG,IAAA;kBACA;kBACAD,QAAA;oBACAC,IAAA;kBACA;kBACAI,SAAA;oBACAN,SAAA;sBACApB,KAAA;sBACAc,IAAA;oBACA;kBACA;gBACA;gBACAa,MAAA,GACA;kBACAb,IAAA;kBACA3E,IAAA,EAAAyC,SAAA,CAAAmC,GAAA,WAAAvB,IAAA;oBAAA;sBACAoC,KAAA,EAAApC,IAAA,CAAAoC,KAAA;sBACAC,SAAA;wBACA7B,KAAA,MAAAnE,OAAA,CAAAiG,OAAA,CAAAC,cAAA,cACA;0BAAAC,MAAA;0BAAAhC,KAAA;wBAAA,GACA;0BAAAgC,MAAA;0BAAAhC,KAAA;wBAAA,EACA;sBACA;oBACA;kBAAA;kBACAiC,QAAA;kBACAC,KAAA;oBACAZ,IAAA;oBACAa,QAAA;oBACAnC,KAAA;oBACAC,QAAA;oBACAW,SAAA;oBACAwB,QAAA;kBACA;kBACAP,SAAA;oBACAQ,YAAA;kBACA;kBACAC,QAAA;oBACAT,SAAA;sBACA7B,KAAA,MAAAnE,OAAA,CAAAiG,OAAA,CAAAC,cAAA,cACA;wBAAAC,MAAA;wBAAAhC,KAAA;sBAAA,GACA;wBAAAgC,MAAA;wBAAAhC,KAAA;sBAAA,EACA;oBACA;kBACA;gBACA;cAEA;cAEAxB,MAAA,CAAAlC,QAAA,CAAAoD,SAAA,CAAAb,MAAA;YAAA;cAAAE,SAAA,CAAAhB,IAAA;cAAA;YAAA;cAAAgB,SAAA,CAAAjB,IAAA;cAAAiB,SAAA,CAAAwD,EAAA,GAAAxD,SAAA;cAGAP,MAAA,CAAAgE,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA1D,SAAA,CAAAX,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IAEA;IAIA;IACAR,YAAA,WAAAA,aAAA;MAAA,IAAAyE,MAAA;MAAA,OAAAnF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkF,SAAA;QAAA,IAAAhE,GAAA,EAAAiE,UAAA;QAAA,OAAApF,mBAAA,GAAAG,IAAA,UAAAkF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhF,IAAA,GAAAgF,SAAA,CAAA/E,IAAA;YAAA;cAAA+E,SAAA,CAAAhF,IAAA;cAAAgF,SAAA,CAAA/E,IAAA;cAAA,OAEAjC,eAAA,CAAA4G,MAAA,CAAAnG,QAAA;YAAA;cAAAoC,GAAA,GAAAmE,SAAA,CAAA3D,IAAA;cACA,IAAAR,GAAA,CAAAS,IAAA,UAAAT,GAAA,CAAAS,IAAA;gBACA;gBACAwD,UAAA,GAAAvD,KAAA,CAAAC,OAAA,CAAAX,GAAA,CAAAxC,IAAA,IAAAwC,GAAA,CAAAxC,IAAA,CAAA4G,KAAA;gBACAL,MAAA,CAAAtG,SAAA,GAAAwG,UAAA,CAAA7B,GAAA,WAAAvB,IAAA;kBAAA;oBACAwD,SAAA,EAAAN,MAAA,CAAAO,cAAA,CAAAzD,IAAA,CAAAwD,SAAA;oBACAE,EAAA,EAAA1D,IAAA,CAAA2D,MAAA;oBACAC,QAAA,KAAAC,MAAA,CAAA7D,IAAA,CAAA8D,aAAA;oBACAC,MAAA,EAAA/D,IAAA,CAAA+D;kBACA;gBAAA;cACA;gBACAb,MAAA,CAAAF,QAAA,CAAAC,KAAA,CAAA9D,GAAA,CAAA6E,GAAA;cACA;cAAAV,SAAA,CAAA/E,IAAA;cAAA;YAAA;cAAA+E,SAAA,CAAAhF,IAAA;cAAAgF,SAAA,CAAAP,EAAA,GAAAO,SAAA;cAEAJ,MAAA,CAAAF,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAA1E,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IAEA;IAEA;IACAzE,gBAAA,WAAAA,iBAAA;MAAA,IAAAuF,MAAA;MAAA,OAAAlG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiG,SAAA;QAAA,IAAA/E,GAAA,EAAAiE,UAAA;QAAA,OAAApF,mBAAA,GAAAG,IAAA,UAAAgG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9F,IAAA,GAAA8F,SAAA,CAAA7F,IAAA;YAAA;cAAA6F,SAAA,CAAA9F,IAAA;cAAA8F,SAAA,CAAA7F,IAAA;cAAA,OAEAhC,cAAA,CAAA0H,MAAA,CAAAlH,QAAA;YAAA;cAAAoC,GAAA,GAAAiF,SAAA,CAAAzE,IAAA;cACA,IAAAR,GAAA,CAAAS,IAAA,UAAAT,GAAA,CAAAS,IAAA;gBACA;gBACAwD,UAAA,GAAAvD,KAAA,CAAAC,OAAA,CAAAX,GAAA,CAAAxC,IAAA,IAAAwC,GAAA,CAAAxC,IAAA,CAAA4G,KAAA;gBACAU,MAAA,CAAApH,aAAA,GAAAuG,UAAA,CAAA7B,GAAA,WAAAvB,IAAA;kBAAA;oBACAqE,WAAA,EAAAJ,MAAA,CAAAR,cAAA,CAAAzD,IAAA,CAAAsE,QAAA;oBACAC,MAAA,EAAAvE,IAAA,CAAAG,KAAA;oBACAqE,SAAA,EAAAxE,IAAA,CAAAyE,QAAA;oBACAV,MAAA,EAAA/D,IAAA,CAAA+D;kBACA;gBAAA;cACA;gBACAE,MAAA,CAAAjB,QAAA,CAAAC,KAAA,CAAA9D,GAAA,CAAA6E,GAAA;cACA;cAAAI,SAAA,CAAA7F,IAAA;cAAA;YAAA;cAAA6F,SAAA,CAAA9F,IAAA;cAAA8F,SAAA,CAAArB,EAAA,GAAAqB,SAAA;cAEAH,MAAA,CAAAjB,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAmB,SAAA,CAAAxF,IAAA;UAAA;QAAA,GAAAsF,QAAA;MAAA;IAEA;IAEA;IACAT,cAAA,WAAAA,eAAAiB,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAArB,MAAA,CAAAgB,IAAA,OAAAhB,MAAA,CAAAkB,KAAA,OAAAlB,MAAA,CAAAsB,GAAA,OAAAtB,MAAA,CAAAwB,KAAA,OAAAxB,MAAA,CAAA0B,OAAA,OAAA1B,MAAA,CAAA4B,OAAA;IACA;IAEA;IACA9G,QAAA,WAAAA,SAAA;MAAA,IAAAgH,MAAA;MAAA,OAAA5H,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2H,SAAA;QAAA,IAAAzG,GAAA;QAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAA0H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,IAAA,GAAAwH,SAAA,CAAAvH,IAAA;YAAA;cAAAuH,SAAA,CAAAxH,IAAA;cAAAwH,SAAA,CAAAvH,IAAA;cAAA,OAEA/B,iBAAA;YAAA;cAAA2C,GAAA,GAAA2G,SAAA,CAAAnG,IAAA;cACA,IAAAR,GAAA,CAAAS,IAAA,UAAAT,GAAA,CAAAS,IAAA;gBACA+F,MAAA,CAAAvI,KAAA;kBACAC,UAAA,EAAA8B,GAAA,CAAAxC,IAAA,CAAAU,UAAA;kBACAC,cAAA,EAAA6B,GAAA,CAAAxC,IAAA,CAAAW,cAAA;kBACAC,WAAA,EAAA4B,GAAA,CAAAxC,IAAA,CAAAY,WAAA;kBACAC,eAAA,EAAA2B,GAAA,CAAAxC,IAAA,CAAAa,eAAA;kBACAC,aAAA,EAAA0B,GAAA,CAAAxC,IAAA,CAAAc,aAAA;kBACAC,iBAAA,EAAAyB,GAAA,CAAAxC,IAAA,CAAAe,iBAAA;kBACAC,YAAA,EAAAwB,GAAA,CAAAxC,IAAA,CAAAgB,YAAA;kBACAC,gBAAA,EAAAuB,GAAA,CAAAxC,IAAA,CAAAiB,gBAAA;gBACA;cACA;cAAAkI,SAAA,CAAAvH,IAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAxH,IAAA;cAAAwH,SAAA,CAAA/C,EAAA,GAAA+C,SAAA;cAEAH,MAAA,CAAA3C,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA6C,SAAA,CAAAlH,IAAA;UAAA;QAAA,GAAAgH,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}