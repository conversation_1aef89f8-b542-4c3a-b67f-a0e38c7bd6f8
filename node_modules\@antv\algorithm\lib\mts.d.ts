import { GraphData, EdgeConfig } from './types';
/**
 * 最小生成树
 * refer: https://en.wikipedia.org/wiki/Kruskal%27s_algorithm
 * @param graph
 * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致
 * @param algo 'prim' | 'kruskal' 算法类型
 * @return EdgeConfig[] 返回构成MST的边的数组
 */
declare const minimumSpanningTree: (graphData: GraphData, weight?: string, algo?: string) => EdgeConfig[];
export default minimumSpanningTree;
