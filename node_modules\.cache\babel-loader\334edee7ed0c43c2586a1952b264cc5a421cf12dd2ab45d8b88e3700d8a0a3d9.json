{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Editor from '@/components/Editor';\nexport default {\n  name: 'NoticePublish',\n  components: {\n    Editor: Editor\n  },\n  data: function data() {\n    return {\n      form: {\n        title: '',\n        type: '',\n        sort: 0,\n        content: ''\n      },\n      rules: {\n        title: [{\n          required: true,\n          message: '请输入公告标题',\n          trigger: 'blur'\n        }],\n        type: [{\n          required: true,\n          message: '请选择公告类型',\n          trigger: 'change'\n        }],\n        content: [{\n          required: true,\n          message: '请输入公告内容',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    var id = this.$route.query.id;\n    if (id) {\n      this.form = {\n        title: '系统维护公告',\n        type: '1',\n        sort: 1,\n        content: '<p>尊敬的用户：</p><p>为了给您提供更好的服务，系统将于2024年1月21日凌晨2:00-4:00进行升级维护。</p><p>给您带来的不便敬请谅解。</p>'\n      };\n    }\n  },\n  methods: {\n    submitForm: function submitForm() {\n      var _this = this;\n      this.$refs.form.validate(function (valid) {\n        if (valid) {\n          _this.$message.success(_this.form.id ? '修改成功' : '发布成功');\n          _this.$router.push('/dashboard/notice/list');\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Editor", "name", "components", "data", "form", "title", "type", "sort", "content", "rules", "required", "message", "trigger", "created", "id", "$route", "query", "methods", "submitForm", "_this", "$refs", "validate", "valid", "$message", "success", "$router", "push"], "sources": ["src/views/notice/publish/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"公告标题\" prop=\"title\">\r\n          <el-input v-model=\"form.title\" placeholder=\"请输入公告标题\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公告类型\" prop=\"type\">\r\n          <el-select v-model=\"form.type\" placeholder=\"请选择公告类型\">\r\n            <el-option label=\"系统公告\" value=\"1\" />\r\n            <el-option label=\"活动公告\" value=\"2\" />\r\n            <el-option label=\"其他公告\" value=\"3\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序号\" prop=\"sort\">\r\n          <el-input-number v-model=\"form.sort\" :min=\"0\" :max=\"999\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公告内容\" prop=\"content\">\r\n          <div class=\"editor-container\">\r\n            <Editor v-model=\"form.content\" :height=\"400\" />\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"submitForm\">发布</el-button>\r\n          <el-button @click=\"$router.push('/dashboard/notice/list')\">取消</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Editor from '@/components/Editor'\r\n\r\nexport default {\r\n  name: 'NoticePublish',\r\n  components: {\r\n    Editor\r\n  },\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: '',\r\n        type: '',\r\n        sort: 0,\r\n        content: ''\r\n      },\r\n      rules: {\r\n        title: [\r\n          { required: true, message: '请输入公告标题', trigger: 'blur' }\r\n        ],\r\n        type: [\r\n          { required: true, message: '请选择公告类型', trigger: 'change' }\r\n        ],\r\n        content: [\r\n          { required: true, message: '请输入公告内容', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    const id = this.$route.query.id\r\n    if (id) {\r\n      this.form = {\r\n        title: '系统维护公告',\r\n        type: '1',\r\n        sort: 1,\r\n        content: '<p>尊敬的用户：</p><p>为了给您提供更好的服务，系统将于2024年1月21日凌晨2:00-4:00进行升级维护。</p><p>给您带来的不便敬请谅解。</p>'\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.$message.success(this.form.id ? '修改成功' : '发布成功')\r\n          this.$router.push('/dashboard/notice/list')\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .editor-container {\r\n    border: 1px solid #DCDFE6;\r\n    border-radius: 4px;\r\n  }\r\n}\r\n</style> "], "mappings": ";AAgCA,OAAAA,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF,MAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACAC,KAAA;QACAJ,KAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,IAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,OAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,EAAA;IACA,IAAAA,EAAA;MACA,KAAAV,IAAA;QACAC,KAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;MACA;IACA;EACA;EACAS,OAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAhB,IAAA,CAAAiB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,KAAA,CAAAI,QAAA,CAAAC,OAAA,CAAAL,KAAA,CAAAf,IAAA,CAAAU,EAAA;UACAK,KAAA,CAAAM,OAAA,CAAAC,IAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}