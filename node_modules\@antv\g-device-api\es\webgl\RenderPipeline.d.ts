import type { Format, MegaStateDescriptor, Render<PERSON><PERSON>eline, RenderPipelineDescriptor } from '../api';
import { ResourceType } from '../api';
import type { Device_GL } from './Device';
import type { InputLayout_GL } from './InputLayout';
import type { Program_GL } from './Program';
import { ResourceBase_GL } from './ResourceBase';
export declare class RenderPipeline_GL extends ResourceBase_GL implements RenderPipeline {
    type: ResourceType.RenderPipeline;
    program: Program_GL;
    drawMode: GLenum;
    megaState: MegaStateDescriptor;
    inputLayout: InputLayout_GL | null;
    colorAttachmentFormats: (Format | null)[];
    depthStencilAttachmentFormat: Format | null;
    sampleCount: number;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: RenderPipelineDescriptor;
    });
}
