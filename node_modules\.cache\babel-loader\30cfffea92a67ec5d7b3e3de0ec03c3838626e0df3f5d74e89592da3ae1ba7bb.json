{"ast": null, "code": "import _objectSpread from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport platform from './node/index.js';\nimport * as utils from './common/utils.js';\nexport default _objectSpread(_objectSpread({}, utils), platform);", "map": {"version": 3, "names": ["platform", "utils", "_objectSpread"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/axios/lib/platform/index.js"], "sourcesContent": ["import platform from './node/index.js';\nimport * as utils from './common/utils.js';\n\nexport default {\n  ...utils,\n  ...platform\n}\n"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,iBAAiB;AACtC,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAE1C,eAAAC,aAAA,CAAAA,aAAA,KACKD,KAAK,GACLD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}