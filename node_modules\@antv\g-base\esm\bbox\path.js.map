{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../../src/bbox/path.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,IAAI,QAAQ,EAAE,KAAK,IAAI,SAAS,EAAE,GAAG,IAAI,cAAc,EAAE,MAAM,cAAc,CAAC;AAC3F,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAGrD,OAAO,EAAE,cAAc,EAAE,MAAM,QAAQ,CAAC;AAExC,SAAS,UAAU,CAAC,QAAQ,EAAE,SAAS;IACrC,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAM,iBAAiB,GAAG,EAAE,CAAC;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpB,IAAA,YAAY,GAAuB,OAAO,aAA9B,EAAE,MAAM,GAAe,OAAO,OAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;QACnD,IAAI,GAAG,SAAA,CAAC;QACR,QAAQ,OAAO,CAAC,OAAO,EAAE;YACvB,KAAK,GAAG;gBACN,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzF,MAAM;YACR,KAAK,GAAG;gBACN,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChH,MAAM;YACR,KAAK,GAAG;gBACN,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;gBACpC,GAAG,GAAG,cAAc,CAAC,GAAG,CACtB,SAAS,CAAC,EAAE,EACZ,SAAS,CAAC,EAAE,EACZ,SAAS,CAAC,EAAE,EACZ,SAAS,CAAC,EAAE,EACZ,SAAS,CAAC,SAAS,EACnB,SAAS,CAAC,UAAU,EACpB,SAAS,CAAC,QAAQ,CACnB,CAAC;gBACF,MAAM;YACR;gBACE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM;SACT;QACD,IAAI,GAAG,EAAE;YACP,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;SACtC;QACD,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,GAAG,IAAI,OAAO,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE;YAC9G,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACjC;KACF;IACD,wDAAwD;IACxD,8CAA8C;IAC9C,gDAAgD;IAChD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,CAAC,QAAQ,EAA9D,CAA8D,CAAC,CAAC;IAC7F,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,CAAC,QAAQ,EAA9D,CAA8D,CAAC,CAAC;IAC7F,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;IACrB,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;IACrB,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;IACrB,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;IACrB,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO;YACL,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,IAAI;YACP,KAAK,EAAE,IAAI,GAAG,IAAI;YAClB,MAAM,EAAE,IAAI,GAAG,IAAI;SACpB,CAAC;KACH;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,IAAM,OAAO,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAA,YAAY,GAAK,OAAO,aAAZ,CAAa;QACjC,IAAI,KAAK,SAAA,CAAC;QACV,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5B,KAAK,GAAG,4BAA4B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACzD,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;SAC5B;aAAM,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACnC,KAAK,GAAG,4BAA4B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACzD,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;SAC5B;QACD,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5B,KAAK,GAAG,4BAA4B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACzD,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;SAC5B;aAAM,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACnC,KAAK,GAAG,4BAA4B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACzD,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;SAC5B;KACF;IACD,OAAO;QACL,CAAC,EAAE,IAAI;QACP,CAAC,EAAE,IAAI;QACP,KAAK,EAAE,IAAI,GAAG,IAAI;QAClB,MAAM,EAAE,IAAI,GAAG,IAAI;KACpB,CAAC;AACJ,CAAC;AAED,SAAS,4BAA4B,CAAC,OAAO,EAAE,SAAS;IAC9C,IAAA,QAAQ,GAA8B,OAAO,SAArC,EAAE,YAAY,GAAgB,OAAO,aAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IACtD,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9G,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjH,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrG,wBAAwB;IACxB,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAC5B,CAAC,aAAa,GAAG,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAC3G,CAAC;IACF,+BAA+B;IAC/B,0DAA0D;IAC1D,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE;QACnF,OAAO;YACL,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;SACV,CAAC;KACH;IACD,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClG,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClG,UAAU;IACV,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;IAC1D,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;IAC1D,4BAA4B;IAC5B,2BAA2B;IAC3B,IAAM,KAAK,GAAG;QACZ,SAAS;QACT,MAAM,EACJ,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,IAAI,CAAC;QACjH,SAAS;QACT,MAAM,EACJ,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,IAAI,CAAC;KAClH,CAAC;IACF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,CAAC,OAAO,WAAW,KAAa;IACpC,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IACnB,IAAA,IAAI,GAAa,KAAK,KAAlB,EAAE,MAAM,GAAK,KAAK,OAAV,CAAW;IAC/B,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B;IAC7E,IAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;IACxD,IAAA,KAA0B,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAvD,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAAoC,CAAC;IAChE,IAAI,IAAI,GAAG;QACT,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC,GAAG,KAAK;QACf,IAAI,EAAE,CAAC,GAAG,MAAM;KACjB,CAAC;IACF,IAAI,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACnC,OAAO;QACL,CAAC,EAAE,IAAI,CAAC,IAAI;QACZ,CAAC,EAAE,IAAI,CAAC,IAAI;QACZ,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QAC5B,MAAM,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;KAC9B,CAAC;AACJ,CAAC"}