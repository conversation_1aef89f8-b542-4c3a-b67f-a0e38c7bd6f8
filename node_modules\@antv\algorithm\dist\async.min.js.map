{"version": 3, "file": "async.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAmB,UAAID,IAEvBD,EAAgB,UAAIC,GACrB,CATD,CASGK,MAAM,I,kCCJTH,EAAOD,QAAU,SAAUK,EAASC,EAAmBC,EAAeC,GACpE,IAAIC,EAAcC,MAAQC,OAE1B,IACE,IACE,IAAIC,EAEJ,IAEEA,EAAO,IAAIH,EAAYI,KAAK,CAACR,GAC/B,CAAE,MAAOS,IAGPF,EAAO,IADWH,EAAYM,aAAeN,EAAYO,mBAAqBP,EAAYQ,gBAAkBR,EAAYS,gBAEnHC,OAAOd,GACZO,EAAOA,EAAKQ,SACd,CAEA,IAAIC,EAAMZ,EAAYY,KAAOZ,EAAYa,UACrCC,EAAYF,EAAIG,gBAAgBZ,GAChCa,EAAS,IAAIhB,EAAYH,GAAmBiB,EAAWhB,GAE3D,OADAc,EAAIK,gBAAgBH,GACbE,CACT,CAAE,MAAOX,GACP,OAAO,IAAIL,EAAYH,GAAmB,+BAA+BqB,OAAOC,mBAAmBvB,IAAWE,EAChH,CACF,CAAE,MAAOO,GACP,IAAKN,EACH,MAAMqB,MAAM,kCAGd,OAAO,IAAIpB,EAAYH,GAAmBE,EAAKD,EACjD,CACF,C,GCrCIuB,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAajC,QAGrB,IAAIC,EAAS6B,EAAyBE,GAAY,CAGjDhC,QAAS,CAAC,GAOX,OAHAmC,EAAoBH,GAAU/B,EAAQA,EAAOD,QAAS+B,GAG/C9B,EAAOD,OACf,CCrBA+B,EAAoBK,EAAKnC,IACxB,IAAIoC,EAASpC,GAAUA,EAAOqC,WAC7B,IAAOrC,EAAiB,QACxB,IAAM,EAEP,OADA8B,EAAoBQ,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdN,EAAoBQ,EAAI,CAACvC,EAASyC,KACjC,IAAI,IAAIC,KAAOD,EACXV,EAAoBY,EAAEF,EAAYC,KAASX,EAAoBY,EAAE3C,EAAS0C,IAC5EE,OAAOC,eAAe7C,EAAS0C,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDX,EAAoBY,EAAI,CAACK,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCAlFlB,EAAoBsB,EAAI,S,4CCAjB,I,kBCGQ,SAASC,IACtB,OAAO,IAAO,4n4CAA+74C,cAAUpB,EAAW,IAA0B,kBAC9/4C,CC2BA,QApBqB,SAAIqB,GAAiB,kB,IAAC,sDACzC,WAAIC,SAAW,SAACC,EAASC,GACrB,IAAMjC,EAAS,IAAI,EACnBA,EAAOkC,YAAY,CACfC,eAAeL,EACjBM,KAAI,IAGNpC,EAAOqC,UAAY,SAACC,GACZ,MAA2BA,EAAMF,KAA/BA,EAAI,OFOT,YEPyB,iBAE1BJ,EAAQI,GAERH,IAGFjC,EAAOuC,WACT,CACJ,GAjBA,CADwC,ECG1C,IAsCMC,EAAmB,SAACC,GACxB,SHjDa,cGiDb,CAE0BA,EAF1B,ECPF,SACEC,kBDjCwB,SAACD,EAAsBE,GAC/C,SHFc,gBGEgC,aAAI,CAACF,EAAWE,GAA9D,ECiCAC,wBD1B8B,SAACH,EAAsBE,GACrD,SHrBoB,sBGqBsC,aAAI,CAACF,EAAWE,GAA1E,EC0BAE,eDpBqB,SAACJ,GACtB,SHhBW,YGgBX,CAA8CA,EAA9C,ECoBAK,iBDbuB,SAACL,EAAsBM,GAC9C,SHvBa,cGuBb,CAAgDN,EAAWM,EAA3D,ECaAC,kBDNwB,SAACP,EAAsBM,GAC/C,SH7Bc,eG6Bd,CAAiDN,EAAWM,EAA5D,ECMAP,iBAAgB,EAChBS,yBAhC+BT,EAiC/BU,qBDO4B,SAACT,GAC9B,SHxDkB,kBGwDlB,CAE8BA,EAF9B,ECPCU,4BDemC,SAACV,GACrC,SHhEyB,yBGgEzB,CAEqCA,EAFrC,ECfCW,8BDuBqC,SAACX,GACvC,SHxE2B,2BGwE3B,CAEuCA,EAFvC,ECvBCY,cD+BoB,SACpBZ,EACAa,EACAX,EACAY,GAEA,SHrFU,YGyFY,aAAI,CAACd,EAAWa,EAAQX,EAAUY,GAJxD,ECpCAC,iBDiDuB,SAACf,EAAsBgB,EAAeC,EAAaf,GAC1E,SHlGa,eGkGkC,aAAI,CAACF,EAAWgB,EAAOC,EAAKf,GAA3E,ECjDAgB,sBD2D4B,SAC5BlB,EACAgB,EACAC,EACAf,EACAY,GAEA,SHlHkB,oBGsHY,aAAI,CAACd,EAAWgB,EAAOC,EAAKf,EAAUY,GAJpE,ECjEAK,mBD4EyB,SAACnB,EAAsBE,GAChD,SH7He,iBG6HgC,aAAI,CAACF,EAAWE,GAA/D,EC5EAkB,sBDqF4B,SAC5BpB,EACAE,EACAY,EACAO,GAEA,YAFA,IAAAA,IAAAA,EAAA,KAEA,EHtIkB,mBGsIlB,CACErB,EACAE,EACAY,EACAO,EAJF,EC1FAC,aDwGmB,SACnBtB,EACAE,EACAY,EACAS,GAEA,SHzJS,UGyJT,CAA6CvB,EAAWE,EAAUY,EAAoBS,EAAtF,EC7GAC,yBDsH+B,SAACxB,EAAsByB,EAAkBC,GACxE,SHjKqB,uBGiKoC,aAAI,CAAC1B,EAAWyB,EAAQC,GAAjF,ECtHAC,cD+HoB,SAAC3B,EAAsB4B,EAAkBC,GAC7D,SHhMU,YGkMY,aAAI,CAAC7B,EAAW4B,EAASC,GAF/C,EC/HAC,kBDyIwB,SACxBxB,EACAyB,EACA1C,GACG,SH9LW,gBG8LmC,aAAI,CAACiB,EAAQyB,EAAO1C,GAAlE,EC5IH2C,WDwJiB,SACjBhC,EACAiC,EACA/B,EACAgC,EACAC,EACAC,EACAC,GAEA,YANA,IAAAnC,IAAAA,GAAA,QAGA,IAAAkC,IAAAA,EAAA,gBACA,IAAAC,IAAAA,EAAA,WAEA,EH/MO,SG+MmC,aACrC,CAACrC,EAAWiC,EAAS/B,EAAUgC,EAAGC,EAAQC,EAAeC,GAD9D,E", "sources": ["webpack://Algorithm/webpack/universalModuleDefinition", "webpack://Algorithm/./node_modules/_worker-loader@3.0.8@worker-loader/dist/runtime/inline.js", "webpack://Algorithm/webpack/bootstrap", "webpack://Algorithm/webpack/runtime/compat get default export", "webpack://Algorithm/webpack/runtime/define property getters", "webpack://Algorithm/webpack/runtime/hasOwnProperty shorthand", "webpack://Algorithm/webpack/runtime/publicPath", "webpack://Algorithm/./src/workers/constant.ts", "webpack://Algorithm/./src/workers/index.worker.ts", "webpack://Algorithm/./src/workers/createWorker.ts", "webpack://Algorithm/./src/workers/index.ts", "webpack://Algorithm/./src/asyncIndex.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Algorithm\"] = factory();\n\telse\n\t\troot[\"Algorithm\"] = factory();\n})(this, () => {\nreturn ", "\"use strict\";\n\n/* eslint-env browser */\n\n/* eslint-disable no-undef, no-use-before-define, new-cap */\nmodule.exports = function (content, workerConstructor, workerOptions, url) {\n  var globalScope = self || window;\n\n  try {\n    try {\n      var blob;\n\n      try {\n        // New API\n        blob = new globalScope.Blob([content]);\n      } catch (e) {\n        // BlobBuilder = Deprecated, but widely implemented\n        var BlobBuilder = globalScope.BlobBuilder || globalScope.WebKitBlobBuilder || globalScope.MozBlobBuilder || globalScope.MSBlobBuilder;\n        blob = new BlobBuilder();\n        blob.append(content);\n        blob = blob.getBlob();\n      }\n\n      var URL = globalScope.URL || globalScope.webkitURL;\n      var objectURL = URL.createObjectURL(blob);\n      var worker = new globalScope[workerConstructor](objectURL, workerOptions);\n      URL.revokeObjectURL(objectURL);\n      return worker;\n    } catch (e) {\n      return new globalScope[workerConstructor](\"data:application/javascript,\".concat(encodeURIComponent(content)), workerOptions);\n    }\n  } catch (e) {\n    if (!url) {\n      throw Error(\"Inline worker is not supported\");\n    }\n\n    return new globalScope[workerConstructor](url, workerOptions);\n  }\n};", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "__webpack_require__.p = \"./dist\";", "export const ALGORITHM = {\n  pageRank: 'pageRank',\n  breadthFirstSearch: 'breadthFirstSearch',\n  connectedComponent: 'connectedComponent',\n  depthFirstSearch: 'depthFirstSearch',\n  detectCycle: 'detectCycle',\n  detectDirectedCycle: 'detectDirectedCycle',\n  detectAllCycles: 'detectAllCycles',\n  detectAllDirectedCycle: 'detectAllDirectedCycle',\n  detectAllUndirectedCycle: 'detectAllUndirectedCycle',\n  dijkstra: 'dijkstra',\n  findAllPath: 'findAllPath',\n  findShortestPath: 'findShortestPath',\n  floydWarshall: 'floydWarshall',\n  getAdjMatrix: 'getAdjMatrix',\n  getDegree: 'getDegree',\n  getInDegree: 'getInDegree',\n  getNeighbors: 'getNeighbors',\n  getOutDegree: 'getOutDegree',\n  labelPropagation: 'labelPropagation',\n  louvain: 'louvain',\n  GADDI: 'GADDI',\n  minimumSpanningTree: 'minimumSpanningTree',\n  SUCCESS: 'SUCCESS',\n  FAILURE: 'FAILURE',\n};\n\nexport const MESSAGE = {\n  SUCCESS: 'SUCCESS',\n  FAILURE: 'FAILURE',\n};\n", "\nimport worker from \"!!../../node_modules/_worker-loader@3.0.8@worker-loader/dist/runtime/inline.js\";\n\nexport default function Worker_fn() {\n  return worker(\"(()=>{\\\"use strict\\\";var e={d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{\\\"undefined\\\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\\\"Module\\\"}),Object.defineProperty(e,\\\"__esModule\\\",{value:!0})}},t={};e.r(t),e.d(t,{GADDI:()=>le,breadthFirstSearch:()=>c,connectedComponent:()=>l,depthFirstSearch:()=>y,detectCycle:()=>m,dijkstra:()=>D,findAllPath:()=>C,findShortestPath:()=>P,floydWarshall:()=>T,getAdjMatrix:()=>r,getDegree:()=>p,getInDegree:()=>g,getNeighbors:()=>d,getOutDegree:()=>b,labelPropagation:()=>q,louvain:()=>z,minimumSpanningTree:()=>V,pageRank:()=>J});const r=function(e,t){var r=e.nodes,n=e.edges,o=[],i={};if(!r)throw new Error(\\\"invalid nodes data!\\\");return r&&r.forEach((function(e,t){i[e.id]=t,o.push([])})),n&&n.forEach((function(e){var r=e.source,n=e.target,a=i[r],d=i[n];!a&&0!==a||!d&&0!==d||(o[a][d]=1,t||(o[d][a]=1))})),o};var n=function(e,t){return e===t},o=function(){function e(e,t){void 0===t&&(t=null),this.value=e,this.next=t}return e.prototype.toString=function(e){return e?e(this.value):\\\"\\\".concat(this.value)},e}();const i=function(){function e(e){void 0===e&&(e=n),this.head=null,this.tail=null,this.compare=e}return e.prototype.prepend=function(e){var t=new o(e,this.head);return this.head=t,this.tail||(this.tail=t),this},e.prototype.append=function(e){var t=new o(e);return this.head?(this.tail.next=t,this.tail=t,this):(this.head=t,this.tail=t,this)},e.prototype.delete=function(e){if(!this.head)return null;for(var t=null;this.head&&this.compare(this.head.value,e);)t=this.head,this.head=this.head.next;var r=this.head;if(null!==r)for(;r.next;)this.compare(r.next.value,e)?(t=r.next,r.next=r.next.next):r=r.next;return this.compare(this.tail.value,e)&&(this.tail=r),t},e.prototype.find=function(e){var t=e.value,r=void 0===t?void 0:t,n=e.callback,o=void 0===n?void 0:n;if(!this.head)return null;for(var i=this.head;i;){if(o&&o(i.value))return i;if(void 0!==r&&this.compare(i.value,r))return i;i=i.next}return null},e.prototype.deleteTail=function(){var e=this.tail;if(this.head===this.tail)return this.head=null,this.tail=null,e;for(var t=this.head;t.next;)t.next.next?t=t.next:t.next=null;return this.tail=t,e},e.prototype.deleteHead=function(){if(!this.head)return null;var e=this.head;return this.head.next?this.head=this.head.next:(this.head=null,this.tail=null),e},e.prototype.fromArray=function(e){var t=this;return e.forEach((function(e){return t.append(e)})),this},e.prototype.toArray=function(){for(var e=[],t=this.head;t;)e.push(t),t=t.next;return e},e.prototype.reverse=function(){for(var e=this.head,t=null,r=null;e;)r=e.next,e.next=t,t=e,e=r;this.tail=this.head,this.head=t},e.prototype.toString=function(e){return void 0===e&&(e=void 0),this.toArray().map((function(t){return t.toString(e)})).toString()},e}(),a=function(){function e(){this.linkedList=new i}return e.prototype.isEmpty=function(){return!this.linkedList.head},e.prototype.peek=function(){return this.linkedList.head?this.linkedList.head.value:null},e.prototype.enqueue=function(e){this.linkedList.append(e)},e.prototype.dequeue=function(){var e=this.linkedList.deleteHead();return e?e.value:null},e.prototype.toString=function(e){return this.linkedList.toString(e)},e}();var d=function(e,t,r){void 0===t&&(t=[]);var n=t.filter((function(t){return t.source===e||t.target===e}));return\\\"target\\\"===r?n.filter((function(t){return t.source===e})).map((function(e){return e.target})):\\\"source\\\"===r?n.filter((function(t){return t.target===e})).map((function(e){return e.source})):n.map((function(t){return t.source===e?t.target:t.source}))},s=function(e,t){return t.filter((function(t){return t.source===e||t.target===e}))},u=function(e){void 0===e&&(e=0);var t=\\\"\\\".concat(Math.random()).split(\\\".\\\")[1].substr(0,5),r=\\\"\\\".concat(Math.random()).split(\\\".\\\")[1].substr(0,5);return\\\"\\\".concat(e,\\\"-\\\").concat(t).concat(r)};const c=function(e,t,r,n){void 0===n&&(n=!0);var o=function(e){void 0===e&&(e={});var t,r=e,n=function(){},o=(t={},function(e){var r=e.next;return!t[r]&&(t[r]=!0,!0)});return r.allowTraversal=e.allowTraversal||o,r.enter=e.enter||n,r.leave=e.leave||n,r}(r),i=new a,s=e.edges,u=void 0===s?[]:s;i.enqueue(t);for(var c=\\\"\\\",f=function(){var e=i.dequeue();o.enter({current:e,previous:c}),d(e,u,n?\\\"target\\\":void 0).forEach((function(t){o.allowTraversal({previous:c,current:e,next:t})&&i.enqueue(t)})),o.leave({current:e,previous:c}),c=e};!i.isEmpty();)f()};var f=function(e){for(var t=e.nodes,r=void 0===t?[]:t,n=e.edges,o=void 0===n?[]:n,i=[],a={},s=[],u=function(e){s.push(e),a[e.id]=!0;for(var t=d(e.id,o),n=function(e){var n=t[e];if(!a[n]){var o=r.filter((function(e){return e.id===n}));o.length>0&&u(o[0])}},i=0;i<t.length;++i)n(i)},c=0;c<r.length;c++){var f=r[c];if(!a[f.id]){u(f);for(var h=[];s.length>0;)h.push(s.pop());i.push(h)}}return i},h=function(e){for(var t=e.nodes,r=void 0===t?[]:t,n=e.edges,o=void 0===n?[]:n,i=[],a={},s={},u={},c=[],f=0,h=function(e){s[e.id]=f,u[e.id]=f,f+=1,i.push(e),a[e.id]=!0;for(var t=d(e.id,o,\\\"target\\\").filter((function(e){return r.map((function(e){return e.id})).indexOf(e)>-1})),n=function(n){var o=t[n];if(s[o]||0===s[o])a[o]&&(u[e.id]=Math.min(u[e.id],s[o]));else{var i=r.filter((function(e){return e.id===o}));i.length>0&&h(i[0]),u[e.id]=Math.min(u[e.id],u[o])}},l=0;l<t.length;l++)n(l);if(u[e.id]===s[e.id]){for(var v=[];i.length>0;){var p=i.pop();if(a[p.id]=!1,v.push(p),p===e)break}v.length>0&&c.push(v)}},l=0,v=r;l<v.length;l++){var p=v[l];s[p.id]||0===s[p.id]||h(p)}return c};function l(e,t){return t?h(e):f(e)}var v=function(e){var t={},r=e.nodes,n=void 0===r?[]:r,o=e.edges,i=void 0===o?[]:o;return n.forEach((function(e){t[e.id]={degree:0,inDegree:0,outDegree:0}})),i.forEach((function(e){t[e.source].degree++,t[e.source].outDegree++,t[e.target].degree++,t[e.target].inDegree++})),t};const p=v;var g=function(e,t){return v(e)[t]?v(e)[t].inDegree:0},b=function(e,t){return v(e)[t]?v(e)[t].outDegree:0};function E(e,t,r,n,o){void 0===o&&(o=!0),n.enter({current:t,previous:r});var i=e.edges;d(t,void 0===i?[]:i,o?\\\"target\\\":void 0).forEach((function(i){n.allowTraversal({previous:r,current:t,next:i})&&E(e,i,t,n,o)})),n.leave({current:t,previous:r})}function y(e,t,r,n){void 0===n&&(n=!0),E(e,t,\\\"\\\",function(e){void 0===e&&(e={});var t,r=e,n=function(){},o=(t={},function(e){var r=e.next;return!t[r]&&(t[r]=!0,!0)});return r.allowTraversal=e.allowTraversal||o,r.enter=e.enter||n,r.leave=e.leave||n,r}(r),n)}const m=function(e){var t=null,r=e.nodes,n={},o={},i={},a={};(void 0===r?[]:r).forEach((function(e){o[e.id]=e}));for(var d={enter:function(e){var r=e.current,a=e.previous;if(i[r]){t={};for(var d=r,s=a;s!==r;)t[d]=s,d=s,s=n[s];t[d]=s}else i[r]=r,delete o[r],n[r]=a},leave:function(e){var t=e.current;a[t]=t,delete i[t]},allowTraversal:function(e){var r=e.next;return!t&&!a[r]}};Object.keys(o).length;)y(e,Object.keys(o)[0],d);return t};function L(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create,\\\"function\\\"==typeof SuppressedError&&SuppressedError;var N={}.toString;const w=function(e,t){return N.call(e)===\\\"[object \\\"+t+\\\"]\\\"},M=function(e){return w(e,\\\"Function\\\")},I=function(e){return Array.isArray?Array.isArray(e):w(e,\\\"Array\\\")};Object.keys;var j=Array.prototype;function k(e,t){void 0===t&&(t=new Map);var r=[];if(Array.isArray(e))for(var n=0,o=e.length;n<o;n++){var i=e[n];t.has(i)||(r.push(i),t.set(i,!0))}return r}j.splice,j.indexOf,Array.prototype.splice,Object.prototype.hasOwnProperty;Number.isInteger&&Number.isInteger,Math.PI,Math.PI;Object.values;Object.prototype;const x=function e(t){if(\\\"object\\\"!=typeof t||null===t)return t;var r;if(I(t)){r=[];for(var n=0,o=t.length;n<o;n++)\\\"object\\\"==typeof t[n]&&null!=t[n]?r[n]=e(t[n]):r[n]=t[n]}else for(var i in r={},t)\\\"object\\\"==typeof t[i]&&null!=t[i]?r[i]=e(t[i]):r[i]=t[i];return r};var O;Object.prototype.hasOwnProperty,Object.prototype.hasOwnProperty,function(e,t){if(!M(e))throw new TypeError(\\\"Expected a function\\\");new Map}((function(e,t){void 0===t&&(t={});var r,n=t.fontSize,o=t.fontFamily,i=t.fontWeight,a=t.fontStyle,d=t.fontVariant;return O||(O=document.createElement(\\\"canvas\\\").getContext(\\\"2d\\\")),O.font=[a,d,i,n+\\\"px\\\",o].join(\\\" \\\"),O.measureText((r=e,w(r,\\\"String\\\")?e:\\\"\\\")).width})),function(){function e(){this.map={}}e.prototype.has=function(e){return void 0!==this.map[e]},e.prototype.get=function(e,t){var r=this.map[e];return void 0===r?t:r},e.prototype.set=function(e,t){this.map[e]=t},e.prototype.clear=function(){this.map={}},e.prototype.delete=function(e){delete this.map[e]},e.prototype.size=function(){return Object.keys(this.map).length}}();const D=function(e,t,r,n){var o=e.nodes,i=void 0===o?[]:o,a=e.edges,d=void 0===a?[]:a,u=[],c={},f={},h={};i.forEach((function(e,r){var n=e.id;u.push(n),f[n]=1/0,n===t&&(f[n]=0)}));for(var l=i.length,v=function(e){var t=function(e,t,r){for(var n,o=1/0,i=0;i<t.length;i++){var a=t[i].id;!r[a]&&e[a]<=o&&(o=e[a],n=t[i])}return n}(f,i,c),o=t.id;if(c[o]=!0,f[o]===1/0)return\\\"continue\\\";var a=[];a=r?function(e,t){return t.filter((function(t){return t.source===e}))}(o,d):s(o,d),a.forEach((function(e){var r=e.target,i=e.source,a=r===o?i:r,d=n&&e[n]?e[n]:1;f[a]>f[t.id]+d?(f[a]=f[t.id]+d,h[a]=[t.id]):f[a]===f[t.id]+d&&h[a].push(t.id)}))},p=0;p<l;p++)v();h[t]=[t];var g={};for(var b in f)f[b]!==1/0&&S(t,b,h,g);var E={};for(var b in g)E[b]=g[b][0];return{length:f,path:E,allPath:g}};function S(e,t,r,n){if(e===t)return[e];if(n[t])return n[t];for(var o=[],i=0,a=r[t];i<a.length;i++){var d=S(e,a[i],r,n);if(!d)return;for(var s=0,u=d;s<u.length;s++){var c=u[s];I(c)?o.push(L(L([],c,!0),[t],!1)):o.push([c,t])}}return n[t]=o,n[t]}var P=function(e,t,r,n,o){var i=D(e,t,n,o),a=i.length,d=i.path,s=i.allPath;return{length:a[r],path:d[r],allPath:s[r]}},C=function(e,t,r,n){var o;if(t===r)return[[t]];var i=e.edges,a=void 0===i?[]:i,s=[t],u=((o={})[t]=!0,o),c=[],f=[],h=n?d(t,a,\\\"target\\\"):d(t,a);for(c.push(h);s.length>0&&c.length>0;){var l=c[c.length-1];if(l.length){var v=l.shift();if(v&&(s.push(v),u[v]=!0,h=n?d(v,a,\\\"target\\\"):d(v,a),c.push(h.filter((function(e){return!u[e]})))),s[s.length-1]===r){var p=s.map((function(e){return e}));f.push(p),g=s.pop(),u[g]=!1,c.pop()}}else{var g=s.pop();u[g]=!1,c.pop()}}return f};const T=function(e,t){for(var n=r(e,t),o=[],i=n.length,a=0;a<i;a+=1){o[a]=[];for(var d=0;d<i;d+=1)a===d?o[a][d]=0:0!==n[a][d]&&n[a][d]?o[a][d]=n[a][d]:o[a][d]=1/0}for(var s=0;s<i;s+=1)for(a=0;a<i;a+=1)for(d=0;d<i;d+=1)o[a][d]>o[a][s]+o[s][d]&&(o[a][d]=o[a][s]+o[s][d]);return o},q=function(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n=\\\"weight\\\"),void 0===o&&(o=1e3);var i=e.nodes,a=void 0===i?[]:i,d=e.edges,s=void 0===d?[]:d,c={},f={};a.forEach((function(e,t){var r=u();e.clusterId=r,c[r]={id:r,nodes:[e]},f[e.id]={node:e,idx:t}}));var h=r(e,t),l=[],v={};h.forEach((function(e,t){var r=0,n=a[t].id;v[n]={},e.forEach((function(e,t){if(e){r+=e;var o=a[t].id;v[n][o]=e}})),l.push(r)}));for(var p=0,g=function(){var e=!1;if(a.forEach((function(t){var r={};Object.keys(v[t.id]).forEach((function(e){var n=v[t.id][e],o=f[e].node.clusterId;r[o]||(r[o]=0),r[o]+=n}));var n=-1/0,o=[];if(Object.keys(r).forEach((function(e){n<r[e]?(n=r[e],o=[e]):n===r[e]&&o.push(e)})),1!==o.length||o[0]!==t.clusterId){var i=o.indexOf(t.clusterId);if(i>=0&&o.splice(i,1),o&&o.length){e=!0;var a=c[t.clusterId],d=a.nodes.indexOf(t);a.nodes.splice(d,1);var s=Math.floor(Math.random()*o.length),u=c[o[s]];u.nodes.push(t),t.clusterId=u.id}}})),!e)return\\\"break\\\";p++};p<o&&\\\"break\\\"!==g(););Object.keys(c).forEach((function(e){var t=c[e];t.nodes&&t.nodes.length||delete c[e]}));var b=[],E={};s.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=f[t].node.clusterId,a=f[r].node.clusterId,d=\\\"\\\".concat(i,\\\"---\\\").concat(a);if(E[d])E[d].weight+=o,E[d].count++;else{var s={source:i,target:a,weight:o,count:1};E[d]=s,b.push(s)}}));var y=[];return Object.keys(c).forEach((function(e){y.push(c[e])})),{clusters:y,clusterEdges:b}};const A=function(){function e(e){this.arr=e}return e.prototype.getArr=function(){return this.arr||[]},e.prototype.add=function(t){var r,n=t.arr;if(!(null===(r=this.arr)||void 0===r?void 0:r.length))return new e(n);if(!(null==n?void 0:n.length))return new e(this.arr);if(this.arr.length===n.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]+n[i];return new e(o)}},e.prototype.subtract=function(t){var r,n=t.arr;if(!(null===(r=this.arr)||void 0===r?void 0:r.length))return new e(n);if(!(null==n?void 0:n.length))return new e(this.arr);if(this.arr.length===n.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]-n[i];return new e(o)}},e.prototype.avg=function(t){var r=[];if(0!==t)for(var n in this.arr)r[n]=this.arr[n]/t;return new e(r)},e.prototype.negate=function(){var t=[];for(var r in this.arr)t[r]=-this.arr[r];return new e(t)},e.prototype.squareEuclideanDistance=function(e){var t,r=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null==r?void 0:r.length))return 0;if(this.arr.length===r.length){var n=0;for(var o in this.arr)n+=Math.pow(this.arr[o]-e.arr[o],2);return n}},e.prototype.euclideanDistance=function(e){var t,r=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null==r?void 0:r.length))return 0;if(this.arr.length===r.length){var n=0;for(var o in this.arr)n+=Math.pow(this.arr[o]-e.arr[o],2);return Math.sqrt(n)}console.error(\\\"The two vectors are unequal in length.\\\")},e.prototype.normalize=function(){var t=[],r=x(this.arr);r.sort((function(e,t){return e-t}));var n=r[r.length-1],o=r[0];for(var i in this.arr)t[i]=(this.arr[i]-o)/(n-o);return new e(t)},e.prototype.norm2=function(){var e;if(!(null===(e=this.arr)||void 0===e?void 0:e.length))return 0;var t=0;for(var r in this.arr)t+=Math.pow(this.arr[r],2);return Math.sqrt(t)},e.prototype.dot=function(e){var t,r=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null==r?void 0:r.length))return 0;if(this.arr.length===r.length){var n=0;for(var o in this.arr)n+=this.arr[o]*e.arr[o];return n}console.error(\\\"The two vectors are unequal in length.\\\")},e.prototype.equal=function(e){var t,r=e.arr;if((null===(t=this.arr)||void 0===t?void 0:t.length)!==(null==r?void 0:r.length))return!1;for(var n in this.arr)if(this.arr[n]!==r[n])return!1;return!0},e}();var F;!function(e){e.EuclideanDistance=\\\"euclideanDistance\\\"}(F||(F={}));var R=function(e,t,r){var n=function(e,t,r){var n=[];(null==t?void 0:t.length)?n=t:(e.forEach((function(e){n=n.concat(Object.keys(e))})),n=k(n));var o={};return n.forEach((function(t){var n=[];e.forEach((function(e){void 0!==e[t]&&\\\"\\\"!==e[t]&&n.push(e[t])})),n.length&&!(null==r?void 0:r.includes(t))&&(o[t]=k(n))})),o}(e,t,r),o=[];if(!Object.keys(n).length)return o;var i=Object.values(n).every((function(e){return e.every((function(e){return\\\"number\\\"==typeof e}))}));return e.forEach((function(e,t){var r=[];Object.keys(n).forEach((function(t){var o=e[t],a=n[t],d=a.findIndex((function(e){return o===e})),s=[];if(i)s.push(o);else for(var u=0;u<a.length;u++)u===d?s.push(1):s.push(0);r=r.concat(s)})),o[t]=r})),o},U=function(e,t,r,n){for(var o=t.length,i=2*n,a=0,d=0;d<o;d++)for(var s=e[d].clusterId,u=0;u<o;u++)s===e[u].clusterId&&(a+=(t[d][u]||0)-(r[d]||0)*(r[u]||0)/i);return a*(1/i)},G=function(e,t){void 0===e&&(e=[]);for(var r=e.length,n=new A([]),o=0;o<r;o++)n=n.add(new A(t[o]));var i=n.avg(r);i.normalize();var a=0;for(o=0;o<r;o++)a+=(s=new A(t[o])).squareEuclideanDistance(i);var d=[];for(e.forEach((function(){d.push([])})),o=0;o<r;o++){var s=new A(t[o]);e[o].clusterInertial=0;for(var u=0;u<r;u++)if(o!==u){var c=new A(t[u]);d[o][u]=s.squareEuclideanDistance(c),e[o].clusterInertial+=d[o][u]}else d[o][u]=0}var f=0,h=2*r*a;for(o=0;o<r;o++){var l=e[o].clusterId;for(u=0;u<r;u++){var v=e[u].clusterId;o!==u&&l===v&&(f+=e[o].clusterInertial*e[u].clusterInertial/Math.pow(h,2)-d[o][u]/h)}}return Number(f.toFixed(4))};const z=function(e,t,n,o,i,a,d,s,u){void 0===t&&(t=!1),void 0===n&&(n=\\\"weight\\\"),void 0===o&&(o=1e-4),void 0===i&&(i=!1),void 0===a&&(a=void 0),void 0===d&&(d=[]),void 0===s&&(s=[\\\"id\\\"]),void 0===u&&(u=1);var c=e.nodes,f=void 0===c?[]:c,h=e.edges,l=void 0===h?[]:h,v=[];if(i){f.forEach((function(e,t){e.properties=e.properties||{},e.originIndex=t}));var p=[];f.every((function(e){return e.hasOwnProperty(\\\"nodeType\\\")}))&&(p=Array.from(new Set(f.map((function(e){return e.nodeType})))),f.forEach((function(e){e.properties.nodeType=p.findIndex((function(t){return t===e.nodeType}))})));var g=function(e,t){void 0===t&&(t=void 0);var r=[];return e.forEach((function(e){void 0===t&&r.push(e),void 0!==e[t]&&r.push(e[t])})),r}(f,a);v=R(g,d,s)}var b=1,E={},y={};f.forEach((function(e,t){var r=String(b++);e.clusterId=r,E[r]={id:r,nodes:[e]},y[e.id]={node:e,idx:t}}));var m=r(e,t),L=[],N={},w=0;m.forEach((function(e,t){var r=0,n=f[t].id;N[n]={},e.forEach((function(e,t){if(e){r+=e;var o=f[t].id;N[n][o]=e,w+=e}})),L.push(r)})),w/=2;for(var M=1/0,I=1/0,j=0,k=[],O={};;){M=i&&f.every((function(e){return e.hasOwnProperty(\\\"properties\\\")}))?U(f,m,L,w)+G(f,v)*u:U(f,m,L,w),0===j&&(I=M,k=f,O=E);var D=M>0&&M>I&&M-I<o;if(M>I&&(k=f.map((function(e){return{node:e,clusterId:e.clusterId}})),O=x(E),I=M),D||j>100)break;j++,Object.keys(E).forEach((function(e){var t=0;l.forEach((function(r){var o=r.source,i=r.target,a=y[o].node.clusterId,d=y[i].node.clusterId;(a===e&&d!==e||d===e&&a!==e)&&(t+=r[n]||1)})),E[e].sumTot=t})),f.forEach((function(e,t){var r,o=E[e.clusterId],a=0,d=L[t]/(2*w),s=0,c=o.nodes;c.forEach((function(e){var r=y[e.id].idx;s+=m[t][r]||0}));var f=s-o.sumTot*d,h=c.filter((function(t){return t.id!==e.id})),p=[];h.forEach((function(e,t){p[t]=v[e.originIndex]}));var g=G(h,v)*u,b=N[e.id];if(Object.keys(b).forEach((function(n){var o=y[n].node.clusterId;if(o!==e.clusterId){var s=E[o],c=s.nodes;if(c&&c.length){var h=0;c.forEach((function(e){var r=y[e.id].idx;h+=m[t][r]||0}));var l=h-s.sumTot*d,p=c.concat([e]),b=[];p.forEach((function(e,t){b[t]=v[e.originIndex]}));var L=G(p,v)*u,N=l-f;i&&(N=l+L-(f+g)),N>a&&(a=N,r=s)}}})),a>0){r.nodes.push(e);var M=e.clusterId;e.clusterId=r.id;var I=o.nodes.indexOf(e);o.nodes.splice(I,1);var j=0,k=0;l.forEach((function(e){var t=e.source,o=e.target,i=y[t].node.clusterId,a=y[o].node.clusterId;(i===r.id&&a!==r.id||a===r.id&&i!==r.id)&&(j+=e[n]||1),(i===M&&a!==M||a===M&&i!==M)&&(k+=e[n]||1)})),r.sumTot=j,o.sumTot=k}}))}var S={},P=0;Object.keys(O).forEach((function(e){var t=O[e];if(t.nodes&&t.nodes.length){var r=String(P+1);r!==e&&(t.id=r,t.nodes=t.nodes.map((function(e){return{id:e.id,clusterId:r}})),O[r]=t,S[e]=r,delete O[e],P++)}else delete O[e]})),k.forEach((function(e){var t=e.node,r=e.clusterId;t&&(t.clusterId=r,t.clusterId&&S[t.clusterId]&&(t.clusterId=S[t.clusterId]))}));var C=[],T={};l.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=y[t].node.clusterId,a=y[r].node.clusterId;if(i&&a){var d=\\\"\\\".concat(i,\\\"---\\\").concat(a);if(T[d])T[d].weight+=o,T[d].count++;else{var s={source:i,target:a,weight:o,count:1};T[d]=s,C.push(s)}}}));var q=[];return Object.keys(O).forEach((function(e){q.push(O[e])})),{clusters:q,clusterEdges:C}},_=function(){function e(e){this.count=e.length,this.parent={};for(var t=0,r=e;t<r.length;t++){var n=r[t];this.parent[n]=n}}return e.prototype.find=function(e){for(;this.parent[e]!==e;)e=this.parent[e];return e},e.prototype.union=function(e,t){var r=this.find(e),n=this.find(t);r!==n&&(r<n?(this.parent[t]!==t&&this.union(this.parent[t],e),this.parent[t]=this.parent[e]):(this.parent[e]!==e&&this.union(this.parent[e],t),this.parent[e]=this.parent[t]))},e.prototype.connected=function(e,t){return this.find(e)===this.find(t)},e}();var B=function(e,t){return e-t};const H=function(){function e(e){void 0===e&&(e=B),this.compareFn=e,this.list=[]}return e.prototype.getLeft=function(e){return 2*e+1},e.prototype.getRight=function(e){return 2*e+2},e.prototype.getParent=function(e){return 0===e?null:Math.floor((e-1)/2)},e.prototype.isEmpty=function(){return this.list.length<=0},e.prototype.top=function(){return this.isEmpty()?void 0:this.list[0]},e.prototype.delMin=function(){var e=this.top(),t=this.list.pop();return this.list.length>0&&(this.list[0]=t,this.moveDown(0)),e},e.prototype.insert=function(e){if(null!==e){this.list.push(e);var t=this.list.length-1;return this.moveUp(t),!0}return!1},e.prototype.moveUp=function(e){for(var t=this.getParent(e);e&&e>0&&this.compareFn(this.list[t],this.list[e])>0;){var r=this.list[t];this.list[t]=this.list[e],this.list[e]=r,e=t,t=this.getParent(e)}},e.prototype.moveDown=function(e){var t,r=e,n=this.getLeft(e),o=this.getRight(e),i=this.list.length;null!==n&&n<i&&this.compareFn(this.list[r],this.list[n])>0?r=n:null!==o&&o<i&&this.compareFn(this.list[r],this.list[o])>0&&(r=o),e!==r&&(t=[this.list[r],this.list[e]],this.list[e]=t[0],this.list[r]=t[1],this.moveDown(r))},e}();var W=function(e,t){var r=[],n=e.nodes,o=void 0===n?[]:n,i=e.edges,a=void 0===i?[]:i;if(0===o.length)return r;var d=o[0],u=new Set;u.add(d);var c=new H((function(e,r){return t?e.weight-r.weight:0}));for(s(d.id,a).forEach((function(e){c.insert(e)}));!c.isEmpty();){var f=c.delMin(),h=f.source,l=f.target;u.has(h)&&u.has(l)||(r.push(f),u.has(h)||(u.add(h),s(h,a).forEach((function(e){c.insert(e)}))),u.has(l)||(u.add(l),s(l,a).forEach((function(e){c.insert(e)}))))}return r},K=function(e,t){var r=[],n=e.nodes,o=void 0===n?[]:n,i=e.edges,a=void 0===i?[]:i;if(0===o.length)return r;var d=a.map((function(e){return e}));t&&d.sort((function(e,t){return e.weight-t.weight}));for(var s=new _(o.map((function(e){return e.id})));d.length>0;){var u=d.shift(),c=u.source,f=u.target;s.connected(c,f)||(r.push(u),s.union(c,f))}return r};const V=function(e,t,r){return r?{prim:W,kruskal:K}[r](e,t):K(e,t)},J=function(e,t,r){\\\"number\\\"!=typeof t&&(t=1e-6),\\\"number\\\"!=typeof r&&(r=.85);for(var n,o=1,i=0,a=1e3,s=e.nodes,u=void 0===s?[]:s,c=e.edges,f=void 0===c?[]:c,h=u.length,l={},v={},g=0;g<h;++g)l[E=(w=u[g]).id]=1/h,v[E]=1/h;for(var b=p(e);a>0&&o>t;){for(i=0,g=0;g<h;++g){var E=(w=u[g]).id;if(n=0,0===b[w.id].inDegree)l[E]=0;else{for(var y=d(E,f,\\\"source\\\"),m=0;m<y.length;++m){var L=y[m],N=b[L].outDegree;N>0&&(n+=v[L]/N)}l[E]=r*n,i+=l[E]}}for(i=(1-i)/h,o=0,g=0;g<h;++g){var w;n=l[E=(w=u[g]).id]+i,o+=Math.abs(n-v[E]),v[E]=n}a-=1}return v};var Q=\\\"-1\\\",X=function(e,t,r,n){void 0===e&&(e=-1),void 0===t&&(t=-1),void 0===r&&(r=-1),void 0===n&&(n=\\\"-1\\\"),this.id=e,this.from=t,this.to=r,this.label=n},Y=function(){function e(e,t){void 0===e&&(e=-1),void 0===t&&(t=Q),this.id=e,this.label=t,this.edges=[],this.edgeMap={}}return e.prototype.addEdge=function(e){this.edges.push(e),this.edgeMap[e.id]=e},e}(),Z=function(){function e(e,t,r){void 0===e&&(e=-1),void 0===t&&(t=!0),void 0===r&&(r=!1),this.id=e,this.edgeIdAutoIncrease=t,this.edges=[],this.nodes=[],this.nodeMap={},this.edgeMap={},this.nodeLabelMap={},this.edgeLabelMap={},this.counter=0,this.directed=r}return e.prototype.getNodeNum=function(){return this.nodes.length},e.prototype.addNode=function(e,t){if(!this.nodeMap[e]){var r=new Y(e,t);this.nodes.push(r),this.nodeMap[e]=r,this.nodeLabelMap[t]||(this.nodeLabelMap[t]=[]),this.nodeLabelMap[t].push(e)}},e.prototype.addEdge=function(e,t,r,n){if((this.edgeIdAutoIncrease||void 0===e)&&(e=this.counter++),!(this.nodeMap[t]&&this.nodeMap[r]&&this.nodeMap[r].edgeMap[e])){var o=new X(e,t,r,n);if(this.edges.push(o),this.edgeMap[e]=o,this.nodeMap[t].addEdge(o),this.edgeLabelMap[n]||(this.edgeLabelMap[n]=[]),this.edgeLabelMap[n].push(o),!this.directed){var i=new X(e,r,t,n);this.nodeMap[r].addEdge(i),this.edgeLabelMap[n].push(i)}}},e}(),$=function(){function e(e,t,r,n,o){this.fromNode=e,this.toNode=t,this.nodeEdgeNodeLabel={nodeLabel1:r||Q,edgeLabel:n||\\\"-1\\\",nodeLabel2:o||Q}}return e.prototype.equalTo=function(e){return this.fromNode===e.formNode&&this.toNode===e.toNode&&this.nodeEdgeNodeLabel===e.nodeEdgeNodeLabel},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e}(),ee=function(){function e(){this.rmpath=[],this.dfsEdgeList=[]}return e.prototype.equalTo=function(e){var t=this.dfsEdgeList.length;if(t!==e.length)return!1;for(var r=0;r<t;r++)if(this.dfsEdgeList[r]!==e[r])return!1;return!0},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e.prototype.pushBack=function(e,t,r,n,o){return this.dfsEdgeList.push(new $(e,t,r,n,o)),this.dfsEdgeList},e.prototype.toGraph=function(e,t){void 0===e&&(e=-1),void 0===t&&(t=!1);var r=new Z(e,!0,t);return this.dfsEdgeList.forEach((function(e){var t=e.fromNode,n=e.toNode,o=e.nodeEdgeNodeLabel,i=o.nodeLabel1,a=o.edgeLabel,d=o.nodeLabel2;i!==Q&&r.addNode(t,i),d!==Q&&r.addNode(n,d),i!==Q&&d!==i&&r.addEdge(void 0,t,n,a)})),r},e.prototype.buildRmpath=function(){this.rmpath=[];for(var e=void 0,t=this.dfsEdgeList.length-1;t>=0;t--){var r=this.dfsEdgeList[t],n=r.fromNode,o=r.toNode;n<o&&(void 0===e||o===e)&&(this.rmpath.push(t),e=n)}return this.rmpath},e.prototype.getNodeNum=function(){var e={};return this.dfsEdgeList.forEach((function(t){e[t.fromNode]||(e[t.fromNode]=!0),e[t.toNode]||(e[t.toNode]=!0)})),Object.keys(e).length},e}(),te=function(){function e(e){if(this.his={},this.nodesUsed={},this.edgesUsed={},this.edges=[],e){for(;e;){var t=e.edge;this.edges.push(t),this.nodesUsed[t.from]=1,this.nodesUsed[t.to]=1,this.edgesUsed[t.id]=1,e=e.preNode}this.edges=this.edges.reverse()}}return e.prototype.hasNode=function(e){return 1===this.nodesUsed[e.id]},e.prototype.hasEdge=function(e){return 1===this.edgesUsed[e.id]},e}(),re=function(){function e(e){var t=e.graphs,r=e.minSupport,n=void 0===r?2:r,o=e.minNodeNum,i=void 0===o?1:o,a=e.maxNodeNum,d=void 0===a?4:a,s=e.top,u=void 0===s?10:s,c=e.directed,f=void 0!==c&&c,h=e.verbose,l=void 0!==h&&h;this.graphs=t,this.dfsCode=new ee,this.support=0,this.frequentSize1Subgraphs=[],this.frequentSubgraphs=[],this.minSupport=n,this.top=u,this.directed=f,this.counter=0,this.maxNodeNum=d,this.minNodeNum=i,this.verbose=l,this.maxNodeNum<this.minNodeNum&&(this.maxNodeNum=this.minNodeNum),this.reportDF=[]}return e.prototype.findForwardRootEdges=function(e,t){var r=this,n=[],o=e.nodeMap;return t.edges.forEach((function(e){(r.directed||t.label<=o[e.to].label)&&n.push(e)})),n},e.prototype.findBackwardEdge=function(e,t,r,n){if(!this.directed&&t===r)return null;for(var o=e.nodeMap,i=o[r.to].edges,a=i.length,d=0;d<a;d++){var s=i[d];if(!n.hasEdge(s)&&s.to===t.from)if(this.directed){if(o[t.from].label<o[r.to].label||o[t.from].label===o[r.to].label&&t.label<=s.label)return s}else if(t.label<s.label||t.label===s.label&&o[t.to].label<=o[r.to].label)return s}return null},e.prototype.findForwardPureEdges=function(e,t,r,n){for(var o=[],i=t.to,a=e.nodeMap[i].edges,d=a.length,s=0;s<d;s++){var u=a[s],c=e.nodeMap[u.to];r<=c.label&&!n.hasNode(c)&&o.push(u)}return o},e.prototype.findForwardRmpathEdges=function(e,t,r,n){for(var o=[],i=e.nodeMap,a=i[t.to].label,d=i[t.from].edges,s=d.length,u=0;u<s;u++){var c=d[u],f=i[c.to].label;t.to===c.to||r>f||n.hasNode(i[c.to])||(t.label<c.label||t.label===c.label&&a<=f)&&o.push(c)}return o},e.prototype.getSupport=function(e){var t={};return e.forEach((function(e){t[e.graphId]||(t[e.graphId]=!0)})),Object.keys(t).length},e.prototype.findMinLabel=function(e){var t=void 0;return Object.keys(e).forEach((function(r){var n=e[r],o=n.nodeLabel1,i=n.edgeLabel,a=n.nodeLabel2;t?(o<t.nodeLabel1||o===t.nodeLabel1&&i<t.edgeLabel||o===t.nodeLabel1&&i===t.edgeLabel&&a<t.nodeLabel2)&&(t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}):t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}})),t},e.prototype.isMin=function(){var e=this,t=this.dfsCode;if(this.verbose&&console.log(\\\"isMin checking\\\",t),1===t.dfsEdgeList.length)return!0;var r=this.directed,n=t.toGraph(-1,r),o=n.nodeMap,i=new ee,a={};n.nodes.forEach((function(t){e.findForwardRootEdges(n,t).forEach((function(e){var r=o[e.to],i=\\\"\\\".concat(t.label,\\\"-\\\").concat(e.label,\\\"-\\\").concat(r.label);a[i]||(a[i]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:r.label});var d={graphId:n.id,edge:e,preNode:null};a[i].projected.push(d)}))}));var d=this.findMinLabel(a);if(d){i.dfsEdgeList.push(new $(0,1,d.nodeLabel1,d.edgeLabel,d.nodeLabel2));var s=function(a){for(var d=i.buildRmpath(),u=i.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,c=i.dfsEdgeList[d[0]].toNode,f={},h=!1,l=0,v=r?-1:0,p=function(t){if(h)return\\\"break\\\";a.forEach((function(r){var o=new te(r),a=e.findBackwardEdge(n,o.edges[d[t]],o.edges[d[0]],o);a&&(f[a.label]||(f[a.label]={projected:[],edgeLabel:a.label}),f[a.label].projected.push({graphId:n.id,edge:f,preNode:r}),l=i.dfsEdgeList[d[t]].fromNode,h=!0)}))},g=d.length-1;g>v&&\\\"break\\\"!==p(g);g--);if(h){var b=e.findMinLabel(f);i.dfsEdgeList.push(new $(c,l,Q,b.edgeLabel,Q));var E=i.dfsEdgeList.length-1;return e.dfsCode.dfsEdgeList[E]===i.dfsEdgeList[E]&&s(f[b.edgeLabel].projected)}var y={};h=!1;var m=0;a.forEach((function(t){var r=new te(t),i=e.findForwardPureEdges(n,r.edges[d[0]],u,r);i.length>0&&(h=!0,m=c,i.forEach((function(e){var r=\\\"\\\".concat(e.label,\\\"-\\\").concat(o[e.to].label);y[r]||(y[r]={projected:[],edgeLabel:e.label,nodeLabel2:o[e.to].label}),y[r].projected.push({graphId:n.id,edge:e,preNode:t})})))}));var L=d.length,N=function(t){if(h)return\\\"break\\\";var r=d[t];a.forEach((function(t){var a=new te(t),d=e.findForwardRmpathEdges(n,a.edges[r],u,a);d.length>0&&(h=!0,m=i.dfsEdgeList[r].fromNode,d.forEach((function(e){var r=\\\"\\\".concat(e.label,\\\"-\\\").concat(o[e.to].label);y[r]||(y[r]={projected:[],edgeLabel:e.label,nodeLabel2:o[e.to].label}),y[r].projected.push({graphId:n.id,edge:e,preNode:t})})))}))};for(g=0;g<L&&\\\"break\\\"!==N(g);g++);if(!h)return!0;var w=e.findMinLabel(y);i.dfsEdgeList.push(new $(m,c+1,Q,w.edgeLabel,w.nodeLabel2));var M=i.dfsEdgeList.length-1;return t.dfsEdgeList[M]===i.dfsEdgeList[M]&&s(y[\\\"\\\".concat(w.edgeLabel,\\\"-\\\").concat(w.nodeLabel2)].projected)},u=\\\"\\\".concat(d.nodeLabel1,\\\"-\\\").concat(d.edgeLabel,\\\"-\\\").concat(d.nodeLabel2);return s(a[u].projected)}},e.prototype.report=function(){if(!(this.dfsCode.getNodeNum()<this.minNodeNum)){this.counter++;var e=this.dfsCode.toGraph(this.counter,this.directed);this.frequentSubgraphs.push(x(e))}},e.prototype.subGraphMining=function(e){var t=this;if(!(this.getSupport(e)<this.minSupport)&&this.isMin()){this.report();var r=this.dfsCode.getNodeNum(),n=this.dfsCode.buildRmpath(),o=this.dfsCode.dfsEdgeList[n[0]].toNode,i=this.dfsCode.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,a={},d={};e.forEach((function(e){for(var s=t.graphs[e.graphId],u=s.nodeMap,c=new te(e),f=n.length-1;f>=0;f--){var h=t.findBackwardEdge(s,c.edges[n[f]],c.edges[n[0]],c);if(h){var l=\\\"\\\".concat(t.dfsCode.dfsEdgeList[n[f]].fromNode,\\\"-\\\").concat(h.label);d[l]||(d[l]={projected:[],toNodeId:t.dfsCode.dfsEdgeList[n[f]].fromNode,edgeLabel:h.label}),d[l].projected.push({graphId:e.graphId,edge:h,preNode:e})}}if(!(r>=t.maxNodeNum)){t.findForwardPureEdges(s,c.edges[n[0]],i,c).forEach((function(t){var r=\\\"\\\".concat(o,\\\"-\\\").concat(t.label,\\\"-\\\").concat(u[t.to].label);a[r]||(a[r]={projected:[],fromNodeId:o,edgeLabel:t.label,nodeLabel2:u[t.to].label}),a[r].projected.push({graphId:e.graphId,edge:t,preNode:e})}));var v=function(r){t.findForwardRmpathEdges(s,c.edges[n[r]],i,c).forEach((function(o){var i=\\\"\\\".concat(t.dfsCode.dfsEdgeList[n[r]].fromNode,\\\"-\\\").concat(o.label,\\\"-\\\").concat(u[o.to].label);a[i]||(a[i]={projected:[],fromNodeId:t.dfsCode.dfsEdgeList[n[r]].fromNode,edgeLabel:o.label,nodeLabel2:u[o.to].label}),a[i].projected.push({graphId:e.graphId,edge:o,preNode:e})}))};for(f=0;f<n.length;f++)v(f)}})),Object.keys(d).forEach((function(e){var r=d[e],n=r.toNodeId,i=r.edgeLabel;t.dfsCode.dfsEdgeList.push(new $(o,n,\\\"-1\\\",i,\\\"-1\\\")),t.subGraphMining(d[e].projected),t.dfsCode.dfsEdgeList.pop()})),Object.keys(a).forEach((function(e){var r=a[e],n=r.fromNodeId,i=r.edgeLabel,d=r.nodeLabel2;t.dfsCode.dfsEdgeList.push(new $(n,o+1,Q,i,d)),t.subGraphMining(a[e].projected),t.dfsCode.dfsEdgeList.pop()}))}},e.prototype.generate1EdgeFrequentSubGraphs=function(){var e=this.graphs,t=this.directed,r=this.minSupport,n=this.frequentSize1Subgraphs,o={},i={},a={},d={};return Object.keys(e).forEach((function(r){var n=e[r],s=n.nodeMap;n.nodes.forEach((function(e,n){var u=e.label,c=\\\"\\\".concat(r,\\\"-\\\").concat(u);if(!a[c]){var f=o[u]||0;f++,o[u]=f}a[c]={graphKey:r,label:u},e.edges.forEach((function(e){var n=u,o=s[e.to].label;if(!t&&n>o){var a=o;o=n,n=a}var c=e.label,f=\\\"\\\".concat(r,\\\"-\\\").concat(n,\\\"-\\\").concat(c,\\\"-\\\").concat(o),h=\\\"\\\".concat(n,\\\"-\\\").concat(c,\\\"-\\\").concat(o);if(!i[h]){var l=i[h]||0;l++,i[h]=l}d[f]={graphId:r,nodeLabel1:n,edgeLabel:c,nodeLabel2:o}}))}))})),Object.keys(o).forEach((function(e){if(!(o[e]<r)){var t={nodes:[],edges:[]};t.nodes.push({id:\\\"0\\\",label:e}),n.push(t)}})),n},e.prototype.run=function(){var e=this;if(this.frequentSize1Subgraphs=this.generate1EdgeFrequentSubGraphs(),!(this.maxNodeNum<2)){var t=this.graphs,r=(this.directed,{});Object.keys(t).forEach((function(n){var o=t[n],i=o.nodeMap;o.nodes.forEach((function(t){e.findForwardRootEdges(o,t).forEach((function(e){var o=i[e.to],a=\\\"\\\".concat(t.label,\\\"-\\\").concat(e.label,\\\"-\\\").concat(o.label);r[a]||(r[a]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:o.label});var d={graphId:n,edge:e,preNode:null};r[a].projected.push(d)}))}))})),Object.keys(r).forEach((function(t){var n=r[t],o=n.projected,i=n.nodeLabel1,a=n.edgeLabel,d=n.nodeLabel2;e.dfsCode.dfsEdgeList.push(new $(0,1,i,a,d)),e.subGraphMining(o),e.dfsCode.dfsEdgeList.pop()}))}},e}(),ne=\\\"cluster\\\";var oe=function(e,t,r,n){void 0===r&&(r=\\\"cluster\\\"),void 0===n&&(n=2);var o=[],i=e.nodes;return t.forEach((function(e,t){o.push(ie(i,e,t,r,n))})),o},ie=function(e,t,r,n,o){var i=[r],a=[],d={};return t.forEach((function(t,s){if(t<=o&&r!==s){i.push(s),a.push(e[s]);var u=e[s][n];d[u]?(d[u].count++,d[u].dists.push(t)):d[u]={count:1,dists:[t]}}})),Object.keys(d).forEach((function(e){d[e].dists=d[e].dists.sort((function(e,t){return e-t}))})),{nodeIdx:r,nodeId:e[r].id,nodeIdxs:i,neighbors:a,neighborNum:i.length-1,nodeLabelCountMap:d}},ae=function(e,t,r,n){var o=r.nodes;return n||(n={}),Object.keys(e).forEach((function(i){var a,d;if(!n||!n[i]){n[i]={nodes:[],edges:[]};var s=e[i],u=null===(a=t[s.start])||void 0===a?void 0:a.nodeIdxs,c=null===(d=t[s.end])||void 0===d?void 0:d.nodeIdxs;if(u&&c){var f=new Set(c),h=u.filter((function(e){return f.has(e)}));if(h&&h.length){for(var l={},v=h.length,p=0;p<v;p++){var g=o[h[p]];n[i].nodes.push(g),l[g.id]=!0}r.edges.forEach((function(e){l[e.source]&&l[e.target]&&n[i].edges.push(e)}))}}}})),n},de=function(e,t,r,n){var o,i,a={};e.nodes.forEach((function(e){a[e.id]=e}));var d=0;return!(null===(o=null==t?void 0:t.edges)||void 0===o?void 0:o.length)||(null===(i=null==t?void 0:t.nodes)||void 0===i?void 0:i.length)<2?0:(e.edges.forEach((function(e){var o=a[e.source][r],i=a[e.target][r],s=null==t?void 0:t.nodes[0][r],u=null==t?void 0:t.nodes[1][r],c=null==t?void 0:t.edges[0][n];e[n]===c&&(o===s&&i===u||o===u&&i===s)&&d++})),d)},se=function(e,t){var r={},n={};return e.forEach((function(e,o){r[e.id]={idx:o,node:e,degree:0,inDegree:0,outDegree:0};var i=e[t];n[i]||(n[i]=[]),n[i].push(e)})),{nodeMap:r,nodeLabelMap:n}},ue=function(e,t,r){var n={},o={};return e.forEach((function(e,i){n[\\\"\\\".concat(u)]={idx:i,edge:e};var a=e[t];o[a]||(o[a]=[]),o[a].push(e);var d=r[e.source];d&&(d.degree++,d.outDegree++);var s=r[e.target];s&&(s.degree++,s.inDegree++)})),{edgeMap:n,edgeLabelMap:o}},ce=function(e,t,r){var n=t.length,o={};return t.forEach((function(t,i){for(var a=r?0:i+1,d=e[i].id,s=a;s<n;s++)if(i!==s){var u=e[s].id,c=t[s];o[\\\"\\\".concat(d,\\\"-\\\").concat(u)]=c,r||(o[\\\"\\\".concat(u,\\\"-\\\").concat(d)]=c)}})),o},fe=function(e,t,r,n,o,i,a,d,s,u,c){var f,h=\\\"\\\".concat(t.id,\\\"-\\\").concat(r.id);if(u&&u[h])return u[h];var l=c?c[h]:void 0;if(!l){var v=((f={})[h]={start:n[t.id].idx,end:n[r.id].idx,distance:o},f);l=(c=ae(v,i,e,c))[h]}return de(l,a,d,s)},he=function(e,t,r,n){var o,i,a,d=null===(o=e[t])||void 0===o?void 0:o.degree,s=null===(i=e[t])||void 0===i?void 0:i.inDegree,u=null===(a=e[t])||void 0===a?void 0:a.outDegree;return void 0===e[t]&&(d=1/0,s=1/0,u=1/0,n[t].forEach((function(e){var t=r[e.id].degree;d>t&&(d=t);var n=r[e.id].inDegree;s>n&&(s=n);var o=r[e.id].outDegree;u>o&&(u=o)})),e[t]={degree:d,inDegree:s,outDegree:u}),{minPatternNodeLabelDegree:d,minPatternNodeLabelInDegree:s,minPatternNodeLabelOutDegree:u}};const le=function(e,t,r,n,o,i,a){var d;if(void 0===r&&(r=!1),void 0===i&&(i=\\\"cluster\\\"),void 0===a&&(a=\\\"cluster\\\"),e&&e.nodes){var s=e.nodes.length;if(s){var u=T(e,r),c=T(t,r),f=ce(e.nodes,u,r),h=ce(t.nodes,c,r),l=se(e.nodes,i),v=l.nodeMap,p=l.nodeLabelMap,g=se(t.nodes,i),b=g.nodeMap,E=g.nodeLabelMap;ue(e.edges,a,v);var y=ue(t.edges,a,b).edgeLabelMap,m=[];null==c||c.forEach((function(e){m=m.concat(e)})),o||(o=Math.max.apply(Math,L(L([],m,!1),[2],!1))),n||(n=o);var N=oe(e,u,i,n),w=oe(t,c,i,n),M=function(e,t,r,n,o){var i=Math.ceil(r/t),a={},d=0;return n.forEach((function(e,n){for(var s=0,u=0,c=e.nodeIdxs,f=e.neighborNum-1;s<i;){for(var h=c[1+Math.floor(Math.random()*f)],l=0;(a[\\\"\\\".concat(n,\\\"-\\\").concat(h)]||a[\\\"\\\".concat(h,\\\"-\\\").concat(n)])&&(h=Math.floor(Math.random()*t),!(++l>2*t)););if(l<2*t&&(a[\\\"\\\".concat(n,\\\"-\\\").concat(h)]={start:n,end:h,distance:o[n][h]},s++,++d>=r))return a;if(++u>2*t)break}s<i&&(i=(i+(i-s))/(t-n-1))})),a}(0,s,Math.min(100,s*(s-1)/2),N,u),I=ae(M,N,e),j=function(e){var t=e.graphs,r=e.directed,n=void 0!==r&&r,o=e.nodeLabelProp,i=void 0===o?ne:o,a=e.edgeLabelProp,d=void 0===a?ne:a,s=function(e,t,r,n){var o={};return Object.keys(e).forEach((function(i,a){var d=e[i],s=new Z(a,!0,t),u={};d.nodes.forEach((function(e,t){s.addNode(t,e[r]),u[e.id]=t})),d.edges.forEach((function(e,t){var r=u[e.source],o=u[e.target];s.addEdge(-1,r,o,e[n])})),s&&s.getNodeNum()&&(o[s.id]=s)})),o}(t,n,i,d),u=e.minSupport,c=e.maxNodeNum,f=e.minNodeNum,h=e.verbose,l=e.top,v=new re({graphs:s,minSupport:u,maxNodeNum:c,minNodeNum:f,top:l,verbose:h,directed:n});v.run();var p=function(e,t,r){var n=[];return e.forEach((function(e){var o={nodes:[],edges:[]};e.nodes.forEach((function(e){var r;o.nodes.push(((r={id:\\\"\\\".concat(e.id)})[t]=e.label,r))})),e.edges.forEach((function(e){var t;o.edges.push(((t={source:\\\"\\\".concat(e.from),target:\\\"\\\".concat(e.to)})[r]=e.label,t))})),n.push(o)})),n}(v.frequentSubgraphs,i,d);return p}({graphs:I,nodeLabelProp:i,edgeLabelProp:a,minSupport:1,minNodeNum:1,maxNodeNum:4,directed:r}).slice(0,10),k=j.length,x=[];j.forEach((function(e,t){x[t]={},Object.keys(I).forEach((function(r){var n=I[r],o=de(n,e,i,a);x[t][r]=o}))}));var O=function(e,t,r){for(var n=1/0,o=0,i=function(t){var r=e[t],i=Object.keys(r).sort((function(e,t){return r[e]-r[t]})),a=[];i.forEach((function(e,t){a[t%10]||(a[t%10]={graphs:[],totalCount:0,aveCount:0}),a[t%10].graphs.push(e),a[t%10].totalCount+=r[e]}));var d=0,s=[];a.forEach((function(e){var t=e.totalCount/e.graphs.length;e.aveCount=t,s.push(t);var n=0,o=e.length;e.graphs.forEach((function(t,o){var i=r[t];e.graphs.forEach((function(e,t){o!==t&&(n+=Math.abs(i-r[e]))}))})),d+=n/=o*(o-1)/2})),d/=a.length;var u=0;s.forEach((function(e,t){s.forEach((function(r,n){t!==n&&(u+=Math.abs(e-r))})),u/=s.length*(s.length-1)/2}));var c=u-d;n<c&&(n=c,o=t)},a=0;a<t;a++)i(a);return{structure:r[o],structureCountMap:e[o]}}(x,k,j),S=O.structure,P=O.structureCountMap,C=t.nodes[0],q=[],A=null===(d=t.nodes[0])||void 0===d?void 0:d[i],F=-1/0;t.nodes.forEach((function(e){var t=e[i],r=p[t];(null==r?void 0:r.length)>F&&(F=r.length,q=r,A=t,C=e)}));var R={},U={},G={},z={},_={},B={};Object.keys(E).forEach((function(n,o){_[n]=[],r&&(B[n]=[]);var d=-1/0,s=E[n],u={};s.forEach((function(e){var t=h[\\\"\\\".concat(C.id,\\\"-\\\").concat(e.id)];if(t&&_[n].push(t),d<t&&(d=t),u[\\\"\\\".concat(C.id,\\\"-\\\").concat(e.id)]={start:0,end:b[e.id].idx,distance:t},r){var o=h[\\\"\\\".concat(e.id,\\\"-\\\").concat(C.id)];o&&B[n].push(o)}})),_[n]=_[n].sort((function(e,t){return e-t})),r&&(B[n]=B[n].sort((function(e,t){return e-t}))),U=ae(u,w,t,U);var c=[];if(Object.keys(u).forEach((function(e){if(G[e])c.push(G[e]);else{var t=U[e];G[e]=de(t,S,i,a),c.push(G[e])}})),c=c.sort((function(e,t){return t-e})),z[\\\"\\\".concat(C.id,\\\"-\\\").concat(n)]=c,n!==A)for(var l=function(t){var r=q[t],o=N[v[r.id].idx],d=o.nodeLabelCountMap[n],s=E[n].length;if(!d||d.count<s)return q.splice(t,1),\\\"continue\\\";for(var u=!1,h=0;h<s;h++)if(d.dists[h]>_[n][h]){u=!0;break}if(u)return q.splice(t,1),\\\"continue\\\";var l={};o.neighbors.forEach((function(e){var t=f[\\\"\\\".concat(r.id,\\\"-\\\").concat(e.id)];l[\\\"\\\".concat(r.id,\\\"-\\\").concat(e.id)]={start:v[r.id].idx,end:v[e.id].idx,distance:t}})),I=ae(l,N,e,I);var p=[];Object.keys(l).forEach((function(e){if(P[e])p.push(P[e]);else{var t=I[e];P[e]=de(t,S,i,a),p.push(P[e])}})),p=p.sort((function(e,t){return t-e}));var g=!1;for(h=0;h<s;h++)if(p[h]<c[h]){g=!0;break}return g?(q.splice(t,1),\\\"continue\\\"):void 0},p=((null==q?void 0:q.length)||0)-1;p>=0;p--)l(p)}));var H=[];null==q||q.forEach((function(n){for(var d=v[n.id].idx,s=ie(e.nodes,u[d],d,i,o).neighbors,c=!1,h=s.length-1;h>=0;h--){if(s.length+1<t.nodes.length)return void(c=!0);var l=s[h],p=l[i];if(E[p]&&E[p].length)if(_[p]&&_[p].length){var g=\\\"\\\".concat(n.id,\\\"-\\\").concat(l.id),y=f[g],m=_[p].length-1;if(y>_[p][m])s.splice(h,1);else{if(r){var L=\\\"\\\".concat(l.id,\\\"-\\\").concat(n.id),w=f[L];if(m=B[p].length-1,w>B[p][m]){s.splice(h,1);continue}}var M=P[g]?P[g]:fe(e,n,l,v,y,N,S,i,a,P,I),j=\\\"\\\".concat(C.id,\\\"-\\\").concat(p);if(M<z[j][z[j].length-1])s.splice(h,1);else{var k=he(R,p,b,E),x=k.minPatternNodeLabelDegree;k.minPatternNodeLabelInDegree,k.minPatternNodeLabelOutDegree,v[l.id].degree<x&&s.splice(h,1)}}}else s.splice(h,1);else s.splice(h,1)}c||H.push({nodes:[n].concat(s)})}));var W=D(t,C.id,!1).length,K={};r?(Object.keys(W).forEach((function(e){var t=b[e].node[i];K[t]?K[t].push(W[e]):K[t]=[W[e]]})),Object.keys(K).forEach((function(e){K[e].sort((function(e,t){return e-t}))}))):K=_;for(var V=function(n){var o=H[n],d=o.nodes[0],s={},u={};o.nodes.forEach((function(e,t){u[e.id]={idx:t,node:e,degree:0,inDegree:0,outDegree:0};var r=e[i];s[r]?s[r]++:s[r]=1}));var c=[],f={};e.edges.forEach((function(e){u[e.source]&&u[e.target]&&(c.push(e),f[e[a]]?f[e[a]]++:f[e[a]]=1,u[e.source].degree++,u[e.target].degree++,u[e.source].outDegree++,u[e.target].inDegree++)}));for(var h=Object.keys(y).length,l=!1,p=0;p<h;p++){var g=Object.keys(y)[p];if(!f[g]||f[g]<y[g].length){l=!0;break}}if(l)return H.splice(n,1),\\\"continue\\\";var m=c.length;if(m<t.edges.length)return H.splice(n,1),\\\"break\\\";var L=!1,N=function(e){var t=c[e],n=t[a],o=y[n];if(!o||!o.length)return f[n]--,o&&f[n]<o.length?(L=!0,\\\"break\\\"):(c.splice(e,1),u[t.source].degree--,u[t.target].degree--,u[t.source].outDegree--,u[t.target].inDegree--,\\\"continue\\\");var d=u[t.source].node[i],s=u[t.target].node[i],h=!1;return o.forEach((function(e){var t=b[e.source].node,n=b[e.target].node;t[i]===d&&n[i]===s&&(h=!0),r||t[i]!==s||n[i]!==d||(h=!0)})),h?void 0:(f[n]--,o&&f[n]<o.length?(L=!0,\\\"break\\\"):(c.splice(e,1),u[t.source].degree--,u[t.target].degree--,u[t.source].outDegree--,u[t.target].inDegree--,\\\"continue\\\"))};for(p=m-1;p>=0&&\\\"break\\\"!==N(p);p--);if(L)return H.splice(n,1),\\\"continue\\\";o.edges=c;var w=D(o,o.nodes[0].id,!1).length;if(Object.keys(w).reverse().forEach((function(e){if(e!==o.nodes[0].id&&!L){if(w[e]===1/0){var t=u[e].node[i];if(s[t]--,s[t]<E[t].length)return void(L=!0);var r=o.nodes.indexOf(u[e].node);return o.nodes.splice(r,1),void(u[e]=void 0)}var n=v[e].node[i];if(!K[n]||!K[n].length||w[e]>K[n][K[n].length-1]){if(t=u[e].node[i],s[t]--,s[t]<E[t].length)return void(L=!0);r=o.nodes.indexOf(u[e].node),o.nodes.splice(r,1),u[e]=void 0}}})),L)return H.splice(n,1),\\\"continue\\\";for(var M=!0,I=0;M&&!L;){if(M=!1,r?u[d.id].degree<b[C.id].degree||u[d.id].inDegree<b[C.id].inDegree||u[d.id].outDegree<b[C.id].outDegree:u[d.id].degree<b[C.id].degree){L=!0;break}if(s[d[i]]<E[d[i]].length){L=!0;break}for(var j=o.nodes.length-1;j>=0;j--){var k=o.nodes[j],x=u[k.id].degree,O=u[k.id].inDegree,S=u[k.id].outDegree,P=k[i],T=he(R,P,b,E),q=T.minPatternNodeLabelDegree,A=T.minPatternNodeLabelInDegree,F=T.minPatternNodeLabelOutDegree;if(r?x<q||O<A||S<F:x<q){if(s[k[i]]--,s[k[i]]<E[k[i]].length){L=!0;break}o.nodes.splice(j,1),u[k.id]=void 0,M=!0}}if(L||!M&&0!==I)break;for(var U=(m=c.length)-1;U>=0;U--){var G=c[U];if(!u[G.source]||!u[G.target]){c.splice(U,1);var z=G[a];if(f[z]--,u[G.source]&&(u[G.source].degree--,u[G.source].outDegree--),u[G.target]&&(u[G.target].degree--,u[G.target].inDegree--),y[z]&&f[z]<y[z].length){L=!0;break}M=!0}}I++}return L||L||o.nodes.length<t.nodes.length||c.length<t.edges.length?(H.splice(n,1),\\\"continue\\\"):void 0},J=H.length-1;J>=0&&\\\"break\\\"!==V(J);J--);var Q=H.length,X=function(e){var t=H[e],r={};t.edges.forEach((function(e){var t=\\\"\\\".concat(e.source,\\\"-\\\").concat(e.target,\\\"-\\\").concat(e.label);r[t]?r[t]++:r[t]=1}));for(var n=function(e){var t=H[e],n={};t.edges.forEach((function(e){var t=\\\"\\\".concat(e.source,\\\"-\\\").concat(e.target,\\\"-\\\").concat(e.label);n[t]?n[t]++:n[t]=1}));var o=!0;Object.keys(n).length!==Object.keys(r).length?o=!1:Object.keys(r).forEach((function(e){n[e]!==r[e]&&(o=!1)})),o&&H.splice(e,1)},o=Q-1;o>e;o--)n(o);Q=H.length};for(J=0;J<=Q-1;J++)X(J);return H}}};var ve=\\\"undefined\\\"!=typeof self?self:{};ve.onmessage=function(e){var r=e.data,n=r._algorithmType,o=r.data;if(n)if(\\\"function\\\"!=typeof t[n])ve.postMessage({_algorithmType:\\\"FAILURE\\\"});else{var i=t[n].apply(t,o);ve.postMessage({_algorithmType:\\\"SUCCESS\\\",data:i})}}})();\\n//# sourceMappingURL=index.worker.js.map\", \"Worker\", undefined, __webpack_public_path__ + \"index.worker.js\");\n}\n", "import { MESSAGE } from './constant';\nimport Worker from './index.worker';\n\ninterface Event {\n  type: string;\n  data: any;\n}\n\n/**\n * 创建一个在worker中运行的算法\n * @param type 算法类型\n */\nconst createWorker = <R>(type: string) => (...data) =>\n  new Promise<R>((resolve, reject) => {\n      const worker = new Worker();\n      worker.postMessage({\n          _algorithmType:type,\n        data,\n      });\n\n      worker.onmessage = (event: Event) => {\n        const { data, _algorithmType } = event.data;\n        if (MESSAGE.SUCCESS === _algorithmType) {\n          resolve(data);\n        } else {\n          reject();\n        }\n\n        worker.terminate();\n      };\n  });\n\nexport default createWorker;\n", "import {\n  GraphData,\n  DegreeType,\n  Matrix,\n  ClusterData,\n  EdgeConfig,\n  NodeConfig,\n} from '../types';\nimport createWorker from './createWorker';\nimport { ALGORITHM } from './constant';\n\n/**\n * @param graphData 图数据\n * @param directed 是否为有向图\n */\nconst getAdjMatrixAsync = (graphData: GraphData, directed?: boolean) =>\n  createWorker<Matrix[]>(ALGORITHM.getAdjMatrix)(...[graphData, directed]);\n\n/**\n * 图的连通分量\n * @param graphData 图数据\n * @param directed 是否为有向图\n */\nconst connectedComponentAsync = (graphData: GraphData, directed?: boolean) =>\n  createWorker<NodeConfig[][]>(ALGORITHM.connectedComponent)(...[graphData, directed]);\n\n/**\n * 获取节点的度\n * @param graphData 图数据\n */\nconst getDegreeAsync = (graphData: GraphData) =>\n  createWorker<DegreeType>(ALGORITHM.getDegree)(graphData);\n\n/**\n * 获取节点的入度\n * @param graphData 图数据\n * @param nodeId 节点ID\n */\nconst getInDegreeAsync = (graphData: GraphData, nodeId: string) =>\n  createWorker<DegreeType>(ALGORITHM.getInDegree)(graphData, nodeId);\n\n/**\n * 获取节点的出度\n * @param graphData 图数据\n * @param nodeId 节点ID\n */\nconst getOutDegreeAsync = (graphData: GraphData, nodeId: string) =>\n  createWorker<DegreeType>(ALGORITHM.getOutDegree)(graphData, nodeId);\n\n/**\n * 检测图中的(有向) Cycle\n * @param graphData 图数据\n */\nconst detectCycleAsync = (graphData: GraphData) =>\n  createWorker<{\n    [key: string]: string;\n  }>(ALGORITHM.detectCycle)(graphData);\n\n/**\n * 检测图中的(无向) Cycle\n * @param graphData 图数据\n */\n const detectAllCyclesAsync = (graphData: GraphData) =>\n createWorker<{\n   [key: string]: string;\n }>(ALGORITHM.detectAllCycles)(graphData);\n\n/**\n * 检测图中的所有(有向) Cycle\n * @param graphData 图数据\n */\n const detectAllDirectedCycleAsync = (graphData: GraphData) =>\n createWorker<{\n   [key: string]: string;\n }>(ALGORITHM.detectAllDirectedCycle)(graphData);\n\n/**\n * 检测图中的所有(无向) Cycle\n * @param graphData 图数据\n */\n const detectAllUndirectedCycleAsync = (graphData: GraphData) =>\n createWorker<{\n   [key: string]: string;\n }>(ALGORITHM.detectAllUndirectedCycle)(graphData);\n\n/**\n * Dijkstra's algorithm, See {@link https://en.wikipedia.org/wiki/Dijkstra%27s_algorithm}\n * @param graphData 图数据\n */\nconst dijkstraAsync = (\n  graphData: GraphData,\n  source: string,\n  directed?: boolean,\n  weightPropertyName?: string,\n) =>\n  createWorker<{\n    length: number;\n    path: any;\n    allPath: any;\n  }>(ALGORITHM.dijkstra)(...[graphData, source, directed, weightPropertyName]);\n\n/**\n * 查找两点之间的所有路径\n * @param graphData 图数据\n * @param start 路径起始点ID\n * @param end 路径终点ID\n * @param directed 是否为有向图\n */\nconst findAllPathAsync = (graphData: GraphData, start: string, end: string, directed?: boolean) =>\n  createWorker<string[][]>(ALGORITHM.findAllPath)(...[graphData, start, end, directed]);\n\n/**\n * 查找两点之间的所有路径\n * @param graphData 图数据\n * @param start 路径起始点ID\n * @param end 路径终点ID\n * @param directed 是否为有向图\n * @param weightPropertyName 边权重的属名称，若数据中没有权重，则默认每条边权重为 1\n */\nconst findShortestPathAsync = (\n  graphData: GraphData,\n  start: string,\n  end: string,\n  directed?: boolean,\n  weightPropertyName?: string,\n) =>\n  createWorker<{\n    length: number;\n    path: any;\n    allPath: any;\n  }>(ALGORITHM.findShortestPath)(...[graphData, start, end, directed, weightPropertyName]);\n\n/**\n * Floyd–Warshall algorithm, See {@link https://en.wikipedia.org/wiki/Floyd%E2%80%93Warshall_algorithm}\n * @param graphData 图数据\n * @param directed 是否为有向图\n */\nconst floydWarshallAsync = (graphData: GraphData, directed?: boolean) =>\n  createWorker<Matrix[]>(ALGORITHM.floydWarshall)(...[graphData, directed]);\n\n/**\n * 标签传播算法\n * @param graphData 图数据\n * @param directed 是否有向图，默认为 false\n * @param weightPropertyName 权重的属性字段\n * @param maxIteration 最大迭代次数\n */\nconst labelPropagationAsync = (\n  graphData: GraphData,\n  directed: boolean,\n  weightPropertyName: string,\n  maxIteration: number = 1000,\n) =>\n  createWorker<ClusterData>(ALGORITHM.labelPropagation)(\n    graphData,\n    directed,\n    weightPropertyName,\n    maxIteration,\n  );\n\n/**\n * 社区发现 louvain 算法\n * @param graphData 图数据\n * @param directed 是否有向图，默认为 false\n * @param weightPropertyName 权重的属性字段\n * @param threshold\n */\nconst louvainAsync = (\n  graphData: GraphData,\n  directed: boolean,\n  weightPropertyName: string,\n  threshold: number,\n) =>\n  createWorker<ClusterData>(ALGORITHM.louvain)(graphData, directed, weightPropertyName, threshold);\n\n/**\n * 最小生成树，See {@link https://en.wikipedia.org/wiki/Kruskal%27s_algorithm}\n * @param graph\n * @param weight 指定用于作为边权重的属性，若不指定，则认为所有边权重一致\n * @param algo 'prim' | 'kruskal' 算法类型\n * @return EdgeConfig[] 返回构成MST的边的数组\n */\nconst minimumSpanningTreeAsync = (graphData: GraphData, weight?: boolean, algo?: string) =>\n  createWorker<EdgeConfig[]>(ALGORITHM.minimumSpanningTree)(...[graphData, weight, algo]);\n\n/**\n * PageRank https://en.wikipedia.org/wiki/PageRank\n * refer: https://github.com/anvaka/ngraph.pagerank\n * @param graph\n * @param epsilon 判断是否收敛的精度值，默认 0.000001\n * @param linkProb 阻尼系数（dumping factor），指任意时刻，用户访问到某节点后继续访问该节点链接的下一个节点的概率，经验值 0.85\n */\nconst pageRankAsync = (graphData: GraphData, epsilon?: number, linkProb?: number) =>\n  createWorker<{\n    [key: string]: number;\n  }>(ALGORITHM.pageRank)(...[graphData, epsilon, linkProb]);\n\n/**\n * 获取指定节点的所有邻居\n * @param nodeId 节点 ID\n * @param edges 图中的所有边数据\n * @param type 邻居类型\n */\nconst getNeighborsAsync = (\n  nodeId: string,\n  edges: EdgeConfig[],\n  type?: 'target' | 'source' | undefined,\n) => createWorker<string[]>(ALGORITHM.getNeighbors)(...[nodeId, edges, type]);\n\n/**\n * GADDI 图模式匹配\n * @param graphData 原图数据\n * @param pattern 搜索图（需要在原图上搜索的模式）数据\n * @param directed 是否计算有向图，默认 false\n * @param k 参数 k，表示 k-近邻\n * @param length 参数 length\n * @param nodeLabelProp 节点数据中代表节点标签（分类信息）的属性名。默认为 cluster\n * @param edgeLabelProp 边数据中代表边标签（分类信息）的属性名。默认为 cluster\n */\nconst GADDIAsync = (\n  graphData: GraphData,\n  pattern: GraphData,\n  directed: boolean = false,\n  k: number,\n  length: number,\n  nodeLabelProp: string = 'cluster',\n  edgeLabelProp: string = 'cluster',\n) =>\n  createWorker<GraphData[]>(ALGORITHM.GADDI)(\n    ...[graphData, pattern, directed, k, length, nodeLabelProp, edgeLabelProp],\n  );\n\nexport {\n  getAdjMatrixAsync,\n  connectedComponentAsync,\n  getDegreeAsync,\n  getInDegreeAsync,\n  getOutDegreeAsync,\n  detectCycleAsync,\n  detectAllCyclesAsync,\n  detectAllDirectedCycleAsync,\n  detectAllUndirectedCycleAsync,\n  dijkstraAsync,\n  findAllPathAsync,\n  findShortestPathAsync,\n  floydWarshallAsync,\n  labelPropagationAsync,\n  louvainAsync,\n  minimumSpanningTreeAsync,\n  pageRankAsync,\n  getNeighborsAsync,\n  GADDIAsync,\n};\n", "import {\n  getAdjMatrixAsync,\n  connectedComponentAsync,\n  getDegreeAsync,\n  getInDegreeAsync,\n  getOutDegreeAsync,\n  detectCycleAsync,\n  detectAllCyclesAsync,\n  detectAllDirectedCycleAsync,\n  detectAllUndirectedCycleAsync,\n  dijkstraAsync,\n  findAllPathAsync,\n  findShortestPathAsync,\n  floydWarshallAsync,\n  labelPropagationAsync,\n  louvainAsync,\n  minimumSpanningTreeAsync,\n  pageRankAsync,\n  getNeighborsAsync,\n  GADDIAsync,\n} from './workers/index';\n\nconst detectDirectedCycleAsync = detectCycleAsync;\n\nexport {\n  getAdjMatrixAsync,\n  connectedComponentAsync,\n  getDegreeAsync,\n  getInDegreeAsync,\n  getOutDegreeAsync,\n  detectCycleAsync,\n  detectDirectedCycleAsync,\n  detectAllCyclesAsync,\n  detectAllDirectedCycleAsync,\n  detectAllUndirectedCycleAsync,\n  dijkstraAsync,\n  findAllPathAsync,\n  findShortestPathAsync,\n  floyd<PERSON>arshallAsync,\n  labelPropagationAsync,\n  louvainAsync,\n  minimumSpanningTreeAsync,\n  pageRankAsync,\n  getNeighborsAsync,\n  GADDIAsync,\n};\n\nexport default {\n  getAdjMatrixAsync,\n  connectedComponentAsync,\n  getDegreeAsync,\n  getInDegreeAsync,\n  getOutDegreeAsync,\n  detectCycleAsync,\n  detectDirectedCycleAsync,\n  detectAllCyclesAsync,\n  detectAllDirectedCycleAsync,\n  detectAllUndirectedCycleAsync,\n  dijkstraAsync,\n  findAllPathAsync,\n  findShortestPathAsync,\n  floydWarshallAsync,\n  labelPropagationAsync,\n  louvainAsync,\n  minimumSpanningTreeAsync,\n  pageRankAsync,\n  getNeighborsAsync,\n  GADDIAsync,\n};"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "content", "workerConstructor", "workerOptions", "url", "globalScope", "self", "window", "blob", "Blob", "e", "BlobBuilder", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "append", "getBlob", "URL", "webkitURL", "objectURL", "createObjectURL", "worker", "revokeObjectURL", "concat", "encodeURIComponent", "Error", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "p", "Worker_fn", "type", "Promise", "resolve", "reject", "postMessage", "_algorithmType", "data", "onmessage", "event", "terminate", "detectCycleAsync", "graphData", "getAdjMatrixAsync", "directed", "connectedComponentAsync", "getDegreeAsync", "getInDegreeAsync", "nodeId", "getOutDegreeAsync", "detectDirectedCycleAsync", "detectAllCyclesAsync", "detectAllDirectedCycleAsync", "detectAllUndirectedCycleAsync", "dijkstraAsync", "source", "weightPropertyName", "findAllPathAsync", "start", "end", "findShortestPathAsync", "floydWarshallAsync", "labelPropagationAsync", "maxIteration", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "minimumSpanningTreeAsync", "weight", "algo", "pageRankAsync", "epsilon", "linkProb", "getNeighborsAsync", "edges", "GADDIAsync", "pattern", "k", "length", "nodeLabelProp", "edgeLabelProp"], "sourceRoot": ""}