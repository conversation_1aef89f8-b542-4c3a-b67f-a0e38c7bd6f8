{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as polygonContain from 'zrender/lib/contain/polygon.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar TMP_TRANSFORM = [];\nfunction transformPoints(points, transform) {\n  for (var p = 0; p < points.length; p++) {\n    vec2.applyTransform(points[p], points[p], transform);\n  }\n}\nfunction updateBBoxFromPoints(points, min, max, projection) {\n  for (var i = 0; i < points.length; i++) {\n    var p = points[i];\n    if (projection) {\n      // projection may return null point.\n      p = projection.project(p);\n    }\n    if (p && isFinite(p[0]) && isFinite(p[1])) {\n      vec2.min(min, min, p);\n      vec2.max(max, max, p);\n    }\n  }\n}\nfunction centroid(points) {\n  var signedArea = 0;\n  var cx = 0;\n  var cy = 0;\n  var len = points.length;\n  var x0 = points[len - 1][0];\n  var y0 = points[len - 1][1];\n  // Polygon should been closed.\n  for (var i = 0; i < len; i++) {\n    var x1 = points[i][0];\n    var y1 = points[i][1];\n    var a = x0 * y1 - x1 * y0;\n    signedArea += a;\n    cx += (x0 + x1) * a;\n    cy += (y0 + y1) * a;\n    x0 = x1;\n    y0 = y1;\n  }\n  return signedArea ? [cx / signedArea / 3, cy / signedArea / 3, signedArea] : [points[0][0] || 0, points[0][1] || 0];\n}\nvar Region = /** @class */function () {\n  function Region(name) {\n    this.name = name;\n  }\n  Region.prototype.setCenter = function (center) {\n    this._center = center;\n  };\n  /**\n   * Get center point in data unit. That is,\n   * for GeoJSONRegion, the unit is lat/lng,\n   * for GeoSVGRegion, the unit is SVG local coord.\n   */\n  Region.prototype.getCenter = function () {\n    var center = this._center;\n    if (!center) {\n      // In most cases there are no need to calculate this center.\n      // So calculate only when called.\n      center = this._center = this.calcCenter();\n    }\n    return center;\n  };\n  return Region;\n}();\nexport { Region };\nvar GeoJSONPolygonGeometry = /** @class */function () {\n  function GeoJSONPolygonGeometry(exterior, interiors) {\n    this.type = 'polygon';\n    this.exterior = exterior;\n    this.interiors = interiors;\n  }\n  return GeoJSONPolygonGeometry;\n}();\nexport { GeoJSONPolygonGeometry };\nvar GeoJSONLineStringGeometry = /** @class */function () {\n  function GeoJSONLineStringGeometry(points) {\n    this.type = 'linestring';\n    this.points = points;\n  }\n  return GeoJSONLineStringGeometry;\n}();\nexport { GeoJSONLineStringGeometry };\nvar GeoJSONRegion = /** @class */function (_super) {\n  __extends(GeoJSONRegion, _super);\n  function GeoJSONRegion(name, geometries, cp) {\n    var _this = _super.call(this, name) || this;\n    _this.type = 'geoJSON';\n    _this.geometries = geometries;\n    _this._center = cp && [cp[0], cp[1]];\n    return _this;\n  }\n  GeoJSONRegion.prototype.calcCenter = function () {\n    var geometries = this.geometries;\n    var largestGeo;\n    var largestGeoSize = 0;\n    for (var i = 0; i < geometries.length; i++) {\n      var geo = geometries[i];\n      var exterior = geo.exterior;\n      // Simple trick to use points count instead of polygon area as region size.\n      // Ignore linestring\n      var size = exterior && exterior.length;\n      if (size > largestGeoSize) {\n        largestGeo = geo;\n        largestGeoSize = size;\n      }\n    }\n    if (largestGeo) {\n      return centroid(largestGeo.exterior);\n    }\n    // from bounding rect by default.\n    var rect = this.getBoundingRect();\n    return [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  };\n  GeoJSONRegion.prototype.getBoundingRect = function (projection) {\n    var rect = this._rect;\n    // Always recalculate if using projection.\n    if (rect && !projection) {\n      return rect;\n    }\n    var min = [Infinity, Infinity];\n    var max = [-Infinity, -Infinity];\n    var geometries = this.geometries;\n    each(geometries, function (geo) {\n      if (geo.type === 'polygon') {\n        // Doesn't consider hole\n        updateBBoxFromPoints(geo.exterior, min, max, projection);\n      } else {\n        each(geo.points, function (points) {\n          updateBBoxFromPoints(points, min, max, projection);\n        });\n      }\n    });\n    // Normalie invalid bounding.\n    if (!(isFinite(min[0]) && isFinite(min[1]) && isFinite(max[0]) && isFinite(max[1]))) {\n      min[0] = min[1] = max[0] = max[1] = 0;\n    }\n    rect = new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    if (!projection) {\n      this._rect = rect;\n    }\n    return rect;\n  };\n  GeoJSONRegion.prototype.contain = function (coord) {\n    var rect = this.getBoundingRect();\n    var geometries = this.geometries;\n    if (!rect.contain(coord[0], coord[1])) {\n      return false;\n    }\n    loopGeo: for (var i = 0, len = geometries.length; i < len; i++) {\n      var geo = geometries[i];\n      // Only support polygon.\n      if (geo.type !== 'polygon') {\n        continue;\n      }\n      var exterior = geo.exterior;\n      var interiors = geo.interiors;\n      if (polygonContain.contain(exterior, coord[0], coord[1])) {\n        // Not in the region if point is in the hole.\n        for (var k = 0; k < (interiors ? interiors.length : 0); k++) {\n          if (polygonContain.contain(interiors[k], coord[0], coord[1])) {\n            continue loopGeo;\n          }\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n  /**\n   * Transform the raw coords to target bounding.\n   * @param x\n   * @param y\n   * @param width\n   * @param height\n   */\n  GeoJSONRegion.prototype.transformTo = function (x, y, width, height) {\n    var rect = this.getBoundingRect();\n    var aspect = rect.width / rect.height;\n    if (!width) {\n      width = aspect * height;\n    } else if (!height) {\n      height = width / aspect;\n    }\n    var target = new BoundingRect(x, y, width, height);\n    var transform = rect.calculateTransform(target);\n    var geometries = this.geometries;\n    for (var i = 0; i < geometries.length; i++) {\n      var geo = geometries[i];\n      if (geo.type === 'polygon') {\n        transformPoints(geo.exterior, transform);\n        each(geo.interiors, function (interior) {\n          transformPoints(interior, transform);\n        });\n      } else {\n        each(geo.points, function (points) {\n          transformPoints(points, transform);\n        });\n      }\n    }\n    rect = this._rect;\n    rect.copy(target);\n    // Update center\n    this._center = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  };\n  GeoJSONRegion.prototype.cloneShallow = function (name) {\n    name == null && (name = this.name);\n    var newRegion = new GeoJSONRegion(name, this.geometries, this._center);\n    newRegion._rect = this._rect;\n    newRegion.transformTo = null; // Simply avoid to be called.\n    return newRegion;\n  };\n  return GeoJSONRegion;\n}(Region);\nexport { GeoJSONRegion };\nvar GeoSVGRegion = /** @class */function (_super) {\n  __extends(GeoSVGRegion, _super);\n  function GeoSVGRegion(name, elOnlyForCalculate) {\n    var _this = _super.call(this, name) || this;\n    _this.type = 'geoSVG';\n    _this._elOnlyForCalculate = elOnlyForCalculate;\n    return _this;\n  }\n  GeoSVGRegion.prototype.calcCenter = function () {\n    var el = this._elOnlyForCalculate;\n    var rect = el.getBoundingRect();\n    var center = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n    var mat = matrix.identity(TMP_TRANSFORM);\n    var target = el;\n    while (target && !target.isGeoSVGGraphicRoot) {\n      matrix.mul(mat, target.getLocalTransform(), mat);\n      target = target.parent;\n    }\n    matrix.invert(mat, mat);\n    vec2.applyTransform(center, center, mat);\n    return center;\n  };\n  return GeoSVGRegion;\n}(Region);\nexport { GeoSVGRegion };", "map": {"version": 3, "names": ["__extends", "BoundingRect", "vec2", "polygonContain", "matrix", "each", "TMP_TRANSFORM", "transformPoints", "points", "transform", "p", "length", "applyTransform", "updateBBoxFromPoints", "min", "max", "projection", "i", "project", "isFinite", "centroid", "signedArea", "cx", "cy", "len", "x0", "y0", "x1", "y1", "a", "Region", "name", "prototype", "setCenter", "center", "_center", "getCenter", "calcCenter", "GeoJSONPolygonGeometry", "exterior", "interiors", "type", "GeoJSONLineStringGeometry", "GeoJSONRegion", "_super", "geometries", "cp", "_this", "call", "largestGeo", "largestGeoSize", "geo", "size", "rect", "getBoundingRect", "x", "width", "y", "height", "_rect", "Infinity", "contain", "coord", "loopGeo", "k", "transformTo", "aspect", "target", "calculateTransform", "interior", "copy", "cloneShallow", "newRegion", "GeoSVGRegion", "elOnlyForCalculate", "_elOnlyForCalculate", "el", "mat", "identity", "isGeoSVGGraphicRoot", "mul", "getLocalTransform", "parent", "invert"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/coord/geo/Region.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as polygonContain from 'zrender/lib/contain/polygon.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar TMP_TRANSFORM = [];\nfunction transformPoints(points, transform) {\n  for (var p = 0; p < points.length; p++) {\n    vec2.applyTransform(points[p], points[p], transform);\n  }\n}\nfunction updateBBoxFromPoints(points, min, max, projection) {\n  for (var i = 0; i < points.length; i++) {\n    var p = points[i];\n    if (projection) {\n      // projection may return null point.\n      p = projection.project(p);\n    }\n    if (p && isFinite(p[0]) && isFinite(p[1])) {\n      vec2.min(min, min, p);\n      vec2.max(max, max, p);\n    }\n  }\n}\nfunction centroid(points) {\n  var signedArea = 0;\n  var cx = 0;\n  var cy = 0;\n  var len = points.length;\n  var x0 = points[len - 1][0];\n  var y0 = points[len - 1][1];\n  // Polygon should been closed.\n  for (var i = 0; i < len; i++) {\n    var x1 = points[i][0];\n    var y1 = points[i][1];\n    var a = x0 * y1 - x1 * y0;\n    signedArea += a;\n    cx += (x0 + x1) * a;\n    cy += (y0 + y1) * a;\n    x0 = x1;\n    y0 = y1;\n  }\n  return signedArea ? [cx / signedArea / 3, cy / signedArea / 3, signedArea] : [points[0][0] || 0, points[0][1] || 0];\n}\nvar Region = /** @class */function () {\n  function Region(name) {\n    this.name = name;\n  }\n  Region.prototype.setCenter = function (center) {\n    this._center = center;\n  };\n  /**\n   * Get center point in data unit. That is,\n   * for GeoJSONRegion, the unit is lat/lng,\n   * for GeoSVGRegion, the unit is SVG local coord.\n   */\n  Region.prototype.getCenter = function () {\n    var center = this._center;\n    if (!center) {\n      // In most cases there are no need to calculate this center.\n      // So calculate only when called.\n      center = this._center = this.calcCenter();\n    }\n    return center;\n  };\n  return Region;\n}();\nexport { Region };\nvar GeoJSONPolygonGeometry = /** @class */function () {\n  function GeoJSONPolygonGeometry(exterior, interiors) {\n    this.type = 'polygon';\n    this.exterior = exterior;\n    this.interiors = interiors;\n  }\n  return GeoJSONPolygonGeometry;\n}();\nexport { GeoJSONPolygonGeometry };\nvar GeoJSONLineStringGeometry = /** @class */function () {\n  function GeoJSONLineStringGeometry(points) {\n    this.type = 'linestring';\n    this.points = points;\n  }\n  return GeoJSONLineStringGeometry;\n}();\nexport { GeoJSONLineStringGeometry };\nvar GeoJSONRegion = /** @class */function (_super) {\n  __extends(GeoJSONRegion, _super);\n  function GeoJSONRegion(name, geometries, cp) {\n    var _this = _super.call(this, name) || this;\n    _this.type = 'geoJSON';\n    _this.geometries = geometries;\n    _this._center = cp && [cp[0], cp[1]];\n    return _this;\n  }\n  GeoJSONRegion.prototype.calcCenter = function () {\n    var geometries = this.geometries;\n    var largestGeo;\n    var largestGeoSize = 0;\n    for (var i = 0; i < geometries.length; i++) {\n      var geo = geometries[i];\n      var exterior = geo.exterior;\n      // Simple trick to use points count instead of polygon area as region size.\n      // Ignore linestring\n      var size = exterior && exterior.length;\n      if (size > largestGeoSize) {\n        largestGeo = geo;\n        largestGeoSize = size;\n      }\n    }\n    if (largestGeo) {\n      return centroid(largestGeo.exterior);\n    }\n    // from bounding rect by default.\n    var rect = this.getBoundingRect();\n    return [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  };\n  GeoJSONRegion.prototype.getBoundingRect = function (projection) {\n    var rect = this._rect;\n    // Always recalculate if using projection.\n    if (rect && !projection) {\n      return rect;\n    }\n    var min = [Infinity, Infinity];\n    var max = [-Infinity, -Infinity];\n    var geometries = this.geometries;\n    each(geometries, function (geo) {\n      if (geo.type === 'polygon') {\n        // Doesn't consider hole\n        updateBBoxFromPoints(geo.exterior, min, max, projection);\n      } else {\n        each(geo.points, function (points) {\n          updateBBoxFromPoints(points, min, max, projection);\n        });\n      }\n    });\n    // Normalie invalid bounding.\n    if (!(isFinite(min[0]) && isFinite(min[1]) && isFinite(max[0]) && isFinite(max[1]))) {\n      min[0] = min[1] = max[0] = max[1] = 0;\n    }\n    rect = new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    if (!projection) {\n      this._rect = rect;\n    }\n    return rect;\n  };\n  GeoJSONRegion.prototype.contain = function (coord) {\n    var rect = this.getBoundingRect();\n    var geometries = this.geometries;\n    if (!rect.contain(coord[0], coord[1])) {\n      return false;\n    }\n    loopGeo: for (var i = 0, len = geometries.length; i < len; i++) {\n      var geo = geometries[i];\n      // Only support polygon.\n      if (geo.type !== 'polygon') {\n        continue;\n      }\n      var exterior = geo.exterior;\n      var interiors = geo.interiors;\n      if (polygonContain.contain(exterior, coord[0], coord[1])) {\n        // Not in the region if point is in the hole.\n        for (var k = 0; k < (interiors ? interiors.length : 0); k++) {\n          if (polygonContain.contain(interiors[k], coord[0], coord[1])) {\n            continue loopGeo;\n          }\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n  /**\n   * Transform the raw coords to target bounding.\n   * @param x\n   * @param y\n   * @param width\n   * @param height\n   */\n  GeoJSONRegion.prototype.transformTo = function (x, y, width, height) {\n    var rect = this.getBoundingRect();\n    var aspect = rect.width / rect.height;\n    if (!width) {\n      width = aspect * height;\n    } else if (!height) {\n      height = width / aspect;\n    }\n    var target = new BoundingRect(x, y, width, height);\n    var transform = rect.calculateTransform(target);\n    var geometries = this.geometries;\n    for (var i = 0; i < geometries.length; i++) {\n      var geo = geometries[i];\n      if (geo.type === 'polygon') {\n        transformPoints(geo.exterior, transform);\n        each(geo.interiors, function (interior) {\n          transformPoints(interior, transform);\n        });\n      } else {\n        each(geo.points, function (points) {\n          transformPoints(points, transform);\n        });\n      }\n    }\n    rect = this._rect;\n    rect.copy(target);\n    // Update center\n    this._center = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  };\n  GeoJSONRegion.prototype.cloneShallow = function (name) {\n    name == null && (name = this.name);\n    var newRegion = new GeoJSONRegion(name, this.geometries, this._center);\n    newRegion._rect = this._rect;\n    newRegion.transformTo = null; // Simply avoid to be called.\n    return newRegion;\n  };\n  return GeoJSONRegion;\n}(Region);\nexport { GeoJSONRegion };\nvar GeoSVGRegion = /** @class */function (_super) {\n  __extends(GeoSVGRegion, _super);\n  function GeoSVGRegion(name, elOnlyForCalculate) {\n    var _this = _super.call(this, name) || this;\n    _this.type = 'geoSVG';\n    _this._elOnlyForCalculate = elOnlyForCalculate;\n    return _this;\n  }\n  GeoSVGRegion.prototype.calcCenter = function () {\n    var el = this._elOnlyForCalculate;\n    var rect = el.getBoundingRect();\n    var center = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n    var mat = matrix.identity(TMP_TRANSFORM);\n    var target = el;\n    while (target && !target.isGeoSVGGraphicRoot) {\n      matrix.mul(mat, target.getLocalTransform(), mat);\n      target = target.parent;\n    }\n    matrix.invert(mat, mat);\n    vec2.applyTransform(center, center, mat);\n    return center;\n  };\n  return GeoSVGRegion;\n}(Region);\nexport { GeoSVGRegion };"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAO,KAAKC,IAAI,MAAM,4BAA4B;AAClD,OAAO,KAAKC,cAAc,MAAM,gCAAgC;AAChE,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,IAAIC,aAAa,GAAG,EAAE;AACtB,SAASC,eAAeA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtCR,IAAI,CAACU,cAAc,CAACJ,MAAM,CAACE,CAAC,CAAC,EAAEF,MAAM,CAACE,CAAC,CAAC,EAAED,SAAS,CAAC;EACtD;AACF;AACA,SAASI,oBAAoBA,CAACL,MAAM,EAAEM,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAE;EAC1D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACG,MAAM,EAAEM,CAAC,EAAE,EAAE;IACtC,IAAIP,CAAC,GAAGF,MAAM,CAACS,CAAC,CAAC;IACjB,IAAID,UAAU,EAAE;MACd;MACAN,CAAC,GAAGM,UAAU,CAACE,OAAO,CAACR,CAAC,CAAC;IAC3B;IACA,IAAIA,CAAC,IAAIS,QAAQ,CAACT,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIS,QAAQ,CAACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzCR,IAAI,CAACY,GAAG,CAACA,GAAG,EAAEA,GAAG,EAAEJ,CAAC,CAAC;MACrBR,IAAI,CAACa,GAAG,CAACA,GAAG,EAAEA,GAAG,EAAEL,CAAC,CAAC;IACvB;EACF;AACF;AACA,SAASU,QAAQA,CAACZ,MAAM,EAAE;EACxB,IAAIa,UAAU,GAAG,CAAC;EAClB,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,GAAG,GAAGhB,MAAM,CAACG,MAAM;EACvB,IAAIc,EAAE,GAAGjB,MAAM,CAACgB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,IAAIE,EAAE,GAAGlB,MAAM,CAACgB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;IAC5B,IAAIU,EAAE,GAAGnB,MAAM,CAACS,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,IAAIW,EAAE,GAAGpB,MAAM,CAACS,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,IAAIY,CAAC,GAAGJ,EAAE,GAAGG,EAAE,GAAGD,EAAE,GAAGD,EAAE;IACzBL,UAAU,IAAIQ,CAAC;IACfP,EAAE,IAAI,CAACG,EAAE,GAAGE,EAAE,IAAIE,CAAC;IACnBN,EAAE,IAAI,CAACG,EAAE,GAAGE,EAAE,IAAIC,CAAC;IACnBJ,EAAE,GAAGE,EAAE;IACPD,EAAE,GAAGE,EAAE;EACT;EACA,OAAOP,UAAU,GAAG,CAACC,EAAE,GAAGD,UAAU,GAAG,CAAC,EAAEE,EAAE,GAAGF,UAAU,GAAG,CAAC,EAAEA,UAAU,CAAC,GAAG,CAACb,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACrH;AACA,IAAIsB,MAAM,GAAG,aAAa,YAAY;EACpC,SAASA,MAAMA,CAACC,IAAI,EAAE;IACpB,IAAI,CAACA,IAAI,GAAGA,IAAI;EAClB;EACAD,MAAM,CAACE,SAAS,CAACC,SAAS,GAAG,UAAUC,MAAM,EAAE;IAC7C,IAAI,CAACC,OAAO,GAAGD,MAAM;EACvB,CAAC;EACD;AACF;AACA;AACA;AACA;EACEJ,MAAM,CAACE,SAAS,CAACI,SAAS,GAAG,YAAY;IACvC,IAAIF,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAI,CAACD,MAAM,EAAE;MACX;MACA;MACAA,MAAM,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACE,UAAU,CAAC,CAAC;IAC3C;IACA,OAAOH,MAAM;EACf,CAAC;EACD,OAAOJ,MAAM;AACf,CAAC,CAAC,CAAC;AACH,SAASA,MAAM;AACf,IAAIQ,sBAAsB,GAAG,aAAa,YAAY;EACpD,SAASA,sBAAsBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;IACnD,IAAI,CAACC,IAAI,GAAG,SAAS;IACrB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC5B;EACA,OAAOF,sBAAsB;AAC/B,CAAC,CAAC,CAAC;AACH,SAASA,sBAAsB;AAC/B,IAAII,yBAAyB,GAAG,aAAa,YAAY;EACvD,SAASA,yBAAyBA,CAAClC,MAAM,EAAE;IACzC,IAAI,CAACiC,IAAI,GAAG,YAAY;IACxB,IAAI,CAACjC,MAAM,GAAGA,MAAM;EACtB;EACA,OAAOkC,yBAAyB;AAClC,CAAC,CAAC,CAAC;AACH,SAASA,yBAAyB;AAClC,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjD5C,SAAS,CAAC2C,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAACZ,IAAI,EAAEc,UAAU,EAAEC,EAAE,EAAE;IAC3C,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,EAAEjB,IAAI,CAAC,IAAI,IAAI;IAC3CgB,KAAK,CAACN,IAAI,GAAG,SAAS;IACtBM,KAAK,CAACF,UAAU,GAAGA,UAAU;IAC7BE,KAAK,CAACZ,OAAO,GAAGW,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,OAAOC,KAAK;EACd;EACAJ,aAAa,CAACX,SAAS,CAACK,UAAU,GAAG,YAAY;IAC/C,IAAIQ,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAII,UAAU;IACd,IAAIC,cAAc,GAAG,CAAC;IACtB,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,UAAU,CAAClC,MAAM,EAAEM,CAAC,EAAE,EAAE;MAC1C,IAAIkC,GAAG,GAAGN,UAAU,CAAC5B,CAAC,CAAC;MACvB,IAAIsB,QAAQ,GAAGY,GAAG,CAACZ,QAAQ;MAC3B;MACA;MACA,IAAIa,IAAI,GAAGb,QAAQ,IAAIA,QAAQ,CAAC5B,MAAM;MACtC,IAAIyC,IAAI,GAAGF,cAAc,EAAE;QACzBD,UAAU,GAAGE,GAAG;QAChBD,cAAc,GAAGE,IAAI;MACvB;IACF;IACA,IAAIH,UAAU,EAAE;MACd,OAAO7B,QAAQ,CAAC6B,UAAU,CAACV,QAAQ,CAAC;IACtC;IACA;IACA,IAAIc,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACjC,OAAO,CAACD,IAAI,CAACE,CAAC,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC,EAAEH,IAAI,CAACI,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EAC5D,CAAC;EACDf,aAAa,CAACX,SAAS,CAACsB,eAAe,GAAG,UAAUtC,UAAU,EAAE;IAC9D,IAAIqC,IAAI,GAAG,IAAI,CAACM,KAAK;IACrB;IACA,IAAIN,IAAI,IAAI,CAACrC,UAAU,EAAE;MACvB,OAAOqC,IAAI;IACb;IACA,IAAIvC,GAAG,GAAG,CAAC8C,QAAQ,EAAEA,QAAQ,CAAC;IAC9B,IAAI7C,GAAG,GAAG,CAAC,CAAC6C,QAAQ,EAAE,CAACA,QAAQ,CAAC;IAChC,IAAIf,UAAU,GAAG,IAAI,CAACA,UAAU;IAChCxC,IAAI,CAACwC,UAAU,EAAE,UAAUM,GAAG,EAAE;MAC9B,IAAIA,GAAG,CAACV,IAAI,KAAK,SAAS,EAAE;QAC1B;QACA5B,oBAAoB,CAACsC,GAAG,CAACZ,QAAQ,EAAEzB,GAAG,EAAEC,GAAG,EAAEC,UAAU,CAAC;MAC1D,CAAC,MAAM;QACLX,IAAI,CAAC8C,GAAG,CAAC3C,MAAM,EAAE,UAAUA,MAAM,EAAE;UACjCK,oBAAoB,CAACL,MAAM,EAAEM,GAAG,EAAEC,GAAG,EAAEC,UAAU,CAAC;QACpD,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF;IACA,IAAI,EAAEG,QAAQ,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIK,QAAQ,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIK,QAAQ,CAACJ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAII,QAAQ,CAACJ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnFD,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACvC;IACAsC,IAAI,GAAG,IAAIpD,YAAY,CAACa,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC;IACzE,IAAI,CAACE,UAAU,EAAE;MACf,IAAI,CAAC2C,KAAK,GAAGN,IAAI;IACnB;IACA,OAAOA,IAAI;EACb,CAAC;EACDV,aAAa,CAACX,SAAS,CAAC6B,OAAO,GAAG,UAAUC,KAAK,EAAE;IACjD,IAAIT,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACjC,IAAIT,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAI,CAACQ,IAAI,CAACQ,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACrC,OAAO,KAAK;IACd;IACAC,OAAO,EAAE,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEO,GAAG,GAAGqB,UAAU,CAAClC,MAAM,EAAEM,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;MAC9D,IAAIkC,GAAG,GAAGN,UAAU,CAAC5B,CAAC,CAAC;MACvB;MACA,IAAIkC,GAAG,CAACV,IAAI,KAAK,SAAS,EAAE;QAC1B;MACF;MACA,IAAIF,QAAQ,GAAGY,GAAG,CAACZ,QAAQ;MAC3B,IAAIC,SAAS,GAAGW,GAAG,CAACX,SAAS;MAC7B,IAAIrC,cAAc,CAAC0D,OAAO,CAACtB,QAAQ,EAAEuB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QACxD;QACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIxB,SAAS,GAAGA,SAAS,CAAC7B,MAAM,GAAG,CAAC,CAAC,EAAEqD,CAAC,EAAE,EAAE;UAC3D,IAAI7D,cAAc,CAAC0D,OAAO,CAACrB,SAAS,CAACwB,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5D,SAASC,OAAO;UAClB;QACF;QACA,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEpB,aAAa,CAACX,SAAS,CAACiC,WAAW,GAAG,UAAUV,CAAC,EAAEE,CAAC,EAAED,KAAK,EAAEE,MAAM,EAAE;IACnE,IAAIL,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACjC,IAAIY,MAAM,GAAGb,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACK,MAAM;IACrC,IAAI,CAACF,KAAK,EAAE;MACVA,KAAK,GAAGU,MAAM,GAAGR,MAAM;IACzB,CAAC,MAAM,IAAI,CAACA,MAAM,EAAE;MAClBA,MAAM,GAAGF,KAAK,GAAGU,MAAM;IACzB;IACA,IAAIC,MAAM,GAAG,IAAIlE,YAAY,CAACsD,CAAC,EAAEE,CAAC,EAAED,KAAK,EAAEE,MAAM,CAAC;IAClD,IAAIjD,SAAS,GAAG4C,IAAI,CAACe,kBAAkB,CAACD,MAAM,CAAC;IAC/C,IAAItB,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,UAAU,CAAClC,MAAM,EAAEM,CAAC,EAAE,EAAE;MAC1C,IAAIkC,GAAG,GAAGN,UAAU,CAAC5B,CAAC,CAAC;MACvB,IAAIkC,GAAG,CAACV,IAAI,KAAK,SAAS,EAAE;QAC1BlC,eAAe,CAAC4C,GAAG,CAACZ,QAAQ,EAAE9B,SAAS,CAAC;QACxCJ,IAAI,CAAC8C,GAAG,CAACX,SAAS,EAAE,UAAU6B,QAAQ,EAAE;UACtC9D,eAAe,CAAC8D,QAAQ,EAAE5D,SAAS,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLJ,IAAI,CAAC8C,GAAG,CAAC3C,MAAM,EAAE,UAAUA,MAAM,EAAE;UACjCD,eAAe,CAACC,MAAM,EAAEC,SAAS,CAAC;QACpC,CAAC,CAAC;MACJ;IACF;IACA4C,IAAI,GAAG,IAAI,CAACM,KAAK;IACjBN,IAAI,CAACiB,IAAI,CAACH,MAAM,CAAC;IACjB;IACA,IAAI,CAAChC,OAAO,GAAG,CAACkB,IAAI,CAACE,CAAC,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC,EAAEH,IAAI,CAACI,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EACpE,CAAC;EACDf,aAAa,CAACX,SAAS,CAACuC,YAAY,GAAG,UAAUxC,IAAI,EAAE;IACrDA,IAAI,IAAI,IAAI,KAAKA,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC;IAClC,IAAIyC,SAAS,GAAG,IAAI7B,aAAa,CAACZ,IAAI,EAAE,IAAI,CAACc,UAAU,EAAE,IAAI,CAACV,OAAO,CAAC;IACtEqC,SAAS,CAACb,KAAK,GAAG,IAAI,CAACA,KAAK;IAC5Ba,SAAS,CAACP,WAAW,GAAG,IAAI,CAAC,CAAC;IAC9B,OAAOO,SAAS;EAClB,CAAC;EACD,OAAO7B,aAAa;AACtB,CAAC,CAACb,MAAM,CAAC;AACT,SAASa,aAAa;AACtB,IAAI8B,YAAY,GAAG,aAAa,UAAU7B,MAAM,EAAE;EAChD5C,SAAS,CAACyE,YAAY,EAAE7B,MAAM,CAAC;EAC/B,SAAS6B,YAAYA,CAAC1C,IAAI,EAAE2C,kBAAkB,EAAE;IAC9C,IAAI3B,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,EAAEjB,IAAI,CAAC,IAAI,IAAI;IAC3CgB,KAAK,CAACN,IAAI,GAAG,QAAQ;IACrBM,KAAK,CAAC4B,mBAAmB,GAAGD,kBAAkB;IAC9C,OAAO3B,KAAK;EACd;EACA0B,YAAY,CAACzC,SAAS,CAACK,UAAU,GAAG,YAAY;IAC9C,IAAIuC,EAAE,GAAG,IAAI,CAACD,mBAAmB;IACjC,IAAItB,IAAI,GAAGuB,EAAE,CAACtB,eAAe,CAAC,CAAC;IAC/B,IAAIpB,MAAM,GAAG,CAACmB,IAAI,CAACE,CAAC,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC,EAAEH,IAAI,CAACI,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;IAChE,IAAImB,GAAG,GAAGzE,MAAM,CAAC0E,QAAQ,CAACxE,aAAa,CAAC;IACxC,IAAI6D,MAAM,GAAGS,EAAE;IACf,OAAOT,MAAM,IAAI,CAACA,MAAM,CAACY,mBAAmB,EAAE;MAC5C3E,MAAM,CAAC4E,GAAG,CAACH,GAAG,EAAEV,MAAM,CAACc,iBAAiB,CAAC,CAAC,EAAEJ,GAAG,CAAC;MAChDV,MAAM,GAAGA,MAAM,CAACe,MAAM;IACxB;IACA9E,MAAM,CAAC+E,MAAM,CAACN,GAAG,EAAEA,GAAG,CAAC;IACvB3E,IAAI,CAACU,cAAc,CAACsB,MAAM,EAAEA,MAAM,EAAE2C,GAAG,CAAC;IACxC,OAAO3C,MAAM;EACf,CAAC;EACD,OAAOuC,YAAY;AACrB,CAAC,CAAC3C,MAAM,CAAC;AACT,SAAS2C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}