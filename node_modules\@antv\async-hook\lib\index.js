'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

function __generator(thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) { throw t[1]; } return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) { throw new TypeError("Generator is already executing."); }
        while (_) { try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) { return t; }
            if (y = 0, t) { op = [op[0] & 2, t.value]; }
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) { _.ops.pop(); }
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; } }
        if (op[0] & 5) { throw op[1]; } return { value: op[0] ? op[1] : void 0, done: true };
    }
}

function __read(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) { return o; }
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) { ar.push(r.value); }
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) { m.call(i); }
        }
        finally { if (e) { throw e.error; } }
    }
    return ar;
}

function __spreadArray(to, from, pack) {
    if (pack || arguments.length === 2) { for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) { ar = Array.prototype.slice.call(from, 0, i); }
            ar[i] = from[i];
        }
    } }
    return to.concat(ar || Array.prototype.slice.call(from));
}

function getDefaultExportFromCjs (x) {
	return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}

var series$1 = {exports: {}};

var parallel$2 = {exports: {}};

var isArrayLike = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = isArrayLike;
function isArrayLike(value) {
    return value && typeof value.length === 'number' && value.length >= 0 && value.length % 1 === 0;
}
module.exports = exports['default'];
}(isArrayLike, isArrayLike.exports));

var wrapAsync$1 = {};

var asyncify = {exports: {}};

var initialParams = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});

exports.default = function (fn) {
    return function (/*, callback*/) {
        var args = [], len = arguments.length;
        while ( len-- ) args[ len ] = arguments[ len ];

        var callback = args.pop();
        return fn.call(this, args, callback);
    };
};

module.exports = exports["default"];
}(initialParams, initialParams.exports));

var setImmediate$1 = {};

Object.defineProperty(setImmediate$1, "__esModule", {
    value: true
});
setImmediate$1.fallback = fallback;
setImmediate$1.wrap = wrap;
/* istanbul ignore file */

var hasQueueMicrotask = setImmediate$1.hasQueueMicrotask = typeof queueMicrotask === 'function' && queueMicrotask;
var hasSetImmediate = setImmediate$1.hasSetImmediate = typeof setImmediate === 'function' && setImmediate;
var hasNextTick = setImmediate$1.hasNextTick = typeof process === 'object' && typeof process.nextTick === 'function';

function fallback(fn) {
    setTimeout(fn, 0);
}

function wrap(defer) {
    return function (fn) {
        var args = [], len = arguments.length - 1;
        while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];

        return defer(function () { return fn.apply(void 0, args); });
    };
}

var _defer;

if (hasQueueMicrotask) {
    _defer = queueMicrotask;
} else if (hasSetImmediate) {
    _defer = setImmediate;
} else if (hasNextTick) {
    _defer = process.nextTick;
} else {
    _defer = fallback;
}

setImmediate$1.default = wrap(_defer);

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = asyncify;

var _initialParams = initialParams.exports;

var _initialParams2 = _interopRequireDefault(_initialParams);

var _setImmediate = setImmediate$1;

var _setImmediate2 = _interopRequireDefault(_setImmediate);

var _wrapAsync = wrapAsync$1;

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Take a sync function and make it async, passing its return value to a
 * callback. This is useful for plugging sync functions into a waterfall,
 * series, or other async functions. Any arguments passed to the generated
 * function will be passed to the wrapped function (except for the final
 * callback argument). Errors thrown will be passed to the callback.
 *
 * If the function passed to `asyncify` returns a Promise, that promises's
 * resolved/rejected state will be used to call the callback, rather than simply
 * the synchronous return value.
 *
 * This also means you can asyncify ES2017 `async` functions.
 *
 * @name asyncify
 * @static
 * @memberOf module:Utils
 * @method
 * @alias wrapSync
 * @category Util
 * @param {Function} func - The synchronous function, or Promise-returning
 * function to convert to an {@link AsyncFunction}.
 * @returns {AsyncFunction} An asynchronous wrapper of the `func`. To be
 * invoked with `(args..., callback)`.
 * @example
 *
 * // passing a regular synchronous function
 * async.waterfall([
 *     async.apply(fs.readFile, filename, "utf8"),
 *     async.asyncify(JSON.parse),
 *     function (data, next) {
 *         // data is the result of parsing the text.
 *         // If there was a parsing error, it would have been caught.
 *     }
 * ], callback);
 *
 * // passing a function returning a promise
 * async.waterfall([
 *     async.apply(fs.readFile, filename, "utf8"),
 *     async.asyncify(function (contents) {
 *         return db.model.create(contents);
 *     }),
 *     function (model, next) {
 *         // `model` is the instantiated model object.
 *         // If there was an error, this function would be skipped.
 *     }
 * ], callback);
 *
 * // es2017 example, though `asyncify` is not needed if your JS environment
 * // supports async functions out of the box
 * var q = async.queue(async.asyncify(async function(file) {
 *     var intermediateStep = await processFile(file);
 *     return await somePromise(intermediateStep)
 * }));
 *
 * q.push(files);
 */
function asyncify(func) {
    if ((0, _wrapAsync.isAsync)(func)) {
        return function (/*, callback*/) {
            var args = [], len = arguments.length;
            while ( len-- ) args[ len ] = arguments[ len ];

            var callback = args.pop();
            var promise = func.apply(this, args);
            return handlePromise(promise, callback);
        };
    }

    return (0, _initialParams2.default)(function (args, callback) {
        var result;
        try {
            result = func.apply(this, args);
        } catch (e) {
            return callback(e);
        }
        // if result is Promise object
        if (result && typeof result.then === 'function') {
            return handlePromise(result, callback);
        } else {
            callback(null, result);
        }
    });
}

function handlePromise(promise, callback) {
    return promise.then(function (value) {
        invokeCallback(callback, null, value);
    }, function (err) {
        invokeCallback(callback, err && err.message ? err : new Error(err));
    });
}

function invokeCallback(callback, error, value) {
    try {
        callback(error, value);
    } catch (err) {
        (0, _setImmediate2.default)(function (e) {
            throw e;
        }, err);
    }
}
module.exports = exports['default'];
}(asyncify, asyncify.exports));

Object.defineProperty(wrapAsync$1, "__esModule", {
    value: true
});
wrapAsync$1.isAsyncIterable = wrapAsync$1.isAsyncGenerator = wrapAsync$1.isAsync = undefined;

var _asyncify = asyncify.exports;

var _asyncify2 = _interopRequireDefault(_asyncify);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function isAsync(fn) {
    return fn[Symbol.toStringTag] === 'AsyncFunction';
}

function isAsyncGenerator(fn) {
    return fn[Symbol.toStringTag] === 'AsyncGenerator';
}

function isAsyncIterable(obj) {
    return typeof obj[Symbol.asyncIterator] === 'function';
}

function wrapAsync(asyncFn) {
    if (typeof asyncFn !== 'function') { throw new Error('expected a function'); }
    return isAsync(asyncFn) ? (0, _asyncify2.default)(asyncFn) : asyncFn;
}

wrapAsync$1.default = wrapAsync;
wrapAsync$1.isAsync = isAsync;
wrapAsync$1.isAsyncGenerator = isAsyncGenerator;
wrapAsync$1.isAsyncIterable = isAsyncIterable;

var awaitify = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = awaitify;
// conditionally promisify a function.
// only return a promise if a callback is omitted
function awaitify(asyncFn, arity) {
    if ( arity === void 0 ) arity = asyncFn.length;

    if (!arity) { throw new Error('arity is undefined'); }
    function awaitable() {
        var this$1$1 = this;
        var args = [], len = arguments.length;
        while ( len-- ) args[ len ] = arguments[ len ];

        if (typeof args[arity - 1] === 'function') {
            return asyncFn.apply(this, args);
        }

        return new Promise(function (resolve, reject) {
            args[arity - 1] = function (err) {
                var cbArgs = [], len = arguments.length - 1;
                while ( len-- > 0 ) cbArgs[ len ] = arguments[ len + 1 ];

                if (err) { return reject(err); }
                resolve(cbArgs.length > 1 ? cbArgs : cbArgs[0]);
            };
            asyncFn.apply(this$1$1, args);
        });
    }

    return awaitable;
}
module.exports = exports['default'];
}(awaitify, awaitify.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _isArrayLike = isArrayLike.exports;

var _isArrayLike2 = _interopRequireDefault(_isArrayLike);

var _wrapAsync = wrapAsync$1;

var _wrapAsync2 = _interopRequireDefault(_wrapAsync);

var _awaitify = awaitify.exports;

var _awaitify2 = _interopRequireDefault(_awaitify);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

exports.default = (0, _awaitify2.default)(function (eachfn, tasks, callback) {
    var results = (0, _isArrayLike2.default)(tasks) ? [] : {};

    eachfn(tasks, function (task, key, taskCb) {
        (0, _wrapAsync2.default)(task)(function (err) {
            var assign;

            var result = [], len = arguments.length - 1;
            while ( len-- > 0 ) result[ len ] = arguments[ len + 1 ];
            if (result.length < 2) {
                (assign = result, result = assign[0]);
            }
            results[key] = result;
            taskCb(err);
        });
    }, function (err) { return callback(err, results); });
}, 3);
module.exports = exports['default'];
}(parallel$2, parallel$2.exports));

var eachOfSeries = {exports: {}};

var eachOfLimit$1 = {exports: {}};

var eachOfLimit = {exports: {}};

var once = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = once;
function once(fn) {
    function wrapper() {
        var args = [], len = arguments.length;
        while ( len-- ) args[ len ] = arguments[ len ];

        if (fn === null) { return; }
        var callFn = fn;
        fn = null;
        callFn.apply(this, args);
    }
    Object.assign(wrapper, fn);
    return wrapper;
}
module.exports = exports["default"];
}(once, once.exports));

var iterator = {exports: {}};

var getIterator = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});

exports.default = function (coll) {
    return coll[Symbol.iterator] && coll[Symbol.iterator]();
};

module.exports = exports["default"];
}(getIterator, getIterator.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = createIterator;

var _isArrayLike = isArrayLike.exports;

var _isArrayLike2 = _interopRequireDefault(_isArrayLike);

var _getIterator = getIterator.exports;

var _getIterator2 = _interopRequireDefault(_getIterator);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function createArrayIterator(coll) {
    var i = -1;
    var len = coll.length;
    return function next() {
        return ++i < len ? { value: coll[i], key: i } : null;
    };
}

function createES2015Iterator(iterator) {
    var i = -1;
    return function next() {
        var item = iterator.next();
        if (item.done) { return null; }
        i++;
        return { value: item.value, key: i };
    };
}

function createObjectIterator(obj) {
    var okeys = obj ? Object.keys(obj) : [];
    var i = -1;
    var len = okeys.length;
    return function next() {
        var key = okeys[++i];
        if (key === '__proto__') {
            return next();
        }
        return i < len ? { value: obj[key], key: key } : null;
    };
}

function createIterator(coll) {
    if ((0, _isArrayLike2.default)(coll)) {
        return createArrayIterator(coll);
    }

    var iterator = (0, _getIterator2.default)(coll);
    return iterator ? createES2015Iterator(iterator) : createObjectIterator(coll);
}
module.exports = exports['default'];
}(iterator, iterator.exports));

var onlyOnce = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = onlyOnce;
function onlyOnce(fn) {
    return function () {
        var args = [], len = arguments.length;
        while ( len-- ) args[ len ] = arguments[ len ];

        if (fn === null) { throw new Error("Callback was already called."); }
        var callFn = fn;
        fn = null;
        callFn.apply(this, args);
    };
}
module.exports = exports["default"];
}(onlyOnce, onlyOnce.exports));

var asyncEachOfLimit = {exports: {}};

var breakLoop = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
// A temporary value used to identify if the loop should be broken.
// See #1064, #1293
var breakLoop = {};
exports.default = breakLoop;
module.exports = exports["default"];
}(breakLoop, breakLoop.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = asyncEachOfLimit;

var _breakLoop = breakLoop.exports;

var _breakLoop2 = _interopRequireDefault(_breakLoop);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

// for async generators
function asyncEachOfLimit(generator, limit, iteratee, callback) {
    var done = false;
    var canceled = false;
    var awaiting = false;
    var running = 0;
    var idx = 0;

    function replenish() {
        //console.log('replenish')
        if (running >= limit || awaiting || done) { return; }
        //console.log('replenish awaiting')
        awaiting = true;
        generator.next().then(function (ref) {
            var value = ref.value;
            var iterDone = ref.done;

            //console.log('got value', value)
            if (canceled || done) { return; }
            awaiting = false;
            if (iterDone) {
                done = true;
                if (running <= 0) {
                    //console.log('done nextCb')
                    callback(null);
                }
                return;
            }
            running++;
            iteratee(value, idx, iterateeCallback);
            idx++;
            replenish();
        }).catch(handleError);
    }

    function iterateeCallback(err, result) {
        //console.log('iterateeCallback')
        running -= 1;
        if (canceled) { return; }
        if (err) { return handleError(err); }

        if (err === false) {
            done = true;
            canceled = true;
            return;
        }

        if (result === _breakLoop2.default || done && running <= 0) {
            done = true;
            //console.log('done iterCb')
            return callback(null);
        }
        replenish();
    }

    function handleError(err) {
        if (canceled) { return; }
        awaiting = false;
        done = true;
        callback(err);
    }

    replenish();
}
module.exports = exports['default'];
}(asyncEachOfLimit, asyncEachOfLimit.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _once = once.exports;

var _once2 = _interopRequireDefault(_once);

var _iterator = iterator.exports;

var _iterator2 = _interopRequireDefault(_iterator);

var _onlyOnce = onlyOnce.exports;

var _onlyOnce2 = _interopRequireDefault(_onlyOnce);

var _wrapAsync = wrapAsync$1;

var _asyncEachOfLimit = asyncEachOfLimit.exports;

var _asyncEachOfLimit2 = _interopRequireDefault(_asyncEachOfLimit);

var _breakLoop = breakLoop.exports;

var _breakLoop2 = _interopRequireDefault(_breakLoop);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

exports.default = function (limit) {
    return function (obj, iteratee, callback) {
        callback = (0, _once2.default)(callback);
        if (limit <= 0) {
            throw new RangeError('concurrency limit cannot be less than 1');
        }
        if (!obj) {
            return callback(null);
        }
        if ((0, _wrapAsync.isAsyncGenerator)(obj)) {
            return (0, _asyncEachOfLimit2.default)(obj, limit, iteratee, callback);
        }
        if ((0, _wrapAsync.isAsyncIterable)(obj)) {
            return (0, _asyncEachOfLimit2.default)(obj[Symbol.asyncIterator](), limit, iteratee, callback);
        }
        var nextElem = (0, _iterator2.default)(obj);
        var done = false;
        var canceled = false;
        var running = 0;
        var looping = false;

        function iterateeCallback(err, value) {
            if (canceled) { return; }
            running -= 1;
            if (err) {
                done = true;
                callback(err);
            } else if (err === false) {
                done = true;
                canceled = true;
            } else if (value === _breakLoop2.default || done && running <= 0) {
                done = true;
                return callback(null);
            } else if (!looping) {
                replenish();
            }
        }

        function replenish() {
            looping = true;
            while (running < limit && !done) {
                var elem = nextElem();
                if (elem === null) {
                    done = true;
                    if (running <= 0) {
                        callback(null);
                    }
                    return;
                }
                running += 1;
                iteratee(elem.value, elem.key, (0, _onlyOnce2.default)(iterateeCallback));
            }
            looping = false;
        }

        replenish();
    };
};

module.exports = exports['default'];
}(eachOfLimit, eachOfLimit.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
  value: true
});

var _eachOfLimit2 = eachOfLimit.exports;

var _eachOfLimit3 = _interopRequireDefault(_eachOfLimit2);

var _wrapAsync = wrapAsync$1;

var _wrapAsync2 = _interopRequireDefault(_wrapAsync);

var _awaitify = awaitify.exports;

var _awaitify2 = _interopRequireDefault(_awaitify);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * The same as [`eachOf`]{@link module:Collections.eachOf} but runs a maximum of `limit` async operations at a
 * time.
 *
 * @name eachOfLimit
 * @static
 * @memberOf module:Collections
 * @method
 * @see [async.eachOf]{@link module:Collections.eachOf}
 * @alias forEachOfLimit
 * @category Collection
 * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.
 * @param {number} limit - The maximum number of async operations at a time.
 * @param {AsyncFunction} iteratee - An async function to apply to each
 * item in `coll`. The `key` is the item's key, or index in the case of an
 * array.
 * Invoked with (item, key, callback).
 * @param {Function} [callback] - A callback which is called when all
 * `iteratee` functions have finished, or an error occurs. Invoked with (err).
 * @returns {Promise} a promise, if a callback is omitted
 */
function eachOfLimit$1(coll, limit, iteratee, callback) {
  return (0, _eachOfLimit3.default)(limit)(coll, (0, _wrapAsync2.default)(iteratee), callback);
}

exports.default = (0, _awaitify2.default)(eachOfLimit$1, 4);
module.exports = exports['default'];
}(eachOfLimit$1, eachOfLimit$1.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
  value: true
});

var _eachOfLimit = eachOfLimit$1.exports;

var _eachOfLimit2 = _interopRequireDefault(_eachOfLimit);

var _awaitify = awaitify.exports;

var _awaitify2 = _interopRequireDefault(_awaitify);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * The same as [`eachOf`]{@link module:Collections.eachOf} but runs only a single async operation at a time.
 *
 * @name eachOfSeries
 * @static
 * @memberOf module:Collections
 * @method
 * @see [async.eachOf]{@link module:Collections.eachOf}
 * @alias forEachOfSeries
 * @category Collection
 * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.
 * @param {AsyncFunction} iteratee - An async function to apply to each item in
 * `coll`.
 * Invoked with (item, key, callback).
 * @param {Function} [callback] - A callback which is called when all `iteratee`
 * functions have finished, or an error occurs. Invoked with (err).
 * @returns {Promise} a promise, if a callback is omitted
 */
function eachOfSeries(coll, iteratee, callback) {
  return (0, _eachOfLimit2.default)(coll, 1, iteratee, callback);
}
exports.default = (0, _awaitify2.default)(eachOfSeries, 3);
module.exports = exports['default'];
}(eachOfSeries, eachOfSeries.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = series;

var _parallel2 = parallel$2.exports;

var _parallel3 = _interopRequireDefault(_parallel2);

var _eachOfSeries = eachOfSeries.exports;

var _eachOfSeries2 = _interopRequireDefault(_eachOfSeries);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Run the functions in the `tasks` collection in series, each one running once
 * the previous function has completed. If any functions in the series pass an
 * error to its callback, no more functions are run, and `callback` is
 * immediately called with the value of the error. Otherwise, `callback`
 * receives an array of results when `tasks` have completed.
 *
 * It is also possible to use an object instead of an array. Each property will
 * be run as a function, and the results will be passed to the final `callback`
 * as an object instead of an array. This can be a more readable way of handling
 *  results from {@link async.series}.
 *
 * **Note** that while many implementations preserve the order of object
 * properties, the [ECMAScript Language Specification](http://www.ecma-international.org/ecma-262/5.1/#sec-8.6)
 * explicitly states that
 *
 * > The mechanics and order of enumerating the properties is not specified.
 *
 * So if you rely on the order in which your series of functions are executed,
 * and want this to work on all platforms, consider using an array.
 *
 * @name series
 * @static
 * @memberOf module:ControlFlow
 * @method
 * @category Control Flow
 * @param {Array|Iterable|AsyncIterable|Object} tasks - A collection containing
 * [async functions]{@link AsyncFunction} to run in series.
 * Each function can complete with any number of optional `result` values.
 * @param {Function} [callback] - An optional callback to run once all the
 * functions have completed. This function gets a results array (or object)
 * containing all the result arguments passed to the `task` callbacks. Invoked
 * with (err, result).
 * @return {Promise} a promise, if no callback is passed
 * @example
 *
 * //Using Callbacks
 * async.series([
 *     function(callback) {
 *         setTimeout(function() {
 *             // do some async task
 *             callback(null, 'one');
 *         }, 200);
 *     },
 *     function(callback) {
 *         setTimeout(function() {
 *             // then do another async task
 *             callback(null, 'two');
 *         }, 100);
 *     }
 * ], function(err, results) {
 *     console.log(results);
 *     // results is equal to ['one','two']
 * });
 *
 * // an example using objects instead of arrays
 * async.series({
 *     one: function(callback) {
 *         setTimeout(function() {
 *             // do some async task
 *             callback(null, 1);
 *         }, 200);
 *     },
 *     two: function(callback) {
 *         setTimeout(function() {
 *             // then do another async task
 *             callback(null, 2);
 *         }, 100);
 *     }
 * }, function(err, results) {
 *     console.log(results);
 *     // results is equal to: { one: 1, two: 2 }
 * });
 *
 * //Using Promises
 * async.series([
 *     function(callback) {
 *         setTimeout(function() {
 *             callback(null, 'one');
 *         }, 200);
 *     },
 *     function(callback) {
 *         setTimeout(function() {
 *             callback(null, 'two');
 *         }, 100);
 *     }
 * ]).then(results => {
 *     console.log(results);
 *     // results is equal to ['one','two']
 * }).catch(err => {
 *     console.log(err);
 * });
 *
 * // an example using an object instead of an array
 * async.series({
 *     one: function(callback) {
 *         setTimeout(function() {
 *             // do some async task
 *             callback(null, 1);
 *         }, 200);
 *     },
 *     two: function(callback) {
 *         setTimeout(function() {
 *             // then do another async task
 *             callback(null, 2);
 *         }, 100);
 *     }
 * }).then(results => {
 *     console.log(results);
 *     // results is equal to: { one: 1, two: 2 }
 * }).catch(err => {
 *     console.log(err);
 * });
 *
 * //Using async/await
 * async () => {
 *     try {
 *         let results = await async.series([
 *             function(callback) {
 *                 setTimeout(function() {
 *                     // do some async task
 *                     callback(null, 'one');
 *                 }, 200);
 *             },
 *             function(callback) {
 *                 setTimeout(function() {
 *                     // then do another async task
 *                     callback(null, 'two');
 *                 }, 100);
 *             }
 *         ]);
 *         console.log(results);
 *         // results is equal to ['one','two']
 *     }
 *     catch (err) {
 *         console.log(err);
 *     }
 * }
 *
 * // an example using an object instead of an array
 * async () => {
 *     try {
 *         let results = await async.parallel({
 *             one: function(callback) {
 *                 setTimeout(function() {
 *                     // do some async task
 *                     callback(null, 1);
 *                 }, 200);
 *             },
 *            two: function(callback) {
 *                 setTimeout(function() {
 *                     // then do another async task
 *                     callback(null, 2);
 *                 }, 100);
 *            }
 *         });
 *         console.log(results);
 *         // results is equal to: { one: 1, two: 2 }
 *     }
 *     catch (err) {
 *         console.log(err);
 *     }
 * }
 *
 */
function series(tasks, callback) {
  return (0, _parallel3.default)(_eachOfSeries2.default, tasks, callback);
}
module.exports = exports['default'];
}(series$1, series$1.exports));

var series = /*@__PURE__*/getDefaultExportFromCjs(series$1.exports);

var SyncHook = (function () {
    function SyncHook() {
        this.args = [];
        this.tasks = [];
    }
    SyncHook.prototype.call = function () {
        var arguments$1 = arguments;

        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments$1[_i];
        }
        this.args = args;
        return series(this.tasks);
    };
    SyncHook.prototype.tap = function (name, cb) {
        var _this = this;
        this.tasks.push(function (callback) {
            cb.apply(void 0, __spreadArray([], __read(_this.args), false));
            callback(null, name);
        });
    };
    return SyncHook;
}());

var parallel$1 = {exports: {}};

var eachOf = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _isArrayLike = isArrayLike.exports;

var _isArrayLike2 = _interopRequireDefault(_isArrayLike);

var _breakLoop = breakLoop.exports;

var _breakLoop2 = _interopRequireDefault(_breakLoop);

var _eachOfLimit = eachOfLimit$1.exports;

var _eachOfLimit2 = _interopRequireDefault(_eachOfLimit);

var _once = once.exports;

var _once2 = _interopRequireDefault(_once);

var _onlyOnce = onlyOnce.exports;

var _onlyOnce2 = _interopRequireDefault(_onlyOnce);

var _wrapAsync = wrapAsync$1;

var _wrapAsync2 = _interopRequireDefault(_wrapAsync);

var _awaitify = awaitify.exports;

var _awaitify2 = _interopRequireDefault(_awaitify);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

// eachOf implementation optimized for array-likes
function eachOfArrayLike(coll, iteratee, callback) {
    callback = (0, _once2.default)(callback);
    var index = 0,
        completed = 0;
    var length = coll.length;
    var canceled = false;
    if (length === 0) {
        callback(null);
    }

    function iteratorCallback(err, value) {
        if (err === false) {
            canceled = true;
        }
        if (canceled === true) { return; }
        if (err) {
            callback(err);
        } else if (++completed === length || value === _breakLoop2.default) {
            callback(null);
        }
    }

    for (; index < length; index++) {
        iteratee(coll[index], index, (0, _onlyOnce2.default)(iteratorCallback));
    }
}

// a generic version of eachOf which can handle array, object, and iterator cases.
function eachOfGeneric(coll, iteratee, callback) {
    return (0, _eachOfLimit2.default)(coll, Infinity, iteratee, callback);
}

/**
 * Like [`each`]{@link module:Collections.each}, except that it passes the key (or index) as the second argument
 * to the iteratee.
 *
 * @name eachOf
 * @static
 * @memberOf module:Collections
 * @method
 * @alias forEachOf
 * @category Collection
 * @see [async.each]{@link module:Collections.each}
 * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.
 * @param {AsyncFunction} iteratee - A function to apply to each
 * item in `coll`.
 * The `key` is the item's key, or index in the case of an array.
 * Invoked with (item, key, callback).
 * @param {Function} [callback] - A callback which is called when all
 * `iteratee` functions have finished, or an error occurs. Invoked with (err).
 * @returns {Promise} a promise, if a callback is omitted
 * @example
 *
 * // dev.json is a file containing a valid json object config for dev environment
 * // dev.json is a file containing a valid json object config for test environment
 * // prod.json is a file containing a valid json object config for prod environment
 * // invalid.json is a file with a malformed json object
 *
 * let configs = {}; //global variable
 * let validConfigFileMap = {dev: 'dev.json', test: 'test.json', prod: 'prod.json'};
 * let invalidConfigFileMap = {dev: 'dev.json', test: 'test.json', invalid: 'invalid.json'};
 *
 * // asynchronous function that reads a json file and parses the contents as json object
 * function parseFile(file, key, callback) {
 *     fs.readFile(file, "utf8", function(err, data) {
 *         if (err) return calback(err);
 *         try {
 *             configs[key] = JSON.parse(data);
 *         } catch (e) {
 *             return callback(e);
 *         }
 *         callback();
 *     });
 * }
 *
 * // Using callbacks
 * async.forEachOf(validConfigFileMap, parseFile, function (err) {
 *     if (err) {
 *         console.error(err);
 *     } else {
 *         console.log(configs);
 *         // configs is now a map of JSON data, e.g.
 *         // { dev: //parsed dev.json, test: //parsed test.json, prod: //parsed prod.json}
 *     }
 * });
 *
 * //Error handing
 * async.forEachOf(invalidConfigFileMap, parseFile, function (err) {
 *     if (err) {
 *         console.error(err);
 *         // JSON parse error exception
 *     } else {
 *         console.log(configs);
 *     }
 * });
 *
 * // Using Promises
 * async.forEachOf(validConfigFileMap, parseFile)
 * .then( () => {
 *     console.log(configs);
 *     // configs is now a map of JSON data, e.g.
 *     // { dev: //parsed dev.json, test: //parsed test.json, prod: //parsed prod.json}
 * }).catch( err => {
 *     console.error(err);
 * });
 *
 * //Error handing
 * async.forEachOf(invalidConfigFileMap, parseFile)
 * .then( () => {
 *     console.log(configs);
 * }).catch( err => {
 *     console.error(err);
 *     // JSON parse error exception
 * });
 *
 * // Using async/await
 * async () => {
 *     try {
 *         let result = await async.forEachOf(validConfigFileMap, parseFile);
 *         console.log(configs);
 *         // configs is now a map of JSON data, e.g.
 *         // { dev: //parsed dev.json, test: //parsed test.json, prod: //parsed prod.json}
 *     }
 *     catch (err) {
 *         console.log(err);
 *     }
 * }
 *
 * //Error handing
 * async () => {
 *     try {
 *         let result = await async.forEachOf(invalidConfigFileMap, parseFile);
 *         console.log(configs);
 *     }
 *     catch (err) {
 *         console.log(err);
 *         // JSON parse error exception
 *     }
 * }
 *
 */
function eachOf(coll, iteratee, callback) {
    var eachOfImplementation = (0, _isArrayLike2.default)(coll) ? eachOfArrayLike : eachOfGeneric;
    return eachOfImplementation(coll, (0, _wrapAsync2.default)(iteratee), callback);
}

exports.default = (0, _awaitify2.default)(eachOf, 3);
module.exports = exports['default'];
}(eachOf, eachOf.exports));

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = parallel;

var _eachOf = eachOf.exports;

var _eachOf2 = _interopRequireDefault(_eachOf);

var _parallel2 = parallel$2.exports;

var _parallel3 = _interopRequireDefault(_parallel2);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Run the `tasks` collection of functions in parallel, without waiting until
 * the previous function has completed. If any of the functions pass an error to
 * its callback, the main `callback` is immediately called with the value of the
 * error. Once the `tasks` have completed, the results are passed to the final
 * `callback` as an array.
 *
 * **Note:** `parallel` is about kicking-off I/O tasks in parallel, not about
 * parallel execution of code.  If your tasks do not use any timers or perform
 * any I/O, they will actually be executed in series.  Any synchronous setup
 * sections for each task will happen one after the other.  JavaScript remains
 * single-threaded.
 *
 * **Hint:** Use [`reflect`]{@link module:Utils.reflect} to continue the
 * execution of other tasks when a task fails.
 *
 * It is also possible to use an object instead of an array. Each property will
 * be run as a function and the results will be passed to the final `callback`
 * as an object instead of an array. This can be a more readable way of handling
 * results from {@link async.parallel}.
 *
 * @name parallel
 * @static
 * @memberOf module:ControlFlow
 * @method
 * @category Control Flow
 * @param {Array|Iterable|AsyncIterable|Object} tasks - A collection of
 * [async functions]{@link AsyncFunction} to run.
 * Each async function can complete with any number of optional `result` values.
 * @param {Function} [callback] - An optional callback to run once all the
 * functions have completed successfully. This function gets a results array
 * (or object) containing all the result arguments passed to the task callbacks.
 * Invoked with (err, results).
 * @returns {Promise} a promise, if a callback is not passed
 *
 * @example
 *
 * //Using Callbacks
 * async.parallel([
 *     function(callback) {
 *         setTimeout(function() {
 *             callback(null, 'one');
 *         }, 200);
 *     },
 *     function(callback) {
 *         setTimeout(function() {
 *             callback(null, 'two');
 *         }, 100);
 *     }
 * ], function(err, results) {
 *     console.log(results);
 *     // results is equal to ['one','two'] even though
 *     // the second function had a shorter timeout.
 * });
 *
 * // an example using an object instead of an array
 * async.parallel({
 *     one: function(callback) {
 *         setTimeout(function() {
 *             callback(null, 1);
 *         }, 200);
 *     },
 *     two: function(callback) {
 *         setTimeout(function() {
 *             callback(null, 2);
 *         }, 100);
 *     }
 * }, function(err, results) {
 *     console.log(results);
 *     // results is equal to: { one: 1, two: 2 }
 * });
 *
 * //Using Promises
 * async.parallel([
 *     function(callback) {
 *         setTimeout(function() {
 *             callback(null, 'one');
 *         }, 200);
 *     },
 *     function(callback) {
 *         setTimeout(function() {
 *             callback(null, 'two');
 *         }, 100);
 *     }
 * ]).then(results => {
 *     console.log(results);
 *     // results is equal to ['one','two'] even though
 *     // the second function had a shorter timeout.
 * }).catch(err => {
 *     console.log(err);
 * });
 *
 * // an example using an object instead of an array
 * async.parallel({
 *     one: function(callback) {
 *         setTimeout(function() {
 *             callback(null, 1);
 *         }, 200);
 *     },
 *     two: function(callback) {
 *         setTimeout(function() {
 *             callback(null, 2);
 *         }, 100);
 *     }
 * }).then(results => {
 *     console.log(results);
 *     // results is equal to: { one: 1, two: 2 }
 * }).catch(err => {
 *     console.log(err);
 * });
 *
 * //Using async/await
 * async () => {
 *     try {
 *         let results = await async.parallel([
 *             function(callback) {
 *                 setTimeout(function() {
 *                     callback(null, 'one');
 *                 }, 200);
 *             },
 *             function(callback) {
 *                 setTimeout(function() {
 *                     callback(null, 'two');
 *                 }, 100);
 *             }
 *         ]);
 *         console.log(results);
 *         // results is equal to ['one','two'] even though
 *         // the second function had a shorter timeout.
 *     }
 *     catch (err) {
 *         console.log(err);
 *     }
 * }
 *
 * // an example using an object instead of an array
 * async () => {
 *     try {
 *         let results = await async.parallel({
 *             one: function(callback) {
 *                 setTimeout(function() {
 *                     callback(null, 1);
 *                 }, 200);
 *             },
 *            two: function(callback) {
 *                 setTimeout(function() {
 *                     callback(null, 2);
 *                 }, 100);
 *            }
 *         });
 *         console.log(results);
 *         // results is equal to: { one: 1, two: 2 }
 *     }
 *     catch (err) {
 *         console.log(err);
 *     }
 * }
 *
 */
function parallel(tasks, callback) {
  return (0, _parallel3.default)(_eachOf2.default, tasks, callback);
}
module.exports = exports['default'];
}(parallel$1, parallel$1.exports));

var parallel = /*@__PURE__*/getDefaultExportFromCjs(parallel$1.exports);

var AsyncParallelHook = (function () {
    function AsyncParallelHook() {
        this.tasks = [];
    }
    AsyncParallelHook.prototype.promise = function () {
        return parallel(this.tasks);
    };
    AsyncParallelHook.prototype.tapPromise = function (name, cb) {
        var _this = this;
        this.tasks.push(function (callback) { return __awaiter(_this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, cb()];
                    case 1:
                        _a.sent();
                        callback(null, name);
                        return [2];
                }
            });
        }); });
    };
    return AsyncParallelHook;
}());

var waterfall$1 = {exports: {}};

(function (module, exports) {

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _once = once.exports;

var _once2 = _interopRequireDefault(_once);

var _onlyOnce = onlyOnce.exports;

var _onlyOnce2 = _interopRequireDefault(_onlyOnce);

var _wrapAsync = wrapAsync$1;

var _wrapAsync2 = _interopRequireDefault(_wrapAsync);

var _awaitify = awaitify.exports;

var _awaitify2 = _interopRequireDefault(_awaitify);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Runs the `tasks` array of functions in series, each passing their results to
 * the next in the array. However, if any of the `tasks` pass an error to their
 * own callback, the next function is not executed, and the main `callback` is
 * immediately called with the error.
 *
 * @name waterfall
 * @static
 * @memberOf module:ControlFlow
 * @method
 * @category Control Flow
 * @param {Array} tasks - An array of [async functions]{@link AsyncFunction}
 * to run.
 * Each function should complete with any number of `result` values.
 * The `result` values will be passed as arguments, in order, to the next task.
 * @param {Function} [callback] - An optional callback to run once all the
 * functions have completed. This will be passed the results of the last task's
 * callback. Invoked with (err, [results]).
 * @returns {Promise} a promise, if a callback is omitted
 * @example
 *
 * async.waterfall([
 *     function(callback) {
 *         callback(null, 'one', 'two');
 *     },
 *     function(arg1, arg2, callback) {
 *         // arg1 now equals 'one' and arg2 now equals 'two'
 *         callback(null, 'three');
 *     },
 *     function(arg1, callback) {
 *         // arg1 now equals 'three'
 *         callback(null, 'done');
 *     }
 * ], function (err, result) {
 *     // result now equals 'done'
 * });
 *
 * // Or, with named functions:
 * async.waterfall([
 *     myFirstFunction,
 *     mySecondFunction,
 *     myLastFunction,
 * ], function (err, result) {
 *     // result now equals 'done'
 * });
 * function myFirstFunction(callback) {
 *     callback(null, 'one', 'two');
 * }
 * function mySecondFunction(arg1, arg2, callback) {
 *     // arg1 now equals 'one' and arg2 now equals 'two'
 *     callback(null, 'three');
 * }
 * function myLastFunction(arg1, callback) {
 *     // arg1 now equals 'three'
 *     callback(null, 'done');
 * }
 */
function waterfall(tasks, callback) {
    callback = (0, _once2.default)(callback);
    if (!Array.isArray(tasks)) { return callback(new Error('First argument to waterfall must be an array of functions')); }
    if (!tasks.length) { return callback(); }
    var taskIndex = 0;

    function nextTask(args) {
        var task = (0, _wrapAsync2.default)(tasks[taskIndex++]);
        task.apply(void 0, args.concat( [(0, _onlyOnce2.default)(next)] ));
    }

    function next(err) {
        var args = [], len = arguments.length - 1;
        while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];

        if (err === false) { return; }
        if (err || taskIndex === tasks.length) {
            return callback.apply(void 0, [ err ].concat( args ));
        }
        nextTask(args);
    }

    nextTask([]);
}

exports.default = (0, _awaitify2.default)(waterfall);
module.exports = exports['default'];
}(waterfall$1, waterfall$1.exports));

var waterfall = /*@__PURE__*/getDefaultExportFromCjs(waterfall$1.exports);

var ASyncWaterfallHook = (function () {
    function ASyncWaterfallHook() {
        this.tasks = [];
    }
    ASyncWaterfallHook.prototype.call = function () {
        return waterfall(this.tasks);
    };
    ASyncWaterfallHook.prototype.tap = function (name, cb) {
        if (this.tasks.length === 0) {
            this.tasks.push(function (callback) {
                var value = cb();
                callback(value ? null : false, value);
            });
        }
        else {
            this.tasks.push(function (arg, callback) {
                var value = cb.apply(void 0, __spreadArray([], __read(arg), false));
                callback(value ? null : false, name);
            });
        }
    };
    return ASyncWaterfallHook;
}());

var SyncBailHook = (function () {
    function SyncBailHook() {
        this.tasks = [];
    }
    SyncBailHook.prototype.call = function () {
        return series(this.tasks);
    };
    SyncBailHook.prototype.tap = function (name, cb) {
        this.tasks.push(function (callback) {
            var err = cb();
            callback(err, name);
        });
    };
    return SyncBailHook;
}());

var AsyncSeriesHook = (function () {
    function AsyncSeriesHook() {
        this.args = [];
        this.tasks = [];
    }
    AsyncSeriesHook.prototype.promise = function () {
        var arguments$1 = arguments;

        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments$1[_i];
        }
        this.args = args;
        return series(this.tasks);
    };
    AsyncSeriesHook.prototype.tapPromise = function (name, cb) {
        var _this = this;
        this.tasks.push(function (callback) { return __awaiter(_this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, cb.apply(void 0, __spreadArray([], __read(this.args), false))];
                    case 1:
                        _a.sent();
                        callback(null, name);
                        return [2];
                }
            });
        }); });
    };
    return AsyncSeriesHook;
}());

var AsyncSeriesBailHook = (function () {
    function AsyncSeriesBailHook() {
        this.args = [];
        this.tasks = [];
    }
    AsyncSeriesBailHook.prototype.promise = function () {
        var arguments$1 = arguments;

        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments$1[_i];
        }
        this.args = args;
        return series(this.tasks);
    };
    AsyncSeriesBailHook.prototype.tapPromise = function (name, cb) {
        var _this = this;
        this.tasks.push(function (callback) { return __awaiter(_this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, cb.apply(void 0, __spreadArray([], __read(this.args), false))];
                    case 1:
                        err = _a.sent();
                        callback(err, name);
                        return [2];
                }
            });
        }); });
    };
    return AsyncSeriesBailHook;
}());

var AsyncWaterfallHook = (function () {
    function AsyncWaterfallHook() {
        this.tasks = [];
    }
    AsyncWaterfallHook.prototype.promise = function () {
        return waterfall(this.tasks);
    };
    AsyncWaterfallHook.prototype.tapPromise = function (name, cb) {
        if (this.tasks.length === 0) {
            this.tasks.push(function (callback) {
                cb()
                    .then(function (value) {
                    callback(null, value);
                });
            });
        }
        else {
            this.tasks.push(function (arg, callback) {
                cb(arg).then(function (v) {
                    callback(null, v);
                });
            });
        }
    };
    return AsyncWaterfallHook;
}());

exports.AsyncParallelHook = AsyncParallelHook;
exports.AsyncSeriesBailHook = AsyncSeriesBailHook;
exports.AsyncSeriesHook = AsyncSeriesHook;
exports.AsyncWaterfallHook = AsyncWaterfallHook;
exports.SyncBailHook = SyncBailHook;
exports.SyncHook = SyncHook;
exports.SyncWaterfallHook = ASyncWaterfallHook;
//# sourceMappingURL=index.js.map
