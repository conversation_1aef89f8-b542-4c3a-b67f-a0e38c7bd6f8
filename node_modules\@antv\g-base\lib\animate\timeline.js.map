{"version": 3, "file": "timeline.js", "sourceRoot": "", "sources": ["../../src/animate/timeline.ts"], "names": [], "mappings": ";;AAAA,mCAA2D;AAC3D,kCAAoC;AACpC,iDAA+D,CAAC,sBAAsB;AACtF,uCAAuC;AACvC,uCAAyC;AACzC,uCAA6D;AAI7D,IAAM,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAEpD;;;;;;GAMG;AACH,SAAS,OAAO,CAAC,KAAe,EAAE,SAAoB,EAAE,KAAa;IACnE,IAAM,MAAM,GAAG,EAAE,CAAC,CAAC,OAAO;IAClB,IAAA,SAAS,GAAc,SAAS,UAAvB,EAAE,OAAO,GAAK,SAAS,QAAd,CAAe;IACzC,IAAI,KAAK,CAAC,SAAS,EAAE;QACnB,OAAO;KACR;IACD,IAAI,MAAM,CAAC,CAAC,QAAQ;IACpB,KAAK,IAAM,CAAC,IAAI,OAAO,EAAE;QACvB,IAAI,CAAC,cAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YACtC,IAAI,CAAC,KAAK,MAAM,EAAE;gBAChB,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACxB,IAAI,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE;oBACnC,MAAM,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;oBACtD,QAAQ,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;oBAC1D,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBACrD,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBACjD,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC;oBACpC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;iBACjC;qBAAM,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;oBACnC,MAAM,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,QAAQ,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClD,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBACjD,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC;oBACpC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;oBAChC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC;iBAChC;gBACD,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACtC,IAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC9B,IAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAM,UAAU,GAAG,EAAE,CAAC;oBACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC3C,IAAI,eAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,IAAI,eAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC3E,MAAM,GAAG,4BAAW,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;4BACvD,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;yBAChC;6BAAM;4BACL,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;yBACjC;qBACF;oBACD,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC5B;aACF;iBAAM,IAAI,CAAC,KAAK,QAAQ,EAAE;gBACzB;;;mBAGG;gBACH,IAAM,QAAQ,GAAG,iCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC;gBAClG,IAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC;aAC3B;iBAAM,IAAI,mBAAW,CAAC,CAAC,CAAC,IAAI,uBAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxD,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;aACxB;iBAAM,IAAI,CAAC,iBAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;gBAClC,eAAe;gBACf,MAAM,GAAG,4BAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;aAC3B;SACF;KACF;IACD,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,MAAM,CAAC,KAAe,EAAE,SAAoB,EAAE,OAAe;IAC5D,IAAA,SAAS,GAAY,SAAS,UAArB,EAAE,KAAK,GAAK,SAAS,MAAd,CAAe;IACvC,oBAAoB;IACpB,IAAI,OAAO,GAAG,SAAS,GAAG,KAAK,IAAI,SAAS,CAAC,OAAO,EAAE;QACpD,OAAO,KAAK,CAAC;KACd;IACD,IAAI,KAAK,CAAC;IACV,IAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;IACpC,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;IAChC,IAAM,MAAM,GAAG,oBAAS,CAAC,MAAM,CAAC,CAAC;IAEjC,QAAQ;IACR,OAAO,GAAG,OAAO,GAAG,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;IAChD,IAAI,SAAS,CAAC,MAAM,EAAE;QACpB,8CAA8C;QAC9C,KAAK,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;QACxC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACL,KAAK,GAAG,OAAO,GAAG,QAAQ,CAAC;QAC3B,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,SAAS;YACT,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;SACvB;aAAM;YACL,SAAS;YACT,IAAI,SAAS,CAAC,OAAO,EAAE;gBACrB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aAClC;iBAAM;gBACL,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;aAC/B;YACD,OAAO,IAAI,CAAC;SACb;KACF;IACD,IAAI,SAAS,CAAC,OAAO,EAAE;QACrB,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnB;SAAM;QACL,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;KAClC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;IAsBE;;;OAGG;IACH,kBAAY,MAAe;QApB3B;;;WAGG;QACH,cAAS,GAAe,EAAE,CAAC;QAC3B;;;WAGG;QACH,YAAO,GAAW,CAAC,CAAC;QACpB;;;WAGG;QACH,UAAK,GAAkB,IAAI,CAAC;QAO1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,4BAAS,GAAT;QAAA,iBAwCC;QAvCC,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,KAAe,CAAC;QACpB,IAAI,UAAuB,CAAC;QAC5B,IAAI,SAAoB,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,UAAC,OAAO;YACjC,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,KAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7B,KAAK,IAAI,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBACnD,KAAK,GAAG,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC1B,IAAI,KAAK,CAAC,SAAS,EAAE;wBACnB,iBAAiB;wBACjB,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;wBACvB,SAAS;qBACV;oBACD,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE;wBAC5B,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;wBACrC,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;4BAC/C,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;4BAC1B,UAAU,GAAG,MAAM,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;4BAC/C,IAAI,UAAU,EAAE;gCACd,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gCACxB,UAAU,GAAG,KAAK,CAAC;gCACnB,IAAI,SAAS,CAAC,QAAQ,EAAE;oCACtB,SAAS,CAAC,QAAQ,EAAE,CAAC;iCACtB;6BACF;yBACF;qBACF;oBACD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC3B,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;qBACxB;iBACF;gBACD,IAAM,QAAQ,GAAG,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC7C,mCAAmC;gBACnC,IAAI,CAAC,QAAQ,EAAE;oBACb,KAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;iBACpB;aACF;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,8BAAW,GAAX,UAAY,KAAK;QACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,iCAAc,GAAd,UAAe,KAAK;QAClB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,8BAAW,GAAX;QACE,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,uBAAI,GAAJ;QACE,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;IACH,CAAC;IAED;;;OAGG;IACH,oCAAiB,GAAjB,UAAkB,KAAY;QAAZ,sBAAA,EAAA,YAAY;QAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,0BAAO,GAAP;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACH,eAAC;AAAD,CAAC,AA3HD,IA2HC;AAED,kBAAe,QAAQ,CAAC"}