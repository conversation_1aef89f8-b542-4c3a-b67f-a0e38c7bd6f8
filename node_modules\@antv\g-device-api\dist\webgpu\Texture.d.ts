/// <reference types="@webgpu/types" />
import { Format, Texture, TextureDescriptor, TextureDimension } from '../api';
import { ResourceType } from '../api';
import type { IDevice_WebGPU, TextureShared_WebGPU } from './interfaces';
import { ResourceBase_WebGPU } from './ResourceBase';
export declare class Texture_WebGPU extends ResourceBase_WebGPU implements TextureShared_WebGPU, Texture {
    type: ResourceType.Texture;
    format: Format;
    dimension: TextureDimension;
    gpuTextureformat: GPUTextureFormat;
    width: number;
    height: number;
    depthOrArrayLayers: number;
    mipLevelCount: number;
    sampleCount: number;
    usage: GPUTextureUsageFlags;
    gpuTexture: GPUTexture;
    gpuTextureView: GPUTextureView;
    private flipY;
    constructor({ id, device, descriptor, skipCreate, sampleCount, }: {
        id: number;
        device: IDevice_WebGPU;
        descriptor: TextureDescriptor;
        skipCreate?: boolean;
        sampleCount?: number;
    });
    private textureFromImageBitmapOrCanvas;
    private isImageBitmapOrCanvases;
    private isVideo;
    /**
     * @see https://toji.dev/webgpu-best-practices/img-textures
     */
    setImageData(datas: (TexImageSource | BufferSource)[], lod?: number): void;
    destroy(): void;
}
