!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).GDeviceAPI={})}(this,(function(e){"use strict";var t,r,n,o,a,i,s,l,_,u,E,c,R,T,p,A,d,F,f,m,h,S,g,B,N,C;function L(e){return e}function O(e,t,r){return e<<16|t<<8|r}function M(e){return e>>>8&255}function I(e){return e>>>16&255}function U(e){return 255&e}function D(t){switch(t){case e.FormatTypeFlags.F32:case e.FormatTypeFlags.U32:case e.FormatTypeFlags.S32:return 4;case e.FormatTypeFlags.U16:case e.FormatTypeFlags.S16:case e.FormatTypeFlags.F16:return 2;case e.FormatTypeFlags.U8:case e.FormatTypeFlags.S8:return 1;default:throw Error("whoops")}}function G(e){return D(I(e))}function P(e){return D(I(e))*M(e)}function v(t){var r=U(t);if(r&e.FormatFlags.Depth)return e.SamplerFormatKind.Depth;if(r&e.FormatFlags.Normalized)return e.SamplerFormatKind.Float;var n=I(t);if(n===e.FormatTypeFlags.F16||n===e.FormatTypeFlags.F32)return e.SamplerFormatKind.Float;if(n===e.FormatTypeFlags.U8||n===e.FormatTypeFlags.U16||n===e.FormatTypeFlags.U32)return e.SamplerFormatKind.Uint;if(n===e.FormatTypeFlags.S8||n===e.FormatTypeFlags.S16||n===e.FormatTypeFlags.S32)return e.SamplerFormatKind.Sint;throw Error("whoops")}function y(e,t){if(void 0===t&&(t=""),!e)throw Error("Assert fail: ".concat(t))}function b(e){if(null!=e)return e;throw Error("Missing object")}function x(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}function X(e,t){e.r=t.r,e.g=t.g,e.b=t.b,e.a=t.a}function w(e){return{r:e.r,g:e.g,b:e.b,a:e.a}}function W(e,t,r,n){return void 0===n&&(n=1),{r:e,g:t,b:r,a:n}}e.GL=void 0,(t=e.GL||(e.GL={}))[t.DEPTH_BUFFER_BIT=256]="DEPTH_BUFFER_BIT",t[t.STENCIL_BUFFER_BIT=1024]="STENCIL_BUFFER_BIT",t[t.COLOR_BUFFER_BIT=16384]="COLOR_BUFFER_BIT",t[t.POINTS=0]="POINTS",t[t.LINES=1]="LINES",t[t.LINE_LOOP=2]="LINE_LOOP",t[t.LINE_STRIP=3]="LINE_STRIP",t[t.TRIANGLES=4]="TRIANGLES",t[t.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",t[t.TRIANGLE_FAN=6]="TRIANGLE_FAN",t[t.ZERO=0]="ZERO",t[t.ONE=1]="ONE",t[t.SRC_COLOR=768]="SRC_COLOR",t[t.ONE_MINUS_SRC_COLOR=769]="ONE_MINUS_SRC_COLOR",t[t.SRC_ALPHA=770]="SRC_ALPHA",t[t.ONE_MINUS_SRC_ALPHA=771]="ONE_MINUS_SRC_ALPHA",t[t.DST_ALPHA=772]="DST_ALPHA",t[t.ONE_MINUS_DST_ALPHA=773]="ONE_MINUS_DST_ALPHA",t[t.DST_COLOR=774]="DST_COLOR",t[t.ONE_MINUS_DST_COLOR=775]="ONE_MINUS_DST_COLOR",t[t.SRC_ALPHA_SATURATE=776]="SRC_ALPHA_SATURATE",t[t.CONSTANT_COLOR=32769]="CONSTANT_COLOR",t[t.ONE_MINUS_CONSTANT_COLOR=32770]="ONE_MINUS_CONSTANT_COLOR",t[t.CONSTANT_ALPHA=32771]="CONSTANT_ALPHA",t[t.ONE_MINUS_CONSTANT_ALPHA=32772]="ONE_MINUS_CONSTANT_ALPHA",t[t.FUNC_ADD=32774]="FUNC_ADD",t[t.FUNC_SUBTRACT=32778]="FUNC_SUBTRACT",t[t.FUNC_REVERSE_SUBTRACT=32779]="FUNC_REVERSE_SUBTRACT",t[t.BLEND_EQUATION=32777]="BLEND_EQUATION",t[t.BLEND_EQUATION_RGB=32777]="BLEND_EQUATION_RGB",t[t.BLEND_EQUATION_ALPHA=34877]="BLEND_EQUATION_ALPHA",t[t.BLEND_DST_RGB=32968]="BLEND_DST_RGB",t[t.BLEND_SRC_RGB=32969]="BLEND_SRC_RGB",t[t.BLEND_DST_ALPHA=32970]="BLEND_DST_ALPHA",t[t.BLEND_SRC_ALPHA=32971]="BLEND_SRC_ALPHA",t[t.BLEND_COLOR=32773]="BLEND_COLOR",t[t.ARRAY_BUFFER_BINDING=34964]="ARRAY_BUFFER_BINDING",t[t.ELEMENT_ARRAY_BUFFER_BINDING=34965]="ELEMENT_ARRAY_BUFFER_BINDING",t[t.LINE_WIDTH=2849]="LINE_WIDTH",t[t.ALIASED_POINT_SIZE_RANGE=33901]="ALIASED_POINT_SIZE_RANGE",t[t.ALIASED_LINE_WIDTH_RANGE=33902]="ALIASED_LINE_WIDTH_RANGE",t[t.CULL_FACE_MODE=2885]="CULL_FACE_MODE",t[t.FRONT_FACE=2886]="FRONT_FACE",t[t.DEPTH_RANGE=2928]="DEPTH_RANGE",t[t.DEPTH_WRITEMASK=2930]="DEPTH_WRITEMASK",t[t.DEPTH_CLEAR_VALUE=2931]="DEPTH_CLEAR_VALUE",t[t.DEPTH_FUNC=2932]="DEPTH_FUNC",t[t.STENCIL_CLEAR_VALUE=2961]="STENCIL_CLEAR_VALUE",t[t.STENCIL_FUNC=2962]="STENCIL_FUNC",t[t.STENCIL_FAIL=2964]="STENCIL_FAIL",t[t.STENCIL_PASS_DEPTH_FAIL=2965]="STENCIL_PASS_DEPTH_FAIL",t[t.STENCIL_PASS_DEPTH_PASS=2966]="STENCIL_PASS_DEPTH_PASS",t[t.STENCIL_REF=2967]="STENCIL_REF",t[t.STENCIL_VALUE_MASK=2963]="STENCIL_VALUE_MASK",t[t.STENCIL_WRITEMASK=2968]="STENCIL_WRITEMASK",t[t.STENCIL_BACK_FUNC=34816]="STENCIL_BACK_FUNC",t[t.STENCIL_BACK_FAIL=34817]="STENCIL_BACK_FAIL",t[t.STENCIL_BACK_PASS_DEPTH_FAIL=34818]="STENCIL_BACK_PASS_DEPTH_FAIL",t[t.STENCIL_BACK_PASS_DEPTH_PASS=34819]="STENCIL_BACK_PASS_DEPTH_PASS",t[t.STENCIL_BACK_REF=36003]="STENCIL_BACK_REF",t[t.STENCIL_BACK_VALUE_MASK=36004]="STENCIL_BACK_VALUE_MASK",t[t.STENCIL_BACK_WRITEMASK=36005]="STENCIL_BACK_WRITEMASK",t[t.VIEWPORT=2978]="VIEWPORT",t[t.SCISSOR_BOX=3088]="SCISSOR_BOX",t[t.COLOR_CLEAR_VALUE=3106]="COLOR_CLEAR_VALUE",t[t.COLOR_WRITEMASK=3107]="COLOR_WRITEMASK",t[t.UNPACK_ALIGNMENT=3317]="UNPACK_ALIGNMENT",t[t.PACK_ALIGNMENT=3333]="PACK_ALIGNMENT",t[t.MAX_TEXTURE_SIZE=3379]="MAX_TEXTURE_SIZE",t[t.MAX_VIEWPORT_DIMS=3386]="MAX_VIEWPORT_DIMS",t[t.SUBPIXEL_BITS=3408]="SUBPIXEL_BITS",t[t.RED_BITS=3410]="RED_BITS",t[t.GREEN_BITS=3411]="GREEN_BITS",t[t.BLUE_BITS=3412]="BLUE_BITS",t[t.ALPHA_BITS=3413]="ALPHA_BITS",t[t.DEPTH_BITS=3414]="DEPTH_BITS",t[t.STENCIL_BITS=3415]="STENCIL_BITS",t[t.POLYGON_OFFSET_UNITS=10752]="POLYGON_OFFSET_UNITS",t[t.POLYGON_OFFSET_FACTOR=32824]="POLYGON_OFFSET_FACTOR",t[t.TEXTURE_BINDING_2D=32873]="TEXTURE_BINDING_2D",t[t.SAMPLE_BUFFERS=32936]="SAMPLE_BUFFERS",t[t.SAMPLES=32937]="SAMPLES",t[t.SAMPLE_COVERAGE_VALUE=32938]="SAMPLE_COVERAGE_VALUE",t[t.SAMPLE_COVERAGE_INVERT=32939]="SAMPLE_COVERAGE_INVERT",t[t.COMPRESSED_TEXTURE_FORMATS=34467]="COMPRESSED_TEXTURE_FORMATS",t[t.VENDOR=7936]="VENDOR",t[t.RENDERER=7937]="RENDERER",t[t.VERSION=7938]="VERSION",t[t.IMPLEMENTATION_COLOR_READ_TYPE=35738]="IMPLEMENTATION_COLOR_READ_TYPE",t[t.IMPLEMENTATION_COLOR_READ_FORMAT=35739]="IMPLEMENTATION_COLOR_READ_FORMAT",t[t.BROWSER_DEFAULT_WEBGL=37444]="BROWSER_DEFAULT_WEBGL",t[t.STATIC_DRAW=35044]="STATIC_DRAW",t[t.STREAM_DRAW=35040]="STREAM_DRAW",t[t.DYNAMIC_DRAW=35048]="DYNAMIC_DRAW",t[t.ARRAY_BUFFER=34962]="ARRAY_BUFFER",t[t.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",t[t.BUFFER_SIZE=34660]="BUFFER_SIZE",t[t.BUFFER_USAGE=34661]="BUFFER_USAGE",t[t.CURRENT_VERTEX_ATTRIB=34342]="CURRENT_VERTEX_ATTRIB",t[t.VERTEX_ATTRIB_ARRAY_ENABLED=34338]="VERTEX_ATTRIB_ARRAY_ENABLED",t[t.VERTEX_ATTRIB_ARRAY_SIZE=34339]="VERTEX_ATTRIB_ARRAY_SIZE",t[t.VERTEX_ATTRIB_ARRAY_STRIDE=34340]="VERTEX_ATTRIB_ARRAY_STRIDE",t[t.VERTEX_ATTRIB_ARRAY_TYPE=34341]="VERTEX_ATTRIB_ARRAY_TYPE",t[t.VERTEX_ATTRIB_ARRAY_NORMALIZED=34922]="VERTEX_ATTRIB_ARRAY_NORMALIZED",t[t.VERTEX_ATTRIB_ARRAY_POINTER=34373]="VERTEX_ATTRIB_ARRAY_POINTER",t[t.VERTEX_ATTRIB_ARRAY_BUFFER_BINDING=34975]="VERTEX_ATTRIB_ARRAY_BUFFER_BINDING",t[t.CULL_FACE=2884]="CULL_FACE",t[t.FRONT=1028]="FRONT",t[t.BACK=1029]="BACK",t[t.FRONT_AND_BACK=1032]="FRONT_AND_BACK",t[t.BLEND=3042]="BLEND",t[t.DEPTH_TEST=2929]="DEPTH_TEST",t[t.DITHER=3024]="DITHER",t[t.POLYGON_OFFSET_FILL=32823]="POLYGON_OFFSET_FILL",t[t.SAMPLE_ALPHA_TO_COVERAGE=32926]="SAMPLE_ALPHA_TO_COVERAGE",t[t.SAMPLE_COVERAGE=32928]="SAMPLE_COVERAGE",t[t.SCISSOR_TEST=3089]="SCISSOR_TEST",t[t.STENCIL_TEST=2960]="STENCIL_TEST",t[t.NO_ERROR=0]="NO_ERROR",t[t.INVALID_ENUM=1280]="INVALID_ENUM",t[t.INVALID_VALUE=1281]="INVALID_VALUE",t[t.INVALID_OPERATION=1282]="INVALID_OPERATION",t[t.OUT_OF_MEMORY=1285]="OUT_OF_MEMORY",t[t.CONTEXT_LOST_WEBGL=37442]="CONTEXT_LOST_WEBGL",t[t.CW=2304]="CW",t[t.CCW=2305]="CCW",t[t.DONT_CARE=4352]="DONT_CARE",t[t.FASTEST=4353]="FASTEST",t[t.NICEST=4354]="NICEST",t[t.GENERATE_MIPMAP_HINT=33170]="GENERATE_MIPMAP_HINT",t[t.BYTE=5120]="BYTE",t[t.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",t[t.SHORT=5122]="SHORT",t[t.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",t[t.INT=5124]="INT",t[t.UNSIGNED_INT=5125]="UNSIGNED_INT",t[t.FLOAT=5126]="FLOAT",t[t.DOUBLE=5130]="DOUBLE",t[t.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",t[t.ALPHA=6406]="ALPHA",t[t.RGB=6407]="RGB",t[t.RGBA=6408]="RGBA",t[t.LUMINANCE=6409]="LUMINANCE",t[t.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",t[t.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",t[t.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",t[t.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",t[t.FRAGMENT_SHADER=35632]="FRAGMENT_SHADER",t[t.VERTEX_SHADER=35633]="VERTEX_SHADER",t[t.COMPILE_STATUS=35713]="COMPILE_STATUS",t[t.DELETE_STATUS=35712]="DELETE_STATUS",t[t.LINK_STATUS=35714]="LINK_STATUS",t[t.VALIDATE_STATUS=35715]="VALIDATE_STATUS",t[t.ATTACHED_SHADERS=35717]="ATTACHED_SHADERS",t[t.ACTIVE_ATTRIBUTES=35721]="ACTIVE_ATTRIBUTES",t[t.ACTIVE_UNIFORMS=35718]="ACTIVE_UNIFORMS",t[t.MAX_VERTEX_ATTRIBS=34921]="MAX_VERTEX_ATTRIBS",t[t.MAX_VERTEX_UNIFORM_VECTORS=36347]="MAX_VERTEX_UNIFORM_VECTORS",t[t.MAX_VARYING_VECTORS=36348]="MAX_VARYING_VECTORS",t[t.MAX_COMBINED_TEXTURE_IMAGE_UNITS=35661]="MAX_COMBINED_TEXTURE_IMAGE_UNITS",t[t.MAX_VERTEX_TEXTURE_IMAGE_UNITS=35660]="MAX_VERTEX_TEXTURE_IMAGE_UNITS",t[t.MAX_TEXTURE_IMAGE_UNITS=34930]="MAX_TEXTURE_IMAGE_UNITS",t[t.MAX_FRAGMENT_UNIFORM_VECTORS=36349]="MAX_FRAGMENT_UNIFORM_VECTORS",t[t.SHADER_TYPE=35663]="SHADER_TYPE",t[t.SHADING_LANGUAGE_VERSION=35724]="SHADING_LANGUAGE_VERSION",t[t.CURRENT_PROGRAM=35725]="CURRENT_PROGRAM",t[t.NEVER=512]="NEVER",t[t.ALWAYS=519]="ALWAYS",t[t.LESS=513]="LESS",t[t.EQUAL=514]="EQUAL",t[t.LEQUAL=515]="LEQUAL",t[t.GREATER=516]="GREATER",t[t.GEQUAL=518]="GEQUAL",t[t.NOTEQUAL=517]="NOTEQUAL",t[t.KEEP=7680]="KEEP",t[t.REPLACE=7681]="REPLACE",t[t.INCR=7682]="INCR",t[t.DECR=7683]="DECR",t[t.INVERT=5386]="INVERT",t[t.INCR_WRAP=34055]="INCR_WRAP",t[t.DECR_WRAP=34056]="DECR_WRAP",t[t.NEAREST=9728]="NEAREST",t[t.LINEAR=9729]="LINEAR",t[t.NEAREST_MIPMAP_NEAREST=9984]="NEAREST_MIPMAP_NEAREST",t[t.LINEAR_MIPMAP_NEAREST=9985]="LINEAR_MIPMAP_NEAREST",t[t.NEAREST_MIPMAP_LINEAR=9986]="NEAREST_MIPMAP_LINEAR",t[t.LINEAR_MIPMAP_LINEAR=9987]="LINEAR_MIPMAP_LINEAR",t[t.TEXTURE_MAG_FILTER=10240]="TEXTURE_MAG_FILTER",t[t.TEXTURE_MIN_FILTER=10241]="TEXTURE_MIN_FILTER",t[t.TEXTURE_WRAP_S=10242]="TEXTURE_WRAP_S",t[t.TEXTURE_WRAP_T=10243]="TEXTURE_WRAP_T",t[t.TEXTURE_2D=3553]="TEXTURE_2D",t[t.TEXTURE=5890]="TEXTURE",t[t.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",t[t.TEXTURE_BINDING_CUBE_MAP=34068]="TEXTURE_BINDING_CUBE_MAP",t[t.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",t[t.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",t[t.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",t[t.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",t[t.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",t[t.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",t[t.MAX_CUBE_MAP_TEXTURE_SIZE=34076]="MAX_CUBE_MAP_TEXTURE_SIZE",t[t.TEXTURE0=33984]="TEXTURE0",t[t.ACTIVE_TEXTURE=34016]="ACTIVE_TEXTURE",t[t.REPEAT=10497]="REPEAT",t[t.CLAMP_TO_EDGE=33071]="CLAMP_TO_EDGE",t[t.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT",t[t.TEXTURE_WIDTH=4096]="TEXTURE_WIDTH",t[t.TEXTURE_HEIGHT=4097]="TEXTURE_HEIGHT",t[t.FLOAT_VEC2=35664]="FLOAT_VEC2",t[t.FLOAT_VEC3=35665]="FLOAT_VEC3",t[t.FLOAT_VEC4=35666]="FLOAT_VEC4",t[t.INT_VEC2=35667]="INT_VEC2",t[t.INT_VEC3=35668]="INT_VEC3",t[t.INT_VEC4=35669]="INT_VEC4",t[t.BOOL=35670]="BOOL",t[t.BOOL_VEC2=35671]="BOOL_VEC2",t[t.BOOL_VEC3=35672]="BOOL_VEC3",t[t.BOOL_VEC4=35673]="BOOL_VEC4",t[t.FLOAT_MAT2=35674]="FLOAT_MAT2",t[t.FLOAT_MAT3=35675]="FLOAT_MAT3",t[t.FLOAT_MAT4=35676]="FLOAT_MAT4",t[t.SAMPLER_2D=35678]="SAMPLER_2D",t[t.SAMPLER_CUBE=35680]="SAMPLER_CUBE",t[t.LOW_FLOAT=36336]="LOW_FLOAT",t[t.MEDIUM_FLOAT=36337]="MEDIUM_FLOAT",t[t.HIGH_FLOAT=36338]="HIGH_FLOAT",t[t.LOW_INT=36339]="LOW_INT",t[t.MEDIUM_INT=36340]="MEDIUM_INT",t[t.HIGH_INT=36341]="HIGH_INT",t[t.FRAMEBUFFER=36160]="FRAMEBUFFER",t[t.RENDERBUFFER=36161]="RENDERBUFFER",t[t.RGBA4=32854]="RGBA4",t[t.RGB5_A1=32855]="RGB5_A1",t[t.RGB565=36194]="RGB565",t[t.DEPTH_COMPONENT16=33189]="DEPTH_COMPONENT16",t[t.STENCIL_INDEX=6401]="STENCIL_INDEX",t[t.STENCIL_INDEX8=36168]="STENCIL_INDEX8",t[t.DEPTH_STENCIL=34041]="DEPTH_STENCIL",t[t.RENDERBUFFER_WIDTH=36162]="RENDERBUFFER_WIDTH",t[t.RENDERBUFFER_HEIGHT=36163]="RENDERBUFFER_HEIGHT",t[t.RENDERBUFFER_INTERNAL_FORMAT=36164]="RENDERBUFFER_INTERNAL_FORMAT",t[t.RENDERBUFFER_RED_SIZE=36176]="RENDERBUFFER_RED_SIZE",t[t.RENDERBUFFER_GREEN_SIZE=36177]="RENDERBUFFER_GREEN_SIZE",t[t.RENDERBUFFER_BLUE_SIZE=36178]="RENDERBUFFER_BLUE_SIZE",t[t.RENDERBUFFER_ALPHA_SIZE=36179]="RENDERBUFFER_ALPHA_SIZE",t[t.RENDERBUFFER_DEPTH_SIZE=36180]="RENDERBUFFER_DEPTH_SIZE",t[t.RENDERBUFFER_STENCIL_SIZE=36181]="RENDERBUFFER_STENCIL_SIZE",t[t.FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE=36048]="FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE",t[t.FRAMEBUFFER_ATTACHMENT_OBJECT_NAME=36049]="FRAMEBUFFER_ATTACHMENT_OBJECT_NAME",t[t.FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL=36050]="FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL",t[t.FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE=36051]="FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE",t[t.COLOR_ATTACHMENT0=36064]="COLOR_ATTACHMENT0",t[t.DEPTH_ATTACHMENT=36096]="DEPTH_ATTACHMENT",t[t.STENCIL_ATTACHMENT=36128]="STENCIL_ATTACHMENT",t[t.DEPTH_STENCIL_ATTACHMENT=33306]="DEPTH_STENCIL_ATTACHMENT",t[t.NONE=0]="NONE",t[t.FRAMEBUFFER_COMPLETE=36053]="FRAMEBUFFER_COMPLETE",t[t.FRAMEBUFFER_INCOMPLETE_ATTACHMENT=36054]="FRAMEBUFFER_INCOMPLETE_ATTACHMENT",t[t.FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT=36055]="FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT",t[t.FRAMEBUFFER_INCOMPLETE_DIMENSIONS=36057]="FRAMEBUFFER_INCOMPLETE_DIMENSIONS",t[t.FRAMEBUFFER_UNSUPPORTED=36061]="FRAMEBUFFER_UNSUPPORTED",t[t.FRAMEBUFFER_BINDING=36006]="FRAMEBUFFER_BINDING",t[t.RENDERBUFFER_BINDING=36007]="RENDERBUFFER_BINDING",t[t.READ_FRAMEBUFFER=36008]="READ_FRAMEBUFFER",t[t.DRAW_FRAMEBUFFER=36009]="DRAW_FRAMEBUFFER",t[t.MAX_RENDERBUFFER_SIZE=34024]="MAX_RENDERBUFFER_SIZE",t[t.INVALID_FRAMEBUFFER_OPERATION=1286]="INVALID_FRAMEBUFFER_OPERATION",t[t.UNPACK_FLIP_Y_WEBGL=37440]="UNPACK_FLIP_Y_WEBGL",t[t.UNPACK_PREMULTIPLY_ALPHA_WEBGL=37441]="UNPACK_PREMULTIPLY_ALPHA_WEBGL",t[t.UNPACK_COLORSPACE_CONVERSION_WEBGL=37443]="UNPACK_COLORSPACE_CONVERSION_WEBGL",t[t.READ_BUFFER=3074]="READ_BUFFER",t[t.UNPACK_ROW_LENGTH=3314]="UNPACK_ROW_LENGTH",t[t.UNPACK_SKIP_ROWS=3315]="UNPACK_SKIP_ROWS",t[t.UNPACK_SKIP_PIXELS=3316]="UNPACK_SKIP_PIXELS",t[t.PACK_ROW_LENGTH=3330]="PACK_ROW_LENGTH",t[t.PACK_SKIP_ROWS=3331]="PACK_SKIP_ROWS",t[t.PACK_SKIP_PIXELS=3332]="PACK_SKIP_PIXELS",t[t.TEXTURE_BINDING_3D=32874]="TEXTURE_BINDING_3D",t[t.UNPACK_SKIP_IMAGES=32877]="UNPACK_SKIP_IMAGES",t[t.UNPACK_IMAGE_HEIGHT=32878]="UNPACK_IMAGE_HEIGHT",t[t.MAX_3D_TEXTURE_SIZE=32883]="MAX_3D_TEXTURE_SIZE",t[t.MAX_ELEMENTS_VERTICES=33e3]="MAX_ELEMENTS_VERTICES",t[t.MAX_ELEMENTS_INDICES=33001]="MAX_ELEMENTS_INDICES",t[t.MAX_TEXTURE_LOD_BIAS=34045]="MAX_TEXTURE_LOD_BIAS",t[t.MAX_FRAGMENT_UNIFORM_COMPONENTS=35657]="MAX_FRAGMENT_UNIFORM_COMPONENTS",t[t.MAX_VERTEX_UNIFORM_COMPONENTS=35658]="MAX_VERTEX_UNIFORM_COMPONENTS",t[t.MAX_ARRAY_TEXTURE_LAYERS=35071]="MAX_ARRAY_TEXTURE_LAYERS",t[t.MIN_PROGRAM_TEXEL_OFFSET=35076]="MIN_PROGRAM_TEXEL_OFFSET",t[t.MAX_PROGRAM_TEXEL_OFFSET=35077]="MAX_PROGRAM_TEXEL_OFFSET",t[t.MAX_VARYING_COMPONENTS=35659]="MAX_VARYING_COMPONENTS",t[t.FRAGMENT_SHADER_DERIVATIVE_HINT=35723]="FRAGMENT_SHADER_DERIVATIVE_HINT",t[t.RASTERIZER_DISCARD=35977]="RASTERIZER_DISCARD",t[t.VERTEX_ARRAY_BINDING=34229]="VERTEX_ARRAY_BINDING",t[t.MAX_VERTEX_OUTPUT_COMPONENTS=37154]="MAX_VERTEX_OUTPUT_COMPONENTS",t[t.MAX_FRAGMENT_INPUT_COMPONENTS=37157]="MAX_FRAGMENT_INPUT_COMPONENTS",t[t.MAX_SERVER_WAIT_TIMEOUT=37137]="MAX_SERVER_WAIT_TIMEOUT",t[t.MAX_ELEMENT_INDEX=36203]="MAX_ELEMENT_INDEX",t[t.RED=6403]="RED",t[t.RGB8=32849]="RGB8",t[t.RGBA8=32856]="RGBA8",t[t.RGB10_A2=32857]="RGB10_A2",t[t.TEXTURE_3D=32879]="TEXTURE_3D",t[t.TEXTURE_WRAP_R=32882]="TEXTURE_WRAP_R",t[t.TEXTURE_MIN_LOD=33082]="TEXTURE_MIN_LOD",t[t.TEXTURE_MAX_LOD=33083]="TEXTURE_MAX_LOD",t[t.TEXTURE_BASE_LEVEL=33084]="TEXTURE_BASE_LEVEL",t[t.TEXTURE_MAX_LEVEL=33085]="TEXTURE_MAX_LEVEL",t[t.TEXTURE_COMPARE_MODE=34892]="TEXTURE_COMPARE_MODE",t[t.TEXTURE_COMPARE_FUNC=34893]="TEXTURE_COMPARE_FUNC",t[t.SRGB=35904]="SRGB",t[t.SRGB8=35905]="SRGB8",t[t.SRGB8_ALPHA8=35907]="SRGB8_ALPHA8",t[t.COMPARE_REF_TO_TEXTURE=34894]="COMPARE_REF_TO_TEXTURE",t[t.RGBA32F=34836]="RGBA32F",t[t.RGB32F=34837]="RGB32F",t[t.RGBA16F=34842]="RGBA16F",t[t.RGB16F=34843]="RGB16F",t[t.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY",t[t.TEXTURE_BINDING_2D_ARRAY=35869]="TEXTURE_BINDING_2D_ARRAY",t[t.R11F_G11F_B10F=35898]="R11F_G11F_B10F",t[t.RGB9_E5=35901]="RGB9_E5",t[t.RGBA32UI=36208]="RGBA32UI",t[t.RGB32UI=36209]="RGB32UI",t[t.RGBA16UI=36214]="RGBA16UI",t[t.RGB16UI=36215]="RGB16UI",t[t.RGBA8UI=36220]="RGBA8UI",t[t.RGB8UI=36221]="RGB8UI",t[t.RGBA32I=36226]="RGBA32I",t[t.RGB32I=36227]="RGB32I",t[t.RGBA16I=36232]="RGBA16I",t[t.RGB16I=36233]="RGB16I",t[t.RGBA8I=36238]="RGBA8I",t[t.RGB8I=36239]="RGB8I",t[t.RED_INTEGER=36244]="RED_INTEGER",t[t.RGB_INTEGER=36248]="RGB_INTEGER",t[t.RGBA_INTEGER=36249]="RGBA_INTEGER",t[t.R8=33321]="R8",t[t.RG8=33323]="RG8",t[t.R16F=33325]="R16F",t[t.R32F=33326]="R32F",t[t.RG16F=33327]="RG16F",t[t.RG32F=33328]="RG32F",t[t.R8I=33329]="R8I",t[t.R8UI=33330]="R8UI",t[t.R16I=33331]="R16I",t[t.R16UI=33332]="R16UI",t[t.R32I=33333]="R32I",t[t.R32UI=33334]="R32UI",t[t.RG8I=33335]="RG8I",t[t.RG8UI=33336]="RG8UI",t[t.RG16I=33337]="RG16I",t[t.RG16UI=33338]="RG16UI",t[t.RG32I=33339]="RG32I",t[t.RG32UI=33340]="RG32UI",t[t.R8_SNORM=36756]="R8_SNORM",t[t.RG8_SNORM=36757]="RG8_SNORM",t[t.RGB8_SNORM=36758]="RGB8_SNORM",t[t.RGBA8_SNORM=36759]="RGBA8_SNORM",t[t.RGB10_A2UI=36975]="RGB10_A2UI",t[t.TEXTURE_IMMUTABLE_FORMAT=37167]="TEXTURE_IMMUTABLE_FORMAT",t[t.TEXTURE_IMMUTABLE_LEVELS=33503]="TEXTURE_IMMUTABLE_LEVELS",t[t.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",t[t.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",t[t.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",t[t.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV",t[t.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",t[t.HALF_FLOAT=5131]="HALF_FLOAT",t[t.RG=33319]="RG",t[t.RG_INTEGER=33320]="RG_INTEGER",t[t.INT_2_10_10_10_REV=36255]="INT_2_10_10_10_REV",t[t.CURRENT_QUERY=34917]="CURRENT_QUERY",t[t.QUERY_RESULT=34918]="QUERY_RESULT",t[t.QUERY_RESULT_AVAILABLE=34919]="QUERY_RESULT_AVAILABLE",t[t.ANY_SAMPLES_PASSED=35887]="ANY_SAMPLES_PASSED",t[t.ANY_SAMPLES_PASSED_CONSERVATIVE=36202]="ANY_SAMPLES_PASSED_CONSERVATIVE",t[t.MAX_DRAW_BUFFERS=34852]="MAX_DRAW_BUFFERS",t[t.DRAW_BUFFER0=34853]="DRAW_BUFFER0",t[t.DRAW_BUFFER1=34854]="DRAW_BUFFER1",t[t.DRAW_BUFFER2=34855]="DRAW_BUFFER2",t[t.DRAW_BUFFER3=34856]="DRAW_BUFFER3",t[t.DRAW_BUFFER4=34857]="DRAW_BUFFER4",t[t.DRAW_BUFFER5=34858]="DRAW_BUFFER5",t[t.DRAW_BUFFER6=34859]="DRAW_BUFFER6",t[t.DRAW_BUFFER7=34860]="DRAW_BUFFER7",t[t.DRAW_BUFFER8=34861]="DRAW_BUFFER8",t[t.DRAW_BUFFER9=34862]="DRAW_BUFFER9",t[t.DRAW_BUFFER10=34863]="DRAW_BUFFER10",t[t.DRAW_BUFFER11=34864]="DRAW_BUFFER11",t[t.DRAW_BUFFER12=34865]="DRAW_BUFFER12",t[t.DRAW_BUFFER13=34866]="DRAW_BUFFER13",t[t.DRAW_BUFFER14=34867]="DRAW_BUFFER14",t[t.DRAW_BUFFER15=34868]="DRAW_BUFFER15",t[t.MAX_COLOR_ATTACHMENTS=36063]="MAX_COLOR_ATTACHMENTS",t[t.COLOR_ATTACHMENT1=36065]="COLOR_ATTACHMENT1",t[t.COLOR_ATTACHMENT2=36066]="COLOR_ATTACHMENT2",t[t.COLOR_ATTACHMENT3=36067]="COLOR_ATTACHMENT3",t[t.COLOR_ATTACHMENT4=36068]="COLOR_ATTACHMENT4",t[t.COLOR_ATTACHMENT5=36069]="COLOR_ATTACHMENT5",t[t.COLOR_ATTACHMENT6=36070]="COLOR_ATTACHMENT6",t[t.COLOR_ATTACHMENT7=36071]="COLOR_ATTACHMENT7",t[t.COLOR_ATTACHMENT8=36072]="COLOR_ATTACHMENT8",t[t.COLOR_ATTACHMENT9=36073]="COLOR_ATTACHMENT9",t[t.COLOR_ATTACHMENT10=36074]="COLOR_ATTACHMENT10",t[t.COLOR_ATTACHMENT11=36075]="COLOR_ATTACHMENT11",t[t.COLOR_ATTACHMENT12=36076]="COLOR_ATTACHMENT12",t[t.COLOR_ATTACHMENT13=36077]="COLOR_ATTACHMENT13",t[t.COLOR_ATTACHMENT14=36078]="COLOR_ATTACHMENT14",t[t.COLOR_ATTACHMENT15=36079]="COLOR_ATTACHMENT15",t[t.SAMPLER_3D=35679]="SAMPLER_3D",t[t.SAMPLER_2D_SHADOW=35682]="SAMPLER_2D_SHADOW",t[t.SAMPLER_2D_ARRAY=36289]="SAMPLER_2D_ARRAY",t[t.SAMPLER_2D_ARRAY_SHADOW=36292]="SAMPLER_2D_ARRAY_SHADOW",t[t.SAMPLER_CUBE_SHADOW=36293]="SAMPLER_CUBE_SHADOW",t[t.INT_SAMPLER_2D=36298]="INT_SAMPLER_2D",t[t.INT_SAMPLER_3D=36299]="INT_SAMPLER_3D",t[t.INT_SAMPLER_CUBE=36300]="INT_SAMPLER_CUBE",t[t.INT_SAMPLER_2D_ARRAY=36303]="INT_SAMPLER_2D_ARRAY",t[t.UNSIGNED_INT_SAMPLER_2D=36306]="UNSIGNED_INT_SAMPLER_2D",t[t.UNSIGNED_INT_SAMPLER_3D=36307]="UNSIGNED_INT_SAMPLER_3D",t[t.UNSIGNED_INT_SAMPLER_CUBE=36308]="UNSIGNED_INT_SAMPLER_CUBE",t[t.UNSIGNED_INT_SAMPLER_2D_ARRAY=36311]="UNSIGNED_INT_SAMPLER_2D_ARRAY",t[t.MAX_SAMPLES=36183]="MAX_SAMPLES",t[t.SAMPLER_BINDING=35097]="SAMPLER_BINDING",t[t.PIXEL_PACK_BUFFER=35051]="PIXEL_PACK_BUFFER",t[t.PIXEL_UNPACK_BUFFER=35052]="PIXEL_UNPACK_BUFFER",t[t.PIXEL_PACK_BUFFER_BINDING=35053]="PIXEL_PACK_BUFFER_BINDING",t[t.PIXEL_UNPACK_BUFFER_BINDING=35055]="PIXEL_UNPACK_BUFFER_BINDING",t[t.COPY_READ_BUFFER=36662]="COPY_READ_BUFFER",t[t.COPY_WRITE_BUFFER=36663]="COPY_WRITE_BUFFER",t[t.COPY_READ_BUFFER_BINDING=36662]="COPY_READ_BUFFER_BINDING",t[t.COPY_WRITE_BUFFER_BINDING=36663]="COPY_WRITE_BUFFER_BINDING",t[t.FLOAT_MAT2x3=35685]="FLOAT_MAT2x3",t[t.FLOAT_MAT2x4=35686]="FLOAT_MAT2x4",t[t.FLOAT_MAT3x2=35687]="FLOAT_MAT3x2",t[t.FLOAT_MAT3x4=35688]="FLOAT_MAT3x4",t[t.FLOAT_MAT4x2=35689]="FLOAT_MAT4x2",t[t.FLOAT_MAT4x3=35690]="FLOAT_MAT4x3",t[t.UNSIGNED_INT_VEC2=36294]="UNSIGNED_INT_VEC2",t[t.UNSIGNED_INT_VEC3=36295]="UNSIGNED_INT_VEC3",t[t.UNSIGNED_INT_VEC4=36296]="UNSIGNED_INT_VEC4",t[t.UNSIGNED_NORMALIZED=35863]="UNSIGNED_NORMALIZED",t[t.SIGNED_NORMALIZED=36764]="SIGNED_NORMALIZED",t[t.VERTEX_ATTRIB_ARRAY_INTEGER=35069]="VERTEX_ATTRIB_ARRAY_INTEGER",t[t.VERTEX_ATTRIB_ARRAY_DIVISOR=35070]="VERTEX_ATTRIB_ARRAY_DIVISOR",t[t.TRANSFORM_FEEDBACK_BUFFER_MODE=35967]="TRANSFORM_FEEDBACK_BUFFER_MODE",t[t.MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS=35968]="MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS",t[t.TRANSFORM_FEEDBACK_VARYINGS=35971]="TRANSFORM_FEEDBACK_VARYINGS",t[t.TRANSFORM_FEEDBACK_BUFFER_START=35972]="TRANSFORM_FEEDBACK_BUFFER_START",t[t.TRANSFORM_FEEDBACK_BUFFER_SIZE=35973]="TRANSFORM_FEEDBACK_BUFFER_SIZE",t[t.TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN=35976]="TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN",t[t.MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS=35978]="MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS",t[t.MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS=35979]="MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS",t[t.INTERLEAVED_ATTRIBS=35980]="INTERLEAVED_ATTRIBS",t[t.SEPARATE_ATTRIBS=35981]="SEPARATE_ATTRIBS",t[t.TRANSFORM_FEEDBACK_BUFFER=35982]="TRANSFORM_FEEDBACK_BUFFER",t[t.TRANSFORM_FEEDBACK_BUFFER_BINDING=35983]="TRANSFORM_FEEDBACK_BUFFER_BINDING",t[t.TRANSFORM_FEEDBACK=36386]="TRANSFORM_FEEDBACK",t[t.TRANSFORM_FEEDBACK_PAUSED=36387]="TRANSFORM_FEEDBACK_PAUSED",t[t.TRANSFORM_FEEDBACK_ACTIVE=36388]="TRANSFORM_FEEDBACK_ACTIVE",t[t.TRANSFORM_FEEDBACK_BINDING=36389]="TRANSFORM_FEEDBACK_BINDING",t[t.FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING=33296]="FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING",t[t.FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE=33297]="FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE",t[t.FRAMEBUFFER_ATTACHMENT_RED_SIZE=33298]="FRAMEBUFFER_ATTACHMENT_RED_SIZE",t[t.FRAMEBUFFER_ATTACHMENT_GREEN_SIZE=33299]="FRAMEBUFFER_ATTACHMENT_GREEN_SIZE",t[t.FRAMEBUFFER_ATTACHMENT_BLUE_SIZE=33300]="FRAMEBUFFER_ATTACHMENT_BLUE_SIZE",t[t.FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE=33301]="FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE",t[t.FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE=33302]="FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE",t[t.FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE=33303]="FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE",t[t.FRAMEBUFFER_DEFAULT=33304]="FRAMEBUFFER_DEFAULT",t[t.DEPTH24_STENCIL8=35056]="DEPTH24_STENCIL8",t[t.DRAW_FRAMEBUFFER_BINDING=36006]="DRAW_FRAMEBUFFER_BINDING",t[t.READ_FRAMEBUFFER_BINDING=36010]="READ_FRAMEBUFFER_BINDING",t[t.RENDERBUFFER_SAMPLES=36011]="RENDERBUFFER_SAMPLES",t[t.FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER=36052]="FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER",t[t.FRAMEBUFFER_INCOMPLETE_MULTISAMPLE=36182]="FRAMEBUFFER_INCOMPLETE_MULTISAMPLE",t[t.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER",t[t.UNIFORM_BUFFER_BINDING=35368]="UNIFORM_BUFFER_BINDING",t[t.UNIFORM_BUFFER_START=35369]="UNIFORM_BUFFER_START",t[t.UNIFORM_BUFFER_SIZE=35370]="UNIFORM_BUFFER_SIZE",t[t.MAX_VERTEX_UNIFORM_BLOCKS=35371]="MAX_VERTEX_UNIFORM_BLOCKS",t[t.MAX_FRAGMENT_UNIFORM_BLOCKS=35373]="MAX_FRAGMENT_UNIFORM_BLOCKS",t[t.MAX_COMBINED_UNIFORM_BLOCKS=35374]="MAX_COMBINED_UNIFORM_BLOCKS",t[t.MAX_UNIFORM_BUFFER_BINDINGS=35375]="MAX_UNIFORM_BUFFER_BINDINGS",t[t.MAX_UNIFORM_BLOCK_SIZE=35376]="MAX_UNIFORM_BLOCK_SIZE",t[t.MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS=35377]="MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS",t[t.MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS=35379]="MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS",t[t.UNIFORM_BUFFER_OFFSET_ALIGNMENT=35380]="UNIFORM_BUFFER_OFFSET_ALIGNMENT",t[t.ACTIVE_UNIFORM_BLOCKS=35382]="ACTIVE_UNIFORM_BLOCKS",t[t.UNIFORM_TYPE=35383]="UNIFORM_TYPE",t[t.UNIFORM_SIZE=35384]="UNIFORM_SIZE",t[t.UNIFORM_BLOCK_INDEX=35386]="UNIFORM_BLOCK_INDEX",t[t.UNIFORM_OFFSET=35387]="UNIFORM_OFFSET",t[t.UNIFORM_ARRAY_STRIDE=35388]="UNIFORM_ARRAY_STRIDE",t[t.UNIFORM_MATRIX_STRIDE=35389]="UNIFORM_MATRIX_STRIDE",t[t.UNIFORM_IS_ROW_MAJOR=35390]="UNIFORM_IS_ROW_MAJOR",t[t.UNIFORM_BLOCK_BINDING=35391]="UNIFORM_BLOCK_BINDING",t[t.UNIFORM_BLOCK_DATA_SIZE=35392]="UNIFORM_BLOCK_DATA_SIZE",t[t.UNIFORM_BLOCK_ACTIVE_UNIFORMS=35394]="UNIFORM_BLOCK_ACTIVE_UNIFORMS",t[t.UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES=35395]="UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES",t[t.UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER=35396]="UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER",t[t.UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER=35398]="UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER",t[t.OBJECT_TYPE=37138]="OBJECT_TYPE",t[t.SYNC_CONDITION=37139]="SYNC_CONDITION",t[t.SYNC_STATUS=37140]="SYNC_STATUS",t[t.SYNC_FLAGS=37141]="SYNC_FLAGS",t[t.SYNC_FENCE=37142]="SYNC_FENCE",t[t.SYNC_GPU_COMMANDS_COMPLETE=37143]="SYNC_GPU_COMMANDS_COMPLETE",t[t.UNSIGNALED=37144]="UNSIGNALED",t[t.SIGNALED=37145]="SIGNALED",t[t.ALREADY_SIGNALED=37146]="ALREADY_SIGNALED",t[t.TIMEOUT_EXPIRED=37147]="TIMEOUT_EXPIRED",t[t.CONDITION_SATISFIED=37148]="CONDITION_SATISFIED",t[t.WAIT_FAILED=37149]="WAIT_FAILED",t[t.SYNC_FLUSH_COMMANDS_BIT=1]="SYNC_FLUSH_COMMANDS_BIT",t[t.COLOR=6144]="COLOR",t[t.DEPTH=6145]="DEPTH",t[t.STENCIL=6146]="STENCIL",t[t.MIN=32775]="MIN",t[t.MAX=32776]="MAX",t[t.DEPTH_COMPONENT24=33190]="DEPTH_COMPONENT24",t[t.STREAM_READ=35041]="STREAM_READ",t[t.STREAM_COPY=35042]="STREAM_COPY",t[t.STATIC_READ=35045]="STATIC_READ",t[t.STATIC_COPY=35046]="STATIC_COPY",t[t.DYNAMIC_READ=35049]="DYNAMIC_READ",t[t.DYNAMIC_COPY=35050]="DYNAMIC_COPY",t[t.DEPTH_COMPONENT32F=36012]="DEPTH_COMPONENT32F",t[t.DEPTH32F_STENCIL8=36013]="DEPTH32F_STENCIL8",t[t.INVALID_INDEX=4294967295]="INVALID_INDEX",t[t.TIMEOUT_IGNORED=-1]="TIMEOUT_IGNORED",t[t.MAX_CLIENT_WAIT_TIMEOUT_WEBGL=37447]="MAX_CLIENT_WAIT_TIMEOUT_WEBGL",t[t.VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE=35070]="VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE",t[t.UNMASKED_VENDOR_WEBGL=37445]="UNMASKED_VENDOR_WEBGL",t[t.UNMASKED_RENDERER_WEBGL=37446]="UNMASKED_RENDERER_WEBGL",t[t.MAX_TEXTURE_MAX_ANISOTROPY_EXT=34047]="MAX_TEXTURE_MAX_ANISOTROPY_EXT",t[t.TEXTURE_MAX_ANISOTROPY_EXT=34046]="TEXTURE_MAX_ANISOTROPY_EXT",t[t.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",t[t.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",t[t.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",t[t.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",t[t.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",t[t.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",t[t.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",t[t.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",t[t.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",t[t.COMPRESSED_RGBA8_ETC2_EAC=37493]="COMPRESSED_RGBA8_ETC2_EAC",t[t.COMPRESSED_SRGB8_ETC2=37494]="COMPRESSED_SRGB8_ETC2",t[t.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37495]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC",t[t.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37496]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",t[t.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37497]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",t[t.COMPRESSED_RGB_PVRTC_4BPPV1_IMG=35840]="COMPRESSED_RGB_PVRTC_4BPPV1_IMG",t[t.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=35842]="COMPRESSED_RGBA_PVRTC_4BPPV1_IMG",t[t.COMPRESSED_RGB_PVRTC_2BPPV1_IMG=35841]="COMPRESSED_RGB_PVRTC_2BPPV1_IMG",t[t.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=35843]="COMPRESSED_RGBA_PVRTC_2BPPV1_IMG",t[t.COMPRESSED_RGB_ETC1_WEBGL=36196]="COMPRESSED_RGB_ETC1_WEBGL",t[t.COMPRESSED_RGB_ATC_WEBGL=35986]="COMPRESSED_RGB_ATC_WEBGL",t[t.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=35986]="COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL",t[t.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=34798]="COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL",t[t.UNSIGNED_INT_24_8_WEBGL=34042]="UNSIGNED_INT_24_8_WEBGL",t[t.HALF_FLOAT_OES=36193]="HALF_FLOAT_OES",t[t.RGBA32F_EXT=34836]="RGBA32F_EXT",t[t.RGB32F_EXT=34837]="RGB32F_EXT",t[t.FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT=33297]="FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT",t[t.UNSIGNED_NORMALIZED_EXT=35863]="UNSIGNED_NORMALIZED_EXT",t[t.MIN_EXT=32775]="MIN_EXT",t[t.MAX_EXT=32776]="MAX_EXT",t[t.SRGB_EXT=35904]="SRGB_EXT",t[t.SRGB_ALPHA_EXT=35906]="SRGB_ALPHA_EXT",t[t.SRGB8_ALPHA8_EXT=35907]="SRGB8_ALPHA8_EXT",t[t.FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT=33296]="FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT",t[t.FRAGMENT_SHADER_DERIVATIVE_HINT_OES=35723]="FRAGMENT_SHADER_DERIVATIVE_HINT_OES",t[t.COLOR_ATTACHMENT0_WEBGL=36064]="COLOR_ATTACHMENT0_WEBGL",t[t.COLOR_ATTACHMENT1_WEBGL=36065]="COLOR_ATTACHMENT1_WEBGL",t[t.COLOR_ATTACHMENT2_WEBGL=36066]="COLOR_ATTACHMENT2_WEBGL",t[t.COLOR_ATTACHMENT3_WEBGL=36067]="COLOR_ATTACHMENT3_WEBGL",t[t.COLOR_ATTACHMENT4_WEBGL=36068]="COLOR_ATTACHMENT4_WEBGL",t[t.COLOR_ATTACHMENT5_WEBGL=36069]="COLOR_ATTACHMENT5_WEBGL",t[t.COLOR_ATTACHMENT6_WEBGL=36070]="COLOR_ATTACHMENT6_WEBGL",t[t.COLOR_ATTACHMENT7_WEBGL=36071]="COLOR_ATTACHMENT7_WEBGL",t[t.COLOR_ATTACHMENT8_WEBGL=36072]="COLOR_ATTACHMENT8_WEBGL",t[t.COLOR_ATTACHMENT9_WEBGL=36073]="COLOR_ATTACHMENT9_WEBGL",t[t.COLOR_ATTACHMENT10_WEBGL=36074]="COLOR_ATTACHMENT10_WEBGL",t[t.COLOR_ATTACHMENT11_WEBGL=36075]="COLOR_ATTACHMENT11_WEBGL",t[t.COLOR_ATTACHMENT12_WEBGL=36076]="COLOR_ATTACHMENT12_WEBGL",t[t.COLOR_ATTACHMENT13_WEBGL=36077]="COLOR_ATTACHMENT13_WEBGL",t[t.COLOR_ATTACHMENT14_WEBGL=36078]="COLOR_ATTACHMENT14_WEBGL",t[t.COLOR_ATTACHMENT15_WEBGL=36079]="COLOR_ATTACHMENT15_WEBGL",t[t.DRAW_BUFFER0_WEBGL=34853]="DRAW_BUFFER0_WEBGL",t[t.DRAW_BUFFER1_WEBGL=34854]="DRAW_BUFFER1_WEBGL",t[t.DRAW_BUFFER2_WEBGL=34855]="DRAW_BUFFER2_WEBGL",t[t.DRAW_BUFFER3_WEBGL=34856]="DRAW_BUFFER3_WEBGL",t[t.DRAW_BUFFER4_WEBGL=34857]="DRAW_BUFFER4_WEBGL",t[t.DRAW_BUFFER5_WEBGL=34858]="DRAW_BUFFER5_WEBGL",t[t.DRAW_BUFFER6_WEBGL=34859]="DRAW_BUFFER6_WEBGL",t[t.DRAW_BUFFER7_WEBGL=34860]="DRAW_BUFFER7_WEBGL",t[t.DRAW_BUFFER8_WEBGL=34861]="DRAW_BUFFER8_WEBGL",t[t.DRAW_BUFFER9_WEBGL=34862]="DRAW_BUFFER9_WEBGL",t[t.DRAW_BUFFER10_WEBGL=34863]="DRAW_BUFFER10_WEBGL",t[t.DRAW_BUFFER11_WEBGL=34864]="DRAW_BUFFER11_WEBGL",t[t.DRAW_BUFFER12_WEBGL=34865]="DRAW_BUFFER12_WEBGL",t[t.DRAW_BUFFER13_WEBGL=34866]="DRAW_BUFFER13_WEBGL",t[t.DRAW_BUFFER14_WEBGL=34867]="DRAW_BUFFER14_WEBGL",t[t.DRAW_BUFFER15_WEBGL=34868]="DRAW_BUFFER15_WEBGL",t[t.MAX_COLOR_ATTACHMENTS_WEBGL=36063]="MAX_COLOR_ATTACHMENTS_WEBGL",t[t.MAX_DRAW_BUFFERS_WEBGL=34852]="MAX_DRAW_BUFFERS_WEBGL",t[t.VERTEX_ARRAY_BINDING_OES=34229]="VERTEX_ARRAY_BINDING_OES",t[t.QUERY_COUNTER_BITS_EXT=34916]="QUERY_COUNTER_BITS_EXT",t[t.CURRENT_QUERY_EXT=34917]="CURRENT_QUERY_EXT",t[t.QUERY_RESULT_EXT=34918]="QUERY_RESULT_EXT",t[t.QUERY_RESULT_AVAILABLE_EXT=34919]="QUERY_RESULT_AVAILABLE_EXT",t[t.TIME_ELAPSED_EXT=35007]="TIME_ELAPSED_EXT",t[t.TIMESTAMP_EXT=36392]="TIMESTAMP_EXT",t[t.GPU_DISJOINT_EXT=36795]="GPU_DISJOINT_EXT",e.ResourceType=void 0,(r=e.ResourceType||(e.ResourceType={}))[r.Buffer=0]="Buffer",r[r.Texture=1]="Texture",r[r.RenderTarget=2]="RenderTarget",r[r.Sampler=3]="Sampler",r[r.Program=4]="Program",r[r.Bindings=5]="Bindings",r[r.InputLayout=6]="InputLayout",r[r.RenderPipeline=7]="RenderPipeline",r[r.ComputePipeline=8]="ComputePipeline",r[r.Readback=9]="Readback",r[r.QueryPool=10]="QueryPool",r[r.RenderBundle=11]="RenderBundle",e.CompareFunction=void 0,(n=e.CompareFunction||(e.CompareFunction={}))[n.NEVER=512]="NEVER",n[n.LESS=513]="LESS",n[n.EQUAL=514]="EQUAL",n[n.LEQUAL=515]="LEQUAL",n[n.GREATER=516]="GREATER",n[n.NOTEQUAL=517]="NOTEQUAL",n[n.GEQUAL=518]="GEQUAL",n[n.ALWAYS=519]="ALWAYS",e.FrontFace=void 0,(o=e.FrontFace||(e.FrontFace={}))[o.CCW=2305]="CCW",o[o.CW=2304]="CW",e.CullMode=void 0,(a=e.CullMode||(e.CullMode={}))[a.NONE=0]="NONE",a[a.FRONT=1]="FRONT",a[a.BACK=2]="BACK",a[a.FRONT_AND_BACK=3]="FRONT_AND_BACK",e.BlendFactor=void 0,(i=e.BlendFactor||(e.BlendFactor={}))[i.ZERO=0]="ZERO",i[i.ONE=1]="ONE",i[i.SRC=768]="SRC",i[i.ONE_MINUS_SRC=769]="ONE_MINUS_SRC",i[i.DST=774]="DST",i[i.ONE_MINUS_DST=775]="ONE_MINUS_DST",i[i.SRC_ALPHA=770]="SRC_ALPHA",i[i.ONE_MINUS_SRC_ALPHA=771]="ONE_MINUS_SRC_ALPHA",i[i.DST_ALPHA=772]="DST_ALPHA",i[i.ONE_MINUS_DST_ALPHA=773]="ONE_MINUS_DST_ALPHA",i[i.CONST=32769]="CONST",i[i.ONE_MINUS_CONSTANT=32770]="ONE_MINUS_CONSTANT",i[i.SRC_ALPHA_SATURATE=776]="SRC_ALPHA_SATURATE",e.BlendMode=void 0,(s=e.BlendMode||(e.BlendMode={}))[s.ADD=32774]="ADD",s[s.SUBSTRACT=32778]="SUBSTRACT",s[s.REVERSE_SUBSTRACT=32779]="REVERSE_SUBSTRACT",s[s.MIN=32775]="MIN",s[s.MAX=32776]="MAX",e.AddressMode=void 0,(l=e.AddressMode||(e.AddressMode={}))[l.CLAMP_TO_EDGE=0]="CLAMP_TO_EDGE",l[l.REPEAT=1]="REPEAT",l[l.MIRRORED_REPEAT=2]="MIRRORED_REPEAT",e.FilterMode=void 0,(_=e.FilterMode||(e.FilterMode={}))[_.POINT=0]="POINT",_[_.BILINEAR=1]="BILINEAR",e.MipmapFilterMode=void 0,(u=e.MipmapFilterMode||(e.MipmapFilterMode={}))[u.NO_MIP=0]="NO_MIP",u[u.NEAREST=1]="NEAREST",u[u.LINEAR=2]="LINEAR",e.PrimitiveTopology=void 0,(E=e.PrimitiveTopology||(e.PrimitiveTopology={}))[E.POINTS=0]="POINTS",E[E.TRIANGLES=1]="TRIANGLES",E[E.TRIANGLE_STRIP=2]="TRIANGLE_STRIP",E[E.LINES=3]="LINES",E[E.LINE_STRIP=4]="LINE_STRIP",e.BufferUsage=void 0,(c=e.BufferUsage||(e.BufferUsage={}))[c.MAP_READ=1]="MAP_READ",c[c.MAP_WRITE=2]="MAP_WRITE",c[c.COPY_SRC=4]="COPY_SRC",c[c.COPY_DST=8]="COPY_DST",c[c.INDEX=16]="INDEX",c[c.VERTEX=32]="VERTEX",c[c.UNIFORM=64]="UNIFORM",c[c.STORAGE=128]="STORAGE",c[c.INDIRECT=256]="INDIRECT",c[c.QUERY_RESOLVE=512]="QUERY_RESOLVE",e.BufferFrequencyHint=void 0,(R=e.BufferFrequencyHint||(e.BufferFrequencyHint={}))[R.STATIC=1]="STATIC",R[R.DYNAMIC=2]="DYNAMIC",e.VertexStepMode=void 0,(T=e.VertexStepMode||(e.VertexStepMode={}))[T.VERTEX=1]="VERTEX",T[T.INSTANCE=2]="INSTANCE",e.TextureEvent=void 0,(e.TextureEvent||(e.TextureEvent={})).LOADED="loaded",e.TextureDimension=void 0,(p=e.TextureDimension||(e.TextureDimension={}))[p.TEXTURE_2D=0]="TEXTURE_2D",p[p.TEXTURE_2D_ARRAY=1]="TEXTURE_2D_ARRAY",p[p.TEXTURE_3D=2]="TEXTURE_3D",p[p.TEXTURE_CUBE_MAP=3]="TEXTURE_CUBE_MAP",e.TextureUsage=void 0,(A=e.TextureUsage||(e.TextureUsage={}))[A.SAMPLED=1]="SAMPLED",A[A.RENDER_TARGET=2]="RENDER_TARGET",A[A.STORAGE=4]="STORAGE",e.ChannelWriteMask=void 0,(d=e.ChannelWriteMask||(e.ChannelWriteMask={}))[d.NONE=0]="NONE",d[d.RED=1]="RED",d[d.GREEN=2]="GREEN",d[d.BLUE=4]="BLUE",d[d.ALPHA=8]="ALPHA",d[d.RGB=7]="RGB",d[d.ALL=15]="ALL",e.StencilOp=void 0,(F=e.StencilOp||(e.StencilOp={}))[F.KEEP=7680]="KEEP",F[F.ZERO=0]="ZERO",F[F.REPLACE=7681]="REPLACE",F[F.INVERT=5386]="INVERT",F[F.INCREMENT_CLAMP=7682]="INCREMENT_CLAMP",F[F.DECREMENT_CLAMP=7683]="DECREMENT_CLAMP",F[F.INCREMENT_WRAP=34055]="INCREMENT_WRAP",F[F.DECREMENT_WRAP=34056]="DECREMENT_WRAP",e.SamplerFormatKind=void 0,(f=e.SamplerFormatKind||(e.SamplerFormatKind={}))[f.Float=0]="Float",f[f.UnfilterableFloat=1]="UnfilterableFloat",f[f.Uint=2]="Uint",f[f.Sint=3]="Sint",f[f.Depth=4]="Depth",e.ViewportOrigin=void 0,(m=e.ViewportOrigin||(e.ViewportOrigin={}))[m.LOWER_LEFT=0]="LOWER_LEFT",m[m.UPPER_LEFT=1]="UPPER_LEFT",e.ClipSpaceNearZ=void 0,(h=e.ClipSpaceNearZ||(e.ClipSpaceNearZ={}))[h.NEGATIVE_ONE=0]="NEGATIVE_ONE",h[h.ZERO=1]="ZERO",e.QueryPoolType=void 0,(S=e.QueryPoolType||(e.QueryPoolType={}))[S.OcclusionConservative=0]="OcclusionConservative",e.FormatTypeFlags=void 0,(g=e.FormatTypeFlags||(e.FormatTypeFlags={}))[g.U8=1]="U8",g[g.U16=2]="U16",g[g.U32=3]="U32",g[g.S8=4]="S8",g[g.S16=5]="S16",g[g.S32=6]="S32",g[g.F16=7]="F16",g[g.F32=8]="F32",g[g.BC1=65]="BC1",g[g.BC2=66]="BC2",g[g.BC3=67]="BC3",g[g.BC4_UNORM=68]="BC4_UNORM",g[g.BC4_SNORM=69]="BC4_SNORM",g[g.BC5_UNORM=70]="BC5_UNORM",g[g.BC5_SNORM=71]="BC5_SNORM",g[g.U16_PACKED_5551=97]="U16_PACKED_5551",g[g.U16_PACKED_565=98]="U16_PACKED_565",g[g.D24=129]="D24",g[g.D32F=130]="D32F",g[g.D24S8=131]="D24S8",g[g.D32FS8=132]="D32FS8",e.FormatCompFlags=void 0,(B=e.FormatCompFlags||(e.FormatCompFlags={}))[B.R=1]="R",B[B.RG=2]="RG",B[B.RGB=3]="RGB",B[B.RGBA=4]="RGBA",B[B.A=5]="A",e.FormatFlags=void 0,(N=e.FormatFlags||(e.FormatFlags={}))[N.None=0]="None",N[N.Normalized=1]="Normalized",N[N.sRGB=2]="sRGB",N[N.Depth=4]="Depth",N[N.Stencil=8]="Stencil",N[N.RenderTarget=16]="RenderTarget",N[N.Luminance=32]="Luminance",e.Format=void 0,(C=e.Format||(e.Format={}))[C.ALPHA=O(e.FormatTypeFlags.U8,e.FormatCompFlags.A,e.FormatFlags.None)]="ALPHA",C[C.U8_LUMINANCE=O(e.FormatTypeFlags.U8,e.FormatCompFlags.A,e.FormatFlags.Luminance)]="U8_LUMINANCE",C[C.F16_LUMINANCE=O(e.FormatTypeFlags.F16,e.FormatCompFlags.A,e.FormatFlags.Luminance)]="F16_LUMINANCE",C[C.F32_LUMINANCE=O(e.FormatTypeFlags.F32,e.FormatCompFlags.A,e.FormatFlags.Luminance)]="F32_LUMINANCE",C[C.F16_R=O(e.FormatTypeFlags.F16,e.FormatCompFlags.R,e.FormatFlags.None)]="F16_R",C[C.F16_RG=O(e.FormatTypeFlags.F16,e.FormatCompFlags.RG,e.FormatFlags.None)]="F16_RG",C[C.F16_RGB=O(e.FormatTypeFlags.F16,e.FormatCompFlags.RGB,e.FormatFlags.None)]="F16_RGB",C[C.F16_RGBA=O(e.FormatTypeFlags.F16,e.FormatCompFlags.RGBA,e.FormatFlags.None)]="F16_RGBA",C[C.F32_R=O(e.FormatTypeFlags.F32,e.FormatCompFlags.R,e.FormatFlags.None)]="F32_R",C[C.F32_RG=O(e.FormatTypeFlags.F32,e.FormatCompFlags.RG,e.FormatFlags.None)]="F32_RG",C[C.F32_RGB=O(e.FormatTypeFlags.F32,e.FormatCompFlags.RGB,e.FormatFlags.None)]="F32_RGB",C[C.F32_RGBA=O(e.FormatTypeFlags.F32,e.FormatCompFlags.RGBA,e.FormatFlags.None)]="F32_RGBA",C[C.U8_R=O(e.FormatTypeFlags.U8,e.FormatCompFlags.R,e.FormatFlags.None)]="U8_R",C[C.U8_R_NORM=O(e.FormatTypeFlags.U8,e.FormatCompFlags.R,e.FormatFlags.Normalized)]="U8_R_NORM",C[C.U8_RG=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RG,e.FormatFlags.None)]="U8_RG",C[C.U8_RG_NORM=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RG,e.FormatFlags.Normalized)]="U8_RG_NORM",C[C.U8_RGB=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGB,e.FormatFlags.None)]="U8_RGB",C[C.U8_RGB_NORM=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGB,e.FormatFlags.Normalized)]="U8_RGB_NORM",C[C.U8_RGB_SRGB=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGB,e.FormatFlags.sRGB|e.FormatFlags.Normalized)]="U8_RGB_SRGB",C[C.U8_RGBA=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGBA,e.FormatFlags.None)]="U8_RGBA",C[C.U8_RGBA_NORM=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="U8_RGBA_NORM",C[C.U8_RGBA_SRGB=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGBA,e.FormatFlags.sRGB|e.FormatFlags.Normalized)]="U8_RGBA_SRGB",C[C.U16_R=O(e.FormatTypeFlags.U16,e.FormatCompFlags.R,e.FormatFlags.None)]="U16_R",C[C.U16_R_NORM=O(e.FormatTypeFlags.U16,e.FormatCompFlags.R,e.FormatFlags.Normalized)]="U16_R_NORM",C[C.U16_RG_NORM=O(e.FormatTypeFlags.U16,e.FormatCompFlags.RG,e.FormatFlags.Normalized)]="U16_RG_NORM",C[C.U16_RGBA_NORM=O(e.FormatTypeFlags.U16,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="U16_RGBA_NORM",C[C.U16_RGBA=O(e.FormatTypeFlags.U16,e.FormatCompFlags.RGBA,e.FormatFlags.None)]="U16_RGBA",C[C.U16_RGB=O(e.FormatTypeFlags.U16,e.FormatCompFlags.RGB,e.FormatFlags.None)]="U16_RGB",C[C.U16_RG=O(e.FormatTypeFlags.U16,e.FormatCompFlags.RG,e.FormatFlags.None)]="U16_RG",C[C.U32_R=O(e.FormatTypeFlags.U32,e.FormatCompFlags.R,e.FormatFlags.None)]="U32_R",C[C.U32_RG=O(e.FormatTypeFlags.U32,e.FormatCompFlags.RG,e.FormatFlags.None)]="U32_RG",C[C.U32_RGB=O(e.FormatTypeFlags.U32,e.FormatCompFlags.RGB,e.FormatFlags.None)]="U32_RGB",C[C.U32_RGBA=O(e.FormatTypeFlags.U32,e.FormatCompFlags.RGBA,e.FormatFlags.None)]="U32_RGBA",C[C.S8_R=O(e.FormatTypeFlags.S8,e.FormatCompFlags.R,e.FormatFlags.None)]="S8_R",C[C.S8_R_NORM=O(e.FormatTypeFlags.S8,e.FormatCompFlags.R,e.FormatFlags.Normalized)]="S8_R_NORM",C[C.S8_RG_NORM=O(e.FormatTypeFlags.S8,e.FormatCompFlags.RG,e.FormatFlags.Normalized)]="S8_RG_NORM",C[C.S8_RGB_NORM=O(e.FormatTypeFlags.S8,e.FormatCompFlags.RGB,e.FormatFlags.Normalized)]="S8_RGB_NORM",C[C.S8_RGBA_NORM=O(e.FormatTypeFlags.S8,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="S8_RGBA_NORM",C[C.S16_R=O(e.FormatTypeFlags.S16,e.FormatCompFlags.R,e.FormatFlags.None)]="S16_R",C[C.S16_RG=O(e.FormatTypeFlags.S16,e.FormatCompFlags.RG,e.FormatFlags.None)]="S16_RG",C[C.S16_RG_NORM=O(e.FormatTypeFlags.S16,e.FormatCompFlags.RG,e.FormatFlags.Normalized)]="S16_RG_NORM",C[C.S16_RGB_NORM=O(e.FormatTypeFlags.S16,e.FormatCompFlags.RGB,e.FormatFlags.Normalized)]="S16_RGB_NORM",C[C.S16_RGBA=O(e.FormatTypeFlags.S16,e.FormatCompFlags.RGBA,e.FormatFlags.None)]="S16_RGBA",C[C.S16_RGBA_NORM=O(e.FormatTypeFlags.S16,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="S16_RGBA_NORM",C[C.S32_R=O(e.FormatTypeFlags.S32,e.FormatCompFlags.R,e.FormatFlags.None)]="S32_R",C[C.S32_RG=O(e.FormatTypeFlags.S32,e.FormatCompFlags.RG,e.FormatFlags.None)]="S32_RG",C[C.S32_RGB=O(e.FormatTypeFlags.S32,e.FormatCompFlags.RGB,e.FormatFlags.None)]="S32_RGB",C[C.S32_RGBA=O(e.FormatTypeFlags.S32,e.FormatCompFlags.RGBA,e.FormatFlags.None)]="S32_RGBA",C[C.U16_RGBA_5551=O(e.FormatTypeFlags.U16_PACKED_5551,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="U16_RGBA_5551",C[C.U16_RGB_565=O(e.FormatTypeFlags.U16_PACKED_565,e.FormatCompFlags.RGB,e.FormatFlags.Normalized)]="U16_RGB_565",C[C.BC1=O(e.FormatTypeFlags.BC1,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="BC1",C[C.BC1_SRGB=O(e.FormatTypeFlags.BC1,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized|e.FormatFlags.sRGB)]="BC1_SRGB",C[C.BC2=O(e.FormatTypeFlags.BC2,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="BC2",C[C.BC2_SRGB=O(e.FormatTypeFlags.BC2,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized|e.FormatFlags.sRGB)]="BC2_SRGB",C[C.BC3=O(e.FormatTypeFlags.BC3,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized)]="BC3",C[C.BC3_SRGB=O(e.FormatTypeFlags.BC3,e.FormatCompFlags.RGBA,e.FormatFlags.Normalized|e.FormatFlags.sRGB)]="BC3_SRGB",C[C.BC4_UNORM=O(e.FormatTypeFlags.BC4_UNORM,e.FormatCompFlags.R,e.FormatFlags.Normalized)]="BC4_UNORM",C[C.BC4_SNORM=O(e.FormatTypeFlags.BC4_SNORM,e.FormatCompFlags.R,e.FormatFlags.Normalized)]="BC4_SNORM",C[C.BC5_UNORM=O(e.FormatTypeFlags.BC5_UNORM,e.FormatCompFlags.RG,e.FormatFlags.Normalized)]="BC5_UNORM",C[C.BC5_SNORM=O(e.FormatTypeFlags.BC5_SNORM,e.FormatCompFlags.RG,e.FormatFlags.Normalized)]="BC5_SNORM",C[C.D24=O(e.FormatTypeFlags.D24,e.FormatCompFlags.R,e.FormatFlags.Depth)]="D24",C[C.D24_S8=O(e.FormatTypeFlags.D24S8,e.FormatCompFlags.RG,e.FormatFlags.Depth|e.FormatFlags.Stencil)]="D24_S8",C[C.D32F=O(e.FormatTypeFlags.D32F,e.FormatCompFlags.R,e.FormatFlags.Depth)]="D32F",C[C.D32F_S8=O(e.FormatTypeFlags.D32FS8,e.FormatCompFlags.RG,e.FormatFlags.Depth|e.FormatFlags.Stencil)]="D32F_S8",C[C.U8_RGB_RT=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGB,e.FormatFlags.RenderTarget|e.FormatFlags.Normalized)]="U8_RGB_RT",C[C.U8_RGBA_RT=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGBA,e.FormatFlags.RenderTarget|e.FormatFlags.Normalized)]="U8_RGBA_RT",C[C.U8_RGBA_RT_SRGB=O(e.FormatTypeFlags.U8,e.FormatCompFlags.RGBA,e.FormatFlags.RenderTarget|e.FormatFlags.Normalized|e.FormatFlags.sRGB)]="U8_RGBA_RT_SRGB";var H=W(0,0,0,0),V=W(0,0,0,1),k=W(1,1,1,0),Y=W(1,1,1,1),K=!0;function q(t,r){if(void 0===r&&(r=K),!r)return t;switch(t){case e.CompareFunction.LESS:return e.CompareFunction.GREATER;case e.CompareFunction.LEQUAL:return e.CompareFunction.GEQUAL;case e.CompareFunction.GEQUAL:return e.CompareFunction.LEQUAL;case e.CompareFunction.GREATER:return e.CompareFunction.LESS;default:return t}}function z(e){return!(!e||0!=(e&e-1))}function Z(e,t){return null!=e?e:t}function Q(e){return void 0===e?null:e}function j(e,t){var r=t-1;return e+r&~r}function $(e,t,r){for(var n=0,o=e.length;o>n;){var a=n+(o-n>>>1);0>r(t,e[a])?o=a:n=a+1}return n}function J(e,t){for(var r=Array(e),n=0;e>n;n++)r[n]=t();return r}function ee(e,t){return void 0===t&&(t=1),e.split("\n").map((function(e,r){return"".concat(te(""+(t+r),4," "),"  ").concat(e)})).join("\n")}function te(e,t,r){for(void 0===r&&(r="0");t>e.length;)e="".concat(r).concat(e);return e}function re(e,t){e.blendDstFactor=t.blendDstFactor,e.blendSrcFactor=t.blendSrcFactor,e.blendMode=t.blendMode}function ne(e,t){return void 0===e&&(e={}),e.compare=t.compare,e.depthFailOp=t.depthFailOp,e.passOp=t.passOp,e.failOp=t.failOp,e.mask=t.mask,e}function oe(e,t){return void 0===e&&(e={rgbBlendState:{},alphaBlendState:{},channelWriteMask:0}),re(e.rgbBlendState,t.rgbBlendState),re(e.alphaBlendState,t.alphaBlendState),e.channelWriteMask=t.channelWriteMask,e}function ae(e,t){e.length!==t.length&&(e.length=t.length);for(var r=0;t.length>r;r++)e[r]=oe(e[r],t[r])}function ie(e,t){void 0!==t.attachmentsState&&ae(e.attachmentsState,t.attachmentsState),e.blendConstant&&t.blendConstant&&X(e.blendConstant,t.blendConstant),e.depthCompare=Z(t.depthCompare,e.depthCompare),e.depthWrite=Z(t.depthWrite,e.depthWrite),e.stencilWrite=Z(t.stencilWrite,e.stencilWrite),e.stencilFront&&t.stencilFront&&ne(e.stencilFront,t.stencilFront),e.stencilBack&&t.stencilBack&&ne(e.stencilBack,t.stencilBack),e.cullMode=Z(t.cullMode,e.cullMode),e.frontFace=Z(t.frontFace,e.frontFace),e.polygonOffset=Z(t.polygonOffset,e.polygonOffset),e.polygonOffsetFactor=Z(t.polygonOffsetFactor,e.polygonOffsetFactor),e.polygonOffsetUnits=Z(t.polygonOffsetUnits,e.polygonOffsetUnits)}function se(e){var t=Object.assign({},e);return t.attachmentsState=[],ae(t.attachmentsState,e.attachmentsState),t.blendConstant=t.blendConstant&&w(t.blendConstant),t.stencilFront=ne(void 0,e.stencilFront),t.stencilBack=ne(void 0,e.stencilBack),t}function le(e,t){void 0!==t.channelWriteMask&&(e.channelWriteMask=t.channelWriteMask),void 0!==t.rgbBlendMode&&(e.rgbBlendState.blendMode=t.rgbBlendMode),void 0!==t.alphaBlendMode&&(e.alphaBlendState.blendMode=t.alphaBlendMode),void 0!==t.rgbBlendSrcFactor&&(e.rgbBlendState.blendSrcFactor=t.rgbBlendSrcFactor),void 0!==t.alphaBlendSrcFactor&&(e.alphaBlendState.blendSrcFactor=t.alphaBlendSrcFactor),void 0!==t.rgbBlendDstFactor&&(e.rgbBlendState.blendDstFactor=t.rgbBlendDstFactor),void 0!==t.alphaBlendDstFactor&&(e.alphaBlendState.blendDstFactor=t.alphaBlendDstFactor)}var _e={blendMode:e.BlendMode.ADD,blendSrcFactor:e.BlendFactor.ONE,blendDstFactor:e.BlendFactor.ZERO},ue={attachmentsState:[{channelWriteMask:e.ChannelWriteMask.ALL,rgbBlendState:_e,alphaBlendState:_e}],blendConstant:w(H),depthWrite:!0,depthCompare:e.CompareFunction.LEQUAL,stencilWrite:!1,stencilFront:{compare:e.CompareFunction.ALWAYS,passOp:e.StencilOp.KEEP,depthFailOp:e.StencilOp.KEEP,failOp:e.StencilOp.KEEP},stencilBack:{compare:e.CompareFunction.ALWAYS,passOp:e.StencilOp.KEEP,depthFailOp:e.StencilOp.KEEP,failOp:e.StencilOp.KEEP},cullMode:e.CullMode.NONE,frontFace:e.FrontFace.CCW,polygonOffset:!1,polygonOffsetFactor:0,polygonOffsetUnits:0};function Ee(e,t){void 0===e&&(e=null),void 0===t&&(t=ue);var r=se(t);return null!==e&&ie(r,e),r}var ce=Ee({depthCompare:e.CompareFunction.ALWAYS,depthWrite:!1},ue);var Re={texture:null,sampler:null,formatKind:e.SamplerFormatKind.Float,dimension:e.TextureDimension.TEXTURE_2D},Te=function(e,t){return Te=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},Te(e,t)};function pe(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+t+" is not a constructor or null");function r(){this.constructor=e}Te(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var Ae=function(){return Ae=Object.assign||function(e){for(var t,r=1,n=arguments.length;n>r;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Ae.apply(this,arguments)};function de(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{l(n.next(e))}catch(e){a(e)}}function s(e){try{l(n.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}l((n=n.apply(e,t||[])).next())}))}function Fe(e,t){var r,n,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(i=0)),i;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return i.label++,{value:s[1],done:!1};case 5:i.label++,n=s[1],s=[0];continue;case 7:s=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){i=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&o[3]>s[1])){i.label=s[1];break}if(6===s[0]&&o[1]>i.label){i.label=o[1],o=s;break}if(o&&o[2]>i.label){i.label=o[2],i.ops.push(s);break}o[2]&&i.ops.pop(),i.trys.pop();continue}s=t.call(e,i)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}function fe(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function me(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i}function he(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;a>o;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}var Se,ge=function(e){return null==e},Be={}.toString,Ne=function(e,t){return Be.call(e)==="[object "+t+"]"},Ce=function(e,t,r){return t>e?t:e>r?r:e},Le=function(e){return Ne(e,"Number")};function Oe(e,t,r){if(e.length!==t.length)return!1;for(var n=0;e.length>n;n++)if(!r(e[n],t[n]))return!1;return!0}function Me(e,t){for(var r=Array(e.length),n=0;e.length>n;n++)r[n]=t(e[n]);return r}function Ie(e,t){return e.texture===t.texture&&e.binding===t.binding}function Ue(e,t){return e.buffer===t.buffer&&e.size===t.size&&e.binding===t.binding&&e.offset===t.offset}function De(e,t){return null===e?null===t:null!==t&&(e.sampler===t.sampler&&e.texture===t.texture&&e.dimension===t.dimension&&e.formatKind===t.formatKind&&e.comparison===t.comparison)}function Ge(e,t){return e.blendMode==t.blendMode&&e.blendSrcFactor===t.blendSrcFactor&&e.blendDstFactor===t.blendDstFactor}function Pe(e,t){return!!Ge(e.rgbBlendState,t.rgbBlendState)&&(!!Ge(e.alphaBlendState,t.alphaBlendState)&&e.channelWriteMask===t.channelWriteMask)}function ve(e,t){return e.compare==t.compare&&e.depthFailOp===t.depthFailOp&&e.failOp===t.failOp&&e.passOp===t.passOp&&e.mask===t.mask}function ye(e,t){return e.id===t.id}function be(e,t){return e===t}function xe(e,t){return e.offset===t.offset&&e.shaderLocation===t.shaderLocation&&e.format===t.format&&e.divisor===t.divisor}function Xe(e,t){return ge(e)?ge(t):!ge(t)&&(e.arrayStride===t.arrayStride&&e.stepMode===t.stepMode&&Oe(e.attributes,t.attributes,xe))}function we(e){return{sampler:e.sampler,texture:e.texture,dimension:e.dimension,formatKind:e.formatKind,comparison:e.comparison}}function We(e){return{binding:e.binding,buffer:e.buffer,offset:e.offset,size:e.size}}function He(e){return{binding:e.binding,texture:e.texture}}function Ve(e){return{shaderLocation:e.shaderLocation,format:e.format,offset:e.offset,divisor:e.divisor}}function ke(e){return ge(e)?e:{arrayStride:e.arrayStride,stepMode:e.stepMode,attributes:Me(e.attributes,Ve)}}var Ye=/([^[]*)(\[[0-9]+\])?/;function Ke(e){if("]"!==e[e.length-1])return{name:e,length:1,isArray:!1};var t=e.match(Ye);if(!t||2>t.length)throw Error("Failed to parse GLSL uniform name ".concat(e));return{name:t[1],length:Number(t[2])||1,isArray:!!t[2]}}function qe(){var e=null;return function(t,r,n){var o=e!==n;return o&&(t.uniform1i(r,n),e=n),o}}function ze(e,t,r,n){var o=null,a=null;return function(i,s,l){var _=t(l,r),u=_.length,E=!1;if(null===o)o=new Float32Array(u),a=u,E=!0;else{y(a===u,"Uniform length cannot change.");for(var c=0;u>c;++c)if(_[c]!==o[c]){E=!0;break}}return E&&(n(i,e,s,_),o.set(_)),E}}function Ze(e,t,r,n){e[t](r,n)}function Qe(e,t,r,n){e[t](r,!1,n)}var je={},$e={},Je={},et=[0];function tt(e,t,r,n){1===t&&"boolean"==typeof e&&(e=e?1:0),Number.isFinite(e)&&(et[0]=e,e=et);var o=e.length;if(e instanceof r)return e;var a=n[o];a||(a=new r(o),n[o]=a);for(var i=0;o>i;i++)a[i]=e[i];return a}function rt(e,t){return tt(e,t,Float32Array,je)}function nt(e,t){return tt(e,t,Int32Array,$e)}function ot(e,t){return tt(e,t,Uint32Array,Je)}var at=((Se={})[e.GL.FLOAT]=ze.bind(null,"uniform1fv",rt,1,Ze),Se[e.GL.FLOAT_VEC2]=ze.bind(null,"uniform2fv",rt,2,Ze),Se[e.GL.FLOAT_VEC3]=ze.bind(null,"uniform3fv",rt,3,Ze),Se[e.GL.FLOAT_VEC4]=ze.bind(null,"uniform4fv",rt,4,Ze),Se[e.GL.INT]=ze.bind(null,"uniform1iv",nt,1,Ze),Se[e.GL.INT_VEC2]=ze.bind(null,"uniform2iv",nt,2,Ze),Se[e.GL.INT_VEC3]=ze.bind(null,"uniform3iv",nt,3,Ze),Se[e.GL.INT_VEC4]=ze.bind(null,"uniform4iv",nt,4,Ze),Se[e.GL.BOOL]=ze.bind(null,"uniform1iv",nt,1,Ze),Se[e.GL.BOOL_VEC2]=ze.bind(null,"uniform2iv",nt,2,Ze),Se[e.GL.BOOL_VEC3]=ze.bind(null,"uniform3iv",nt,3,Ze),Se[e.GL.BOOL_VEC4]=ze.bind(null,"uniform4iv",nt,4,Ze),Se[e.GL.FLOAT_MAT2]=ze.bind(null,"uniformMatrix2fv",rt,4,Qe),Se[e.GL.FLOAT_MAT3]=ze.bind(null,"uniformMatrix3fv",rt,9,Qe),Se[e.GL.FLOAT_MAT4]=ze.bind(null,"uniformMatrix4fv",rt,16,Qe),Se[e.GL.UNSIGNED_INT]=ze.bind(null,"uniform1uiv",ot,1,Ze),Se[e.GL.UNSIGNED_INT_VEC2]=ze.bind(null,"uniform2uiv",ot,2,Ze),Se[e.GL.UNSIGNED_INT_VEC3]=ze.bind(null,"uniform3uiv",ot,3,Ze),Se[e.GL.UNSIGNED_INT_VEC4]=ze.bind(null,"uniform4uiv",ot,4,Ze),Se[e.GL.FLOAT_MAT2x3]=ze.bind(null,"uniformMatrix2x3fv",rt,6,Qe),Se[e.GL.FLOAT_MAT2x4]=ze.bind(null,"uniformMatrix2x4fv",rt,8,Qe),Se[e.GL.FLOAT_MAT3x2]=ze.bind(null,"uniformMatrix3x2fv",rt,6,Qe),Se[e.GL.FLOAT_MAT3x4]=ze.bind(null,"uniformMatrix3x4fv",rt,12,Qe),Se[e.GL.FLOAT_MAT4x2]=ze.bind(null,"uniformMatrix4x2fv",rt,8,Qe),Se[e.GL.FLOAT_MAT4x3]=ze.bind(null,"uniformMatrix4x3fv",rt,12,Qe),Se[e.GL.SAMPLER_2D]=qe,Se[e.GL.SAMPLER_CUBE]=qe,Se[e.GL.SAMPLER_3D]=qe,Se[e.GL.SAMPLER_2D_SHADOW]=qe,Se[e.GL.SAMPLER_2D_ARRAY]=qe,Se[e.GL.SAMPLER_2D_ARRAY_SHADOW]=qe,Se[e.GL.SAMPLER_CUBE_SHADOW]=qe,Se[e.GL.INT_SAMPLER_2D]=qe,Se[e.GL.INT_SAMPLER_3D]=qe,Se[e.GL.INT_SAMPLER_CUBE]=qe,Se[e.GL.INT_SAMPLER_2D_ARRAY]=qe,Se[e.GL.UNSIGNED_INT_SAMPLER_2D]=qe,Se[e.GL.UNSIGNED_INT_SAMPLER_3D]=qe,Se[e.GL.UNSIGNED_INT_SAMPLER_CUBE]=qe,Se[e.GL.UNSIGNED_INT_SAMPLER_2D_ARRAY]=qe,Se);function it(e,t,r){var n=at[r.type];if(!n)throw Error("Unknown GLSL uniform type ".concat(r.type));return n().bind(null,e,t)}var st={"[object Int8Array]":5120,"[object Int16Array]":5122,"[object Int32Array]":5124,"[object Uint8Array]":5121,"[object Uint8ClampedArray]":5121,"[object Uint16Array]":5123,"[object Uint32Array]":5125,"[object Float32Array]":5126,"[object Float64Array]":5121,"[object ArrayBuffer]":5121};function lt(e){return Object.prototype.toString.call(e)in st}function _t(e,t){return"#define ".concat(e," ").concat(t)}function ut(e){var t={};return e.replace(/^\s*#define\s*(\S*)\s*(\S*)\s*$/gm,(function(e,r,n){var o=Number(n);return t[r]=isNaN(o)?n:o,""})),t}function Et(e,t){var r=[];return e.replace(/^\s*layout\(location\s*=\s*(\S*)\)\s*in\s+\S+\s*(.*);$/gm,(function(e,n,o){var a=Number(n);return r.push({location:isNaN(a)?t[n]:a,name:o}),""})),r}function ct(e){if(void 0===e)return null;var t=/binding\s*=\s*(\d+)/.exec(e);if(null!==t){var r=parseInt(t[1],10);if(!Number.isNaN(r))return r}return null}function Rt(e){return[e,""]}function Tt(t,r,n,o,a){var i;void 0===o&&(o=null),void 0===a&&(a=!0);var s="#version 100"===t.glslVersion,l="frag"===r&&(null===(i=n.match(/^\s*layout\(location\s*=\s*\d*\)\s*out\s+vec4\s*(.*);$/gm))||void 0===i?void 0:i.length)>1,_=n.replace("\r\n","\n").split("\n").map((function(e){return e.replace(/[/][/].*$/,"")})).filter((function(e){return!(!e||/^\s+$/.test(e))})),u="";null!==o&&(u=Object.keys(o).map((function(e){return _t(e,o[e])})).join("\n"));var E=_.find((function(e){return e.startsWith("precision")}))||"precision mediump float;",c=a?_.filter((function(e){return!e.startsWith("precision")})).join("\n"):_.join("\n"),R="";if(t.viewportOrigin===e.ViewportOrigin.UPPER_LEFT&&(R+="".concat(_t("VIEWPORT_ORIGIN_TL","1"),"\n")),t.clipSpaceNearZ===e.ClipSpaceNearZ.ZERO&&(R+="".concat(_t("CLIPSPACE_NEAR_ZERO","1"),"\n")),t.explicitBindingLocations){var T=0,p=0,A=0;c=c.replace(/^\s*(layout\((.*)\))?\s*uniform(.+{)$/gm,(function(e,t,r,n){return"layout(".concat(r?"".concat(r,", "):"","set = ").concat(T,", binding = ").concat(p++,") uniform ").concat(n)})),T++,p=0,y(t.separateSamplerTextures),c=c.replace(/^\s*(layout\((.*)\))?\s*uniform sampler(\w+) (.*);/gm,(function(e,t,n,o,a){var i=ct(n);null===i&&(i=p++);var s=me(Rt(o),2),l=s[0],_=s[1];return"frag"===r?"\nlayout(set = ".concat(T,", binding = ").concat(2*i+0,") uniform texture").concat(l," T_").concat(a,";\nlayout(set = ").concat(T,", binding = ").concat(2*i+1,") uniform sampler").concat(_," S_").concat(a,";").trim():""})),c=c.replace("frag"===r?/^\s*\b(varying|in)\b/gm:/^\s*\b(varying|out)\b/gm,(function(e,t){return"layout(location = ".concat(A++,") ").concat(t)})),R+="".concat(_t("gl_VertexID","gl_VertexIndex"),"\n"),R+="".concat(_t("gl_InstanceID","gl_InstanceIndex"),"\n"),E=E.replace(/^precision (.*) sampler(.*);$/gm,"")}else{var d=0;c=c.replace(/^\s*(layout\((.*)\))?\s*uniform sampler(\w+) (.*);/gm,(function(e,t,r,n,o){var a=ct(r);return null===a&&(a=d++),"uniform sampler".concat(n," ").concat(o,"; // BINDING=").concat(a)}))}if(c=(c=(c=c.replace(/\bPU_SAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){return"SAMPLER_".concat(t,"(P_").concat(r,")")}))).replace(/\bPF_SAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){return"PP_SAMPLER_".concat(t,"(P_").concat(r,")")}))).replace(/\bPU_TEXTURE\((.*?)\)/g,(function(e,t){return"TEXTURE(P_".concat(t,")")})),t.separateSamplerTextures)c=c.replace(/\bPD_SAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){var n=me(Rt(t),2),o=n[1];return"texture".concat(n[0]," T_P_").concat(r,", sampler").concat(o," S_P_").concat(r)})),c=(c=(c=c.replace(/\bPP_SAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){return"T_".concat(r,", S_").concat(r)}))).replace(/\bSAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){return"sampler".concat(t,"(T_").concat(r,", S_").concat(r,")")}))).replace(/\bTEXTURE\((.*?)\)/g,(function(e,t){return"T_".concat(t)}));else{var F=[];c=(c=(c=c.replace(/\bPD_SAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){return"sampler".concat(t," P_").concat(r)}))).replace(/\bPP_SAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){return r}))).replace(/\bSAMPLER_(\w+)\((.*?)\)/g,(function(e,t,r){return F.push([r,t]),r})),s&&F.forEach((function(e){var t=me(e,2),r=t[0],n=t[1];c=c.replace(RegExp("texture\\(".concat(r),"g"),(function(){return"texture".concat(n,"(").concat(r)}))})),c=c.replace(/\bTEXTURE\((.*?)\)/g,(function(e,t){return t}))}var f="".concat(s?"":t.glslVersion,"\n").concat(s&&l?"#extension GL_EXT_draw_buffers : require\n":"","\n").concat(s&&"frag"===r?"#extension GL_OES_standard_derivatives : enable\n":"").concat(a?E:"","\n").concat(R||"").concat(u?u+"\n":"","\n").concat(c,"\n").trim();if(t.explicitBindingLocations&&"frag"===r&&(f=f.replace(/^\b(out)\b/g,(function(e,t){return"layout(location = 0) ".concat(t)}))),s){if("frag"===r&&(f=f.replace(/^\s*in\s+(\S+)\s*(.*);$/gm,(function(e,t,r){return"varying ".concat(t," ").concat(r,";\n")}))),"vert"===r&&(f=(f=f.replace(/^\s*out\s+(\S+)\s*(.*);$/gm,(function(e,t,r){return"varying ".concat(t," ").concat(r,";\n")}))).replace(/^\s*layout\(location\s*=\s*\S*\)\s*in\s+(\S+)\s*(.*);$/gm,(function(e,t,r){return"attribute ".concat(t," ").concat(r,";\n")}))),f=f.replace(/\s*uniform\s*.*\s*{((?:\s*.*\s*)*?)};/g,(function(e,t){return t.trim().replace(/^.*$/gm,(function(e){var t=e.trim();return t.startsWith("#")?t:e?"uniform ".concat(t):""}))})),"frag"===r)if(l){var m=[],h=(f=f.replace(/^\s*layout\(location\s*=\s*\d*\)\s*out\s+vec4\s*(.*);$/gm,(function(e,t){return m.push(t),"vec4 ".concat(t,";\n")}))).lastIndexOf("}");f=f.substring(0,h)+"\n    ".concat(m.map((function(e,t){return"gl_FragData[".concat(t,"] = ").concat(e,";\n    ")})).join("\n"))+f.substring(h)}else{var S;if(f=f.replace(/^\s*out\s+(\S+)\s*(.*);$/gm,(function(e,t,r){return S=r,"".concat(t," ").concat(r,";\n")})),S){h=f.lastIndexOf("}");f=f.substring(0,h)+"\n  gl_FragColor = vec4(".concat(S,");\n")+f.substring(h)}}f=f.replace(/^\s*layout\((.*)\)/gm,"")}return f}function pt(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var At={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,i){if("function"!=typeof n)throw new TypeError("The listener must be a function");var s=new o(n,a||e,i),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function i(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},s.prototype.listeners=function(e){var t=this._events[r?r+e:e];if(!t)return[];if(t.fn)return[t.fn];for(var n=0,o=t.length,a=Array(o);o>n;n++)a[n]=t[n].fn;return a},s.prototype.listenerCount=function(e){var t=this._events[r?r+e:e];return t?t.fn?1:t.length:0},s.prototype.emit=function(e,t,n,o,a,i){var s=r?r+e:e;if(!this._events[s])return!1;var l,_,u=this._events[s],E=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),E){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,a),!0;case 6:return u.fn.call(u.context,t,n,o,a,i),!0}for(_=1,l=Array(E-1);E>_;_++)l[_-1]=arguments[_];u.fn.apply(u.context,l)}else{var c,R=u.length;for(_=0;R>_;_++)switch(u[_].once&&this.removeListener(e,u[_].fn,void 0,!0),E){case 1:u[_].fn.call(u[_].context);break;case 2:u[_].fn.call(u[_].context,t);break;case 3:u[_].fn.call(u[_].context,t,n);break;case 4:u[_].fn.call(u[_].context,t,n,o);break;default:if(!l)for(c=1,l=Array(E-1);E>c;c++)l[c-1]=arguments[c];u[_].fn.apply(u[_].context,l)}}return!0},s.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,o){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return i(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||o&&!s.once||n&&s.context!==n||i(this,a);else{for(var l=0,_=[],u=s.length;u>l;l++)(s[l].fn!==t||o&&!s[l].once||n&&s[l].context!==n)&&_.push(s[l]);_.length?this._events[a]=1===_.length?_[0]:_:i(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?this._events[t=r?r+e:e]&&i(this,t):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s}(At);var dt,Ft=pt(At.exports),ft=function(e){function t(t){var r=t.id,n=t.device,o=e.call(this)||this;return o.id=r,o.device=n,null!==o.device.resourceCreationTracker&&o.device.resourceCreationTracker.trackResourceCreated(o),o}return pe(t,e),t.prototype.destroy=function(){null!==this.device.resourceCreationTracker&&this.device.resourceCreationTracker.trackResourceDestroyed(this)},t}(Ft),mt=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;o.type=e.ResourceType.Bindings;var a=n.samplerBindings;return o.uniformBufferBindings=n.uniformBufferBindings||[],o.samplerBindings=a||[],o.bindingLayouts=o.createBindingLayouts(),o}return pe(r,t),r.prototype.createBindingLayouts=function(){var e=0,t=0,r=[],n=this.uniformBufferBindings.length,o=this.samplerBindings.length;return r.push({firstUniformBuffer:e,numUniformBuffers:n,firstSampler:t,numSamplers:o}),{numUniformBuffers:e+=n,numSamplers:t+=o,bindingLayoutTables:r}},r}(ft);function ht(e){return void 0!==dt?dt:"undefined"!=typeof WebGL2RenderingContext&&e instanceof WebGL2RenderingContext?(dt=!0,!0):dt=!(!e||2!==e._version)}function St(t){switch(I(t)){case e.FormatTypeFlags.BC1:case e.FormatTypeFlags.BC2:case e.FormatTypeFlags.BC3:case e.FormatTypeFlags.BC4_UNORM:case e.FormatTypeFlags.BC4_SNORM:case e.FormatTypeFlags.BC5_UNORM:case e.FormatTypeFlags.BC5_SNORM:return!0;default:return!1}}function gt(t){if(U(t)&e.FormatFlags.Normalized)return!1;var r=I(t);return r===e.FormatTypeFlags.S8||r===e.FormatTypeFlags.S16||r===e.FormatTypeFlags.S32||(r===e.FormatTypeFlags.U8||r===e.FormatTypeFlags.U16||r===e.FormatTypeFlags.U32)}function Bt(t){return t&e.BufferUsage.INDEX?e.GL.ELEMENT_ARRAY_BUFFER:t&e.BufferUsage.VERTEX?e.GL.ARRAY_BUFFER:t&e.BufferUsage.UNIFORM?e.GL.UNIFORM_BUFFER:void 0}function Nt(t){var r=I(t),n=M(t),o=U(t),a=function(t){switch(t){case e.FormatTypeFlags.U8:return e.GL.UNSIGNED_BYTE;case e.FormatTypeFlags.U16:return e.GL.UNSIGNED_SHORT;case e.FormatTypeFlags.U32:return e.GL.UNSIGNED_INT;case e.FormatTypeFlags.S8:return e.GL.BYTE;case e.FormatTypeFlags.S16:return e.GL.SHORT;case e.FormatTypeFlags.S32:return e.GL.INT;case e.FormatTypeFlags.F16:return e.GL.HALF_FLOAT;case e.FormatTypeFlags.F32:return e.GL.FLOAT;default:throw Error("whoops")}}(r),i=function(t){switch(t){case e.FormatCompFlags.R:return 1;case e.FormatCompFlags.RG:return 2;case e.FormatCompFlags.RGB:return 3;case e.FormatCompFlags.RGBA:return 4;default:return 1}}(n);return{size:i,type:a,normalized:!!(o&e.FormatFlags.Normalized)}}function Ct(t){switch(t){case e.AddressMode.CLAMP_TO_EDGE:return e.GL.CLAMP_TO_EDGE;case e.AddressMode.REPEAT:return e.GL.REPEAT;case e.AddressMode.MIRRORED_REPEAT:return e.GL.MIRRORED_REPEAT;default:throw Error("whoops")}}function Lt(t,r){if(r===e.MipmapFilterMode.LINEAR&&t===e.FilterMode.BILINEAR)return e.GL.LINEAR_MIPMAP_LINEAR;if(r===e.MipmapFilterMode.LINEAR&&t===e.FilterMode.POINT)return e.GL.NEAREST_MIPMAP_LINEAR;if(r===e.MipmapFilterMode.NEAREST&&t===e.FilterMode.BILINEAR)return e.GL.LINEAR_MIPMAP_NEAREST;if(r===e.MipmapFilterMode.NEAREST&&t===e.FilterMode.POINT)return e.GL.NEAREST_MIPMAP_NEAREST;if(r===e.MipmapFilterMode.NO_MIP&&t===e.FilterMode.BILINEAR)return e.GL.LINEAR;if(r===e.MipmapFilterMode.NO_MIP&&t===e.FilterMode.POINT)return e.GL.NEAREST;throw Error("Unknown texture filter mode")}function Ot(e,t){void 0===t&&(t=0);return e.gl_buffer_pages[t/e.pageByteSize|0]}function Mt(e){return e.gl_texture}function It(e){return e.gl_sampler}function Ut(e,t){e.name=t,e.__SPECTOR_Metadata={name:t}}function Dt(e,t){for(var r=[];;){var n=t.exec(e);if(!n)break;r.push(n)}return r}function Gt(t){return t.blendMode==e.BlendMode.ADD&&t.blendSrcFactor==e.BlendFactor.ONE&&t.blendDstFactor===e.BlendFactor.ZERO}function Pt(t){if(t===e.TextureDimension.TEXTURE_2D)return e.GL.TEXTURE_2D;if(t===e.TextureDimension.TEXTURE_2D_ARRAY)return e.GL.TEXTURE_2D_ARRAY;if(t===e.TextureDimension.TEXTURE_CUBE_MAP)return e.GL.TEXTURE_CUBE_MAP;if(t===e.TextureDimension.TEXTURE_3D)return e.GL.TEXTURE_3D;throw Error("whoops")}function vt(e,t,r,n){return e%r==0&&t%n==0}var yt,bt=function(t){function r(r){var n=r.device,o=r.descriptor,a=t.call(this,{id:r.id,device:n})||this;a.type=e.ResourceType.Buffer;var i=o.viewOrSize,s=o.usage,l=o.hint,_=void 0===l?e.BufferFrequencyHint.STATIC:l,u=n.uniformBufferMaxPageByteSize,E=n.gl,c=s&e.BufferUsage.UNIFORM;c||(ht(E)?E.bindVertexArray(null):n.OES_vertex_array_object.bindVertexArrayOES(null));var R,T=Le(i)?j(i,4):j(i.byteLength,4);if(a.gl_buffer_pages=[],c){for(var p=T;p>0;)a.gl_buffer_pages.push(a.createBufferPage(Math.min(p,u),s,_)),p-=u;R=u}else a.gl_buffer_pages.push(a.createBufferPage(T,s,_)),R=T;return a.pageByteSize=R,a.byteSize=T,a.usage=s,a.gl_target=Bt(s),Le(i)||a.setSubData(0,new Uint8Array(i.buffer)),c||(ht(E)?E.bindVertexArray(a.device.currentBoundVAO):n.OES_vertex_array_object.bindVertexArrayOES(a.device.currentBoundVAO)),a}return pe(r,t),r.prototype.setSubData=function(e,t,r,n){void 0===r&&(r=0),void 0===n&&(n=t.byteLength-r);for(var o=this.device.gl,a=this.pageByteSize,i=e+n,s=e,l=e%a;i>s;){var _=ht(o)?o.COPY_WRITE_BUFFER:this.gl_target,u=Ot(this,s);if(u.ubo)return;o.bindBuffer(_,u),ht(o)?o.bufferSubData(_,l,t,r,Math.min(i-s,a)):o.bufferSubData(_,l,t),s+=a,l=0,r+=a,this.device.debugGroupStatisticsBufferUpload()}},r.prototype.destroy=function(){t.prototype.destroy.call(this);for(var e=0;this.gl_buffer_pages.length>e;e++)this.gl_buffer_pages[e].ubo||this.device.gl.deleteBuffer(this.gl_buffer_pages[e]);this.gl_buffer_pages=[]},r.prototype.createBufferPage=function(t,r,n){var o=this.device.gl,a=r&e.BufferUsage.UNIFORM;if(!ht(o)&&a)return{ubo:!0};var i=this.device.ensureResourceExists(o.createBuffer()),s=Bt(r),l=function(t){switch(t){case e.BufferFrequencyHint.STATIC:return e.GL.STATIC_DRAW;case e.BufferFrequencyHint.DYNAMIC:return e.GL.DYNAMIC_DRAW}}(n);return o.bindBuffer(s,i),o.bufferData(s,t,l),i},r}(ft),xt=function(t){function r(r){var n,o,a,i,s,l=r.device,_=r.descriptor,u=t.call(this,{id:r.id,device:l})||this;u.type=e.ResourceType.InputLayout;var E=_.vertexBufferDescriptors,c=_.indexBufferFormat,R=_.program;y(c===e.Format.U16_R||c===e.Format.U32_R||null===c);var T=null!==c?function(t){switch(t){case e.Format.U8_R:return e.GL.UNSIGNED_BYTE;case e.Format.U16_R:return e.GL.UNSIGNED_SHORT;case e.Format.U32_R:return e.GL.UNSIGNED_INT;default:throw Error("whoops")}}(c):null,p=null!==c?G(c):null,A=u.device.gl,d=u.device.ensureResourceExists(ht(A)?A.createVertexArray():l.OES_vertex_array_object.createVertexArrayOES());ht(A)?A.bindVertexArray(d):l.OES_vertex_array_object.bindVertexArrayOES(d),A.bindBuffer(A.ARRAY_BUFFER,Ot(u.device.fallbackVertexBuffer));try{for(var F=fe(_.vertexBufferDescriptors),f=F.next();!f.done;f=F.next()){var m=f.value,h=m.stepMode,S=m.attributes;try{for(var g=(a=void 0,fe(S)),B=g.next();!B.done;B=g.next()){var N=B.value,C=N.shaderLocation,L=N.format,O=N.divisor,M=void 0===O?1:O,I=ht(A)?C:null===(s=R.attributes[C])||void 0===s?void 0:s.location,U=Nt(L);if(N.vertexFormat=U,!ge(I))gt(L),A.vertexAttribPointer(I,U.size,U.type,U.normalized,0,0),h===e.VertexStepMode.INSTANCE&&(ht(A)?A.vertexAttribDivisor(I,M):l.ANGLE_instanced_arrays.vertexAttribDivisorANGLE(I,M)),A.enableVertexAttribArray(I)}}catch(e){a={error:e}}finally{try{B&&!B.done&&(i=g.return)&&i.call(g)}finally{if(a)throw a.error}}}}catch(e){n={error:e}}finally{try{f&&!f.done&&(o=F.return)&&o.call(F)}finally{if(n)throw n.error}}return ht(A)?A.bindVertexArray(null):l.OES_vertex_array_object.bindVertexArrayOES(null),u.vertexBufferDescriptors=E,u.vao=d,u.indexBufferFormat=c,u.indexBufferType=T,u.indexBufferCompByteSize=p,u.program=R,u}return pe(r,t),r.prototype.destroy=function(){t.prototype.destroy.call(this),this.device.currentBoundVAO===this.vao&&(ht(this.device.gl)?(this.device.gl.bindVertexArray(null),this.device.gl.deleteVertexArray(this.vao)):(this.device.OES_vertex_array_object.bindVertexArrayOES(null),this.device.OES_vertex_array_object.deleteVertexArrayOES(this.vao)),this.device.currentBoundVAO=null)},r}(ft),Xt=function(t){function r(r){var n=r.device,o=r.descriptor,a=r.fake,i=t.call(this,{id:r.id,device:n})||this;i.type=e.ResourceType.Texture,o=Ae({dimension:e.TextureDimension.TEXTURE_2D,depthOrArrayLayers:1,mipLevelCount:1},o);var s,l,_=i.device.gl,u=i.clampmipLevelCount(o);if(i.immutable=o.usage===e.TextureUsage.RENDER_TARGET,i.pixelStore=o.pixelStore,i.format=o.format,i.dimension=o.dimension,i.formatKind=v(o.format),i.width=o.width,i.height=o.height,i.depthOrArrayLayers=o.depthOrArrayLayers,i.mipmaps=u>=1,!a){l=i.device.ensureResourceExists(_.createTexture());var E=i.device.translateTextureType(o.format),c=i.device.translateTextureInternalFormat(o.format);if(i.device.setActiveTexture(_.TEXTURE0),i.device.currentTextures[0]=null,i.preprocessImage(),o.dimension===e.TextureDimension.TEXTURE_2D){if(_.bindTexture(s=e.GL.TEXTURE_2D,l),i.immutable)if(ht(_))_.texStorage2D(s,u,c,o.width,o.height);else{var R=(c===e.GL.DEPTH_COMPONENT||i.isNPOT(),0);(i.format!==e.Format.D32F&&i.format!==e.Format.D24_S8||ht(_)||n.WEBGL_depth_texture)&&(_.texImage2D(s,R,c,o.width,o.height,0,c,E,null),i.mipmaps&&(i.mipmaps=!1,_.texParameteri(e.GL.TEXTURE_2D,e.GL.TEXTURE_MIN_FILTER,e.GL.LINEAR),_.texParameteri(e.GL.TEXTURE_2D,e.GL.TEXTURE_WRAP_S,e.GL.CLAMP_TO_EDGE),_.texParameteri(e.GL.TEXTURE_2D,e.GL.TEXTURE_WRAP_T,e.GL.CLAMP_TO_EDGE)))}y(1===o.depthOrArrayLayers)}else if(o.dimension===e.TextureDimension.TEXTURE_2D_ARRAY)_.bindTexture(s=e.GL.TEXTURE_2D_ARRAY,l),i.immutable&&ht(_)&&_.texStorage3D(s,u,c,o.width,o.height,o.depthOrArrayLayers);else if(o.dimension===e.TextureDimension.TEXTURE_3D)_.bindTexture(s=e.GL.TEXTURE_3D,l),i.immutable&&ht(_)&&_.texStorage3D(s,u,c,o.width,o.height,o.depthOrArrayLayers);else{if(o.dimension!==e.TextureDimension.TEXTURE_CUBE_MAP)throw Error("whoops");_.bindTexture(s=e.GL.TEXTURE_CUBE_MAP,l),i.immutable&&ht(_)&&_.texStorage2D(s,u,c,o.width,o.height),y(6===o.depthOrArrayLayers)}}return i.gl_texture=l,i.gl_target=s,i.mipLevelCount=u,i}return pe(r,t),r.prototype.setImageData=function(t,r){void 0===r&&(r=0);var n=this.device.gl;St(this.format);var o=this.gl_target===e.GL.TEXTURE_3D||this.gl_target===e.GL.TEXTURE_2D_ARRAY,a=this.gl_target===e.GL.TEXTURE_CUBE_MAP,i=lt(t[0]);this.device.setActiveTexture(n.TEXTURE0),this.device.currentTextures[0]=null;var s,l,_=t[0];i?(s=this.width,l=this.height):(l=_.height,this.width=s=_.width,this.height=l),n.bindTexture(this.gl_target,this.gl_texture);var u=this.device.translateTextureFormat(this.format),E=ht(n)?this.device.translateInternalTextureFormat(this.format):u,c=this.device.translateTextureType(this.format);this.preprocessImage();for(var R=0;this.depthOrArrayLayers>R;R++){var T=t[R],p=this.gl_target;a&&(p=e.GL.TEXTURE_CUBE_MAP_POSITIVE_X+R%6),this.immutable?n.texSubImage2D(p,r,0,0,s,l,u,c,T):ht(n)?o?n.texImage3D(p,r,E,s,l,this.depthOrArrayLayers,0,u,c,T):n.texImage2D(p,r,E,s,l,0,u,c,T):i?n.texImage2D(p,r,u,s,l,0,u,c,T):n.texImage2D(p,r,u,u,c,T)}this.mipmaps&&this.generateMipmap(o)},r.prototype.destroy=function(){t.prototype.destroy.call(this),this.device.gl.deleteTexture(Mt(this))},r.prototype.clampmipLevelCount=function(t){if(t.dimension===e.TextureDimension.TEXTURE_2D_ARRAY&&t.depthOrArrayLayers>1&&I(t.format)===e.FormatTypeFlags.BC1)for(var r=t.width,n=t.height,o=0;t.mipLevelCount>o;o++){if(2>=r||2>=n)return o-1;r=Math.max(r/2|0,1),n=Math.max(n/2|0,1)}return t.mipLevelCount},r.prototype.preprocessImage=function(){var t=this.device.gl;this.pixelStore&&(this.pixelStore.unpackFlipY&&t.pixelStorei(e.GL.UNPACK_FLIP_Y_WEBGL,!0),this.pixelStore.packAlignment&&t.pixelStorei(e.GL.PACK_ALIGNMENT,this.pixelStore.packAlignment),this.pixelStore.unpackAlignment&&t.pixelStorei(e.GL.UNPACK_ALIGNMENT,this.pixelStore.unpackAlignment))},r.prototype.generateMipmap=function(t){void 0===t&&(t=!1);var r=this.device.gl;return!ht(r)&&this.isNPOT()||this.gl_texture&&this.gl_target&&(r.bindTexture(this.gl_target,this.gl_texture),t?(r.texParameteri(this.gl_target,e.GL.TEXTURE_BASE_LEVEL,0),r.texParameteri(this.gl_target,e.GL.TEXTURE_MAX_LEVEL,Math.log2(this.width)),r.texParameteri(this.gl_target,e.GL.TEXTURE_MIN_FILTER,e.GL.LINEAR_MIPMAP_LINEAR),r.texParameteri(this.gl_target,e.GL.TEXTURE_MAG_FILTER,e.GL.LINEAR)):r.texParameteri(e.GL.TEXTURE_2D,e.GL.TEXTURE_MIN_FILTER,e.GL.NEAREST_MIPMAP_LINEAR),r.generateMipmap(this.gl_target),r.bindTexture(this.gl_target,null)),this},r.prototype.isNPOT=function(){return!ht(this.device.gl)&&(!z(this.width)||!z(this.height))},r}(ft),wt=function(t){function r(r){var n=r.device,o=r.descriptor,a=t.call(this,{id:r.id,device:n})||this;a.type=e.ResourceType.RenderTarget,a.gl_renderbuffer=null,a.texture=null;var i=a.device.gl,s=o.format,l=o.width,_=o.height,u=o.sampleCount,E=void 0===u?1:u,c=o.texture,R=!1;if(s!==e.Format.D32F&&s!==e.Format.D24_S8||!c||ht(i)||n.WEBGL_depth_texture||(c.destroy(),a.texture=null,R=!0),!R&&c)a.texture=c;else{a.gl_renderbuffer=a.device.ensureResourceExists(i.createRenderbuffer()),i.bindRenderbuffer(i.RENDERBUFFER,a.gl_renderbuffer);var T=a.device.translateTextureInternalFormat(s,!0);ht(i)&&E>1?i.renderbufferStorageMultisample(e.GL.RENDERBUFFER,E,T,l,_):i.renderbufferStorage(e.GL.RENDERBUFFER,T,l,_)}return a.format=s,a.width=l,a.height=_,a.sampleCount=E,a}return pe(r,t),r.prototype.destroy=function(){t.prototype.destroy.call(this),null!==this.gl_renderbuffer&&this.device.gl.deleteRenderbuffer(this.gl_renderbuffer),this.texture&&this.texture.destroy()},r}(ft);!function(e){e[e.NeedsCompile=0]="NeedsCompile",e[e.Compiling=1]="Compiling",e[e.NeedsBind=2]="NeedsBind",e[e.ReadyToUse=3]="ReadyToUse"}(yt||(yt={}));var Wt=function(t){function r(r,n){var o=r.descriptor,a=t.call(this,{id:r.id,device:r.device})||this;a.rawVertexGLSL=n,a.type=e.ResourceType.Program,a.uniformSetters={},a.attributes=[];var i=a.device.gl;return a.descriptor=o,a.gl_program=a.device.ensureResourceExists(i.createProgram()),a.gl_shader_vert=null,a.gl_shader_frag=null,a.compileState=yt.NeedsCompile,a.tryCompileProgram(),a}return pe(r,t),r.prototype.destroy=function(){t.prototype.destroy.call(this),this.device.gl.deleteProgram(this.gl_program),this.device.gl.deleteShader(this.gl_shader_vert),this.device.gl.deleteShader(this.gl_shader_frag)},r.prototype.tryCompileProgram=function(){y(this.compileState===yt.NeedsCompile);var e=this.descriptor,t=e.vertex,r=e.fragment,n=this.device.gl;(null==t?void 0:t.glsl)&&(null==r?void 0:r.glsl)&&(this.gl_shader_vert=this.compileShader(t.postprocess?t.postprocess(t.glsl):t.glsl,n.VERTEX_SHADER),this.gl_shader_frag=this.compileShader(r.postprocess?r.postprocess(r.glsl):r.glsl,n.FRAGMENT_SHADER),n.attachShader(this.gl_program,this.gl_shader_vert),n.attachShader(this.gl_program,this.gl_shader_frag),n.linkProgram(this.gl_program),this.compileState=yt.Compiling,ht(n)||(this.readUniformLocationsFromLinkedProgram(),this.readAttributesFromLinkedProgram()))},r.prototype.readAttributesFromLinkedProgram=function(){for(var e,t=this.device.gl,r=t.getProgramParameter(this.gl_program,t.ACTIVE_ATTRIBUTES),n=ut(this.descriptor.vertex.glsl),o=Et(this.rawVertexGLSL,n),a=function(r){var n=t.getActiveAttrib(i.gl_program,r),a=n.name,s=n.type,l=n.size,_=t.getAttribLocation(i.gl_program,a),u=null===(e=o.find((function(e){return e.name===a})))||void 0===e?void 0:e.location;0>_||ge(u)||(i.attributes[u]={name:a,location:_,type:s,size:l})},i=this,s=0;r>s;s++)a(s)},r.prototype.readUniformLocationsFromLinkedProgram=function(){for(var e=this.device.gl,t=e.getProgramParameter(this.gl_program,e.ACTIVE_UNIFORMS),r=0;t>r;r++){var n=e.getActiveUniform(this.gl_program,r),o=Ke(n.name).name,a=e.getUniformLocation(this.gl_program,o);if(this.uniformSetters[o]=it(e,a,n),n&&n.size>1)for(var i=0;n.size>i;i++)a=e.getUniformLocation(this.gl_program,"".concat(o,"[").concat(i,"]")),this.uniformSetters["".concat(o,"[").concat(i,"]")]=it(e,a,n)}},r.prototype.compileShader=function(e,t){var r=this.device.gl,n=this.device.ensureResourceExists(r.createShader(t));return r.shaderSource(n,e),r.compileShader(n),n},r.prototype.setUniformsLegacy=function(e){void 0===e&&(e={});var t=this.device.gl;if(!ht(t)){var r=!1;for(var n in e){r||(t.useProgram(this.gl_program),r=!0);var o=this.uniformSetters[n];if(o){var a=e[n];a instanceof Xt&&(a=a.textureIndex),o(a)}}}return this},r}(ft),Ht=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;o.type=e.ResourceType.QueryPool;var a=o.device.gl;if(ht(a)){var i=n.type;o.gl_query=J(n.elemCount,(function(){return o.device.ensureResourceExists(a.createQuery())})),o.gl_query_type=function(t){if(t===e.QueryPoolType.OcclusionConservative)return e.GL.ANY_SAMPLES_PASSED_CONSERVATIVE;throw Error("whoops")}(i)}return o}return pe(r,t),r.prototype.queryResultOcclusion=function(e){var t=this.device.gl;if(ht(t)){var r=this.gl_query[e];return t.getQueryParameter(r,t.QUERY_RESULT_AVAILABLE)?!!t.getQueryParameter(r,t.QUERY_RESULT):null}return null},r.prototype.destroy=function(){t.prototype.destroy.call(this);var e=this.device.gl;if(ht(e))for(var r=0;this.gl_query.length>r;r++)e.deleteQuery(this.gl_query[r])},r}(ft),Vt=function(t){function r(r){var n=t.call(this,{id:r.id,device:r.device})||this;return n.type=e.ResourceType.Readback,n.gl_pbo=null,n.gl_sync=null,n}return pe(r,t),r.prototype.clientWaitAsync=function(e,t,r){void 0===t&&(t=0),void 0===r&&(r=10);var n=this.device.gl;return new Promise((function(o,a){!function i(){var s=n.clientWaitSync(e,t,0);s!=n.WAIT_FAILED?s!=n.TIMEOUT_EXPIRED?o():setTimeout(i,Ce(r,0,n.MAX_CLIENT_WAIT_TIMEOUT_WEBGL)):a()}()}))},r.prototype.getBufferSubDataAsync=function(e,t,r,n,o,a){return de(this,void 0,void 0,(function(){var i;return Fe(this,(function(s){switch(s.label){case 0:return ht(i=this.device.gl)?(this.gl_sync=i.fenceSync(i.SYNC_GPU_COMMANDS_COMPLETE,0),i.flush(),[4,this.clientWaitAsync(this.gl_sync,0,10)]):[3,2];case 1:return s.sent(),i.bindBuffer(e,t),i.getBufferSubData(e,r,n,o,a),i.bindBuffer(e,null),[2,n];case 2:return[2]}}))}))},r.prototype.readTexture=function(t,r,n,o,a,i,s,l){return void 0===s&&(s=0),void 0===l&&(l=i.byteLength||0),de(this,void 0,void 0,(function(){var _,u,E,c,R;return Fe(this,(function(T){return _=this.device.gl,E=this.device.translateTextureFormat((u=t).format),c=this.device.translateTextureType(u.format),R=P(u.format),ht(_)?(this.gl_pbo=this.device.ensureResourceExists(_.createBuffer()),_.bindBuffer(_.PIXEL_PACK_BUFFER,this.gl_pbo),_.bufferData(_.PIXEL_PACK_BUFFER,l,_.STREAM_READ),_.bindBuffer(_.PIXEL_PACK_BUFFER,null),_.bindFramebuffer(e.GL.READ_FRAMEBUFFER,this.device.readbackFramebuffer),_.framebufferTexture2D(e.GL.READ_FRAMEBUFFER,e.GL.COLOR_ATTACHMENT0,e.GL.TEXTURE_2D,u.gl_texture,0),_.bindBuffer(_.PIXEL_PACK_BUFFER,this.gl_pbo),_.readPixels(r,n,o,a,E,c,s*R),_.bindBuffer(_.PIXEL_PACK_BUFFER,null),[2,this.getBufferSubDataAsync(_.PIXEL_PACK_BUFFER,this.gl_pbo,0,i,s,0)]):[2,this.readTextureSync(t,r,n,o,a,i,s,l)]}))}))},r.prototype.readTextureSync=function(t,r,n,o,a,i,s,l){void 0===l&&(l=i.byteLength||0);var _=this.device.gl,u=t,E=this.device.translateTextureType(u.format);return _.bindFramebuffer(e.GL.FRAMEBUFFER,this.device.readbackFramebuffer),_.framebufferTexture2D(e.GL.FRAMEBUFFER,e.GL.COLOR_ATTACHMENT0,e.GL.TEXTURE_2D,u.gl_texture,0),_.pixelStorei(_.PACK_ALIGNMENT,4),_.readPixels(r,n,o,a,_.RGBA,E,i),i},r.prototype.readBuffer=function(e,t,r,n,o){return de(this,void 0,void 0,(function(){var a;return Fe(this,(function(i){return ht(a=this.device.gl)?[2,this.getBufferSubDataAsync(a.ARRAY_BUFFER,Ot(e,t),t,r,n,o)]:[2,Promise.reject()]}))}))},r.prototype.destroy=function(){t.prototype.destroy.call(this),ht(this.device.gl)&&(null!==this.gl_sync&&this.device.gl.deleteSync(this.gl_sync),null!==this.gl_pbo&&this.device.gl.deleteBuffer(this.gl_pbo))},r}(ft),kt=function(t){function r(r){var n,o,a=r.descriptor,i=t.call(this,{id:r.id,device:r.device})||this;return i.type=e.ResourceType.RenderPipeline,i.drawMode=function(t){switch(t){case e.PrimitiveTopology.TRIANGLES:return e.GL.TRIANGLES;case e.PrimitiveTopology.POINTS:return e.GL.POINTS;case e.PrimitiveTopology.TRIANGLE_STRIP:return e.GL.TRIANGLE_STRIP;case e.PrimitiveTopology.LINES:return e.GL.LINES;case e.PrimitiveTopology.LINE_STRIP:return e.GL.LINE_STRIP;default:throw Error("Unknown primitive topology mode")}}(null!==(n=a.topology)&&void 0!==n?n:e.PrimitiveTopology.TRIANGLES),i.program=a.program,i.inputLayout=a.inputLayout,i.megaState=Ae(Ae({},se(ue)),a.megaStateDescriptor),i.colorAttachmentFormats=a.colorAttachmentFormats.slice(),i.depthStencilAttachmentFormat=a.depthStencilAttachmentFormat,i.sampleCount=null!==(o=a.sampleCount)&&void 0!==o?o:1,i}return pe(r,t),r}(ft),Yt=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;return o.type=e.ResourceType.ComputePipeline,o.descriptor=n,o}return pe(r,t),r}(ft),Kt=function(){function e(){this.liveObjects=new Set,this.creationStacks=new Map,this.deletionStacks=new Map}return e.prototype.trackResourceCreated=function(e){this.creationStacks.set(e,Error().stack),this.liveObjects.add(e)},e.prototype.trackResourceDestroyed=function(e){this.deletionStacks.has(e)&&console.warn("Object double freed:",e,"\n\nCreation stack: ",this.creationStacks.get(e),"\n\nDeletion stack: ",this.deletionStacks.get(e),"\n\nThis stack: ",Error().stack),this.deletionStacks.set(e,Error().stack),this.liveObjects.delete(e)},e.prototype.checkForLeaks=function(){var e,t;try{for(var r=fe(this.liveObjects.values()),n=r.next();!n.done;n=r.next()){var o=n.value;console.warn("Object leaked:",o,"Creation stack:",this.creationStacks.get(o))}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},e.prototype.setResourceLeakCheck=function(e,t){t?this.liveObjects.add(e):this.liveObjects.delete(e)},e}(),qt=function(t){function r(r){var n,o,a=r.descriptor,i=t.call(this,{id:r.id,device:r.device})||this;i.type=e.ResourceType.Sampler;var s=i.device.gl;if(ht(s)){var l=i.device.ensureResourceExists(s.createSampler());s.samplerParameteri(l,e.GL.TEXTURE_WRAP_S,Ct(a.addressModeU)),s.samplerParameteri(l,e.GL.TEXTURE_WRAP_T,Ct(a.addressModeV)),s.samplerParameteri(l,e.GL.TEXTURE_WRAP_R,Ct(null!==(n=a.addressModeW)&&void 0!==n?n:a.addressModeU)),s.samplerParameteri(l,e.GL.TEXTURE_MIN_FILTER,Lt(a.minFilter,a.mipmapFilter)),s.samplerParameteri(l,e.GL.TEXTURE_MAG_FILTER,Lt(a.magFilter,e.MipmapFilterMode.NO_MIP)),void 0!==a.lodMinClamp&&s.samplerParameterf(l,e.GL.TEXTURE_MIN_LOD,a.lodMinClamp),void 0!==a.lodMaxClamp&&s.samplerParameterf(l,e.GL.TEXTURE_MAX_LOD,a.lodMaxClamp),void 0!==a.compareFunction&&(s.samplerParameteri(l,s.TEXTURE_COMPARE_MODE,s.COMPARE_REF_TO_TEXTURE),s.samplerParameteri(l,s.TEXTURE_COMPARE_FUNC,a.compareFunction));var _=null!==(o=a.maxAnisotropy)&&void 0!==o?o:1;_>1&&null!==i.device.EXT_texture_filter_anisotropic&&(y(a.minFilter===e.FilterMode.BILINEAR&&a.magFilter===e.FilterMode.BILINEAR&&a.mipmapFilter===e.MipmapFilterMode.LINEAR),s.samplerParameterf(l,i.device.EXT_texture_filter_anisotropic.TEXTURE_MAX_ANISOTROPY_EXT,_)),i.gl_sampler=l}else i.descriptor=a;return i}return pe(r,t),r.prototype.setTextureParameters=function(t,r,n){var o,a=this.device.gl,i=this.descriptor;this.isNPOT(r,n)?a.texParameteri(e.GL.TEXTURE_2D,e.GL.TEXTURE_MIN_FILTER,e.GL.LINEAR):a.texParameteri(t,e.GL.TEXTURE_MIN_FILTER,Lt(i.minFilter,i.mipmapFilter)),a.texParameteri(e.GL.TEXTURE_2D,e.GL.TEXTURE_WRAP_S,Ct(i.addressModeU)),a.texParameteri(e.GL.TEXTURE_2D,e.GL.TEXTURE_WRAP_T,Ct(i.addressModeV)),a.texParameteri(t,e.GL.TEXTURE_MAG_FILTER,Lt(i.magFilter,e.MipmapFilterMode.NO_MIP));var s=null!==(o=i.maxAnisotropy)&&void 0!==o?o:1;s>1&&null!==this.device.EXT_texture_filter_anisotropic&&(y(i.minFilter===e.FilterMode.BILINEAR&&i.magFilter===e.FilterMode.BILINEAR&&i.mipmapFilter===e.MipmapFilterMode.LINEAR),a.texParameteri(t,this.device.EXT_texture_filter_anisotropic.TEXTURE_MAX_ANISOTROPY_EXT,s))},r.prototype.destroy=function(){t.prototype.destroy.call(this),ht(this.device.gl)&&this.device.gl.deleteSampler(It(this))},r.prototype.isNPOT=function(e,t){return!z(e)||!z(t)},r}(ft),zt=function(){function e(){}return e.prototype.dispatchWorkgroups=function(e,t,r){},e.prototype.dispatchWorkgroupsIndirect=function(e,t){},e.prototype.setPipeline=function(e){},e.prototype.setBindings=function(e){},e.prototype.pushDebugGroup=function(e){},e.prototype.popDebugGroup=function(){},e.prototype.insertDebugMarker=function(e){},e}(),Zt=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type=e.ResourceType.RenderBundle,r.commands=[],r}return pe(r,t),r.prototype.push=function(e){this.commands.push(e)},r.prototype.replay=function(){this.commands.forEach((function(e){return e()}))},r}(ft),Qt=/uniform(?:\s+)(\w+)(?:\s?){([^]*?)}/g,jt=function(){function t(t,r){void 0===r&&(r={}),this.shaderDebug=!1,this.OES_vertex_array_object=null,this.ANGLE_instanced_arrays=null,this.OES_texture_float=null,this.OES_draw_buffers_indexed=null,this.WEBGL_draw_buffers=null,this.WEBGL_depth_texture=null,this.WEBGL_color_buffer_float=null,this.EXT_color_buffer_half_float=null,this.WEBGL_compressed_texture_s3tc=null,this.WEBGL_compressed_texture_s3tc_srgb=null,this.EXT_texture_compression_rgtc=null,this.EXT_texture_filter_anisotropic=null,this.KHR_parallel_shader_compile=null,this.EXT_texture_norm16=null,this.EXT_color_buffer_float=null,this.OES_texture_float_linear=null,this.OES_texture_half_float_linear=null,this.scTexture=null,this.scPlatformFramebuffer=null,this.currentActiveTexture=null,this.currentBoundVAO=null,this.currentProgram=null,this.resourceCreationTracker=null,this.resourceUniqueId=0,this.currentColorAttachments=[],this.currentColorAttachmentLevels=[],this.currentColorResolveTos=[],this.currentColorResolveToLevels=[],this.currentSampleCount=-1,this.currentIndexBufferByteOffset=null,this.currentMegaState=se(ue),this.currentSamplers=[],this.currentTextures=[],this.currentUniformBuffers=[],this.currentUniformBufferByteOffsets=[],this.currentUniformBufferByteSizes=[],this.currentScissorEnabled=!1,this.currentStencilRef=null,this.currentRenderPassDescriptor=null,this.currentRenderPassDescriptorStack=[],this.debugGroupStack=[],this.resolveColorAttachmentsChanged=!1,this.resolveDepthStencilAttachmentsChanged=!1,this.explicitBindingLocations=!1,this.separateSamplerTextures=!1,this.viewportOrigin=e.ViewportOrigin.LOWER_LEFT,this.clipSpaceNearZ=e.ClipSpaceNearZ.NEGATIVE_ONE,this.supportMRT=!1,this.inBlitRenderPass=!1,this.supportedSampleCounts=[],this.occlusionQueriesRecommended=!1,this.computeShadersSupported=!1,this.gl=t,this.contextAttributes=b(t.getContextAttributes()),ht(t)?(this.EXT_texture_norm16=t.getExtension("EXT_texture_norm16"),this.EXT_color_buffer_float=t.getExtension("EXT_color_buffer_float")):(this.OES_vertex_array_object=t.getExtension("OES_vertex_array_object"),this.ANGLE_instanced_arrays=t.getExtension("ANGLE_instanced_arrays"),this.OES_texture_float=t.getExtension("OES_texture_float"),this.WEBGL_draw_buffers=t.getExtension("WEBGL_draw_buffers"),this.WEBGL_depth_texture=t.getExtension("WEBGL_depth_texture"),this.WEBGL_color_buffer_float=t.getExtension("WEBGL_color_buffer_float"),this.EXT_color_buffer_half_float=t.getExtension("EXT_color_buffer_half_float"),t.getExtension("EXT_frag_depth"),t.getExtension("OES_element_index_uint"),t.getExtension("OES_standard_derivatives")),this.WEBGL_compressed_texture_s3tc=t.getExtension("WEBGL_compressed_texture_s3tc"),this.WEBGL_compressed_texture_s3tc_srgb=t.getExtension("WEBGL_compressed_texture_s3tc_srgb"),this.EXT_texture_compression_rgtc=t.getExtension("EXT_texture_compression_rgtc"),this.EXT_texture_filter_anisotropic=t.getExtension("EXT_texture_filter_anisotropic"),this.EXT_texture_norm16=t.getExtension("EXT_texture_norm16"),this.OES_texture_float_linear=t.getExtension("OES_texture_float_linear"),this.OES_texture_half_float_linear=t.getExtension("OES_texture_half_float_linear"),this.KHR_parallel_shader_compile=t.getExtension("KHR_parallel_shader_compile"),ht(t)?(this.platformString="WebGL2",this.glslVersion="#version 300 es"):(this.platformString="WebGL1",this.glslVersion="#version 100"),this.scTexture=new Xt({id:this.getNextUniqueId(),device:this,descriptor:{width:0,height:0,depthOrArrayLayers:1,dimension:e.TextureDimension.TEXTURE_2D,mipLevelCount:1,usage:e.TextureUsage.RENDER_TARGET,format:!1===this.contextAttributes.alpha?e.Format.U8_RGB_RT:e.Format.U8_RGBA_RT},fake:!0}),this.scTexture.formatKind=e.SamplerFormatKind.Float,this.scTexture.gl_target=null,this.scTexture.gl_texture=null,this.resolveColorReadFramebuffer=this.ensureResourceExists(t.createFramebuffer()),this.resolveColorDrawFramebuffer=this.ensureResourceExists(t.createFramebuffer()),this.resolveDepthStencilReadFramebuffer=this.ensureResourceExists(t.createFramebuffer()),this.resolveDepthStencilDrawFramebuffer=this.ensureResourceExists(t.createFramebuffer()),this.renderPassDrawFramebuffer=this.ensureResourceExists(t.createFramebuffer()),this.readbackFramebuffer=this.ensureResourceExists(t.createFramebuffer()),this.fallbackTexture2D=this.createFallbackTexture(e.TextureDimension.TEXTURE_2D,e.SamplerFormatKind.Float),this.fallbackTexture2DDepth=this.createFallbackTexture(e.TextureDimension.TEXTURE_2D,e.SamplerFormatKind.Depth),this.fallbackVertexBuffer=this.createBuffer({viewOrSize:1,usage:e.BufferUsage.VERTEX,hint:e.BufferFrequencyHint.STATIC}),ht(t)&&(this.fallbackTexture2DArray=this.createFallbackTexture(e.TextureDimension.TEXTURE_2D_ARRAY,e.SamplerFormatKind.Float),this.fallbackTexture3D=this.createFallbackTexture(e.TextureDimension.TEXTURE_3D,e.SamplerFormatKind.Float),this.fallbackTextureCube=this.createFallbackTexture(e.TextureDimension.TEXTURE_CUBE_MAP,e.SamplerFormatKind.Float)),this.currentMegaState.depthCompare=e.CompareFunction.LESS,this.currentMegaState.depthWrite=!1,this.currentMegaState.attachmentsState[0].channelWriteMask=e.ChannelWriteMask.ALL,t.enable(t.DEPTH_TEST),t.enable(t.STENCIL_TEST),this.checkLimits(),r.shaderDebug&&(this.shaderDebug=!0),r.trackResources&&(this.resourceCreationTracker=new Kt)}return t.prototype.destroy=function(){this.blitBindings&&this.blitBindings.destroy(),this.blitInputLayout&&this.blitInputLayout.destroy(),this.blitRenderPipeline&&this.blitRenderPipeline.destroy(),this.blitVertexBuffer&&this.blitVertexBuffer.destroy(),this.blitProgram&&this.blitProgram.destroy()},t.prototype.createFallbackTexture=function(t,r){var n=t===e.TextureDimension.TEXTURE_CUBE_MAP?6:1,o=this.createTexture({dimension:t,format:r===e.SamplerFormatKind.Depth?e.Format.D32F:e.Format.U8_RGBA_NORM,usage:e.TextureUsage.SAMPLED,width:1,height:1,depthOrArrayLayers:n,mipLevelCount:1});return r===e.SamplerFormatKind.Float&&o.setImageData([new Uint8Array(4*n)]),Mt(o)},t.prototype.getNextUniqueId=function(){return++this.resourceUniqueId},t.prototype.checkLimits=function(){var t=this.gl;if(this.maxVertexAttribs=t.getParameter(e.GL.MAX_VERTEX_ATTRIBS),ht(t)){this.uniformBufferMaxPageByteSize=Math.min(t.getParameter(e.GL.MAX_UNIFORM_BLOCK_SIZE),65536),this.uniformBufferWordAlignment=t.getParameter(t.UNIFORM_BUFFER_OFFSET_ALIGNMENT)/4;var r=t.getInternalformatParameter(t.RENDERBUFFER,t.DEPTH32F_STENCIL8,t.SAMPLES);this.supportedSampleCounts=r?he([],me(r),!1):[],this.occlusionQueriesRecommended=!0}else this.uniformBufferWordAlignment=64,this.uniformBufferMaxPageByteSize=65536;this.uniformBufferMaxPageWordSize=this.uniformBufferMaxPageByteSize/4,this.supportedSampleCounts.includes(1)||this.supportedSampleCounts.push(1),this.supportedSampleCounts.sort((function(e,t){return e-t}))},t.prototype.configureSwapChain=function(e,t,r){var n=this.scTexture;n.width=e,n.height=t,this.scPlatformFramebuffer=Q(r)},t.prototype.getDevice=function(){return this},t.prototype.getCanvas=function(){return this.gl.canvas},t.prototype.getOnscreenTexture=function(){return this.scTexture},t.prototype.beginFrame=function(){},t.prototype.endFrame=function(){},t.prototype.translateTextureInternalFormat=function(t,r){switch(void 0===r&&(r=!1),t){case e.Format.ALPHA:return e.GL.ALPHA;case e.Format.U8_LUMINANCE:case e.Format.F16_LUMINANCE:case e.Format.F32_LUMINANCE:return e.GL.LUMINANCE;case e.Format.F16_R:return e.GL.R16F;case e.Format.F16_RG:return e.GL.RG16F;case e.Format.F16_RGB:return e.GL.RGB16F;case e.Format.F16_RGBA:return e.GL.RGBA16F;case e.Format.F32_R:return e.GL.R32F;case e.Format.F32_RG:return e.GL.RG32F;case e.Format.F32_RGB:return e.GL.RGB32F;case e.Format.F32_RGBA:return ht(this.gl)?e.GL.RGBA32F:r?this.WEBGL_color_buffer_float.RGBA32F_EXT:e.GL.RGBA;case e.Format.U8_R_NORM:return e.GL.R8;case e.Format.U8_RG_NORM:return e.GL.RG8;case e.Format.U8_RGB_NORM:case e.Format.U8_RGB_RT:return e.GL.RGB8;case e.Format.U8_RGB_SRGB:return e.GL.SRGB8;case e.Format.U8_RGBA_NORM:case e.Format.U8_RGBA_RT:return ht(this.gl)?e.GL.RGBA8:r?e.GL.RGBA4:e.GL.RGBA;case e.Format.U8_RGBA:return e.GL.RGBA;case e.Format.U8_RGBA_SRGB:case e.Format.U8_RGBA_RT_SRGB:return e.GL.SRGB8_ALPHA8;case e.Format.U16_R:return e.GL.R16UI;case e.Format.U16_R_NORM:return this.EXT_texture_norm16.R16_EXT;case e.Format.U16_RG_NORM:return this.EXT_texture_norm16.RG16_EXT;case e.Format.U16_RGBA_NORM:return this.EXT_texture_norm16.RGBA16_EXT;case e.Format.U16_RGBA_5551:return e.GL.RGB5_A1;case e.Format.U16_RGB_565:return e.GL.RGB565;case e.Format.U32_R:return e.GL.R32UI;case e.Format.S8_RGBA_NORM:return e.GL.RGBA8_SNORM;case e.Format.S8_RG_NORM:return e.GL.RG8_SNORM;case e.Format.BC1:return this.WEBGL_compressed_texture_s3tc.COMPRESSED_RGBA_S3TC_DXT1_EXT;case e.Format.BC1_SRGB:return this.WEBGL_compressed_texture_s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;case e.Format.BC2:return this.WEBGL_compressed_texture_s3tc.COMPRESSED_RGBA_S3TC_DXT3_EXT;case e.Format.BC2_SRGB:return this.WEBGL_compressed_texture_s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT;case e.Format.BC3:return this.WEBGL_compressed_texture_s3tc.COMPRESSED_RGBA_S3TC_DXT5_EXT;case e.Format.BC3_SRGB:return this.WEBGL_compressed_texture_s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT;case e.Format.BC4_UNORM:return this.EXT_texture_compression_rgtc.COMPRESSED_RED_RGTC1_EXT;case e.Format.BC4_SNORM:return this.EXT_texture_compression_rgtc.COMPRESSED_SIGNED_RED_RGTC1_EXT;case e.Format.BC5_UNORM:return this.EXT_texture_compression_rgtc.COMPRESSED_RED_GREEN_RGTC2_EXT;case e.Format.BC5_SNORM:return this.EXT_texture_compression_rgtc.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT;case e.Format.D32F_S8:return ht(this.gl)?e.GL.DEPTH32F_STENCIL8:this.WEBGL_depth_texture?e.GL.DEPTH_STENCIL:e.GL.DEPTH_COMPONENT16;case e.Format.D24_S8:return ht(this.gl)?e.GL.DEPTH24_STENCIL8:this.WEBGL_depth_texture?e.GL.DEPTH_STENCIL:e.GL.DEPTH_COMPONENT16;case e.Format.D32F:return ht(this.gl)?e.GL.DEPTH_COMPONENT32F:this.WEBGL_depth_texture?e.GL.DEPTH_COMPONENT:e.GL.DEPTH_COMPONENT16;case e.Format.D24:return ht(this.gl)?e.GL.DEPTH_COMPONENT24:this.WEBGL_depth_texture?e.GL.DEPTH_COMPONENT:e.GL.DEPTH_COMPONENT16;default:throw Error("whoops")}},t.prototype.translateTextureType=function(t){switch(I(t)){case e.FormatTypeFlags.U8:return e.GL.UNSIGNED_BYTE;case e.FormatTypeFlags.U16:return e.GL.UNSIGNED_SHORT;case e.FormatTypeFlags.U32:return e.GL.UNSIGNED_INT;case e.FormatTypeFlags.S8:return e.GL.BYTE;case e.FormatTypeFlags.F16:return e.GL.HALF_FLOAT;case e.FormatTypeFlags.F32:return e.GL.FLOAT;case e.FormatTypeFlags.U16_PACKED_5551:return e.GL.UNSIGNED_SHORT_5_5_5_1;case e.FormatTypeFlags.D32F:return ht(this.gl)?e.GL.FLOAT:this.WEBGL_depth_texture?e.GL.UNSIGNED_INT:e.GL.UNSIGNED_BYTE;case e.FormatTypeFlags.D24:return ht(this.gl)?e.GL.UNSIGNED_INT_24_8:this.WEBGL_depth_texture?e.GL.UNSIGNED_SHORT:e.GL.UNSIGNED_BYTE;case e.FormatTypeFlags.D24S8:return ht(this.gl)?e.GL.UNSIGNED_INT_24_8:this.WEBGL_depth_texture?e.GL.UNSIGNED_INT_24_8_WEBGL:e.GL.UNSIGNED_BYTE;case e.FormatTypeFlags.D32FS8:return e.GL.FLOAT_32_UNSIGNED_INT_24_8_REV;default:throw Error("whoops")}},t.prototype.translateInternalTextureFormat=function(t){switch(t){case e.Format.F32_R:return e.GL.R32F;case e.Format.F32_RG:return e.GL.RG32F;case e.Format.F32_RGB:return e.GL.RGB32F;case e.Format.F32_RGBA:return e.GL.RGBA32F;case e.Format.F16_R:return e.GL.R16F;case e.Format.F16_RG:return e.GL.RG16F;case e.Format.F16_RGB:return e.GL.RGB16F;case e.Format.F16_RGBA:return e.GL.RGBA16F}return this.translateTextureFormat(t)},t.prototype.translateTextureFormat=function(t){if(St(t)||t===e.Format.F32_LUMINANCE||t===e.Format.U8_LUMINANCE)return this.translateTextureInternalFormat(t);var r=ht(this.gl)||!ht(this.gl)&&!!this.WEBGL_depth_texture;switch(t){case e.Format.D24_S8:case e.Format.D32F_S8:return r?e.GL.DEPTH_STENCIL:e.GL.RGBA;case e.Format.D24:case e.Format.D32F:return r?e.GL.DEPTH_COMPONENT:e.GL.RGBA}var n=gt(t);switch(M(t)){case e.FormatCompFlags.A:return e.GL.ALPHA;case e.FormatCompFlags.R:return n?e.GL.RED_INTEGER:e.GL.RED;case e.FormatCompFlags.RG:return n?e.GL.RG_INTEGER:e.GL.RG;case e.FormatCompFlags.RGB:return n?e.GL.RGB_INTEGER:e.GL.RGB;case e.FormatCompFlags.RGBA:return e.GL.RGBA}},t.prototype.setActiveTexture=function(e){this.currentActiveTexture!==e&&(this.gl.activeTexture(e),this.currentActiveTexture=e)},t.prototype.bindVAO=function(e){this.currentBoundVAO!==e&&(ht(this.gl)?this.gl.bindVertexArray(e):this.OES_vertex_array_object.bindVertexArrayOES(e),this.currentBoundVAO=e)},t.prototype.programCompiled=function(e){y(e.compileState!==yt.NeedsCompile),e.compileState===yt.Compiling&&(e.compileState=yt.NeedsBind,this.shaderDebug&&this.checkProgramCompilationForErrors(e))},t.prototype.useProgram=function(e){this.currentProgram!==e&&(this.programCompiled(e),this.gl.useProgram(e.gl_program),this.currentProgram=e)},t.prototype.ensureResourceExists=function(e){if(null===e){var t=this.gl.getError();throw Error("Created resource is null; GL error encountered: ".concat(t))}return e},t.prototype.createBuffer=function(e){return new bt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createTexture=function(e){return new Xt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createSampler=function(e){return new qt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createRenderTarget=function(e){return new wt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createRenderTargetFromTexture=function(e){var t=e.format,r=e.width,n=e.height;return y(1===e.mipLevelCount),this.createRenderTarget({format:t,width:r,height:n,sampleCount:1,texture:e})},t.prototype.createProgram=function(e){var t,r,n,o=null===(t=e.vertex)||void 0===t?void 0:t.glsl;return(null===(r=e.vertex)||void 0===r?void 0:r.glsl)&&(e.vertex.glsl=Tt(this.queryVendorInfo(),"vert",e.vertex.glsl)),(null===(n=e.fragment)||void 0===n?void 0:n.glsl)&&(e.fragment.glsl=Tt(this.queryVendorInfo(),"frag",e.fragment.glsl)),this.createProgramSimple(e,o)},t.prototype.createProgramSimple=function(e,t){return new Wt({id:this.getNextUniqueId(),device:this,descriptor:e},t)},t.prototype.createBindings=function(e){return new mt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createInputLayout=function(e){return new xt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createRenderPipeline=function(e){return new kt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createComputePass=function(){return new zt},t.prototype.createComputePipeline=function(e){return new Yt({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createReadback=function(){return new Vt({id:this.getNextUniqueId(),device:this})},t.prototype.createQueryPool=function(e,t){return new Ht({id:this.getNextUniqueId(),device:this,descriptor:{type:e,elemCount:t}})},t.prototype.formatRenderPassDescriptor=function(e){var t,r,n,o,a,i,s=e.colorAttachment;e.depthClearValue=null!==(t=e.depthClearValue)&&void 0!==t?t:"load",e.stencilClearValue=null!==(r=e.stencilClearValue)&&void 0!==r?r:"load";for(var l=0;s.length>l;l++)e.colorAttachmentLevel||(e.colorAttachmentLevel=[]),e.colorAttachmentLevel[l]=null!==(n=e.colorAttachmentLevel[l])&&void 0!==n?n:0,e.colorResolveToLevel||(e.colorResolveToLevel=[]),e.colorResolveToLevel[l]=null!==(o=e.colorResolveToLevel[l])&&void 0!==o?o:0,e.colorClearColor||(e.colorClearColor=[]),e.colorClearColor[l]=null!==(a=e.colorClearColor[l])&&void 0!==a?a:"load",e.colorStore||(e.colorStore=[]),e.colorStore[l]=null!==(i=e.colorStore[l])&&void 0!==i&&i},t.prototype.createRenderBundle=function(){return new Zt({id:this.getNextUniqueId(),device:this})},t.prototype.beginBundle=function(e){this.renderBundle=e},t.prototype.endBundle=function(){this.renderBundle=void 0},t.prototype.executeBundles=function(e){e.forEach((function(e){e.replay()}))},t.prototype.createRenderPass=function(e){null!==this.currentRenderPassDescriptor&&this.currentRenderPassDescriptorStack.push(this.currentRenderPassDescriptor),this.currentRenderPassDescriptor=e,this.formatRenderPassDescriptor(e);var t=e.colorAttachment,r=e.colorAttachmentLevel,n=e.colorClearColor,o=e.colorResolveTo,a=e.colorResolveToLevel,i=e.depthStencilAttachment,s=e.depthClearValue,l=e.stencilClearValue,_=e.depthStencilResolveTo,u=o&&1===o.length&&o[0]===this.scTexture;this.setRenderPassParametersBegin(t.length,u);for(var E=0;t.length>E;E++)this.setRenderPassParametersColor(E,t[E],r[E],o[E],a[E],u);this.setRenderPassParametersDepthStencil(i,_,u),this.validateCurrentAttachments();for(E=0;t.length>E;E++){var c=n[E];"load"!==c&&this.setRenderPassParametersClearColor(E,c.r,c.g,c.b,c.a)}return this.setRenderPassParametersClearDepthStencil(s,l),this},t.prototype.submitPass=function(e){y(null!==this.currentRenderPassDescriptor),this.endPass(),this.currentRenderPassDescriptor=this.currentRenderPassDescriptorStack.length?this.currentRenderPassDescriptorStack.pop():null},t.prototype.copySubTexture2D=function(e,t,r,n,o,a){var i=this.gl,s=e,l=n;if(y(1===l.mipLevelCount),y(1===s.mipLevelCount),ht(i))s===this.scTexture?i.bindFramebuffer(i.DRAW_FRAMEBUFFER,this.scPlatformFramebuffer):(i.bindFramebuffer(i.DRAW_FRAMEBUFFER,this.resolveColorDrawFramebuffer),this.bindFramebufferAttachment(i.DRAW_FRAMEBUFFER,i.COLOR_ATTACHMENT0,s,0)),i.bindFramebuffer(i.READ_FRAMEBUFFER,this.resolveColorReadFramebuffer),this.bindFramebufferAttachment(i.READ_FRAMEBUFFER,i.COLOR_ATTACHMENT0,l,0),i.blitFramebuffer(o,a,o+l.width,a+l.height,t,r,t+l.width,r+l.height,i.COLOR_BUFFER_BIT,i.LINEAR),i.bindFramebuffer(i.READ_FRAMEBUFFER,null),i.bindFramebuffer(i.DRAW_FRAMEBUFFER,null);else if(s===this.scTexture){var _=this.createRenderTargetFromTexture(n);this.submitBlitRenderPass(_,s)}},t.prototype.queryLimits=function(){return this},t.prototype.queryTextureFormatSupported=function(t,r,n){switch(t){case e.Format.BC1_SRGB:case e.Format.BC2_SRGB:case e.Format.BC3_SRGB:return null!==this.WEBGL_compressed_texture_s3tc_srgb&&vt(r,n,4,4);case e.Format.BC1:case e.Format.BC2:case e.Format.BC3:return null!==this.WEBGL_compressed_texture_s3tc&&vt(r,n,4,4);case e.Format.BC4_UNORM:case e.Format.BC4_SNORM:case e.Format.BC5_UNORM:case e.Format.BC5_SNORM:return null!==this.EXT_texture_compression_rgtc&&vt(r,n,4,4);case e.Format.U16_R_NORM:case e.Format.U16_RG_NORM:case e.Format.U16_RGBA_NORM:return null!==this.EXT_texture_norm16;case e.Format.F32_R:case e.Format.F32_RG:case e.Format.F32_RGB:case e.Format.F32_RGBA:return null!==this.OES_texture_float_linear;case e.Format.F16_R:case e.Format.F16_RG:case e.Format.F16_RGB:case e.Format.F16_RGBA:return null!==this.OES_texture_half_float_linear;default:return!0}},t.prototype.queryProgramReady=function(e){var t=this.gl;if(e.compileState===yt.NeedsCompile)throw Error("whoops");if(e.compileState===yt.Compiling){var r=void 0;return(r=null===this.KHR_parallel_shader_compile||t.getProgramParameter(e.gl_program,this.KHR_parallel_shader_compile.COMPLETION_STATUS_KHR))&&this.programCompiled(e),r}return e.compileState===yt.NeedsBind||e.compileState===yt.ReadyToUse},t.prototype.queryPlatformAvailable=function(){return this.gl.isContextLost()},t.prototype.queryVendorInfo=function(){return this},t.prototype.queryRenderPass=function(e){return this.currentRenderPassDescriptor},t.prototype.queryRenderTarget=function(e){return e},t.prototype.setResourceName=function(t,r){if(t.name=r,t.type===e.ResourceType.Buffer)for(var n=t.gl_buffer_pages,o=0;n.length>o;o++)Ut(n[o],"".concat(r," Page ").concat(o));else if(t.type===e.ResourceType.Texture)Ut(Mt(t),r);else if(t.type===e.ResourceType.Sampler)Ut(It(t),r);else if(t.type===e.ResourceType.RenderTarget){var a=t.gl_renderbuffer;null!==a&&Ut(a,r)}else t.type===e.ResourceType.InputLayout&&Ut(t.vao,r)},t.prototype.setResourceLeakCheck=function(e,t){null!==this.resourceCreationTracker&&this.resourceCreationTracker.setResourceLeakCheck(e,t)},t.prototype.checkForLeaks=function(){null!==this.resourceCreationTracker&&this.resourceCreationTracker.checkForLeaks()},t.prototype.pushDebugGroup=function(e){},t.prototype.popDebugGroup=function(){},t.prototype.insertDebugMarker=function(e){},t.prototype.programPatched=function(e,t){y(this.shaderDebug)},t.prototype.getBufferData=function(e,t,r){void 0===r&&(r=0);var n=this.gl;ht(n)&&(n.bindBuffer(n.COPY_READ_BUFFER,Ot(e,4*r)),n.getBufferSubData(n.COPY_READ_BUFFER,4*r,t))},t.prototype.debugGroupStatisticsDrawCall=function(e){void 0===e&&(e=1);for(var t=this.debugGroupStack.length-1;t>=0;t--)this.debugGroupStack[t].drawCallCount+=e},t.prototype.debugGroupStatisticsBufferUpload=function(e){void 0===e&&(e=1);for(var t=this.debugGroupStack.length-1;t>=0;t--)this.debugGroupStack[t].bufferUploadCount+=e},t.prototype.debugGroupStatisticsTextureBind=function(e){void 0===e&&(e=1);for(var t=this.debugGroupStack.length-1;t>=0;t--)this.debugGroupStack[t].textureBindCount+=e},t.prototype.debugGroupStatisticsTriangles=function(e){for(var t=this.debugGroupStack.length-1;t>=0;t--)this.debugGroupStack[t].triangleCount+=e},t.prototype.reportShaderError=function(e,t){var r=this.gl,n=r.getShaderParameter(e,r.COMPILE_STATUS);if(!n){console.error(ee(t));var o=r.getExtension("WEBGL_debug_shaders");o&&console.error(o.getTranslatedShaderSource(e)),console.error(r.getShaderInfoLog(e))}return n},t.prototype.checkProgramCompilationForErrors=function(e){var t=this.gl;if(!t.getProgramParameter(e.gl_program,t.LINK_STATUS)){var r=e.descriptor;if(!this.reportShaderError(e.gl_shader_vert,r.vertex.glsl))return;if(!this.reportShaderError(e.gl_shader_frag,r.fragment.glsl))return;console.error(t.getProgramInfoLog(e.gl_program))}},t.prototype.bindFramebufferAttachment=function(t,r,n,o){var a=this.gl;if(ge(n))a.framebufferRenderbuffer(t,r,a.RENDERBUFFER,null);else if(n.type===e.ResourceType.RenderTarget)null!==n.gl_renderbuffer?a.framebufferRenderbuffer(t,r,a.RENDERBUFFER,n.gl_renderbuffer):null!==n.texture&&a.framebufferTexture2D(t,r,e.GL.TEXTURE_2D,Mt(n.texture),o);else if(n.type===e.ResourceType.Texture){var i=Mt(n);n.dimension===e.TextureDimension.TEXTURE_2D?a.framebufferTexture2D(t,r,e.GL.TEXTURE_2D,i,o):ht(a)}},t.prototype.bindFramebufferDepthStencilAttachment=function(t,r){var n=this.gl,o=ge(r)?e.FormatFlags.Depth|e.FormatFlags.Stencil:U(r.format),a=!!(o&e.FormatFlags.Depth),i=!!(o&e.FormatFlags.Stencil);if(a&&i){var s=ht(this.gl)||!ht(this.gl)&&!!this.WEBGL_depth_texture;this.bindFramebufferAttachment(t,s?n.DEPTH_STENCIL_ATTACHMENT:n.DEPTH_ATTACHMENT,r,0)}else a?(this.bindFramebufferAttachment(t,n.DEPTH_ATTACHMENT,r,0),this.bindFramebufferAttachment(t,n.STENCIL_ATTACHMENT,null,0)):i&&(this.bindFramebufferAttachment(t,n.STENCIL_ATTACHMENT,r,0),this.bindFramebufferAttachment(t,n.DEPTH_ATTACHMENT,null,0))},t.prototype.validateCurrentAttachments=function(){for(var e=-1,t=-1,r=-1,n=0;this.currentColorAttachments.length>n;n++){var o=this.currentColorAttachments[n];null!==o&&(-1===e?(e=o.sampleCount,t=o.width,r=o.height):(y(e===o.sampleCount),y(t===o.width),y(r===o.height)))}this.currentDepthStencilAttachment&&(-1===e?e=this.currentDepthStencilAttachment.sampleCount:(y(e===this.currentDepthStencilAttachment.sampleCount),y(t===this.currentDepthStencilAttachment.width),y(r===this.currentDepthStencilAttachment.height))),this.currentSampleCount=e},t.prototype.setRenderPassParametersBegin=function(t,r){void 0===r&&(r=!1);var n=this.gl;if(r)n.bindFramebuffer(e.GL.FRAMEBUFFER,null);else if(ht(n)?n.bindFramebuffer(e.GL.DRAW_FRAMEBUFFER,this.renderPassDrawFramebuffer):this.inBlitRenderPass||n.bindFramebuffer(e.GL.FRAMEBUFFER,this.renderPassDrawFramebuffer),ht(n)?n.drawBuffers([e.GL.COLOR_ATTACHMENT0,e.GL.COLOR_ATTACHMENT1,e.GL.COLOR_ATTACHMENT2,e.GL.COLOR_ATTACHMENT3]):!this.inBlitRenderPass&&this.WEBGL_draw_buffers&&this.WEBGL_draw_buffers.drawBuffersWEBGL([e.GL.COLOR_ATTACHMENT0_WEBGL,e.GL.COLOR_ATTACHMENT1_WEBGL,e.GL.COLOR_ATTACHMENT2_WEBGL,e.GL.COLOR_ATTACHMENT3_WEBGL]),!this.inBlitRenderPass)for(var o=t;this.currentColorAttachments.length>o;o++){var a=ht(n)?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,i=ht(n)?e.GL.COLOR_ATTACHMENT0:e.GL.COLOR_ATTACHMENT0_WEBGL;n.framebufferRenderbuffer(a,i+o,e.GL.RENDERBUFFER,null),n.framebufferTexture2D(a,i+o,e.GL.TEXTURE_2D,null,0)}this.currentColorAttachments.length=t},t.prototype.setRenderPassParametersColor=function(t,r,n,o,a,i){void 0===i&&(i=!1);var s=ht(this.gl);this.currentColorAttachments[t]===r&&this.currentColorAttachmentLevels[t]===n||(this.currentColorAttachments[t]=r,this.currentColorAttachmentLevels[t]=n,!i&&(s||!s&&this.WEBGL_draw_buffers)&&this.bindFramebufferAttachment(s?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,(s?e.GL.COLOR_ATTACHMENT0:e.GL.COLOR_ATTACHMENT0_WEBGL)+t,r,n),this.resolveColorAttachmentsChanged=!0),this.currentColorResolveTos[t]===o&&this.currentColorResolveToLevels[t]===a||(this.currentColorResolveTos[t]=o,this.currentColorResolveToLevels[t]=a,null!==o&&(this.resolveColorAttachmentsChanged=!0))},t.prototype.setRenderPassParametersDepthStencil=function(t,r,n){void 0===n&&(n=!1);var o=this.gl;this.currentDepthStencilAttachment!==t&&(this.currentDepthStencilAttachment=t,n||this.inBlitRenderPass||this.bindFramebufferDepthStencilAttachment(ht(o)?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,this.currentDepthStencilAttachment),this.resolveDepthStencilAttachmentsChanged=!0),this.currentDepthStencilResolveTo!==r&&(this.currentDepthStencilResolveTo=r,r&&(this.resolveDepthStencilAttachmentsChanged=!0))},t.prototype.setRenderPassParametersClearColor=function(t,r,n,o,a){var i,s=this.gl;null!==this.OES_draw_buffers_indexed?(i=this.currentMegaState.attachmentsState[t])&&i.channelWriteMask!==e.ChannelWriteMask.ALL&&(this.OES_draw_buffers_indexed.colorMaskiOES(t,!0,!0,!0,!0),i.channelWriteMask=e.ChannelWriteMask.ALL):(i=this.currentMegaState.attachmentsState[0])&&i.channelWriteMask!==e.ChannelWriteMask.ALL&&(s.colorMask(!0,!0,!0,!0),i.channelWriteMask=e.ChannelWriteMask.ALL);this.setScissorRectEnabled(!1),ht(s)?s.clearBufferfv(s.COLOR,t,[r,n,o,a]):(s.clearColor(r,n,o,a),s.clear(s.COLOR_BUFFER_BIT))},t.prototype.setRenderPassParametersClearDepthStencil=function(e,t){void 0===e&&(e="load"),void 0===t&&(t="load");var r=this.gl;"load"!==e&&(y(!!this.currentDepthStencilAttachment),this.currentMegaState.depthWrite||(r.depthMask(!0),this.currentMegaState.depthWrite=!0),ht(r)?r.clearBufferfv(r.DEPTH,0,[e]):(r.clearDepth(e),r.clear(r.DEPTH_BUFFER_BIT))),"load"!==t&&(y(!!this.currentDepthStencilAttachment),this.currentMegaState.stencilWrite||(r.enable(r.STENCIL_TEST),r.stencilMask(255),this.currentMegaState.stencilWrite=!0),ht(r)?r.clearBufferiv(r.STENCIL,0,[t]):(r.clearStencil(t),r.clear(r.STENCIL_BUFFER_BIT)))},t.prototype.setBindings=function(e){var t,r=this;if(this.renderBundle)this.renderBundle.push((function(){return r.setBindings(e)}));else{var n=this.gl,o=e.uniformBufferBindings,a=e.samplerBindings,i=e.bindingLayouts;y(i.bindingLayoutTables.length>0);var s=i.bindingLayoutTables[0];y(o.length>=s.numUniformBuffers),y(a.length>=s.numSamplers);for(var l=0;o.length>l;l++){if(0!==(p=o[l]).size){var _=s.firstUniformBuffer+l,u=p.buffer,E=p.offset||0,c=p.size||u.byteSize;if(u!==this.currentUniformBuffers[_]||E!==this.currentUniformBufferByteOffsets[_]||c!==this.currentUniformBufferByteSizes[_]){var R=E%u.pageByteSize,T=u.gl_buffer_pages[E/u.pageByteSize|0];y(u.pageByteSize>=R+c),ht(n)&&n.bindBufferRange(n.UNIFORM_BUFFER,_,T,R,c),this.currentUniformBuffers[_]=u,this.currentUniformBufferByteOffsets[_]=E,this.currentUniformBufferByteSizes[_]=c}}}for(l=0;s.numSamplers>l;l++){var p,A=s.firstSampler+l,d=null!==(p=a[l])&&null!==p.sampler?It(p.sampler):null,F=null!==p&&null!==p.texture?Mt(p.texture):null;if(this.currentSamplers[A]!==d&&(ht(n)&&n.bindSampler(A,d),this.currentSamplers[A]=d),this.currentTextures[A]!==F){if(this.setActiveTexture(n.TEXTURE0+A),null!==F){var f=b(p).texture,m=f.gl_target,h=f.width,S=f.height;p.texture.textureIndex=A,n.bindTexture(m,F),ht(n)||null===(t=p.sampler)||void 0===t||t.setTextureParameters(m,h,S),this.debugGroupStatisticsTextureBind()}else{var g=Ae(Ae({},p),Re),B=g.formatKind;m=Pt(g.dimension);n.bindTexture(m,this.getFallbackTexture(Ae({gl_target:m,formatKind:B},g)))}this.currentTextures[A]=F}}}},t.prototype.setViewport=function(e,t,r,n){this.gl.viewport(e,t,r,n)},t.prototype.setScissorRect=function(e,t,r,n){var o=this.gl;this.setScissorRectEnabled(!0),o.scissor(e,t,r,n)},t.prototype.applyAttachmentStateIndexed=function(t,r,n){var o=this.gl,a=this.OES_draw_buffers_indexed;r.channelWriteMask!==n.channelWriteMask&&(a.colorMaskiOES(t,!!(n.channelWriteMask&e.ChannelWriteMask.RED),!!(n.channelWriteMask&e.ChannelWriteMask.GREEN),!!(n.channelWriteMask&e.ChannelWriteMask.BLUE),!!(n.channelWriteMask&e.ChannelWriteMask.ALPHA)),r.channelWriteMask=n.channelWriteMask);var i=r.rgbBlendState.blendMode!==n.rgbBlendState.blendMode||r.alphaBlendState.blendMode!==n.alphaBlendState.blendMode,s=r.rgbBlendState.blendSrcFactor!==n.rgbBlendState.blendSrcFactor||r.alphaBlendState.blendSrcFactor!==n.alphaBlendState.blendSrcFactor||r.rgbBlendState.blendDstFactor!==n.rgbBlendState.blendDstFactor||r.alphaBlendState.blendDstFactor!==n.alphaBlendState.blendDstFactor;(s||i)&&(Gt(r.rgbBlendState)&&Gt(r.alphaBlendState)?a.enableiOES(t,o.BLEND):Gt(n.rgbBlendState)&&Gt(n.alphaBlendState)&&a.disableiOES(t,o.BLEND)),i&&(a.blendEquationSeparateiOES(t,n.rgbBlendState.blendMode,n.alphaBlendState.blendMode),r.rgbBlendState.blendMode=n.rgbBlendState.blendMode,r.alphaBlendState.blendMode=n.alphaBlendState.blendMode),s&&(a.blendFuncSeparateiOES(t,n.rgbBlendState.blendSrcFactor,n.rgbBlendState.blendDstFactor,n.alphaBlendState.blendSrcFactor,n.alphaBlendState.blendDstFactor),r.rgbBlendState.blendSrcFactor=n.rgbBlendState.blendSrcFactor,r.alphaBlendState.blendSrcFactor=n.alphaBlendState.blendSrcFactor,r.rgbBlendState.blendDstFactor=n.rgbBlendState.blendDstFactor,r.alphaBlendState.blendDstFactor=n.alphaBlendState.blendDstFactor)},t.prototype.applyAttachmentState=function(t,r){var n=this.gl;t.channelWriteMask!==r.channelWriteMask&&(n.colorMask(!!(r.channelWriteMask&e.ChannelWriteMask.RED),!!(r.channelWriteMask&e.ChannelWriteMask.GREEN),!!(r.channelWriteMask&e.ChannelWriteMask.BLUE),!!(r.channelWriteMask&e.ChannelWriteMask.ALPHA)),t.channelWriteMask=r.channelWriteMask);var o=t.rgbBlendState.blendMode!==r.rgbBlendState.blendMode||t.alphaBlendState.blendMode!==r.alphaBlendState.blendMode,a=t.rgbBlendState.blendSrcFactor!==r.rgbBlendState.blendSrcFactor||t.alphaBlendState.blendSrcFactor!==r.alphaBlendState.blendSrcFactor||t.rgbBlendState.blendDstFactor!==r.rgbBlendState.blendDstFactor||t.alphaBlendState.blendDstFactor!==r.alphaBlendState.blendDstFactor;(a||o)&&(Gt(t.rgbBlendState)&&Gt(t.alphaBlendState)?n.enable(n.BLEND):Gt(r.rgbBlendState)&&Gt(r.alphaBlendState)&&n.disable(n.BLEND)),o&&(n.blendEquationSeparate(r.rgbBlendState.blendMode,r.alphaBlendState.blendMode),t.rgbBlendState.blendMode=r.rgbBlendState.blendMode,t.alphaBlendState.blendMode=r.alphaBlendState.blendMode),a&&(n.blendFuncSeparate(r.rgbBlendState.blendSrcFactor,r.rgbBlendState.blendDstFactor,r.alphaBlendState.blendSrcFactor,r.alphaBlendState.blendDstFactor),t.rgbBlendState.blendSrcFactor=r.rgbBlendState.blendSrcFactor,t.alphaBlendState.blendSrcFactor=r.alphaBlendState.blendSrcFactor,t.rgbBlendState.blendDstFactor=r.rgbBlendState.blendDstFactor,t.alphaBlendState.blendDstFactor=r.alphaBlendState.blendDstFactor)},t.prototype.setMegaState=function(t){var r=this.gl,n=this.currentMegaState;if(null!==this.OES_draw_buffers_indexed)for(var o=0;t.attachmentsState.length>o;o++)this.applyAttachmentStateIndexed(o,n.attachmentsState[0],t.attachmentsState[0]);else y(1===t.attachmentsState.length),this.applyAttachmentState(n.attachmentsState[0],t.attachmentsState[0]);x(n.blendConstant,t.blendConstant)||(r.blendColor(t.blendConstant.r,t.blendConstant.g,t.blendConstant.b,t.blendConstant.a),X(n.blendConstant,t.blendConstant)),n.depthCompare!==t.depthCompare&&(r.depthFunc(t.depthCompare),n.depthCompare=t.depthCompare),!!n.depthWrite!=!!t.depthWrite&&(r.depthMask(t.depthWrite),n.depthWrite=t.depthWrite),!!n.stencilWrite!=!!t.stencilWrite&&(r.stencilMask(t.stencilWrite?255:0),n.stencilWrite=t.stencilWrite);var a=!1;if(!ve(n.stencilFront,t.stencilFront)){a=!0;var i=t.stencilFront,s=i.failOp,l=i.depthFailOp,_=i.compare;n.stencilFront.passOp===(u=i.passOp)&&n.stencilFront.failOp===s&&n.stencilFront.depthFailOp===l||(r.stencilOpSeparate(r.FRONT,s,l,u),n.stencilFront.passOp=u,n.stencilFront.failOp=s,n.stencilFront.depthFailOp=l),n.stencilFront.compare!==_&&(this.setStencilReference(0),n.stencilFront.compare=_)}if(!ve(n.stencilBack,t.stencilBack)){a=!0;var u,E=t.stencilBack;s=E.failOp,l=E.depthFailOp,_=E.compare;n.stencilBack.passOp===(u=E.passOp)&&n.stencilBack.failOp===s&&n.stencilBack.depthFailOp===l||(r.stencilOpSeparate(r.BACK,s,l,u),n.stencilBack.passOp=u,n.stencilBack.failOp=s,n.stencilBack.depthFailOp=l),n.stencilBack.compare!==_&&(this.setStencilReference(0),n.stencilBack.compare=_)}n.stencilFront.mask===t.stencilFront.mask&&n.stencilBack.mask===t.stencilBack.mask||(a=!0,n.stencilFront.mask=t.stencilFront.mask,n.stencilBack.mask=t.stencilBack.mask),a&&this.applyStencil(),n.cullMode!==t.cullMode&&(n.cullMode===e.CullMode.NONE?r.enable(r.CULL_FACE):t.cullMode===e.CullMode.NONE&&r.disable(r.CULL_FACE),t.cullMode===e.CullMode.BACK?r.cullFace(r.BACK):t.cullMode===e.CullMode.FRONT?r.cullFace(r.FRONT):t.cullMode===e.CullMode.FRONT_AND_BACK&&r.cullFace(r.FRONT_AND_BACK),n.cullMode=t.cullMode),n.frontFace!==t.frontFace&&(r.frontFace(t.frontFace),n.frontFace=t.frontFace),n.polygonOffset!==t.polygonOffset&&(t.polygonOffset?r.enable(r.POLYGON_OFFSET_FILL):r.disable(r.POLYGON_OFFSET_FILL),n.polygonOffset=t.polygonOffset),n.polygonOffsetFactor===t.polygonOffsetFactor&&n.polygonOffsetUnits===t.polygonOffsetUnits||(r.polygonOffset(t.polygonOffsetFactor,t.polygonOffsetUnits),n.polygonOffsetFactor=t.polygonOffsetFactor,n.polygonOffsetUnits=t.polygonOffsetUnits)},t.prototype.validatePipelineFormats=function(e){for(var t=0;this.currentColorAttachments.length>t;t++);this.currentDepthStencilAttachment&&y(this.currentDepthStencilAttachment.format===e.depthStencilAttachmentFormat),-1!==this.currentSampleCount&&y(this.currentSampleCount===e.sampleCount)},t.prototype.setPipeline=function(e){var t=this;if(this.renderBundle)this.renderBundle.push((function(){return t.setPipeline(e)}));else{this.currentPipeline=e,this.validatePipelineFormats(this.currentPipeline),this.setMegaState(this.currentPipeline.megaState);var r=this.currentPipeline.program;if(this.useProgram(r),r.compileState===yt.NeedsBind){var n=this.gl,o=r.gl_program,a=r.descriptor,i=Dt(a.vertex.glsl,Qt);if(ht(n))for(var s=0;i.length>s;s++){var l=me(i[s],2),_=n.getUniformBlockIndex(o,l[1]);-1!==_&&4294967295!==_&&n.uniformBlockBinding(o,_,s)}var u=Dt(a.fragment.glsl,/^uniform .*sampler\S+ (\w+);\s* \/\/ BINDING=(\d+)$/gm);for(s=0;u.length>s;s++){var E=me(u[s],3),c=E[2],R=n.getUniformLocation(o,E[1]);n.uniform1i(R,parseInt(c))}r.compileState=yt.ReadyToUse}}},t.prototype.setVertexInput=function(t,r,n){var o,a,i,s=this;if(this.renderBundle)this.renderBundle.push((function(){return s.setVertexInput(t,r,n)}));else if(null!==t){y(this.currentPipeline.inputLayout===t);var l=t;this.bindVAO(l.vao);for(var _=this.gl,u=0;l.vertexBufferDescriptors.length>u;u++){var E=l.vertexBufferDescriptors[u],c=E.arrayStride,R=E.attributes;try{for(var T=(o=void 0,fe(R)),p=T.next();!p.done;p=T.next()){var A=p.value,d=A.shaderLocation,F=A.offset,f=ht(_)?d:null===(i=l.program.attributes[d])||void 0===i?void 0:i.location;if(!ge(f)){var m=r[u];if(null===m)continue;var h=A.vertexFormat;_.bindBuffer(_.ARRAY_BUFFER,Ot(m.buffer)),_.vertexAttribPointer(f,h.size,h.type,h.normalized,c,(m.offset||0)+F)}}}catch(e){o={error:e}}finally{try{p&&!p.done&&(a=T.return)&&a.call(T)}finally{if(o)throw o.error}}}if(y(null!==n==(null!==l.indexBufferFormat)),null!==n){var S=n.buffer;y(S.usage===e.BufferUsage.INDEX),_.bindBuffer(_.ELEMENT_ARRAY_BUFFER,Ot(S)),this.currentIndexBufferByteOffset=n.offset||0}else this.currentIndexBufferByteOffset=null}else y(null===this.currentPipeline.inputLayout),y(null===n),this.bindVAO(null),this.currentIndexBufferByteOffset=0},t.prototype.setStencilReference=function(e){this.currentStencilRef!==e&&(this.currentStencilRef=e,this.applyStencil())},t.prototype.draw=function(e,t,r,n){var o,a=this;if(this.renderBundle)this.renderBundle.push((function(){return a.draw(e,t,r,n)}));else{var i=this.gl,s=this.currentPipeline;if(t){var l=[s.drawMode,r||0,e,t];ht(i)?i.drawArraysInstanced.apply(i,he([],me(l),!1)):(o=this.ANGLE_instanced_arrays).drawArraysInstancedANGLE.apply(o,he([],me(l),!1))}else i.drawArrays(s.drawMode,r,e);this.debugGroupStatisticsDrawCall(),this.debugGroupStatisticsTriangles(e/3*Math.max(t,1))}},t.prototype.drawIndexed=function(e,t,r,n,o){var a,i=this;if(this.renderBundle)this.renderBundle.push((function(){return i.drawIndexed(e,t,r,n,o)}));else{var s=this.gl,l=this.currentPipeline,_=b(l.inputLayout),u=b(this.currentIndexBufferByteOffset)+r*_.indexBufferCompByteSize;if(t){var E=[l.drawMode,e,_.indexBufferType,u,t];ht(s)?s.drawElementsInstanced.apply(s,he([],me(E),!1)):(a=this.ANGLE_instanced_arrays).drawElementsInstancedANGLE.apply(a,he([],me(E),!1))}else s.drawElements(l.drawMode,e,_.indexBufferType,u);this.debugGroupStatisticsDrawCall(),this.debugGroupStatisticsTriangles(e/3*Math.max(t,1))}},t.prototype.drawIndirect=function(e,t){},t.prototype.drawIndexedIndirect=function(e,t){},t.prototype.beginOcclusionQuery=function(e){var t=this.gl;if(ht(t)){var r=this.currentRenderPassDescriptor.occlusionQueryPool;t.beginQuery(r.gl_query_type,r.gl_query[e])}},t.prototype.endOcclusionQuery=function(){var e=this.gl;ht(e)&&e.endQuery(this.currentRenderPassDescriptor.occlusionQueryPool.gl_query_type)},t.prototype.pipelineQueryReady=function(e){return this.queryProgramReady(e.program)},t.prototype.pipelineForceReady=function(e){},t.prototype.endPass=function(){for(var t=this.gl,r=ht(t),n=1===this.currentColorResolveTos.length&&this.currentColorResolveTos[0]===this.scTexture,o=!1,a=0;this.currentColorAttachments.length>a;a++){var i=this.currentColorAttachments[a];if(null!==i){var s=this.currentColorResolveTos[a],l=!1;null!==s&&(y(i.width===s.width&&i.height===s.height),this.setScissorRectEnabled(!1),n||(r&&t.bindFramebuffer(t.READ_FRAMEBUFFER,this.resolveColorReadFramebuffer),this.resolveColorAttachmentsChanged&&r&&this.bindFramebufferAttachment(t.READ_FRAMEBUFFER,t.COLOR_ATTACHMENT0,i,this.currentColorAttachmentLevels[a])),l=!0,n||(s===this.scTexture?t.bindFramebuffer(r?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,this.scPlatformFramebuffer):(t.bindFramebuffer(r?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,this.resolveColorDrawFramebuffer),this.resolveColorAttachmentsChanged&&t.framebufferTexture2D(r?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,s.gl_texture,this.currentColorResolveToLevels[a]))),n||(r?(t.blitFramebuffer(0,0,i.width,i.height,0,0,s.width,s.height,t.COLOR_BUFFER_BIT,t.LINEAR),t.bindFramebuffer(t.DRAW_FRAMEBUFFER,null)):this.submitBlitRenderPass(i,s)),o=!0),this.currentRenderPassDescriptor.colorStore[a]||n||l||(t.bindFramebuffer(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,this.resolveColorReadFramebuffer),this.resolveColorAttachmentsChanged&&this.bindFramebufferAttachment(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,t.COLOR_ATTACHMENT0,i,this.currentColorAttachmentLevels[a])),n||t.bindFramebuffer(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,null)}}this.resolveColorAttachmentsChanged=!1;var _=this.currentDepthStencilAttachment;if(_){var u=this.currentDepthStencilResolveTo;l=!1;u&&(y(_.width===u.width&&_.height===u.height),this.setScissorRectEnabled(!1),n||(t.bindFramebuffer(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,this.resolveDepthStencilReadFramebuffer),t.bindFramebuffer(r?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,this.resolveDepthStencilDrawFramebuffer),this.resolveDepthStencilAttachmentsChanged&&(this.bindFramebufferDepthStencilAttachment(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,_),this.bindFramebufferDepthStencilAttachment(r?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,u))),l=!0,n||(r&&t.blitFramebuffer(0,0,_.width,_.height,0,0,u.width,u.height,t.DEPTH_BUFFER_BIT,t.NEAREST),t.bindFramebuffer(r?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,null)),o=!0),n||this.currentRenderPassDescriptor.depthStencilStore||(l||(t.bindFramebuffer(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,this.resolveDepthStencilReadFramebuffer),this.resolveDepthStencilAttachmentsChanged&&this.bindFramebufferDepthStencilAttachment(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,_),l=!0),r&&t.invalidateFramebuffer(t.READ_FRAMEBUFFER,[t.DEPTH_STENCIL_ATTACHMENT])),!n&&l&&t.bindFramebuffer(r?e.GL.READ_FRAMEBUFFER:e.GL.FRAMEBUFFER,null),this.resolveDepthStencilAttachmentsChanged=!1}n||o||t.bindFramebuffer(r?e.GL.DRAW_FRAMEBUFFER:e.GL.FRAMEBUFFER,null)},t.prototype.setScissorRectEnabled=function(e){if(this.currentScissorEnabled!==e){var t=this.gl;e?t.enable(t.SCISSOR_TEST):t.disable(t.SCISSOR_TEST),this.currentScissorEnabled=e}},t.prototype.applyStencil=function(){ge(this.currentStencilRef)||(this.gl.stencilFuncSeparate(e.GL.FRONT,this.currentMegaState.stencilFront.compare,this.currentStencilRef,this.currentMegaState.stencilFront.mask||255),this.gl.stencilFuncSeparate(e.GL.BACK,this.currentMegaState.stencilBack.compare,this.currentStencilRef,this.currentMegaState.stencilBack.mask||255))},t.prototype.getFallbackTexture=function(t){var r=t.gl_target;if(r===e.GL.TEXTURE_2D)return t.formatKind===e.SamplerFormatKind.Depth?this.fallbackTexture2DDepth:this.fallbackTexture2D;if(r===e.GL.TEXTURE_2D_ARRAY)return this.fallbackTexture2DArray;if(r===e.GL.TEXTURE_3D)return this.fallbackTexture3D;if(r===e.GL.TEXTURE_CUBE_MAP)return this.fallbackTextureCube;throw Error("whoops")},t.prototype.submitBlitRenderPass=function(t,r){this.blitRenderPipeline||(this.blitProgram=this.createProgram({vertex:{glsl:"layout(location = 0) in vec2 a_Position;\nout vec2 v_TexCoord;\nvoid main() {\n  v_TexCoord = 0.5 * (a_Position + 1.0);\n  gl_Position = vec4(a_Position, 0., 1.);\n\n  #ifdef VIEWPORT_ORIGIN_TL\n    v_TexCoord.y = 1.0 - v_TexCoord.y;\n  #endif\n}"},fragment:{glsl:"uniform sampler2D u_Texture;\nin vec2 v_TexCoord;\nout vec4 outputColor;\nvoid main() {\n  outputColor = texture(SAMPLER_2D(u_Texture), v_TexCoord);\n}"}}),this.blitVertexBuffer=this.createBuffer({usage:e.BufferUsage.VERTEX|e.BufferUsage.COPY_DST,viewOrSize:new Float32Array([-4,-4,4,-4,0,4])}),this.blitInputLayout=this.createInputLayout({vertexBufferDescriptors:[{arrayStride:8,stepMode:e.VertexStepMode.VERTEX,attributes:[{format:e.Format.F32_RG,offset:0,shaderLocation:0}]}],indexBufferFormat:null,program:this.blitProgram}),this.blitRenderPipeline=this.createRenderPipeline({topology:e.PrimitiveTopology.TRIANGLES,sampleCount:1,program:this.blitProgram,colorAttachmentFormats:[e.Format.U8_RGBA_RT],depthStencilAttachmentFormat:null,inputLayout:this.blitInputLayout,megaStateDescriptor:se(ue)}),this.blitBindings=this.createBindings({samplerBindings:[{sampler:null,texture:t.texture}],uniformBufferBindings:[]}),this.blitProgram.setUniformsLegacy({u_Texture:t}));var n=this.currentRenderPassDescriptor;this.currentRenderPassDescriptor=null,this.inBlitRenderPass=!0;var o=this.createRenderPass({colorAttachment:[t],colorResolveTo:[r],colorClearColor:[k]}),a=this.getCanvas(),i=a.width,s=a.height;o.setPipeline(this.blitRenderPipeline),o.setBindings(this.blitBindings),o.setVertexInput(this.blitInputLayout,[{buffer:this.blitVertexBuffer}],null),o.setViewport(0,0,i,s),this.gl.disable(this.gl.BLEND),o.draw(3,0),this.gl.enable(this.gl.BLEND),this.currentRenderPassDescriptor=n,this.inBlitRenderPass=!1},t}(),$t=function(){function e(e){this.pluginOptions=e}return e.prototype.createSwapChain=function(e){return de(this,void 0,void 0,(function(){var t,r,n,o,a,i,s,l,_;return Fe(this,(function(u){return r=(t=this.pluginOptions).targets,i=t.shaderDebug,s=t.trackResources,l={antialias:void 0!==(n=t.antialias)&&n,preserveDrawingBuffer:void 0!==(o=t.preserveDrawingBuffer)&&o,stencil:!0,premultipliedAlpha:void 0===(a=t.premultipliedAlpha)||a,xrCompatible:t.xrCompatible},this.handleContextEvents(e),r.includes("webgl2")&&(_=e.getContext("webgl2",l)||e.getContext("experimental-webgl2",l)),!_&&r.includes("webgl1")&&(_=e.getContext("webgl",l)||e.getContext("experimental-webgl",l)),[2,new jt(_,{shaderDebug:i,trackResources:s})]}))}))},e.prototype.handleContextEvents=function(e){var t=this.pluginOptions,r=t.onContextLost,n=t.onContextRestored,o=t.onContextCreationError;o&&e.addEventListener("webglcontextcreationerror",o,!1),r&&e.addEventListener("webglcontextlost",r,!1),n&&e.addEventListener("webglcontextrestored",n,!1)},e}();let Jt;const er="undefined"!=typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:()=>{throw Error("TextDecoder not available")}};"undefined"!=typeof TextDecoder&&er.decode();let tr=null;function rr(){return null!==tr&&0!==tr.byteLength||(tr=new Uint8Array(Jt.memory.buffer)),tr}function nr(e,t){return e>>>=0,er.decode(rr().subarray(e,e+t))}const or=Array(128).fill(void 0);or.push(void 0,null,!0,!1);let ar=or.length;function ir(e){return or[e]}function sr(e){const t=ir(e);return function(e){132>e||(or[e]=ar,ar=e)}(e),t}let lr=0;const _r="undefined"!=typeof TextEncoder?new TextEncoder("utf-8"):{encode:()=>{throw Error("TextEncoder not available")}},ur="function"==typeof _r.encodeInto?function(e,t){return _r.encodeInto(e,t)}:function(e,t){const r=_r.encode(e);return t.set(r),{read:e.length,written:r.length}};function Er(e,t,r){if(void 0===r){const r=_r.encode(e),n=t(r.length,1)>>>0;return rr().subarray(n,n+r.length).set(r),lr=r.length,n}let n=e.length,o=t(n,1)>>>0;const a=rr();let i=0;for(;n>i;i++){const t=e.charCodeAt(i);if(t>127)break;a[o+i]=t}if(i!==n){0!==i&&(e=e.slice(i)),o=r(o,n,n=i+3*e.length,1)>>>0;const t=rr().subarray(o+i,o+n);i+=ur(e,t).written}return lr=i,o}let cr=null;function Rr(){return null!==cr&&0!==cr.byteLength||(cr=new Int32Array(Jt.memory.buffer)),cr}function Tr(e,t,r){let n,o;try{const s=Jt.__wbindgen_add_to_stack_pointer(-16),l=Er(e,Jt.__wbindgen_malloc,Jt.__wbindgen_realloc),_=lr,u=Er(t,Jt.__wbindgen_malloc,Jt.__wbindgen_realloc);Jt.glsl_compile(s,l,_,u,lr,r);var a=Rr()[s/4+0],i=Rr()[s/4+1];return n=a,o=i,nr(a,i)}finally{Jt.__wbindgen_add_to_stack_pointer(16),Jt.__wbindgen_free(n,o,1)}}class pr{static __wrap(e){e>>>=0;const t=Object.create(pr.prototype);return t.__wbg_ptr=e,t}__destroy_into_raw(){const e=this.__wbg_ptr;return this.__wbg_ptr=0,e}free(){const e=this.__destroy_into_raw();Jt.__wbg_wgslcomposer_free(e)}constructor(){const e=Jt.wgslcomposer_new();return pr.__wrap(e)}load_composable(e){const t=Er(e,Jt.__wbindgen_malloc,Jt.__wbindgen_realloc);Jt.wgslcomposer_load_composable(this.__wbg_ptr,t,lr)}wgsl_compile(e){let t,r;try{const a=Jt.__wbindgen_add_to_stack_pointer(-16),i=Er(e,Jt.__wbindgen_malloc,Jt.__wbindgen_realloc);Jt.wgslcomposer_wgsl_compile(a,this.__wbg_ptr,i,lr);var n=Rr()[a/4+0],o=Rr()[a/4+1];return t=n,r=o,nr(n,o)}finally{Jt.__wbindgen_add_to_stack_pointer(16),Jt.__wbindgen_free(t,r,1)}}}function Ar(){const e={wbg:{}};return e.wbg.__wbindgen_string_new=function(e,t){return function(e){ar===or.length&&or.push(or.length+1);const t=ar;return ar=or[t],or[t]=e,t}(nr(e,t))},e.wbg.__wbindgen_object_drop_ref=function(e){sr(e)},e.wbg.__wbg_log_1d3ae0273d8f4f8a=function(e){console.log(ir(e))},e.wbg.__wbg_log_576ca876af0d4a77=function(e,t){console.log(ir(e),ir(t))},e.wbg.__wbindgen_throw=function(e,t){throw Error(nr(e,t))},e}async function dr(e){if(void 0!==Jt)return Jt;const t=Ar();("string"==typeof e||"function"==typeof Request&&e instanceof Request||"function"==typeof URL&&e instanceof URL)&&(e=fetch(e));const{instance:r,module:n}=await async function(e,t){if("function"==typeof Response&&e instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(e,t)}catch(t){if("application/wasm"==e.headers.get("Content-Type"))throw t;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",t)}const r=await e.arrayBuffer();return await WebAssembly.instantiate(r,t)}{const r=await WebAssembly.instantiate(e,t);return r instanceof WebAssembly.Instance?{instance:r,module:e}:r}}(await e,t);return function(e,t){return Jt=e.exports,dr.__wbindgen_wasm_module=t,cr=null,tr=null,Jt}(r,n)}var Fr,fr;function mr(t){if(t===e.Format.U8_R_NORM)return"r8unorm";if(t===e.Format.S8_R_NORM)return"r8snorm";if(t===e.Format.U8_RG_NORM)return"rg8unorm";if(t===e.Format.S8_RG_NORM)return"rg8snorm";if(t===e.Format.U32_R)return"r32uint";if(t===e.Format.S32_R)return"r32sint";if(t===e.Format.F32_R)return"r32float";if(t===e.Format.U16_RG)return"rg16uint";if(t===e.Format.S16_RG)return"rg16sint";if(t===e.Format.F16_RG)return"rg16float";if(t===e.Format.U8_RGBA_RT)return"bgra8unorm";if(t===e.Format.U8_RGBA_RT_SRGB)return"bgra8unorm-srgb";if(t===e.Format.U8_RGBA_NORM)return"rgba8unorm";if(t===e.Format.U8_RGBA_SRGB)return"rgba8unorm-srgb";if(t===e.Format.S8_RGBA_NORM)return"rgba8snorm";if(t===e.Format.U32_RG)return"rg32uint";if(t===e.Format.S32_RG)return"rg32sint";if(t===e.Format.F32_RG)return"rg32float";if(t===e.Format.U16_RGBA)return"rgba16uint";if(t===e.Format.S16_RGBA)return"rgba16sint";if(t===e.Format.F16_RGBA)return"rgba16float";if(t===e.Format.F32_RGBA)return"rgba32float";if(t===e.Format.U32_RGBA)return"rgba32uint";if(t===e.Format.S32_RGBA)return"rgba32sint";if(t===e.Format.D24)return"depth24plus";if(t===e.Format.D24_S8)return"depth24plus-stencil8";if(t===e.Format.D32F)return"depth32float";if(t===e.Format.D32F_S8)return"depth32float-stencil8";if(t===e.Format.BC1)return"bc1-rgba-unorm";if(t===e.Format.BC1_SRGB)return"bc1-rgba-unorm-srgb";if(t===e.Format.BC2)return"bc2-rgba-unorm";if(t===e.Format.BC2_SRGB)return"bc2-rgba-unorm-srgb";if(t===e.Format.BC3)return"bc3-rgba-unorm";if(t===e.Format.BC3_SRGB)return"bc3-rgba-unorm-srgb";if(t===e.Format.BC4_SNORM)return"bc4-r-snorm";if(t===e.Format.BC4_UNORM)return"bc4-r-unorm";if(t===e.Format.BC5_SNORM)return"bc5-rg-snorm";if(t===e.Format.BC5_UNORM)return"bc5-rg-unorm";throw"whoops"}function hr(t){if(t===e.TextureDimension.TEXTURE_2D)return"2d";if(t===e.TextureDimension.TEXTURE_CUBE_MAP)return"cube";if(t===e.TextureDimension.TEXTURE_2D_ARRAY)return"2d-array";if(t===e.TextureDimension.TEXTURE_3D)return"3d";throw Error("whoops")}function Sr(t){if(t===e.AddressMode.CLAMP_TO_EDGE)return"clamp-to-edge";if(t===e.AddressMode.REPEAT)return"repeat";if(t===e.AddressMode.MIRRORED_REPEAT)return"mirror-repeat";throw Error("whoops")}function gr(t){if(t===e.FilterMode.BILINEAR)return"linear";if(t===e.FilterMode.POINT)return"nearest";throw Error("whoops")}function Br(t){if(t===e.MipmapFilterMode.LINEAR)return"linear";if(t===e.MipmapFilterMode.NEAREST)return"nearest";if(t===e.MipmapFilterMode.NO_MIP)return"nearest";throw Error("whoops")}function Nr(e){return e.gpuBuffer}function Cr(t){if(t===e.QueryPoolType.OcclusionConservative)return"occlusion";throw Error("whoops")}function Lr(t){switch(t){case e.PrimitiveTopology.TRIANGLES:return"triangle-list";case e.PrimitiveTopology.POINTS:return"point-list";case e.PrimitiveTopology.TRIANGLE_STRIP:return"triangle-strip";case e.PrimitiveTopology.LINES:return"line-list";case e.PrimitiveTopology.LINE_STRIP:return"line-strip";default:throw Error("Unknown primitive topology mode")}}function Or(t){if(t===e.CullMode.NONE)return"none";if(t===e.CullMode.FRONT)return"front";if(t===e.CullMode.BACK)return"back";throw Error("whoops")}function Mr(t){if(t===e.FrontFace.CCW)return"ccw";if(t===e.FrontFace.CW)return"cw";throw Error("whoops")}function Ir(t){if(t===e.BlendFactor.ZERO)return"zero";if(t===e.BlendFactor.ONE)return"one";if(t===e.BlendFactor.SRC)return"src";if(t===e.BlendFactor.ONE_MINUS_SRC)return"one-minus-src";if(t===e.BlendFactor.DST)return"dst";if(t===e.BlendFactor.ONE_MINUS_DST)return"one-minus-dst";if(t===e.BlendFactor.SRC_ALPHA)return"src-alpha";if(t===e.BlendFactor.ONE_MINUS_SRC_ALPHA)return"one-minus-src-alpha";if(t===e.BlendFactor.DST_ALPHA)return"dst-alpha";if(t===e.BlendFactor.ONE_MINUS_DST_ALPHA)return"one-minus-dst-alpha";if(t===e.BlendFactor.CONST)return"constant";if(t===e.BlendFactor.ONE_MINUS_CONSTANT)return"one-minus-constant";if(t===e.BlendFactor.SRC_ALPHA_SATURATE)return"src-alpha-saturated";throw Error("whoops")}function Ur(t){if(t===e.BlendMode.ADD)return"add";if(t===e.BlendMode.SUBSTRACT)return"subtract";if(t===e.BlendMode.REVERSE_SUBSTRACT)return"reverse-subtract";if(t===e.BlendMode.MIN)return"min";if(t===e.BlendMode.MAX)return"max";throw Error("whoops")}function Dr(e){return{operation:Ur(e.blendMode),srcFactor:Ir(e.blendSrcFactor),dstFactor:Ir(e.blendDstFactor)}}function Gr(t){return t.blendMode===e.BlendMode.ADD&&t.blendSrcFactor===e.BlendFactor.ONE&&t.blendDstFactor===e.BlendFactor.ZERO}function Pr(e){return Gr(e.rgbBlendState)&&Gr(e.alphaBlendState)?void 0:{color:Dr(e.rgbBlendState),alpha:Dr(e.alphaBlendState)}}function vr(e,t){return t.attachmentsState.map((function(t,r){return function(e,t){return{format:mr(t),blend:Pr(e),writeMask:e.channelWriteMask}}(t,e[r])}))}function yr(t){if(t===e.CompareFunction.NEVER)return"never";if(t===e.CompareFunction.LESS)return"less";if(t===e.CompareFunction.EQUAL)return"equal";if(t===e.CompareFunction.LEQUAL)return"less-equal";if(t===e.CompareFunction.GREATER)return"greater";if(t===e.CompareFunction.NOTEQUAL)return"not-equal";if(t===e.CompareFunction.GEQUAL)return"greater-equal";if(t===e.CompareFunction.ALWAYS)return"always";throw Error("whoops")}function br(t){if(t===e.StencilOp.KEEP)return"keep";if(t===e.StencilOp.REPLACE)return"replace";if(t===e.StencilOp.ZERO)return"zero";if(t===e.StencilOp.DECREMENT_CLAMP)return"decrement-clamp";if(t===e.StencilOp.DECREMENT_WRAP)return"decrement-wrap";if(t===e.StencilOp.INCREMENT_CLAMP)return"increment-clamp";if(t===e.StencilOp.INCREMENT_WRAP)return"increment-wrap";if(t===e.StencilOp.INVERT)return"invert";throw Error("whoops")}function xr(t){if(t===e.VertexStepMode.VERTEX)return"vertex";if(t===e.VertexStepMode.INSTANCE)return"instance";throw Error("whoops")}function Xr(t){if(t===e.Format.U8_R)return"uint8x2";if(t===e.Format.U8_RG)return"uint8x2";if(t===e.Format.U8_RGB)return"uint8x4";if(t===e.Format.U8_RGBA)return"uint8x4";if(t===e.Format.U8_RG_NORM)return"unorm8x2";if(t===e.Format.U8_RGBA_NORM)return"unorm8x4";if(t===e.Format.S8_RGB_NORM)return"snorm8x4";if(t===e.Format.S8_RGBA_NORM)return"snorm8x4";if(t===e.Format.U16_RG_NORM)return"unorm16x2";if(t===e.Format.U16_RGBA_NORM)return"unorm16x4";if(t===e.Format.S16_RG_NORM)return"snorm16x2";if(t===e.Format.S16_RGBA_NORM)return"snorm16x4";if(t===e.Format.S16_RG)return"uint16x2";if(t===e.Format.F16_RG)return"float16x2";if(t===e.Format.F16_RGBA)return"float16x4";if(t===e.Format.F32_R)return"float32";if(t===e.Format.F32_RG)return"float32x2";if(t===e.Format.F32_RGB)return"float32x3";if(t===e.Format.F32_RGBA)return"float32x4";throw"whoops"}function wr(t,r,n,o){switch(void 0===n&&(n=!1),t){case e.Format.S8_R:case e.Format.S8_R_NORM:case e.Format.S8_RG_NORM:case e.Format.S8_RGB_NORM:case e.Format.S8_RGBA_NORM:var a=(ArrayBuffer,new Int8Array(r));return o&&a.set(new Int8Array(o)),a;case e.Format.U8_R:case e.Format.U8_R_NORM:case e.Format.U8_RG:case e.Format.U8_RG_NORM:case e.Format.U8_RGB:case e.Format.U8_RGB_NORM:case e.Format.U8_RGB_SRGB:case e.Format.U8_RGBA:case e.Format.U8_RGBA_NORM:case e.Format.U8_RGBA_SRGB:var i=(ArrayBuffer,new Uint8Array(r));return o&&i.set(new Uint8Array(o)),i;case e.Format.S16_R:case e.Format.S16_RG:case e.Format.S16_RG_NORM:case e.Format.S16_RGB_NORM:case e.Format.S16_RGBA:case e.Format.S16_RGBA_NORM:var s=r instanceof ArrayBuffer?new Int16Array(r):new Int16Array(n?r/2:r);return o&&s.set(new Int16Array(o)),s;case e.Format.U16_R:case e.Format.U16_RGB:case e.Format.U16_RGBA_5551:case e.Format.U16_RGBA_NORM:case e.Format.U16_RG_NORM:case e.Format.U16_R_NORM:var l=r instanceof ArrayBuffer?new Uint16Array(r):new Uint16Array(n?r/2:r);return o&&l.set(new Uint16Array(o)),l;case e.Format.S32_R:var _=r instanceof ArrayBuffer?new Int32Array(r):new Int32Array(n?r/4:r);return o&&_.set(new Int32Array(o)),_;case e.Format.U32_R:case e.Format.U32_RG:var u=r instanceof ArrayBuffer?new Uint32Array(r):new Uint32Array(n?r/4:r);return o&&u.set(new Uint32Array(o)),u;case e.Format.F32_R:case e.Format.F32_RG:case e.Format.F32_RGB:case e.Format.F32_RGBA:var E=r instanceof ArrayBuffer?new Float32Array(r):new Float32Array(n?r/4:r);return o&&E.set(new Float32Array(o)),E}var c=(ArrayBuffer,new Uint8Array(r));return o&&c.set(new Uint8Array(o)),c}function Wr(e){switch(e){case"r8unorm":case"r8snorm":case"r8uint":case"r8sint":return{width:1,height:1,length:1};case"r16uint":case"r16sint":case"r16float":case"rg8unorm":case"rg8snorm":case"rg8uint":case"rg8sint":case"depth16unorm":return{width:1,height:1,length:2};case"r32uint":case"r32sint":case"r32float":case"rg16uint":case"rg16sint":case"rg16float":case"rgba8unorm":case"rgba8unorm-srgb":case"rgba8snorm":case"rgba8uint":case"rgba8sint":case"bgra8unorm":case"bgra8unorm-srgb":case"rgb9e5ufloat":case"rgb10a2unorm":case"rg11b10ufloat":case"depth32float":default:return{width:1,height:1,length:4};case"rg32uint":case"rg32sint":case"rg32float":case"rgba16uint":case"rgba16sint":case"rgba16float":return{width:1,height:1,length:8};case"rgba32uint":case"rgba32sint":case"rgba32float":return{width:1,height:1,length:16};case"stencil8":throw Error("No fixed size for Stencil8 format!");case"depth24plus":throw Error("No fixed size for Depth24Plus format!");case"depth24plus-stencil8":throw Error("No fixed size for Depth24PlusStencil8 format!");case"depth32float-stencil8":return{width:1,height:1,length:5};case"bc7-rgba-unorm":case"bc7-rgba-unorm-srgb":case"bc6h-rgb-ufloat":case"bc6h-rgb-float":case"bc2-rgba-unorm":case"bc2-rgba-unorm-srgb":case"bc3-rgba-unorm":case"bc3-rgba-unorm-srgb":case"bc5-rg-unorm":case"bc5-rg-snorm":return{width:4,height:4,length:16};case"bc4-r-unorm":case"bc4-r-snorm":case"bc1-rgba-unorm":case"bc1-rgba-unorm-srgb":return{width:4,height:4,length:8}}}!function(e){e[e.COPY_SRC=1]="COPY_SRC",e[e.COPY_DST=2]="COPY_DST",e[e.TEXTURE_BINDING=4]="TEXTURE_BINDING",e[e.STORAGE_BINDING=8]="STORAGE_BINDING",e[e.STORAGE=8]="STORAGE",e[e.RENDER_ATTACHMENT=16]="RENDER_ATTACHMENT"}(Fr||(Fr={})),function(e){e[e.READ=1]="READ",e[e.WRITE=2]="WRITE"}(fr||(fr={}));var Hr=function(e){function t(t){var r=t.id,n=t.device,o=e.call(this)||this;return o.id=r,o.device=n,o}return pe(t,e),t.prototype.destroy=function(){},t}(Ft),Vr=function(t){function r(r){var n,o,a=r.descriptor,i=t.call(this,{id:r.id,device:r.device})||this;i.type=e.ResourceType.Bindings;var s=a.pipeline;y(!!s);var l=a.uniformBufferBindings,_=a.storageBufferBindings,u=a.samplerBindings,E=a.storageTextureBindings;i.numUniformBuffers=(null==l?void 0:l.length)||0;var c=[[],[],[],[]],R=0;if(l&&l.length)for(var T=0;l.length>T;T++){var p=a.uniformBufferBindings[T],A=p.binding,d=p.size,F=p.offset,f={buffer:Nr(p.buffer),offset:null!=F?F:0,size:d};c[0].push({binding:null!=A?A:R++,resource:f})}if(u&&u.length){R=0;for(T=0;u.length>T;T++){var m=Ae(Ae({},u[T]),Re),h=null!==(A=a.samplerBindings[T]).texture?A.texture:i.device.getFallbackTexture(m);if(m.dimension=h.dimension,m.formatKind=v(h.format),c[1].push({binding:null!==(n=A.textureBinding)&&void 0!==n?n:R++,resource:h.gpuTextureView}),-1!==A.samplerBinding){var S=null!==A.sampler?A.sampler:i.device.getFallbackSampler(m),g=S.gpuSampler;c[1].push({binding:null!==(o=A.samplerBinding)&&void 0!==o?o:R++,resource:g})}}}if(_&&_.length){R=0;for(T=0;_.length>T;T++){var B=a.storageBufferBindings[T];A=B.binding,d=B.size,F=B.offset,f={buffer:Nr(B.buffer),offset:null!=F?F:0,size:d};c[2].push({binding:null!=A?A:R++,resource:f})}}if(E&&E.length){R=0;for(T=0;E.length>T;T++){var N=a.storageTextureBindings[T];c[3].push({binding:null!=(A=N.binding)?A:R++,resource:(h=N.texture).gpuTextureView})}}var C=c.findLastIndex((function(e){return!!e.length}));return i.gpuBindGroup=c.map((function(e,t){return C>=t&&i.device.device.createBindGroup({layout:s.getBindGroupLayout(t),entries:e})})),i}return pe(r,t),r}(Hr),kr=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;o.type=e.ResourceType.Buffer;var a=n.usage,i=n.viewOrSize,s=!!(a&e.BufferUsage.MAP_READ);o.usage=function(t){var r=0;return t&e.BufferUsage.INDEX&&(r|=GPUBufferUsage.INDEX),t&e.BufferUsage.VERTEX&&(r|=GPUBufferUsage.VERTEX),t&e.BufferUsage.UNIFORM&&(r|=GPUBufferUsage.UNIFORM),t&e.BufferUsage.STORAGE&&(r|=GPUBufferUsage.STORAGE),t&e.BufferUsage.COPY_SRC&&(r|=GPUBufferUsage.COPY_SRC),t&e.BufferUsage.INDIRECT&&(r|=GPUBufferUsage.INDIRECT),r|GPUBufferUsage.COPY_DST}(a),s&&(o.usage=e.BufferUsage.MAP_READ|e.BufferUsage.COPY_DST);var l=!Le(i);(o.view=Le(i)?null:i,o.size=Le(i)?j(i,4):j(i.byteLength,4),Le(i))?o.gpuBuffer=o.device.device.createBuffer({usage:o.usage,size:o.size,mappedAtCreation:!!s&&l}):(o.gpuBuffer=o.device.device.createBuffer({usage:o.usage,size:o.size,mappedAtCreation:!0}),new(i&&i.constructor||Float32Array)(o.gpuBuffer.getMappedRange()).set(i),o.gpuBuffer.unmap());return o}return pe(r,t),r.prototype.setSubData=function(e,t,r,n){void 0===r&&(r=0),void 0===n&&(n=0);var o=this.gpuBuffer,a=t.byteOffset+r,i=a+(n=Math.min(n=n||t.byteLength,this.size-e)),s=n+3&-4;if(s!==n){var l=new Uint8Array(t.buffer.slice(a,i));(t=new Uint8Array(s)).set(l),r=0,a=0,i=s,n=s}for(var _=15728640,u=0;i-(a+u)>_;)this.device.device.queue.writeBuffer(o,e+u,t.buffer,a+u,_),u+=_;this.device.device.queue.writeBuffer(o,e+u,t.buffer,a+u,n-u)},r.prototype.destroy=function(){t.prototype.destroy.call(this),this.gpuBuffer.destroy()},r}(Hr),Yr=function(){function e(){this.gpuComputePassEncoder=null}return e.prototype.dispatchWorkgroups=function(e,t,r){this.gpuComputePassEncoder.dispatchWorkgroups(e,t,r)},e.prototype.dispatchWorkgroupsIndirect=function(e,t){this.gpuComputePassEncoder.dispatchWorkgroupsIndirect(e.gpuBuffer,t)},e.prototype.finish=function(){this.gpuComputePassEncoder.end(),this.gpuComputePassEncoder=null,this.frameCommandEncoder=null},e.prototype.beginComputePass=function(e){y(null===this.gpuComputePassEncoder),this.frameCommandEncoder=e,this.gpuComputePassEncoder=this.frameCommandEncoder.beginComputePass(this.gpuComputePassDescriptor)},e.prototype.setPipeline=function(e){var t=b(e.gpuComputePipeline);this.gpuComputePassEncoder.setPipeline(t)},e.prototype.setBindings=function(e){var t=this,r=e;r.gpuBindGroup.forEach((function(e,n){e&&t.gpuComputePassEncoder.setBindGroup(n,r.gpuBindGroup[n])}))},e.prototype.pushDebugGroup=function(e){this.gpuComputePassEncoder.pushDebugGroup(e)},e.prototype.popDebugGroup=function(){this.gpuComputePassEncoder.popDebugGroup()},e.prototype.insertDebugMarker=function(e){this.gpuComputePassEncoder.insertDebugMarker(e)},e}(),Kr=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;o.type=e.ResourceType.ComputePipeline,o.gpuComputePipeline=null,o.descriptor=n;var a=n.program.computeStage;if(null===a)return o;var i={layout:"auto",compute:Ae({},a)};return o.gpuComputePipeline=o.device.device.createComputePipeline(i),void 0!==o.name&&(o.gpuComputePipeline.label=o.name),o}return pe(r,t),r.prototype.getBindGroupLayout=function(e){return this.gpuComputePipeline.getBindGroupLayout(e)},r}(Hr),qr=function(t){function r(r){var n,o,a,i,s=r.descriptor,l=t.call(this,{id:r.id,device:r.device})||this;l.type=e.ResourceType.InputLayout;var _=[];try{for(var u=fe(s.vertexBufferDescriptors),E=u.next();!E.done;E=u.next()){var c=E.value,R=c.attributes;_.push({arrayStride:c.arrayStride,stepMode:xr(c.stepMode),attributes:[]});try{for(var T=(a=void 0,fe(R)),p=T.next();!p.done;p=T.next()){var A=p.value,d=A.offset;_[_.length-1].attributes.push({shaderLocation:A.shaderLocation,format:Xr(A.format),offset:d})}}catch(e){a={error:e}}finally{try{p&&!p.done&&(i=T.return)&&i.call(T)}finally{if(a)throw a.error}}}}catch(e){n={error:e}}finally{try{E&&!E.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}return l.indexFormat=function(t){if(null!==t){if(t===e.Format.U16_R)return"uint16";if(t===e.Format.U32_R)return"uint32";throw Error("whoops")}}(s.indexBufferFormat),l.buffers=_,l}return pe(r,t),r}(Hr),zr=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;return o.type=e.ResourceType.Program,o.vertexStage=null,o.fragmentStage=null,o.computeStage=null,o.descriptor=n,n.vertex&&(o.vertexStage=o.createShaderStage(n.vertex,"vertex")),n.fragment&&(o.fragmentStage=o.createShaderStage(n.fragment,"fragment")),n.compute&&(o.computeStage=o.createShaderStage(n.compute,"compute")),o}return pe(r,t),r.prototype.setUniformsLegacy=function(e){},r.prototype.createShaderStage=function(e,t){var r,n,o=e.glsl,a=e.entryPoint,i=e.postprocess,s=e.wgsl;if(!s)try{s=this.device.glsl_compile(o,t,!1)}catch(e){throw console.error(e,o),Error("whoops")}var l=function(e){if(!s.includes(e))return"continue";s=(s=s.replace("var T_".concat(e,": texture_2d<f32>;"),"var T_".concat(e,": texture_depth_2d;"))).replace(RegExp("textureSample\\(T_".concat(e,"(.*)\\);$"),"gm"),(function(t,r){return"vec4<f32>(textureSample(T_".concat(e).concat(r,"), 0.0, 0.0, 0.0);")}))};try{for(var _=fe(["u_TextureFramebufferDepth"]),u=_.next();!u.done;u=_.next()){l(u.value)}}catch(e){r={error:e}}finally{try{u&&!u.done&&(n=_.return)&&n.call(_)}finally{if(r)throw r.error}}return i&&(s=i(s)),{module:this.device.device.createShaderModule({code:s}),entryPoint:a||"main"}},r}(Hr),Zr=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;o.type=e.ResourceType.QueryPool;var a=n.elemCount;return o.querySet=o.device.device.createQuerySet({type:Cr(n.type),count:a}),o.resolveBuffer=o.device.device.createBuffer({size:8*a,usage:GPUBufferUsage.QUERY_RESOLVE|GPUBufferUsage.COPY_SRC}),o.cpuBuffer=o.device.device.createBuffer({size:8*a,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ}),o.results=null,o}return pe(r,t),r.prototype.queryResultOcclusion=function(e){return null===this.results?null:this.results[e]!==BigInt(0)},r.prototype.destroy=function(){t.prototype.destroy.call(this),this.querySet.destroy(),this.resolveBuffer.destroy(),this.cpuBuffer.destroy()},r}(Hr),Qr=function(t){function r(r){var n=t.call(this,{id:r.id,device:r.device})||this;return n.type=e.ResourceType.Readback,n}return pe(r,t),r.prototype.readTexture=function(t,r,n,o,a,i,s,l){return void 0===s&&(s=0),de(this,void 0,void 0,(function(){var l,_,u,E,c,R,T;return Fe(this,(function(p){return 0,_=Wr((l=t).gpuTextureformat),R=this.device.createBuffer({usage:e.BufferUsage.STORAGE|e.BufferUsage.MAP_READ|e.BufferUsage.COPY_DST,hint:e.BufferFrequencyHint.STATIC,viewOrSize:c=(E=256*Math.ceil((u=Math.ceil(o/_.width)*_.length)/256))*a}),(T=this.device.device.createCommandEncoder()).copyTextureToBuffer({texture:l.gpuTexture,mipLevel:0,origin:{x:r,y:n,z:0}},{buffer:R.gpuBuffer,offset:0,bytesPerRow:E},{width:o,height:a,depthOrArrayLayers:1}),this.device.device.queue.submit([T.finish()]),[2,this.readBuffer(R,0,i.byteLength===c?i:null,s,c,l.format,!0,!1,u,E,a)]}))}))},r.prototype.readTextureSync=function(e,t,r,n,o,a,i,s){throw Error("ERROR_MSG_METHOD_NOT_IMPLEMENTED")},r.prototype.readBuffer=function(t,r,n,o,a,i,s,l,_,u,E){var c=this;void 0===r&&(r=0),void 0===n&&(n=null),void 0===a&&(a=0),void 0===i&&(i=e.Format.U8_RGB),void 0===s&&(s=!1),void 0===_&&(_=0),void 0===u&&(u=0),void 0===E&&(E=0);var R=t,T=a||R.size,p=n||R.view,A=p&&p.constructor&&p.constructor.BYTES_PER_ELEMENT||G(i),d=R;if(!(R.usage&e.BufferUsage.MAP_READ&&R.usage&e.BufferUsage.COPY_DST)){var F=this.device.device.createCommandEncoder();d=this.device.createBuffer({usage:e.BufferUsage.STORAGE|e.BufferUsage.MAP_READ|e.BufferUsage.COPY_DST,hint:e.BufferFrequencyHint.STATIC,viewOrSize:T}),F.copyBufferToBuffer(R.gpuBuffer,r,d.gpuBuffer,0,T),this.device.device.queue.submit([F.finish()])}return new Promise((function(e,t){d.gpuBuffer.mapAsync(fr.READ,r,T).then((function(){var t=d.gpuBuffer.getMappedRange(r,T),n=p;if(s)n=null===n?wr(i,T,!0,t):wr(i,n.buffer,void 0,t);else if(null===n)switch(A){case 1:(n=new Uint8Array(T)).set(new Uint8Array(t));break;case 2:n=c.getHalfFloatAsFloatRGBAArrayBuffer(T/2,t);break;case 4:(n=new Float32Array(T/4)).set(new Float32Array(t))}else switch(A){case 1:(n=new Uint8Array(n.buffer)).set(new Uint8Array(t));break;case 2:n=c.getHalfFloatAsFloatRGBAArrayBuffer(T/2,t,p);break;case 4:var o=p&&p.constructor||Float32Array;(n=new o(n.buffer)).set(new o(t))}if(_!==u){1!==A||s||(_*=2,u*=2);for(var a=new Uint8Array(n.buffer),l=_,R=0,F=1;E>F;++F){R=F*u;for(var f=0;_>f;++f)a[l++]=a[R++]}n=0===A||s?new Uint8Array(a.buffer,0,l):new Float32Array(a.buffer,0,l/4)}d.gpuBuffer.unmap(),e(n)}),(function(e){return t(e)}))}))},r.prototype.getHalfFloatAsFloatRGBAArrayBuffer=function(e,t,r){r||(r=new Float32Array(e));for(var n,o,a,i,s=new Uint16Array(t);e--;)r[e]=(o=void 0,a=void 0,i=void 0,o=(32768&(n=s[e]))>>15,i=1023&n,0===(a=(31744&n)>>10)?(o?-1:1)*Math.pow(2,-14)*(i/1024):31==a?i?NaN:1/0*(o?-1:1):(o?-1:1)*Math.pow(2,a-15)*(1+i/1024));return r},r}(Hr),jr=function(){function t(e){this.device=e,this.gpuRenderPassEncoder=null,this.gfxColorAttachment=[],this.gfxColorAttachmentLevel=[],this.gfxColorResolveTo=[],this.gfxColorResolveToLevel=[],this.gfxDepthStencilAttachment=null,this.gfxDepthStencilResolveTo=null,this.gpuColorAttachments=[],this.gpuDepthStencilAttachment={view:null,depthLoadOp:"load",depthStoreOp:"store",stencilLoadOp:"load",stencilStoreOp:"store"},this.gpuRenderPassDescriptor={colorAttachments:this.gpuColorAttachments,depthStencilAttachment:this.gpuDepthStencilAttachment}}return t.prototype.getEncoder=function(){var e;return(null===(e=this.renderBundle)||void 0===e?void 0:e.renderBundleEncoder)||this.gpuRenderPassEncoder},t.prototype.getTextureView=function(e,t){return y(e.mipLevelCount>t),1===e.mipLevelCount?e.gpuTextureView:e.gpuTexture.createView({baseMipLevel:t,mipLevelCount:1})},t.prototype.setRenderPassDescriptor=function(t){var r,n,o,a,i,s;this.descriptor=t,this.gpuRenderPassDescriptor.colorAttachments=this.gpuColorAttachments;var l=t.colorAttachment.length;this.gfxColorAttachment.length=l,this.gfxColorResolveTo.length=l;for(var _=0;t.colorAttachment.length>_;_++){var u=t.colorAttachment[_],E=t.colorResolveTo[_];if(null===u&&null!==E&&(u=E,E=null),this.gfxColorAttachment[_]=u,this.gfxColorResolveTo[_]=E,this.gfxColorAttachmentLevel[_]=(null===(r=t.colorAttachmentLevel)||void 0===r?void 0:r[_])||0,this.gfxColorResolveToLevel[_]=(null===(n=t.colorResolveToLevel)||void 0===n?void 0:n[_])||0,null===u){this.gpuColorAttachments.length=_,this.gfxColorAttachment.length=_,this.gfxColorResolveTo.length=_;break}void 0===this.gpuColorAttachments[_]&&(this.gpuColorAttachments[_]={}),(R=this.gpuColorAttachments[_]).view=this.getTextureView(u,(null===(o=this.gfxColorAttachmentLevel)||void 0===o?void 0:o[_])||0);var c=null!==(i=null===(a=t.colorClearColor)||void 0===a?void 0:a[_])&&void 0!==i?i:"load";"load"===c?R.loadOp="load":(R.loadOp="clear",R.clearValue=c),R.storeOp=(null===(s=t.colorStore)||void 0===s?void 0:s[_])?"store":"discard",R.resolveTarget=void 0,null!==E&&(u.sampleCount>1?R.resolveTarget=this.getTextureView(E,this.gfxColorResolveToLevel[_]):R.storeOp="store")}if(this.gfxDepthStencilAttachment=t.depthStencilAttachment,this.gfxDepthStencilResolveTo=t.depthStencilResolveTo,t.depthStencilAttachment){var R,T=t.depthStencilAttachment;(R=this.gpuDepthStencilAttachment).view=T.gpuTextureView,!!(U(T.format)&e.FormatFlags.Depth)?("load"===t.depthClearValue?R.depthLoadOp="load":(R.depthLoadOp="clear",R.depthClearValue=t.depthClearValue),R.depthStoreOp=t.depthStencilStore||null!==this.gfxDepthStencilResolveTo?"store":"discard"):(R.depthLoadOp=void 0,R.depthStoreOp=void 0),!!(U(T.format)&e.FormatFlags.Stencil)?("load"===t.stencilClearValue?R.stencilLoadOp="load":(R.stencilLoadOp="clear",R.stencilClearValue=t.stencilClearValue),R.stencilStoreOp=t.depthStencilStore||null!==this.gfxDepthStencilResolveTo?"store":"discard"):(R.stencilLoadOp=void 0,R.stencilStoreOp=void 0),this.gpuRenderPassDescriptor.depthStencilAttachment=this.gpuDepthStencilAttachment}else this.gpuRenderPassDescriptor.depthStencilAttachment=void 0;this.gpuRenderPassDescriptor.occlusionQuerySet=ge(t.occlusionQueryPool)?void 0:t.occlusionQueryPool.querySet},t.prototype.beginRenderPass=function(e,t){y(null===this.gpuRenderPassEncoder),this.setRenderPassDescriptor(t),this.frameCommandEncoder=e,this.gpuRenderPassEncoder=this.frameCommandEncoder.beginRenderPass(this.gpuRenderPassDescriptor)},t.prototype.flipY=function(e,t){return this.device.swapChainHeight-e-t},t.prototype.setViewport=function(e,t,r,n,o,a){void 0===o&&(o=0),void 0===a&&(a=1),this.gpuRenderPassEncoder.setViewport(e,this.flipY(t,n),r,n,o,a)},t.prototype.setScissorRect=function(e,t,r,n){this.gpuRenderPassEncoder.setScissorRect(e,this.flipY(t,n),r,n)},t.prototype.setPipeline=function(e){var t=b(e.gpuRenderPipeline);this.getEncoder().setPipeline(t)},t.prototype.setVertexInput=function(e,t,r){if(null!==e){var n=this.getEncoder(),o=e;null!==r&&n.setIndexBuffer(Nr(r.buffer),b(o.indexFormat),r.offset);for(var a=0;t.length>a;a++){var i=t[a];null!==i&&n.setVertexBuffer(a,Nr(i.buffer),i.offset)}}},t.prototype.setBindings=function(e){var t=e,r=this.getEncoder();t.gpuBindGroup.forEach((function(e,n){e&&r.setBindGroup(n,t.gpuBindGroup[n])}))},t.prototype.setStencilReference=function(e){this.gpuRenderPassEncoder.setStencilReference(e)},t.prototype.draw=function(e,t,r,n){this.getEncoder().draw(e,t,r,n)},t.prototype.drawIndexed=function(e,t,r,n,o){this.getEncoder().drawIndexed(e,t,r,n,o)},t.prototype.drawIndirect=function(e,t){this.getEncoder().drawIndirect(Nr(e),t)},t.prototype.drawIndexedIndirect=function(e,t){this.getEncoder().drawIndexedIndirect(Nr(e),t)},t.prototype.beginOcclusionQuery=function(e){this.gpuRenderPassEncoder.beginOcclusionQuery(e)},t.prototype.endOcclusionQuery=function(){this.gpuRenderPassEncoder.endOcclusionQuery()},t.prototype.pushDebugGroup=function(e){this.gpuRenderPassEncoder.pushDebugGroup(e)},t.prototype.popDebugGroup=function(){this.gpuRenderPassEncoder.popDebugGroup()},t.prototype.insertDebugMarker=function(e){this.gpuRenderPassEncoder.insertDebugMarker(e)},t.prototype.beginBundle=function(e){this.renderBundle=e},t.prototype.endBundle=function(){this.renderBundle.finish()},t.prototype.executeBundles=function(e){this.gpuRenderPassEncoder.executeBundles(e.map((function(e){return e.renderBundle})))},t.prototype.finish=function(){var e;null===(e=this.gpuRenderPassEncoder)||void 0===e||e.end(),this.gpuRenderPassEncoder=null;for(var t=0;this.gfxColorAttachment.length>t;t++){var r=this.gfxColorAttachment[t],n=this.gfxColorResolveTo[t];null!==r&&null!==n&&1===r.sampleCount&&this.copyAttachment(n,this.gfxColorAttachmentLevel[t],r,this.gfxColorResolveToLevel[t])}this.gfxDepthStencilAttachment&&this.gfxDepthStencilResolveTo&&(this.gfxDepthStencilAttachment.sampleCount>1||this.copyAttachment(this.gfxDepthStencilResolveTo,0,this.gfxDepthStencilAttachment,0)),this.frameCommandEncoder=null},t.prototype.copyAttachment=function(e,t,r,n){y(1===r.sampleCount);var o={texture:r.gpuTexture,mipLevel:n},a={texture:e.gpuTexture,mipLevel:t};y(r.width>>>n==e.width>>>t),y(r.height>>>n==e.height>>>t),y(!!(r.usage&Fr.COPY_SRC)),y(!!(e.usage&Fr.COPY_DST)),this.frameCommandEncoder.copyTextureToTexture(o,a,[e.width,e.height,1])},t}(),$r=function(t){function r(r){var n=r.descriptor,o=t.call(this,{id:r.id,device:r.device})||this;return o.type=e.ResourceType.RenderPipeline,o.isCreatingAsync=!1,o.gpuRenderPipeline=null,o.descriptor=n,o.device.createRenderPipelineInternal(o,!1),o}return pe(r,t),r.prototype.getBindGroupLayout=function(e){return this.gpuRenderPipeline.getBindGroupLayout(e)},r}(Hr),Jr=function(t){function r(r){var n,o,a=r.descriptor,i=t.call(this,{id:r.id,device:r.device})||this;i.type=e.ResourceType.Sampler;var s=a.lodMinClamp,l=a.mipmapFilter===e.MipmapFilterMode.NO_MIP?a.lodMinClamp:a.lodMaxClamp,_=null!==(n=a.maxAnisotropy)&&void 0!==n?n:1;return _>1&&y(a.minFilter===e.FilterMode.BILINEAR&&a.magFilter===e.FilterMode.BILINEAR&&a.mipmapFilter===e.MipmapFilterMode.LINEAR),i.gpuSampler=i.device.device.createSampler({addressModeU:Sr(a.addressModeU),addressModeV:Sr(a.addressModeV),addressModeW:Sr(null!==(o=a.addressModeW)&&void 0!==o?o:a.addressModeU),lodMinClamp:s,lodMaxClamp:l,minFilter:gr(a.minFilter),magFilter:gr(a.magFilter),mipmapFilter:Br(a.mipmapFilter),compare:void 0!==a.compareFunction?yr(a.compareFunction):void 0,maxAnisotropy:_}),i}return pe(r,t),r}(Hr),en=function(t){function r(r){var n=r.descriptor,o=r.skipCreate,a=r.sampleCount,i=t.call(this,{id:r.id,device:r.device})||this;i.type=e.ResourceType.Texture,i.flipY=!1;var s=n.format,l=n.dimension,_=n.width,u=n.height,E=n.depthOrArrayLayers,c=n.mipLevelCount,R=n.usage,T=n.pixelStore;return i.flipY=!!(null==T?void 0:T.unpackFlipY),i.device.createTextureShared({format:s,dimension:null!=l?l:e.TextureDimension.TEXTURE_2D,width:_,height:u,depthOrArrayLayers:null!=E?E:1,mipLevelCount:null!=c?c:1,usage:R,sampleCount:null!=a?a:1},i,o),i}return pe(r,t),r.prototype.textureFromImageBitmapOrCanvas=function(e,t,r){for(var n=t[0].width,o=t[0].height,a={size:{width:n,height:o,depthOrArrayLayers:r},format:"rgba8unorm",usage:GPUTextureUsage.TEXTURE_BINDING|GPUTextureUsage.COPY_DST|GPUTextureUsage.RENDER_ATTACHMENT},i=e.createTexture(a),s=0;t.length>s;s++)e.queue.copyExternalImageToTexture({source:t[s],flipY:this.flipY},{texture:i,origin:[0,0,s]},[n,o]);return[i,n,o]},r.prototype.isImageBitmapOrCanvases=function(e){var t=e[0];return t instanceof ImageBitmap||t instanceof HTMLCanvasElement||t instanceof OffscreenCanvas},r.prototype.isVideo=function(e){return e[0]instanceof HTMLVideoElement},r.prototype.setImageData=function(e,t){var r,n,o,a,i=this,s=this.device.device;if(this.isImageBitmapOrCanvases(e))n=(r=me(this.textureFromImageBitmapOrCanvas(s,e,this.depthOrArrayLayers),3))[0],o=r[1],a=r[2];else if(this.isVideo(e))n=s.importExternalTexture({source:e[0]});else{var l=Wr(this.gpuTextureformat),_=Math.ceil(this.width/l.width)*l.length;e.forEach((function(e){s.queue.writeTexture({texture:i.gpuTexture},e,{bytesPerRow:_},{width:i.width,height:i.height})}))}this.width=o,this.height=a,n&&(this.gpuTexture=n),this.gpuTextureView=this.gpuTexture.createView({dimension:hr(this.dimension)})},r.prototype.destroy=function(){t.prototype.destroy.call(this),this.gpuTexture.destroy()},r}(Hr),tn=function(t){function r(r){var n=t.call(this,{id:r.id,device:r.device})||this;return n.type=e.ResourceType.RenderBundle,n.renderBundleEncoder=n.device.device.createRenderBundleEncoder({colorFormats:[n.device.swapChainFormat]}),n}return pe(r,t),r.prototype.finish=function(){this.renderBundle=this.renderBundleEncoder.finish()},r}(Hr),rn=function(){function t(t,r,n,o,a,i){this.swapChainWidth=0,this.swapChainHeight=0,this.swapChainTextureUsage=Fr.RENDER_ATTACHMENT|Fr.COPY_DST,this._resourceUniqueId=0,this.renderPassPool=[],this.computePassPool=[],this.frameCommandEncoderPool=[],this.featureTextureCompressionBC=!1,this.platformString="WebGPU",this.glslVersion="#version 440",this.explicitBindingLocations=!0,this.separateSamplerTextures=!0,this.viewportOrigin=e.ViewportOrigin.UPPER_LEFT,this.clipSpaceNearZ=e.ClipSpaceNearZ.ZERO,this.supportsSyncPipelineCompilation=!1,this.supportMRT=!0,this.device=r,this.canvas=n,this.canvasContext=o,this.glsl_compile=a,this.WGSLComposer=i,this.fallbackTexture2D=this.createFallbackTexture(e.TextureDimension.TEXTURE_2D,e.SamplerFormatKind.Float),this.setResourceName(this.fallbackTexture2D,"Fallback Texture2D"),this.fallbackTexture2DDepth=this.createFallbackTexture(e.TextureDimension.TEXTURE_2D,e.SamplerFormatKind.Depth),this.setResourceName(this.fallbackTexture2DDepth,"Fallback Depth Texture2D"),this.fallbackTexture2DArray=this.createFallbackTexture(e.TextureDimension.TEXTURE_2D_ARRAY,e.SamplerFormatKind.Float),this.setResourceName(this.fallbackTexture2DArray,"Fallback Texture2DArray"),this.fallbackTexture3D=this.createFallbackTexture(e.TextureDimension.TEXTURE_3D,e.SamplerFormatKind.Float),this.setResourceName(this.fallbackTexture3D,"Fallback Texture3D"),this.fallbackTextureCube=this.createFallbackTexture(e.TextureDimension.TEXTURE_CUBE_MAP,e.SamplerFormatKind.Float),this.setResourceName(this.fallbackTextureCube,"Fallback TextureCube"),this.fallbackSamplerFiltering=this.createSampler({addressModeU:e.AddressMode.REPEAT,addressModeV:e.AddressMode.REPEAT,minFilter:e.FilterMode.POINT,magFilter:e.FilterMode.POINT,mipmapFilter:e.MipmapFilterMode.NEAREST}),this.setResourceName(this.fallbackSamplerFiltering,"Fallback Sampler Filtering"),this.fallbackSamplerComparison=this.createSampler({addressModeU:e.AddressMode.REPEAT,addressModeV:e.AddressMode.REPEAT,minFilter:e.FilterMode.POINT,magFilter:e.FilterMode.POINT,mipmapFilter:e.MipmapFilterMode.NEAREST,compareFunction:e.CompareFunction.ALWAYS}),this.setResourceName(this.fallbackSamplerComparison,"Fallback Sampler Comparison Filtering"),this.device.features&&(this.featureTextureCompressionBC=this.device.features.has("texture-compression-bc")),this.device.onuncapturederror=function(e){console.error(e.error)},this.swapChainFormat=navigator.gpu.getPreferredCanvasFormat(),this.canvasContext.configure({device:this.device,format:this.swapChainFormat,usage:this.swapChainTextureUsage,alphaMode:"premultiplied"})}return t.prototype.destroy=function(){},t.prototype.configureSwapChain=function(e,t){this.swapChainWidth===e&&this.swapChainHeight===t||(this.swapChainWidth=e,this.swapChainHeight=t)},t.prototype.getOnscreenTexture=function(){var t=this.canvasContext.getCurrentTexture(),r=t.createView(),n=new en({id:0,device:this,descriptor:{format:e.Format.U8_RGBA_RT,width:this.swapChainWidth,height:this.swapChainHeight,depthOrArrayLayers:0,dimension:e.TextureDimension.TEXTURE_2D,mipLevelCount:1,usage:this.swapChainTextureUsage},skipCreate:!0});return n.depthOrArrayLayers=1,n.sampleCount=1,n.gpuTexture=t,n.gpuTextureView=r,n.name="Onscreen",this.setResourceName(n,"Onscreen Texture"),n},t.prototype.getDevice=function(){return this},t.prototype.getCanvas=function(){return this.canvas},t.prototype.beginFrame=function(){y(0===this.frameCommandEncoderPool.length)},t.prototype.endFrame=function(){y(this.frameCommandEncoderPool.every((function(e){return null!==e}))),this.device.queue.submit(this.frameCommandEncoderPool.map((function(e){return e.finish()}))),this.frameCommandEncoderPool=[]},t.prototype.getNextUniqueId=function(){return++this._resourceUniqueId},t.prototype.createBuffer=function(e){return new kr({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createTexture=function(e){return new en({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createSampler=function(e){return new Jr({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createRenderTarget=function(t){var r=new en({id:this.getNextUniqueId(),device:this,descriptor:Ae(Ae({},t),{dimension:e.TextureDimension.TEXTURE_2D,mipLevelCount:1,depthOrArrayLayers:1,usage:e.TextureUsage.RENDER_TARGET}),sampleCount:t.sampleCount});return r.depthOrArrayLayers=1,r.type=e.ResourceType.RenderTarget,r},t.prototype.createRenderTargetFromTexture=function(t){var r=t.format,n=t.width,o=t.height,a=t.depthOrArrayLayers,i=t.sampleCount,s=t.mipLevelCount,l=t.gpuTexture,_=t.gpuTextureView,u=t.usage;y(!!(u&Fr.RENDER_ATTACHMENT));var E=new en({id:this.getNextUniqueId(),device:this,descriptor:{format:r,width:n,height:o,depthOrArrayLayers:a,dimension:e.TextureDimension.TEXTURE_2D,mipLevelCount:s,usage:u},skipCreate:!0});return E.depthOrArrayLayers=a,E.sampleCount=i,E.gpuTexture=l,E.gpuTextureView=_,E},t.prototype.createProgram=function(e){var t,r;return(null===(t=e.vertex)||void 0===t?void 0:t.glsl)&&(e.vertex.glsl=Tt(this.queryVendorInfo(),"vert",e.vertex.glsl)),(null===(r=e.fragment)||void 0===r?void 0:r.glsl)&&(e.fragment.glsl=Tt(this.queryVendorInfo(),"frag",e.fragment.glsl)),new zr({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createProgramSimple=function(e){return new zr({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createTextureShared=function(t,r,n){var o={width:t.width,height:t.height,depthOrArrayLayers:t.depthOrArrayLayers},a=t.mipLevelCount,i=mr(t.format),s=function(t){if(t===e.TextureDimension.TEXTURE_2D)return"2d";if(t===e.TextureDimension.TEXTURE_CUBE_MAP)return"2d";if(t===e.TextureDimension.TEXTURE_2D_ARRAY)return"2d";if(t===e.TextureDimension.TEXTURE_3D)return"3d";throw Error("whoops")}(t.dimension),l=function(t){var r=0;return t&e.TextureUsage.SAMPLED&&(r|=Fr.TEXTURE_BINDING|Fr.COPY_DST|Fr.COPY_SRC),t&e.TextureUsage.STORAGE&&(r|=Fr.TEXTURE_BINDING|Fr.STORAGE_BINDING|Fr.COPY_SRC|Fr.COPY_DST),t&e.TextureUsage.RENDER_TARGET&&(r|=Fr.RENDER_ATTACHMENT|Fr.TEXTURE_BINDING|Fr.COPY_SRC|Fr.COPY_DST),r}(t.usage);if(r.gpuTextureformat=i,r.dimension=t.dimension,r.format=t.format,r.width=t.width,r.height=t.height,r.depthOrArrayLayers=t.depthOrArrayLayers,r.mipLevelCount=a,r.usage=l,r.sampleCount=t.sampleCount,!n){var _=this.device.createTexture({size:o,mipLevelCount:a,format:i,dimension:s,sampleCount:t.sampleCount,usage:l}),u=_.createView();r.gpuTexture=_,r.gpuTextureView=u}},t.prototype.getFallbackSampler=function(t){return t.formatKind===e.SamplerFormatKind.Depth&&t.comparison?this.fallbackSamplerComparison:this.fallbackSamplerFiltering},t.prototype.getFallbackTexture=function(t){var r=t.dimension;if(r===e.TextureDimension.TEXTURE_2D)return t.formatKind===e.SamplerFormatKind.Depth?this.fallbackTexture2DDepth:this.fallbackTexture2D;if(r===e.TextureDimension.TEXTURE_2D_ARRAY)return this.fallbackTexture2DArray;if(r===e.TextureDimension.TEXTURE_3D)return this.fallbackTexture3D;if(r===e.TextureDimension.TEXTURE_CUBE_MAP)return this.fallbackTextureCube;throw Error("whoops")},t.prototype.createFallbackTexture=function(t,r){return this.createTexture({dimension:t,format:r===e.SamplerFormatKind.Float?e.Format.U8_RGBA_NORM:e.Format.D24,usage:e.TextureUsage.SAMPLED,width:1,height:1,depthOrArrayLayers:t===e.TextureDimension.TEXTURE_CUBE_MAP?6:1,mipLevelCount:1})},t.prototype.createBindings=function(e){return new Vr({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createInputLayout=function(e){return new qr({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createComputePipeline=function(e){return new Kr({id:this.getNextUniqueId(),device:this,descriptor:e})},t.prototype.createRenderPipeline=function(e){return new $r({id:this.getNextUniqueId(),device:this,descriptor:Ae({},e)})},t.prototype.createQueryPool=function(e,t){return new Zr({id:this.getNextUniqueId(),device:this,descriptor:{type:e,elemCount:t}})},t.prototype.createRenderPipelineInternal=function(t,r){var n;if(null===t.gpuRenderPipeline){var o=t.descriptor,a=o.program,i=a.vertexStage,s=a.fragmentStage;if(null!==i&&null!==s){var l=o.megaStateDescriptor||{},_=l.stencilBack,u=l.stencilFront,E=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);n.length>o;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}(l,["stencilBack","stencilFront"]),c=se(ue);o.megaStateDescriptor=Ae(Ae(Ae({},c),{stencilBack:Ae(Ae({},c.stencilBack),_),stencilFront:Ae(Ae({},c.stencilFront),u)}),E);var R=o.megaStateDescriptor.attachmentsState[0];o.colorAttachmentFormats.forEach((function(e,t){o.megaStateDescriptor.attachmentsState[t]||(o.megaStateDescriptor.attachmentsState[t]=oe(void 0,R))}));var T,p,A=(T=null!==(n=o.topology)&&void 0!==n?n:e.PrimitiveTopology.TRIANGLES,p=o.megaStateDescriptor,{topology:Lr(T),cullMode:Or(p.cullMode),frontFace:Mr(p.frontFace)}),d=vr(o.colorAttachmentFormats,o.megaStateDescriptor),F=function(e,t){if(!ge(e))return{format:mr(e),depthWriteEnabled:!!t.depthWrite,depthCompare:yr(t.depthCompare),depthBias:t.polygonOffset?t.polygonOffsetUnits:0,depthBiasSlopeScale:t.polygonOffset?t.polygonOffsetFactor:0,stencilFront:{compare:yr(t.stencilFront.compare),passOp:br(t.stencilFront.passOp),failOp:br(t.stencilFront.failOp),depthFailOp:br(t.stencilFront.depthFailOp)},stencilBack:{compare:yr(t.stencilBack.compare),passOp:br(t.stencilBack.passOp),failOp:br(t.stencilBack.failOp),depthFailOp:br(t.stencilBack.depthFailOp)},stencilReadMask:4294967295,stencilWriteMask:4294967295}}(o.depthStencilAttachmentFormat,o.megaStateDescriptor),f=void 0;null!==o.inputLayout&&(f=o.inputLayout.buffers);var m=o.sampleCount,h={layout:"auto",vertex:Ae(Ae({},i),{buffers:f}),primitive:A,depthStencil:F,multisample:{count:m},fragment:Ae(Ae({},s),{targets:d})};t.gpuRenderPipeline=this.device.createRenderPipeline(h)}}},t.prototype.createReadback=function(){return new Qr({id:this.getNextUniqueId(),device:this})},t.prototype.createRenderBundle=function(){return new tn({id:this.getNextUniqueId(),device:this})},t.prototype.createRenderPass=function(e){var t=this.renderPassPool.pop();void 0===t&&(t=new jr(this));var r=this.frameCommandEncoderPool.pop();return void 0===r&&(r=this.device.createCommandEncoder()),t.beginRenderPass(r,e),t},t.prototype.createComputePass=function(){var e=this.computePassPool.pop();void 0===e&&(e=new Yr);var t=this.frameCommandEncoderPool.pop();return void 0===t&&(t=this.device.createCommandEncoder()),e.beginComputePass(t),e},t.prototype.submitPass=function(e){var t=e;t instanceof jr?(this.frameCommandEncoderPool.push(t.frameCommandEncoder),t.finish(),this.renderPassPool.push(t)):t instanceof Yr&&(this.frameCommandEncoderPool.push(t.frameCommandEncoder),t.finish(),this.computePassPool.push(t))},t.prototype.copySubTexture2D=function(e,t,r,n,o,a,i){var s=this.device.createCommandEncoder(),l=e,_=n,u={texture:_.gpuTexture,origin:[o,a,0],mipLevel:0,aspect:"all"},E={texture:l.gpuTexture,origin:[t,r,0],mipLevel:0,aspect:"all"};y(!!(_.usage&Fr.COPY_SRC)),y(!!(l.usage&Fr.COPY_DST)),s.copyTextureToTexture(u,E,[_.width,_.height,i||1]),this.device.queue.submit([s.finish()])},t.prototype.queryLimits=function(){return{uniformBufferMaxPageWordSize:this.device.limits.maxUniformBufferBindingSize>>>2,uniformBufferWordAlignment:this.device.limits.minUniformBufferOffsetAlignment>>>2,supportedSampleCounts:[1],occlusionQueriesRecommended:!0,computeShadersSupported:!0}},t.prototype.queryTextureFormatSupported=function(t,r,n){if(function(t){switch(I(t)){case e.FormatTypeFlags.BC1:case e.FormatTypeFlags.BC2:case e.FormatTypeFlags.BC3:case e.FormatTypeFlags.BC4_SNORM:case e.FormatTypeFlags.BC4_UNORM:case e.FormatTypeFlags.BC5_SNORM:case e.FormatTypeFlags.BC5_UNORM:return!0;default:return!1}}(t)){if(!this.featureTextureCompressionBC)return!1;var o=function(t){switch(I(t)){case e.FormatTypeFlags.BC1:case e.FormatTypeFlags.BC2:case e.FormatTypeFlags.BC3:case e.FormatTypeFlags.BC4_SNORM:case e.FormatTypeFlags.BC4_UNORM:case e.FormatTypeFlags.BC5_SNORM:case e.FormatTypeFlags.BC5_UNORM:return 4;default:return 1}}(t);return r%o==0&&n%o==0&&this.featureTextureCompressionBC}switch(t){case e.Format.U16_RGBA_NORM:case e.Format.F32_RGBA:return!1}return!0},t.prototype.queryPlatformAvailable=function(){return!0},t.prototype.queryVendorInfo=function(){return this},t.prototype.queryRenderPass=function(e){return e.descriptor},t.prototype.queryRenderTarget=function(e){return e},t.prototype.setResourceName=function(t,r){if(t.name=r,t.type===e.ResourceType.Buffer)(n=t).gpuBuffer.label=r;else if(t.type===e.ResourceType.Texture){(n=t).gpuTexture.label=r,n.gpuTextureView.label=r}else if(t.type===e.ResourceType.RenderTarget){(n=t).gpuTexture.label=r,n.gpuTextureView.label=r}else if(t.type===e.ResourceType.Sampler){(n=t).gpuSampler.label=r}else if(t.type===e.ResourceType.RenderPipeline){var n;null!==(n=t).gpuRenderPipeline&&(n.gpuRenderPipeline.label=r)}},t.prototype.setResourceLeakCheck=function(e,t){},t.prototype.checkForLeaks=function(){},t.prototype.programPatched=function(e){},t.prototype.pipelineQueryReady=function(e){return null!==e.gpuRenderPipeline},t.prototype.pipelineForceReady=function(e){this.createRenderPipelineInternal(e,!1)},t}(),nn=function(){function e(e){this.pluginOptions=e}return e.prototype.createSwapChain=function(e){return de(this,void 0,void 0,(function(){var t,r,n,o,a,i;return Fe(this,(function(s){switch(s.label){case 0:if(void 0===globalThis.navigator.gpu)return[2,null];t=null,s.label=1;case 1:return s.trys.push([1,3,,4]),[4,globalThis.navigator.gpu.requestAdapter({xrCompatible:this.pluginOptions.xrCompatible})];case 2:return t=s.sent(),[3,4];case 3:return r=s.sent(),console.log(r),[3,4];case 4:return null===t?[2,null]:(n=["depth32float-stencil8","texture-compression-bc","float32-filterable"].filter((function(e){return t.features.has(e)})),[4,t.requestDevice({requiredFeatures:n})]);case 5:if((o=s.sent())&&(a=this.pluginOptions.onContextLost,o.lost.then((function(){a&&a()}))),null===o)return[2,null];if(!(i=e.getContext("webgpu")))return[2,null];s.label=6;case 6:return s.trys.push([6,8,,9]),[4,dr(this.pluginOptions.shaderCompilerPath)];case 7:case 8:return s.sent(),[3,9];case 9:return[2,new rn(t,o,e,i,Tr,pr&&new pr)]}}))}))},e}();e.IsDepthReversed=K,e.OpaqueBlack=V,e.OpaqueWhite=Y,e.TransparentBlack=H,e.TransparentWhite=k,e.UNIFORM_SETTERS=at,e.WebGLDeviceContribution=$t,e.WebGPUDeviceContribution=nn,e.align=j,e.alignNonPowerOfTwo=function(e,t){return((e+t-1)/t|0)*t},e.arrayCopy=Me,e.arrayEqual=Oe,e.assert=y,e.assertExists=b,e.bindingsDescriptorCopy=function(e){return{samplerBindings:e.samplerBindings&&Me(e.samplerBindings,we),uniformBufferBindings:e.uniformBufferBindings&&Me(e.uniformBufferBindings,We),storageBufferBindings:e.storageBufferBindings&&Me(e.storageBufferBindings,We),storageTextureBindings:e.storageTextureBindings&&Me(e.storageTextureBindings,He),pipeline:e.pipeline}},e.bindingsDescriptorEquals=function(e,t){return e.samplerBindings=e.samplerBindings||[],e.uniformBufferBindings=e.uniformBufferBindings||[],e.storageBufferBindings=e.storageBufferBindings||[],e.storageTextureBindings=e.storageTextureBindings||[],t.samplerBindings=t.samplerBindings||[],t.uniformBufferBindings=t.uniformBufferBindings||[],t.storageBufferBindings=t.storageBufferBindings||[],t.storageTextureBindings=t.storageTextureBindings||[],e.samplerBindings.length===t.samplerBindings.length&&(!!Oe(e.samplerBindings,t.samplerBindings,De)&&(!!Oe(e.uniformBufferBindings,t.uniformBufferBindings,Ue)&&(!!Oe(e.storageBufferBindings,t.storageBufferBindings,Ue)&&!!Oe(e.storageTextureBindings,t.storageTextureBindings,Ie))))},e.bisectRight=$,e.bufferBindingCopy=We,e.colorCopy=X,e.colorEqual=x,e.colorNewCopy=w,e.colorNewFromRGBA=W,e.compareDepthValues=function(t,r,n,o){if(void 0===o&&(o=K),(n=q(n,o))===e.CompareFunction.LESS)return r>t;if(n===e.CompareFunction.LEQUAL)return r>=t;if(n===e.CompareFunction.GREATER)return t>r;if(n===e.CompareFunction.GEQUAL)return t>=r;throw Error("whoops")},e.copyAttachmentState=oe,e.copyAttachmentStateFromSimple=le,e.copyMegaState=se,e.copyStencilFaceState=ne,e.defaultBindingLayoutSamplerDescriptor=Re,e.defaultMegaState=ue,e.fallbackUndefined=Z,e.fillArray=function(e,t,r){e.length=t,e.fill(r)},e.fullscreenMegaState=ce,e.getAttributeLocations=Et,e.getDefines=ut,e.getFormatByteSize=P,e.getFormatCompByteSize=G,e.getFormatCompFlags=M,e.getFormatCompFlagsComponentCount=L,e.getFormatComponentCount=function(e){return M(e)},e.getFormatFlags=U,e.getFormatSamplerKind=v,e.getFormatTypeFlags=I,e.getFormatTypeFlagsByteSize=D,e.getUniformSetter=it,e.getUniforms=function(e){var t=[],r=[];return e.replace(/\s*struct\s*(.*)\s*{((?:\s*.*\s*)*?)};/g,(function(e,t,n){var o=[];return n.trim().replace("\r\n","\n").split("\n").forEach((function(e){var t=me(e.trim().split(/\s+/),2),r=t[1];o.push({type:t[0].trim(),name:r.replace(";","").trim()})})),r.push({type:t.trim(),uniforms:o}),""})),e.replace(/\s*uniform(?:\s+)(?:\w+)(?:\s?){([^]*?)};?/g,(function(e,n){return n.trim().replace("\r\n","\n").split("\n").forEach((function(e){var n=e.trim().split(" "),o=n[0]||"",a=n[1]||"",i=a.indexOf("[")>-1;if(a=a.replace(";","").replace("[","").trim(),!o.startsWith("#")){if(o){var s=r.find((function(e){return o===e.type}));if(s)if(i)for(var l=function(e){s.uniforms.forEach((function(r){t.push("".concat(a,"[").concat(e,"].").concat(r.name))}))},_=0;5>_;_++)l(_);else s.uniforms.forEach((function(e){t.push("".concat(a,".").concat(e.name))}))}a&&t.push(a)}})),""})),t},e.inputLayoutBufferDescriptorCopy=ke,e.inputLayoutBufferDescriptorEquals=Xe,e.inputLayoutDescriptorCopy=function(e){return{vertexBufferDescriptors:Me(e.vertexBufferDescriptors,ke),indexBufferFormat:e.indexBufferFormat,program:e.program}},e.inputLayoutDescriptorEquals=function(e,t){return e.indexBufferFormat===t.indexBufferFormat&&(!!Oe(e.vertexBufferDescriptors,t.vertexBufferDescriptors,Xe)&&!!ye(e.program,t.program))},e.isPowerOfTwo=z,e.isTypedArray=lt,e.leftPad=te,e.makeFormat=O,e.makeMegaState=Ee,e.makeTextureDescriptor2D=function(t,r,n,o){return{dimension:e.TextureDimension.TEXTURE_2D,format:t,width:r,height:n,depthOrArrayLayers:1,mipLevelCount:o,usage:e.TextureUsage.SAMPLED}},e.nArray=J,e.nullify=Q,e.parseUniformName=Ke,e.prependLineNo=ee,e.preprocessProgram_GLSL=function(e,t,r,n){return void 0===n&&(n=null),{vert:t,frag:r,preprocessedVert:Tt(e,"vert",t,n),preprocessedFrag:Tt(e,"frag",r,n)}},e.preprocessShader_GLSL=Tt,e.range=function(e,t){for(var r=[],n=e;e+t>n;n++)r.push(n);return r},e.renderPipelineDescriptorCopy=function(e){var t=e.program,r=e.topology;return{inputLayout:e.inputLayout,megaStateDescriptor:e.megaStateDescriptor&&se(e.megaStateDescriptor),program:t,topology:r,colorAttachmentFormats:e.colorAttachmentFormats.slice(),depthStencilAttachmentFormat:e.depthStencilAttachmentFormat,sampleCount:e.sampleCount}},e.renderPipelineDescriptorEquals=function(e,t){return e.topology===t.topology&&(e.inputLayout===t.inputLayout&&(e.sampleCount===t.sampleCount&&(!(e.megaStateDescriptor&&t.megaStateDescriptor&&!function(e,t){return!(!Oe(e.attachmentsState,t.attachmentsState,Pe)||e.blendConstant&&t.blendConstant&&!x(e.blendConstant,t.blendConstant)||e.stencilFront&&t.stencilFront&&!ve(e.stencilFront,t.stencilFront)||e.stencilBack&&t.stencilBack&&!ve(e.stencilBack,t.stencilBack)||e.depthCompare!==t.depthCompare||e.depthWrite!==t.depthWrite||e.stencilWrite!==t.stencilWrite||e.cullMode!==t.cullMode||e.frontFace!==t.frontFace||e.polygonOffset!==t.polygonOffset||e.polygonOffsetFactor!==t.polygonOffsetFactor||e.polygonOffsetUnits!==t.polygonOffsetUnits)}(e.megaStateDescriptor,t.megaStateDescriptor))&&(!!ye(e.program,t.program)&&(!!Oe(e.colorAttachmentFormats,t.colorAttachmentFormats,be)&&e.depthStencilAttachmentFormat===t.depthStencilAttachmentFormat)))))},e.reverseDepthForClearValue=function(e,t){return void 0===t&&(t=K),t?1-e:e},e.reverseDepthForCompareFunction=q,e.reverseDepthForDepthOffset=function(e,t){return void 0===t&&(t=K),t?-e:e},e.reverseDepthForOrthographicProjectionMatrix=function(e,t){void 0===t&&(t=K),t&&(e[10]=-e[10],e[14]=1-e[14])},e.reverseDepthForPerspectiveProjectionMatrix=function(e,t){void 0===t&&(t=K),t&&(e[10]=-e[10],e[14]=-e[14])},e.samplerBindingCopy=we,e.samplerDescriptorEquals=function(e,t){return e.addressModeU===t.addressModeU&&e.addressModeV===t.addressModeV&&e.minFilter===t.minFilter&&e.magFilter===t.magFilter&&e.mipmapFilter===t.mipmapFilter&&e.lodMinClamp===t.lodMinClamp&&e.lodMaxClamp===t.lodMaxClamp&&e.maxAnisotropy===t.maxAnisotropy&&e.compareFunction===t.compareFunction},e.setAttachmentStateSimple=function(e,t){return void 0===e.attachmentsState&&(e.attachmentsState=[],ae(e.attachmentsState,ue.attachmentsState)),le(e.attachmentsState[0],t),e},e.setBitFlagEnabled=function(e,t,r){return r?e|=t:e&=~t,e},e.setFormatComponentCount=function(e,t){return 4294902015&e|t<<8},e.setFormatFlags=function(e,t){return 4294967040&e|t},e.setMegaStateFlags=ie,e.spliceBisectRight=function(e,t,r){var n=$(e,t,r);e.splice(n,0,t)},e.stencilFaceStateEquals=ve,e.textureBindingCopy=He,e.vertexAttributeDescriptorCopy=Ve,e.vertexAttributeDescriptorEquals=xe}));
//# sourceMappingURL=index.umd.min.js.map
