import { ResourceType } from '../api';
import { Format, RenderTarget, RenderTargetDescriptor, Texture } from '../api';
import type { Device_GL } from './Device';
import { ResourceBase_GL } from './ResourceBase';
export declare class RenderTarget_GL extends ResourceBase_GL implements RenderTarget {
    type: ResourceType.RenderTarget;
    gl_renderbuffer: WebGLRenderbuffer | null;
    texture: Texture | null;
    format: Format;
    width: number;
    height: number;
    sampleCount: number;
    constructor({ id, device, descriptor, }: {
        id: number;
        device: Device_GL;
        descriptor: RenderTargetDescriptor;
    });
    destroy(): void;
}
