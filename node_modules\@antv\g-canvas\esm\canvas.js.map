{"version": 3, "file": "canvas.js", "sourceRoot": "", "sources": ["../src/canvas.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAG9C,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,KAAK,MAAM,SAAS,CAAC;AAC5B,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AAC9F,OAAO,EAAE,mBAAmB,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAExH;IAAqB,0BAAc;IAAnC;;IA4OA,CAAC;IA3OC,8BAAa,GAAb;QACE,IAAM,GAAG,GAAG,iBAAM,aAAa,WAAE,CAAC;QAClC,sBAAsB;QACtB,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;QAC3B,yBAAyB;QACzB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;QACvB,aAAa;QACb,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;QAC3B,GAAG,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;QAC5B,aAAa;QACb,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;QACvB,+BAA+B;QAC/B,GAAG,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;QACxB,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;OAGG;IACH,+BAAc,GAAd,UAAe,UAAsB;QACnC;;;;;WAKG;QACH,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,YAAY,EAAE;YACjF,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;IACH,CAAC;IAED,6BAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,6BAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IACD;;OAEG;IACH,8BAAa,GAAb;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,aAAa,EAAE,CAAC;QAC7D,qBAAqB;QACrB,OAAO,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,6BAAY,GAAZ;QACE,OAAO;YACL,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK;YACpB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM;SACtB,CAAC;IACJ,CAAC;IAED,cAAc;IACd,0BAAS,GAAT;QACE,IAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACzC,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,2BAAU,GAAV,UAAW,KAAa,EAAE,MAAc;QACtC,iBAAM,UAAU,YAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAChC,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,EAAE,CAAC,KAAK,GAAG,UAAU,GAAG,KAAK,CAAC;QAC9B,EAAE,CAAC,MAAM,GAAG,UAAU,GAAG,MAAM,CAAC;QAChC,0DAA0D;QAC1D,IAAI,UAAU,GAAG,CAAC,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;SACvC;IACH,CAAC;IACD,SAAS;IACT,sBAAK,GAAL;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,cAAc;QAClC,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IAED,yBAAQ,GAAR,UAAS,CAAS,EAAE,CAAS;QAC3B,IAAI,KAAK,CAAC;QACV,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACxB,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC9B;aAAM;YACL,KAAK,GAAG,iBAAM,QAAQ,YAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SACpC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,oBAAoB;IACpB,kCAAiB,GAAjB;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC7C,IAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACvC,IAAI,MAAM,CAAC;QACX,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC3C,MAAM,GAAG,UAAU,CAAC;SACrB;aAAM;YACL,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,6BAA6B;gBAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACtC,mBAAmB;gBACnB,IAAI,QAAQ,EAAE;oBACZ,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;iBACxC;aACF;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,+BAAc,GAAd,UAAe,OAAiB;QAC9B,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,8BAA8B;QAC9B,uBAAuB;QACvB,IAAI;IACN,CAAC;IACD,YAAY;IACZ,4BAAW,GAAX;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,SAAS,EAAE;YACb,0BAA0B;YAC1B,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAC/B,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;SACjC;IACH,CAAC;IAED,WAAW;IACX,qBAAI,GAAJ;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,SAAS,EAAE;YACrC,OAAO;SACR;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IACD,SAAS;IACT,yBAAQ,GAAR;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAgB,CAAC;QAClD,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACvD,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAChC,kFAAkF;QAClF,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IACD,OAAO;IACP,4BAAW,GAAX;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAgB,CAAC;QAClD,IAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,sBAAsB;QACtB,kDAAkD;QAClD,IAAI,MAAM,EAAE;YACV,SAAS;YACT,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAClG,gBAAgB;YAChB,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAC7F,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACnC,sEAAsE;YACtE,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACrC,QAAQ;YACR,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxC,OAAO,CAAC,OAAO,EAAE,CAAC;SACnB;aAAM,IAAI,eAAe,CAAC,MAAM,EAAE;YACjC,4CAA4C;YAC5C,cAAc;YACd,yBAAyB;YACzB,4BAA4B;YAC5B,6BAA6B;YAC7B,YAAY,CAAC,eAAe,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,eAAe,EAAE,UAAC,OAAO;YAC5B,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;gBAC7B,kEAAkE;gBAClE,+DAA+D;gBAC/D,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;aAClC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,OAAO;IACP,2BAAU,GAAV;QAAA,iBAiBC;QAhBC,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACtD,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,qBAAqB,CAAC;gBAChC,IAAI,KAAI,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;oBAC5B,KAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;qBAAM;oBACL,KAAI,CAAC,QAAQ,EAAE,CAAC;iBACjB;gBACD,KAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC5B,IAAI,iBAAiB,EAAE;oBACrB,iBAAiB,EAAE,CAAC;iBACrB;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;SAClC;IACH,CAAC;IAED,yBAAQ,GAAR,cAAY,CAAC;IAEb,0BAAS,GAAT;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,6EAA6E;QAC7E,SAAS;QACT,2GAA2G;QAC3G,uDAAuD;QACvD,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;QACb,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;QACd,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACH,aAAC;AAAD,CAAC,AA5OD,CAAqB,cAAc,GA4OlC;AAED,eAAe,MAAM,CAAC"}