{"version": 3, "file": "marker.js", "sourceRoot": "", "sources": ["../../src/shape/marker.ts"], "names": [], "mappings": "AAAA;;;GAGG;;AAEH,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,SAAS,MAAM,QAAQ,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,IAAM,OAAO,GAAG;IACd,IAAI;IACJ,MAAM,YAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACZ,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SAC/B,CAAC;IACJ,CAAC;IACD,MAAM;IACN,MAAM,YAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACZ,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACrG,CAAC;IACD,KAAK;IACL,OAAO,YAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACb,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACrF,CAAC;IACD,MAAM;IACN,QAAQ,YAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACd,IAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACxF,CAAC;IACD,OAAO;IACP,eAAe,YAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACrB,IAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACxF,CAAC;CACF,CAAC;AAEF;IAAqB,0BAAS;IAA9B;;IAkEA,CAAC;IAjEC,0BAAS,GAAT,UAAU,KAAK;QACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED,kBAAkB;IAClB,kCAAiB,GAAjB;QACE,mCAAmC;QACnC,+CAA+C;QAC/C,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO;IACtC,CAAC;IAED,qBAAqB;IACrB,6BAAY,GAAZ,UAAa,IAAY,EAAE,KAAU,EAAE,WAAgB;QACrD,iBAAM,YAAY,YAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5D,oBAAoB;YACpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC1B;IACH,CAAC;IAED,iBAAiB;IACjB,+BAA+B;IAC/B,6BAAY,GAAZ;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,sBAAK,GAAL,UAAM,KAAK;QACT,4BAA4B;QAC5B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,yBAAQ,GAAR;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAA,CAAC,GAAQ,KAAK,EAAb,EAAE,CAAC,GAAK,KAAK,EAAV,CAAW;QACvB,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,QAAQ,CAAC;QACxC,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,MAAM,CAAC;QACX,IAAI,IAAI,CAAC;QACT,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;YACtB,MAAM,GAAG,MAAM,CAAC;YAChB,IAAI,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvB,gBAAgB;YAChB,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;SAC5B;aAAM;YACL,kDAAkD;YAClD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEhC,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,CAAC,IAAI,CAAI,MAAM,8BAA2B,CAAC,CAAC;gBACnD,OAAO,IAAI,CAAC;aACb;YAED,IAAI,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACxB;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,2BAAU,GAAV,UAAW,OAAO;QAChB,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC7B,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC5C,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,IAAI,MAAA,EAAE,EAAE,WAAW,CAAC,CAAC;IACjD,CAAC;IAEM,cAAO,GAAG,OAAO,CAAC;IAC3B,aAAC;CAAA,AAlED,CAAqB,SAAS,GAkE7B;AAED,eAAe,MAAM,CAAC"}