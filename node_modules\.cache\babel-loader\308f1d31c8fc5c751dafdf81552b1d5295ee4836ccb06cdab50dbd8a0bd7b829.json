{"ast": null, "code": "import { isArray, isObject } from '../core/util.js';\nimport { createElement, createVNode, XMLNS, XML_NAMESPACE, XLINKNS } from './core.js';\nimport * as api from './domapi.js';\nvar colonChar = 58;\nvar xChar = 120;\nvar emptyNode = createVNode('', '');\nfunction isUndef(s) {\n  return s === undefined;\n}\nfunction isDef(s) {\n  return s !== undefined;\n}\nfunction createKeyToOldIdx(children, beginIdx, endIdx) {\n  var map = {};\n  for (var i = beginIdx; i <= endIdx; ++i) {\n    var key = children[i].key;\n    if (key !== undefined) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (map[key] != null) {\n          console.error(\"Duplicate key \" + key);\n        }\n      }\n      map[key] = i;\n    }\n  }\n  return map;\n}\nfunction sameVnode(vnode1, vnode2) {\n  var isSameKey = vnode1.key === vnode2.key;\n  var isSameTag = vnode1.tag === vnode2.tag;\n  return isSameTag && isSameKey;\n}\nfunction createElm(vnode) {\n  var i;\n  var children = vnode.children;\n  var tag = vnode.tag;\n  if (isDef(tag)) {\n    var elm = vnode.elm = createElement(tag);\n    updateAttrs(emptyNode, vnode);\n    if (isArray(children)) {\n      for (i = 0; i < children.length; ++i) {\n        var ch = children[i];\n        if (ch != null) {\n          api.appendChild(elm, createElm(ch));\n        }\n      }\n    } else if (isDef(vnode.text) && !isObject(vnode.text)) {\n      api.appendChild(elm, api.createTextNode(vnode.text));\n    }\n  } else {\n    vnode.elm = api.createTextNode(vnode.text);\n  }\n  return vnode.elm;\n}\nfunction addVnodes(parentElm, before, vnodes, startIdx, endIdx) {\n  for (; startIdx <= endIdx; ++startIdx) {\n    var ch = vnodes[startIdx];\n    if (ch != null) {\n      api.insertBefore(parentElm, createElm(ch), before);\n    }\n  }\n}\nfunction removeVnodes(parentElm, vnodes, startIdx, endIdx) {\n  for (; startIdx <= endIdx; ++startIdx) {\n    var ch = vnodes[startIdx];\n    if (ch != null) {\n      if (isDef(ch.tag)) {\n        var parent_1 = api.parentNode(ch.elm);\n        api.removeChild(parent_1, ch.elm);\n      } else {\n        api.removeChild(parentElm, ch.elm);\n      }\n    }\n  }\n}\nexport function updateAttrs(oldVnode, vnode) {\n  var key;\n  var elm = vnode.elm;\n  var oldAttrs = oldVnode && oldVnode.attrs || {};\n  var attrs = vnode.attrs || {};\n  if (oldAttrs === attrs) {\n    return;\n  }\n  for (key in attrs) {\n    var cur = attrs[key];\n    var old = oldAttrs[key];\n    if (old !== cur) {\n      if (cur === true) {\n        elm.setAttribute(key, '');\n      } else if (cur === false) {\n        elm.removeAttribute(key);\n      } else {\n        if (key === 'style') {\n          elm.style.cssText = cur;\n        } else if (key.charCodeAt(0) !== xChar) {\n          elm.setAttribute(key, cur);\n        } else if (key === 'xmlns:xlink' || key === 'xmlns') {\n          elm.setAttributeNS(XMLNS, key, cur);\n        } else if (key.charCodeAt(3) === colonChar) {\n          elm.setAttributeNS(XML_NAMESPACE, key, cur);\n        } else if (key.charCodeAt(5) === colonChar) {\n          elm.setAttributeNS(XLINKNS, key, cur);\n        } else {\n          elm.setAttribute(key, cur);\n        }\n      }\n    }\n  }\n  for (key in oldAttrs) {\n    if (!(key in attrs)) {\n      elm.removeAttribute(key);\n    }\n  }\n}\nfunction updateChildren(parentElm, oldCh, newCh) {\n  var oldStartIdx = 0;\n  var newStartIdx = 0;\n  var oldEndIdx = oldCh.length - 1;\n  var oldStartVnode = oldCh[0];\n  var oldEndVnode = oldCh[oldEndIdx];\n  var newEndIdx = newCh.length - 1;\n  var newStartVnode = newCh[0];\n  var newEndVnode = newCh[newEndIdx];\n  var oldKeyToIdx;\n  var idxInOld;\n  var elmToMove;\n  var before;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (sameVnode(oldStartVnode, newStartVnode)) {\n      patchVnode(oldStartVnode, newStartVnode);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (sameVnode(oldEndVnode, newEndVnode)) {\n      patchVnode(oldEndVnode, newEndVnode);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (sameVnode(oldStartVnode, newEndVnode)) {\n      patchVnode(oldStartVnode, newEndVnode);\n      api.insertBefore(parentElm, oldStartVnode.elm, api.nextSibling(oldEndVnode.elm));\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (sameVnode(oldEndVnode, newStartVnode)) {\n      patchVnode(oldEndVnode, newStartVnode);\n      api.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      if (isUndef(oldKeyToIdx)) {\n        oldKeyToIdx = createKeyToOldIdx(oldCh, oldStartIdx, oldEndIdx);\n      }\n      idxInOld = oldKeyToIdx[newStartVnode.key];\n      if (isUndef(idxInOld)) {\n        api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n      } else {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.tag !== newStartVnode.tag) {\n          api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n        } else {\n          patchVnode(elmToMove, newStartVnode);\n          oldCh[idxInOld] = undefined;\n          api.insertBefore(parentElm, elmToMove.elm, oldStartVnode.elm);\n        }\n      }\n      newStartVnode = newCh[++newStartIdx];\n    }\n  }\n  if (oldStartIdx <= oldEndIdx || newStartIdx <= newEndIdx) {\n    if (oldStartIdx > oldEndIdx) {\n      before = newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].elm;\n      addVnodes(parentElm, before, newCh, newStartIdx, newEndIdx);\n    } else {\n      removeVnodes(parentElm, oldCh, oldStartIdx, oldEndIdx);\n    }\n  }\n}\nfunction patchVnode(oldVnode, vnode) {\n  var elm = vnode.elm = oldVnode.elm;\n  var oldCh = oldVnode.children;\n  var ch = vnode.children;\n  if (oldVnode === vnode) {\n    return;\n  }\n  updateAttrs(oldVnode, vnode);\n  if (isUndef(vnode.text)) {\n    if (isDef(oldCh) && isDef(ch)) {\n      if (oldCh !== ch) {\n        updateChildren(elm, oldCh, ch);\n      }\n    } else if (isDef(ch)) {\n      if (isDef(oldVnode.text)) {\n        api.setTextContent(elm, '');\n      }\n      addVnodes(elm, null, ch, 0, ch.length - 1);\n    } else if (isDef(oldCh)) {\n      removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n    } else if (isDef(oldVnode.text)) {\n      api.setTextContent(elm, '');\n    }\n  } else if (oldVnode.text !== vnode.text) {\n    if (isDef(oldCh)) {\n      removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n    }\n    api.setTextContent(elm, vnode.text);\n  }\n}\nexport default function patch(oldVnode, vnode) {\n  if (sameVnode(oldVnode, vnode)) {\n    patchVnode(oldVnode, vnode);\n  } else {\n    var elm = oldVnode.elm;\n    var parent_2 = api.parentNode(elm);\n    createElm(vnode);\n    if (parent_2 !== null) {\n      api.insertBefore(parent_2, vnode.elm, api.nextSibling(elm));\n      removeVnodes(parent_2, [oldVnode], 0, 0);\n    }\n  }\n  return vnode;\n}", "map": {"version": 3, "names": ["isArray", "isObject", "createElement", "createVNode", "XMLNS", "XML_NAMESPACE", "XLINKNS", "api", "colonChar", "xChar", "emptyNode", "isUndef", "s", "undefined", "isDef", "createKeyToOldIdx", "children", "beginIdx", "endIdx", "map", "i", "key", "process", "env", "NODE_ENV", "console", "error", "sameVnode", "vnode1", "vnode2", "isSameKey", "isSameTag", "tag", "createElm", "vnode", "elm", "updateAttrs", "length", "ch", "append<PERSON><PERSON><PERSON>", "text", "createTextNode", "addVnodes", "parentElm", "before", "vnodes", "startIdx", "insertBefore", "removeVnodes", "parent_1", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "oldVnode", "oldAttrs", "attrs", "cur", "old", "setAttribute", "removeAttribute", "style", "cssText", "charCodeAt", "setAttributeNS", "update<PERSON><PERSON><PERSON>n", "oldCh", "newCh", "oldStartIdx", "newStartIdx", "oldEndIdx", "oldStartVnode", "oldEndVnode", "newEndIdx", "newStartVnode", "newEndVnode", "oldKeyToIdx", "idxInOld", "elmToMove", "patchVnode", "nextS<PERSON>ling", "setTextContent", "patch", "parent_2"], "sources": ["E:/新项目/adminweb/node_modules/zrender/lib/svg/patch.js"], "sourcesContent": ["import { isArray, isObject } from '../core/util.js';\nimport { createElement, createVNode, XMLNS, XML_NAMESPACE, XLINKNS } from './core.js';\nimport * as api from './domapi.js';\nvar colonChar = 58;\nvar xChar = 120;\nvar emptyNode = createVNode('', '');\nfunction isUndef(s) {\n    return s === undefined;\n}\nfunction isDef(s) {\n    return s !== undefined;\n}\nfunction createKeyToOldIdx(children, beginIdx, endIdx) {\n    var map = {};\n    for (var i = beginIdx; i <= endIdx; ++i) {\n        var key = children[i].key;\n        if (key !== undefined) {\n            if (process.env.NODE_ENV !== 'production') {\n                if (map[key] != null) {\n                    console.error(\"Duplicate key \" + key);\n                }\n            }\n            map[key] = i;\n        }\n    }\n    return map;\n}\nfunction sameVnode(vnode1, vnode2) {\n    var isSameKey = vnode1.key === vnode2.key;\n    var isSameTag = vnode1.tag === vnode2.tag;\n    return isSameTag && isSameKey;\n}\nfunction createElm(vnode) {\n    var i;\n    var children = vnode.children;\n    var tag = vnode.tag;\n    if (isDef(tag)) {\n        var elm = (vnode.elm = createElement(tag));\n        updateAttrs(emptyNode, vnode);\n        if (isArray(children)) {\n            for (i = 0; i < children.length; ++i) {\n                var ch = children[i];\n                if (ch != null) {\n                    api.appendChild(elm, createElm(ch));\n                }\n            }\n        }\n        else if (isDef(vnode.text) && !isObject(vnode.text)) {\n            api.appendChild(elm, api.createTextNode(vnode.text));\n        }\n    }\n    else {\n        vnode.elm = api.createTextNode(vnode.text);\n    }\n    return vnode.elm;\n}\nfunction addVnodes(parentElm, before, vnodes, startIdx, endIdx) {\n    for (; startIdx <= endIdx; ++startIdx) {\n        var ch = vnodes[startIdx];\n        if (ch != null) {\n            api.insertBefore(parentElm, createElm(ch), before);\n        }\n    }\n}\nfunction removeVnodes(parentElm, vnodes, startIdx, endIdx) {\n    for (; startIdx <= endIdx; ++startIdx) {\n        var ch = vnodes[startIdx];\n        if (ch != null) {\n            if (isDef(ch.tag)) {\n                var parent_1 = api.parentNode(ch.elm);\n                api.removeChild(parent_1, ch.elm);\n            }\n            else {\n                api.removeChild(parentElm, ch.elm);\n            }\n        }\n    }\n}\nexport function updateAttrs(oldVnode, vnode) {\n    var key;\n    var elm = vnode.elm;\n    var oldAttrs = oldVnode && oldVnode.attrs || {};\n    var attrs = vnode.attrs || {};\n    if (oldAttrs === attrs) {\n        return;\n    }\n    for (key in attrs) {\n        var cur = attrs[key];\n        var old = oldAttrs[key];\n        if (old !== cur) {\n            if (cur === true) {\n                elm.setAttribute(key, '');\n            }\n            else if (cur === false) {\n                elm.removeAttribute(key);\n            }\n            else {\n                if (key === 'style') {\n                    elm.style.cssText = cur;\n                }\n                else if (key.charCodeAt(0) !== xChar) {\n                    elm.setAttribute(key, cur);\n                }\n                else if (key === 'xmlns:xlink' || key === 'xmlns') {\n                    elm.setAttributeNS(XMLNS, key, cur);\n                }\n                else if (key.charCodeAt(3) === colonChar) {\n                    elm.setAttributeNS(XML_NAMESPACE, key, cur);\n                }\n                else if (key.charCodeAt(5) === colonChar) {\n                    elm.setAttributeNS(XLINKNS, key, cur);\n                }\n                else {\n                    elm.setAttribute(key, cur);\n                }\n            }\n        }\n    }\n    for (key in oldAttrs) {\n        if (!(key in attrs)) {\n            elm.removeAttribute(key);\n        }\n    }\n}\nfunction updateChildren(parentElm, oldCh, newCh) {\n    var oldStartIdx = 0;\n    var newStartIdx = 0;\n    var oldEndIdx = oldCh.length - 1;\n    var oldStartVnode = oldCh[0];\n    var oldEndVnode = oldCh[oldEndIdx];\n    var newEndIdx = newCh.length - 1;\n    var newStartVnode = newCh[0];\n    var newEndVnode = newCh[newEndIdx];\n    var oldKeyToIdx;\n    var idxInOld;\n    var elmToMove;\n    var before;\n    while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n        if (oldStartVnode == null) {\n            oldStartVnode = oldCh[++oldStartIdx];\n        }\n        else if (oldEndVnode == null) {\n            oldEndVnode = oldCh[--oldEndIdx];\n        }\n        else if (newStartVnode == null) {\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (newEndVnode == null) {\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldStartVnode, newStartVnode)) {\n            patchVnode(oldStartVnode, newStartVnode);\n            oldStartVnode = oldCh[++oldStartIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (sameVnode(oldEndVnode, newEndVnode)) {\n            patchVnode(oldEndVnode, newEndVnode);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldStartVnode, newEndVnode)) {\n            patchVnode(oldStartVnode, newEndVnode);\n            api.insertBefore(parentElm, oldStartVnode.elm, api.nextSibling(oldEndVnode.elm));\n            oldStartVnode = oldCh[++oldStartIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (sameVnode(oldEndVnode, newStartVnode)) {\n            patchVnode(oldEndVnode, newStartVnode);\n            api.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else {\n            if (isUndef(oldKeyToIdx)) {\n                oldKeyToIdx = createKeyToOldIdx(oldCh, oldStartIdx, oldEndIdx);\n            }\n            idxInOld = oldKeyToIdx[newStartVnode.key];\n            if (isUndef(idxInOld)) {\n                api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n            }\n            else {\n                elmToMove = oldCh[idxInOld];\n                if (elmToMove.tag !== newStartVnode.tag) {\n                    api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n                }\n                else {\n                    patchVnode(elmToMove, newStartVnode);\n                    oldCh[idxInOld] = undefined;\n                    api.insertBefore(parentElm, elmToMove.elm, oldStartVnode.elm);\n                }\n            }\n            newStartVnode = newCh[++newStartIdx];\n        }\n    }\n    if (oldStartIdx <= oldEndIdx || newStartIdx <= newEndIdx) {\n        if (oldStartIdx > oldEndIdx) {\n            before = newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].elm;\n            addVnodes(parentElm, before, newCh, newStartIdx, newEndIdx);\n        }\n        else {\n            removeVnodes(parentElm, oldCh, oldStartIdx, oldEndIdx);\n        }\n    }\n}\nfunction patchVnode(oldVnode, vnode) {\n    var elm = (vnode.elm = oldVnode.elm);\n    var oldCh = oldVnode.children;\n    var ch = vnode.children;\n    if (oldVnode === vnode) {\n        return;\n    }\n    updateAttrs(oldVnode, vnode);\n    if (isUndef(vnode.text)) {\n        if (isDef(oldCh) && isDef(ch)) {\n            if (oldCh !== ch) {\n                updateChildren(elm, oldCh, ch);\n            }\n        }\n        else if (isDef(ch)) {\n            if (isDef(oldVnode.text)) {\n                api.setTextContent(elm, '');\n            }\n            addVnodes(elm, null, ch, 0, ch.length - 1);\n        }\n        else if (isDef(oldCh)) {\n            removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n        }\n        else if (isDef(oldVnode.text)) {\n            api.setTextContent(elm, '');\n        }\n    }\n    else if (oldVnode.text !== vnode.text) {\n        if (isDef(oldCh)) {\n            removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n        }\n        api.setTextContent(elm, vnode.text);\n    }\n}\nexport default function patch(oldVnode, vnode) {\n    if (sameVnode(oldVnode, vnode)) {\n        patchVnode(oldVnode, vnode);\n    }\n    else {\n        var elm = oldVnode.elm;\n        var parent_2 = api.parentNode(elm);\n        createElm(vnode);\n        if (parent_2 !== null) {\n            api.insertBefore(parent_2, vnode.elm, api.nextSibling(elm));\n            removeVnodes(parent_2, [oldVnode], 0, 0);\n        }\n    }\n    return vnode;\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,iBAAiB;AACnD,SAASC,aAAa,EAAEC,WAAW,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,QAAQ,WAAW;AACrF,OAAO,KAAKC,GAAG,MAAM,aAAa;AAClC,IAAIC,SAAS,GAAG,EAAE;AAClB,IAAIC,KAAK,GAAG,GAAG;AACf,IAAIC,SAAS,GAAGP,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;AACnC,SAASQ,OAAOA,CAACC,CAAC,EAAE;EAChB,OAAOA,CAAC,KAAKC,SAAS;AAC1B;AACA,SAASC,KAAKA,CAACF,CAAC,EAAE;EACd,OAAOA,CAAC,KAAKC,SAAS;AAC1B;AACA,SAASE,iBAAiBA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACnD,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,KAAK,IAAIC,CAAC,GAAGH,QAAQ,EAAEG,CAAC,IAAIF,MAAM,EAAE,EAAEE,CAAC,EAAE;IACrC,IAAIC,GAAG,GAAGL,QAAQ,CAACI,CAAC,CAAC,CAACC,GAAG;IACzB,IAAIA,GAAG,KAAKR,SAAS,EAAE;MACnB,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC,IAAIL,GAAG,CAACE,GAAG,CAAC,IAAI,IAAI,EAAE;UAClBI,OAAO,CAACC,KAAK,CAAC,gBAAgB,GAAGL,GAAG,CAAC;QACzC;MACJ;MACAF,GAAG,CAACE,GAAG,CAAC,GAAGD,CAAC;IAChB;EACJ;EACA,OAAOD,GAAG;AACd;AACA,SAASQ,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B,IAAIC,SAAS,GAAGF,MAAM,CAACP,GAAG,KAAKQ,MAAM,CAACR,GAAG;EACzC,IAAIU,SAAS,GAAGH,MAAM,CAACI,GAAG,KAAKH,MAAM,CAACG,GAAG;EACzC,OAAOD,SAAS,IAAID,SAAS;AACjC;AACA,SAASG,SAASA,CAACC,KAAK,EAAE;EACtB,IAAId,CAAC;EACL,IAAIJ,QAAQ,GAAGkB,KAAK,CAAClB,QAAQ;EAC7B,IAAIgB,GAAG,GAAGE,KAAK,CAACF,GAAG;EACnB,IAAIlB,KAAK,CAACkB,GAAG,CAAC,EAAE;IACZ,IAAIG,GAAG,GAAID,KAAK,CAACC,GAAG,GAAGjC,aAAa,CAAC8B,GAAG,CAAE;IAC1CI,WAAW,CAAC1B,SAAS,EAAEwB,KAAK,CAAC;IAC7B,IAAIlC,OAAO,CAACgB,QAAQ,CAAC,EAAE;MACnB,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACqB,MAAM,EAAE,EAAEjB,CAAC,EAAE;QAClC,IAAIkB,EAAE,GAAGtB,QAAQ,CAACI,CAAC,CAAC;QACpB,IAAIkB,EAAE,IAAI,IAAI,EAAE;UACZ/B,GAAG,CAACgC,WAAW,CAACJ,GAAG,EAAEF,SAAS,CAACK,EAAE,CAAC,CAAC;QACvC;MACJ;IACJ,CAAC,MACI,IAAIxB,KAAK,CAACoB,KAAK,CAACM,IAAI,CAAC,IAAI,CAACvC,QAAQ,CAACiC,KAAK,CAACM,IAAI,CAAC,EAAE;MACjDjC,GAAG,CAACgC,WAAW,CAACJ,GAAG,EAAE5B,GAAG,CAACkC,cAAc,CAACP,KAAK,CAACM,IAAI,CAAC,CAAC;IACxD;EACJ,CAAC,MACI;IACDN,KAAK,CAACC,GAAG,GAAG5B,GAAG,CAACkC,cAAc,CAACP,KAAK,CAACM,IAAI,CAAC;EAC9C;EACA,OAAON,KAAK,CAACC,GAAG;AACpB;AACA,SAASO,SAASA,CAACC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE5B,MAAM,EAAE;EAC5D,OAAO4B,QAAQ,IAAI5B,MAAM,EAAE,EAAE4B,QAAQ,EAAE;IACnC,IAAIR,EAAE,GAAGO,MAAM,CAACC,QAAQ,CAAC;IACzB,IAAIR,EAAE,IAAI,IAAI,EAAE;MACZ/B,GAAG,CAACwC,YAAY,CAACJ,SAAS,EAAEV,SAAS,CAACK,EAAE,CAAC,EAAEM,MAAM,CAAC;IACtD;EACJ;AACJ;AACA,SAASI,YAAYA,CAACL,SAAS,EAAEE,MAAM,EAAEC,QAAQ,EAAE5B,MAAM,EAAE;EACvD,OAAO4B,QAAQ,IAAI5B,MAAM,EAAE,EAAE4B,QAAQ,EAAE;IACnC,IAAIR,EAAE,GAAGO,MAAM,CAACC,QAAQ,CAAC;IACzB,IAAIR,EAAE,IAAI,IAAI,EAAE;MACZ,IAAIxB,KAAK,CAACwB,EAAE,CAACN,GAAG,CAAC,EAAE;QACf,IAAIiB,QAAQ,GAAG1C,GAAG,CAAC2C,UAAU,CAACZ,EAAE,CAACH,GAAG,CAAC;QACrC5B,GAAG,CAAC4C,WAAW,CAACF,QAAQ,EAAEX,EAAE,CAACH,GAAG,CAAC;MACrC,CAAC,MACI;QACD5B,GAAG,CAAC4C,WAAW,CAACR,SAAS,EAAEL,EAAE,CAACH,GAAG,CAAC;MACtC;IACJ;EACJ;AACJ;AACA,OAAO,SAASC,WAAWA,CAACgB,QAAQ,EAAElB,KAAK,EAAE;EACzC,IAAIb,GAAG;EACP,IAAIc,GAAG,GAAGD,KAAK,CAACC,GAAG;EACnB,IAAIkB,QAAQ,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,KAAK,IAAI,CAAC,CAAC;EAC/C,IAAIA,KAAK,GAAGpB,KAAK,CAACoB,KAAK,IAAI,CAAC,CAAC;EAC7B,IAAID,QAAQ,KAAKC,KAAK,EAAE;IACpB;EACJ;EACA,KAAKjC,GAAG,IAAIiC,KAAK,EAAE;IACf,IAAIC,GAAG,GAAGD,KAAK,CAACjC,GAAG,CAAC;IACpB,IAAImC,GAAG,GAAGH,QAAQ,CAAChC,GAAG,CAAC;IACvB,IAAImC,GAAG,KAAKD,GAAG,EAAE;MACb,IAAIA,GAAG,KAAK,IAAI,EAAE;QACdpB,GAAG,CAACsB,YAAY,CAACpC,GAAG,EAAE,EAAE,CAAC;MAC7B,CAAC,MACI,IAAIkC,GAAG,KAAK,KAAK,EAAE;QACpBpB,GAAG,CAACuB,eAAe,CAACrC,GAAG,CAAC;MAC5B,CAAC,MACI;QACD,IAAIA,GAAG,KAAK,OAAO,EAAE;UACjBc,GAAG,CAACwB,KAAK,CAACC,OAAO,GAAGL,GAAG;QAC3B,CAAC,MACI,IAAIlC,GAAG,CAACwC,UAAU,CAAC,CAAC,CAAC,KAAKpD,KAAK,EAAE;UAClC0B,GAAG,CAACsB,YAAY,CAACpC,GAAG,EAAEkC,GAAG,CAAC;QAC9B,CAAC,MACI,IAAIlC,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,OAAO,EAAE;UAC/Cc,GAAG,CAAC2B,cAAc,CAAC1D,KAAK,EAAEiB,GAAG,EAAEkC,GAAG,CAAC;QACvC,CAAC,MACI,IAAIlC,GAAG,CAACwC,UAAU,CAAC,CAAC,CAAC,KAAKrD,SAAS,EAAE;UACtC2B,GAAG,CAAC2B,cAAc,CAACzD,aAAa,EAAEgB,GAAG,EAAEkC,GAAG,CAAC;QAC/C,CAAC,MACI,IAAIlC,GAAG,CAACwC,UAAU,CAAC,CAAC,CAAC,KAAKrD,SAAS,EAAE;UACtC2B,GAAG,CAAC2B,cAAc,CAACxD,OAAO,EAAEe,GAAG,EAAEkC,GAAG,CAAC;QACzC,CAAC,MACI;UACDpB,GAAG,CAACsB,YAAY,CAACpC,GAAG,EAAEkC,GAAG,CAAC;QAC9B;MACJ;IACJ;EACJ;EACA,KAAKlC,GAAG,IAAIgC,QAAQ,EAAE;IAClB,IAAI,EAAEhC,GAAG,IAAIiC,KAAK,CAAC,EAAE;MACjBnB,GAAG,CAACuB,eAAe,CAACrC,GAAG,CAAC;IAC5B;EACJ;AACJ;AACA,SAAS0C,cAAcA,CAACpB,SAAS,EAAEqB,KAAK,EAAEC,KAAK,EAAE;EAC7C,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,SAAS,GAAGJ,KAAK,CAAC3B,MAAM,GAAG,CAAC;EAChC,IAAIgC,aAAa,GAAGL,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIM,WAAW,GAAGN,KAAK,CAACI,SAAS,CAAC;EAClC,IAAIG,SAAS,GAAGN,KAAK,CAAC5B,MAAM,GAAG,CAAC;EAChC,IAAImC,aAAa,GAAGP,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIQ,WAAW,GAAGR,KAAK,CAACM,SAAS,CAAC;EAClC,IAAIG,WAAW;EACf,IAAIC,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIhC,MAAM;EACV,OAAOsB,WAAW,IAAIE,SAAS,IAAID,WAAW,IAAII,SAAS,EAAE;IACzD,IAAIF,aAAa,IAAI,IAAI,EAAE;MACvBA,aAAa,GAAGL,KAAK,CAAC,EAAEE,WAAW,CAAC;IACxC,CAAC,MACI,IAAII,WAAW,IAAI,IAAI,EAAE;MAC1BA,WAAW,GAAGN,KAAK,CAAC,EAAEI,SAAS,CAAC;IACpC,CAAC,MACI,IAAII,aAAa,IAAI,IAAI,EAAE;MAC5BA,aAAa,GAAGP,KAAK,CAAC,EAAEE,WAAW,CAAC;IACxC,CAAC,MACI,IAAIM,WAAW,IAAI,IAAI,EAAE;MAC1BA,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;IACpC,CAAC,MACI,IAAI5C,SAAS,CAAC0C,aAAa,EAAEG,aAAa,CAAC,EAAE;MAC9CK,UAAU,CAACR,aAAa,EAAEG,aAAa,CAAC;MACxCH,aAAa,GAAGL,KAAK,CAAC,EAAEE,WAAW,CAAC;MACpCM,aAAa,GAAGP,KAAK,CAAC,EAAEE,WAAW,CAAC;IACxC,CAAC,MACI,IAAIxC,SAAS,CAAC2C,WAAW,EAAEG,WAAW,CAAC,EAAE;MAC1CI,UAAU,CAACP,WAAW,EAAEG,WAAW,CAAC;MACpCH,WAAW,GAAGN,KAAK,CAAC,EAAEI,SAAS,CAAC;MAChCK,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;IACpC,CAAC,MACI,IAAI5C,SAAS,CAAC0C,aAAa,EAAEI,WAAW,CAAC,EAAE;MAC5CI,UAAU,CAACR,aAAa,EAAEI,WAAW,CAAC;MACtClE,GAAG,CAACwC,YAAY,CAACJ,SAAS,EAAE0B,aAAa,CAAClC,GAAG,EAAE5B,GAAG,CAACuE,WAAW,CAACR,WAAW,CAACnC,GAAG,CAAC,CAAC;MAChFkC,aAAa,GAAGL,KAAK,CAAC,EAAEE,WAAW,CAAC;MACpCO,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;IACpC,CAAC,MACI,IAAI5C,SAAS,CAAC2C,WAAW,EAAEE,aAAa,CAAC,EAAE;MAC5CK,UAAU,CAACP,WAAW,EAAEE,aAAa,CAAC;MACtCjE,GAAG,CAACwC,YAAY,CAACJ,SAAS,EAAE2B,WAAW,CAACnC,GAAG,EAAEkC,aAAa,CAAClC,GAAG,CAAC;MAC/DmC,WAAW,GAAGN,KAAK,CAAC,EAAEI,SAAS,CAAC;MAChCI,aAAa,GAAGP,KAAK,CAAC,EAAEE,WAAW,CAAC;IACxC,CAAC,MACI;MACD,IAAIxD,OAAO,CAAC+D,WAAW,CAAC,EAAE;QACtBA,WAAW,GAAG3D,iBAAiB,CAACiD,KAAK,EAAEE,WAAW,EAAEE,SAAS,CAAC;MAClE;MACAO,QAAQ,GAAGD,WAAW,CAACF,aAAa,CAACnD,GAAG,CAAC;MACzC,IAAIV,OAAO,CAACgE,QAAQ,CAAC,EAAE;QACnBpE,GAAG,CAACwC,YAAY,CAACJ,SAAS,EAAEV,SAAS,CAACuC,aAAa,CAAC,EAAEH,aAAa,CAAClC,GAAG,CAAC;MAC5E,CAAC,MACI;QACDyC,SAAS,GAAGZ,KAAK,CAACW,QAAQ,CAAC;QAC3B,IAAIC,SAAS,CAAC5C,GAAG,KAAKwC,aAAa,CAACxC,GAAG,EAAE;UACrCzB,GAAG,CAACwC,YAAY,CAACJ,SAAS,EAAEV,SAAS,CAACuC,aAAa,CAAC,EAAEH,aAAa,CAAClC,GAAG,CAAC;QAC5E,CAAC,MACI;UACD0C,UAAU,CAACD,SAAS,EAAEJ,aAAa,CAAC;UACpCR,KAAK,CAACW,QAAQ,CAAC,GAAG9D,SAAS;UAC3BN,GAAG,CAACwC,YAAY,CAACJ,SAAS,EAAEiC,SAAS,CAACzC,GAAG,EAAEkC,aAAa,CAAClC,GAAG,CAAC;QACjE;MACJ;MACAqC,aAAa,GAAGP,KAAK,CAAC,EAAEE,WAAW,CAAC;IACxC;EACJ;EACA,IAAID,WAAW,IAAIE,SAAS,IAAID,WAAW,IAAII,SAAS,EAAE;IACtD,IAAIL,WAAW,GAAGE,SAAS,EAAE;MACzBxB,MAAM,GAAGqB,KAAK,CAACM,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGN,KAAK,CAACM,SAAS,GAAG,CAAC,CAAC,CAACpC,GAAG;MACvEO,SAAS,CAACC,SAAS,EAAEC,MAAM,EAAEqB,KAAK,EAAEE,WAAW,EAAEI,SAAS,CAAC;IAC/D,CAAC,MACI;MACDvB,YAAY,CAACL,SAAS,EAAEqB,KAAK,EAAEE,WAAW,EAAEE,SAAS,CAAC;IAC1D;EACJ;AACJ;AACA,SAASS,UAAUA,CAACzB,QAAQ,EAAElB,KAAK,EAAE;EACjC,IAAIC,GAAG,GAAID,KAAK,CAACC,GAAG,GAAGiB,QAAQ,CAACjB,GAAI;EACpC,IAAI6B,KAAK,GAAGZ,QAAQ,CAACpC,QAAQ;EAC7B,IAAIsB,EAAE,GAAGJ,KAAK,CAAClB,QAAQ;EACvB,IAAIoC,QAAQ,KAAKlB,KAAK,EAAE;IACpB;EACJ;EACAE,WAAW,CAACgB,QAAQ,EAAElB,KAAK,CAAC;EAC5B,IAAIvB,OAAO,CAACuB,KAAK,CAACM,IAAI,CAAC,EAAE;IACrB,IAAI1B,KAAK,CAACkD,KAAK,CAAC,IAAIlD,KAAK,CAACwB,EAAE,CAAC,EAAE;MAC3B,IAAI0B,KAAK,KAAK1B,EAAE,EAAE;QACdyB,cAAc,CAAC5B,GAAG,EAAE6B,KAAK,EAAE1B,EAAE,CAAC;MAClC;IACJ,CAAC,MACI,IAAIxB,KAAK,CAACwB,EAAE,CAAC,EAAE;MAChB,IAAIxB,KAAK,CAACsC,QAAQ,CAACZ,IAAI,CAAC,EAAE;QACtBjC,GAAG,CAACwE,cAAc,CAAC5C,GAAG,EAAE,EAAE,CAAC;MAC/B;MACAO,SAAS,CAACP,GAAG,EAAE,IAAI,EAAEG,EAAE,EAAE,CAAC,EAAEA,EAAE,CAACD,MAAM,GAAG,CAAC,CAAC;IAC9C,CAAC,MACI,IAAIvB,KAAK,CAACkD,KAAK,CAAC,EAAE;MACnBhB,YAAY,CAACb,GAAG,EAAE6B,KAAK,EAAE,CAAC,EAAEA,KAAK,CAAC3B,MAAM,GAAG,CAAC,CAAC;IACjD,CAAC,MACI,IAAIvB,KAAK,CAACsC,QAAQ,CAACZ,IAAI,CAAC,EAAE;MAC3BjC,GAAG,CAACwE,cAAc,CAAC5C,GAAG,EAAE,EAAE,CAAC;IAC/B;EACJ,CAAC,MACI,IAAIiB,QAAQ,CAACZ,IAAI,KAAKN,KAAK,CAACM,IAAI,EAAE;IACnC,IAAI1B,KAAK,CAACkD,KAAK,CAAC,EAAE;MACdhB,YAAY,CAACb,GAAG,EAAE6B,KAAK,EAAE,CAAC,EAAEA,KAAK,CAAC3B,MAAM,GAAG,CAAC,CAAC;IACjD;IACA9B,GAAG,CAACwE,cAAc,CAAC5C,GAAG,EAAED,KAAK,CAACM,IAAI,CAAC;EACvC;AACJ;AACA,eAAe,SAASwC,KAAKA,CAAC5B,QAAQ,EAAElB,KAAK,EAAE;EAC3C,IAAIP,SAAS,CAACyB,QAAQ,EAAElB,KAAK,CAAC,EAAE;IAC5B2C,UAAU,CAACzB,QAAQ,EAAElB,KAAK,CAAC;EAC/B,CAAC,MACI;IACD,IAAIC,GAAG,GAAGiB,QAAQ,CAACjB,GAAG;IACtB,IAAI8C,QAAQ,GAAG1E,GAAG,CAAC2C,UAAU,CAACf,GAAG,CAAC;IAClCF,SAAS,CAACC,KAAK,CAAC;IAChB,IAAI+C,QAAQ,KAAK,IAAI,EAAE;MACnB1E,GAAG,CAACwC,YAAY,CAACkC,QAAQ,EAAE/C,KAAK,CAACC,GAAG,EAAE5B,GAAG,CAACuE,WAAW,CAAC3C,GAAG,CAAC,CAAC;MAC3Da,YAAY,CAACiC,QAAQ,EAAE,CAAC7B,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C;EACJ;EACA,OAAOlB,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}