{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.index-of.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.string.sub.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrender from 'zrender/lib/zrender.js';\nimport { assert, each, isFunction, isObject, indexOf, bind, clone, setAsPrimitive, extend, createHashMap, map, defaults, isDom, isArray, noop, isString, retrieve2 } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport timsort from 'zrender/lib/core/timsort.js';\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport CoordinateSystemManager from './CoordinateSystem.js';\nimport OptionManager from '../model/OptionManager.js';\nimport backwardCompat from '../preprocessor/backwardCompat.js';\nimport dataStack from '../processor/dataStack.js';\nimport SeriesModel from '../model/Series.js';\nimport ComponentView from '../view/Component.js';\nimport ChartView from '../view/Chart.js';\nimport * as graphic from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { isHighDownDispatcher, HOVER_STATE_EMPHASIS, HOVER_STATE_BLUR, blurSeriesFromHighlightPayload, toggleSelectionFromPayload, updateSeriesElementSelection, getAllSelectedIndices, isSelectChangePayload, isHighDownPayload, HIGHLIGHT_ACTION_TYPE, DOWNPLAY_ACTION_TYPE, SELECT_ACTION_TYPE, UNSELECT_ACTION_TYPE, TOGGLE_SELECT_ACTION_TYPE, savePathStates, enterEmphasis, leaveEmphasis, leaveBlur, enterSelect, leaveSelect, enterBlur, allLeaveBlur, findComponentHighDownDispatchers, blurComponent, handleGlobalMouseOverForHighDown, handleGlobalMouseOutForHighDown } from '../util/states.js';\nimport * as modelUtil from '../util/model.js';\nimport { throttle } from '../util/throttle.js';\nimport { seriesStyleTask, dataStyleTask, dataColorPaletteTask } from '../visual/style.js';\nimport loadingDefault from '../loading/default.js';\nimport Scheduler from './Scheduler.js';\nimport lightTheme from '../theme/light.js';\nimport darkTheme from '../theme/dark.js';\nimport { parseClassType } from '../util/clazz.js';\nimport { ECEventProcessor } from '../util/ECEventProcessor.js';\nimport { seriesSymbolTask, dataSymbolTask } from '../visual/symbol.js';\nimport { getVisualFromData, getItemVisualFromData } from '../visual/helper.js';\nimport { deprecateLog, deprecateReplaceLog, error, warn } from '../util/log.js';\nimport { handleLegacySelectEvents } from '../legacy/dataSelectAction.js';\nimport { registerExternalTransform } from '../data/helper/transform.js';\nimport { createLocaleObject, SYSTEM_LANG } from './locale.js';\nimport { findEventDispatcher } from '../util/event.js';\nimport decal from '../visual/decal.js';\nimport lifecycle from './lifecycle.js';\nimport { platformApi, setPlatformAPI } from 'zrender/lib/core/platform.js';\nimport { getImpl } from './impl.js';\nexport var version = '5.5.1';\nexport var dependencies = {\n  zrender: '5.6.0'\n};\nvar TEST_FRAME_REMAIN_TIME = 1;\nvar PRIORITY_PROCESSOR_SERIES_FILTER = 800;\n// Some data processors depends on the stack result dimension (to calculate data extent).\n// So data stack stage should be in front of data processing stage.\nvar PRIORITY_PROCESSOR_DATASTACK = 900;\n// \"Data filter\" will block the stream, so it should be\n// put at the beginning of data processing.\nvar PRIORITY_PROCESSOR_FILTER = 1000;\nvar PRIORITY_PROCESSOR_DEFAULT = 2000;\nvar PRIORITY_PROCESSOR_STATISTIC = 5000;\nvar PRIORITY_VISUAL_LAYOUT = 1000;\nvar PRIORITY_VISUAL_PROGRESSIVE_LAYOUT = 1100;\nvar PRIORITY_VISUAL_GLOBAL = 2000;\nvar PRIORITY_VISUAL_CHART = 3000;\nvar PRIORITY_VISUAL_COMPONENT = 4000;\n// Visual property in data. Greater than `PRIORITY_VISUAL_COMPONENT` to enable to\n// overwrite the viusal result of component (like `visualMap`)\n// using data item specific setting (like itemStyle.xxx on data item)\nvar PRIORITY_VISUAL_CHART_DATA_CUSTOM = 4500;\n// Greater than `PRIORITY_VISUAL_CHART_DATA_CUSTOM` to enable to layout based on\n// visual result like `symbolSize`.\nvar PRIORITY_VISUAL_POST_CHART_LAYOUT = 4600;\nvar PRIORITY_VISUAL_BRUSH = 5000;\nvar PRIORITY_VISUAL_ARIA = 6000;\nvar PRIORITY_VISUAL_DECAL = 7000;\nexport var PRIORITY = {\n  PROCESSOR: {\n    FILTER: PRIORITY_PROCESSOR_FILTER,\n    SERIES_FILTER: PRIORITY_PROCESSOR_SERIES_FILTER,\n    STATISTIC: PRIORITY_PROCESSOR_STATISTIC\n  },\n  VISUAL: {\n    LAYOUT: PRIORITY_VISUAL_LAYOUT,\n    PROGRESSIVE_LAYOUT: PRIORITY_VISUAL_PROGRESSIVE_LAYOUT,\n    GLOBAL: PRIORITY_VISUAL_GLOBAL,\n    CHART: PRIORITY_VISUAL_CHART,\n    POST_CHART_LAYOUT: PRIORITY_VISUAL_POST_CHART_LAYOUT,\n    COMPONENT: PRIORITY_VISUAL_COMPONENT,\n    BRUSH: PRIORITY_VISUAL_BRUSH,\n    CHART_ITEM: PRIORITY_VISUAL_CHART_DATA_CUSTOM,\n    ARIA: PRIORITY_VISUAL_ARIA,\n    DECAL: PRIORITY_VISUAL_DECAL\n  }\n};\n// Main process have three entries: `setOption`, `dispatchAction` and `resize`,\n// where they must not be invoked nestedly, except the only case: invoke\n// dispatchAction with updateMethod \"none\" in main process.\n// This flag is used to carry out this rule.\n// All events will be triggered out side main process (i.e. when !this[IN_MAIN_PROCESS]).\nvar IN_MAIN_PROCESS_KEY = '__flagInMainProcess';\nvar PENDING_UPDATE = '__pendingUpdate';\nvar STATUS_NEEDS_UPDATE_KEY = '__needsUpdateStatus';\nvar ACTION_REG = /^[a-zA-Z0-9_]+$/;\nvar CONNECT_STATUS_KEY = '__connectUpdateStatus';\nvar CONNECT_STATUS_PENDING = 0;\nvar CONNECT_STATUS_UPDATING = 1;\nvar CONNECT_STATUS_UPDATED = 2;\n;\n;\nfunction createRegisterEventWithLowercaseECharts(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (this.isDisposed()) {\n      disposedWarning(this.id);\n      return;\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction createRegisterEventWithLowercaseMessageCenter(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction toLowercaseNameAndCallEventful(host, method, args) {\n  // `args[0]` is event name. Event name is all lowercase.\n  args[0] = args[0] && args[0].toLowerCase();\n  return Eventful.prototype[method].apply(host, args);\n}\nvar MessageCenter = /** @class */function (_super) {\n  __extends(MessageCenter, _super);\n  function MessageCenter() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return MessageCenter;\n}(Eventful);\nvar messageCenterProto = MessageCenter.prototype;\nmessageCenterProto.on = createRegisterEventWithLowercaseMessageCenter('on');\nmessageCenterProto.off = createRegisterEventWithLowercaseMessageCenter('off');\n// ---------------------------------------\n// Internal method names for class ECharts\n// ---------------------------------------\nvar prepare;\nvar prepareView;\nvar updateDirectly;\nvar updateMethods;\nvar doConvertPixel;\nvar updateStreamModes;\nvar doDispatchAction;\nvar flushPendingActions;\nvar triggerUpdatedEvent;\nvar bindRenderedEvent;\nvar bindMouseEvent;\nvar render;\nvar renderComponents;\nvar renderSeries;\nvar createExtensionAPI;\nvar enableConnect;\nvar markStatusToUpdate;\nvar applyChangedStates;\nvar ECharts = /** @class */function (_super) {\n  __extends(ECharts, _super);\n  function ECharts(dom,\n  // Theme name or themeOption.\n  theme, opts) {\n    var _this = _super.call(this, new ECEventProcessor()) || this;\n    _this._chartsViews = [];\n    _this._chartsMap = {};\n    _this._componentsViews = [];\n    _this._componentsMap = {};\n    // Can't dispatch action during rendering procedure\n    _this._pendingActions = [];\n    opts = opts || {};\n    // Get theme by name\n    if (isString(theme)) {\n      theme = themeStorage[theme];\n    }\n    _this._dom = dom;\n    var defaultRenderer = 'canvas';\n    var defaultCoarsePointer = 'auto';\n    var defaultUseDirtyRect = false;\n    if (process.env.NODE_ENV !== 'production') {\n      var root = /* eslint-disable-next-line */\n      env.hasGlobalWindow ? window : global;\n      if (root) {\n        defaultRenderer = retrieve2(root.__ECHARTS__DEFAULT__RENDERER__, defaultRenderer);\n        defaultCoarsePointer = retrieve2(root.__ECHARTS__DEFAULT__COARSE_POINTER, defaultCoarsePointer);\n        defaultUseDirtyRect = retrieve2(root.__ECHARTS__DEFAULT__USE_DIRTY_RECT__, defaultUseDirtyRect);\n      }\n    }\n    if (opts.ssr) {\n      zrender.registerSSRDataGetter(function (el) {\n        var ecData = getECData(el);\n        var dataIndex = ecData.dataIndex;\n        if (dataIndex == null) {\n          return;\n        }\n        var hashMap = createHashMap();\n        hashMap.set('series_index', ecData.seriesIndex);\n        hashMap.set('data_index', dataIndex);\n        ecData.ssrType && hashMap.set('ssr_type', ecData.ssrType);\n        return hashMap;\n      });\n    }\n    var zr = _this._zr = zrender.init(dom, {\n      renderer: opts.renderer || defaultRenderer,\n      devicePixelRatio: opts.devicePixelRatio,\n      width: opts.width,\n      height: opts.height,\n      ssr: opts.ssr,\n      useDirtyRect: retrieve2(opts.useDirtyRect, defaultUseDirtyRect),\n      useCoarsePointer: retrieve2(opts.useCoarsePointer, defaultCoarsePointer),\n      pointerSize: opts.pointerSize\n    });\n    _this._ssr = opts.ssr;\n    // Expect 60 fps.\n    _this._throttledZrFlush = throttle(bind(zr.flush, zr), 17);\n    theme = clone(theme);\n    theme && backwardCompat(theme, true);\n    _this._theme = theme;\n    _this._locale = createLocaleObject(opts.locale || SYSTEM_LANG);\n    _this._coordSysMgr = new CoordinateSystemManager();\n    var api = _this._api = createExtensionAPI(_this);\n    // Sort on demand\n    function prioritySortFunc(a, b) {\n      return a.__prio - b.__prio;\n    }\n    timsort(visualFuncs, prioritySortFunc);\n    timsort(dataProcessorFuncs, prioritySortFunc);\n    _this._scheduler = new Scheduler(_this, api, dataProcessorFuncs, visualFuncs);\n    _this._messageCenter = new MessageCenter();\n    // Init mouse events\n    _this._initEvents();\n    // In case some people write `window.onresize = chart.resize`\n    _this.resize = bind(_this.resize, _this);\n    zr.animation.on('frame', _this._onframe, _this);\n    bindRenderedEvent(zr, _this);\n    bindMouseEvent(zr, _this);\n    // ECharts instance can be used as value.\n    setAsPrimitive(_this);\n    return _this;\n  }\n  ECharts.prototype._onframe = function () {\n    if (this._disposed) {\n      return;\n    }\n    applyChangedStates(this);\n    var scheduler = this._scheduler;\n    // Lazy update\n    if (this[PENDING_UPDATE]) {\n      var silent = this[PENDING_UPDATE].silent;\n      this[IN_MAIN_PROCESS_KEY] = true;\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, this[PENDING_UPDATE].updateParams);\n      } catch (e) {\n        this[IN_MAIN_PROCESS_KEY] = false;\n        this[PENDING_UPDATE] = null;\n        throw e;\n      }\n      // At present, in each frame, zrender performs:\n      //   (1) animation step forward.\n      //   (2) trigger('frame') (where this `_onframe` is called)\n      //   (3) zrender flush (render).\n      // If we do nothing here, since we use `setToFinal: true`, the step (3) above\n      // will render the final state of the elements before the real animation started.\n      this._zr.flush();\n      this[IN_MAIN_PROCESS_KEY] = false;\n      this[PENDING_UPDATE] = null;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n    // Avoid do both lazy update and progress in one frame.\n    else if (scheduler.unfinished) {\n      // Stream progress.\n      var remainTime = TEST_FRAME_REMAIN_TIME;\n      var ecModel = this._model;\n      var api = this._api;\n      scheduler.unfinished = false;\n      do {\n        var startTime = +new Date();\n        scheduler.performSeriesTasks(ecModel);\n        // Currently dataProcessorFuncs do not check threshold.\n        scheduler.performDataProcessorTasks(ecModel);\n        updateStreamModes(this, ecModel);\n        // Do not update coordinate system here. Because that coord system update in\n        // each frame is not a good user experience. So we follow the rule that\n        // the extent of the coordinate system is determined in the first frame (the\n        // frame is executed immediately after task reset.\n        // this._coordSysMgr.update(ecModel, api);\n        // console.log('--- ec frame visual ---', remainTime);\n        scheduler.performVisualTasks(ecModel);\n        renderSeries(this, this._model, api, 'remain', {});\n        remainTime -= +new Date() - startTime;\n      } while (remainTime > 0 && scheduler.unfinished);\n      // Call flush explicitly for trigger finished event.\n      if (!scheduler.unfinished) {\n        this._zr.flush();\n      }\n      // Else, zr flushing be ensue within the same frame,\n      // because zr flushing is after onframe event.\n    }\n  };\n  ECharts.prototype.getDom = function () {\n    return this._dom;\n  };\n  ECharts.prototype.getId = function () {\n    return this.id;\n  };\n  ECharts.prototype.getZr = function () {\n    return this._zr;\n  };\n  ECharts.prototype.isSSR = function () {\n    return this._ssr;\n  };\n  /* eslint-disable-next-line */\n  ECharts.prototype.setOption = function (option, notMerge, lazyUpdate) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`setOption` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var silent;\n    var replaceMerge;\n    var transitionOpt;\n    if (isObject(notMerge)) {\n      lazyUpdate = notMerge.lazyUpdate;\n      silent = notMerge.silent;\n      replaceMerge = notMerge.replaceMerge;\n      transitionOpt = notMerge.transition;\n      notMerge = notMerge.notMerge;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    if (!this._model || notMerge) {\n      var optionManager = new OptionManager(this._api);\n      var theme = this._theme;\n      var ecModel = this._model = new GlobalModel();\n      ecModel.scheduler = this._scheduler;\n      ecModel.ssr = this._ssr;\n      ecModel.init(null, null, null, theme, this._locale, optionManager);\n    }\n    this._model.setOption(option, {\n      replaceMerge: replaceMerge\n    }, optionPreprocessorFuncs);\n    var updateParams = {\n      seriesTransition: transitionOpt,\n      optionChanged: true\n    };\n    if (lazyUpdate) {\n      this[PENDING_UPDATE] = {\n        silent: silent,\n        updateParams: updateParams\n      };\n      this[IN_MAIN_PROCESS_KEY] = false;\n      // `setOption(option, {lazyMode: true})` may be called when zrender has been slept.\n      // It should wake it up to make sure zrender start to render at the next frame.\n      this.getZr().wakeUp();\n    } else {\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, updateParams);\n      } catch (e) {\n        this[PENDING_UPDATE] = null;\n        this[IN_MAIN_PROCESS_KEY] = false;\n        throw e;\n      }\n      // Ensure zr refresh sychronously, and then pixel in canvas can be\n      // fetched after `setOption`.\n      if (!this._ssr) {\n        // not use flush when using ssr mode.\n        this._zr.flush();\n      }\n      this[PENDING_UPDATE] = null;\n      this[IN_MAIN_PROCESS_KEY] = false;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n  };\n  /**\n   * @deprecated\n   */\n  ECharts.prototype.setTheme = function () {\n    deprecateLog('ECharts#setTheme() is DEPRECATED in ECharts 3.0');\n  };\n  // We don't want developers to use getModel directly.\n  ECharts.prototype.getModel = function () {\n    return this._model;\n  };\n  ECharts.prototype.getOption = function () {\n    return this._model && this._model.getOption();\n  };\n  ECharts.prototype.getWidth = function () {\n    return this._zr.getWidth();\n  };\n  ECharts.prototype.getHeight = function () {\n    return this._zr.getHeight();\n  };\n  ECharts.prototype.getDevicePixelRatio = function () {\n    return this._zr.painter.dpr\n    /* eslint-disable-next-line */ || env.hasGlobalWindow && window.devicePixelRatio || 1;\n  };\n  /**\n   * Get canvas which has all thing rendered\n   * @deprecated Use renderToCanvas instead.\n   */\n  ECharts.prototype.getRenderedCanvas = function (opts) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('getRenderedCanvas', 'renderToCanvas');\n    }\n    return this.renderToCanvas(opts);\n  };\n  ECharts.prototype.renderToCanvas = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'canvas') {\n        throw new Error('renderToCanvas can only be used in the canvas renderer.');\n      }\n    }\n    return painter.getRenderedCanvas({\n      backgroundColor: opts.backgroundColor || this._model.get('backgroundColor'),\n      pixelRatio: opts.pixelRatio || this.getDevicePixelRatio()\n    });\n  };\n  ECharts.prototype.renderToSVGString = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'svg') {\n        throw new Error('renderToSVGString can only be used in the svg renderer.');\n      }\n    }\n    return painter.renderToString({\n      useViewBox: opts.useViewBox\n    });\n  };\n  /**\n   * Get svg data url\n   */\n  ECharts.prototype.getSvgDataURL = function () {\n    if (!env.svgSupported) {\n      return;\n    }\n    var zr = this._zr;\n    var list = zr.storage.getDisplayList();\n    // Stop animations\n    each(list, function (el) {\n      el.stopAnimation(null, true);\n    });\n    return zr.painter.toDataURL();\n  };\n  ECharts.prototype.getDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    opts = opts || {};\n    var excludeComponents = opts.excludeComponents;\n    var ecModel = this._model;\n    var excludesComponentViews = [];\n    var self = this;\n    each(excludeComponents, function (componentType) {\n      ecModel.eachComponent({\n        mainType: componentType\n      }, function (component) {\n        var view = self._componentsMap[component.__viewId];\n        if (!view.group.ignore) {\n          excludesComponentViews.push(view);\n          view.group.ignore = true;\n        }\n      });\n    });\n    var url = this._zr.painter.getType() === 'svg' ? this.getSvgDataURL() : this.renderToCanvas(opts).toDataURL('image/' + (opts && opts.type || 'png'));\n    each(excludesComponentViews, function (view) {\n      view.group.ignore = false;\n    });\n    return url;\n  };\n  ECharts.prototype.getConnectedDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var isSvg = opts.type === 'svg';\n    var groupId = this.group;\n    var mathMin = Math.min;\n    var mathMax = Math.max;\n    var MAX_NUMBER = Infinity;\n    if (connectedGroups[groupId]) {\n      var left_1 = MAX_NUMBER;\n      var top_1 = MAX_NUMBER;\n      var right_1 = -MAX_NUMBER;\n      var bottom_1 = -MAX_NUMBER;\n      var canvasList_1 = [];\n      var dpr_1 = opts && opts.pixelRatio || this.getDevicePixelRatio();\n      each(instances, function (chart, id) {\n        if (chart.group === groupId) {\n          var canvas = isSvg ? chart.getZr().painter.getSvgDom().innerHTML : chart.renderToCanvas(clone(opts));\n          var boundingRect = chart.getDom().getBoundingClientRect();\n          left_1 = mathMin(boundingRect.left, left_1);\n          top_1 = mathMin(boundingRect.top, top_1);\n          right_1 = mathMax(boundingRect.right, right_1);\n          bottom_1 = mathMax(boundingRect.bottom, bottom_1);\n          canvasList_1.push({\n            dom: canvas,\n            left: boundingRect.left,\n            top: boundingRect.top\n          });\n        }\n      });\n      left_1 *= dpr_1;\n      top_1 *= dpr_1;\n      right_1 *= dpr_1;\n      bottom_1 *= dpr_1;\n      var width = right_1 - left_1;\n      var height = bottom_1 - top_1;\n      var targetCanvas = platformApi.createCanvas();\n      var zr_1 = zrender.init(targetCanvas, {\n        renderer: isSvg ? 'svg' : 'canvas'\n      });\n      zr_1.resize({\n        width: width,\n        height: height\n      });\n      if (isSvg) {\n        var content_1 = '';\n        each(canvasList_1, function (item) {\n          var x = item.left - left_1;\n          var y = item.top - top_1;\n          content_1 += '<g transform=\"translate(' + x + ',' + y + ')\">' + item.dom + '</g>';\n        });\n        zr_1.painter.getSvgRoot().innerHTML = content_1;\n        if (opts.connectedBackgroundColor) {\n          zr_1.painter.setBackgroundColor(opts.connectedBackgroundColor);\n        }\n        zr_1.refreshImmediately();\n        return zr_1.painter.toDataURL();\n      } else {\n        // Background between the charts\n        if (opts.connectedBackgroundColor) {\n          zr_1.add(new graphic.Rect({\n            shape: {\n              x: 0,\n              y: 0,\n              width: width,\n              height: height\n            },\n            style: {\n              fill: opts.connectedBackgroundColor\n            }\n          }));\n        }\n        each(canvasList_1, function (item) {\n          var img = new graphic.Image({\n            style: {\n              x: item.left * dpr_1 - left_1,\n              y: item.top * dpr_1 - top_1,\n              image: item.dom\n            }\n          });\n          zr_1.add(img);\n        });\n        zr_1.refreshImmediately();\n        return targetCanvas.toDataURL('image/' + (opts && opts.type || 'png'));\n      }\n    } else {\n      return this.getDataURL(opts);\n    }\n  };\n  ECharts.prototype.convertToPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertToPixel', finder, value);\n  };\n  ECharts.prototype.convertFromPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertFromPixel', finder, value);\n  };\n  /**\n   * Is the specified coordinate systems or components contain the given pixel point.\n   * @param {Array|number} value\n   * @return {boolean} result\n   */\n  ECharts.prototype.containPixel = function (finder, value) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var ecModel = this._model;\n    var result;\n    var findResult = modelUtil.parseFinder(ecModel, finder);\n    each(findResult, function (models, key) {\n      key.indexOf('Models') >= 0 && each(models, function (model) {\n        var coordSys = model.coordinateSystem;\n        if (coordSys && coordSys.containPoint) {\n          result = result || !!coordSys.containPoint(value);\n        } else if (key === 'seriesModels') {\n          var view = this._chartsMap[model.__viewId];\n          if (view && view.containPoint) {\n            result = result || view.containPoint(value, model);\n          } else {\n            if (process.env.NODE_ENV !== 'production') {\n              warn(key + ': ' + (view ? 'The found component do not support containPoint.' : 'No view mapping to the found component.'));\n            }\n          }\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            warn(key + ': containPoint is not supported');\n          }\n        }\n      }, this);\n    }, this);\n    return !!result;\n  };\n  /**\n   * Get visual from series or data.\n   * @param finder\n   *        If string, e.g., 'series', means {seriesIndex: 0}.\n   *        If Object, could contain some of these properties below:\n   *        {\n   *            seriesIndex / seriesId / seriesName,\n   *            dataIndex / dataIndexInside\n   *        }\n   *        If dataIndex is not specified, series visual will be fetched,\n   *        but not data item visual.\n   *        If all of seriesIndex, seriesId, seriesName are not specified,\n   *        visual will be fetched from first series.\n   * @param visualType 'color', 'symbol', 'symbolSize'\n   */\n  ECharts.prototype.getVisual = function (finder, visualType) {\n    var ecModel = this._model;\n    var parsedFinder = modelUtil.parseFinder(ecModel, finder, {\n      defaultMainType: 'series'\n    });\n    var seriesModel = parsedFinder.seriesModel;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!seriesModel) {\n        warn('There is no specified series model');\n      }\n    }\n    var data = seriesModel.getData();\n    var dataIndexInside = parsedFinder.hasOwnProperty('dataIndexInside') ? parsedFinder.dataIndexInside : parsedFinder.hasOwnProperty('dataIndex') ? data.indexOfRawIndex(parsedFinder.dataIndex) : null;\n    return dataIndexInside != null ? getItemVisualFromData(data, dataIndexInside, visualType) : getVisualFromData(data, visualType);\n  };\n  /**\n   * Get view of corresponding component model\n   */\n  ECharts.prototype.getViewOfComponentModel = function (componentModel) {\n    return this._componentsMap[componentModel.__viewId];\n  };\n  /**\n   * Get view of corresponding series model\n   */\n  ECharts.prototype.getViewOfSeriesModel = function (seriesModel) {\n    return this._chartsMap[seriesModel.__viewId];\n  };\n  ECharts.prototype._initEvents = function () {\n    var _this = this;\n    each(MOUSE_EVENT_NAMES, function (eveName) {\n      var handler = function handler(e) {\n        var ecModel = _this.getModel();\n        var el = e.target;\n        var params;\n        var isGlobalOut = eveName === 'globalout';\n        // no e.target when 'globalout'.\n        if (isGlobalOut) {\n          params = {};\n        } else {\n          el && findEventDispatcher(el, function (parent) {\n            var ecData = getECData(parent);\n            if (ecData && ecData.dataIndex != null) {\n              var dataModel = ecData.dataModel || ecModel.getSeriesByIndex(ecData.seriesIndex);\n              params = dataModel && dataModel.getDataParams(ecData.dataIndex, ecData.dataType, el) || {};\n              return true;\n            }\n            // If element has custom eventData of components\n            else if (ecData.eventData) {\n              params = extend({}, ecData.eventData);\n              return true;\n            }\n          }, true);\n        }\n        // Contract: if params prepared in mouse event,\n        // these properties must be specified:\n        // {\n        //    componentType: string (component main type)\n        //    componentIndex: number\n        // }\n        // Otherwise event query can not work.\n        if (params) {\n          var componentType = params.componentType;\n          var componentIndex = params.componentIndex;\n          // Special handling for historic reason: when trigger by\n          // markLine/markPoint/markArea, the componentType is\n          // 'markLine'/'markPoint'/'markArea', but we should better\n          // enable them to be queried by seriesIndex, since their\n          // option is set in each series.\n          if (componentType === 'markLine' || componentType === 'markPoint' || componentType === 'markArea') {\n            componentType = 'series';\n            componentIndex = params.seriesIndex;\n          }\n          var model = componentType && componentIndex != null && ecModel.getComponent(componentType, componentIndex);\n          var view = model && _this[model.mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId];\n          if (process.env.NODE_ENV !== 'production') {\n            // `event.componentType` and `event[componentTpype + 'Index']` must not\n            // be missed, otherwise there is no way to distinguish source component.\n            // See `dataFormat.getDataParams`.\n            if (!isGlobalOut && !(model && view)) {\n              warn('model or view can not be found by params');\n            }\n          }\n          params.event = e;\n          params.type = eveName;\n          _this._$eventProcessor.eventInfo = {\n            targetEl: el,\n            packedEvent: params,\n            model: model,\n            view: view\n          };\n          _this.trigger(eveName, params);\n        }\n      };\n      // Consider that some component (like tooltip, brush, ...)\n      // register zr event handler, but user event handler might\n      // do anything, such as call `setOption` or `dispatchAction`,\n      // which probably update any of the content and probably\n      // cause problem if it is called previous other inner handlers.\n      handler.zrEventfulCallAtLast = true;\n      _this._zr.on(eveName, handler, _this);\n    });\n    each(eventActionMap, function (actionType, eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    // Extra events\n    // TODO register?\n    each(['selectchanged'], function (eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    handleLegacySelectEvents(this._messageCenter, this, this._api);\n  };\n  ECharts.prototype.isDisposed = function () {\n    return this._disposed;\n  };\n  ECharts.prototype.clear = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this.setOption({\n      series: []\n    }, true);\n  };\n  ECharts.prototype.dispose = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._disposed = true;\n    var dom = this.getDom();\n    if (dom) {\n      modelUtil.setAttribute(this.getDom(), DOM_ATTRIBUTE_KEY, '');\n    }\n    var chart = this;\n    var api = chart._api;\n    var ecModel = chart._model;\n    each(chart._componentsViews, function (component) {\n      component.dispose(ecModel, api);\n    });\n    each(chart._chartsViews, function (chart) {\n      chart.dispose(ecModel, api);\n    });\n    // Dispose after all views disposed\n    chart._zr.dispose();\n    // Set properties to null.\n    // To reduce the memory cost in case the top code still holds this instance unexpectedly.\n    chart._dom = chart._model = chart._chartsMap = chart._componentsMap = chart._chartsViews = chart._componentsViews = chart._scheduler = chart._api = chart._zr = chart._throttledZrFlush = chart._theme = chart._coordSysMgr = chart._messageCenter = null;\n    delete instances[chart.id];\n  };\n  /**\n   * Resize the chart\n   */\n  ECharts.prototype.resize = function (opts) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`resize` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._zr.resize(opts);\n    var ecModel = this._model;\n    // Resize loading effect\n    this._loadingFX && this._loadingFX.resize();\n    if (!ecModel) {\n      return;\n    }\n    var needPrepare = ecModel.resetOption('media');\n    var silent = opts && opts.silent;\n    // There is some real cases that:\n    // chart.setOption(option, { lazyUpdate: true });\n    // chart.resize();\n    if (this[PENDING_UPDATE]) {\n      if (silent == null) {\n        silent = this[PENDING_UPDATE].silent;\n      }\n      needPrepare = true;\n      this[PENDING_UPDATE] = null;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    try {\n      needPrepare && prepare(this);\n      updateMethods.update.call(this, {\n        type: 'resize',\n        animation: extend({\n          // Disable animation\n          duration: 0\n        }, opts && opts.animation)\n      });\n    } catch (e) {\n      this[IN_MAIN_PROCESS_KEY] = false;\n      throw e;\n    }\n    this[IN_MAIN_PROCESS_KEY] = false;\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.showLoading = function (name, cfg) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (isObject(name)) {\n      cfg = name;\n      name = '';\n    }\n    name = name || 'default';\n    this.hideLoading();\n    if (!loadingEffects[name]) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Loading effects ' + name + ' not exists.');\n      }\n      return;\n    }\n    var el = loadingEffects[name](this._api, cfg);\n    var zr = this._zr;\n    this._loadingFX = el;\n    zr.add(el);\n  };\n  /**\n   * Hide loading effect\n   */\n  ECharts.prototype.hideLoading = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._loadingFX && this._zr.remove(this._loadingFX);\n    this._loadingFX = null;\n  };\n  ECharts.prototype.makeActionFromEvent = function (eventObj) {\n    var payload = extend({}, eventObj);\n    payload.type = eventActionMap[eventObj.type];\n    return payload;\n  };\n  /**\n   * @param opt If pass boolean, means opt.silent\n   * @param opt.silent Default `false`. Whether trigger events.\n   * @param opt.flush Default `undefined`.\n   *        true: Flush immediately, and then pixel in canvas can be fetched\n   *            immediately. Caution: it might affect performance.\n   *        false: Not flush.\n   *        undefined: Auto decide whether perform flush.\n   */\n  ECharts.prototype.dispatchAction = function (payload, opt) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (!isObject(opt)) {\n      opt = {\n        silent: !!opt\n      };\n    }\n    if (!actions[payload.type]) {\n      return;\n    }\n    // Avoid dispatch action before setOption. Especially in `connect`.\n    if (!this._model) {\n      return;\n    }\n    // May dispatchAction in rendering procedure\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      this._pendingActions.push(payload);\n      return;\n    }\n    var silent = opt.silent;\n    doDispatchAction.call(this, payload, silent);\n    var flush = opt.flush;\n    if (flush) {\n      this._zr.flush();\n    } else if (flush !== false && env.browser.weChat) {\n      // In WeChat embedded browser, `requestAnimationFrame` and `setInterval`\n      // hang when sliding page (on touch event), which cause that zr does not\n      // refresh until user interaction finished, which is not expected.\n      // But `dispatchAction` may be called too frequently when pan on touch\n      // screen, which impacts performance if do not throttle them.\n      this._throttledZrFlush();\n    }\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.updateLabelLayout = function () {\n    lifecycle.trigger('series:layoutlabels', this._model, this._api, {\n      // Not adding series labels.\n      // TODO\n      updatedSeries: []\n    });\n  };\n  ECharts.prototype.appendData = function (params) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var seriesIndex = params.seriesIndex;\n    var ecModel = this.getModel();\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (process.env.NODE_ENV !== 'production') {\n      assert(params.data && seriesModel);\n    }\n    seriesModel.appendData(params);\n    // Note: `appendData` does not support that update extent of coordinate\n    // system, util some scenario require that. In the expected usage of\n    // `appendData`, the initial extent of coordinate system should better\n    // be fixed by axis `min`/`max` setting or initial data, otherwise if\n    // the extent changed while `appendData`, the location of the painted\n    // graphic elements have to be changed, which make the usage of\n    // `appendData` meaningless.\n    this._scheduler.unfinished = true;\n    this.getZr().wakeUp();\n  };\n  // A work around for no `internal` modifier in ts yet but\n  // need to strictly hide private methods to JS users.\n  ECharts.internalField = function () {\n    prepare = function prepare(ecIns) {\n      var scheduler = ecIns._scheduler;\n      scheduler.restorePipelines(ecIns._model);\n      scheduler.prepareStageTasks();\n      prepareView(ecIns, true);\n      prepareView(ecIns, false);\n      scheduler.plan();\n    };\n    /**\n     * Prepare view instances of charts and components\n     */\n    prepareView = function prepareView(ecIns, isComponent) {\n      var ecModel = ecIns._model;\n      var scheduler = ecIns._scheduler;\n      var viewList = isComponent ? ecIns._componentsViews : ecIns._chartsViews;\n      var viewMap = isComponent ? ecIns._componentsMap : ecIns._chartsMap;\n      var zr = ecIns._zr;\n      var api = ecIns._api;\n      for (var i = 0; i < viewList.length; i++) {\n        viewList[i].__alive = false;\n      }\n      isComponent ? ecModel.eachComponent(function (componentType, model) {\n        componentType !== 'series' && doPrepare(model);\n      }) : ecModel.eachSeries(doPrepare);\n      function doPrepare(model) {\n        // By default view will be reused if possible for the case that `setOption` with \"notMerge\"\n        // mode and need to enable transition animation. (Usually, when they have the same id, or\n        // especially no id but have the same type & name & index. See the `model.id` generation\n        // rule in `makeIdAndName` and `viewId` generation rule here).\n        // But in `replaceMerge` mode, this feature should be able to disabled when it is clear that\n        // the new model has nothing to do with the old model.\n        var requireNewView = model.__requireNewView;\n        // This command should not work twice.\n        model.__requireNewView = false;\n        // Consider: id same and type changed.\n        var viewId = '_ec_' + model.id + '_' + model.type;\n        var view = !requireNewView && viewMap[viewId];\n        if (!view) {\n          var classType = parseClassType(model.type);\n          var Clazz = isComponent ? ComponentView.getClass(classType.main, classType.sub) :\n          // FIXME:TS\n          // (ChartView as ChartViewConstructor).getClass('series', classType.sub)\n          // For backward compat, still support a chart type declared as only subType\n          // like \"liquidfill\", but recommend \"series.liquidfill\"\n          // But need a base class to make a type series.\n          ChartView.getClass(classType.sub);\n          if (process.env.NODE_ENV !== 'production') {\n            assert(Clazz, classType.sub + ' does not exist.');\n          }\n          view = new Clazz();\n          view.init(ecModel, api);\n          viewMap[viewId] = view;\n          viewList.push(view);\n          zr.add(view.group);\n        }\n        model.__viewId = view.__id = viewId;\n        view.__alive = true;\n        view.__model = model;\n        view.group.__ecComponentInfo = {\n          mainType: model.mainType,\n          index: model.componentIndex\n        };\n        !isComponent && scheduler.prepareView(view, model, ecModel, api);\n      }\n      for (var i = 0; i < viewList.length;) {\n        var view = viewList[i];\n        if (!view.__alive) {\n          !isComponent && view.renderTask.dispose();\n          zr.remove(view.group);\n          view.dispose(ecModel, api);\n          viewList.splice(i, 1);\n          if (viewMap[view.__id] === view) {\n            delete viewMap[view.__id];\n          }\n          view.__id = view.group.__ecComponentInfo = null;\n        } else {\n          i++;\n        }\n      }\n    };\n    updateDirectly = function updateDirectly(ecIns, method, payload, mainType, subType) {\n      var ecModel = ecIns._model;\n      ecModel.setUpdatePayload(payload);\n      // broadcast\n      if (!mainType) {\n        // FIXME\n        // Chart will not be update directly here, except set dirty.\n        // But there is no such scenario now.\n        each([].concat(ecIns._componentsViews).concat(ecIns._chartsViews), callView);\n        return;\n      }\n      var query = {};\n      query[mainType + 'Id'] = payload[mainType + 'Id'];\n      query[mainType + 'Index'] = payload[mainType + 'Index'];\n      query[mainType + 'Name'] = payload[mainType + 'Name'];\n      var condition = {\n        mainType: mainType,\n        query: query\n      };\n      subType && (condition.subType = subType); // subType may be '' by parseClassType;\n      var excludeSeriesId = payload.excludeSeriesId;\n      var excludeSeriesIdMap;\n      if (excludeSeriesId != null) {\n        excludeSeriesIdMap = createHashMap();\n        each(modelUtil.normalizeToArray(excludeSeriesId), function (id) {\n          var modelId = modelUtil.convertOptionIdName(id, null);\n          if (modelId != null) {\n            excludeSeriesIdMap.set(modelId, true);\n          }\n        });\n      }\n      // If dispatchAction before setOption, do nothing.\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        if (isHighDownPayload(payload)) {\n          if (model instanceof SeriesModel) {\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && !payload.notBlur && !model.get(['emphasis', 'disabled'])) {\n              blurSeriesFromHighlightPayload(model, payload, ecIns._api);\n            }\n          } else {\n            var _a = findComponentHighDownDispatchers(model.mainType, model.componentIndex, payload.name, ecIns._api),\n              focusSelf = _a.focusSelf,\n              dispatchers = _a.dispatchers;\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && focusSelf && !payload.notBlur) {\n              blurComponent(model.mainType, model.componentIndex, ecIns._api);\n            }\n            // PENDING:\n            // Whether to put this \"enter emphasis\" code in `ComponentView`,\n            // which will be the same as `ChartView` but might be not necessary\n            // and will be far from this logic.\n            if (dispatchers) {\n              each(dispatchers, function (dispatcher) {\n                payload.type === HIGHLIGHT_ACTION_TYPE ? enterEmphasis(dispatcher) : leaveEmphasis(dispatcher);\n              });\n            }\n          }\n        } else if (isSelectChangePayload(payload)) {\n          // TODO geo\n          if (model instanceof SeriesModel) {\n            toggleSelectionFromPayload(model, payload, ecIns._api);\n            updateSeriesElementSelection(model);\n            markStatusToUpdate(ecIns);\n          }\n        }\n      }, ecIns);\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        callView(ecIns[mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId]);\n      }, ecIns);\n      function callView(view) {\n        view && view.__alive && view[method] && view[method](view.__model, ecModel, ecIns._api, payload);\n      }\n    };\n    updateMethods = {\n      prepareAndUpdate: function prepareAndUpdate(payload) {\n        prepare(this);\n        updateMethods.update.call(this, payload, {\n          // Needs to mark option changed if newOption is given.\n          // It's from MagicType.\n          // TODO If use a separate flag optionChanged in payload?\n          optionChanged: payload.newOption != null\n        });\n      },\n      update: function update(payload, updateParams) {\n        var ecModel = this._model;\n        var api = this._api;\n        var zr = this._zr;\n        var coordSysMgr = this._coordSysMgr;\n        var scheduler = this._scheduler;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        scheduler.restoreData(ecModel, payload);\n        scheduler.performSeriesTasks(ecModel);\n        // TODO\n        // Save total ecModel here for undo/redo (after restoring data and before processing data).\n        // Undo (restoration of total ecModel) can be carried out in 'action' or outside API call.\n        // Create new coordinate system each update\n        // In LineView may save the old coordinate system and use it to get the original point.\n        coordSysMgr.create(ecModel, api);\n        scheduler.performDataProcessorTasks(ecModel, payload);\n        // Current stream render is not supported in data process. So we can update\n        // stream modes after data processing, where the filtered data is used to\n        // determine whether to use progressive rendering.\n        updateStreamModes(this, ecModel);\n        // We update stream modes before coordinate system updated, then the modes info\n        // can be fetched when coord sys updating (consider the barGrid extent fix). But\n        // the drawback is the full coord info can not be fetched. Fortunately this full\n        // coord is not required in stream mode updater currently.\n        coordSysMgr.update(ecModel, api);\n        clearColorPalette(ecModel);\n        scheduler.performVisualTasks(ecModel, payload);\n        render(this, ecModel, api, payload, updateParams);\n        // Set background\n        var backgroundColor = ecModel.get('backgroundColor') || 'transparent';\n        var darkMode = ecModel.get('darkMode');\n        zr.setBackgroundColor(backgroundColor);\n        // Force set dark mode.\n        if (darkMode != null && darkMode !== 'auto') {\n          zr.setDarkMode(darkMode);\n        }\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateTransform: function updateTransform(payload) {\n        var _this = this;\n        var ecModel = this._model;\n        var api = this._api;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // ChartView.markUpdateMethod(payload, 'updateTransform');\n        var componentDirtyList = [];\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType === 'series') {\n            return;\n          }\n          var componentView = _this.getViewOfComponentModel(componentModel);\n          if (componentView && componentView.__alive) {\n            if (componentView.updateTransform) {\n              var result = componentView.updateTransform(componentModel, ecModel, api, payload);\n              result && result.update && componentDirtyList.push(componentView);\n            } else {\n              componentDirtyList.push(componentView);\n            }\n          }\n        });\n        var seriesDirtyMap = createHashMap();\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          if (chartView.updateTransform) {\n            var result = chartView.updateTransform(seriesModel, ecModel, api, payload);\n            result && result.update && seriesDirtyMap.set(seriesModel.uid, 1);\n          } else {\n            seriesDirtyMap.set(seriesModel.uid, 1);\n          }\n        });\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        // this._scheduler.performVisualTasks(ecModel, payload, 'layout', true);\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true,\n          dirtyMap: seriesDirtyMap\n        });\n        // Currently, not call render of components. Geo render cost a lot.\n        // renderComponents(ecIns, ecModel, api, payload, componentDirtyList);\n        renderSeries(this, ecModel, api, payload, {}, seriesDirtyMap);\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateView: function updateView(payload) {\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        ChartView.markUpdateMethod(payload, 'updateView');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true\n        });\n        render(this, ecModel, this._api, payload, {});\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateVisual: function updateVisual(payload) {\n        // updateMethods.update.call(this, payload);\n        var _this = this;\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // clear all visual\n        ecModel.eachSeries(function (seriesModel) {\n          seriesModel.getData().clearAllVisual();\n        });\n        // Perform visual\n        ChartView.markUpdateMethod(payload, 'updateVisual');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          visualType: 'visual',\n          setDirty: true\n        });\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType !== 'series') {\n            var componentView = _this.getViewOfComponentModel(componentModel);\n            componentView && componentView.__alive && componentView.updateVisual(componentModel, ecModel, _this._api, payload);\n          }\n        });\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          chartView.updateVisual(seriesModel, ecModel, _this._api, payload);\n        });\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateLayout: function updateLayout(payload) {\n        updateMethods.update.call(this, payload);\n      }\n    };\n    doConvertPixel = function doConvertPixel(ecIns, methodName, finder, value) {\n      if (ecIns._disposed) {\n        disposedWarning(ecIns.id);\n        return;\n      }\n      var ecModel = ecIns._model;\n      var coordSysList = ecIns._coordSysMgr.getCoordinateSystems();\n      var result;\n      var parsedFinder = modelUtil.parseFinder(ecModel, finder);\n      for (var i = 0; i < coordSysList.length; i++) {\n        var coordSys = coordSysList[i];\n        if (coordSys[methodName] && (result = coordSys[methodName](ecModel, parsedFinder, value)) != null) {\n          return result;\n        }\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        warn('No coordinate system that supports ' + methodName + ' found by the given finder.');\n      }\n    };\n    updateStreamModes = function updateStreamModes(ecIns, ecModel) {\n      var chartsMap = ecIns._chartsMap;\n      var scheduler = ecIns._scheduler;\n      ecModel.eachSeries(function (seriesModel) {\n        scheduler.updateStreamModes(seriesModel, chartsMap[seriesModel.__viewId]);\n      });\n    };\n    doDispatchAction = function doDispatchAction(payload, silent) {\n      var _this = this;\n      var ecModel = this.getModel();\n      var payloadType = payload.type;\n      var escapeConnect = payload.escapeConnect;\n      var actionWrap = actions[payloadType];\n      var actionInfo = actionWrap.actionInfo;\n      var cptTypeTmp = (actionInfo.update || 'update').split(':');\n      var updateMethod = cptTypeTmp.pop();\n      var cptType = cptTypeTmp[0] != null && parseClassType(cptTypeTmp[0]);\n      this[IN_MAIN_PROCESS_KEY] = true;\n      var payloads = [payload];\n      var batched = false;\n      // Batch action\n      if (payload.batch) {\n        batched = true;\n        payloads = map(payload.batch, function (item) {\n          item = defaults(extend({}, item), payload);\n          item.batch = null;\n          return item;\n        });\n      }\n      var eventObjBatch = [];\n      var eventObj;\n      var isSelectChange = isSelectChangePayload(payload);\n      var isHighDown = isHighDownPayload(payload);\n      // Only leave blur once if there are multiple batches.\n      if (isHighDown) {\n        allLeaveBlur(this._api);\n      }\n      each(payloads, function (batchItem) {\n        // Action can specify the event by return it.\n        eventObj = actionWrap.action(batchItem, _this._model, _this._api);\n        // Emit event outside\n        eventObj = eventObj || extend({}, batchItem);\n        // Convert type to eventType\n        eventObj.type = actionInfo.event || eventObj.type;\n        eventObjBatch.push(eventObj);\n        // light update does not perform data process, layout and visual.\n        if (isHighDown) {\n          var _a = modelUtil.preParseFinder(payload),\n            queryOptionMap = _a.queryOptionMap,\n            mainTypeSpecified = _a.mainTypeSpecified;\n          var componentMainType = mainTypeSpecified ? queryOptionMap.keys()[0] : 'series';\n          updateDirectly(_this, updateMethod, batchItem, componentMainType);\n          markStatusToUpdate(_this);\n        } else if (isSelectChange) {\n          // At present `dispatchAction({ type: 'select', ... })` is not supported on components.\n          // geo still use 'geoselect'.\n          updateDirectly(_this, updateMethod, batchItem, 'series');\n          markStatusToUpdate(_this);\n        } else if (cptType) {\n          updateDirectly(_this, updateMethod, batchItem, cptType.main, cptType.sub);\n        }\n      });\n      if (updateMethod !== 'none' && !isHighDown && !isSelectChange && !cptType) {\n        try {\n          // Still dirty\n          if (this[PENDING_UPDATE]) {\n            prepare(this);\n            updateMethods.update.call(this, payload);\n            this[PENDING_UPDATE] = null;\n          } else {\n            updateMethods[updateMethod].call(this, payload);\n          }\n        } catch (e) {\n          this[IN_MAIN_PROCESS_KEY] = false;\n          throw e;\n        }\n      }\n      // Follow the rule of action batch\n      if (batched) {\n        eventObj = {\n          type: actionInfo.event || payloadType,\n          escapeConnect: escapeConnect,\n          batch: eventObjBatch\n        };\n      } else {\n        eventObj = eventObjBatch[0];\n      }\n      this[IN_MAIN_PROCESS_KEY] = false;\n      if (!silent) {\n        var messageCenter = this._messageCenter;\n        messageCenter.trigger(eventObj.type, eventObj);\n        // Extra triggered 'selectchanged' event\n        if (isSelectChange) {\n          var newObj = {\n            type: 'selectchanged',\n            escapeConnect: escapeConnect,\n            selected: getAllSelectedIndices(ecModel),\n            isFromClick: payload.isFromClick || false,\n            fromAction: payload.type,\n            fromActionPayload: payload\n          };\n          messageCenter.trigger(newObj.type, newObj);\n        }\n      }\n    };\n    flushPendingActions = function flushPendingActions(silent) {\n      var pendingActions = this._pendingActions;\n      while (pendingActions.length) {\n        var payload = pendingActions.shift();\n        doDispatchAction.call(this, payload, silent);\n      }\n    };\n    triggerUpdatedEvent = function triggerUpdatedEvent(silent) {\n      !silent && this.trigger('updated');\n    };\n    /**\n     * Event `rendered` is triggered when zr\n     * rendered. It is useful for realtime\n     * snapshot (reflect animation).\n     *\n     * Event `finished` is triggered when:\n     * (1) zrender rendering finished.\n     * (2) initial animation finished.\n     * (3) progressive rendering finished.\n     * (4) no pending action.\n     * (5) no delayed setOption needs to be processed.\n     */\n    bindRenderedEvent = function bindRenderedEvent(zr, ecIns) {\n      zr.on('rendered', function (params) {\n        ecIns.trigger('rendered', params);\n        // The `finished` event should not be triggered repeatedly,\n        // so it should only be triggered when rendering indeed happens\n        // in zrender. (Consider the case that dipatchAction is keep\n        // triggering when mouse move).\n        if (\n        // Although zr is dirty if initial animation is not finished\n        // and this checking is called on frame, we also check\n        // animation finished for robustness.\n        zr.animation.isFinished() && !ecIns[PENDING_UPDATE] && !ecIns._scheduler.unfinished && !ecIns._pendingActions.length) {\n          ecIns.trigger('finished');\n        }\n      });\n    };\n    bindMouseEvent = function bindMouseEvent(zr, ecIns) {\n      zr.on('mouseover', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOverForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('mouseout', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOutForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('click', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, function (target) {\n          return getECData(target).dataIndex != null;\n        }, true);\n        if (dispatcher) {\n          var actionType = dispatcher.selected ? 'unselect' : 'select';\n          var ecData = getECData(dispatcher);\n          ecIns._api.dispatchAction({\n            type: actionType,\n            dataType: ecData.dataType,\n            dataIndexInside: ecData.dataIndex,\n            seriesIndex: ecData.seriesIndex,\n            isFromClick: true\n          });\n        }\n      });\n    };\n    function clearColorPalette(ecModel) {\n      ecModel.clearColorPalette();\n      ecModel.eachSeries(function (seriesModel) {\n        seriesModel.clearColorPalette();\n      });\n    }\n    ;\n    // Allocate zlevels for series and components\n    function allocateZlevels(ecModel) {\n      ;\n      var componentZLevels = [];\n      var seriesZLevels = [];\n      var hasSeparateZLevel = false;\n      ecModel.eachComponent(function (componentType, componentModel) {\n        var zlevel = componentModel.get('zlevel') || 0;\n        var z = componentModel.get('z') || 0;\n        var zlevelKey = componentModel.getZLevelKey();\n        hasSeparateZLevel = hasSeparateZLevel || !!zlevelKey;\n        (componentType === 'series' ? seriesZLevels : componentZLevels).push({\n          zlevel: zlevel,\n          z: z,\n          idx: componentModel.componentIndex,\n          type: componentType,\n          key: zlevelKey\n        });\n      });\n      if (hasSeparateZLevel) {\n        // Series after component\n        var zLevels = componentZLevels.concat(seriesZLevels);\n        var lastSeriesZLevel_1;\n        var lastSeriesKey_1;\n        timsort(zLevels, function (a, b) {\n          if (a.zlevel === b.zlevel) {\n            return a.z - b.z;\n          }\n          return a.zlevel - b.zlevel;\n        });\n        each(zLevels, function (item) {\n          var componentModel = ecModel.getComponent(item.type, item.idx);\n          var zlevel = item.zlevel;\n          var key = item.key;\n          if (lastSeriesZLevel_1 != null) {\n            zlevel = Math.max(lastSeriesZLevel_1, zlevel);\n          }\n          if (key) {\n            if (zlevel === lastSeriesZLevel_1 && key !== lastSeriesKey_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = key;\n          } else if (lastSeriesKey_1) {\n            if (zlevel === lastSeriesZLevel_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = '';\n          }\n          lastSeriesZLevel_1 = zlevel;\n          componentModel.setZLevel(zlevel);\n        });\n      }\n    }\n    render = function render(ecIns, ecModel, api, payload, updateParams) {\n      allocateZlevels(ecModel);\n      renderComponents(ecIns, ecModel, api, payload, updateParams);\n      each(ecIns._chartsViews, function (chart) {\n        chart.__alive = false;\n      });\n      renderSeries(ecIns, ecModel, api, payload, updateParams);\n      // Remove groups of unrendered charts\n      each(ecIns._chartsViews, function (chart) {\n        if (!chart.__alive) {\n          chart.remove(ecModel, api);\n        }\n      });\n    };\n    renderComponents = function renderComponents(ecIns, ecModel, api, payload, updateParams, dirtyList) {\n      each(dirtyList || ecIns._componentsViews, function (componentView) {\n        var componentModel = componentView.__model;\n        clearStates(componentModel, componentView);\n        componentView.render(componentModel, ecModel, api, payload);\n        updateZ(componentModel, componentView);\n        updateStates(componentModel, componentView);\n      });\n    };\n    /**\n     * Render each chart and component\n     */\n    renderSeries = function renderSeries(ecIns, ecModel, api, payload, updateParams, dirtyMap) {\n      // Render all charts\n      var scheduler = ecIns._scheduler;\n      updateParams = extend(updateParams || {}, {\n        updatedSeries: ecModel.getSeries()\n      });\n      // TODO progressive?\n      lifecycle.trigger('series:beforeupdate', ecModel, api, updateParams);\n      var unfinished = false;\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        chartView.__alive = true;\n        var renderTask = chartView.renderTask;\n        scheduler.updatePayload(renderTask, payload);\n        // TODO states on marker.\n        clearStates(seriesModel, chartView);\n        if (dirtyMap && dirtyMap.get(seriesModel.uid)) {\n          renderTask.dirty();\n        }\n        if (renderTask.perform(scheduler.getPerformArgs(renderTask))) {\n          unfinished = true;\n        }\n        chartView.group.silent = !!seriesModel.get('silent');\n        // Should not call markRedraw on group, because it will disable zrender\n        // incremental render (always render from the __startIndex each frame)\n        // chartView.group.markRedraw();\n        updateBlend(seriesModel, chartView);\n        updateSeriesElementSelection(seriesModel);\n      });\n      scheduler.unfinished = unfinished || scheduler.unfinished;\n      lifecycle.trigger('series:layoutlabels', ecModel, api, updateParams);\n      // transition after label is layouted.\n      lifecycle.trigger('series:transition', ecModel, api, updateParams);\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        // Update Z after labels updated. Before applying states.\n        updateZ(seriesModel, chartView);\n        // NOTE: Update states after label is updated.\n        // label should be in normal status when layouting.\n        updateStates(seriesModel, chartView);\n      });\n      // If use hover layer\n      updateHoverLayerStatus(ecIns, ecModel);\n      lifecycle.trigger('series:afterupdate', ecModel, api, updateParams);\n    };\n    markStatusToUpdate = function markStatusToUpdate(ecIns) {\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = true;\n      // Wake up zrender if it's sleep. Let it update states in the next frame.\n      ecIns.getZr().wakeUp();\n    };\n    applyChangedStates = function applyChangedStates(ecIns) {\n      if (!ecIns[STATUS_NEEDS_UPDATE_KEY]) {\n        return;\n      }\n      ecIns.getZr().storage.traverse(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        applyElementStates(el);\n      });\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = false;\n    };\n    function applyElementStates(el) {\n      var newStates = [];\n      var oldStates = el.currentStates;\n      // Keep other states.\n      for (var i = 0; i < oldStates.length; i++) {\n        var stateName = oldStates[i];\n        if (!(stateName === 'emphasis' || stateName === 'blur' || stateName === 'select')) {\n          newStates.push(stateName);\n        }\n      }\n      // Only use states when it's exists.\n      if (el.selected && el.states.select) {\n        newStates.push('select');\n      }\n      if (el.hoverState === HOVER_STATE_EMPHASIS && el.states.emphasis) {\n        newStates.push('emphasis');\n      } else if (el.hoverState === HOVER_STATE_BLUR && el.states.blur) {\n        newStates.push('blur');\n      }\n      el.useStates(newStates);\n    }\n    function updateHoverLayerStatus(ecIns, ecModel) {\n      var zr = ecIns._zr;\n      var storage = zr.storage;\n      var elCount = 0;\n      storage.traverse(function (el) {\n        if (!el.isGroup) {\n          elCount++;\n        }\n      });\n      if (elCount > ecModel.get('hoverLayerThreshold') && !env.node && !env.worker) {\n        ecModel.eachSeries(function (seriesModel) {\n          if (seriesModel.preventUsingHoverLayer) {\n            return;\n          }\n          var chartView = ecIns._chartsMap[seriesModel.__viewId];\n          if (chartView.__alive) {\n            chartView.eachRendered(function (el) {\n              if (el.states.emphasis) {\n                el.states.emphasis.hoverLayer = true;\n              }\n            });\n          }\n        });\n      }\n    }\n    ;\n    /**\n     * Update chart and blend.\n     */\n    function updateBlend(seriesModel, chartView) {\n      var blendMode = seriesModel.get('blendMode') || null;\n      chartView.eachRendered(function (el) {\n        // FIXME marker and other components\n        if (!el.isGroup) {\n          // DON'T mark the element dirty. In case element is incremental and don't want to rerender.\n          el.style.blend = blendMode;\n        }\n      });\n    }\n    ;\n    function updateZ(model, view) {\n      if (model.preventAutoZ) {\n        return;\n      }\n      var z = model.get('z') || 0;\n      var zlevel = model.get('zlevel') || 0;\n      // Set z and zlevel\n      view.eachRendered(function (el) {\n        doUpdateZ(el, z, zlevel, -Infinity);\n        // Don't traverse the children because it has been traversed in _updateZ.\n        return true;\n      });\n    }\n    ;\n    function doUpdateZ(el, z, zlevel, maxZ2) {\n      // Group may also have textContent\n      var label = el.getTextContent();\n      var labelLine = el.getTextGuideLine();\n      var isGroup = el.isGroup;\n      if (isGroup) {\n        // set z & zlevel of children elements of Group\n        var children = el.childrenRef();\n        for (var i = 0; i < children.length; i++) {\n          maxZ2 = Math.max(doUpdateZ(children[i], z, zlevel, maxZ2), maxZ2);\n        }\n      } else {\n        // not Group\n        el.z = z;\n        el.zlevel = zlevel;\n        maxZ2 = Math.max(el.z2, maxZ2);\n      }\n      // always set z and zlevel if label/labelLine exists\n      if (label) {\n        label.z = z;\n        label.zlevel = zlevel;\n        // lift z2 of text content\n        // TODO if el.emphasis.z2 is spcefied, what about textContent.\n        isFinite(maxZ2) && (label.z2 = maxZ2 + 2);\n      }\n      if (labelLine) {\n        var textGuideLineConfig = el.textGuideLineConfig;\n        labelLine.z = z;\n        labelLine.zlevel = zlevel;\n        isFinite(maxZ2) && (labelLine.z2 = maxZ2 + (textGuideLineConfig && textGuideLineConfig.showAbove ? 1 : -1));\n      }\n      return maxZ2;\n    }\n    // Clear states without animation.\n    // TODO States on component.\n    function clearStates(model, view) {\n      view.eachRendered(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        var textContent = el.getTextContent();\n        var textGuide = el.getTextGuideLine();\n        if (el.stateTransition) {\n          el.stateTransition = null;\n        }\n        if (textContent && textContent.stateTransition) {\n          textContent.stateTransition = null;\n        }\n        if (textGuide && textGuide.stateTransition) {\n          textGuide.stateTransition = null;\n        }\n        // TODO If el is incremental.\n        if (el.hasState()) {\n          el.prevStates = el.currentStates;\n          el.clearStates();\n        } else if (el.prevStates) {\n          el.prevStates = null;\n        }\n      });\n    }\n    function updateStates(model, view) {\n      var stateAnimationModel = model.getModel('stateAnimation');\n      var enableAnimation = model.isAnimationEnabled();\n      var duration = stateAnimationModel.get('duration');\n      var stateTransition = duration > 0 ? {\n        duration: duration,\n        delay: stateAnimationModel.get('delay'),\n        easing: stateAnimationModel.get('easing')\n        // additive: stateAnimationModel.get('additive')\n      } : null;\n      view.eachRendered(function (el) {\n        if (el.states && el.states.emphasis) {\n          // Not applied on removed elements, it may still in fading.\n          if (graphic.isElementRemoved(el)) {\n            return;\n          }\n          if (el instanceof graphic.Path) {\n            savePathStates(el);\n          }\n          // Only updated on changed element. In case element is incremental and don't want to rerender.\n          // TODO, a more proper way?\n          if (el.__dirty) {\n            var prevStates = el.prevStates;\n            // Restore states without animation\n            if (prevStates) {\n              el.useStates(prevStates);\n            }\n          }\n          // Update state transition and enable animation again.\n          if (enableAnimation) {\n            el.stateTransition = stateTransition;\n            var textContent = el.getTextContent();\n            var textGuide = el.getTextGuideLine();\n            // TODO Is it necessary to animate label?\n            if (textContent) {\n              textContent.stateTransition = stateTransition;\n            }\n            if (textGuide) {\n              textGuide.stateTransition = stateTransition;\n            }\n          }\n          // Use highlighted and selected flag to toggle states.\n          if (el.__dirty) {\n            applyElementStates(el);\n          }\n        }\n      });\n    }\n    ;\n    createExtensionAPI = function createExtensionAPI(ecIns) {\n      return new (/** @class */function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n          return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.getCoordinateSystems = function () {\n          return ecIns._coordSysMgr.getCoordinateSystems();\n        };\n        class_1.prototype.getComponentByElement = function (el) {\n          while (el) {\n            var modelInfo = el.__ecComponentInfo;\n            if (modelInfo != null) {\n              return ecIns._model.getComponent(modelInfo.mainType, modelInfo.index);\n            }\n            el = el.parent;\n          }\n        };\n        class_1.prototype.enterEmphasis = function (el, highlightDigit) {\n          enterEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveEmphasis = function (el, highlightDigit) {\n          leaveEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterBlur = function (el) {\n          enterBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveBlur = function (el) {\n          leaveBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterSelect = function (el) {\n          enterSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveSelect = function (el) {\n          leaveSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.getModel = function () {\n          return ecIns.getModel();\n        };\n        class_1.prototype.getViewOfComponentModel = function (componentModel) {\n          return ecIns.getViewOfComponentModel(componentModel);\n        };\n        class_1.prototype.getViewOfSeriesModel = function (seriesModel) {\n          return ecIns.getViewOfSeriesModel(seriesModel);\n        };\n        return class_1;\n      }(ExtensionAPI))(ecIns);\n    };\n    enableConnect = function enableConnect(chart) {\n      function updateConnectedChartsStatus(charts, status) {\n        for (var i = 0; i < charts.length; i++) {\n          var otherChart = charts[i];\n          otherChart[CONNECT_STATUS_KEY] = status;\n        }\n      }\n      each(eventActionMap, function (actionType, eventType) {\n        chart._messageCenter.on(eventType, function (event) {\n          if (connectedGroups[chart.group] && chart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_PENDING) {\n            if (event && event.escapeConnect) {\n              return;\n            }\n            var action_1 = chart.makeActionFromEvent(event);\n            var otherCharts_1 = [];\n            each(instances, function (otherChart) {\n              if (otherChart !== chart && otherChart.group === chart.group) {\n                otherCharts_1.push(otherChart);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_PENDING);\n            each(otherCharts_1, function (otherChart) {\n              if (otherChart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_UPDATING) {\n                otherChart.dispatchAction(action_1);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_UPDATED);\n          }\n        });\n      });\n    };\n  }();\n  return ECharts;\n}(Eventful);\nvar echartsProto = ECharts.prototype;\nechartsProto.on = createRegisterEventWithLowercaseECharts('on');\nechartsProto.off = createRegisterEventWithLowercaseECharts('off');\n/**\n * @deprecated\n */\n// @ts-ignore\nechartsProto.one = function (eventName, cb, ctx) {\n  var self = this;\n  deprecateLog('ECharts#one is deprecated.');\n  function wrapped() {\n    var args2 = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args2[_i] = arguments[_i];\n    }\n    cb && cb.apply && cb.apply(this, args2);\n    // @ts-ignore\n    self.off(eventName, wrapped);\n  }\n  ;\n  // @ts-ignore\n  this.on.call(this, eventName, wrapped, ctx);\n};\nvar MOUSE_EVENT_NAMES = ['click', 'dblclick', 'mouseover', 'mouseout', 'mousemove', 'mousedown', 'mouseup', 'globalout', 'contextmenu'];\nfunction disposedWarning(id) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn('Instance ' + id + ' has been disposed');\n  }\n}\nvar actions = {};\n/**\n * Map eventType to actionType\n */\nvar eventActionMap = {};\nvar dataProcessorFuncs = [];\nvar optionPreprocessorFuncs = [];\nvar visualFuncs = [];\nvar themeStorage = {};\nvar loadingEffects = {};\nvar instances = {};\nvar connectedGroups = {};\nvar idBase = +new Date() - 0;\nvar groupIdBase = +new Date() - 0;\nvar DOM_ATTRIBUTE_KEY = '_echarts_instance_';\n/**\n * @param opts.devicePixelRatio Use window.devicePixelRatio by default\n * @param opts.renderer Can choose 'canvas' or 'svg' to render the chart.\n * @param opts.width Use clientWidth of the input `dom` by default.\n *        Can be 'auto' (the same as null/undefined)\n * @param opts.height Use clientHeight of the input `dom` by default.\n *        Can be 'auto' (the same as null/undefined)\n * @param opts.locale Specify the locale.\n * @param opts.useDirtyRect Enable dirty rectangle rendering or not.\n */\nexport function init(dom, theme, opts) {\n  var isClient = !(opts && opts.ssr);\n  if (isClient) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!dom) {\n        throw new Error('Initialize failed: invalid dom.');\n      }\n    }\n    var existInstance = getInstanceByDom(dom);\n    if (existInstance) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('There is a chart instance already initialized on the dom.');\n      }\n      return existInstance;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isDom(dom) && dom.nodeName.toUpperCase() !== 'CANVAS' && (!dom.clientWidth && (!opts || opts.width == null) || !dom.clientHeight && (!opts || opts.height == null))) {\n        warn('Can\\'t get DOM width or height. Please check ' + 'dom.clientWidth and dom.clientHeight. They should not be 0.' + 'For example, you may need to call this in the callback ' + 'of window.onload.');\n      }\n    }\n  }\n  var chart = new ECharts(dom, theme, opts);\n  chart.id = 'ec_' + idBase++;\n  instances[chart.id] = chart;\n  isClient && modelUtil.setAttribute(dom, DOM_ATTRIBUTE_KEY, chart.id);\n  enableConnect(chart);\n  lifecycle.trigger('afterinit', chart);\n  return chart;\n}\n/**\n * @usage\n * (A)\n * ```js\n * let chart1 = echarts.init(dom1);\n * let chart2 = echarts.init(dom2);\n * chart1.group = 'xxx';\n * chart2.group = 'xxx';\n * echarts.connect('xxx');\n * ```\n * (B)\n * ```js\n * let chart1 = echarts.init(dom1);\n * let chart2 = echarts.init(dom2);\n * echarts.connect('xxx', [chart1, chart2]);\n * ```\n */\nexport function connect(groupId) {\n  // Is array of charts\n  if (isArray(groupId)) {\n    var charts = groupId;\n    groupId = null;\n    // If any chart has group\n    each(charts, function (chart) {\n      if (chart.group != null) {\n        groupId = chart.group;\n      }\n    });\n    groupId = groupId || 'g_' + groupIdBase++;\n    each(charts, function (chart) {\n      chart.group = groupId;\n    });\n  }\n  connectedGroups[groupId] = true;\n  return groupId;\n}\nexport function disconnect(groupId) {\n  connectedGroups[groupId] = false;\n}\n/**\n * Alias and backward compatibility\n * @deprecated\n */\nexport var disConnect = disconnect;\n/**\n * Dispose a chart instance\n */\nexport function dispose(chart) {\n  if (isString(chart)) {\n    chart = instances[chart];\n  } else if (!(chart instanceof ECharts)) {\n    // Try to treat as dom\n    chart = getInstanceByDom(chart);\n  }\n  if (chart instanceof ECharts && !chart.isDisposed()) {\n    chart.dispose();\n  }\n}\nexport function getInstanceByDom(dom) {\n  return instances[modelUtil.getAttribute(dom, DOM_ATTRIBUTE_KEY)];\n}\nexport function getInstanceById(key) {\n  return instances[key];\n}\n/**\n * Register theme\n */\nexport function registerTheme(name, theme) {\n  themeStorage[name] = theme;\n}\n/**\n * Register option preprocessor\n */\nexport function registerPreprocessor(preprocessorFunc) {\n  if (indexOf(optionPreprocessorFuncs, preprocessorFunc) < 0) {\n    optionPreprocessorFuncs.push(preprocessorFunc);\n  }\n}\nexport function registerProcessor(priority, processor) {\n  normalizeRegister(dataProcessorFuncs, priority, processor, PRIORITY_PROCESSOR_DEFAULT);\n}\n/**\n * Register postIniter\n * @param {Function} postInitFunc\n */\nexport function registerPostInit(postInitFunc) {\n  registerUpdateLifecycle('afterinit', postInitFunc);\n}\n/**\n * Register postUpdater\n * @param {Function} postUpdateFunc\n */\nexport function registerPostUpdate(postUpdateFunc) {\n  registerUpdateLifecycle('afterupdate', postUpdateFunc);\n}\nexport function registerUpdateLifecycle(name, cb) {\n  lifecycle.on(name, cb);\n}\nexport function registerAction(actionInfo, eventName, action) {\n  if (isFunction(eventName)) {\n    action = eventName;\n    eventName = '';\n  }\n  var actionType = isObject(actionInfo) ? actionInfo.type : [actionInfo, actionInfo = {\n    event: eventName\n  }][0];\n  // Event name is all lowercase\n  actionInfo.event = (actionInfo.event || actionType).toLowerCase();\n  eventName = actionInfo.event;\n  if (eventActionMap[eventName]) {\n    // Already registered.\n    return;\n  }\n  // Validate action type and event name.\n  assert(ACTION_REG.test(actionType) && ACTION_REG.test(eventName));\n  if (!actions[actionType]) {\n    actions[actionType] = {\n      action: action,\n      actionInfo: actionInfo\n    };\n  }\n  eventActionMap[eventName] = actionType;\n}\nexport function registerCoordinateSystem(type, coordSysCreator) {\n  CoordinateSystemManager.register(type, coordSysCreator);\n}\n/**\n * Get dimensions of specified coordinate system.\n * @param {string} type\n * @return {Array.<string|Object>}\n */\nexport function getCoordinateSystemDimensions(type) {\n  var coordSysCreator = CoordinateSystemManager.get(type);\n  if (coordSysCreator) {\n    return coordSysCreator.getDimensionsInfo ? coordSysCreator.getDimensionsInfo() : coordSysCreator.dimensions.slice();\n  }\n}\nexport { registerLocale } from './locale.js';\nfunction registerLayout(priority, layoutTask) {\n  normalizeRegister(visualFuncs, priority, layoutTask, PRIORITY_VISUAL_LAYOUT, 'layout');\n}\nfunction registerVisual(priority, visualTask) {\n  normalizeRegister(visualFuncs, priority, visualTask, PRIORITY_VISUAL_CHART, 'visual');\n}\nexport { registerLayout, registerVisual };\nvar registeredTasks = [];\nfunction normalizeRegister(targetList, priority, fn, defaultPriority, visualType) {\n  if (isFunction(priority) || isObject(priority)) {\n    fn = priority;\n    priority = defaultPriority;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (isNaN(priority) || priority == null) {\n      throw new Error('Illegal priority');\n    }\n    // Check duplicate\n    each(targetList, function (wrap) {\n      assert(wrap.__raw !== fn);\n    });\n  }\n  // Already registered\n  if (indexOf(registeredTasks, fn) >= 0) {\n    return;\n  }\n  registeredTasks.push(fn);\n  var stageHandler = Scheduler.wrapStageHandler(fn, visualType);\n  stageHandler.__prio = priority;\n  stageHandler.__raw = fn;\n  targetList.push(stageHandler);\n}\nexport function registerLoading(name, loadingFx) {\n  loadingEffects[name] = loadingFx;\n}\n/**\n * ZRender need a canvas context to do measureText.\n * But in node environment canvas may be created by node-canvas.\n * So we need to specify how to create a canvas instead of using document.createElement('canvas')\n *\n *\n * @deprecated use setPlatformAPI({ createCanvas }) instead.\n *\n * @example\n *     let Canvas = require('canvas');\n *     let echarts = require('echarts');\n *     echarts.setCanvasCreator(function () {\n *         // Small size is enough.\n *         return new Canvas(32, 32);\n *     });\n */\nexport function setCanvasCreator(creator) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateLog('setCanvasCreator is deprecated. Use setPlatformAPI({ createCanvas }) instead.');\n  }\n  setPlatformAPI({\n    createCanvas: creator\n  });\n}\n/**\n * The parameters and usage: see `geoSourceManager.registerMap`.\n * Compatible with previous `echarts.registerMap`.\n */\nexport function registerMap(mapName, geoJson, specialAreas) {\n  var registerMap = getImpl('registerMap');\n  registerMap && registerMap(mapName, geoJson, specialAreas);\n}\nexport function getMap(mapName) {\n  var getMap = getImpl('getMap');\n  return getMap && getMap(mapName);\n}\nexport var registerTransform = registerExternalTransform;\n/**\n * Globa dispatchAction to a specified chart instance.\n */\n// export function dispatchAction(payload: { chartId: string } & Payload, opt?: Parameters<ECharts['dispatchAction']>[1]) {\n//     if (!payload || !payload.chartId) {\n//         // Must have chartId to find chart\n//         return;\n//     }\n//     const chart = instances[payload.chartId];\n//     if (chart) {\n//         chart.dispatchAction(payload, opt);\n//     }\n// }\n// Builtin global visual\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataColorPaletteTask);\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesSymbolTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataSymbolTask);\nregisterVisual(PRIORITY_VISUAL_DECAL, decal);\nregisterPreprocessor(backwardCompat);\nregisterProcessor(PRIORITY_PROCESSOR_DATASTACK, dataStack);\nregisterLoading('default', loadingDefault);\n// Default actions\nregisterAction({\n  type: HIGHLIGHT_ACTION_TYPE,\n  event: HIGHLIGHT_ACTION_TYPE,\n  update: HIGHLIGHT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: DOWNPLAY_ACTION_TYPE,\n  event: DOWNPLAY_ACTION_TYPE,\n  update: DOWNPLAY_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: SELECT_ACTION_TYPE,\n  event: SELECT_ACTION_TYPE,\n  update: SELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: UNSELECT_ACTION_TYPE,\n  event: UNSELECT_ACTION_TYPE,\n  update: UNSELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: TOGGLE_SELECT_ACTION_TYPE,\n  event: TOGGLE_SELECT_ACTION_TYPE,\n  update: TOGGLE_SELECT_ACTION_TYPE\n}, noop);\n// Default theme\nregisterTheme('light', lightTheme);\nregisterTheme('dark', darkTheme);\n// For backward compatibility, where the namespace `dataTool` will\n// be mounted on `echarts` is the extension `dataTool` is imported.\nexport var dataTool = {};", "map": {"version": 3, "names": ["__extends", "zrender", "assert", "each", "isFunction", "isObject", "indexOf", "bind", "clone", "setAsPrimitive", "extend", "createHashMap", "map", "defaults", "isDom", "isArray", "noop", "isString", "retrieve2", "env", "timsort", "Eventful", "GlobalModel", "ExtensionAPI", "CoordinateSystemManager", "OptionManager", "backwardCompat", "dataStack", "SeriesModel", "ComponentView", "ChartView", "graphic", "getECData", "is<PERSON>ighD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HOVER_STATE_EMPHASIS", "HOVER_STATE_BLUR", "blurSeriesFromHighlightPayload", "toggleSelectionFromPayload", "updateSeriesElementSelection", "getAllSelectedIndices", "isSelectChangePayload", "isHighDownPayload", "HIGHLIGHT_ACTION_TYPE", "DOWNPLAY_ACTION_TYPE", "SELECT_ACTION_TYPE", "UNSELECT_ACTION_TYPE", "TOGGLE_SELECT_ACTION_TYPE", "savePathStates", "enterEmphasis", "leaveEmphasis", "leaveBlur", "enterSelect", "leaveSelect", "enterBlur", "allLeaveBlur", "findComponentHighDownDispatchers", "blurComponent", "handleGlobalMouseOverForHighDown", "handleGlobalMouseOutForHighDown", "modelUtil", "throttle", "seriesStyleTask", "dataStyleTask", "dataColorPaletteTask", "loadingDefault", "Scheduler", "lightTheme", "darkTheme", "parseClassType", "ECEventProcessor", "seriesSymbolTask", "dataSymbolTask", "getVisualFromData", "getItemVisualFromData", "deprecateLog", "deprecateReplaceLog", "error", "warn", "handleLegacySelectEvents", "registerExternalTransform", "createLocaleObject", "SYSTEM_LANG", "find<PERSON>vent<PERSON><PERSON><PERSON><PERSON><PERSON>", "decal", "lifecycle", "platformApi", "setPlatformAPI", "getImpl", "version", "dependencies", "TEST_FRAME_REMAIN_TIME", "PRIORITY_PROCESSOR_SERIES_FILTER", "PRIORITY_PROCESSOR_DATASTACK", "PRIORITY_PROCESSOR_FILTER", "PRIORITY_PROCESSOR_DEFAULT", "PRIORITY_PROCESSOR_STATISTIC", "PRIORITY_VISUAL_LAYOUT", "PRIORITY_VISUAL_PROGRESSIVE_LAYOUT", "PRIORITY_VISUAL_GLOBAL", "PRIORITY_VISUAL_CHART", "PRIORITY_VISUAL_COMPONENT", "PRIORITY_VISUAL_CHART_DATA_CUSTOM", "PRIORITY_VISUAL_POST_CHART_LAYOUT", "PRIORITY_VISUAL_BRUSH", "PRIORITY_VISUAL_ARIA", "PRIORITY_VISUAL_DECAL", "PRIORITY", "PROCESSOR", "FILTER", "SERIES_FILTER", "STATISTIC", "VISUAL", "LAYOUT", "PROGRESSIVE_LAYOUT", "GLOBAL", "CHART", "POST_CHART_LAYOUT", "COMPONENT", "BRUSH", "CHART_ITEM", "ARIA", "DECAL", "IN_MAIN_PROCESS_KEY", "PENDING_UPDATE", "STATUS_NEEDS_UPDATE_KEY", "ACTION_REG", "CONNECT_STATUS_KEY", "CONNECT_STATUS_PENDING", "CONNECT_STATUS_UPDATING", "CONNECT_STATUS_UPDATED", "createRegisterEventWithLowercaseECharts", "method", "args", "_i", "arguments", "length", "isDisposed", "disposedWarning", "id", "toLowercaseNameAndCallEventful", "createRegisterEventWithLowercaseMessageCenter", "host", "toLowerCase", "prototype", "apply", "MessageCenter", "_super", "messageCenterProto", "on", "off", "prepare", "<PERSON><PERSON><PERSON><PERSON>", "updateDirectly", "updateMethods", "doConvertPixel", "updateStreamModes", "doDispatchAction", "flushPendingActions", "triggerUpdatedEvent", "bindRenderedEvent", "bindMouseEvent", "render", "renderComponents", "renderSeries", "createExtensionAPI", "enableConnect", "markStatusToUpdate", "applyChangedStates", "<PERSON><PERSON><PERSON>", "dom", "theme", "opts", "_this", "call", "_chartsViews", "_chartsMap", "_componentsViews", "_componentsMap", "_pendingActions", "themeStorage", "_dom", "defaultRenderer", "defaultCoarsePointer", "defaultUseDirtyRect", "process", "NODE_ENV", "root", "hasGlobalWindow", "window", "global", "__ECHARTS__DEFAULT__RENDERER__", "__ECHARTS__DEFAULT__COARSE_POINTER", "__ECHARTS__DEFAULT__USE_DIRTY_RECT__", "ssr", "registerSSRDataGetter", "el", "ecData", "dataIndex", "hashMap", "set", "seriesIndex", "ssrType", "zr", "_zr", "init", "renderer", "devicePixelRatio", "width", "height", "useDirtyRect", "useCoarsePointer", "pointerSize", "_ssr", "_throttledZrFlush", "flush", "_theme", "_locale", "locale", "_coordSysMgr", "api", "_api", "prioritySortFunc", "a", "b", "__prio", "visualFuncs", "dataProcessorFuncs", "_scheduler", "_messageCenter", "_initEvents", "resize", "animation", "_onframe", "_disposed", "scheduler", "silent", "update", "updateParams", "e", "unfinished", "remainTime", "ecModel", "_model", "startTime", "Date", "performSeriesTasks", "performDataProcessorTasks", "performVisualTasks", "getDom", "getId", "getZr", "isSSR", "setOption", "option", "notMerge", "lazyUpdate", "replaceMerge", "transitionOpt", "transition", "optionManager", "optionPreprocessorFuncs", "seriesTransition", "optionChanged", "wakeUp", "setTheme", "getModel", "getOption", "getWidth", "getHeight", "getDevicePixelRatio", "painter", "dpr", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderToCanvas", "type", "Error", "backgroundColor", "get", "pixelRatio", "renderToSVGString", "renderToString", "useViewBox", "getSvgDataURL", "svgSupported", "list", "storage", "getDisplayList", "stopAnimation", "toDataURL", "getDataURL", "excludeComponents", "excludesComponentViews", "self", "componentType", "eachComponent", "mainType", "component", "view", "__viewId", "group", "ignore", "push", "url", "getType", "getConnectedDataURL", "isSvg", "groupId", "mathMin", "Math", "min", "mathMax", "max", "MAX_NUMBER", "Infinity", "connectedGroups", "left_1", "top_1", "right_1", "bottom_1", "canvasList_1", "dpr_1", "instances", "chart", "canvas", "getSvgDom", "innerHTML", "boundingRect", "getBoundingClientRect", "left", "top", "right", "bottom", "targetCanvas", "createCanvas", "zr_1", "content_1", "item", "x", "y", "getSvgRoot", "connectedBackgroundColor", "setBackgroundColor", "refreshImmediately", "add", "Rect", "shape", "style", "fill", "img", "Image", "image", "convertToPixel", "finder", "value", "convertFromPixel", "containPixel", "result", "findResult", "parseFinder", "models", "key", "model", "coordSys", "coordinateSystem", "containPoint", "getVisual", "visualType", "parsedFinder", "defaultMainType", "seriesModel", "data", "getData", "dataIndexInside", "hasOwnProperty", "indexOfRawIndex", "getViewOfComponentModel", "componentModel", "getViewOfSeriesModel", "MOUSE_EVENT_NAMES", "<PERSON><PERSON><PERSON>", "handler", "target", "params", "isGlobalOut", "parent", "dataModel", "getSeriesByIndex", "getDataParams", "dataType", "eventData", "componentIndex", "getComponent", "event", "_$eventProcessor", "eventInfo", "targetEl", "packedEvent", "trigger", "zrEventfulCallAtLast", "eventActionMap", "actionType", "eventType", "clear", "series", "dispose", "setAttribute", "DOM_ATTRIBUTE_KEY", "_loadingFX", "needPrepare", "resetOption", "duration", "showLoading", "name", "cfg", "hideLoading", "loadingEffects", "remove", "makeActionFromEvent", "eventObj", "payload", "dispatchAction", "opt", "actions", "browser", "weChat", "updateLabelLayout", "updatedSeries", "appendData", "internalField", "ecIns", "restorePipelines", "prepareStageTasks", "plan", "isComponent", "viewList", "viewMap", "i", "__alive", "doPrepare", "eachSeries", "<PERSON><PERSON>ew<PERSON>iew", "__require<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewId", "classType", "Clazz", "getClass", "main", "sub", "__id", "__model", "__ecComponentInfo", "index", "renderTask", "splice", "subType", "setUpdatePayload", "concat", "callView", "query", "condition", "excludeSeriesId", "excludeSeriesIdMap", "normalizeToArray", "modelId", "convertOptionIdName", "isExcluded", "notBlur", "_a", "focusSelf", "dispatchers", "dispatcher", "prepareAndUpdate", "newOption", "coordSysMgr", "restoreData", "create", "clearColorPalette", "darkMode", "setDarkMode", "updateTransform", "componentDirtyList", "componentView", "seriesDirtyMap", "chartView", "uid", "set<PERSON>irty", "dirtyMap", "updateView", "markUpdateMethod", "updateVisual", "clearAllVisual", "updateLayout", "methodName", "coordSysList", "getCoordinateSystems", "chartsMap", "payloadType", "escapeConnect", "actionWrap", "actionInfo", "cptTypeTmp", "split", "updateMethod", "pop", "cptType", "payloads", "batched", "batch", "eventObjBatch", "isSelectChange", "isHighDown", "batchItem", "action", "preParseFinder", "queryOptionMap", "mainTypeSpecified", "componentMainType", "keys", "messageCenter", "newObj", "selected", "isFromClick", "fromAction", "fromActionPayload", "pendingActions", "shift", "isFinished", "allocateZlevels", "componentZLevels", "seriesZLevels", "hasSeparateZLevel", "zlevel", "z", "zlevelKey", "getZLevelKey", "idx", "zLevels", "lastSeriesZLevel_1", "lastSeriesKey_1", "setZLevel", "dirtyList", "clearStates", "updateZ", "updateStates", "getSeries", "updatePayload", "dirty", "perform", "getPerformArgs", "updateBlend", "updateHoverLayerStatus", "traverse", "isElementRemoved", "applyElementStates", "newStates", "oldStates", "currentStates", "stateName", "states", "select", "hoverState", "emphasis", "blur", "useStates", "elCount", "isGroup", "node", "worker", "preventUsingHoverLayer", "eachRendered", "hoverLayer", "blendMode", "blend", "preventAutoZ", "doUpdateZ", "maxZ2", "label", "getTextContent", "labelLine", "getTextGuideLine", "children", "childrenRef", "z2", "isFinite", "textGuideLineConfig", "showAbove", "textContent", "textGuide", "stateTransition", "hasState", "prevStates", "stateAnimationModel", "enableAnimation", "isAnimationEnabled", "delay", "easing", "Path", "__dirty", "class_1", "getComponentByElement", "modelInfo", "highlightDigit", "updateConnectedChartsStatus", "charts", "status", "otherChart", "action_1", "otherCharts_1", "echartsProto", "one", "eventName", "cb", "ctx", "wrapped", "args2", "idBase", "groupIdBase", "isClient", "existInstance", "getInstanceByDom", "nodeName", "toUpperCase", "clientWidth", "clientHeight", "connect", "disconnect", "disConnect", "getAttribute", "getInstanceById", "registerTheme", "registerPreprocessor", "preprocessorFunc", "registerProcessor", "priority", "processor", "normalizeRegister", "registerPostInit", "postInitFunc", "registerUpdateLifecycle", "registerPostUpdate", "postUpdateFunc", "registerAction", "test", "registerCoordinateSystem", "coordSysCreator", "register", "getCoordinateSystemDimensions", "getDimensionsInfo", "dimensions", "slice", "registerLocale", "registerLayout", "layoutTask", "registerVisual", "visualTask", "registeredTasks", "targetList", "fn", "defaultPriority", "isNaN", "wrap", "__raw", "<PERSON><PERSON><PERSON><PERSON>", "wrapStageHandler", "registerLoading", "loadingFx", "setCanvasCreator", "creator", "registerMap", "mapName", "geoJson", "<PERSON><PERSON><PERSON><PERSON>", "getMap", "registerTransform", "dataTool"], "sources": ["F:/常规项目/adminweb/node_modules/echarts/lib/core/echarts.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrender from 'zrender/lib/zrender.js';\nimport { assert, each, isFunction, isObject, indexOf, bind, clone, setAsPrimitive, extend, createHashMap, map, defaults, isDom, isArray, noop, isString, retrieve2 } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport timsort from 'zrender/lib/core/timsort.js';\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport CoordinateSystemManager from './CoordinateSystem.js';\nimport OptionManager from '../model/OptionManager.js';\nimport backwardCompat from '../preprocessor/backwardCompat.js';\nimport dataStack from '../processor/dataStack.js';\nimport SeriesModel from '../model/Series.js';\nimport ComponentView from '../view/Component.js';\nimport ChartView from '../view/Chart.js';\nimport * as graphic from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { isHighDownDispatcher, HOVER_STATE_EMPHASIS, HOVER_STATE_BLUR, blurSeriesFromHighlightPayload, toggleSelectionFromPayload, updateSeriesElementSelection, getAllSelectedIndices, isSelectChangePayload, isHighDownPayload, HIGHLIGHT_ACTION_TYPE, DOWNPLAY_ACTION_TYPE, SELECT_ACTION_TYPE, UNSELECT_ACTION_TYPE, TOGGLE_SELECT_ACTION_TYPE, savePathStates, enterEmphasis, leaveEmphasis, leaveBlur, enterSelect, leaveSelect, enterBlur, allLeaveBlur, findComponentHighDownDispatchers, blurComponent, handleGlobalMouseOverForHighDown, handleGlobalMouseOutForHighDown } from '../util/states.js';\nimport * as modelUtil from '../util/model.js';\nimport { throttle } from '../util/throttle.js';\nimport { seriesStyleTask, dataStyleTask, dataColorPaletteTask } from '../visual/style.js';\nimport loadingDefault from '../loading/default.js';\nimport Scheduler from './Scheduler.js';\nimport lightTheme from '../theme/light.js';\nimport darkTheme from '../theme/dark.js';\nimport { parseClassType } from '../util/clazz.js';\nimport { ECEventProcessor } from '../util/ECEventProcessor.js';\nimport { seriesSymbolTask, dataSymbolTask } from '../visual/symbol.js';\nimport { getVisualFromData, getItemVisualFromData } from '../visual/helper.js';\nimport { deprecateLog, deprecateReplaceLog, error, warn } from '../util/log.js';\nimport { handleLegacySelectEvents } from '../legacy/dataSelectAction.js';\nimport { registerExternalTransform } from '../data/helper/transform.js';\nimport { createLocaleObject, SYSTEM_LANG } from './locale.js';\nimport { findEventDispatcher } from '../util/event.js';\nimport decal from '../visual/decal.js';\nimport lifecycle from './lifecycle.js';\nimport { platformApi, setPlatformAPI } from 'zrender/lib/core/platform.js';\nimport { getImpl } from './impl.js';\nexport var version = '5.5.1';\nexport var dependencies = {\n  zrender: '5.6.0'\n};\nvar TEST_FRAME_REMAIN_TIME = 1;\nvar PRIORITY_PROCESSOR_SERIES_FILTER = 800;\n// Some data processors depends on the stack result dimension (to calculate data extent).\n// So data stack stage should be in front of data processing stage.\nvar PRIORITY_PROCESSOR_DATASTACK = 900;\n// \"Data filter\" will block the stream, so it should be\n// put at the beginning of data processing.\nvar PRIORITY_PROCESSOR_FILTER = 1000;\nvar PRIORITY_PROCESSOR_DEFAULT = 2000;\nvar PRIORITY_PROCESSOR_STATISTIC = 5000;\nvar PRIORITY_VISUAL_LAYOUT = 1000;\nvar PRIORITY_VISUAL_PROGRESSIVE_LAYOUT = 1100;\nvar PRIORITY_VISUAL_GLOBAL = 2000;\nvar PRIORITY_VISUAL_CHART = 3000;\nvar PRIORITY_VISUAL_COMPONENT = 4000;\n// Visual property in data. Greater than `PRIORITY_VISUAL_COMPONENT` to enable to\n// overwrite the viusal result of component (like `visualMap`)\n// using data item specific setting (like itemStyle.xxx on data item)\nvar PRIORITY_VISUAL_CHART_DATA_CUSTOM = 4500;\n// Greater than `PRIORITY_VISUAL_CHART_DATA_CUSTOM` to enable to layout based on\n// visual result like `symbolSize`.\nvar PRIORITY_VISUAL_POST_CHART_LAYOUT = 4600;\nvar PRIORITY_VISUAL_BRUSH = 5000;\nvar PRIORITY_VISUAL_ARIA = 6000;\nvar PRIORITY_VISUAL_DECAL = 7000;\nexport var PRIORITY = {\n  PROCESSOR: {\n    FILTER: PRIORITY_PROCESSOR_FILTER,\n    SERIES_FILTER: PRIORITY_PROCESSOR_SERIES_FILTER,\n    STATISTIC: PRIORITY_PROCESSOR_STATISTIC\n  },\n  VISUAL: {\n    LAYOUT: PRIORITY_VISUAL_LAYOUT,\n    PROGRESSIVE_LAYOUT: PRIORITY_VISUAL_PROGRESSIVE_LAYOUT,\n    GLOBAL: PRIORITY_VISUAL_GLOBAL,\n    CHART: PRIORITY_VISUAL_CHART,\n    POST_CHART_LAYOUT: PRIORITY_VISUAL_POST_CHART_LAYOUT,\n    COMPONENT: PRIORITY_VISUAL_COMPONENT,\n    BRUSH: PRIORITY_VISUAL_BRUSH,\n    CHART_ITEM: PRIORITY_VISUAL_CHART_DATA_CUSTOM,\n    ARIA: PRIORITY_VISUAL_ARIA,\n    DECAL: PRIORITY_VISUAL_DECAL\n  }\n};\n// Main process have three entries: `setOption`, `dispatchAction` and `resize`,\n// where they must not be invoked nestedly, except the only case: invoke\n// dispatchAction with updateMethod \"none\" in main process.\n// This flag is used to carry out this rule.\n// All events will be triggered out side main process (i.e. when !this[IN_MAIN_PROCESS]).\nvar IN_MAIN_PROCESS_KEY = '__flagInMainProcess';\nvar PENDING_UPDATE = '__pendingUpdate';\nvar STATUS_NEEDS_UPDATE_KEY = '__needsUpdateStatus';\nvar ACTION_REG = /^[a-zA-Z0-9_]+$/;\nvar CONNECT_STATUS_KEY = '__connectUpdateStatus';\nvar CONNECT_STATUS_PENDING = 0;\nvar CONNECT_STATUS_UPDATING = 1;\nvar CONNECT_STATUS_UPDATED = 2;\n;\n;\nfunction createRegisterEventWithLowercaseECharts(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (this.isDisposed()) {\n      disposedWarning(this.id);\n      return;\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction createRegisterEventWithLowercaseMessageCenter(method) {\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return toLowercaseNameAndCallEventful(this, method, args);\n  };\n}\nfunction toLowercaseNameAndCallEventful(host, method, args) {\n  // `args[0]` is event name. Event name is all lowercase.\n  args[0] = args[0] && args[0].toLowerCase();\n  return Eventful.prototype[method].apply(host, args);\n}\nvar MessageCenter = /** @class */function (_super) {\n  __extends(MessageCenter, _super);\n  function MessageCenter() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return MessageCenter;\n}(Eventful);\nvar messageCenterProto = MessageCenter.prototype;\nmessageCenterProto.on = createRegisterEventWithLowercaseMessageCenter('on');\nmessageCenterProto.off = createRegisterEventWithLowercaseMessageCenter('off');\n// ---------------------------------------\n// Internal method names for class ECharts\n// ---------------------------------------\nvar prepare;\nvar prepareView;\nvar updateDirectly;\nvar updateMethods;\nvar doConvertPixel;\nvar updateStreamModes;\nvar doDispatchAction;\nvar flushPendingActions;\nvar triggerUpdatedEvent;\nvar bindRenderedEvent;\nvar bindMouseEvent;\nvar render;\nvar renderComponents;\nvar renderSeries;\nvar createExtensionAPI;\nvar enableConnect;\nvar markStatusToUpdate;\nvar applyChangedStates;\nvar ECharts = /** @class */function (_super) {\n  __extends(ECharts, _super);\n  function ECharts(dom,\n  // Theme name or themeOption.\n  theme, opts) {\n    var _this = _super.call(this, new ECEventProcessor()) || this;\n    _this._chartsViews = [];\n    _this._chartsMap = {};\n    _this._componentsViews = [];\n    _this._componentsMap = {};\n    // Can't dispatch action during rendering procedure\n    _this._pendingActions = [];\n    opts = opts || {};\n    // Get theme by name\n    if (isString(theme)) {\n      theme = themeStorage[theme];\n    }\n    _this._dom = dom;\n    var defaultRenderer = 'canvas';\n    var defaultCoarsePointer = 'auto';\n    var defaultUseDirtyRect = false;\n    if (process.env.NODE_ENV !== 'production') {\n      var root = /* eslint-disable-next-line */\n      env.hasGlobalWindow ? window : global;\n      if (root) {\n        defaultRenderer = retrieve2(root.__ECHARTS__DEFAULT__RENDERER__, defaultRenderer);\n        defaultCoarsePointer = retrieve2(root.__ECHARTS__DEFAULT__COARSE_POINTER, defaultCoarsePointer);\n        defaultUseDirtyRect = retrieve2(root.__ECHARTS__DEFAULT__USE_DIRTY_RECT__, defaultUseDirtyRect);\n      }\n    }\n    if (opts.ssr) {\n      zrender.registerSSRDataGetter(function (el) {\n        var ecData = getECData(el);\n        var dataIndex = ecData.dataIndex;\n        if (dataIndex == null) {\n          return;\n        }\n        var hashMap = createHashMap();\n        hashMap.set('series_index', ecData.seriesIndex);\n        hashMap.set('data_index', dataIndex);\n        ecData.ssrType && hashMap.set('ssr_type', ecData.ssrType);\n        return hashMap;\n      });\n    }\n    var zr = _this._zr = zrender.init(dom, {\n      renderer: opts.renderer || defaultRenderer,\n      devicePixelRatio: opts.devicePixelRatio,\n      width: opts.width,\n      height: opts.height,\n      ssr: opts.ssr,\n      useDirtyRect: retrieve2(opts.useDirtyRect, defaultUseDirtyRect),\n      useCoarsePointer: retrieve2(opts.useCoarsePointer, defaultCoarsePointer),\n      pointerSize: opts.pointerSize\n    });\n    _this._ssr = opts.ssr;\n    // Expect 60 fps.\n    _this._throttledZrFlush = throttle(bind(zr.flush, zr), 17);\n    theme = clone(theme);\n    theme && backwardCompat(theme, true);\n    _this._theme = theme;\n    _this._locale = createLocaleObject(opts.locale || SYSTEM_LANG);\n    _this._coordSysMgr = new CoordinateSystemManager();\n    var api = _this._api = createExtensionAPI(_this);\n    // Sort on demand\n    function prioritySortFunc(a, b) {\n      return a.__prio - b.__prio;\n    }\n    timsort(visualFuncs, prioritySortFunc);\n    timsort(dataProcessorFuncs, prioritySortFunc);\n    _this._scheduler = new Scheduler(_this, api, dataProcessorFuncs, visualFuncs);\n    _this._messageCenter = new MessageCenter();\n    // Init mouse events\n    _this._initEvents();\n    // In case some people write `window.onresize = chart.resize`\n    _this.resize = bind(_this.resize, _this);\n    zr.animation.on('frame', _this._onframe, _this);\n    bindRenderedEvent(zr, _this);\n    bindMouseEvent(zr, _this);\n    // ECharts instance can be used as value.\n    setAsPrimitive(_this);\n    return _this;\n  }\n  ECharts.prototype._onframe = function () {\n    if (this._disposed) {\n      return;\n    }\n    applyChangedStates(this);\n    var scheduler = this._scheduler;\n    // Lazy update\n    if (this[PENDING_UPDATE]) {\n      var silent = this[PENDING_UPDATE].silent;\n      this[IN_MAIN_PROCESS_KEY] = true;\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, this[PENDING_UPDATE].updateParams);\n      } catch (e) {\n        this[IN_MAIN_PROCESS_KEY] = false;\n        this[PENDING_UPDATE] = null;\n        throw e;\n      }\n      // At present, in each frame, zrender performs:\n      //   (1) animation step forward.\n      //   (2) trigger('frame') (where this `_onframe` is called)\n      //   (3) zrender flush (render).\n      // If we do nothing here, since we use `setToFinal: true`, the step (3) above\n      // will render the final state of the elements before the real animation started.\n      this._zr.flush();\n      this[IN_MAIN_PROCESS_KEY] = false;\n      this[PENDING_UPDATE] = null;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n    // Avoid do both lazy update and progress in one frame.\n    else if (scheduler.unfinished) {\n      // Stream progress.\n      var remainTime = TEST_FRAME_REMAIN_TIME;\n      var ecModel = this._model;\n      var api = this._api;\n      scheduler.unfinished = false;\n      do {\n        var startTime = +new Date();\n        scheduler.performSeriesTasks(ecModel);\n        // Currently dataProcessorFuncs do not check threshold.\n        scheduler.performDataProcessorTasks(ecModel);\n        updateStreamModes(this, ecModel);\n        // Do not update coordinate system here. Because that coord system update in\n        // each frame is not a good user experience. So we follow the rule that\n        // the extent of the coordinate system is determined in the first frame (the\n        // frame is executed immediately after task reset.\n        // this._coordSysMgr.update(ecModel, api);\n        // console.log('--- ec frame visual ---', remainTime);\n        scheduler.performVisualTasks(ecModel);\n        renderSeries(this, this._model, api, 'remain', {});\n        remainTime -= +new Date() - startTime;\n      } while (remainTime > 0 && scheduler.unfinished);\n      // Call flush explicitly for trigger finished event.\n      if (!scheduler.unfinished) {\n        this._zr.flush();\n      }\n      // Else, zr flushing be ensue within the same frame,\n      // because zr flushing is after onframe event.\n    }\n  };\n\n  ECharts.prototype.getDom = function () {\n    return this._dom;\n  };\n  ECharts.prototype.getId = function () {\n    return this.id;\n  };\n  ECharts.prototype.getZr = function () {\n    return this._zr;\n  };\n  ECharts.prototype.isSSR = function () {\n    return this._ssr;\n  };\n  /* eslint-disable-next-line */\n  ECharts.prototype.setOption = function (option, notMerge, lazyUpdate) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`setOption` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var silent;\n    var replaceMerge;\n    var transitionOpt;\n    if (isObject(notMerge)) {\n      lazyUpdate = notMerge.lazyUpdate;\n      silent = notMerge.silent;\n      replaceMerge = notMerge.replaceMerge;\n      transitionOpt = notMerge.transition;\n      notMerge = notMerge.notMerge;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    if (!this._model || notMerge) {\n      var optionManager = new OptionManager(this._api);\n      var theme = this._theme;\n      var ecModel = this._model = new GlobalModel();\n      ecModel.scheduler = this._scheduler;\n      ecModel.ssr = this._ssr;\n      ecModel.init(null, null, null, theme, this._locale, optionManager);\n    }\n    this._model.setOption(option, {\n      replaceMerge: replaceMerge\n    }, optionPreprocessorFuncs);\n    var updateParams = {\n      seriesTransition: transitionOpt,\n      optionChanged: true\n    };\n    if (lazyUpdate) {\n      this[PENDING_UPDATE] = {\n        silent: silent,\n        updateParams: updateParams\n      };\n      this[IN_MAIN_PROCESS_KEY] = false;\n      // `setOption(option, {lazyMode: true})` may be called when zrender has been slept.\n      // It should wake it up to make sure zrender start to render at the next frame.\n      this.getZr().wakeUp();\n    } else {\n      try {\n        prepare(this);\n        updateMethods.update.call(this, null, updateParams);\n      } catch (e) {\n        this[PENDING_UPDATE] = null;\n        this[IN_MAIN_PROCESS_KEY] = false;\n        throw e;\n      }\n      // Ensure zr refresh sychronously, and then pixel in canvas can be\n      // fetched after `setOption`.\n      if (!this._ssr) {\n        // not use flush when using ssr mode.\n        this._zr.flush();\n      }\n      this[PENDING_UPDATE] = null;\n      this[IN_MAIN_PROCESS_KEY] = false;\n      flushPendingActions.call(this, silent);\n      triggerUpdatedEvent.call(this, silent);\n    }\n  };\n  /**\n   * @deprecated\n   */\n  ECharts.prototype.setTheme = function () {\n    deprecateLog('ECharts#setTheme() is DEPRECATED in ECharts 3.0');\n  };\n  // We don't want developers to use getModel directly.\n  ECharts.prototype.getModel = function () {\n    return this._model;\n  };\n  ECharts.prototype.getOption = function () {\n    return this._model && this._model.getOption();\n  };\n  ECharts.prototype.getWidth = function () {\n    return this._zr.getWidth();\n  };\n  ECharts.prototype.getHeight = function () {\n    return this._zr.getHeight();\n  };\n  ECharts.prototype.getDevicePixelRatio = function () {\n    return this._zr.painter.dpr\n    /* eslint-disable-next-line */ || env.hasGlobalWindow && window.devicePixelRatio || 1;\n  };\n  /**\n   * Get canvas which has all thing rendered\n   * @deprecated Use renderToCanvas instead.\n   */\n  ECharts.prototype.getRenderedCanvas = function (opts) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('getRenderedCanvas', 'renderToCanvas');\n    }\n    return this.renderToCanvas(opts);\n  };\n  ECharts.prototype.renderToCanvas = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'canvas') {\n        throw new Error('renderToCanvas can only be used in the canvas renderer.');\n      }\n    }\n    return painter.getRenderedCanvas({\n      backgroundColor: opts.backgroundColor || this._model.get('backgroundColor'),\n      pixelRatio: opts.pixelRatio || this.getDevicePixelRatio()\n    });\n  };\n  ECharts.prototype.renderToSVGString = function (opts) {\n    opts = opts || {};\n    var painter = this._zr.painter;\n    if (process.env.NODE_ENV !== 'production') {\n      if (painter.type !== 'svg') {\n        throw new Error('renderToSVGString can only be used in the svg renderer.');\n      }\n    }\n    return painter.renderToString({\n      useViewBox: opts.useViewBox\n    });\n  };\n  /**\n   * Get svg data url\n   */\n  ECharts.prototype.getSvgDataURL = function () {\n    if (!env.svgSupported) {\n      return;\n    }\n    var zr = this._zr;\n    var list = zr.storage.getDisplayList();\n    // Stop animations\n    each(list, function (el) {\n      el.stopAnimation(null, true);\n    });\n    return zr.painter.toDataURL();\n  };\n  ECharts.prototype.getDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    opts = opts || {};\n    var excludeComponents = opts.excludeComponents;\n    var ecModel = this._model;\n    var excludesComponentViews = [];\n    var self = this;\n    each(excludeComponents, function (componentType) {\n      ecModel.eachComponent({\n        mainType: componentType\n      }, function (component) {\n        var view = self._componentsMap[component.__viewId];\n        if (!view.group.ignore) {\n          excludesComponentViews.push(view);\n          view.group.ignore = true;\n        }\n      });\n    });\n    var url = this._zr.painter.getType() === 'svg' ? this.getSvgDataURL() : this.renderToCanvas(opts).toDataURL('image/' + (opts && opts.type || 'png'));\n    each(excludesComponentViews, function (view) {\n      view.group.ignore = false;\n    });\n    return url;\n  };\n  ECharts.prototype.getConnectedDataURL = function (opts) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var isSvg = opts.type === 'svg';\n    var groupId = this.group;\n    var mathMin = Math.min;\n    var mathMax = Math.max;\n    var MAX_NUMBER = Infinity;\n    if (connectedGroups[groupId]) {\n      var left_1 = MAX_NUMBER;\n      var top_1 = MAX_NUMBER;\n      var right_1 = -MAX_NUMBER;\n      var bottom_1 = -MAX_NUMBER;\n      var canvasList_1 = [];\n      var dpr_1 = opts && opts.pixelRatio || this.getDevicePixelRatio();\n      each(instances, function (chart, id) {\n        if (chart.group === groupId) {\n          var canvas = isSvg ? chart.getZr().painter.getSvgDom().innerHTML : chart.renderToCanvas(clone(opts));\n          var boundingRect = chart.getDom().getBoundingClientRect();\n          left_1 = mathMin(boundingRect.left, left_1);\n          top_1 = mathMin(boundingRect.top, top_1);\n          right_1 = mathMax(boundingRect.right, right_1);\n          bottom_1 = mathMax(boundingRect.bottom, bottom_1);\n          canvasList_1.push({\n            dom: canvas,\n            left: boundingRect.left,\n            top: boundingRect.top\n          });\n        }\n      });\n      left_1 *= dpr_1;\n      top_1 *= dpr_1;\n      right_1 *= dpr_1;\n      bottom_1 *= dpr_1;\n      var width = right_1 - left_1;\n      var height = bottom_1 - top_1;\n      var targetCanvas = platformApi.createCanvas();\n      var zr_1 = zrender.init(targetCanvas, {\n        renderer: isSvg ? 'svg' : 'canvas'\n      });\n      zr_1.resize({\n        width: width,\n        height: height\n      });\n      if (isSvg) {\n        var content_1 = '';\n        each(canvasList_1, function (item) {\n          var x = item.left - left_1;\n          var y = item.top - top_1;\n          content_1 += '<g transform=\"translate(' + x + ',' + y + ')\">' + item.dom + '</g>';\n        });\n        zr_1.painter.getSvgRoot().innerHTML = content_1;\n        if (opts.connectedBackgroundColor) {\n          zr_1.painter.setBackgroundColor(opts.connectedBackgroundColor);\n        }\n        zr_1.refreshImmediately();\n        return zr_1.painter.toDataURL();\n      } else {\n        // Background between the charts\n        if (opts.connectedBackgroundColor) {\n          zr_1.add(new graphic.Rect({\n            shape: {\n              x: 0,\n              y: 0,\n              width: width,\n              height: height\n            },\n            style: {\n              fill: opts.connectedBackgroundColor\n            }\n          }));\n        }\n        each(canvasList_1, function (item) {\n          var img = new graphic.Image({\n            style: {\n              x: item.left * dpr_1 - left_1,\n              y: item.top * dpr_1 - top_1,\n              image: item.dom\n            }\n          });\n          zr_1.add(img);\n        });\n        zr_1.refreshImmediately();\n        return targetCanvas.toDataURL('image/' + (opts && opts.type || 'png'));\n      }\n    } else {\n      return this.getDataURL(opts);\n    }\n  };\n  ECharts.prototype.convertToPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertToPixel', finder, value);\n  };\n  ECharts.prototype.convertFromPixel = function (finder, value) {\n    return doConvertPixel(this, 'convertFromPixel', finder, value);\n  };\n  /**\n   * Is the specified coordinate systems or components contain the given pixel point.\n   * @param {Array|number} value\n   * @return {boolean} result\n   */\n  ECharts.prototype.containPixel = function (finder, value) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var ecModel = this._model;\n    var result;\n    var findResult = modelUtil.parseFinder(ecModel, finder);\n    each(findResult, function (models, key) {\n      key.indexOf('Models') >= 0 && each(models, function (model) {\n        var coordSys = model.coordinateSystem;\n        if (coordSys && coordSys.containPoint) {\n          result = result || !!coordSys.containPoint(value);\n        } else if (key === 'seriesModels') {\n          var view = this._chartsMap[model.__viewId];\n          if (view && view.containPoint) {\n            result = result || view.containPoint(value, model);\n          } else {\n            if (process.env.NODE_ENV !== 'production') {\n              warn(key + ': ' + (view ? 'The found component do not support containPoint.' : 'No view mapping to the found component.'));\n            }\n          }\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            warn(key + ': containPoint is not supported');\n          }\n        }\n      }, this);\n    }, this);\n    return !!result;\n  };\n  /**\n   * Get visual from series or data.\n   * @param finder\n   *        If string, e.g., 'series', means {seriesIndex: 0}.\n   *        If Object, could contain some of these properties below:\n   *        {\n   *            seriesIndex / seriesId / seriesName,\n   *            dataIndex / dataIndexInside\n   *        }\n   *        If dataIndex is not specified, series visual will be fetched,\n   *        but not data item visual.\n   *        If all of seriesIndex, seriesId, seriesName are not specified,\n   *        visual will be fetched from first series.\n   * @param visualType 'color', 'symbol', 'symbolSize'\n   */\n  ECharts.prototype.getVisual = function (finder, visualType) {\n    var ecModel = this._model;\n    var parsedFinder = modelUtil.parseFinder(ecModel, finder, {\n      defaultMainType: 'series'\n    });\n    var seriesModel = parsedFinder.seriesModel;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!seriesModel) {\n        warn('There is no specified series model');\n      }\n    }\n    var data = seriesModel.getData();\n    var dataIndexInside = parsedFinder.hasOwnProperty('dataIndexInside') ? parsedFinder.dataIndexInside : parsedFinder.hasOwnProperty('dataIndex') ? data.indexOfRawIndex(parsedFinder.dataIndex) : null;\n    return dataIndexInside != null ? getItemVisualFromData(data, dataIndexInside, visualType) : getVisualFromData(data, visualType);\n  };\n  /**\n   * Get view of corresponding component model\n   */\n  ECharts.prototype.getViewOfComponentModel = function (componentModel) {\n    return this._componentsMap[componentModel.__viewId];\n  };\n  /**\n   * Get view of corresponding series model\n   */\n  ECharts.prototype.getViewOfSeriesModel = function (seriesModel) {\n    return this._chartsMap[seriesModel.__viewId];\n  };\n  ECharts.prototype._initEvents = function () {\n    var _this = this;\n    each(MOUSE_EVENT_NAMES, function (eveName) {\n      var handler = function (e) {\n        var ecModel = _this.getModel();\n        var el = e.target;\n        var params;\n        var isGlobalOut = eveName === 'globalout';\n        // no e.target when 'globalout'.\n        if (isGlobalOut) {\n          params = {};\n        } else {\n          el && findEventDispatcher(el, function (parent) {\n            var ecData = getECData(parent);\n            if (ecData && ecData.dataIndex != null) {\n              var dataModel = ecData.dataModel || ecModel.getSeriesByIndex(ecData.seriesIndex);\n              params = dataModel && dataModel.getDataParams(ecData.dataIndex, ecData.dataType, el) || {};\n              return true;\n            }\n            // If element has custom eventData of components\n            else if (ecData.eventData) {\n              params = extend({}, ecData.eventData);\n              return true;\n            }\n          }, true);\n        }\n        // Contract: if params prepared in mouse event,\n        // these properties must be specified:\n        // {\n        //    componentType: string (component main type)\n        //    componentIndex: number\n        // }\n        // Otherwise event query can not work.\n        if (params) {\n          var componentType = params.componentType;\n          var componentIndex = params.componentIndex;\n          // Special handling for historic reason: when trigger by\n          // markLine/markPoint/markArea, the componentType is\n          // 'markLine'/'markPoint'/'markArea', but we should better\n          // enable them to be queried by seriesIndex, since their\n          // option is set in each series.\n          if (componentType === 'markLine' || componentType === 'markPoint' || componentType === 'markArea') {\n            componentType = 'series';\n            componentIndex = params.seriesIndex;\n          }\n          var model = componentType && componentIndex != null && ecModel.getComponent(componentType, componentIndex);\n          var view = model && _this[model.mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId];\n          if (process.env.NODE_ENV !== 'production') {\n            // `event.componentType` and `event[componentTpype + 'Index']` must not\n            // be missed, otherwise there is no way to distinguish source component.\n            // See `dataFormat.getDataParams`.\n            if (!isGlobalOut && !(model && view)) {\n              warn('model or view can not be found by params');\n            }\n          }\n          params.event = e;\n          params.type = eveName;\n          _this._$eventProcessor.eventInfo = {\n            targetEl: el,\n            packedEvent: params,\n            model: model,\n            view: view\n          };\n          _this.trigger(eveName, params);\n        }\n      };\n      // Consider that some component (like tooltip, brush, ...)\n      // register zr event handler, but user event handler might\n      // do anything, such as call `setOption` or `dispatchAction`,\n      // which probably update any of the content and probably\n      // cause problem if it is called previous other inner handlers.\n      handler.zrEventfulCallAtLast = true;\n      _this._zr.on(eveName, handler, _this);\n    });\n    each(eventActionMap, function (actionType, eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    // Extra events\n    // TODO register?\n    each(['selectchanged'], function (eventType) {\n      _this._messageCenter.on(eventType, function (event) {\n        this.trigger(eventType, event);\n      }, _this);\n    });\n    handleLegacySelectEvents(this._messageCenter, this, this._api);\n  };\n  ECharts.prototype.isDisposed = function () {\n    return this._disposed;\n  };\n  ECharts.prototype.clear = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this.setOption({\n      series: []\n    }, true);\n  };\n  ECharts.prototype.dispose = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._disposed = true;\n    var dom = this.getDom();\n    if (dom) {\n      modelUtil.setAttribute(this.getDom(), DOM_ATTRIBUTE_KEY, '');\n    }\n    var chart = this;\n    var api = chart._api;\n    var ecModel = chart._model;\n    each(chart._componentsViews, function (component) {\n      component.dispose(ecModel, api);\n    });\n    each(chart._chartsViews, function (chart) {\n      chart.dispose(ecModel, api);\n    });\n    // Dispose after all views disposed\n    chart._zr.dispose();\n    // Set properties to null.\n    // To reduce the memory cost in case the top code still holds this instance unexpectedly.\n    chart._dom = chart._model = chart._chartsMap = chart._componentsMap = chart._chartsViews = chart._componentsViews = chart._scheduler = chart._api = chart._zr = chart._throttledZrFlush = chart._theme = chart._coordSysMgr = chart._messageCenter = null;\n    delete instances[chart.id];\n  };\n  /**\n   * Resize the chart\n   */\n  ECharts.prototype.resize = function (opts) {\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('`resize` should not be called during main process.');\n      }\n      return;\n    }\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._zr.resize(opts);\n    var ecModel = this._model;\n    // Resize loading effect\n    this._loadingFX && this._loadingFX.resize();\n    if (!ecModel) {\n      return;\n    }\n    var needPrepare = ecModel.resetOption('media');\n    var silent = opts && opts.silent;\n    // There is some real cases that:\n    // chart.setOption(option, { lazyUpdate: true });\n    // chart.resize();\n    if (this[PENDING_UPDATE]) {\n      if (silent == null) {\n        silent = this[PENDING_UPDATE].silent;\n      }\n      needPrepare = true;\n      this[PENDING_UPDATE] = null;\n    }\n    this[IN_MAIN_PROCESS_KEY] = true;\n    try {\n      needPrepare && prepare(this);\n      updateMethods.update.call(this, {\n        type: 'resize',\n        animation: extend({\n          // Disable animation\n          duration: 0\n        }, opts && opts.animation)\n      });\n    } catch (e) {\n      this[IN_MAIN_PROCESS_KEY] = false;\n      throw e;\n    }\n    this[IN_MAIN_PROCESS_KEY] = false;\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.showLoading = function (name, cfg) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (isObject(name)) {\n      cfg = name;\n      name = '';\n    }\n    name = name || 'default';\n    this.hideLoading();\n    if (!loadingEffects[name]) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Loading effects ' + name + ' not exists.');\n      }\n      return;\n    }\n    var el = loadingEffects[name](this._api, cfg);\n    var zr = this._zr;\n    this._loadingFX = el;\n    zr.add(el);\n  };\n  /**\n   * Hide loading effect\n   */\n  ECharts.prototype.hideLoading = function () {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    this._loadingFX && this._zr.remove(this._loadingFX);\n    this._loadingFX = null;\n  };\n  ECharts.prototype.makeActionFromEvent = function (eventObj) {\n    var payload = extend({}, eventObj);\n    payload.type = eventActionMap[eventObj.type];\n    return payload;\n  };\n  /**\n   * @param opt If pass boolean, means opt.silent\n   * @param opt.silent Default `false`. Whether trigger events.\n   * @param opt.flush Default `undefined`.\n   *        true: Flush immediately, and then pixel in canvas can be fetched\n   *            immediately. Caution: it might affect performance.\n   *        false: Not flush.\n   *        undefined: Auto decide whether perform flush.\n   */\n  ECharts.prototype.dispatchAction = function (payload, opt) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    if (!isObject(opt)) {\n      opt = {\n        silent: !!opt\n      };\n    }\n    if (!actions[payload.type]) {\n      return;\n    }\n    // Avoid dispatch action before setOption. Especially in `connect`.\n    if (!this._model) {\n      return;\n    }\n    // May dispatchAction in rendering procedure\n    if (this[IN_MAIN_PROCESS_KEY]) {\n      this._pendingActions.push(payload);\n      return;\n    }\n    var silent = opt.silent;\n    doDispatchAction.call(this, payload, silent);\n    var flush = opt.flush;\n    if (flush) {\n      this._zr.flush();\n    } else if (flush !== false && env.browser.weChat) {\n      // In WeChat embedded browser, `requestAnimationFrame` and `setInterval`\n      // hang when sliding page (on touch event), which cause that zr does not\n      // refresh until user interaction finished, which is not expected.\n      // But `dispatchAction` may be called too frequently when pan on touch\n      // screen, which impacts performance if do not throttle them.\n      this._throttledZrFlush();\n    }\n    flushPendingActions.call(this, silent);\n    triggerUpdatedEvent.call(this, silent);\n  };\n  ECharts.prototype.updateLabelLayout = function () {\n    lifecycle.trigger('series:layoutlabels', this._model, this._api, {\n      // Not adding series labels.\n      // TODO\n      updatedSeries: []\n    });\n  };\n  ECharts.prototype.appendData = function (params) {\n    if (this._disposed) {\n      disposedWarning(this.id);\n      return;\n    }\n    var seriesIndex = params.seriesIndex;\n    var ecModel = this.getModel();\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (process.env.NODE_ENV !== 'production') {\n      assert(params.data && seriesModel);\n    }\n    seriesModel.appendData(params);\n    // Note: `appendData` does not support that update extent of coordinate\n    // system, util some scenario require that. In the expected usage of\n    // `appendData`, the initial extent of coordinate system should better\n    // be fixed by axis `min`/`max` setting or initial data, otherwise if\n    // the extent changed while `appendData`, the location of the painted\n    // graphic elements have to be changed, which make the usage of\n    // `appendData` meaningless.\n    this._scheduler.unfinished = true;\n    this.getZr().wakeUp();\n  };\n  // A work around for no `internal` modifier in ts yet but\n  // need to strictly hide private methods to JS users.\n  ECharts.internalField = function () {\n    prepare = function (ecIns) {\n      var scheduler = ecIns._scheduler;\n      scheduler.restorePipelines(ecIns._model);\n      scheduler.prepareStageTasks();\n      prepareView(ecIns, true);\n      prepareView(ecIns, false);\n      scheduler.plan();\n    };\n    /**\n     * Prepare view instances of charts and components\n     */\n    prepareView = function (ecIns, isComponent) {\n      var ecModel = ecIns._model;\n      var scheduler = ecIns._scheduler;\n      var viewList = isComponent ? ecIns._componentsViews : ecIns._chartsViews;\n      var viewMap = isComponent ? ecIns._componentsMap : ecIns._chartsMap;\n      var zr = ecIns._zr;\n      var api = ecIns._api;\n      for (var i = 0; i < viewList.length; i++) {\n        viewList[i].__alive = false;\n      }\n      isComponent ? ecModel.eachComponent(function (componentType, model) {\n        componentType !== 'series' && doPrepare(model);\n      }) : ecModel.eachSeries(doPrepare);\n      function doPrepare(model) {\n        // By default view will be reused if possible for the case that `setOption` with \"notMerge\"\n        // mode and need to enable transition animation. (Usually, when they have the same id, or\n        // especially no id but have the same type & name & index. See the `model.id` generation\n        // rule in `makeIdAndName` and `viewId` generation rule here).\n        // But in `replaceMerge` mode, this feature should be able to disabled when it is clear that\n        // the new model has nothing to do with the old model.\n        var requireNewView = model.__requireNewView;\n        // This command should not work twice.\n        model.__requireNewView = false;\n        // Consider: id same and type changed.\n        var viewId = '_ec_' + model.id + '_' + model.type;\n        var view = !requireNewView && viewMap[viewId];\n        if (!view) {\n          var classType = parseClassType(model.type);\n          var Clazz = isComponent ? ComponentView.getClass(classType.main, classType.sub) :\n          // FIXME:TS\n          // (ChartView as ChartViewConstructor).getClass('series', classType.sub)\n          // For backward compat, still support a chart type declared as only subType\n          // like \"liquidfill\", but recommend \"series.liquidfill\"\n          // But need a base class to make a type series.\n          ChartView.getClass(classType.sub);\n          if (process.env.NODE_ENV !== 'production') {\n            assert(Clazz, classType.sub + ' does not exist.');\n          }\n          view = new Clazz();\n          view.init(ecModel, api);\n          viewMap[viewId] = view;\n          viewList.push(view);\n          zr.add(view.group);\n        }\n        model.__viewId = view.__id = viewId;\n        view.__alive = true;\n        view.__model = model;\n        view.group.__ecComponentInfo = {\n          mainType: model.mainType,\n          index: model.componentIndex\n        };\n        !isComponent && scheduler.prepareView(view, model, ecModel, api);\n      }\n      for (var i = 0; i < viewList.length;) {\n        var view = viewList[i];\n        if (!view.__alive) {\n          !isComponent && view.renderTask.dispose();\n          zr.remove(view.group);\n          view.dispose(ecModel, api);\n          viewList.splice(i, 1);\n          if (viewMap[view.__id] === view) {\n            delete viewMap[view.__id];\n          }\n          view.__id = view.group.__ecComponentInfo = null;\n        } else {\n          i++;\n        }\n      }\n    };\n    updateDirectly = function (ecIns, method, payload, mainType, subType) {\n      var ecModel = ecIns._model;\n      ecModel.setUpdatePayload(payload);\n      // broadcast\n      if (!mainType) {\n        // FIXME\n        // Chart will not be update directly here, except set dirty.\n        // But there is no such scenario now.\n        each([].concat(ecIns._componentsViews).concat(ecIns._chartsViews), callView);\n        return;\n      }\n      var query = {};\n      query[mainType + 'Id'] = payload[mainType + 'Id'];\n      query[mainType + 'Index'] = payload[mainType + 'Index'];\n      query[mainType + 'Name'] = payload[mainType + 'Name'];\n      var condition = {\n        mainType: mainType,\n        query: query\n      };\n      subType && (condition.subType = subType); // subType may be '' by parseClassType;\n      var excludeSeriesId = payload.excludeSeriesId;\n      var excludeSeriesIdMap;\n      if (excludeSeriesId != null) {\n        excludeSeriesIdMap = createHashMap();\n        each(modelUtil.normalizeToArray(excludeSeriesId), function (id) {\n          var modelId = modelUtil.convertOptionIdName(id, null);\n          if (modelId != null) {\n            excludeSeriesIdMap.set(modelId, true);\n          }\n        });\n      }\n      // If dispatchAction before setOption, do nothing.\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        if (isHighDownPayload(payload)) {\n          if (model instanceof SeriesModel) {\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && !payload.notBlur && !model.get(['emphasis', 'disabled'])) {\n              blurSeriesFromHighlightPayload(model, payload, ecIns._api);\n            }\n          } else {\n            var _a = findComponentHighDownDispatchers(model.mainType, model.componentIndex, payload.name, ecIns._api),\n              focusSelf = _a.focusSelf,\n              dispatchers = _a.dispatchers;\n            if (payload.type === HIGHLIGHT_ACTION_TYPE && focusSelf && !payload.notBlur) {\n              blurComponent(model.mainType, model.componentIndex, ecIns._api);\n            }\n            // PENDING:\n            // Whether to put this \"enter emphasis\" code in `ComponentView`,\n            // which will be the same as `ChartView` but might be not necessary\n            // and will be far from this logic.\n            if (dispatchers) {\n              each(dispatchers, function (dispatcher) {\n                payload.type === HIGHLIGHT_ACTION_TYPE ? enterEmphasis(dispatcher) : leaveEmphasis(dispatcher);\n              });\n            }\n          }\n        } else if (isSelectChangePayload(payload)) {\n          // TODO geo\n          if (model instanceof SeriesModel) {\n            toggleSelectionFromPayload(model, payload, ecIns._api);\n            updateSeriesElementSelection(model);\n            markStatusToUpdate(ecIns);\n          }\n        }\n      }, ecIns);\n      ecModel && ecModel.eachComponent(condition, function (model) {\n        var isExcluded = excludeSeriesIdMap && excludeSeriesIdMap.get(model.id) != null;\n        if (isExcluded) {\n          return;\n        }\n        ;\n        callView(ecIns[mainType === 'series' ? '_chartsMap' : '_componentsMap'][model.__viewId]);\n      }, ecIns);\n      function callView(view) {\n        view && view.__alive && view[method] && view[method](view.__model, ecModel, ecIns._api, payload);\n      }\n    };\n    updateMethods = {\n      prepareAndUpdate: function (payload) {\n        prepare(this);\n        updateMethods.update.call(this, payload, {\n          // Needs to mark option changed if newOption is given.\n          // It's from MagicType.\n          // TODO If use a separate flag optionChanged in payload?\n          optionChanged: payload.newOption != null\n        });\n      },\n      update: function (payload, updateParams) {\n        var ecModel = this._model;\n        var api = this._api;\n        var zr = this._zr;\n        var coordSysMgr = this._coordSysMgr;\n        var scheduler = this._scheduler;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        scheduler.restoreData(ecModel, payload);\n        scheduler.performSeriesTasks(ecModel);\n        // TODO\n        // Save total ecModel here for undo/redo (after restoring data and before processing data).\n        // Undo (restoration of total ecModel) can be carried out in 'action' or outside API call.\n        // Create new coordinate system each update\n        // In LineView may save the old coordinate system and use it to get the original point.\n        coordSysMgr.create(ecModel, api);\n        scheduler.performDataProcessorTasks(ecModel, payload);\n        // Current stream render is not supported in data process. So we can update\n        // stream modes after data processing, where the filtered data is used to\n        // determine whether to use progressive rendering.\n        updateStreamModes(this, ecModel);\n        // We update stream modes before coordinate system updated, then the modes info\n        // can be fetched when coord sys updating (consider the barGrid extent fix). But\n        // the drawback is the full coord info can not be fetched. Fortunately this full\n        // coord is not required in stream mode updater currently.\n        coordSysMgr.update(ecModel, api);\n        clearColorPalette(ecModel);\n        scheduler.performVisualTasks(ecModel, payload);\n        render(this, ecModel, api, payload, updateParams);\n        // Set background\n        var backgroundColor = ecModel.get('backgroundColor') || 'transparent';\n        var darkMode = ecModel.get('darkMode');\n        zr.setBackgroundColor(backgroundColor);\n        // Force set dark mode.\n        if (darkMode != null && darkMode !== 'auto') {\n          zr.setDarkMode(darkMode);\n        }\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateTransform: function (payload) {\n        var _this = this;\n        var ecModel = this._model;\n        var api = this._api;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // ChartView.markUpdateMethod(payload, 'updateTransform');\n        var componentDirtyList = [];\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType === 'series') {\n            return;\n          }\n          var componentView = _this.getViewOfComponentModel(componentModel);\n          if (componentView && componentView.__alive) {\n            if (componentView.updateTransform) {\n              var result = componentView.updateTransform(componentModel, ecModel, api, payload);\n              result && result.update && componentDirtyList.push(componentView);\n            } else {\n              componentDirtyList.push(componentView);\n            }\n          }\n        });\n        var seriesDirtyMap = createHashMap();\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          if (chartView.updateTransform) {\n            var result = chartView.updateTransform(seriesModel, ecModel, api, payload);\n            result && result.update && seriesDirtyMap.set(seriesModel.uid, 1);\n          } else {\n            seriesDirtyMap.set(seriesModel.uid, 1);\n          }\n        });\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        // this._scheduler.performVisualTasks(ecModel, payload, 'layout', true);\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true,\n          dirtyMap: seriesDirtyMap\n        });\n        // Currently, not call render of components. Geo render cost a lot.\n        // renderComponents(ecIns, ecModel, api, payload, componentDirtyList);\n        renderSeries(this, ecModel, api, payload, {}, seriesDirtyMap);\n        lifecycle.trigger('afterupdate', ecModel, api);\n      },\n      updateView: function (payload) {\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        ChartView.markUpdateMethod(payload, 'updateView');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          setDirty: true\n        });\n        render(this, ecModel, this._api, payload, {});\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateVisual: function (payload) {\n        // updateMethods.update.call(this, payload);\n        var _this = this;\n        var ecModel = this._model;\n        // update before setOption\n        if (!ecModel) {\n          return;\n        }\n        ecModel.setUpdatePayload(payload);\n        // clear all visual\n        ecModel.eachSeries(function (seriesModel) {\n          seriesModel.getData().clearAllVisual();\n        });\n        // Perform visual\n        ChartView.markUpdateMethod(payload, 'updateVisual');\n        clearColorPalette(ecModel);\n        // Keep pipe to the exist pipeline because it depends on the render task of the full pipeline.\n        this._scheduler.performVisualTasks(ecModel, payload, {\n          visualType: 'visual',\n          setDirty: true\n        });\n        ecModel.eachComponent(function (componentType, componentModel) {\n          if (componentType !== 'series') {\n            var componentView = _this.getViewOfComponentModel(componentModel);\n            componentView && componentView.__alive && componentView.updateVisual(componentModel, ecModel, _this._api, payload);\n          }\n        });\n        ecModel.eachSeries(function (seriesModel) {\n          var chartView = _this._chartsMap[seriesModel.__viewId];\n          chartView.updateVisual(seriesModel, ecModel, _this._api, payload);\n        });\n        lifecycle.trigger('afterupdate', ecModel, this._api);\n      },\n      updateLayout: function (payload) {\n        updateMethods.update.call(this, payload);\n      }\n    };\n    doConvertPixel = function (ecIns, methodName, finder, value) {\n      if (ecIns._disposed) {\n        disposedWarning(ecIns.id);\n        return;\n      }\n      var ecModel = ecIns._model;\n      var coordSysList = ecIns._coordSysMgr.getCoordinateSystems();\n      var result;\n      var parsedFinder = modelUtil.parseFinder(ecModel, finder);\n      for (var i = 0; i < coordSysList.length; i++) {\n        var coordSys = coordSysList[i];\n        if (coordSys[methodName] && (result = coordSys[methodName](ecModel, parsedFinder, value)) != null) {\n          return result;\n        }\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        warn('No coordinate system that supports ' + methodName + ' found by the given finder.');\n      }\n    };\n    updateStreamModes = function (ecIns, ecModel) {\n      var chartsMap = ecIns._chartsMap;\n      var scheduler = ecIns._scheduler;\n      ecModel.eachSeries(function (seriesModel) {\n        scheduler.updateStreamModes(seriesModel, chartsMap[seriesModel.__viewId]);\n      });\n    };\n    doDispatchAction = function (payload, silent) {\n      var _this = this;\n      var ecModel = this.getModel();\n      var payloadType = payload.type;\n      var escapeConnect = payload.escapeConnect;\n      var actionWrap = actions[payloadType];\n      var actionInfo = actionWrap.actionInfo;\n      var cptTypeTmp = (actionInfo.update || 'update').split(':');\n      var updateMethod = cptTypeTmp.pop();\n      var cptType = cptTypeTmp[0] != null && parseClassType(cptTypeTmp[0]);\n      this[IN_MAIN_PROCESS_KEY] = true;\n      var payloads = [payload];\n      var batched = false;\n      // Batch action\n      if (payload.batch) {\n        batched = true;\n        payloads = map(payload.batch, function (item) {\n          item = defaults(extend({}, item), payload);\n          item.batch = null;\n          return item;\n        });\n      }\n      var eventObjBatch = [];\n      var eventObj;\n      var isSelectChange = isSelectChangePayload(payload);\n      var isHighDown = isHighDownPayload(payload);\n      // Only leave blur once if there are multiple batches.\n      if (isHighDown) {\n        allLeaveBlur(this._api);\n      }\n      each(payloads, function (batchItem) {\n        // Action can specify the event by return it.\n        eventObj = actionWrap.action(batchItem, _this._model, _this._api);\n        // Emit event outside\n        eventObj = eventObj || extend({}, batchItem);\n        // Convert type to eventType\n        eventObj.type = actionInfo.event || eventObj.type;\n        eventObjBatch.push(eventObj);\n        // light update does not perform data process, layout and visual.\n        if (isHighDown) {\n          var _a = modelUtil.preParseFinder(payload),\n            queryOptionMap = _a.queryOptionMap,\n            mainTypeSpecified = _a.mainTypeSpecified;\n          var componentMainType = mainTypeSpecified ? queryOptionMap.keys()[0] : 'series';\n          updateDirectly(_this, updateMethod, batchItem, componentMainType);\n          markStatusToUpdate(_this);\n        } else if (isSelectChange) {\n          // At present `dispatchAction({ type: 'select', ... })` is not supported on components.\n          // geo still use 'geoselect'.\n          updateDirectly(_this, updateMethod, batchItem, 'series');\n          markStatusToUpdate(_this);\n        } else if (cptType) {\n          updateDirectly(_this, updateMethod, batchItem, cptType.main, cptType.sub);\n        }\n      });\n      if (updateMethod !== 'none' && !isHighDown && !isSelectChange && !cptType) {\n        try {\n          // Still dirty\n          if (this[PENDING_UPDATE]) {\n            prepare(this);\n            updateMethods.update.call(this, payload);\n            this[PENDING_UPDATE] = null;\n          } else {\n            updateMethods[updateMethod].call(this, payload);\n          }\n        } catch (e) {\n          this[IN_MAIN_PROCESS_KEY] = false;\n          throw e;\n        }\n      }\n      // Follow the rule of action batch\n      if (batched) {\n        eventObj = {\n          type: actionInfo.event || payloadType,\n          escapeConnect: escapeConnect,\n          batch: eventObjBatch\n        };\n      } else {\n        eventObj = eventObjBatch[0];\n      }\n      this[IN_MAIN_PROCESS_KEY] = false;\n      if (!silent) {\n        var messageCenter = this._messageCenter;\n        messageCenter.trigger(eventObj.type, eventObj);\n        // Extra triggered 'selectchanged' event\n        if (isSelectChange) {\n          var newObj = {\n            type: 'selectchanged',\n            escapeConnect: escapeConnect,\n            selected: getAllSelectedIndices(ecModel),\n            isFromClick: payload.isFromClick || false,\n            fromAction: payload.type,\n            fromActionPayload: payload\n          };\n          messageCenter.trigger(newObj.type, newObj);\n        }\n      }\n    };\n    flushPendingActions = function (silent) {\n      var pendingActions = this._pendingActions;\n      while (pendingActions.length) {\n        var payload = pendingActions.shift();\n        doDispatchAction.call(this, payload, silent);\n      }\n    };\n    triggerUpdatedEvent = function (silent) {\n      !silent && this.trigger('updated');\n    };\n    /**\n     * Event `rendered` is triggered when zr\n     * rendered. It is useful for realtime\n     * snapshot (reflect animation).\n     *\n     * Event `finished` is triggered when:\n     * (1) zrender rendering finished.\n     * (2) initial animation finished.\n     * (3) progressive rendering finished.\n     * (4) no pending action.\n     * (5) no delayed setOption needs to be processed.\n     */\n    bindRenderedEvent = function (zr, ecIns) {\n      zr.on('rendered', function (params) {\n        ecIns.trigger('rendered', params);\n        // The `finished` event should not be triggered repeatedly,\n        // so it should only be triggered when rendering indeed happens\n        // in zrender. (Consider the case that dipatchAction is keep\n        // triggering when mouse move).\n        if (\n        // Although zr is dirty if initial animation is not finished\n        // and this checking is called on frame, we also check\n        // animation finished for robustness.\n        zr.animation.isFinished() && !ecIns[PENDING_UPDATE] && !ecIns._scheduler.unfinished && !ecIns._pendingActions.length) {\n          ecIns.trigger('finished');\n        }\n      });\n    };\n    bindMouseEvent = function (zr, ecIns) {\n      zr.on('mouseover', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOverForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('mouseout', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, isHighDownDispatcher);\n        if (dispatcher) {\n          handleGlobalMouseOutForHighDown(dispatcher, e, ecIns._api);\n          markStatusToUpdate(ecIns);\n        }\n      }).on('click', function (e) {\n        var el = e.target;\n        var dispatcher = findEventDispatcher(el, function (target) {\n          return getECData(target).dataIndex != null;\n        }, true);\n        if (dispatcher) {\n          var actionType = dispatcher.selected ? 'unselect' : 'select';\n          var ecData = getECData(dispatcher);\n          ecIns._api.dispatchAction({\n            type: actionType,\n            dataType: ecData.dataType,\n            dataIndexInside: ecData.dataIndex,\n            seriesIndex: ecData.seriesIndex,\n            isFromClick: true\n          });\n        }\n      });\n    };\n    function clearColorPalette(ecModel) {\n      ecModel.clearColorPalette();\n      ecModel.eachSeries(function (seriesModel) {\n        seriesModel.clearColorPalette();\n      });\n    }\n    ;\n    // Allocate zlevels for series and components\n    function allocateZlevels(ecModel) {\n      ;\n      var componentZLevels = [];\n      var seriesZLevels = [];\n      var hasSeparateZLevel = false;\n      ecModel.eachComponent(function (componentType, componentModel) {\n        var zlevel = componentModel.get('zlevel') || 0;\n        var z = componentModel.get('z') || 0;\n        var zlevelKey = componentModel.getZLevelKey();\n        hasSeparateZLevel = hasSeparateZLevel || !!zlevelKey;\n        (componentType === 'series' ? seriesZLevels : componentZLevels).push({\n          zlevel: zlevel,\n          z: z,\n          idx: componentModel.componentIndex,\n          type: componentType,\n          key: zlevelKey\n        });\n      });\n      if (hasSeparateZLevel) {\n        // Series after component\n        var zLevels = componentZLevels.concat(seriesZLevels);\n        var lastSeriesZLevel_1;\n        var lastSeriesKey_1;\n        timsort(zLevels, function (a, b) {\n          if (a.zlevel === b.zlevel) {\n            return a.z - b.z;\n          }\n          return a.zlevel - b.zlevel;\n        });\n        each(zLevels, function (item) {\n          var componentModel = ecModel.getComponent(item.type, item.idx);\n          var zlevel = item.zlevel;\n          var key = item.key;\n          if (lastSeriesZLevel_1 != null) {\n            zlevel = Math.max(lastSeriesZLevel_1, zlevel);\n          }\n          if (key) {\n            if (zlevel === lastSeriesZLevel_1 && key !== lastSeriesKey_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = key;\n          } else if (lastSeriesKey_1) {\n            if (zlevel === lastSeriesZLevel_1) {\n              zlevel++;\n            }\n            lastSeriesKey_1 = '';\n          }\n          lastSeriesZLevel_1 = zlevel;\n          componentModel.setZLevel(zlevel);\n        });\n      }\n    }\n    render = function (ecIns, ecModel, api, payload, updateParams) {\n      allocateZlevels(ecModel);\n      renderComponents(ecIns, ecModel, api, payload, updateParams);\n      each(ecIns._chartsViews, function (chart) {\n        chart.__alive = false;\n      });\n      renderSeries(ecIns, ecModel, api, payload, updateParams);\n      // Remove groups of unrendered charts\n      each(ecIns._chartsViews, function (chart) {\n        if (!chart.__alive) {\n          chart.remove(ecModel, api);\n        }\n      });\n    };\n    renderComponents = function (ecIns, ecModel, api, payload, updateParams, dirtyList) {\n      each(dirtyList || ecIns._componentsViews, function (componentView) {\n        var componentModel = componentView.__model;\n        clearStates(componentModel, componentView);\n        componentView.render(componentModel, ecModel, api, payload);\n        updateZ(componentModel, componentView);\n        updateStates(componentModel, componentView);\n      });\n    };\n    /**\n     * Render each chart and component\n     */\n    renderSeries = function (ecIns, ecModel, api, payload, updateParams, dirtyMap) {\n      // Render all charts\n      var scheduler = ecIns._scheduler;\n      updateParams = extend(updateParams || {}, {\n        updatedSeries: ecModel.getSeries()\n      });\n      // TODO progressive?\n      lifecycle.trigger('series:beforeupdate', ecModel, api, updateParams);\n      var unfinished = false;\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        chartView.__alive = true;\n        var renderTask = chartView.renderTask;\n        scheduler.updatePayload(renderTask, payload);\n        // TODO states on marker.\n        clearStates(seriesModel, chartView);\n        if (dirtyMap && dirtyMap.get(seriesModel.uid)) {\n          renderTask.dirty();\n        }\n        if (renderTask.perform(scheduler.getPerformArgs(renderTask))) {\n          unfinished = true;\n        }\n        chartView.group.silent = !!seriesModel.get('silent');\n        // Should not call markRedraw on group, because it will disable zrender\n        // incremental render (always render from the __startIndex each frame)\n        // chartView.group.markRedraw();\n        updateBlend(seriesModel, chartView);\n        updateSeriesElementSelection(seriesModel);\n      });\n      scheduler.unfinished = unfinished || scheduler.unfinished;\n      lifecycle.trigger('series:layoutlabels', ecModel, api, updateParams);\n      // transition after label is layouted.\n      lifecycle.trigger('series:transition', ecModel, api, updateParams);\n      ecModel.eachSeries(function (seriesModel) {\n        var chartView = ecIns._chartsMap[seriesModel.__viewId];\n        // Update Z after labels updated. Before applying states.\n        updateZ(seriesModel, chartView);\n        // NOTE: Update states after label is updated.\n        // label should be in normal status when layouting.\n        updateStates(seriesModel, chartView);\n      });\n      // If use hover layer\n      updateHoverLayerStatus(ecIns, ecModel);\n      lifecycle.trigger('series:afterupdate', ecModel, api, updateParams);\n    };\n    markStatusToUpdate = function (ecIns) {\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = true;\n      // Wake up zrender if it's sleep. Let it update states in the next frame.\n      ecIns.getZr().wakeUp();\n    };\n    applyChangedStates = function (ecIns) {\n      if (!ecIns[STATUS_NEEDS_UPDATE_KEY]) {\n        return;\n      }\n      ecIns.getZr().storage.traverse(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        applyElementStates(el);\n      });\n      ecIns[STATUS_NEEDS_UPDATE_KEY] = false;\n    };\n    function applyElementStates(el) {\n      var newStates = [];\n      var oldStates = el.currentStates;\n      // Keep other states.\n      for (var i = 0; i < oldStates.length; i++) {\n        var stateName = oldStates[i];\n        if (!(stateName === 'emphasis' || stateName === 'blur' || stateName === 'select')) {\n          newStates.push(stateName);\n        }\n      }\n      // Only use states when it's exists.\n      if (el.selected && el.states.select) {\n        newStates.push('select');\n      }\n      if (el.hoverState === HOVER_STATE_EMPHASIS && el.states.emphasis) {\n        newStates.push('emphasis');\n      } else if (el.hoverState === HOVER_STATE_BLUR && el.states.blur) {\n        newStates.push('blur');\n      }\n      el.useStates(newStates);\n    }\n    function updateHoverLayerStatus(ecIns, ecModel) {\n      var zr = ecIns._zr;\n      var storage = zr.storage;\n      var elCount = 0;\n      storage.traverse(function (el) {\n        if (!el.isGroup) {\n          elCount++;\n        }\n      });\n      if (elCount > ecModel.get('hoverLayerThreshold') && !env.node && !env.worker) {\n        ecModel.eachSeries(function (seriesModel) {\n          if (seriesModel.preventUsingHoverLayer) {\n            return;\n          }\n          var chartView = ecIns._chartsMap[seriesModel.__viewId];\n          if (chartView.__alive) {\n            chartView.eachRendered(function (el) {\n              if (el.states.emphasis) {\n                el.states.emphasis.hoverLayer = true;\n              }\n            });\n          }\n        });\n      }\n    }\n    ;\n    /**\n     * Update chart and blend.\n     */\n    function updateBlend(seriesModel, chartView) {\n      var blendMode = seriesModel.get('blendMode') || null;\n      chartView.eachRendered(function (el) {\n        // FIXME marker and other components\n        if (!el.isGroup) {\n          // DON'T mark the element dirty. In case element is incremental and don't want to rerender.\n          el.style.blend = blendMode;\n        }\n      });\n    }\n    ;\n    function updateZ(model, view) {\n      if (model.preventAutoZ) {\n        return;\n      }\n      var z = model.get('z') || 0;\n      var zlevel = model.get('zlevel') || 0;\n      // Set z and zlevel\n      view.eachRendered(function (el) {\n        doUpdateZ(el, z, zlevel, -Infinity);\n        // Don't traverse the children because it has been traversed in _updateZ.\n        return true;\n      });\n    }\n    ;\n    function doUpdateZ(el, z, zlevel, maxZ2) {\n      // Group may also have textContent\n      var label = el.getTextContent();\n      var labelLine = el.getTextGuideLine();\n      var isGroup = el.isGroup;\n      if (isGroup) {\n        // set z & zlevel of children elements of Group\n        var children = el.childrenRef();\n        for (var i = 0; i < children.length; i++) {\n          maxZ2 = Math.max(doUpdateZ(children[i], z, zlevel, maxZ2), maxZ2);\n        }\n      } else {\n        // not Group\n        el.z = z;\n        el.zlevel = zlevel;\n        maxZ2 = Math.max(el.z2, maxZ2);\n      }\n      // always set z and zlevel if label/labelLine exists\n      if (label) {\n        label.z = z;\n        label.zlevel = zlevel;\n        // lift z2 of text content\n        // TODO if el.emphasis.z2 is spcefied, what about textContent.\n        isFinite(maxZ2) && (label.z2 = maxZ2 + 2);\n      }\n      if (labelLine) {\n        var textGuideLineConfig = el.textGuideLineConfig;\n        labelLine.z = z;\n        labelLine.zlevel = zlevel;\n        isFinite(maxZ2) && (labelLine.z2 = maxZ2 + (textGuideLineConfig && textGuideLineConfig.showAbove ? 1 : -1));\n      }\n      return maxZ2;\n    }\n    // Clear states without animation.\n    // TODO States on component.\n    function clearStates(model, view) {\n      view.eachRendered(function (el) {\n        // Not applied on removed elements, it may still in fading.\n        if (graphic.isElementRemoved(el)) {\n          return;\n        }\n        var textContent = el.getTextContent();\n        var textGuide = el.getTextGuideLine();\n        if (el.stateTransition) {\n          el.stateTransition = null;\n        }\n        if (textContent && textContent.stateTransition) {\n          textContent.stateTransition = null;\n        }\n        if (textGuide && textGuide.stateTransition) {\n          textGuide.stateTransition = null;\n        }\n        // TODO If el is incremental.\n        if (el.hasState()) {\n          el.prevStates = el.currentStates;\n          el.clearStates();\n        } else if (el.prevStates) {\n          el.prevStates = null;\n        }\n      });\n    }\n    function updateStates(model, view) {\n      var stateAnimationModel = model.getModel('stateAnimation');\n      var enableAnimation = model.isAnimationEnabled();\n      var duration = stateAnimationModel.get('duration');\n      var stateTransition = duration > 0 ? {\n        duration: duration,\n        delay: stateAnimationModel.get('delay'),\n        easing: stateAnimationModel.get('easing')\n        // additive: stateAnimationModel.get('additive')\n      } : null;\n      view.eachRendered(function (el) {\n        if (el.states && el.states.emphasis) {\n          // Not applied on removed elements, it may still in fading.\n          if (graphic.isElementRemoved(el)) {\n            return;\n          }\n          if (el instanceof graphic.Path) {\n            savePathStates(el);\n          }\n          // Only updated on changed element. In case element is incremental and don't want to rerender.\n          // TODO, a more proper way?\n          if (el.__dirty) {\n            var prevStates = el.prevStates;\n            // Restore states without animation\n            if (prevStates) {\n              el.useStates(prevStates);\n            }\n          }\n          // Update state transition and enable animation again.\n          if (enableAnimation) {\n            el.stateTransition = stateTransition;\n            var textContent = el.getTextContent();\n            var textGuide = el.getTextGuideLine();\n            // TODO Is it necessary to animate label?\n            if (textContent) {\n              textContent.stateTransition = stateTransition;\n            }\n            if (textGuide) {\n              textGuide.stateTransition = stateTransition;\n            }\n          }\n          // Use highlighted and selected flag to toggle states.\n          if (el.__dirty) {\n            applyElementStates(el);\n          }\n        }\n      });\n    }\n    ;\n    createExtensionAPI = function (ecIns) {\n      return new ( /** @class */function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n          return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.getCoordinateSystems = function () {\n          return ecIns._coordSysMgr.getCoordinateSystems();\n        };\n        class_1.prototype.getComponentByElement = function (el) {\n          while (el) {\n            var modelInfo = el.__ecComponentInfo;\n            if (modelInfo != null) {\n              return ecIns._model.getComponent(modelInfo.mainType, modelInfo.index);\n            }\n            el = el.parent;\n          }\n        };\n        class_1.prototype.enterEmphasis = function (el, highlightDigit) {\n          enterEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveEmphasis = function (el, highlightDigit) {\n          leaveEmphasis(el, highlightDigit);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterBlur = function (el) {\n          enterBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveBlur = function (el) {\n          leaveBlur(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.enterSelect = function (el) {\n          enterSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.leaveSelect = function (el) {\n          leaveSelect(el);\n          markStatusToUpdate(ecIns);\n        };\n        class_1.prototype.getModel = function () {\n          return ecIns.getModel();\n        };\n        class_1.prototype.getViewOfComponentModel = function (componentModel) {\n          return ecIns.getViewOfComponentModel(componentModel);\n        };\n        class_1.prototype.getViewOfSeriesModel = function (seriesModel) {\n          return ecIns.getViewOfSeriesModel(seriesModel);\n        };\n        return class_1;\n      }(ExtensionAPI))(ecIns);\n    };\n    enableConnect = function (chart) {\n      function updateConnectedChartsStatus(charts, status) {\n        for (var i = 0; i < charts.length; i++) {\n          var otherChart = charts[i];\n          otherChart[CONNECT_STATUS_KEY] = status;\n        }\n      }\n      each(eventActionMap, function (actionType, eventType) {\n        chart._messageCenter.on(eventType, function (event) {\n          if (connectedGroups[chart.group] && chart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_PENDING) {\n            if (event && event.escapeConnect) {\n              return;\n            }\n            var action_1 = chart.makeActionFromEvent(event);\n            var otherCharts_1 = [];\n            each(instances, function (otherChart) {\n              if (otherChart !== chart && otherChart.group === chart.group) {\n                otherCharts_1.push(otherChart);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_PENDING);\n            each(otherCharts_1, function (otherChart) {\n              if (otherChart[CONNECT_STATUS_KEY] !== CONNECT_STATUS_UPDATING) {\n                otherChart.dispatchAction(action_1);\n              }\n            });\n            updateConnectedChartsStatus(otherCharts_1, CONNECT_STATUS_UPDATED);\n          }\n        });\n      });\n    };\n  }();\n  return ECharts;\n}(Eventful);\nvar echartsProto = ECharts.prototype;\nechartsProto.on = createRegisterEventWithLowercaseECharts('on');\nechartsProto.off = createRegisterEventWithLowercaseECharts('off');\n/**\n * @deprecated\n */\n// @ts-ignore\nechartsProto.one = function (eventName, cb, ctx) {\n  var self = this;\n  deprecateLog('ECharts#one is deprecated.');\n  function wrapped() {\n    var args2 = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args2[_i] = arguments[_i];\n    }\n    cb && cb.apply && cb.apply(this, args2);\n    // @ts-ignore\n    self.off(eventName, wrapped);\n  }\n  ;\n  // @ts-ignore\n  this.on.call(this, eventName, wrapped, ctx);\n};\nvar MOUSE_EVENT_NAMES = ['click', 'dblclick', 'mouseover', 'mouseout', 'mousemove', 'mousedown', 'mouseup', 'globalout', 'contextmenu'];\nfunction disposedWarning(id) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn('Instance ' + id + ' has been disposed');\n  }\n}\nvar actions = {};\n/**\n * Map eventType to actionType\n */\nvar eventActionMap = {};\nvar dataProcessorFuncs = [];\nvar optionPreprocessorFuncs = [];\nvar visualFuncs = [];\nvar themeStorage = {};\nvar loadingEffects = {};\nvar instances = {};\nvar connectedGroups = {};\nvar idBase = +new Date() - 0;\nvar groupIdBase = +new Date() - 0;\nvar DOM_ATTRIBUTE_KEY = '_echarts_instance_';\n/**\n * @param opts.devicePixelRatio Use window.devicePixelRatio by default\n * @param opts.renderer Can choose 'canvas' or 'svg' to render the chart.\n * @param opts.width Use clientWidth of the input `dom` by default.\n *        Can be 'auto' (the same as null/undefined)\n * @param opts.height Use clientHeight of the input `dom` by default.\n *        Can be 'auto' (the same as null/undefined)\n * @param opts.locale Specify the locale.\n * @param opts.useDirtyRect Enable dirty rectangle rendering or not.\n */\nexport function init(dom, theme, opts) {\n  var isClient = !(opts && opts.ssr);\n  if (isClient) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!dom) {\n        throw new Error('Initialize failed: invalid dom.');\n      }\n    }\n    var existInstance = getInstanceByDom(dom);\n    if (existInstance) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('There is a chart instance already initialized on the dom.');\n      }\n      return existInstance;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isDom(dom) && dom.nodeName.toUpperCase() !== 'CANVAS' && (!dom.clientWidth && (!opts || opts.width == null) || !dom.clientHeight && (!opts || opts.height == null))) {\n        warn('Can\\'t get DOM width or height. Please check ' + 'dom.clientWidth and dom.clientHeight. They should not be 0.' + 'For example, you may need to call this in the callback ' + 'of window.onload.');\n      }\n    }\n  }\n  var chart = new ECharts(dom, theme, opts);\n  chart.id = 'ec_' + idBase++;\n  instances[chart.id] = chart;\n  isClient && modelUtil.setAttribute(dom, DOM_ATTRIBUTE_KEY, chart.id);\n  enableConnect(chart);\n  lifecycle.trigger('afterinit', chart);\n  return chart;\n}\n/**\n * @usage\n * (A)\n * ```js\n * let chart1 = echarts.init(dom1);\n * let chart2 = echarts.init(dom2);\n * chart1.group = 'xxx';\n * chart2.group = 'xxx';\n * echarts.connect('xxx');\n * ```\n * (B)\n * ```js\n * let chart1 = echarts.init(dom1);\n * let chart2 = echarts.init(dom2);\n * echarts.connect('xxx', [chart1, chart2]);\n * ```\n */\nexport function connect(groupId) {\n  // Is array of charts\n  if (isArray(groupId)) {\n    var charts = groupId;\n    groupId = null;\n    // If any chart has group\n    each(charts, function (chart) {\n      if (chart.group != null) {\n        groupId = chart.group;\n      }\n    });\n    groupId = groupId || 'g_' + groupIdBase++;\n    each(charts, function (chart) {\n      chart.group = groupId;\n    });\n  }\n  connectedGroups[groupId] = true;\n  return groupId;\n}\nexport function disconnect(groupId) {\n  connectedGroups[groupId] = false;\n}\n/**\n * Alias and backward compatibility\n * @deprecated\n */\nexport var disConnect = disconnect;\n/**\n * Dispose a chart instance\n */\nexport function dispose(chart) {\n  if (isString(chart)) {\n    chart = instances[chart];\n  } else if (!(chart instanceof ECharts)) {\n    // Try to treat as dom\n    chart = getInstanceByDom(chart);\n  }\n  if (chart instanceof ECharts && !chart.isDisposed()) {\n    chart.dispose();\n  }\n}\nexport function getInstanceByDom(dom) {\n  return instances[modelUtil.getAttribute(dom, DOM_ATTRIBUTE_KEY)];\n}\nexport function getInstanceById(key) {\n  return instances[key];\n}\n/**\n * Register theme\n */\nexport function registerTheme(name, theme) {\n  themeStorage[name] = theme;\n}\n/**\n * Register option preprocessor\n */\nexport function registerPreprocessor(preprocessorFunc) {\n  if (indexOf(optionPreprocessorFuncs, preprocessorFunc) < 0) {\n    optionPreprocessorFuncs.push(preprocessorFunc);\n  }\n}\nexport function registerProcessor(priority, processor) {\n  normalizeRegister(dataProcessorFuncs, priority, processor, PRIORITY_PROCESSOR_DEFAULT);\n}\n/**\n * Register postIniter\n * @param {Function} postInitFunc\n */\nexport function registerPostInit(postInitFunc) {\n  registerUpdateLifecycle('afterinit', postInitFunc);\n}\n/**\n * Register postUpdater\n * @param {Function} postUpdateFunc\n */\nexport function registerPostUpdate(postUpdateFunc) {\n  registerUpdateLifecycle('afterupdate', postUpdateFunc);\n}\nexport function registerUpdateLifecycle(name, cb) {\n  lifecycle.on(name, cb);\n}\nexport function registerAction(actionInfo, eventName, action) {\n  if (isFunction(eventName)) {\n    action = eventName;\n    eventName = '';\n  }\n  var actionType = isObject(actionInfo) ? actionInfo.type : [actionInfo, actionInfo = {\n    event: eventName\n  }][0];\n  // Event name is all lowercase\n  actionInfo.event = (actionInfo.event || actionType).toLowerCase();\n  eventName = actionInfo.event;\n  if (eventActionMap[eventName]) {\n    // Already registered.\n    return;\n  }\n  // Validate action type and event name.\n  assert(ACTION_REG.test(actionType) && ACTION_REG.test(eventName));\n  if (!actions[actionType]) {\n    actions[actionType] = {\n      action: action,\n      actionInfo: actionInfo\n    };\n  }\n  eventActionMap[eventName] = actionType;\n}\nexport function registerCoordinateSystem(type, coordSysCreator) {\n  CoordinateSystemManager.register(type, coordSysCreator);\n}\n/**\n * Get dimensions of specified coordinate system.\n * @param {string} type\n * @return {Array.<string|Object>}\n */\nexport function getCoordinateSystemDimensions(type) {\n  var coordSysCreator = CoordinateSystemManager.get(type);\n  if (coordSysCreator) {\n    return coordSysCreator.getDimensionsInfo ? coordSysCreator.getDimensionsInfo() : coordSysCreator.dimensions.slice();\n  }\n}\nexport { registerLocale } from './locale.js';\nfunction registerLayout(priority, layoutTask) {\n  normalizeRegister(visualFuncs, priority, layoutTask, PRIORITY_VISUAL_LAYOUT, 'layout');\n}\nfunction registerVisual(priority, visualTask) {\n  normalizeRegister(visualFuncs, priority, visualTask, PRIORITY_VISUAL_CHART, 'visual');\n}\nexport { registerLayout, registerVisual };\nvar registeredTasks = [];\nfunction normalizeRegister(targetList, priority, fn, defaultPriority, visualType) {\n  if (isFunction(priority) || isObject(priority)) {\n    fn = priority;\n    priority = defaultPriority;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (isNaN(priority) || priority == null) {\n      throw new Error('Illegal priority');\n    }\n    // Check duplicate\n    each(targetList, function (wrap) {\n      assert(wrap.__raw !== fn);\n    });\n  }\n  // Already registered\n  if (indexOf(registeredTasks, fn) >= 0) {\n    return;\n  }\n  registeredTasks.push(fn);\n  var stageHandler = Scheduler.wrapStageHandler(fn, visualType);\n  stageHandler.__prio = priority;\n  stageHandler.__raw = fn;\n  targetList.push(stageHandler);\n}\nexport function registerLoading(name, loadingFx) {\n  loadingEffects[name] = loadingFx;\n}\n/**\n * ZRender need a canvas context to do measureText.\n * But in node environment canvas may be created by node-canvas.\n * So we need to specify how to create a canvas instead of using document.createElement('canvas')\n *\n *\n * @deprecated use setPlatformAPI({ createCanvas }) instead.\n *\n * @example\n *     let Canvas = require('canvas');\n *     let echarts = require('echarts');\n *     echarts.setCanvasCreator(function () {\n *         // Small size is enough.\n *         return new Canvas(32, 32);\n *     });\n */\nexport function setCanvasCreator(creator) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateLog('setCanvasCreator is deprecated. Use setPlatformAPI({ createCanvas }) instead.');\n  }\n  setPlatformAPI({\n    createCanvas: creator\n  });\n}\n/**\n * The parameters and usage: see `geoSourceManager.registerMap`.\n * Compatible with previous `echarts.registerMap`.\n */\nexport function registerMap(mapName, geoJson, specialAreas) {\n  var registerMap = getImpl('registerMap');\n  registerMap && registerMap(mapName, geoJson, specialAreas);\n}\nexport function getMap(mapName) {\n  var getMap = getImpl('getMap');\n  return getMap && getMap(mapName);\n}\nexport var registerTransform = registerExternalTransform;\n/**\n * Globa dispatchAction to a specified chart instance.\n */\n// export function dispatchAction(payload: { chartId: string } & Payload, opt?: Parameters<ECharts['dispatchAction']>[1]) {\n//     if (!payload || !payload.chartId) {\n//         // Must have chartId to find chart\n//         return;\n//     }\n//     const chart = instances[payload.chartId];\n//     if (chart) {\n//         chart.dispatchAction(payload, opt);\n//     }\n// }\n// Builtin global visual\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataStyleTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataColorPaletteTask);\nregisterVisual(PRIORITY_VISUAL_GLOBAL, seriesSymbolTask);\nregisterVisual(PRIORITY_VISUAL_CHART_DATA_CUSTOM, dataSymbolTask);\nregisterVisual(PRIORITY_VISUAL_DECAL, decal);\nregisterPreprocessor(backwardCompat);\nregisterProcessor(PRIORITY_PROCESSOR_DATASTACK, dataStack);\nregisterLoading('default', loadingDefault);\n// Default actions\nregisterAction({\n  type: HIGHLIGHT_ACTION_TYPE,\n  event: HIGHLIGHT_ACTION_TYPE,\n  update: HIGHLIGHT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: DOWNPLAY_ACTION_TYPE,\n  event: DOWNPLAY_ACTION_TYPE,\n  update: DOWNPLAY_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: SELECT_ACTION_TYPE,\n  event: SELECT_ACTION_TYPE,\n  update: SELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: UNSELECT_ACTION_TYPE,\n  event: UNSELECT_ACTION_TYPE,\n  update: UNSELECT_ACTION_TYPE\n}, noop);\nregisterAction({\n  type: TOGGLE_SELECT_ACTION_TYPE,\n  event: TOGGLE_SELECT_ACTION_TYPE,\n  update: TOGGLE_SELECT_ACTION_TYPE\n}, noop);\n// Default theme\nregisterTheme('light', lightTheme);\nregisterTheme('dark', darkTheme);\n// For backward compatibility, where the namespace `dataTool` will\n// be mounted on `echarts` is the extension `dataTool` is imported.\nexport var dataTool = {};"], "mappings": ";;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,cAAc,EAAEC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,0BAA0B;AACpM,OAAOC,GAAG,MAAM,yBAAyB;AACzC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,uBAAuB,MAAM,uBAAuB;AAC3D,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAO,KAAKC,OAAO,MAAM,oBAAoB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,8BAA8B,EAAEC,0BAA0B,EAAEC,4BAA4B,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,gCAAgC,EAAEC,aAAa,EAAEC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AAC7kB,OAAO,KAAKC,SAAS,MAAM,kBAAkB;AAC7C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,eAAe,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,oBAAoB;AACzF,OAAOC,cAAc,MAAM,uBAAuB;AAClD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,qBAAqB;AACtE,SAASC,iBAAiB,EAAEC,qBAAqB,QAAQ,qBAAqB;AAC9E,SAASC,YAAY,EAAEC,mBAAmB,EAAEC,KAAK,EAAEC,IAAI,QAAQ,gBAAgB;AAC/E,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,aAAa;AAC7D,SAASC,mBAAmB,QAAQ,kBAAkB;AACtD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,SAASC,WAAW,EAAEC,cAAc,QAAQ,8BAA8B;AAC1E,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,IAAIC,OAAO,GAAG,OAAO;AAC5B,OAAO,IAAIC,YAAY,GAAG;EACxBxF,OAAO,EAAE;AACX,CAAC;AACD,IAAIyF,sBAAsB,GAAG,CAAC;AAC9B,IAAIC,gCAAgC,GAAG,GAAG;AAC1C;AACA;AACA,IAAIC,4BAA4B,GAAG,GAAG;AACtC;AACA;AACA,IAAIC,yBAAyB,GAAG,IAAI;AACpC,IAAIC,0BAA0B,GAAG,IAAI;AACrC,IAAIC,4BAA4B,GAAG,IAAI;AACvC,IAAIC,sBAAsB,GAAG,IAAI;AACjC,IAAIC,kCAAkC,GAAG,IAAI;AAC7C,IAAIC,sBAAsB,GAAG,IAAI;AACjC,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,yBAAyB,GAAG,IAAI;AACpC;AACA;AACA;AACA,IAAIC,iCAAiC,GAAG,IAAI;AAC5C;AACA;AACA,IAAIC,iCAAiC,GAAG,IAAI;AAC5C,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,qBAAqB,GAAG,IAAI;AAChC,OAAO,IAAIC,QAAQ,GAAG;EACpBC,SAAS,EAAE;IACTC,MAAM,EAAEf,yBAAyB;IACjCgB,aAAa,EAAElB,gCAAgC;IAC/CmB,SAAS,EAAEf;EACb,CAAC;EACDgB,MAAM,EAAE;IACNC,MAAM,EAAEhB,sBAAsB;IAC9BiB,kBAAkB,EAAEhB,kCAAkC;IACtDiB,MAAM,EAAEhB,sBAAsB;IAC9BiB,KAAK,EAAEhB,qBAAqB;IAC5BiB,iBAAiB,EAAEd,iCAAiC;IACpDe,SAAS,EAAEjB,yBAAyB;IACpCkB,KAAK,EAAEf,qBAAqB;IAC5BgB,UAAU,EAAElB,iCAAiC;IAC7CmB,IAAI,EAAEhB,oBAAoB;IAC1BiB,KAAK,EAAEhB;EACT;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAIiB,mBAAmB,GAAG,qBAAqB;AAC/C,IAAIC,cAAc,GAAG,iBAAiB;AACtC,IAAIC,uBAAuB,GAAG,qBAAqB;AACnD,IAAIC,UAAU,GAAG,iBAAiB;AAClC,IAAIC,kBAAkB,GAAG,uBAAuB;AAChD,IAAIC,sBAAsB,GAAG,CAAC;AAC9B,IAAIC,uBAAuB,GAAG,CAAC;AAC/B,IAAIC,sBAAsB,GAAG,CAAC;AAC9B;AACA;AACA,SAASC,uCAAuCA,CAACC,MAAM,EAAE;EACvD,OAAO,YAAY;IACjB,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC1B;IACA,IAAI,IAAI,CAACG,UAAU,CAAC,CAAC,EAAE;MACrBC,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,OAAOC,8BAA8B,CAAC,IAAI,EAAER,MAAM,EAAEC,IAAI,CAAC;EAC3D,CAAC;AACH;AACA,SAASQ,6CAA6CA,CAACT,MAAM,EAAE;EAC7D,OAAO,YAAY;IACjB,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC1B;IACA,OAAOM,8BAA8B,CAAC,IAAI,EAAER,MAAM,EAAEC,IAAI,CAAC;EAC3D,CAAC;AACH;AACA,SAASO,8BAA8BA,CAACE,IAAI,EAAEV,MAAM,EAAEC,IAAI,EAAE;EAC1D;EACAA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC;EAC1C,OAAOzH,QAAQ,CAAC0H,SAAS,CAACZ,MAAM,CAAC,CAACa,KAAK,CAACH,IAAI,EAAET,IAAI,CAAC;AACrD;AACA,IAAIa,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDlJ,SAAS,CAACiJ,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACF,KAAK,CAAC,IAAI,EAAEV,SAAS,CAAC,IAAI,IAAI;EACjE;EACA,OAAOW,aAAa;AACtB,CAAC,CAAC5H,QAAQ,CAAC;AACX,IAAI8H,kBAAkB,GAAGF,aAAa,CAACF,SAAS;AAChDI,kBAAkB,CAACC,EAAE,GAAGR,6CAA6C,CAAC,IAAI,CAAC;AAC3EO,kBAAkB,CAACE,GAAG,GAAGT,6CAA6C,CAAC,KAAK,CAAC;AAC7E;AACA;AACA;AACA,IAAIU,OAAO;AACX,IAAIC,WAAW;AACf,IAAIC,cAAc;AAClB,IAAIC,aAAa;AACjB,IAAIC,cAAc;AAClB,IAAIC,iBAAiB;AACrB,IAAIC,gBAAgB;AACpB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,iBAAiB;AACrB,IAAIC,cAAc;AAClB,IAAIC,MAAM;AACV,IAAIC,gBAAgB;AACpB,IAAIC,YAAY;AAChB,IAAIC,kBAAkB;AACtB,IAAIC,aAAa;AACjB,IAAIC,kBAAkB;AACtB,IAAIC,kBAAkB;AACtB,IAAIC,OAAO,GAAG,aAAa,UAAUtB,MAAM,EAAE;EAC3ClJ,SAAS,CAACwK,OAAO,EAAEtB,MAAM,CAAC;EAC1B,SAASsB,OAAOA,CAACC,GAAG;EACpB;EACAC,KAAK,EAAEC,IAAI,EAAE;IACX,IAAIC,KAAK,GAAG1B,MAAM,CAAC2B,IAAI,CAAC,IAAI,EAAE,IAAIxG,gBAAgB,CAAC,CAAC,CAAC,IAAI,IAAI;IAC7DuG,KAAK,CAACE,YAAY,GAAG,EAAE;IACvBF,KAAK,CAACG,UAAU,GAAG,CAAC,CAAC;IACrBH,KAAK,CAACI,gBAAgB,GAAG,EAAE;IAC3BJ,KAAK,CAACK,cAAc,GAAG,CAAC,CAAC;IACzB;IACAL,KAAK,CAACM,eAAe,GAAG,EAAE;IAC1BP,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB;IACA,IAAI1J,QAAQ,CAACyJ,KAAK,CAAC,EAAE;MACnBA,KAAK,GAAGS,YAAY,CAACT,KAAK,CAAC;IAC7B;IACAE,KAAK,CAACQ,IAAI,GAAGX,GAAG;IAChB,IAAIY,eAAe,GAAG,QAAQ;IAC9B,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIC,IAAI,GAAG;MACXvK,GAAG,CAACwK,eAAe,GAAGC,MAAM,GAAGC,MAAM;MACrC,IAAIH,IAAI,EAAE;QACRL,eAAe,GAAGnK,SAAS,CAACwK,IAAI,CAACI,8BAA8B,EAAET,eAAe,CAAC;QACjFC,oBAAoB,GAAGpK,SAAS,CAACwK,IAAI,CAACK,kCAAkC,EAAET,oBAAoB,CAAC;QAC/FC,mBAAmB,GAAGrK,SAAS,CAACwK,IAAI,CAACM,oCAAoC,EAAET,mBAAmB,CAAC;MACjG;IACF;IACA,IAAIZ,IAAI,CAACsB,GAAG,EAAE;MACZhM,OAAO,CAACiM,qBAAqB,CAAC,UAAUC,EAAE,EAAE;QAC1C,IAAIC,MAAM,GAAGpK,SAAS,CAACmK,EAAE,CAAC;QAC1B,IAAIE,SAAS,GAAGD,MAAM,CAACC,SAAS;QAChC,IAAIA,SAAS,IAAI,IAAI,EAAE;UACrB;QACF;QACA,IAAIC,OAAO,GAAG3L,aAAa,CAAC,CAAC;QAC7B2L,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,MAAM,CAACI,WAAW,CAAC;QAC/CF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,SAAS,CAAC;QACpCD,MAAM,CAACK,OAAO,IAAIH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,MAAM,CAACK,OAAO,CAAC;QACzD,OAAOH,OAAO;MAChB,CAAC,CAAC;IACJ;IACA,IAAII,EAAE,GAAG9B,KAAK,CAAC+B,GAAG,GAAG1M,OAAO,CAAC2M,IAAI,CAACnC,GAAG,EAAE;MACrCoC,QAAQ,EAAElC,IAAI,CAACkC,QAAQ,IAAIxB,eAAe;MAC1CyB,gBAAgB,EAAEnC,IAAI,CAACmC,gBAAgB;MACvCC,KAAK,EAAEpC,IAAI,CAACoC,KAAK;MACjBC,MAAM,EAAErC,IAAI,CAACqC,MAAM;MACnBf,GAAG,EAAEtB,IAAI,CAACsB,GAAG;MACbgB,YAAY,EAAE/L,SAAS,CAACyJ,IAAI,CAACsC,YAAY,EAAE1B,mBAAmB,CAAC;MAC/D2B,gBAAgB,EAAEhM,SAAS,CAACyJ,IAAI,CAACuC,gBAAgB,EAAE5B,oBAAoB,CAAC;MACxE6B,WAAW,EAAExC,IAAI,CAACwC;IACpB,CAAC,CAAC;IACFvC,KAAK,CAACwC,IAAI,GAAGzC,IAAI,CAACsB,GAAG;IACrB;IACArB,KAAK,CAACyC,iBAAiB,GAAGzJ,QAAQ,CAACrD,IAAI,CAACmM,EAAE,CAACY,KAAK,EAAEZ,EAAE,CAAC,EAAE,EAAE,CAAC;IAC1DhC,KAAK,GAAGlK,KAAK,CAACkK,KAAK,CAAC;IACpBA,KAAK,IAAIhJ,cAAc,CAACgJ,KAAK,EAAE,IAAI,CAAC;IACpCE,KAAK,CAAC2C,MAAM,GAAG7C,KAAK;IACpBE,KAAK,CAAC4C,OAAO,GAAGxI,kBAAkB,CAAC2F,IAAI,CAAC8C,MAAM,IAAIxI,WAAW,CAAC;IAC9D2F,KAAK,CAAC8C,YAAY,GAAG,IAAIlM,uBAAuB,CAAC,CAAC;IAClD,IAAImM,GAAG,GAAG/C,KAAK,CAACgD,IAAI,GAAGxD,kBAAkB,CAACQ,KAAK,CAAC;IAChD;IACA,SAASiD,gBAAgBA,CAACC,CAAC,EAAEC,CAAC,EAAE;MAC9B,OAAOD,CAAC,CAACE,MAAM,GAAGD,CAAC,CAACC,MAAM;IAC5B;IACA5M,OAAO,CAAC6M,WAAW,EAAEJ,gBAAgB,CAAC;IACtCzM,OAAO,CAAC8M,kBAAkB,EAAEL,gBAAgB,CAAC;IAC7CjD,KAAK,CAACuD,UAAU,GAAG,IAAIlK,SAAS,CAAC2G,KAAK,EAAE+C,GAAG,EAAEO,kBAAkB,EAAED,WAAW,CAAC;IAC7ErD,KAAK,CAACwD,cAAc,GAAG,IAAInF,aAAa,CAAC,CAAC;IAC1C;IACA2B,KAAK,CAACyD,WAAW,CAAC,CAAC;IACnB;IACAzD,KAAK,CAAC0D,MAAM,GAAG/N,IAAI,CAACqK,KAAK,CAAC0D,MAAM,EAAE1D,KAAK,CAAC;IACxC8B,EAAE,CAAC6B,SAAS,CAACnF,EAAE,CAAC,OAAO,EAAEwB,KAAK,CAAC4D,QAAQ,EAAE5D,KAAK,CAAC;IAC/Cb,iBAAiB,CAAC2C,EAAE,EAAE9B,KAAK,CAAC;IAC5BZ,cAAc,CAAC0C,EAAE,EAAE9B,KAAK,CAAC;IACzB;IACAnK,cAAc,CAACmK,KAAK,CAAC;IACrB,OAAOA,KAAK;EACd;EACAJ,OAAO,CAACzB,SAAS,CAACyF,QAAQ,GAAG,YAAY;IACvC,IAAI,IAAI,CAACC,SAAS,EAAE;MAClB;IACF;IACAlE,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAImE,SAAS,GAAG,IAAI,CAACP,UAAU;IAC/B;IACA,IAAI,IAAI,CAACxG,cAAc,CAAC,EAAE;MACxB,IAAIgH,MAAM,GAAG,IAAI,CAAChH,cAAc,CAAC,CAACgH,MAAM;MACxC,IAAI,CAACjH,mBAAmB,CAAC,GAAG,IAAI;MAChC,IAAI;QACF4B,OAAO,CAAC,IAAI,CAAC;QACbG,aAAa,CAACmF,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAClD,cAAc,CAAC,CAACkH,YAAY,CAAC;MAC1E,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,IAAI,CAACpH,mBAAmB,CAAC,GAAG,KAAK;QACjC,IAAI,CAACC,cAAc,CAAC,GAAG,IAAI;QAC3B,MAAMmH,CAAC;MACT;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACnC,GAAG,CAACW,KAAK,CAAC,CAAC;MAChB,IAAI,CAAC5F,mBAAmB,CAAC,GAAG,KAAK;MACjC,IAAI,CAACC,cAAc,CAAC,GAAG,IAAI;MAC3BkC,mBAAmB,CAACgB,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;MACtC7E,mBAAmB,CAACe,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;IACxC;IACA;IAAA,KACK,IAAID,SAAS,CAACK,UAAU,EAAE;MAC7B;MACA,IAAIC,UAAU,GAAGtJ,sBAAsB;MACvC,IAAIuJ,OAAO,GAAG,IAAI,CAACC,MAAM;MACzB,IAAIvB,GAAG,GAAG,IAAI,CAACC,IAAI;MACnBc,SAAS,CAACK,UAAU,GAAG,KAAK;MAC5B,GAAG;QACD,IAAII,SAAS,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;QAC3BV,SAAS,CAACW,kBAAkB,CAACJ,OAAO,CAAC;QACrC;QACAP,SAAS,CAACY,yBAAyB,CAACL,OAAO,CAAC;QAC5CtF,iBAAiB,CAAC,IAAI,EAAEsF,OAAO,CAAC;QAChC;QACA;QACA;QACA;QACA;QACA;QACAP,SAAS,CAACa,kBAAkB,CAACN,OAAO,CAAC;QACrC9E,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC+E,MAAM,EAAEvB,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;QAClDqB,UAAU,IAAI,CAAC,IAAII,IAAI,CAAC,CAAC,GAAGD,SAAS;MACvC,CAAC,QAAQH,UAAU,GAAG,CAAC,IAAIN,SAAS,CAACK,UAAU;MAC/C;MACA,IAAI,CAACL,SAAS,CAACK,UAAU,EAAE;QACzB,IAAI,CAACpC,GAAG,CAACW,KAAK,CAAC,CAAC;MAClB;MACA;MACA;IACF;EACF,CAAC;EAED9C,OAAO,CAACzB,SAAS,CAACyG,MAAM,GAAG,YAAY;IACrC,OAAO,IAAI,CAACpE,IAAI;EAClB,CAAC;EACDZ,OAAO,CAACzB,SAAS,CAAC0G,KAAK,GAAG,YAAY;IACpC,OAAO,IAAI,CAAC/G,EAAE;EAChB,CAAC;EACD8B,OAAO,CAACzB,SAAS,CAAC2G,KAAK,GAAG,YAAY;IACpC,OAAO,IAAI,CAAC/C,GAAG;EACjB,CAAC;EACDnC,OAAO,CAACzB,SAAS,CAAC4G,KAAK,GAAG,YAAY;IACpC,OAAO,IAAI,CAACvC,IAAI;EAClB,CAAC;EACD;EACA5C,OAAO,CAACzB,SAAS,CAAC6G,SAAS,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IACpE,IAAI,IAAI,CAACrI,mBAAmB,CAAC,EAAE;MAC7B,IAAI8D,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;QACzC7G,KAAK,CAAC,uDAAuD,CAAC;MAChE;MACA;IACF;IACA,IAAI,IAAI,CAAC6J,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAIiG,MAAM;IACV,IAAIqB,YAAY;IAChB,IAAIC,aAAa;IACjB,IAAI5P,QAAQ,CAACyP,QAAQ,CAAC,EAAE;MACtBC,UAAU,GAAGD,QAAQ,CAACC,UAAU;MAChCpB,MAAM,GAAGmB,QAAQ,CAACnB,MAAM;MACxBqB,YAAY,GAAGF,QAAQ,CAACE,YAAY;MACpCC,aAAa,GAAGH,QAAQ,CAACI,UAAU;MACnCJ,QAAQ,GAAGA,QAAQ,CAACA,QAAQ;IAC9B;IACA,IAAI,CAACpI,mBAAmB,CAAC,GAAG,IAAI;IAChC,IAAI,CAAC,IAAI,CAACwH,MAAM,IAAIY,QAAQ,EAAE;MAC5B,IAAIK,aAAa,GAAG,IAAI1O,aAAa,CAAC,IAAI,CAACmM,IAAI,CAAC;MAChD,IAAIlD,KAAK,GAAG,IAAI,CAAC6C,MAAM;MACvB,IAAI0B,OAAO,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI5N,WAAW,CAAC,CAAC;MAC7C2N,OAAO,CAACP,SAAS,GAAG,IAAI,CAACP,UAAU;MACnCc,OAAO,CAAChD,GAAG,GAAG,IAAI,CAACmB,IAAI;MACvB6B,OAAO,CAACrC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAElC,KAAK,EAAE,IAAI,CAAC8C,OAAO,EAAE2C,aAAa,CAAC;IACpE;IACA,IAAI,CAACjB,MAAM,CAACU,SAAS,CAACC,MAAM,EAAE;MAC5BG,YAAY,EAAEA;IAChB,CAAC,EAAEI,uBAAuB,CAAC;IAC3B,IAAIvB,YAAY,GAAG;MACjBwB,gBAAgB,EAAEJ,aAAa;MAC/BK,aAAa,EAAE;IACjB,CAAC;IACD,IAAIP,UAAU,EAAE;MACd,IAAI,CAACpI,cAAc,CAAC,GAAG;QACrBgH,MAAM,EAAEA,MAAM;QACdE,YAAY,EAAEA;MAChB,CAAC;MACD,IAAI,CAACnH,mBAAmB,CAAC,GAAG,KAAK;MACjC;MACA;MACA,IAAI,CAACgI,KAAK,CAAC,CAAC,CAACa,MAAM,CAAC,CAAC;IACvB,CAAC,MAAM;MACL,IAAI;QACFjH,OAAO,CAAC,IAAI,CAAC;QACbG,aAAa,CAACmF,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEgE,YAAY,CAAC;MACrD,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,IAAI,CAACnH,cAAc,CAAC,GAAG,IAAI;QAC3B,IAAI,CAACD,mBAAmB,CAAC,GAAG,KAAK;QACjC,MAAMoH,CAAC;MACT;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAAC1B,IAAI,EAAE;QACd;QACA,IAAI,CAACT,GAAG,CAACW,KAAK,CAAC,CAAC;MAClB;MACA,IAAI,CAAC3F,cAAc,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACD,mBAAmB,CAAC,GAAG,KAAK;MACjCmC,mBAAmB,CAACgB,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;MACtC7E,mBAAmB,CAACe,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;IACxC;EACF,CAAC;EACD;AACF;AACA;EACEnE,OAAO,CAACzB,SAAS,CAACyH,QAAQ,GAAG,YAAY;IACvC9L,YAAY,CAAC,iDAAiD,CAAC;EACjE,CAAC;EACD;EACA8F,OAAO,CAACzB,SAAS,CAAC0H,QAAQ,GAAG,YAAY;IACvC,OAAO,IAAI,CAACvB,MAAM;EACpB,CAAC;EACD1E,OAAO,CAACzB,SAAS,CAAC2H,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACxB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACwB,SAAS,CAAC,CAAC;EAC/C,CAAC;EACDlG,OAAO,CAACzB,SAAS,CAAC4H,QAAQ,GAAG,YAAY;IACvC,OAAO,IAAI,CAAChE,GAAG,CAACgE,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACDnG,OAAO,CAACzB,SAAS,CAAC6H,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACjE,GAAG,CAACiE,SAAS,CAAC,CAAC;EAC7B,CAAC;EACDpG,OAAO,CAACzB,SAAS,CAAC8H,mBAAmB,GAAG,YAAY;IAClD,OAAO,IAAI,CAAClE,GAAG,CAACmE,OAAO,CAACC;IACxB,kCAAkC5P,GAAG,CAACwK,eAAe,IAAIC,MAAM,CAACkB,gBAAgB,IAAI,CAAC;EACvF,CAAC;EACD;AACF;AACA;AACA;EACEtC,OAAO,CAACzB,SAAS,CAACiI,iBAAiB,GAAG,UAAUrG,IAAI,EAAE;IACpD,IAAIa,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzC9G,mBAAmB,CAAC,mBAAmB,EAAE,gBAAgB,CAAC;IAC5D;IACA,OAAO,IAAI,CAACsM,cAAc,CAACtG,IAAI,CAAC;EAClC,CAAC;EACDH,OAAO,CAACzB,SAAS,CAACkI,cAAc,GAAG,UAAUtG,IAAI,EAAE;IACjDA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAImG,OAAO,GAAG,IAAI,CAACnE,GAAG,CAACmE,OAAO;IAC9B,IAAItF,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIqF,OAAO,CAACI,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;MAC5E;IACF;IACA,OAAOL,OAAO,CAACE,iBAAiB,CAAC;MAC/BI,eAAe,EAAEzG,IAAI,CAACyG,eAAe,IAAI,IAAI,CAAClC,MAAM,CAACmC,GAAG,CAAC,iBAAiB,CAAC;MAC3EC,UAAU,EAAE3G,IAAI,CAAC2G,UAAU,IAAI,IAAI,CAACT,mBAAmB,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC;EACDrG,OAAO,CAACzB,SAAS,CAACwI,iBAAiB,GAAG,UAAU5G,IAAI,EAAE;IACpDA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAImG,OAAO,GAAG,IAAI,CAACnE,GAAG,CAACmE,OAAO;IAC9B,IAAItF,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIqF,OAAO,CAACI,IAAI,KAAK,KAAK,EAAE;QAC1B,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;MAC5E;IACF;IACA,OAAOL,OAAO,CAACU,cAAc,CAAC;MAC5BC,UAAU,EAAE9G,IAAI,CAAC8G;IACnB,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEjH,OAAO,CAACzB,SAAS,CAAC2I,aAAa,GAAG,YAAY;IAC5C,IAAI,CAACvQ,GAAG,CAACwQ,YAAY,EAAE;MACrB;IACF;IACA,IAAIjF,EAAE,GAAG,IAAI,CAACC,GAAG;IACjB,IAAIiF,IAAI,GAAGlF,EAAE,CAACmF,OAAO,CAACC,cAAc,CAAC,CAAC;IACtC;IACA3R,IAAI,CAACyR,IAAI,EAAE,UAAUzF,EAAE,EAAE;MACvBA,EAAE,CAAC4F,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9B,CAAC,CAAC;IACF,OAAOrF,EAAE,CAACoE,OAAO,CAACkB,SAAS,CAAC,CAAC;EAC/B,CAAC;EACDxH,OAAO,CAACzB,SAAS,CAACkJ,UAAU,GAAG,UAAUtH,IAAI,EAAE;IAC7C,IAAI,IAAI,CAAC8D,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACAiC,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAIuH,iBAAiB,GAAGvH,IAAI,CAACuH,iBAAiB;IAC9C,IAAIjD,OAAO,GAAG,IAAI,CAACC,MAAM;IACzB,IAAIiD,sBAAsB,GAAG,EAAE;IAC/B,IAAIC,IAAI,GAAG,IAAI;IACfjS,IAAI,CAAC+R,iBAAiB,EAAE,UAAUG,aAAa,EAAE;MAC/CpD,OAAO,CAACqD,aAAa,CAAC;QACpBC,QAAQ,EAAEF;MACZ,CAAC,EAAE,UAAUG,SAAS,EAAE;QACtB,IAAIC,IAAI,GAAGL,IAAI,CAACnH,cAAc,CAACuH,SAAS,CAACE,QAAQ,CAAC;QAClD,IAAI,CAACD,IAAI,CAACE,KAAK,CAACC,MAAM,EAAE;UACtBT,sBAAsB,CAACU,IAAI,CAACJ,IAAI,CAAC;UACjCA,IAAI,CAACE,KAAK,CAACC,MAAM,GAAG,IAAI;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIE,GAAG,GAAG,IAAI,CAACnG,GAAG,CAACmE,OAAO,CAACiC,OAAO,CAAC,CAAC,KAAK,KAAK,GAAG,IAAI,CAACrB,aAAa,CAAC,CAAC,GAAG,IAAI,CAACT,cAAc,CAACtG,IAAI,CAAC,CAACqH,SAAS,CAAC,QAAQ,IAAIrH,IAAI,IAAIA,IAAI,CAACuG,IAAI,IAAI,KAAK,CAAC,CAAC;IACpJ/Q,IAAI,CAACgS,sBAAsB,EAAE,UAAUM,IAAI,EAAE;MAC3CA,IAAI,CAACE,KAAK,CAACC,MAAM,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF,OAAOE,GAAG;EACZ,CAAC;EACDtI,OAAO,CAACzB,SAAS,CAACiK,mBAAmB,GAAG,UAAUrI,IAAI,EAAE;IACtD,IAAI,IAAI,CAAC8D,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAIuK,KAAK,GAAGtI,IAAI,CAACuG,IAAI,KAAK,KAAK;IAC/B,IAAIgC,OAAO,GAAG,IAAI,CAACP,KAAK;IACxB,IAAIQ,OAAO,GAAGC,IAAI,CAACC,GAAG;IACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;IACtB,IAAIC,UAAU,GAAGC,QAAQ;IACzB,IAAIC,eAAe,CAACR,OAAO,CAAC,EAAE;MAC5B,IAAIS,MAAM,GAAGH,UAAU;MACvB,IAAII,KAAK,GAAGJ,UAAU;MACtB,IAAIK,OAAO,GAAG,CAACL,UAAU;MACzB,IAAIM,QAAQ,GAAG,CAACN,UAAU;MAC1B,IAAIO,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,GAAGrJ,IAAI,IAAIA,IAAI,CAAC2G,UAAU,IAAI,IAAI,CAACT,mBAAmB,CAAC,CAAC;MACjE1Q,IAAI,CAAC8T,SAAS,EAAE,UAAUC,KAAK,EAAExL,EAAE,EAAE;QACnC,IAAIwL,KAAK,CAACvB,KAAK,KAAKO,OAAO,EAAE;UAC3B,IAAIiB,MAAM,GAAGlB,KAAK,GAAGiB,KAAK,CAACxE,KAAK,CAAC,CAAC,CAACoB,OAAO,CAACsD,SAAS,CAAC,CAAC,CAACC,SAAS,GAAGH,KAAK,CAACjD,cAAc,CAACzQ,KAAK,CAACmK,IAAI,CAAC,CAAC;UACpG,IAAI2J,YAAY,GAAGJ,KAAK,CAAC1E,MAAM,CAAC,CAAC,CAAC+E,qBAAqB,CAAC,CAAC;UACzDZ,MAAM,GAAGR,OAAO,CAACmB,YAAY,CAACE,IAAI,EAAEb,MAAM,CAAC;UAC3CC,KAAK,GAAGT,OAAO,CAACmB,YAAY,CAACG,GAAG,EAAEb,KAAK,CAAC;UACxCC,OAAO,GAAGP,OAAO,CAACgB,YAAY,CAACI,KAAK,EAAEb,OAAO,CAAC;UAC9CC,QAAQ,GAAGR,OAAO,CAACgB,YAAY,CAACK,MAAM,EAAEb,QAAQ,CAAC;UACjDC,YAAY,CAAClB,IAAI,CAAC;YAChBpI,GAAG,EAAE0J,MAAM;YACXK,IAAI,EAAEF,YAAY,CAACE,IAAI;YACvBC,GAAG,EAAEH,YAAY,CAACG;UACpB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACFd,MAAM,IAAIK,KAAK;MACfJ,KAAK,IAAII,KAAK;MACdH,OAAO,IAAIG,KAAK;MAChBF,QAAQ,IAAIE,KAAK;MACjB,IAAIjH,KAAK,GAAG8G,OAAO,GAAGF,MAAM;MAC5B,IAAI3G,MAAM,GAAG8G,QAAQ,GAAGF,KAAK;MAC7B,IAAIgB,YAAY,GAAGvP,WAAW,CAACwP,YAAY,CAAC,CAAC;MAC7C,IAAIC,IAAI,GAAG7U,OAAO,CAAC2M,IAAI,CAACgI,YAAY,EAAE;QACpC/H,QAAQ,EAAEoG,KAAK,GAAG,KAAK,GAAG;MAC5B,CAAC,CAAC;MACF6B,IAAI,CAACxG,MAAM,CAAC;QACVvB,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC,CAAC;MACF,IAAIiG,KAAK,EAAE;QACT,IAAI8B,SAAS,GAAG,EAAE;QAClB5U,IAAI,CAAC4T,YAAY,EAAE,UAAUiB,IAAI,EAAE;UACjC,IAAIC,CAAC,GAAGD,IAAI,CAACR,IAAI,GAAGb,MAAM;UAC1B,IAAIuB,CAAC,GAAGF,IAAI,CAACP,GAAG,GAAGb,KAAK;UACxBmB,SAAS,IAAI,0BAA0B,GAAGE,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAG,KAAK,GAAGF,IAAI,CAACvK,GAAG,GAAG,MAAM;QACnF,CAAC,CAAC;QACFqK,IAAI,CAAChE,OAAO,CAACqE,UAAU,CAAC,CAAC,CAACd,SAAS,GAAGU,SAAS;QAC/C,IAAIpK,IAAI,CAACyK,wBAAwB,EAAE;UACjCN,IAAI,CAAChE,OAAO,CAACuE,kBAAkB,CAAC1K,IAAI,CAACyK,wBAAwB,CAAC;QAChE;QACAN,IAAI,CAACQ,kBAAkB,CAAC,CAAC;QACzB,OAAOR,IAAI,CAAChE,OAAO,CAACkB,SAAS,CAAC,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAIrH,IAAI,CAACyK,wBAAwB,EAAE;UACjCN,IAAI,CAACS,GAAG,CAAC,IAAIxT,OAAO,CAACyT,IAAI,CAAC;YACxBC,KAAK,EAAE;cACLR,CAAC,EAAE,CAAC;cACJC,CAAC,EAAE,CAAC;cACJnI,KAAK,EAAEA,KAAK;cACZC,MAAM,EAAEA;YACV,CAAC;YACD0I,KAAK,EAAE;cACLC,IAAI,EAAEhL,IAAI,CAACyK;YACb;UACF,CAAC,CAAC,CAAC;QACL;QACAjV,IAAI,CAAC4T,YAAY,EAAE,UAAUiB,IAAI,EAAE;UACjC,IAAIY,GAAG,GAAG,IAAI7T,OAAO,CAAC8T,KAAK,CAAC;YAC1BH,KAAK,EAAE;cACLT,CAAC,EAAED,IAAI,CAACR,IAAI,GAAGR,KAAK,GAAGL,MAAM;cAC7BuB,CAAC,EAAEF,IAAI,CAACP,GAAG,GAAGT,KAAK,GAAGJ,KAAK;cAC3BkC,KAAK,EAAEd,IAAI,CAACvK;YACd;UACF,CAAC,CAAC;UACFqK,IAAI,CAACS,GAAG,CAACK,GAAG,CAAC;QACf,CAAC,CAAC;QACFd,IAAI,CAACQ,kBAAkB,CAAC,CAAC;QACzB,OAAOV,YAAY,CAAC5C,SAAS,CAAC,QAAQ,IAAIrH,IAAI,IAAIA,IAAI,CAACuG,IAAI,IAAI,KAAK,CAAC,CAAC;MACxE;IACF,CAAC,MAAM;MACL,OAAO,IAAI,CAACe,UAAU,CAACtH,IAAI,CAAC;IAC9B;EACF,CAAC;EACDH,OAAO,CAACzB,SAAS,CAACgN,cAAc,GAAG,UAAUC,MAAM,EAAEC,KAAK,EAAE;IAC1D,OAAOvM,cAAc,CAAC,IAAI,EAAE,gBAAgB,EAAEsM,MAAM,EAAEC,KAAK,CAAC;EAC9D,CAAC;EACDzL,OAAO,CAACzB,SAAS,CAACmN,gBAAgB,GAAG,UAAUF,MAAM,EAAEC,KAAK,EAAE;IAC5D,OAAOvM,cAAc,CAAC,IAAI,EAAE,kBAAkB,EAAEsM,MAAM,EAAEC,KAAK,CAAC;EAChE,CAAC;EACD;AACF;AACA;AACA;AACA;EACEzL,OAAO,CAACzB,SAAS,CAACoN,YAAY,GAAG,UAAUH,MAAM,EAAEC,KAAK,EAAE;IACxD,IAAI,IAAI,CAACxH,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAIuG,OAAO,GAAG,IAAI,CAACC,MAAM;IACzB,IAAIkH,MAAM;IACV,IAAIC,UAAU,GAAG1S,SAAS,CAAC2S,WAAW,CAACrH,OAAO,EAAE+G,MAAM,CAAC;IACvD7V,IAAI,CAACkW,UAAU,EAAE,UAAUE,MAAM,EAAEC,GAAG,EAAE;MACtCA,GAAG,CAAClW,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAIH,IAAI,CAACoW,MAAM,EAAE,UAAUE,KAAK,EAAE;QAC1D,IAAIC,QAAQ,GAAGD,KAAK,CAACE,gBAAgB;QACrC,IAAID,QAAQ,IAAIA,QAAQ,CAACE,YAAY,EAAE;UACrCR,MAAM,GAAGA,MAAM,IAAI,CAAC,CAACM,QAAQ,CAACE,YAAY,CAACX,KAAK,CAAC;QACnD,CAAC,MAAM,IAAIO,GAAG,KAAK,cAAc,EAAE;UACjC,IAAI/D,IAAI,GAAG,IAAI,CAAC1H,UAAU,CAAC0L,KAAK,CAAC/D,QAAQ,CAAC;UAC1C,IAAID,IAAI,IAAIA,IAAI,CAACmE,YAAY,EAAE;YAC7BR,MAAM,GAAGA,MAAM,IAAI3D,IAAI,CAACmE,YAAY,CAACX,KAAK,EAAEQ,KAAK,CAAC;UACpD,CAAC,MAAM;YACL,IAAIjL,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;cACzC5G,IAAI,CAAC2R,GAAG,GAAG,IAAI,IAAI/D,IAAI,GAAG,kDAAkD,GAAG,yCAAyC,CAAC,CAAC;YAC5H;UACF;QACF,CAAC,MAAM;UACL,IAAIjH,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;YACzC5G,IAAI,CAAC2R,GAAG,GAAG,iCAAiC,CAAC;UAC/C;QACF;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,CAAC,CAACJ,MAAM;EACjB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE5L,OAAO,CAACzB,SAAS,CAAC8N,SAAS,GAAG,UAAUb,MAAM,EAAEc,UAAU,EAAE;IAC1D,IAAI7H,OAAO,GAAG,IAAI,CAACC,MAAM;IACzB,IAAI6H,YAAY,GAAGpT,SAAS,CAAC2S,WAAW,CAACrH,OAAO,EAAE+G,MAAM,EAAE;MACxDgB,eAAe,EAAE;IACnB,CAAC,CAAC;IACF,IAAIC,WAAW,GAAGF,YAAY,CAACE,WAAW;IAC1C,IAAIzL,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACwL,WAAW,EAAE;QAChBpS,IAAI,CAAC,oCAAoC,CAAC;MAC5C;IACF;IACA,IAAIqS,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;IAChC,IAAIC,eAAe,GAAGL,YAAY,CAACM,cAAc,CAAC,iBAAiB,CAAC,GAAGN,YAAY,CAACK,eAAe,GAAGL,YAAY,CAACM,cAAc,CAAC,WAAW,CAAC,GAAGH,IAAI,CAACI,eAAe,CAACP,YAAY,CAAC1K,SAAS,CAAC,GAAG,IAAI;IACpM,OAAO+K,eAAe,IAAI,IAAI,GAAG3S,qBAAqB,CAACyS,IAAI,EAAEE,eAAe,EAAEN,UAAU,CAAC,GAAGtS,iBAAiB,CAAC0S,IAAI,EAAEJ,UAAU,CAAC;EACjI,CAAC;EACD;AACF;AACA;EACEtM,OAAO,CAACzB,SAAS,CAACwO,uBAAuB,GAAG,UAAUC,cAAc,EAAE;IACpE,OAAO,IAAI,CAACvM,cAAc,CAACuM,cAAc,CAAC9E,QAAQ,CAAC;EACrD,CAAC;EACD;AACF;AACA;EACElI,OAAO,CAACzB,SAAS,CAAC0O,oBAAoB,GAAG,UAAUR,WAAW,EAAE;IAC9D,OAAO,IAAI,CAAClM,UAAU,CAACkM,WAAW,CAACvE,QAAQ,CAAC;EAC9C,CAAC;EACDlI,OAAO,CAACzB,SAAS,CAACsF,WAAW,GAAG,YAAY;IAC1C,IAAIzD,KAAK,GAAG,IAAI;IAChBzK,IAAI,CAACuX,iBAAiB,EAAE,UAAUC,OAAO,EAAE;MACzC,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAa9I,CAAC,EAAE;QACzB,IAAIG,OAAO,GAAGrE,KAAK,CAAC6F,QAAQ,CAAC,CAAC;QAC9B,IAAItE,EAAE,GAAG2C,CAAC,CAAC+I,MAAM;QACjB,IAAIC,MAAM;QACV,IAAIC,WAAW,GAAGJ,OAAO,KAAK,WAAW;QACzC;QACA,IAAII,WAAW,EAAE;UACfD,MAAM,GAAG,CAAC,CAAC;QACb,CAAC,MAAM;UACL3L,EAAE,IAAIjH,mBAAmB,CAACiH,EAAE,EAAE,UAAU6L,MAAM,EAAE;YAC9C,IAAI5L,MAAM,GAAGpK,SAAS,CAACgW,MAAM,CAAC;YAC9B,IAAI5L,MAAM,IAAIA,MAAM,CAACC,SAAS,IAAI,IAAI,EAAE;cACtC,IAAI4L,SAAS,GAAG7L,MAAM,CAAC6L,SAAS,IAAIhJ,OAAO,CAACiJ,gBAAgB,CAAC9L,MAAM,CAACI,WAAW,CAAC;cAChFsL,MAAM,GAAGG,SAAS,IAAIA,SAAS,CAACE,aAAa,CAAC/L,MAAM,CAACC,SAAS,EAAED,MAAM,CAACgM,QAAQ,EAAEjM,EAAE,CAAC,IAAI,CAAC,CAAC;cAC1F,OAAO,IAAI;YACb;YACA;YAAA,KACK,IAAIC,MAAM,CAACiM,SAAS,EAAE;cACzBP,MAAM,GAAGpX,MAAM,CAAC,CAAC,CAAC,EAAE0L,MAAM,CAACiM,SAAS,CAAC;cACrC,OAAO,IAAI;YACb;UACF,CAAC,EAAE,IAAI,CAAC;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIP,MAAM,EAAE;UACV,IAAIzF,aAAa,GAAGyF,MAAM,CAACzF,aAAa;UACxC,IAAIiG,cAAc,GAAGR,MAAM,CAACQ,cAAc;UAC1C;UACA;UACA;UACA;UACA;UACA,IAAIjG,aAAa,KAAK,UAAU,IAAIA,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,UAAU,EAAE;YACjGA,aAAa,GAAG,QAAQ;YACxBiG,cAAc,GAAGR,MAAM,CAACtL,WAAW;UACrC;UACA,IAAIiK,KAAK,GAAGpE,aAAa,IAAIiG,cAAc,IAAI,IAAI,IAAIrJ,OAAO,CAACsJ,YAAY,CAAClG,aAAa,EAAEiG,cAAc,CAAC;UAC1G,IAAI7F,IAAI,GAAGgE,KAAK,IAAI7L,KAAK,CAAC6L,KAAK,CAAClE,QAAQ,KAAK,QAAQ,GAAG,YAAY,GAAG,gBAAgB,CAAC,CAACkE,KAAK,CAAC/D,QAAQ,CAAC;UACxG,IAAIlH,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;YACzC;YACA;YACA;YACA,IAAI,CAACsM,WAAW,IAAI,EAAEtB,KAAK,IAAIhE,IAAI,CAAC,EAAE;cACpC5N,IAAI,CAAC,0CAA0C,CAAC;YAClD;UACF;UACAiT,MAAM,CAACU,KAAK,GAAG1J,CAAC;UAChBgJ,MAAM,CAAC5G,IAAI,GAAGyG,OAAO;UACrB/M,KAAK,CAAC6N,gBAAgB,CAACC,SAAS,GAAG;YACjCC,QAAQ,EAAExM,EAAE;YACZyM,WAAW,EAAEd,MAAM;YACnBrB,KAAK,EAAEA,KAAK;YACZhE,IAAI,EAAEA;UACR,CAAC;UACD7H,KAAK,CAACiO,OAAO,CAAClB,OAAO,EAAEG,MAAM,CAAC;QAChC;MACF,CAAC;MACD;MACA;MACA;MACA;MACA;MACAF,OAAO,CAACkB,oBAAoB,GAAG,IAAI;MACnClO,KAAK,CAAC+B,GAAG,CAACvD,EAAE,CAACuO,OAAO,EAAEC,OAAO,EAAEhN,KAAK,CAAC;IACvC,CAAC,CAAC;IACFzK,IAAI,CAAC4Y,cAAc,EAAE,UAAUC,UAAU,EAAEC,SAAS,EAAE;MACpDrO,KAAK,CAACwD,cAAc,CAAChF,EAAE,CAAC6P,SAAS,EAAE,UAAUT,KAAK,EAAE;QAClD,IAAI,CAACK,OAAO,CAACI,SAAS,EAAET,KAAK,CAAC;MAChC,CAAC,EAAE5N,KAAK,CAAC;IACX,CAAC,CAAC;IACF;IACA;IACAzK,IAAI,CAAC,CAAC,eAAe,CAAC,EAAE,UAAU8Y,SAAS,EAAE;MAC3CrO,KAAK,CAACwD,cAAc,CAAChF,EAAE,CAAC6P,SAAS,EAAE,UAAUT,KAAK,EAAE;QAClD,IAAI,CAACK,OAAO,CAACI,SAAS,EAAET,KAAK,CAAC;MAChC,CAAC,EAAE5N,KAAK,CAAC;IACX,CAAC,CAAC;IACF9F,wBAAwB,CAAC,IAAI,CAACsJ,cAAc,EAAE,IAAI,EAAE,IAAI,CAACR,IAAI,CAAC;EAChE,CAAC;EACDpD,OAAO,CAACzB,SAAS,CAACP,UAAU,GAAG,YAAY;IACzC,OAAO,IAAI,CAACiG,SAAS;EACvB,CAAC;EACDjE,OAAO,CAACzB,SAAS,CAACmQ,KAAK,GAAG,YAAY;IACpC,IAAI,IAAI,CAACzK,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAI,CAACkH,SAAS,CAAC;MACbuJ,MAAM,EAAE;IACV,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD3O,OAAO,CAACzB,SAAS,CAACqQ,OAAO,GAAG,YAAY;IACtC,IAAI,IAAI,CAAC3K,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAI,CAAC+F,SAAS,GAAG,IAAI;IACrB,IAAIhE,GAAG,GAAG,IAAI,CAAC+E,MAAM,CAAC,CAAC;IACvB,IAAI/E,GAAG,EAAE;MACP9G,SAAS,CAAC0V,YAAY,CAAC,IAAI,CAAC7J,MAAM,CAAC,CAAC,EAAE8J,iBAAiB,EAAE,EAAE,CAAC;IAC9D;IACA,IAAIpF,KAAK,GAAG,IAAI;IAChB,IAAIvG,GAAG,GAAGuG,KAAK,CAACtG,IAAI;IACpB,IAAIqB,OAAO,GAAGiF,KAAK,CAAChF,MAAM;IAC1B/O,IAAI,CAAC+T,KAAK,CAAClJ,gBAAgB,EAAE,UAAUwH,SAAS,EAAE;MAChDA,SAAS,CAAC4G,OAAO,CAACnK,OAAO,EAAEtB,GAAG,CAAC;IACjC,CAAC,CAAC;IACFxN,IAAI,CAAC+T,KAAK,CAACpJ,YAAY,EAAE,UAAUoJ,KAAK,EAAE;MACxCA,KAAK,CAACkF,OAAO,CAACnK,OAAO,EAAEtB,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF;IACAuG,KAAK,CAACvH,GAAG,CAACyM,OAAO,CAAC,CAAC;IACnB;IACA;IACAlF,KAAK,CAAC9I,IAAI,GAAG8I,KAAK,CAAChF,MAAM,GAAGgF,KAAK,CAACnJ,UAAU,GAAGmJ,KAAK,CAACjJ,cAAc,GAAGiJ,KAAK,CAACpJ,YAAY,GAAGoJ,KAAK,CAAClJ,gBAAgB,GAAGkJ,KAAK,CAAC/F,UAAU,GAAG+F,KAAK,CAACtG,IAAI,GAAGsG,KAAK,CAACvH,GAAG,GAAGuH,KAAK,CAAC7G,iBAAiB,GAAG6G,KAAK,CAAC3G,MAAM,GAAG2G,KAAK,CAACxG,YAAY,GAAGwG,KAAK,CAAC9F,cAAc,GAAG,IAAI;IACzP,OAAO6F,SAAS,CAACC,KAAK,CAACxL,EAAE,CAAC;EAC5B,CAAC;EACD;AACF;AACA;EACE8B,OAAO,CAACzB,SAAS,CAACuF,MAAM,GAAG,UAAU3D,IAAI,EAAE;IACzC,IAAI,IAAI,CAACjD,mBAAmB,CAAC,EAAE;MAC7B,IAAI8D,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;QACzC7G,KAAK,CAAC,oDAAoD,CAAC;MAC7D;MACA;IACF;IACA,IAAI,IAAI,CAAC6J,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAI,CAACiE,GAAG,CAAC2B,MAAM,CAAC3D,IAAI,CAAC;IACrB,IAAIsE,OAAO,GAAG,IAAI,CAACC,MAAM;IACzB;IACA,IAAI,CAACqK,UAAU,IAAI,IAAI,CAACA,UAAU,CAACjL,MAAM,CAAC,CAAC;IAC3C,IAAI,CAACW,OAAO,EAAE;MACZ;IACF;IACA,IAAIuK,WAAW,GAAGvK,OAAO,CAACwK,WAAW,CAAC,OAAO,CAAC;IAC9C,IAAI9K,MAAM,GAAGhE,IAAI,IAAIA,IAAI,CAACgE,MAAM;IAChC;IACA;IACA;IACA,IAAI,IAAI,CAAChH,cAAc,CAAC,EAAE;MACxB,IAAIgH,MAAM,IAAI,IAAI,EAAE;QAClBA,MAAM,GAAG,IAAI,CAAChH,cAAc,CAAC,CAACgH,MAAM;MACtC;MACA6K,WAAW,GAAG,IAAI;MAClB,IAAI,CAAC7R,cAAc,CAAC,GAAG,IAAI;IAC7B;IACA,IAAI,CAACD,mBAAmB,CAAC,GAAG,IAAI;IAChC,IAAI;MACF8R,WAAW,IAAIlQ,OAAO,CAAC,IAAI,CAAC;MAC5BG,aAAa,CAACmF,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAE;QAC9BqG,IAAI,EAAE,QAAQ;QACd3C,SAAS,EAAE7N,MAAM,CAAC;UAChB;UACAgZ,QAAQ,EAAE;QACZ,CAAC,EAAE/O,IAAI,IAAIA,IAAI,CAAC4D,SAAS;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,CAAC,EAAE;MACV,IAAI,CAACpH,mBAAmB,CAAC,GAAG,KAAK;MACjC,MAAMoH,CAAC;IACT;IACA,IAAI,CAACpH,mBAAmB,CAAC,GAAG,KAAK;IACjCmC,mBAAmB,CAACgB,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;IACtC7E,mBAAmB,CAACe,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;EACxC,CAAC;EACDnE,OAAO,CAACzB,SAAS,CAAC4Q,WAAW,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACnD,IAAI,IAAI,CAACpL,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAIrI,QAAQ,CAACuZ,IAAI,CAAC,EAAE;MAClBC,GAAG,GAAGD,IAAI;MACVA,IAAI,GAAG,EAAE;IACX;IACAA,IAAI,GAAGA,IAAI,IAAI,SAAS;IACxB,IAAI,CAACE,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,cAAc,CAACH,IAAI,CAAC,EAAE;MACzB,IAAIpO,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;QACzC5G,IAAI,CAAC,kBAAkB,GAAG+U,IAAI,GAAG,cAAc,CAAC;MAClD;MACA;IACF;IACA,IAAIzN,EAAE,GAAG4N,cAAc,CAACH,IAAI,CAAC,CAAC,IAAI,CAAChM,IAAI,EAAEiM,GAAG,CAAC;IAC7C,IAAInN,EAAE,GAAG,IAAI,CAACC,GAAG;IACjB,IAAI,CAAC4M,UAAU,GAAGpN,EAAE;IACpBO,EAAE,CAAC6I,GAAG,CAACpJ,EAAE,CAAC;EACZ,CAAC;EACD;AACF;AACA;EACE3B,OAAO,CAACzB,SAAS,CAAC+Q,WAAW,GAAG,YAAY;IAC1C,IAAI,IAAI,CAACrL,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAI,CAAC6Q,UAAU,IAAI,IAAI,CAAC5M,GAAG,CAACqN,MAAM,CAAC,IAAI,CAACT,UAAU,CAAC;IACnD,IAAI,CAACA,UAAU,GAAG,IAAI;EACxB,CAAC;EACD/O,OAAO,CAACzB,SAAS,CAACkR,mBAAmB,GAAG,UAAUC,QAAQ,EAAE;IAC1D,IAAIC,OAAO,GAAGzZ,MAAM,CAAC,CAAC,CAAC,EAAEwZ,QAAQ,CAAC;IAClCC,OAAO,CAACjJ,IAAI,GAAG6H,cAAc,CAACmB,QAAQ,CAAChJ,IAAI,CAAC;IAC5C,OAAOiJ,OAAO;EAChB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3P,OAAO,CAACzB,SAAS,CAACqR,cAAc,GAAG,UAAUD,OAAO,EAAEE,GAAG,EAAE;IACzD,IAAI,IAAI,CAAC5L,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAI,CAACrI,QAAQ,CAACga,GAAG,CAAC,EAAE;MAClBA,GAAG,GAAG;QACJ1L,MAAM,EAAE,CAAC,CAAC0L;MACZ,CAAC;IACH;IACA,IAAI,CAACC,OAAO,CAACH,OAAO,CAACjJ,IAAI,CAAC,EAAE;MAC1B;IACF;IACA;IACA,IAAI,CAAC,IAAI,CAAChC,MAAM,EAAE;MAChB;IACF;IACA;IACA,IAAI,IAAI,CAACxH,mBAAmB,CAAC,EAAE;MAC7B,IAAI,CAACwD,eAAe,CAAC2H,IAAI,CAACsH,OAAO,CAAC;MAClC;IACF;IACA,IAAIxL,MAAM,GAAG0L,GAAG,CAAC1L,MAAM;IACvB/E,gBAAgB,CAACiB,IAAI,CAAC,IAAI,EAAEsP,OAAO,EAAExL,MAAM,CAAC;IAC5C,IAAIrB,KAAK,GAAG+M,GAAG,CAAC/M,KAAK;IACrB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACX,GAAG,CAACW,KAAK,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIA,KAAK,KAAK,KAAK,IAAInM,GAAG,CAACoZ,OAAO,CAACC,MAAM,EAAE;MAChD;MACA;MACA;MACA;MACA;MACA,IAAI,CAACnN,iBAAiB,CAAC,CAAC;IAC1B;IACAxD,mBAAmB,CAACgB,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;IACtC7E,mBAAmB,CAACe,IAAI,CAAC,IAAI,EAAE8D,MAAM,CAAC;EACxC,CAAC;EACDnE,OAAO,CAACzB,SAAS,CAAC0R,iBAAiB,GAAG,YAAY;IAChDrV,SAAS,CAACyT,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC3J,MAAM,EAAE,IAAI,CAACtB,IAAI,EAAE;MAC/D;MACA;MACA8M,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EACDlQ,OAAO,CAACzB,SAAS,CAAC4R,UAAU,GAAG,UAAU7C,MAAM,EAAE;IAC/C,IAAI,IAAI,CAACrJ,SAAS,EAAE;MAClBhG,eAAe,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IACF;IACA,IAAI8D,WAAW,GAAGsL,MAAM,CAACtL,WAAW;IACpC,IAAIyC,OAAO,GAAG,IAAI,CAACwB,QAAQ,CAAC,CAAC;IAC7B,IAAIwG,WAAW,GAAGhI,OAAO,CAACiJ,gBAAgB,CAAC1L,WAAW,CAAC;IACvD,IAAIhB,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzCvL,MAAM,CAAC4X,MAAM,CAACZ,IAAI,IAAID,WAAW,CAAC;IACpC;IACAA,WAAW,CAAC0D,UAAU,CAAC7C,MAAM,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC3J,UAAU,CAACY,UAAU,GAAG,IAAI;IACjC,IAAI,CAACW,KAAK,CAAC,CAAC,CAACa,MAAM,CAAC,CAAC;EACvB,CAAC;EACD;EACA;EACA/F,OAAO,CAACoQ,aAAa,GAAG,YAAY;IAClCtR,OAAO,GAAG,SAAVA,OAAOA,CAAauR,KAAK,EAAE;MACzB,IAAInM,SAAS,GAAGmM,KAAK,CAAC1M,UAAU;MAChCO,SAAS,CAACoM,gBAAgB,CAACD,KAAK,CAAC3L,MAAM,CAAC;MACxCR,SAAS,CAACqM,iBAAiB,CAAC,CAAC;MAC7BxR,WAAW,CAACsR,KAAK,EAAE,IAAI,CAAC;MACxBtR,WAAW,CAACsR,KAAK,EAAE,KAAK,CAAC;MACzBnM,SAAS,CAACsM,IAAI,CAAC,CAAC;IAClB,CAAC;IACD;AACJ;AACA;IACIzR,WAAW,GAAG,SAAdA,WAAWA,CAAasR,KAAK,EAAEI,WAAW,EAAE;MAC1C,IAAIhM,OAAO,GAAG4L,KAAK,CAAC3L,MAAM;MAC1B,IAAIR,SAAS,GAAGmM,KAAK,CAAC1M,UAAU;MAChC,IAAI+M,QAAQ,GAAGD,WAAW,GAAGJ,KAAK,CAAC7P,gBAAgB,GAAG6P,KAAK,CAAC/P,YAAY;MACxE,IAAIqQ,OAAO,GAAGF,WAAW,GAAGJ,KAAK,CAAC5P,cAAc,GAAG4P,KAAK,CAAC9P,UAAU;MACnE,IAAI2B,EAAE,GAAGmO,KAAK,CAAClO,GAAG;MAClB,IAAIgB,GAAG,GAAGkN,KAAK,CAACjN,IAAI;MACpB,KAAK,IAAIwN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAC3S,MAAM,EAAE6S,CAAC,EAAE,EAAE;QACxCF,QAAQ,CAACE,CAAC,CAAC,CAACC,OAAO,GAAG,KAAK;MAC7B;MACAJ,WAAW,GAAGhM,OAAO,CAACqD,aAAa,CAAC,UAAUD,aAAa,EAAEoE,KAAK,EAAE;QAClEpE,aAAa,KAAK,QAAQ,IAAIiJ,SAAS,CAAC7E,KAAK,CAAC;MAChD,CAAC,CAAC,GAAGxH,OAAO,CAACsM,UAAU,CAACD,SAAS,CAAC;MAClC,SAASA,SAASA,CAAC7E,KAAK,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA;QACA,IAAI+E,cAAc,GAAG/E,KAAK,CAACgF,gBAAgB;QAC3C;QACAhF,KAAK,CAACgF,gBAAgB,GAAG,KAAK;QAC9B;QACA,IAAIC,MAAM,GAAG,MAAM,GAAGjF,KAAK,CAAC/N,EAAE,GAAG,GAAG,GAAG+N,KAAK,CAACvF,IAAI;QACjD,IAAIuB,IAAI,GAAG,CAAC+I,cAAc,IAAIL,OAAO,CAACO,MAAM,CAAC;QAC7C,IAAI,CAACjJ,IAAI,EAAE;UACT,IAAIkJ,SAAS,GAAGvX,cAAc,CAACqS,KAAK,CAACvF,IAAI,CAAC;UAC1C,IAAI0K,KAAK,GAAGX,WAAW,GAAGpZ,aAAa,CAACga,QAAQ,CAACF,SAAS,CAACG,IAAI,EAAEH,SAAS,CAACI,GAAG,CAAC;UAC/E;UACA;UACA;UACA;UACA;UACAja,SAAS,CAAC+Z,QAAQ,CAACF,SAAS,CAACI,GAAG,CAAC;UACjC,IAAIvQ,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;YACzCvL,MAAM,CAAC0b,KAAK,EAAED,SAAS,CAACI,GAAG,GAAG,kBAAkB,CAAC;UACnD;UACAtJ,IAAI,GAAG,IAAImJ,KAAK,CAAC,CAAC;UAClBnJ,IAAI,CAAC7F,IAAI,CAACqC,OAAO,EAAEtB,GAAG,CAAC;UACvBwN,OAAO,CAACO,MAAM,CAAC,GAAGjJ,IAAI;UACtByI,QAAQ,CAACrI,IAAI,CAACJ,IAAI,CAAC;UACnB/F,EAAE,CAAC6I,GAAG,CAAC9C,IAAI,CAACE,KAAK,CAAC;QACpB;QACA8D,KAAK,CAAC/D,QAAQ,GAAGD,IAAI,CAACuJ,IAAI,GAAGN,MAAM;QACnCjJ,IAAI,CAAC4I,OAAO,GAAG,IAAI;QACnB5I,IAAI,CAACwJ,OAAO,GAAGxF,KAAK;QACpBhE,IAAI,CAACE,KAAK,CAACuJ,iBAAiB,GAAG;UAC7B3J,QAAQ,EAAEkE,KAAK,CAAClE,QAAQ;UACxB4J,KAAK,EAAE1F,KAAK,CAAC6B;QACf,CAAC;QACD,CAAC2C,WAAW,IAAIvM,SAAS,CAACnF,WAAW,CAACkJ,IAAI,EAAEgE,KAAK,EAAExH,OAAO,EAAEtB,GAAG,CAAC;MAClE;MACA,KAAK,IAAIyN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAC3S,MAAM,GAAG;QACpC,IAAIkK,IAAI,GAAGyI,QAAQ,CAACE,CAAC,CAAC;QACtB,IAAI,CAAC3I,IAAI,CAAC4I,OAAO,EAAE;UACjB,CAACJ,WAAW,IAAIxI,IAAI,CAAC2J,UAAU,CAAChD,OAAO,CAAC,CAAC;UACzC1M,EAAE,CAACsN,MAAM,CAACvH,IAAI,CAACE,KAAK,CAAC;UACrBF,IAAI,CAAC2G,OAAO,CAACnK,OAAO,EAAEtB,GAAG,CAAC;UAC1BuN,QAAQ,CAACmB,MAAM,CAACjB,CAAC,EAAE,CAAC,CAAC;UACrB,IAAID,OAAO,CAAC1I,IAAI,CAACuJ,IAAI,CAAC,KAAKvJ,IAAI,EAAE;YAC/B,OAAO0I,OAAO,CAAC1I,IAAI,CAACuJ,IAAI,CAAC;UAC3B;UACAvJ,IAAI,CAACuJ,IAAI,GAAGvJ,IAAI,CAACE,KAAK,CAACuJ,iBAAiB,GAAG,IAAI;QACjD,CAAC,MAAM;UACLd,CAAC,EAAE;QACL;MACF;IACF,CAAC;IACD5R,cAAc,GAAG,SAAjBA,cAAcA,CAAaqR,KAAK,EAAE1S,MAAM,EAAEgS,OAAO,EAAE5H,QAAQ,EAAE+J,OAAO,EAAE;MACpE,IAAIrN,OAAO,GAAG4L,KAAK,CAAC3L,MAAM;MAC1BD,OAAO,CAACsN,gBAAgB,CAACpC,OAAO,CAAC;MACjC;MACA,IAAI,CAAC5H,QAAQ,EAAE;QACb;QACA;QACA;QACApS,IAAI,CAAC,EAAE,CAACqc,MAAM,CAAC3B,KAAK,CAAC7P,gBAAgB,CAAC,CAACwR,MAAM,CAAC3B,KAAK,CAAC/P,YAAY,CAAC,EAAE2R,QAAQ,CAAC;QAC5E;MACF;MACA,IAAIC,KAAK,GAAG,CAAC,CAAC;MACdA,KAAK,CAACnK,QAAQ,GAAG,IAAI,CAAC,GAAG4H,OAAO,CAAC5H,QAAQ,GAAG,IAAI,CAAC;MACjDmK,KAAK,CAACnK,QAAQ,GAAG,OAAO,CAAC,GAAG4H,OAAO,CAAC5H,QAAQ,GAAG,OAAO,CAAC;MACvDmK,KAAK,CAACnK,QAAQ,GAAG,MAAM,CAAC,GAAG4H,OAAO,CAAC5H,QAAQ,GAAG,MAAM,CAAC;MACrD,IAAIoK,SAAS,GAAG;QACdpK,QAAQ,EAAEA,QAAQ;QAClBmK,KAAK,EAAEA;MACT,CAAC;MACDJ,OAAO,KAAKK,SAAS,CAACL,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;MAC1C,IAAIM,eAAe,GAAGzC,OAAO,CAACyC,eAAe;MAC7C,IAAIC,kBAAkB;MACtB,IAAID,eAAe,IAAI,IAAI,EAAE;QAC3BC,kBAAkB,GAAGlc,aAAa,CAAC,CAAC;QACpCR,IAAI,CAACwD,SAAS,CAACmZ,gBAAgB,CAACF,eAAe,CAAC,EAAE,UAAUlU,EAAE,EAAE;UAC9D,IAAIqU,OAAO,GAAGpZ,SAAS,CAACqZ,mBAAmB,CAACtU,EAAE,EAAE,IAAI,CAAC;UACrD,IAAIqU,OAAO,IAAI,IAAI,EAAE;YACnBF,kBAAkB,CAACtQ,GAAG,CAACwQ,OAAO,EAAE,IAAI,CAAC;UACvC;QACF,CAAC,CAAC;MACJ;MACA;MACA9N,OAAO,IAAIA,OAAO,CAACqD,aAAa,CAACqK,SAAS,EAAE,UAAUlG,KAAK,EAAE;QAC3D,IAAIwG,UAAU,GAAGJ,kBAAkB,IAAIA,kBAAkB,CAACxL,GAAG,CAACoF,KAAK,CAAC/N,EAAE,CAAC,IAAI,IAAI;QAC/E,IAAIuU,UAAU,EAAE;UACd;QACF;QACA;QACA,IAAIxa,iBAAiB,CAAC0X,OAAO,CAAC,EAAE;UAC9B,IAAI1D,KAAK,YAAY7U,WAAW,EAAE;YAChC,IAAIuY,OAAO,CAACjJ,IAAI,KAAKxO,qBAAqB,IAAI,CAACyX,OAAO,CAAC+C,OAAO,IAAI,CAACzG,KAAK,CAACpF,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE;cACtGjP,8BAA8B,CAACqU,KAAK,EAAE0D,OAAO,EAAEU,KAAK,CAACjN,IAAI,CAAC;YAC5D;UACF,CAAC,MAAM;YACL,IAAIuP,EAAE,GAAG5Z,gCAAgC,CAACkT,KAAK,CAAClE,QAAQ,EAAEkE,KAAK,CAAC6B,cAAc,EAAE6B,OAAO,CAACP,IAAI,EAAEiB,KAAK,CAACjN,IAAI,CAAC;cACvGwP,SAAS,GAAGD,EAAE,CAACC,SAAS;cACxBC,WAAW,GAAGF,EAAE,CAACE,WAAW;YAC9B,IAAIlD,OAAO,CAACjJ,IAAI,KAAKxO,qBAAqB,IAAI0a,SAAS,IAAI,CAACjD,OAAO,CAAC+C,OAAO,EAAE;cAC3E1Z,aAAa,CAACiT,KAAK,CAAClE,QAAQ,EAAEkE,KAAK,CAAC6B,cAAc,EAAEuC,KAAK,CAACjN,IAAI,CAAC;YACjE;YACA;YACA;YACA;YACA;YACA,IAAIyP,WAAW,EAAE;cACfld,IAAI,CAACkd,WAAW,EAAE,UAAUC,UAAU,EAAE;gBACtCnD,OAAO,CAACjJ,IAAI,KAAKxO,qBAAqB,GAAGM,aAAa,CAACsa,UAAU,CAAC,GAAGra,aAAa,CAACqa,UAAU,CAAC;cAChG,CAAC,CAAC;YACJ;UACF;QACF,CAAC,MAAM,IAAI9a,qBAAqB,CAAC2X,OAAO,CAAC,EAAE;UACzC;UACA,IAAI1D,KAAK,YAAY7U,WAAW,EAAE;YAChCS,0BAA0B,CAACoU,KAAK,EAAE0D,OAAO,EAAEU,KAAK,CAACjN,IAAI,CAAC;YACtDtL,4BAA4B,CAACmU,KAAK,CAAC;YACnCnM,kBAAkB,CAACuQ,KAAK,CAAC;UAC3B;QACF;MACF,CAAC,EAAEA,KAAK,CAAC;MACT5L,OAAO,IAAIA,OAAO,CAACqD,aAAa,CAACqK,SAAS,EAAE,UAAUlG,KAAK,EAAE;QAC3D,IAAIwG,UAAU,GAAGJ,kBAAkB,IAAIA,kBAAkB,CAACxL,GAAG,CAACoF,KAAK,CAAC/N,EAAE,CAAC,IAAI,IAAI;QAC/E,IAAIuU,UAAU,EAAE;UACd;QACF;QACA;QACAR,QAAQ,CAAC5B,KAAK,CAACtI,QAAQ,KAAK,QAAQ,GAAG,YAAY,GAAG,gBAAgB,CAAC,CAACkE,KAAK,CAAC/D,QAAQ,CAAC,CAAC;MAC1F,CAAC,EAAEmI,KAAK,CAAC;MACT,SAAS4B,QAAQA,CAAChK,IAAI,EAAE;QACtBA,IAAI,IAAIA,IAAI,CAAC4I,OAAO,IAAI5I,IAAI,CAACtK,MAAM,CAAC,IAAIsK,IAAI,CAACtK,MAAM,CAAC,CAACsK,IAAI,CAACwJ,OAAO,EAAEhN,OAAO,EAAE4L,KAAK,CAACjN,IAAI,EAAEuM,OAAO,CAAC;MAClG;IACF,CAAC;IACD1Q,aAAa,GAAG;MACd8T,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYpD,OAAO,EAAE;QACnC7Q,OAAO,CAAC,IAAI,CAAC;QACbG,aAAa,CAACmF,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAEsP,OAAO,EAAE;UACvC;UACA;UACA;UACA7J,aAAa,EAAE6J,OAAO,CAACqD,SAAS,IAAI;QACtC,CAAC,CAAC;MACJ,CAAC;MACD5O,MAAM,EAAE,SAARA,MAAMA,CAAYuL,OAAO,EAAEtL,YAAY,EAAE;QACvC,IAAII,OAAO,GAAG,IAAI,CAACC,MAAM;QACzB,IAAIvB,GAAG,GAAG,IAAI,CAACC,IAAI;QACnB,IAAIlB,EAAE,GAAG,IAAI,CAACC,GAAG;QACjB,IAAI8Q,WAAW,GAAG,IAAI,CAAC/P,YAAY;QACnC,IAAIgB,SAAS,GAAG,IAAI,CAACP,UAAU;QAC/B;QACA,IAAI,CAACc,OAAO,EAAE;UACZ;QACF;QACAA,OAAO,CAACsN,gBAAgB,CAACpC,OAAO,CAAC;QACjCzL,SAAS,CAACgP,WAAW,CAACzO,OAAO,EAAEkL,OAAO,CAAC;QACvCzL,SAAS,CAACW,kBAAkB,CAACJ,OAAO,CAAC;QACrC;QACA;QACA;QACA;QACA;QACAwO,WAAW,CAACE,MAAM,CAAC1O,OAAO,EAAEtB,GAAG,CAAC;QAChCe,SAAS,CAACY,yBAAyB,CAACL,OAAO,EAAEkL,OAAO,CAAC;QACrD;QACA;QACA;QACAxQ,iBAAiB,CAAC,IAAI,EAAEsF,OAAO,CAAC;QAChC;QACA;QACA;QACA;QACAwO,WAAW,CAAC7O,MAAM,CAACK,OAAO,EAAEtB,GAAG,CAAC;QAChCiQ,iBAAiB,CAAC3O,OAAO,CAAC;QAC1BP,SAAS,CAACa,kBAAkB,CAACN,OAAO,EAAEkL,OAAO,CAAC;QAC9ClQ,MAAM,CAAC,IAAI,EAAEgF,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,EAAEtL,YAAY,CAAC;QACjD;QACA,IAAIuC,eAAe,GAAGnC,OAAO,CAACoC,GAAG,CAAC,iBAAiB,CAAC,IAAI,aAAa;QACrE,IAAIwM,QAAQ,GAAG5O,OAAO,CAACoC,GAAG,CAAC,UAAU,CAAC;QACtC3E,EAAE,CAAC2I,kBAAkB,CAACjE,eAAe,CAAC;QACtC;QACA,IAAIyM,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,MAAM,EAAE;UAC3CnR,EAAE,CAACoR,WAAW,CAACD,QAAQ,CAAC;QAC1B;QACAzY,SAAS,CAACyT,OAAO,CAAC,aAAa,EAAE5J,OAAO,EAAEtB,GAAG,CAAC;MAChD,CAAC;MACDoQ,eAAe,EAAE,SAAjBA,eAAeA,CAAY5D,OAAO,EAAE;QAClC,IAAIvP,KAAK,GAAG,IAAI;QAChB,IAAIqE,OAAO,GAAG,IAAI,CAACC,MAAM;QACzB,IAAIvB,GAAG,GAAG,IAAI,CAACC,IAAI;QACnB;QACA,IAAI,CAACqB,OAAO,EAAE;UACZ;QACF;QACAA,OAAO,CAACsN,gBAAgB,CAACpC,OAAO,CAAC;QACjC;QACA,IAAI6D,kBAAkB,GAAG,EAAE;QAC3B/O,OAAO,CAACqD,aAAa,CAAC,UAAUD,aAAa,EAAEmF,cAAc,EAAE;UAC7D,IAAInF,aAAa,KAAK,QAAQ,EAAE;YAC9B;UACF;UACA,IAAI4L,aAAa,GAAGrT,KAAK,CAAC2M,uBAAuB,CAACC,cAAc,CAAC;UACjE,IAAIyG,aAAa,IAAIA,aAAa,CAAC5C,OAAO,EAAE;YAC1C,IAAI4C,aAAa,CAACF,eAAe,EAAE;cACjC,IAAI3H,MAAM,GAAG6H,aAAa,CAACF,eAAe,CAACvG,cAAc,EAAEvI,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,CAAC;cACjF/D,MAAM,IAAIA,MAAM,CAACxH,MAAM,IAAIoP,kBAAkB,CAACnL,IAAI,CAACoL,aAAa,CAAC;YACnE,CAAC,MAAM;cACLD,kBAAkB,CAACnL,IAAI,CAACoL,aAAa,CAAC;YACxC;UACF;QACF,CAAC,CAAC;QACF,IAAIC,cAAc,GAAGvd,aAAa,CAAC,CAAC;QACpCsO,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;UACxC,IAAIkH,SAAS,GAAGvT,KAAK,CAACG,UAAU,CAACkM,WAAW,CAACvE,QAAQ,CAAC;UACtD,IAAIyL,SAAS,CAACJ,eAAe,EAAE;YAC7B,IAAI3H,MAAM,GAAG+H,SAAS,CAACJ,eAAe,CAAC9G,WAAW,EAAEhI,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,CAAC;YAC1E/D,MAAM,IAAIA,MAAM,CAACxH,MAAM,IAAIsP,cAAc,CAAC3R,GAAG,CAAC0K,WAAW,CAACmH,GAAG,EAAE,CAAC,CAAC;UACnE,CAAC,MAAM;YACLF,cAAc,CAAC3R,GAAG,CAAC0K,WAAW,CAACmH,GAAG,EAAE,CAAC,CAAC;UACxC;QACF,CAAC,CAAC;QACFR,iBAAiB,CAAC3O,OAAO,CAAC;QAC1B;QACA;QACA,IAAI,CAACd,UAAU,CAACoB,kBAAkB,CAACN,OAAO,EAAEkL,OAAO,EAAE;UACnDkE,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAEJ;QACZ,CAAC,CAAC;QACF;QACA;QACA/T,YAAY,CAAC,IAAI,EAAE8E,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,EAAE,CAAC,CAAC,EAAE+D,cAAc,CAAC;QAC7D9Y,SAAS,CAACyT,OAAO,CAAC,aAAa,EAAE5J,OAAO,EAAEtB,GAAG,CAAC;MAChD,CAAC;MACD4Q,UAAU,EAAE,SAAZA,UAAUA,CAAYpE,OAAO,EAAE;QAC7B,IAAIlL,OAAO,GAAG,IAAI,CAACC,MAAM;QACzB;QACA,IAAI,CAACD,OAAO,EAAE;UACZ;QACF;QACAA,OAAO,CAACsN,gBAAgB,CAACpC,OAAO,CAAC;QACjCrY,SAAS,CAAC0c,gBAAgB,CAACrE,OAAO,EAAE,YAAY,CAAC;QACjDyD,iBAAiB,CAAC3O,OAAO,CAAC;QAC1B;QACA,IAAI,CAACd,UAAU,CAACoB,kBAAkB,CAACN,OAAO,EAAEkL,OAAO,EAAE;UACnDkE,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFpU,MAAM,CAAC,IAAI,EAAEgF,OAAO,EAAE,IAAI,CAACrB,IAAI,EAAEuM,OAAO,EAAE,CAAC,CAAC,CAAC;QAC7C/U,SAAS,CAACyT,OAAO,CAAC,aAAa,EAAE5J,OAAO,EAAE,IAAI,CAACrB,IAAI,CAAC;MACtD,CAAC;MACD6Q,YAAY,EAAE,SAAdA,YAAYA,CAAYtE,OAAO,EAAE;QAC/B;QACA,IAAIvP,KAAK,GAAG,IAAI;QAChB,IAAIqE,OAAO,GAAG,IAAI,CAACC,MAAM;QACzB;QACA,IAAI,CAACD,OAAO,EAAE;UACZ;QACF;QACAA,OAAO,CAACsN,gBAAgB,CAACpC,OAAO,CAAC;QACjC;QACAlL,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;UACxCA,WAAW,CAACE,OAAO,CAAC,CAAC,CAACuH,cAAc,CAAC,CAAC;QACxC,CAAC,CAAC;QACF;QACA5c,SAAS,CAAC0c,gBAAgB,CAACrE,OAAO,EAAE,cAAc,CAAC;QACnDyD,iBAAiB,CAAC3O,OAAO,CAAC;QAC1B;QACA,IAAI,CAACd,UAAU,CAACoB,kBAAkB,CAACN,OAAO,EAAEkL,OAAO,EAAE;UACnDrD,UAAU,EAAE,QAAQ;UACpBuH,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFpP,OAAO,CAACqD,aAAa,CAAC,UAAUD,aAAa,EAAEmF,cAAc,EAAE;UAC7D,IAAInF,aAAa,KAAK,QAAQ,EAAE;YAC9B,IAAI4L,aAAa,GAAGrT,KAAK,CAAC2M,uBAAuB,CAACC,cAAc,CAAC;YACjEyG,aAAa,IAAIA,aAAa,CAAC5C,OAAO,IAAI4C,aAAa,CAACQ,YAAY,CAACjH,cAAc,EAAEvI,OAAO,EAAErE,KAAK,CAACgD,IAAI,EAAEuM,OAAO,CAAC;UACpH;QACF,CAAC,CAAC;QACFlL,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;UACxC,IAAIkH,SAAS,GAAGvT,KAAK,CAACG,UAAU,CAACkM,WAAW,CAACvE,QAAQ,CAAC;UACtDyL,SAAS,CAACM,YAAY,CAACxH,WAAW,EAAEhI,OAAO,EAAErE,KAAK,CAACgD,IAAI,EAAEuM,OAAO,CAAC;QACnE,CAAC,CAAC;QACF/U,SAAS,CAACyT,OAAO,CAAC,aAAa,EAAE5J,OAAO,EAAE,IAAI,CAACrB,IAAI,CAAC;MACtD,CAAC;MACD+Q,YAAY,EAAE,SAAdA,YAAYA,CAAYxE,OAAO,EAAE;QAC/B1Q,aAAa,CAACmF,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAEsP,OAAO,CAAC;MAC1C;IACF,CAAC;IACDzQ,cAAc,GAAG,SAAjBA,cAAcA,CAAamR,KAAK,EAAE+D,UAAU,EAAE5I,MAAM,EAAEC,KAAK,EAAE;MAC3D,IAAI4E,KAAK,CAACpM,SAAS,EAAE;QACnBhG,eAAe,CAACoS,KAAK,CAACnS,EAAE,CAAC;QACzB;MACF;MACA,IAAIuG,OAAO,GAAG4L,KAAK,CAAC3L,MAAM;MAC1B,IAAI2P,YAAY,GAAGhE,KAAK,CAACnN,YAAY,CAACoR,oBAAoB,CAAC,CAAC;MAC5D,IAAI1I,MAAM;MACV,IAAIW,YAAY,GAAGpT,SAAS,CAAC2S,WAAW,CAACrH,OAAO,EAAE+G,MAAM,CAAC;MACzD,KAAK,IAAIoF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,YAAY,CAACtW,MAAM,EAAE6S,CAAC,EAAE,EAAE;QAC5C,IAAI1E,QAAQ,GAAGmI,YAAY,CAACzD,CAAC,CAAC;QAC9B,IAAI1E,QAAQ,CAACkI,UAAU,CAAC,IAAI,CAACxI,MAAM,GAAGM,QAAQ,CAACkI,UAAU,CAAC,CAAC3P,OAAO,EAAE8H,YAAY,EAAEd,KAAK,CAAC,KAAK,IAAI,EAAE;UACjG,OAAOG,MAAM;QACf;MACF;MACA,IAAI5K,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;QACzC5G,IAAI,CAAC,qCAAqC,GAAG+Z,UAAU,GAAG,6BAA6B,CAAC;MAC1F;IACF,CAAC;IACDjV,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAakR,KAAK,EAAE5L,OAAO,EAAE;MAC5C,IAAI8P,SAAS,GAAGlE,KAAK,CAAC9P,UAAU;MAChC,IAAI2D,SAAS,GAAGmM,KAAK,CAAC1M,UAAU;MAChCc,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;QACxCvI,SAAS,CAAC/E,iBAAiB,CAACsN,WAAW,EAAE8H,SAAS,CAAC9H,WAAW,CAACvE,QAAQ,CAAC,CAAC;MAC3E,CAAC,CAAC;IACJ,CAAC;IACD9I,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAauQ,OAAO,EAAExL,MAAM,EAAE;MAC5C,IAAI/D,KAAK,GAAG,IAAI;MAChB,IAAIqE,OAAO,GAAG,IAAI,CAACwB,QAAQ,CAAC,CAAC;MAC7B,IAAIuO,WAAW,GAAG7E,OAAO,CAACjJ,IAAI;MAC9B,IAAI+N,aAAa,GAAG9E,OAAO,CAAC8E,aAAa;MACzC,IAAIC,UAAU,GAAG5E,OAAO,CAAC0E,WAAW,CAAC;MACrC,IAAIG,UAAU,GAAGD,UAAU,CAACC,UAAU;MACtC,IAAIC,UAAU,GAAG,CAACD,UAAU,CAACvQ,MAAM,IAAI,QAAQ,EAAEyQ,KAAK,CAAC,GAAG,CAAC;MAC3D,IAAIC,YAAY,GAAGF,UAAU,CAACG,GAAG,CAAC,CAAC;MACnC,IAAIC,OAAO,GAAGJ,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIhb,cAAc,CAACgb,UAAU,CAAC,CAAC,CAAC,CAAC;MACpE,IAAI,CAAC1X,mBAAmB,CAAC,GAAG,IAAI;MAChC,IAAI+X,QAAQ,GAAG,CAACtF,OAAO,CAAC;MACxB,IAAIuF,OAAO,GAAG,KAAK;MACnB;MACA,IAAIvF,OAAO,CAACwF,KAAK,EAAE;QACjBD,OAAO,GAAG,IAAI;QACdD,QAAQ,GAAG7e,GAAG,CAACuZ,OAAO,CAACwF,KAAK,EAAE,UAAU3K,IAAI,EAAE;UAC5CA,IAAI,GAAGnU,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEsU,IAAI,CAAC,EAAEmF,OAAO,CAAC;UAC1CnF,IAAI,CAAC2K,KAAK,GAAG,IAAI;UACjB,OAAO3K,IAAI;QACb,CAAC,CAAC;MACJ;MACA,IAAI4K,aAAa,GAAG,EAAE;MACtB,IAAI1F,QAAQ;MACZ,IAAI2F,cAAc,GAAGrd,qBAAqB,CAAC2X,OAAO,CAAC;MACnD,IAAI2F,UAAU,GAAGrd,iBAAiB,CAAC0X,OAAO,CAAC;MAC3C;MACA,IAAI2F,UAAU,EAAE;QACdxc,YAAY,CAAC,IAAI,CAACsK,IAAI,CAAC;MACzB;MACAzN,IAAI,CAACsf,QAAQ,EAAE,UAAUM,SAAS,EAAE;QAClC;QACA7F,QAAQ,GAAGgF,UAAU,CAACc,MAAM,CAACD,SAAS,EAAEnV,KAAK,CAACsE,MAAM,EAAEtE,KAAK,CAACgD,IAAI,CAAC;QACjE;QACAsM,QAAQ,GAAGA,QAAQ,IAAIxZ,MAAM,CAAC,CAAC,CAAC,EAAEqf,SAAS,CAAC;QAC5C;QACA7F,QAAQ,CAAChJ,IAAI,GAAGiO,UAAU,CAAC3G,KAAK,IAAI0B,QAAQ,CAAChJ,IAAI;QACjD0O,aAAa,CAAC/M,IAAI,CAACqH,QAAQ,CAAC;QAC5B;QACA,IAAI4F,UAAU,EAAE;UACd,IAAI3C,EAAE,GAAGxZ,SAAS,CAACsc,cAAc,CAAC9F,OAAO,CAAC;YACxC+F,cAAc,GAAG/C,EAAE,CAAC+C,cAAc;YAClCC,iBAAiB,GAAGhD,EAAE,CAACgD,iBAAiB;UAC1C,IAAIC,iBAAiB,GAAGD,iBAAiB,GAAGD,cAAc,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ;UAC/E7W,cAAc,CAACoB,KAAK,EAAE0U,YAAY,EAAES,SAAS,EAAEK,iBAAiB,CAAC;UACjE9V,kBAAkB,CAACM,KAAK,CAAC;QAC3B,CAAC,MAAM,IAAIiV,cAAc,EAAE;UACzB;UACA;UACArW,cAAc,CAACoB,KAAK,EAAE0U,YAAY,EAAES,SAAS,EAAE,QAAQ,CAAC;UACxDzV,kBAAkB,CAACM,KAAK,CAAC;QAC3B,CAAC,MAAM,IAAI4U,OAAO,EAAE;UAClBhW,cAAc,CAACoB,KAAK,EAAE0U,YAAY,EAAES,SAAS,EAAEP,OAAO,CAAC1D,IAAI,EAAE0D,OAAO,CAACzD,GAAG,CAAC;QAC3E;MACF,CAAC,CAAC;MACF,IAAIuD,YAAY,KAAK,MAAM,IAAI,CAACQ,UAAU,IAAI,CAACD,cAAc,IAAI,CAACL,OAAO,EAAE;QACzE,IAAI;UACF;UACA,IAAI,IAAI,CAAC7X,cAAc,CAAC,EAAE;YACxB2B,OAAO,CAAC,IAAI,CAAC;YACbG,aAAa,CAACmF,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAEsP,OAAO,CAAC;YACxC,IAAI,CAACxS,cAAc,CAAC,GAAG,IAAI;UAC7B,CAAC,MAAM;YACL8B,aAAa,CAAC6V,YAAY,CAAC,CAACzU,IAAI,CAAC,IAAI,EAAEsP,OAAO,CAAC;UACjD;QACF,CAAC,CAAC,OAAOrL,CAAC,EAAE;UACV,IAAI,CAACpH,mBAAmB,CAAC,GAAG,KAAK;UACjC,MAAMoH,CAAC;QACT;MACF;MACA;MACA,IAAI4Q,OAAO,EAAE;QACXxF,QAAQ,GAAG;UACThJ,IAAI,EAAEiO,UAAU,CAAC3G,KAAK,IAAIwG,WAAW;UACrCC,aAAa,EAAEA,aAAa;UAC5BU,KAAK,EAAEC;QACT,CAAC;MACH,CAAC,MAAM;QACL1F,QAAQ,GAAG0F,aAAa,CAAC,CAAC,CAAC;MAC7B;MACA,IAAI,CAAClY,mBAAmB,CAAC,GAAG,KAAK;MACjC,IAAI,CAACiH,MAAM,EAAE;QACX,IAAI2R,aAAa,GAAG,IAAI,CAAClS,cAAc;QACvCkS,aAAa,CAACzH,OAAO,CAACqB,QAAQ,CAAChJ,IAAI,EAAEgJ,QAAQ,CAAC;QAC9C;QACA,IAAI2F,cAAc,EAAE;UAClB,IAAIU,MAAM,GAAG;YACXrP,IAAI,EAAE,eAAe;YACrB+N,aAAa,EAAEA,aAAa;YAC5BuB,QAAQ,EAAEje,qBAAqB,CAAC0M,OAAO,CAAC;YACxCwR,WAAW,EAAEtG,OAAO,CAACsG,WAAW,IAAI,KAAK;YACzCC,UAAU,EAAEvG,OAAO,CAACjJ,IAAI;YACxByP,iBAAiB,EAAExG;UACrB,CAAC;UACDmG,aAAa,CAACzH,OAAO,CAAC0H,MAAM,CAACrP,IAAI,EAAEqP,MAAM,CAAC;QAC5C;MACF;IACF,CAAC;IACD1W,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAa8E,MAAM,EAAE;MACtC,IAAIiS,cAAc,GAAG,IAAI,CAAC1V,eAAe;MACzC,OAAO0V,cAAc,CAACrY,MAAM,EAAE;QAC5B,IAAI4R,OAAO,GAAGyG,cAAc,CAACC,KAAK,CAAC,CAAC;QACpCjX,gBAAgB,CAACiB,IAAI,CAAC,IAAI,EAAEsP,OAAO,EAAExL,MAAM,CAAC;MAC9C;IACF,CAAC;IACD7E,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAa6E,MAAM,EAAE;MACtC,CAACA,MAAM,IAAI,IAAI,CAACkK,OAAO,CAAC,SAAS,CAAC;IACpC,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI9O,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAa2C,EAAE,EAAEmO,KAAK,EAAE;MACvCnO,EAAE,CAACtD,EAAE,CAAC,UAAU,EAAE,UAAU0O,MAAM,EAAE;QAClC+C,KAAK,CAAChC,OAAO,CAAC,UAAU,EAAEf,MAAM,CAAC;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACApL,EAAE,CAAC6B,SAAS,CAACuS,UAAU,CAAC,CAAC,IAAI,CAACjG,KAAK,CAAClT,cAAc,CAAC,IAAI,CAACkT,KAAK,CAAC1M,UAAU,CAACY,UAAU,IAAI,CAAC8L,KAAK,CAAC3P,eAAe,CAAC3C,MAAM,EAAE;UACpHsS,KAAK,CAAChC,OAAO,CAAC,UAAU,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ,CAAC;IACD7O,cAAc,GAAG,SAAjBA,cAAcA,CAAa0C,EAAE,EAAEmO,KAAK,EAAE;MACpCnO,EAAE,CAACtD,EAAE,CAAC,WAAW,EAAE,UAAU0F,CAAC,EAAE;QAC9B,IAAI3C,EAAE,GAAG2C,CAAC,CAAC+I,MAAM;QACjB,IAAIyF,UAAU,GAAGpY,mBAAmB,CAACiH,EAAE,EAAElK,oBAAoB,CAAC;QAC9D,IAAIqb,UAAU,EAAE;UACd7Z,gCAAgC,CAAC6Z,UAAU,EAAExO,CAAC,EAAE+L,KAAK,CAACjN,IAAI,CAAC;UAC3DtD,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B;MACF,CAAC,CAAC,CAACzR,EAAE,CAAC,UAAU,EAAE,UAAU0F,CAAC,EAAE;QAC7B,IAAI3C,EAAE,GAAG2C,CAAC,CAAC+I,MAAM;QACjB,IAAIyF,UAAU,GAAGpY,mBAAmB,CAACiH,EAAE,EAAElK,oBAAoB,CAAC;QAC9D,IAAIqb,UAAU,EAAE;UACd5Z,+BAA+B,CAAC4Z,UAAU,EAAExO,CAAC,EAAE+L,KAAK,CAACjN,IAAI,CAAC;UAC1DtD,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B;MACF,CAAC,CAAC,CAACzR,EAAE,CAAC,OAAO,EAAE,UAAU0F,CAAC,EAAE;QAC1B,IAAI3C,EAAE,GAAG2C,CAAC,CAAC+I,MAAM;QACjB,IAAIyF,UAAU,GAAGpY,mBAAmB,CAACiH,EAAE,EAAE,UAAU0L,MAAM,EAAE;UACzD,OAAO7V,SAAS,CAAC6V,MAAM,CAAC,CAACxL,SAAS,IAAI,IAAI;QAC5C,CAAC,EAAE,IAAI,CAAC;QACR,IAAIiR,UAAU,EAAE;UACd,IAAItE,UAAU,GAAGsE,UAAU,CAACkD,QAAQ,GAAG,UAAU,GAAG,QAAQ;UAC5D,IAAIpU,MAAM,GAAGpK,SAAS,CAACsb,UAAU,CAAC;UAClCzC,KAAK,CAACjN,IAAI,CAACwM,cAAc,CAAC;YACxBlJ,IAAI,EAAE8H,UAAU;YAChBZ,QAAQ,EAAEhM,MAAM,CAACgM,QAAQ;YACzBhB,eAAe,EAAEhL,MAAM,CAACC,SAAS;YACjCG,WAAW,EAAEJ,MAAM,CAACI,WAAW;YAC/BiU,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IACD,SAAS7C,iBAAiBA,CAAC3O,OAAO,EAAE;MAClCA,OAAO,CAAC2O,iBAAiB,CAAC,CAAC;MAC3B3O,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;QACxCA,WAAW,CAAC2G,iBAAiB,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ;IACA;IACA;IACA,SAASmD,eAAeA,CAAC9R,OAAO,EAAE;MAChC;MACA,IAAI+R,gBAAgB,GAAG,EAAE;MACzB,IAAIC,aAAa,GAAG,EAAE;MACtB,IAAIC,iBAAiB,GAAG,KAAK;MAC7BjS,OAAO,CAACqD,aAAa,CAAC,UAAUD,aAAa,EAAEmF,cAAc,EAAE;QAC7D,IAAI2J,MAAM,GAAG3J,cAAc,CAACnG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC9C,IAAI+P,CAAC,GAAG5J,cAAc,CAACnG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;QACpC,IAAIgQ,SAAS,GAAG7J,cAAc,CAAC8J,YAAY,CAAC,CAAC;QAC7CJ,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC,CAACG,SAAS;QACpD,CAAChP,aAAa,KAAK,QAAQ,GAAG4O,aAAa,GAAGD,gBAAgB,EAAEnO,IAAI,CAAC;UACnEsO,MAAM,EAAEA,MAAM;UACdC,CAAC,EAAEA,CAAC;UACJG,GAAG,EAAE/J,cAAc,CAACc,cAAc;UAClCpH,IAAI,EAAEmB,aAAa;UACnBmE,GAAG,EAAE6K;QACP,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAIH,iBAAiB,EAAE;QACrB;QACA,IAAIM,OAAO,GAAGR,gBAAgB,CAACxE,MAAM,CAACyE,aAAa,CAAC;QACpD,IAAIQ,kBAAkB;QACtB,IAAIC,eAAe;QACnBtgB,OAAO,CAACogB,OAAO,EAAE,UAAU1T,CAAC,EAAEC,CAAC,EAAE;UAC/B,IAAID,CAAC,CAACqT,MAAM,KAAKpT,CAAC,CAACoT,MAAM,EAAE;YACzB,OAAOrT,CAAC,CAACsT,CAAC,GAAGrT,CAAC,CAACqT,CAAC;UAClB;UACA,OAAOtT,CAAC,CAACqT,MAAM,GAAGpT,CAAC,CAACoT,MAAM;QAC5B,CAAC,CAAC;QACFhhB,IAAI,CAACqhB,OAAO,EAAE,UAAUxM,IAAI,EAAE;UAC5B,IAAIwC,cAAc,GAAGvI,OAAO,CAACsJ,YAAY,CAACvD,IAAI,CAAC9D,IAAI,EAAE8D,IAAI,CAACuM,GAAG,CAAC;UAC9D,IAAIJ,MAAM,GAAGnM,IAAI,CAACmM,MAAM;UACxB,IAAI3K,GAAG,GAAGxB,IAAI,CAACwB,GAAG;UAClB,IAAIiL,kBAAkB,IAAI,IAAI,EAAE;YAC9BN,MAAM,GAAG/N,IAAI,CAACG,GAAG,CAACkO,kBAAkB,EAAEN,MAAM,CAAC;UAC/C;UACA,IAAI3K,GAAG,EAAE;YACP,IAAI2K,MAAM,KAAKM,kBAAkB,IAAIjL,GAAG,KAAKkL,eAAe,EAAE;cAC5DP,MAAM,EAAE;YACV;YACAO,eAAe,GAAGlL,GAAG;UACvB,CAAC,MAAM,IAAIkL,eAAe,EAAE;YAC1B,IAAIP,MAAM,KAAKM,kBAAkB,EAAE;cACjCN,MAAM,EAAE;YACV;YACAO,eAAe,GAAG,EAAE;UACtB;UACAD,kBAAkB,GAAGN,MAAM;UAC3B3J,cAAc,CAACmK,SAAS,CAACR,MAAM,CAAC;QAClC,CAAC,CAAC;MACJ;IACF;IACAlX,MAAM,GAAG,SAATA,MAAMA,CAAa4Q,KAAK,EAAE5L,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,EAAEtL,YAAY,EAAE;MAC7DkS,eAAe,CAAC9R,OAAO,CAAC;MACxB/E,gBAAgB,CAAC2Q,KAAK,EAAE5L,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,EAAEtL,YAAY,CAAC;MAC5D1O,IAAI,CAAC0a,KAAK,CAAC/P,YAAY,EAAE,UAAUoJ,KAAK,EAAE;QACxCA,KAAK,CAACmH,OAAO,GAAG,KAAK;MACvB,CAAC,CAAC;MACFlR,YAAY,CAAC0Q,KAAK,EAAE5L,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,EAAEtL,YAAY,CAAC;MACxD;MACA1O,IAAI,CAAC0a,KAAK,CAAC/P,YAAY,EAAE,UAAUoJ,KAAK,EAAE;QACxC,IAAI,CAACA,KAAK,CAACmH,OAAO,EAAE;UAClBnH,KAAK,CAAC8F,MAAM,CAAC/K,OAAO,EAAEtB,GAAG,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDzD,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAa2Q,KAAK,EAAE5L,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,EAAEtL,YAAY,EAAE+S,SAAS,EAAE;MAClFzhB,IAAI,CAACyhB,SAAS,IAAI/G,KAAK,CAAC7P,gBAAgB,EAAE,UAAUiT,aAAa,EAAE;QACjE,IAAIzG,cAAc,GAAGyG,aAAa,CAAChC,OAAO;QAC1C4F,WAAW,CAACrK,cAAc,EAAEyG,aAAa,CAAC;QAC1CA,aAAa,CAAChU,MAAM,CAACuN,cAAc,EAAEvI,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,CAAC;QAC3D2H,OAAO,CAACtK,cAAc,EAAEyG,aAAa,CAAC;QACtC8D,YAAY,CAACvK,cAAc,EAAEyG,aAAa,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC;IACD;AACJ;AACA;IACI9T,YAAY,GAAG,SAAfA,YAAYA,CAAa0Q,KAAK,EAAE5L,OAAO,EAAEtB,GAAG,EAAEwM,OAAO,EAAEtL,YAAY,EAAEyP,QAAQ,EAAE;MAC7E;MACA,IAAI5P,SAAS,GAAGmM,KAAK,CAAC1M,UAAU;MAChCU,YAAY,GAAGnO,MAAM,CAACmO,YAAY,IAAI,CAAC,CAAC,EAAE;QACxC6L,aAAa,EAAEzL,OAAO,CAAC+S,SAAS,CAAC;MACnC,CAAC,CAAC;MACF;MACA5c,SAAS,CAACyT,OAAO,CAAC,qBAAqB,EAAE5J,OAAO,EAAEtB,GAAG,EAAEkB,YAAY,CAAC;MACpE,IAAIE,UAAU,GAAG,KAAK;MACtBE,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;QACxC,IAAIkH,SAAS,GAAGtD,KAAK,CAAC9P,UAAU,CAACkM,WAAW,CAACvE,QAAQ,CAAC;QACtDyL,SAAS,CAAC9C,OAAO,GAAG,IAAI;QACxB,IAAIe,UAAU,GAAG+B,SAAS,CAAC/B,UAAU;QACrC1N,SAAS,CAACuT,aAAa,CAAC7F,UAAU,EAAEjC,OAAO,CAAC;QAC5C;QACA0H,WAAW,CAAC5K,WAAW,EAAEkH,SAAS,CAAC;QACnC,IAAIG,QAAQ,IAAIA,QAAQ,CAACjN,GAAG,CAAC4F,WAAW,CAACmH,GAAG,CAAC,EAAE;UAC7ChC,UAAU,CAAC8F,KAAK,CAAC,CAAC;QACpB;QACA,IAAI9F,UAAU,CAAC+F,OAAO,CAACzT,SAAS,CAAC0T,cAAc,CAAChG,UAAU,CAAC,CAAC,EAAE;UAC5DrN,UAAU,GAAG,IAAI;QACnB;QACAoP,SAAS,CAACxL,KAAK,CAAChE,MAAM,GAAG,CAAC,CAACsI,WAAW,CAAC5F,GAAG,CAAC,QAAQ,CAAC;QACpD;QACA;QACA;QACAgR,WAAW,CAACpL,WAAW,EAAEkH,SAAS,CAAC;QACnC7b,4BAA4B,CAAC2U,WAAW,CAAC;MAC3C,CAAC,CAAC;MACFvI,SAAS,CAACK,UAAU,GAAGA,UAAU,IAAIL,SAAS,CAACK,UAAU;MACzD3J,SAAS,CAACyT,OAAO,CAAC,qBAAqB,EAAE5J,OAAO,EAAEtB,GAAG,EAAEkB,YAAY,CAAC;MACpE;MACAzJ,SAAS,CAACyT,OAAO,CAAC,mBAAmB,EAAE5J,OAAO,EAAEtB,GAAG,EAAEkB,YAAY,CAAC;MAClEI,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;QACxC,IAAIkH,SAAS,GAAGtD,KAAK,CAAC9P,UAAU,CAACkM,WAAW,CAACvE,QAAQ,CAAC;QACtD;QACAoP,OAAO,CAAC7K,WAAW,EAAEkH,SAAS,CAAC;QAC/B;QACA;QACA4D,YAAY,CAAC9K,WAAW,EAAEkH,SAAS,CAAC;MACtC,CAAC,CAAC;MACF;MACAmE,sBAAsB,CAACzH,KAAK,EAAE5L,OAAO,CAAC;MACtC7J,SAAS,CAACyT,OAAO,CAAC,oBAAoB,EAAE5J,OAAO,EAAEtB,GAAG,EAAEkB,YAAY,CAAC;IACrE,CAAC;IACDvE,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAauQ,KAAK,EAAE;MACpCA,KAAK,CAACjT,uBAAuB,CAAC,GAAG,IAAI;MACrC;MACAiT,KAAK,CAACnL,KAAK,CAAC,CAAC,CAACa,MAAM,CAAC,CAAC;IACxB,CAAC;IACDhG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAasQ,KAAK,EAAE;MACpC,IAAI,CAACA,KAAK,CAACjT,uBAAuB,CAAC,EAAE;QACnC;MACF;MACAiT,KAAK,CAACnL,KAAK,CAAC,CAAC,CAACmC,OAAO,CAAC0Q,QAAQ,CAAC,UAAUpW,EAAE,EAAE;QAC3C;QACA,IAAIpK,OAAO,CAACygB,gBAAgB,CAACrW,EAAE,CAAC,EAAE;UAChC;QACF;QACAsW,kBAAkB,CAACtW,EAAE,CAAC;MACxB,CAAC,CAAC;MACF0O,KAAK,CAACjT,uBAAuB,CAAC,GAAG,KAAK;IACxC,CAAC;IACD,SAAS6a,kBAAkBA,CAACtW,EAAE,EAAE;MAC9B,IAAIuW,SAAS,GAAG,EAAE;MAClB,IAAIC,SAAS,GAAGxW,EAAE,CAACyW,aAAa;MAChC;MACA,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuH,SAAS,CAACpa,MAAM,EAAE6S,CAAC,EAAE,EAAE;QACzC,IAAIyH,SAAS,GAAGF,SAAS,CAACvH,CAAC,CAAC;QAC5B,IAAI,EAAEyH,SAAS,KAAK,UAAU,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,QAAQ,CAAC,EAAE;UACjFH,SAAS,CAAC7P,IAAI,CAACgQ,SAAS,CAAC;QAC3B;MACF;MACA;MACA,IAAI1W,EAAE,CAACqU,QAAQ,IAAIrU,EAAE,CAAC2W,MAAM,CAACC,MAAM,EAAE;QACnCL,SAAS,CAAC7P,IAAI,CAAC,QAAQ,CAAC;MAC1B;MACA,IAAI1G,EAAE,CAAC6W,UAAU,KAAK9gB,oBAAoB,IAAIiK,EAAE,CAAC2W,MAAM,CAACG,QAAQ,EAAE;QAChEP,SAAS,CAAC7P,IAAI,CAAC,UAAU,CAAC;MAC5B,CAAC,MAAM,IAAI1G,EAAE,CAAC6W,UAAU,KAAK7gB,gBAAgB,IAAIgK,EAAE,CAAC2W,MAAM,CAACI,IAAI,EAAE;QAC/DR,SAAS,CAAC7P,IAAI,CAAC,MAAM,CAAC;MACxB;MACA1G,EAAE,CAACgX,SAAS,CAACT,SAAS,CAAC;IACzB;IACA,SAASJ,sBAAsBA,CAACzH,KAAK,EAAE5L,OAAO,EAAE;MAC9C,IAAIvC,EAAE,GAAGmO,KAAK,CAAClO,GAAG;MAClB,IAAIkF,OAAO,GAAGnF,EAAE,CAACmF,OAAO;MACxB,IAAIuR,OAAO,GAAG,CAAC;MACfvR,OAAO,CAAC0Q,QAAQ,CAAC,UAAUpW,EAAE,EAAE;QAC7B,IAAI,CAACA,EAAE,CAACkX,OAAO,EAAE;UACfD,OAAO,EAAE;QACX;MACF,CAAC,CAAC;MACF,IAAIA,OAAO,GAAGnU,OAAO,CAACoC,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAClQ,GAAG,CAACmiB,IAAI,IAAI,CAACniB,GAAG,CAACoiB,MAAM,EAAE;QAC5EtU,OAAO,CAACsM,UAAU,CAAC,UAAUtE,WAAW,EAAE;UACxC,IAAIA,WAAW,CAACuM,sBAAsB,EAAE;YACtC;UACF;UACA,IAAIrF,SAAS,GAAGtD,KAAK,CAAC9P,UAAU,CAACkM,WAAW,CAACvE,QAAQ,CAAC;UACtD,IAAIyL,SAAS,CAAC9C,OAAO,EAAE;YACrB8C,SAAS,CAACsF,YAAY,CAAC,UAAUtX,EAAE,EAAE;cACnC,IAAIA,EAAE,CAAC2W,MAAM,CAACG,QAAQ,EAAE;gBACtB9W,EAAE,CAAC2W,MAAM,CAACG,QAAQ,CAACS,UAAU,GAAG,IAAI;cACtC;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF;IACA;IACA;AACJ;AACA;IACI,SAASrB,WAAWA,CAACpL,WAAW,EAAEkH,SAAS,EAAE;MAC3C,IAAIwF,SAAS,GAAG1M,WAAW,CAAC5F,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI;MACpD8M,SAAS,CAACsF,YAAY,CAAC,UAAUtX,EAAE,EAAE;QACnC;QACA,IAAI,CAACA,EAAE,CAACkX,OAAO,EAAE;UACf;UACAlX,EAAE,CAACuJ,KAAK,CAACkO,KAAK,GAAGD,SAAS;QAC5B;MACF,CAAC,CAAC;IACJ;IACA;IACA,SAAS7B,OAAOA,CAACrL,KAAK,EAAEhE,IAAI,EAAE;MAC5B,IAAIgE,KAAK,CAACoN,YAAY,EAAE;QACtB;MACF;MACA,IAAIzC,CAAC,GAAG3K,KAAK,CAACpF,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAI8P,MAAM,GAAG1K,KAAK,CAACpF,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;MACrC;MACAoB,IAAI,CAACgR,YAAY,CAAC,UAAUtX,EAAE,EAAE;QAC9B2X,SAAS,CAAC3X,EAAE,EAAEiV,CAAC,EAAED,MAAM,EAAE,CAAC1N,QAAQ,CAAC;QACnC;QACA,OAAO,IAAI;MACb,CAAC,CAAC;IACJ;IACA;IACA,SAASqQ,SAASA,CAAC3X,EAAE,EAAEiV,CAAC,EAAED,MAAM,EAAE4C,KAAK,EAAE;MACvC;MACA,IAAIC,KAAK,GAAG7X,EAAE,CAAC8X,cAAc,CAAC,CAAC;MAC/B,IAAIC,SAAS,GAAG/X,EAAE,CAACgY,gBAAgB,CAAC,CAAC;MACrC,IAAId,OAAO,GAAGlX,EAAE,CAACkX,OAAO;MACxB,IAAIA,OAAO,EAAE;QACX;QACA,IAAIe,QAAQ,GAAGjY,EAAE,CAACkY,WAAW,CAAC,CAAC;QAC/B,KAAK,IAAIjJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,QAAQ,CAAC7b,MAAM,EAAE6S,CAAC,EAAE,EAAE;UACxC2I,KAAK,GAAG3Q,IAAI,CAACG,GAAG,CAACuQ,SAAS,CAACM,QAAQ,CAAChJ,CAAC,CAAC,EAAEgG,CAAC,EAAED,MAAM,EAAE4C,KAAK,CAAC,EAAEA,KAAK,CAAC;QACnE;MACF,CAAC,MAAM;QACL;QACA5X,EAAE,CAACiV,CAAC,GAAGA,CAAC;QACRjV,EAAE,CAACgV,MAAM,GAAGA,MAAM;QAClB4C,KAAK,GAAG3Q,IAAI,CAACG,GAAG,CAACpH,EAAE,CAACmY,EAAE,EAAEP,KAAK,CAAC;MAChC;MACA;MACA,IAAIC,KAAK,EAAE;QACTA,KAAK,CAAC5C,CAAC,GAAGA,CAAC;QACX4C,KAAK,CAAC7C,MAAM,GAAGA,MAAM;QACrB;QACA;QACAoD,QAAQ,CAACR,KAAK,CAAC,KAAKC,KAAK,CAACM,EAAE,GAAGP,KAAK,GAAG,CAAC,CAAC;MAC3C;MACA,IAAIG,SAAS,EAAE;QACb,IAAIM,mBAAmB,GAAGrY,EAAE,CAACqY,mBAAmB;QAChDN,SAAS,CAAC9C,CAAC,GAAGA,CAAC;QACf8C,SAAS,CAAC/C,MAAM,GAAGA,MAAM;QACzBoD,QAAQ,CAACR,KAAK,CAAC,KAAKG,SAAS,CAACI,EAAE,GAAGP,KAAK,IAAIS,mBAAmB,IAAIA,mBAAmB,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7G;MACA,OAAOV,KAAK;IACd;IACA;IACA;IACA,SAASlC,WAAWA,CAACpL,KAAK,EAAEhE,IAAI,EAAE;MAChCA,IAAI,CAACgR,YAAY,CAAC,UAAUtX,EAAE,EAAE;QAC9B;QACA,IAAIpK,OAAO,CAACygB,gBAAgB,CAACrW,EAAE,CAAC,EAAE;UAChC;QACF;QACA,IAAIuY,WAAW,GAAGvY,EAAE,CAAC8X,cAAc,CAAC,CAAC;QACrC,IAAIU,SAAS,GAAGxY,EAAE,CAACgY,gBAAgB,CAAC,CAAC;QACrC,IAAIhY,EAAE,CAACyY,eAAe,EAAE;UACtBzY,EAAE,CAACyY,eAAe,GAAG,IAAI;QAC3B;QACA,IAAIF,WAAW,IAAIA,WAAW,CAACE,eAAe,EAAE;UAC9CF,WAAW,CAACE,eAAe,GAAG,IAAI;QACpC;QACA,IAAID,SAAS,IAAIA,SAAS,CAACC,eAAe,EAAE;UAC1CD,SAAS,CAACC,eAAe,GAAG,IAAI;QAClC;QACA;QACA,IAAIzY,EAAE,CAAC0Y,QAAQ,CAAC,CAAC,EAAE;UACjB1Y,EAAE,CAAC2Y,UAAU,GAAG3Y,EAAE,CAACyW,aAAa;UAChCzW,EAAE,CAAC0V,WAAW,CAAC,CAAC;QAClB,CAAC,MAAM,IAAI1V,EAAE,CAAC2Y,UAAU,EAAE;UACxB3Y,EAAE,CAAC2Y,UAAU,GAAG,IAAI;QACtB;MACF,CAAC,CAAC;IACJ;IACA,SAAS/C,YAAYA,CAACtL,KAAK,EAAEhE,IAAI,EAAE;MACjC,IAAIsS,mBAAmB,GAAGtO,KAAK,CAAChG,QAAQ,CAAC,gBAAgB,CAAC;MAC1D,IAAIuU,eAAe,GAAGvO,KAAK,CAACwO,kBAAkB,CAAC,CAAC;MAChD,IAAIvL,QAAQ,GAAGqL,mBAAmB,CAAC1T,GAAG,CAAC,UAAU,CAAC;MAClD,IAAIuT,eAAe,GAAGlL,QAAQ,GAAG,CAAC,GAAG;QACnCA,QAAQ,EAAEA,QAAQ;QAClBwL,KAAK,EAAEH,mBAAmB,CAAC1T,GAAG,CAAC,OAAO,CAAC;QACvC8T,MAAM,EAAEJ,mBAAmB,CAAC1T,GAAG,CAAC,QAAQ;QACxC;MACF,CAAC,GAAG,IAAI;MACRoB,IAAI,CAACgR,YAAY,CAAC,UAAUtX,EAAE,EAAE;QAC9B,IAAIA,EAAE,CAAC2W,MAAM,IAAI3W,EAAE,CAAC2W,MAAM,CAACG,QAAQ,EAAE;UACnC;UACA,IAAIlhB,OAAO,CAACygB,gBAAgB,CAACrW,EAAE,CAAC,EAAE;YAChC;UACF;UACA,IAAIA,EAAE,YAAYpK,OAAO,CAACqjB,IAAI,EAAE;YAC9BriB,cAAc,CAACoJ,EAAE,CAAC;UACpB;UACA;UACA;UACA,IAAIA,EAAE,CAACkZ,OAAO,EAAE;YACd,IAAIP,UAAU,GAAG3Y,EAAE,CAAC2Y,UAAU;YAC9B;YACA,IAAIA,UAAU,EAAE;cACd3Y,EAAE,CAACgX,SAAS,CAAC2B,UAAU,CAAC;YAC1B;UACF;UACA;UACA,IAAIE,eAAe,EAAE;YACnB7Y,EAAE,CAACyY,eAAe,GAAGA,eAAe;YACpC,IAAIF,WAAW,GAAGvY,EAAE,CAAC8X,cAAc,CAAC,CAAC;YACrC,IAAIU,SAAS,GAAGxY,EAAE,CAACgY,gBAAgB,CAAC,CAAC;YACrC;YACA,IAAIO,WAAW,EAAE;cACfA,WAAW,CAACE,eAAe,GAAGA,eAAe;YAC/C;YACA,IAAID,SAAS,EAAE;cACbA,SAAS,CAACC,eAAe,GAAGA,eAAe;YAC7C;UACF;UACA;UACA,IAAIzY,EAAE,CAACkZ,OAAO,EAAE;YACd5C,kBAAkB,CAACtW,EAAE,CAAC;UACxB;QACF;MACF,CAAC,CAAC;IACJ;IACA;IACA/B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAayQ,KAAK,EAAE;MACpC,OAAO,KAAM,aAAa,UAAU3R,MAAM,EAAE;QAC1ClJ,SAAS,CAACslB,OAAO,EAAEpc,MAAM,CAAC;QAC1B,SAASoc,OAAOA,CAAA,EAAG;UACjB,OAAOpc,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACF,KAAK,CAAC,IAAI,EAAEV,SAAS,CAAC,IAAI,IAAI;QACjE;QACAgd,OAAO,CAACvc,SAAS,CAAC+V,oBAAoB,GAAG,YAAY;UACnD,OAAOjE,KAAK,CAACnN,YAAY,CAACoR,oBAAoB,CAAC,CAAC;QAClD,CAAC;QACDwG,OAAO,CAACvc,SAAS,CAACwc,qBAAqB,GAAG,UAAUpZ,EAAE,EAAE;UACtD,OAAOA,EAAE,EAAE;YACT,IAAIqZ,SAAS,GAAGrZ,EAAE,CAAC+P,iBAAiB;YACpC,IAAIsJ,SAAS,IAAI,IAAI,EAAE;cACrB,OAAO3K,KAAK,CAAC3L,MAAM,CAACqJ,YAAY,CAACiN,SAAS,CAACjT,QAAQ,EAAEiT,SAAS,CAACrJ,KAAK,CAAC;YACvE;YACAhQ,EAAE,GAAGA,EAAE,CAAC6L,MAAM;UAChB;QACF,CAAC;QACDsN,OAAO,CAACvc,SAAS,CAAC/F,aAAa,GAAG,UAAUmJ,EAAE,EAAEsZ,cAAc,EAAE;UAC9DziB,aAAa,CAACmJ,EAAE,EAAEsZ,cAAc,CAAC;UACjCnb,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B,CAAC;QACDyK,OAAO,CAACvc,SAAS,CAAC9F,aAAa,GAAG,UAAUkJ,EAAE,EAAEsZ,cAAc,EAAE;UAC9DxiB,aAAa,CAACkJ,EAAE,EAAEsZ,cAAc,CAAC;UACjCnb,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B,CAAC;QACDyK,OAAO,CAACvc,SAAS,CAAC1F,SAAS,GAAG,UAAU8I,EAAE,EAAE;UAC1C9I,SAAS,CAAC8I,EAAE,CAAC;UACb7B,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B,CAAC;QACDyK,OAAO,CAACvc,SAAS,CAAC7F,SAAS,GAAG,UAAUiJ,EAAE,EAAE;UAC1CjJ,SAAS,CAACiJ,EAAE,CAAC;UACb7B,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B,CAAC;QACDyK,OAAO,CAACvc,SAAS,CAAC5F,WAAW,GAAG,UAAUgJ,EAAE,EAAE;UAC5ChJ,WAAW,CAACgJ,EAAE,CAAC;UACf7B,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B,CAAC;QACDyK,OAAO,CAACvc,SAAS,CAAC3F,WAAW,GAAG,UAAU+I,EAAE,EAAE;UAC5C/I,WAAW,CAAC+I,EAAE,CAAC;UACf7B,kBAAkB,CAACuQ,KAAK,CAAC;QAC3B,CAAC;QACDyK,OAAO,CAACvc,SAAS,CAAC0H,QAAQ,GAAG,YAAY;UACvC,OAAOoK,KAAK,CAACpK,QAAQ,CAAC,CAAC;QACzB,CAAC;QACD6U,OAAO,CAACvc,SAAS,CAACwO,uBAAuB,GAAG,UAAUC,cAAc,EAAE;UACpE,OAAOqD,KAAK,CAACtD,uBAAuB,CAACC,cAAc,CAAC;QACtD,CAAC;QACD8N,OAAO,CAACvc,SAAS,CAAC0O,oBAAoB,GAAG,UAAUR,WAAW,EAAE;UAC9D,OAAO4D,KAAK,CAACpD,oBAAoB,CAACR,WAAW,CAAC;QAChD,CAAC;QACD,OAAOqO,OAAO;MAChB,CAAC,CAAC/jB,YAAY,CAAC,EAAEsZ,KAAK,CAAC;IACzB,CAAC;IACDxQ,aAAa,GAAG,SAAhBA,aAAaA,CAAa6J,KAAK,EAAE;MAC/B,SAASwR,2BAA2BA,CAACC,MAAM,EAAEC,MAAM,EAAE;QACnD,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuK,MAAM,CAACpd,MAAM,EAAE6S,CAAC,EAAE,EAAE;UACtC,IAAIyK,UAAU,GAAGF,MAAM,CAACvK,CAAC,CAAC;UAC1ByK,UAAU,CAAC/d,kBAAkB,CAAC,GAAG8d,MAAM;QACzC;MACF;MACAzlB,IAAI,CAAC4Y,cAAc,EAAE,UAAUC,UAAU,EAAEC,SAAS,EAAE;QACpD/E,KAAK,CAAC9F,cAAc,CAAChF,EAAE,CAAC6P,SAAS,EAAE,UAAUT,KAAK,EAAE;UAClD,IAAI9E,eAAe,CAACQ,KAAK,CAACvB,KAAK,CAAC,IAAIuB,KAAK,CAACpM,kBAAkB,CAAC,KAAKC,sBAAsB,EAAE;YACxF,IAAIyQ,KAAK,IAAIA,KAAK,CAACyG,aAAa,EAAE;cAChC;YACF;YACA,IAAI6G,QAAQ,GAAG5R,KAAK,CAAC+F,mBAAmB,CAACzB,KAAK,CAAC;YAC/C,IAAIuN,aAAa,GAAG,EAAE;YACtB5lB,IAAI,CAAC8T,SAAS,EAAE,UAAU4R,UAAU,EAAE;cACpC,IAAIA,UAAU,KAAK3R,KAAK,IAAI2R,UAAU,CAAClT,KAAK,KAAKuB,KAAK,CAACvB,KAAK,EAAE;gBAC5DoT,aAAa,CAAClT,IAAI,CAACgT,UAAU,CAAC;cAChC;YACF,CAAC,CAAC;YACFH,2BAA2B,CAACK,aAAa,EAAEhe,sBAAsB,CAAC;YAClE5H,IAAI,CAAC4lB,aAAa,EAAE,UAAUF,UAAU,EAAE;cACxC,IAAIA,UAAU,CAAC/d,kBAAkB,CAAC,KAAKE,uBAAuB,EAAE;gBAC9D6d,UAAU,CAACzL,cAAc,CAAC0L,QAAQ,CAAC;cACrC;YACF,CAAC,CAAC;YACFJ,2BAA2B,CAACK,aAAa,EAAE9d,sBAAsB,CAAC;UACpE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC,CAAC;EACH,OAAOuC,OAAO;AAChB,CAAC,CAACnJ,QAAQ,CAAC;AACX,IAAI2kB,YAAY,GAAGxb,OAAO,CAACzB,SAAS;AACpCid,YAAY,CAAC5c,EAAE,GAAGlB,uCAAuC,CAAC,IAAI,CAAC;AAC/D8d,YAAY,CAAC3c,GAAG,GAAGnB,uCAAuC,CAAC,KAAK,CAAC;AACjE;AACA;AACA;AACA;AACA8d,YAAY,CAACC,GAAG,GAAG,UAAUC,SAAS,EAAEC,EAAE,EAAEC,GAAG,EAAE;EAC/C,IAAIhU,IAAI,GAAG,IAAI;EACf1N,YAAY,CAAC,4BAA4B,CAAC;EAC1C,SAAS2hB,OAAOA,CAAA,EAAG;IACjB,IAAIC,KAAK,GAAG,EAAE;IACd,KAAK,IAAIje,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5Cie,KAAK,CAACje,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC3B;IACA8d,EAAE,IAAIA,EAAE,CAACnd,KAAK,IAAImd,EAAE,CAACnd,KAAK,CAAC,IAAI,EAAEsd,KAAK,CAAC;IACvC;IACAlU,IAAI,CAAC/I,GAAG,CAAC6c,SAAS,EAAEG,OAAO,CAAC;EAC9B;EACA;EACA;EACA,IAAI,CAACjd,EAAE,CAACyB,IAAI,CAAC,IAAI,EAAEqb,SAAS,EAAEG,OAAO,EAAED,GAAG,CAAC;AAC7C,CAAC;AACD,IAAI1O,iBAAiB,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC;AACvI,SAASjP,eAAeA,CAACC,EAAE,EAAE;EAC3B,IAAI8C,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;IACzC5G,IAAI,CAAC,WAAW,GAAG6D,EAAE,GAAG,oBAAoB,CAAC;EAC/C;AACF;AACA,IAAI4R,OAAO,GAAG,CAAC,CAAC;AAChB;AACA;AACA;AACA,IAAIvB,cAAc,GAAG,CAAC,CAAC;AACvB,IAAI7K,kBAAkB,GAAG,EAAE;AAC3B,IAAIkC,uBAAuB,GAAG,EAAE;AAChC,IAAInC,WAAW,GAAG,EAAE;AACpB,IAAI9C,YAAY,GAAG,CAAC,CAAC;AACrB,IAAI4O,cAAc,GAAG,CAAC,CAAC;AACvB,IAAI9F,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIP,eAAe,GAAG,CAAC,CAAC;AACxB,IAAI6S,MAAM,GAAG,CAAC,IAAInX,IAAI,CAAC,CAAC,GAAG,CAAC;AAC5B,IAAIoX,WAAW,GAAG,CAAC,IAAIpX,IAAI,CAAC,CAAC,GAAG,CAAC;AACjC,IAAIkK,iBAAiB,GAAG,oBAAoB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS1M,IAAIA,CAACnC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;EACrC,IAAI8b,QAAQ,GAAG,EAAE9b,IAAI,IAAIA,IAAI,CAACsB,GAAG,CAAC;EAClC,IAAIwa,QAAQ,EAAE;IACZ,IAAIjb,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAAChB,GAAG,EAAE;QACR,MAAM,IAAI0G,KAAK,CAAC,iCAAiC,CAAC;MACpD;IACF;IACA,IAAIuV,aAAa,GAAGC,gBAAgB,CAAClc,GAAG,CAAC;IACzC,IAAIic,aAAa,EAAE;MACjB,IAAIlb,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;QACzC5G,IAAI,CAAC,2DAA2D,CAAC;MACnE;MACA,OAAO6hB,aAAa;IACtB;IACA,IAAIlb,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI3K,KAAK,CAAC2J,GAAG,CAAC,IAAIA,GAAG,CAACmc,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,KAAK,CAACpc,GAAG,CAACqc,WAAW,KAAK,CAACnc,IAAI,IAAIA,IAAI,CAACoC,KAAK,IAAI,IAAI,CAAC,IAAI,CAACtC,GAAG,CAACsc,YAAY,KAAK,CAACpc,IAAI,IAAIA,IAAI,CAACqC,MAAM,IAAI,IAAI,CAAC,CAAC,EAAE;QACvKnI,IAAI,CAAC,+CAA+C,GAAG,6DAA6D,GAAG,yDAAyD,GAAG,mBAAmB,CAAC;MACzM;IACF;EACF;EACA,IAAIqP,KAAK,GAAG,IAAI1J,OAAO,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC;EACzCuJ,KAAK,CAACxL,EAAE,GAAG,KAAK,GAAG6d,MAAM,EAAE;EAC3BtS,SAAS,CAACC,KAAK,CAACxL,EAAE,CAAC,GAAGwL,KAAK;EAC3BuS,QAAQ,IAAI9iB,SAAS,CAAC0V,YAAY,CAAC5O,GAAG,EAAE6O,iBAAiB,EAAEpF,KAAK,CAACxL,EAAE,CAAC;EACpE2B,aAAa,CAAC6J,KAAK,CAAC;EACpB9O,SAAS,CAACyT,OAAO,CAAC,WAAW,EAAE3E,KAAK,CAAC;EACrC,OAAOA,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8S,OAAOA,CAAC9T,OAAO,EAAE;EAC/B;EACA,IAAInS,OAAO,CAACmS,OAAO,CAAC,EAAE;IACpB,IAAIyS,MAAM,GAAGzS,OAAO;IACpBA,OAAO,GAAG,IAAI;IACd;IACA/S,IAAI,CAACwlB,MAAM,EAAE,UAAUzR,KAAK,EAAE;MAC5B,IAAIA,KAAK,CAACvB,KAAK,IAAI,IAAI,EAAE;QACvBO,OAAO,GAAGgB,KAAK,CAACvB,KAAK;MACvB;IACF,CAAC,CAAC;IACFO,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAGsT,WAAW,EAAE;IACzCrmB,IAAI,CAACwlB,MAAM,EAAE,UAAUzR,KAAK,EAAE;MAC5BA,KAAK,CAACvB,KAAK,GAAGO,OAAO;IACvB,CAAC,CAAC;EACJ;EACAQ,eAAe,CAACR,OAAO,CAAC,GAAG,IAAI;EAC/B,OAAOA,OAAO;AAChB;AACA,OAAO,SAAS+T,UAAUA,CAAC/T,OAAO,EAAE;EAClCQ,eAAe,CAACR,OAAO,CAAC,GAAG,KAAK;AAClC;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgU,UAAU,GAAGD,UAAU;AAClC;AACA;AACA;AACA,OAAO,SAAS7N,OAAOA,CAAClF,KAAK,EAAE;EAC7B,IAAIjT,QAAQ,CAACiT,KAAK,CAAC,EAAE;IACnBA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC;EAC1B,CAAC,MAAM,IAAI,EAAEA,KAAK,YAAY1J,OAAO,CAAC,EAAE;IACtC;IACA0J,KAAK,GAAGyS,gBAAgB,CAACzS,KAAK,CAAC;EACjC;EACA,IAAIA,KAAK,YAAY1J,OAAO,IAAI,CAAC0J,KAAK,CAAC1L,UAAU,CAAC,CAAC,EAAE;IACnD0L,KAAK,CAACkF,OAAO,CAAC,CAAC;EACjB;AACF;AACA,OAAO,SAASuN,gBAAgBA,CAAClc,GAAG,EAAE;EACpC,OAAOwJ,SAAS,CAACtQ,SAAS,CAACwjB,YAAY,CAAC1c,GAAG,EAAE6O,iBAAiB,CAAC,CAAC;AAClE;AACA,OAAO,SAAS8N,eAAeA,CAAC5Q,GAAG,EAAE;EACnC,OAAOvC,SAAS,CAACuC,GAAG,CAAC;AACvB;AACA;AACA;AACA;AACA,OAAO,SAAS6Q,aAAaA,CAACzN,IAAI,EAAElP,KAAK,EAAE;EACzCS,YAAY,CAACyO,IAAI,CAAC,GAAGlP,KAAK;AAC5B;AACA;AACA;AACA;AACA,OAAO,SAAS4c,oBAAoBA,CAACC,gBAAgB,EAAE;EACrD,IAAIjnB,OAAO,CAAC8P,uBAAuB,EAAEmX,gBAAgB,CAAC,GAAG,CAAC,EAAE;IAC1DnX,uBAAuB,CAACyC,IAAI,CAAC0U,gBAAgB,CAAC;EAChD;AACF;AACA,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACrDC,iBAAiB,CAACzZ,kBAAkB,EAAEuZ,QAAQ,EAAEC,SAAS,EAAE5hB,0BAA0B,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8hB,gBAAgBA,CAACC,YAAY,EAAE;EAC7CC,uBAAuB,CAAC,WAAW,EAAED,YAAY,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,kBAAkBA,CAACC,cAAc,EAAE;EACjDF,uBAAuB,CAAC,aAAa,EAAEE,cAAc,CAAC;AACxD;AACA,OAAO,SAASF,uBAAuBA,CAAClO,IAAI,EAAEuM,EAAE,EAAE;EAChD/gB,SAAS,CAACgE,EAAE,CAACwQ,IAAI,EAAEuM,EAAE,CAAC;AACxB;AACA,OAAO,SAAS8B,cAAcA,CAAC9I,UAAU,EAAE+G,SAAS,EAAElG,MAAM,EAAE;EAC5D,IAAI5f,UAAU,CAAC8lB,SAAS,CAAC,EAAE;IACzBlG,MAAM,GAAGkG,SAAS;IAClBA,SAAS,GAAG,EAAE;EAChB;EACA,IAAIlN,UAAU,GAAG3Y,QAAQ,CAAC8e,UAAU,CAAC,GAAGA,UAAU,CAACjO,IAAI,GAAG,CAACiO,UAAU,EAAEA,UAAU,GAAG;IAClF3G,KAAK,EAAE0N;EACT,CAAC,CAAC,CAAC,CAAC,CAAC;EACL;EACA/G,UAAU,CAAC3G,KAAK,GAAG,CAAC2G,UAAU,CAAC3G,KAAK,IAAIQ,UAAU,EAAElQ,WAAW,CAAC,CAAC;EACjEod,SAAS,GAAG/G,UAAU,CAAC3G,KAAK;EAC5B,IAAIO,cAAc,CAACmN,SAAS,CAAC,EAAE;IAC7B;IACA;EACF;EACA;EACAhmB,MAAM,CAAC2H,UAAU,CAACqgB,IAAI,CAAClP,UAAU,CAAC,IAAInR,UAAU,CAACqgB,IAAI,CAAChC,SAAS,CAAC,CAAC;EACjE,IAAI,CAAC5L,OAAO,CAACtB,UAAU,CAAC,EAAE;IACxBsB,OAAO,CAACtB,UAAU,CAAC,GAAG;MACpBgH,MAAM,EAAEA,MAAM;MACdb,UAAU,EAAEA;IACd,CAAC;EACH;EACApG,cAAc,CAACmN,SAAS,CAAC,GAAGlN,UAAU;AACxC;AACA,OAAO,SAASmP,wBAAwBA,CAACjX,IAAI,EAAEkX,eAAe,EAAE;EAC9D5mB,uBAAuB,CAAC6mB,QAAQ,CAACnX,IAAI,EAAEkX,eAAe,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,6BAA6BA,CAACpX,IAAI,EAAE;EAClD,IAAIkX,eAAe,GAAG5mB,uBAAuB,CAAC6P,GAAG,CAACH,IAAI,CAAC;EACvD,IAAIkX,eAAe,EAAE;IACnB,OAAOA,eAAe,CAACG,iBAAiB,GAAGH,eAAe,CAACG,iBAAiB,CAAC,CAAC,GAAGH,eAAe,CAACI,UAAU,CAACC,KAAK,CAAC,CAAC;EACrH;AACF;AACA,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,cAAcA,CAAClB,QAAQ,EAAEmB,UAAU,EAAE;EAC5CjB,iBAAiB,CAAC1Z,WAAW,EAAEwZ,QAAQ,EAAEmB,UAAU,EAAE5iB,sBAAsB,EAAE,QAAQ,CAAC;AACxF;AACA,SAAS6iB,cAAcA,CAACpB,QAAQ,EAAEqB,UAAU,EAAE;EAC5CnB,iBAAiB,CAAC1Z,WAAW,EAAEwZ,QAAQ,EAAEqB,UAAU,EAAE3iB,qBAAqB,EAAE,QAAQ,CAAC;AACvF;AACA,SAASwiB,cAAc,EAAEE,cAAc;AACvC,IAAIE,eAAe,GAAG,EAAE;AACxB,SAASpB,iBAAiBA,CAACqB,UAAU,EAAEvB,QAAQ,EAAEwB,EAAE,EAAEC,eAAe,EAAEpS,UAAU,EAAE;EAChF,IAAI1W,UAAU,CAACqnB,QAAQ,CAAC,IAAIpnB,QAAQ,CAAConB,QAAQ,CAAC,EAAE;IAC9CwB,EAAE,GAAGxB,QAAQ;IACbA,QAAQ,GAAGyB,eAAe;EAC5B;EACA,IAAI1d,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI0d,KAAK,CAAC1B,QAAQ,CAAC,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACvC,MAAM,IAAItW,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA;IACAhR,IAAI,CAAC6oB,UAAU,EAAE,UAAUI,IAAI,EAAE;MAC/BlpB,MAAM,CAACkpB,IAAI,CAACC,KAAK,KAAKJ,EAAE,CAAC;IAC3B,CAAC,CAAC;EACJ;EACA;EACA,IAAI3oB,OAAO,CAACyoB,eAAe,EAAEE,EAAE,CAAC,IAAI,CAAC,EAAE;IACrC;EACF;EACAF,eAAe,CAAClW,IAAI,CAACoW,EAAE,CAAC;EACxB,IAAIK,YAAY,GAAGrlB,SAAS,CAACslB,gBAAgB,CAACN,EAAE,EAAEnS,UAAU,CAAC;EAC7DwS,YAAY,CAACtb,MAAM,GAAGyZ,QAAQ;EAC9B6B,YAAY,CAACD,KAAK,GAAGJ,EAAE;EACvBD,UAAU,CAACnW,IAAI,CAACyW,YAAY,CAAC;AAC/B;AACA,OAAO,SAASE,eAAeA,CAAC5P,IAAI,EAAE6P,SAAS,EAAE;EAC/C1P,cAAc,CAACH,IAAI,CAAC,GAAG6P,SAAS;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACxC,IAAIne,OAAO,CAACrK,GAAG,CAACsK,QAAQ,KAAK,YAAY,EAAE;IACzC/G,YAAY,CAAC,+EAA+E,CAAC;EAC/F;EACAY,cAAc,CAAC;IACbuP,YAAY,EAAE8U;EAChB,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAEC,YAAY,EAAE;EAC1D,IAAIH,WAAW,GAAGrkB,OAAO,CAAC,aAAa,CAAC;EACxCqkB,WAAW,IAAIA,WAAW,CAACC,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC;AAC5D;AACA,OAAO,SAASC,MAAMA,CAACH,OAAO,EAAE;EAC9B,IAAIG,MAAM,GAAGzkB,OAAO,CAAC,QAAQ,CAAC;EAC9B,OAAOykB,MAAM,IAAIA,MAAM,CAACH,OAAO,CAAC;AAClC;AACA,OAAO,IAAII,iBAAiB,GAAGllB,yBAAyB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA8jB,cAAc,CAAC3iB,sBAAsB,EAAErC,eAAe,CAAC;AACvDglB,cAAc,CAACxiB,iCAAiC,EAAEvC,aAAa,CAAC;AAChE+kB,cAAc,CAACxiB,iCAAiC,EAAEtC,oBAAoB,CAAC;AACvE8kB,cAAc,CAAC3iB,sBAAsB,EAAE5B,gBAAgB,CAAC;AACxDukB,cAAc,CAACxiB,iCAAiC,EAAE9B,cAAc,CAAC;AACjEskB,cAAc,CAACpiB,qBAAqB,EAAEtB,KAAK,CAAC;AAC5CmiB,oBAAoB,CAAC5lB,cAAc,CAAC;AACpC8lB,iBAAiB,CAAC5hB,4BAA4B,EAAEjE,SAAS,CAAC;AAC1D6nB,eAAe,CAAC,SAAS,EAAExlB,cAAc,CAAC;AAC1C;AACAikB,cAAc,CAAC;EACb/W,IAAI,EAAExO,qBAAqB;EAC3B8V,KAAK,EAAE9V,qBAAqB;EAC5BkM,MAAM,EAAElM;AACV,CAAC,EAAE1B,IAAI,CAAC;AACRinB,cAAc,CAAC;EACb/W,IAAI,EAAEvO,oBAAoB;EAC1B6V,KAAK,EAAE7V,oBAAoB;EAC3BiM,MAAM,EAAEjM;AACV,CAAC,EAAE3B,IAAI,CAAC;AACRinB,cAAc,CAAC;EACb/W,IAAI,EAAEtO,kBAAkB;EACxB4V,KAAK,EAAE5V,kBAAkB;EACzBgM,MAAM,EAAEhM;AACV,CAAC,EAAE5B,IAAI,CAAC;AACRinB,cAAc,CAAC;EACb/W,IAAI,EAAErO,oBAAoB;EAC1B2V,KAAK,EAAE3V,oBAAoB;EAC3B+L,MAAM,EAAE/L;AACV,CAAC,EAAE7B,IAAI,CAAC;AACRinB,cAAc,CAAC;EACb/W,IAAI,EAAEpO,yBAAyB;EAC/B0V,KAAK,EAAE1V,yBAAyB;EAChC8L,MAAM,EAAE9L;AACV,CAAC,EAAE9B,IAAI,CAAC;AACR;AACAqmB,aAAa,CAAC,OAAO,EAAEnjB,UAAU,CAAC;AAClCmjB,aAAa,CAAC,MAAM,EAAEljB,SAAS,CAAC;AAChC;AACA;AACA,OAAO,IAAI+lB,QAAQ,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}