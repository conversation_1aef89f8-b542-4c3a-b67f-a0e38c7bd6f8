{"ast": null, "code": "import \"core-js/modules/es.array.is-array.js\";\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };", "map": {"version": 3, "names": ["_arrayWithHoles", "r", "Array", "isArray", "default"], "sources": ["F:/常规项目/华通云(V2)/adminweb/node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js"], "sourcesContent": ["function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };"], "mappings": ";AAAA,SAASA,eAAeA,CAACC,CAAC,EAAE;EAC1B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AACA,SAASD,eAAe,IAAII,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}