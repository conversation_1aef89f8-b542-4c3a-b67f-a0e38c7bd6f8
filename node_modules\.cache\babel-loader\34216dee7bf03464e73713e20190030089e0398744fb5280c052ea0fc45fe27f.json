{"ast": null, "code": "var _typeof = require(\"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91(V2)/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\nrequire(\"core-js/modules/es.array.includes.js\");\nrequire(\"core-js/modules/es.string.includes.js\");\n//! moment.js locale configuration\n//! locale : Northern Kurdish [ku-kmr]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/mergehez\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(num, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['çend sanîye', 'çend sanîyeyan'],\n      ss: [num + ' sanîye', num + ' sanîyeyan'],\n      m: ['deqîqeyek', 'deqîqeyekê'],\n      mm: [num + ' deqîqe', num + ' deqîqeyan'],\n      h: ['saetek', 'saetekê'],\n      hh: [num + ' saet', num + ' saetan'],\n      d: ['rojek', 'rojekê'],\n      dd: [num + ' roj', num + ' rojan'],\n      w: ['hefteyek', 'hefteyekê'],\n      ww: [num + ' hefte', num + ' hefteyan'],\n      M: ['mehek', 'mehekê'],\n      MM: [num + ' meh', num + ' mehan'],\n      y: ['salek', 'salekê'],\n      yy: [num + ' sal', num + ' salan']\n    };\n    return withoutSuffix ? format[key][0] : format[key][1];\n  }\n  // function obliqueNumSuffix(num) {\n  //     if(num.includes(':'))\n  //         num = parseInt(num.split(':')[0]);\n  //     else\n  //         num = parseInt(num);\n  //     return num == 0 || num % 10 == 1 ? 'ê'\n  //                         : (num > 10 && num % 10 == 0 ? 'î' : 'an');\n  // }\n  function ezafeNumSuffix(num) {\n    num = '' + num;\n    var l = num.substring(num.length - 1),\n      ll = num.length > 1 ? num.substring(num.length - 2) : '';\n    if (!(ll == 12 || ll == 13) && (l == '2' || l == '3' || ll == '50' || l == '70' || l == '80')) return 'yê';\n    return 'ê';\n  }\n  var kuKmr = moment.defineLocale('ku-kmr', {\n    // According to the spelling rules defined by the work group of Weqfa Mezopotamyayê (Mesopotamia Foundation)\n    // this should be: 'Kanûna Paşîn_Sibat_Adar_Nîsan_Gulan_Hezîran_Tîrmeh_Tebax_Îlon_Çirîya Pêşîn_Çirîya Paşîn_Kanûna Pêşîn'\n    // But the names below are more well known and handy\n    months: 'Rêbendan_Sibat_Adar_Nîsan_Gulan_Hezîran_Tîrmeh_Tebax_Îlon_Cotmeh_Mijdar_Berfanbar'.split('_'),\n    monthsShort: 'Rêb_Sib_Ada_Nîs_Gul_Hez_Tîr_Teb_Îlo_Cot_Mij_Ber'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'Yekşem_Duşem_Sêşem_Çarşem_Pêncşem_În_Şemî'.split('_'),\n    weekdaysShort: 'Yek_Du_Sê_Çar_Pên_În_Şem'.split('_'),\n    weekdaysMin: 'Ye_Du_Sê_Ça_Pê_În_Şe'.split('_'),\n    meridiem: function meridiem(hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'bn' : 'BN';\n      } else {\n        return isLower ? 'pn' : 'PN';\n      }\n    },\n    meridiemParse: /bn|BN|pn|PN/,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'Do MMMM[a] YYYY[an]',\n      LLL: 'Do MMMM[a] YYYY[an] HH:mm',\n      LLLL: 'dddd, Do MMMM[a] YYYY[an] HH:mm',\n      ll: 'Do MMM[.] YYYY[an]',\n      lll: 'Do MMM[.] YYYY[an] HH:mm',\n      llll: 'ddd[.], Do MMM[.] YYYY[an] HH:mm'\n    },\n    calendar: {\n      sameDay: '[Îro di saet] LT [de]',\n      nextDay: '[Sibê di saet] LT [de]',\n      nextWeek: 'dddd [di saet] LT [de]',\n      lastDay: '[Duh di saet] LT [de]',\n      lastWeek: 'dddd[a borî di saet] LT [de]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'di %s de',\n      past: 'berî %s',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      w: processRelativeTime,\n      ww: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(?:yê|ê|\\.)/,\n    ordinal: function ordinal(num, period) {\n      var p = period.toLowerCase();\n      if (p.includes('w') || p.includes('m')) return num + '.';\n      return num + ezafeNumSuffix(num);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return kuKmr;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "processRelativeTime", "num", "withoutSuffix", "key", "isFuture", "format", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "ezafeNumSuffix", "l", "substring", "length", "ll", "kuKmr", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "meridiem", "hours", "minutes", "isLower", "meridiemParse", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "lll", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "dayOfMonthOrdinalParse", "ordinal", "period", "p", "toLowerCase", "includes", "week", "dow", "doy"], "sources": ["F:/常规项目/华通云(V2)/adminweb/node_modules/moment/locale/ku-kmr.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Northern Kurdish [ku-kmr]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/mergehez\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(num, withoutSuffix, key, isFuture) {\n        var format = {\n            s: ['çend sanîye', 'çend sanîyeyan'],\n            ss: [num + ' sanîye', num + ' sanîyeyan'],\n            m: ['deqîqeyek', 'deqîqeyekê'],\n            mm: [num + ' deqîqe', num + ' deqîqeyan'],\n            h: ['saetek', 'saetekê'],\n            hh: [num + ' saet', num + ' saetan'],\n            d: ['rojek', 'rojekê'],\n            dd: [num + ' roj', num + ' rojan'],\n            w: ['hefteyek', 'hefteyekê'],\n            ww: [num + ' hefte', num + ' hefteyan'],\n            M: ['mehek', 'mehekê'],\n            MM: [num + ' meh', num + ' mehan'],\n            y: ['salek', 'salekê'],\n            yy: [num + ' sal', num + ' salan'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n    // function obliqueNumSuffix(num) {\n    //     if(num.includes(':'))\n    //         num = parseInt(num.split(':')[0]);\n    //     else\n    //         num = parseInt(num);\n    //     return num == 0 || num % 10 == 1 ? 'ê'\n    //                         : (num > 10 && num % 10 == 0 ? 'î' : 'an');\n    // }\n    function ezafeNumSuffix(num) {\n        num = '' + num;\n        var l = num.substring(num.length - 1),\n            ll = num.length > 1 ? num.substring(num.length - 2) : '';\n        if (\n            !(ll == 12 || ll == 13) &&\n            (l == '2' || l == '3' || ll == '50' || l == '70' || l == '80')\n        )\n            return 'yê';\n        return 'ê';\n    }\n\n    var kuKmr = moment.defineLocale('ku-kmr', {\n        // According to the spelling rules defined by the work group of Weqfa Mezopotamyayê (Mesopotamia Foundation)\n        // this should be: 'Kanûna Paşîn_Sibat_Adar_Nîsan_Gulan_Hezîran_Tîrmeh_Tebax_Îlon_Çirîya Pêşîn_Çirîya Paşîn_Kanûna Pêşîn'\n        // But the names below are more well known and handy\n        months: 'Rêbendan_Sibat_Adar_Nîsan_Gulan_Hezîran_Tîrmeh_Tebax_Îlon_Cotmeh_Mijdar_Berfanbar'.split(\n            '_'\n        ),\n        monthsShort: 'Rêb_Sib_Ada_Nîs_Gul_Hez_Tîr_Teb_Îlo_Cot_Mij_Ber'.split('_'),\n        monthsParseExact: true,\n        weekdays: 'Yekşem_Duşem_Sêşem_Çarşem_Pêncşem_În_Şemî'.split('_'),\n        weekdaysShort: 'Yek_Du_Sê_Çar_Pên_În_Şem'.split('_'),\n        weekdaysMin: 'Ye_Du_Sê_Ça_Pê_În_Şe'.split('_'),\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 12) {\n                return isLower ? 'bn' : 'BN';\n            } else {\n                return isLower ? 'pn' : 'PN';\n            }\n        },\n        meridiemParse: /bn|BN|pn|PN/,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'Do MMMM[a] YYYY[an]',\n            LLL: 'Do MMMM[a] YYYY[an] HH:mm',\n            LLLL: 'dddd, Do MMMM[a] YYYY[an] HH:mm',\n            ll: 'Do MMM[.] YYYY[an]',\n            lll: 'Do MMM[.] YYYY[an] HH:mm',\n            llll: 'ddd[.], Do MMM[.] YYYY[an] HH:mm',\n        },\n        calendar: {\n            sameDay: '[Îro di saet] LT [de]',\n            nextDay: '[Sibê di saet] LT [de]',\n            nextWeek: 'dddd [di saet] LT [de]',\n            lastDay: '[Duh di saet] LT [de]',\n            lastWeek: 'dddd[a borî di saet] LT [de]',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'di %s de',\n            past: 'berî %s',\n            s: processRelativeTime,\n            ss: processRelativeTime,\n            m: processRelativeTime,\n            mm: processRelativeTime,\n            h: processRelativeTime,\n            hh: processRelativeTime,\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: processRelativeTime,\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(?:yê|ê|\\.)/,\n        ordinal: function (num, period) {\n            var p = period.toLowerCase();\n            if (p.includes('w') || p.includes('m')) return num + '.';\n\n            return num + ezafeNumSuffix(num);\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return kuKmr;\n\n})));\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,mBAAmBA,CAACC,GAAG,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC5D,IAAIC,MAAM,GAAG;MACTC,CAAC,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;MACpCC,EAAE,EAAE,CAACN,GAAG,GAAG,SAAS,EAAEA,GAAG,GAAG,YAAY,CAAC;MACzCO,CAAC,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;MAC9BC,EAAE,EAAE,CAACR,GAAG,GAAG,SAAS,EAAEA,GAAG,GAAG,YAAY,CAAC;MACzCS,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;MACxBC,EAAE,EAAE,CAACV,GAAG,GAAG,OAAO,EAAEA,GAAG,GAAG,SAAS,CAAC;MACpCW,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;MACtBC,EAAE,EAAE,CAACZ,GAAG,GAAG,MAAM,EAAEA,GAAG,GAAG,QAAQ,CAAC;MAClCa,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;MAC5BC,EAAE,EAAE,CAACd,GAAG,GAAG,QAAQ,EAAEA,GAAG,GAAG,WAAW,CAAC;MACvCe,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;MACtBC,EAAE,EAAE,CAAChB,GAAG,GAAG,MAAM,EAAEA,GAAG,GAAG,QAAQ,CAAC;MAClCiB,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;MACtBC,EAAE,EAAE,CAAClB,GAAG,GAAG,MAAM,EAAEA,GAAG,GAAG,QAAQ;IACrC,CAAC;IACD,OAAOC,aAAa,GAAGG,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASiB,cAAcA,CAACnB,GAAG,EAAE;IACzBA,GAAG,GAAG,EAAE,GAAGA,GAAG;IACd,IAAIoB,CAAC,GAAGpB,GAAG,CAACqB,SAAS,CAACrB,GAAG,CAACsB,MAAM,GAAG,CAAC,CAAC;MACjCC,EAAE,GAAGvB,GAAG,CAACsB,MAAM,GAAG,CAAC,GAAGtB,GAAG,CAACqB,SAAS,CAACrB,GAAG,CAACsB,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;IAC5D,IACI,EAAEC,EAAE,IAAI,EAAE,IAAIA,EAAE,IAAI,EAAE,CAAC,KACtBH,CAAC,IAAI,GAAG,IAAIA,CAAC,IAAI,GAAG,IAAIG,EAAE,IAAI,IAAI,IAAIH,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI,CAAC,EAE9D,OAAO,IAAI;IACf,OAAO,GAAG;EACd;EAEA,IAAII,KAAK,GAAG1B,MAAM,CAAC2B,YAAY,CAAC,QAAQ,EAAE;IACtC;IACA;IACA;IACAC,MAAM,EAAE,mFAAmF,CAACC,KAAK,CAC7F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,2CAA2C,CAACH,KAAK,CAAC,GAAG,CAAC;IAChEI,aAAa,EAAE,0BAA0B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACpDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC;IACJ,CAAC;IACDC,aAAa,EAAE,aAAa;IAC5BC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,qBAAqB;MACzBC,GAAG,EAAE,2BAA2B;MAChCC,IAAI,EAAE,iCAAiC;MACvCrB,EAAE,EAAE,oBAAoB;MACxBsB,GAAG,EAAE,0BAA0B;MAC/BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,OAAO,EAAE,wBAAwB;MACjCC,QAAQ,EAAE,wBAAwB;MAClCC,OAAO,EAAE,uBAAuB;MAChCC,QAAQ,EAAE,8BAA8B;MACxCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,SAAS;MACfnD,CAAC,EAAEN,mBAAmB;MACtBO,EAAE,EAAEP,mBAAmB;MACvBQ,CAAC,EAAER,mBAAmB;MACtBS,EAAE,EAAET,mBAAmB;MACvBU,CAAC,EAAEV,mBAAmB;MACtBW,EAAE,EAAEX,mBAAmB;MACvBY,CAAC,EAAEZ,mBAAmB;MACtBa,EAAE,EAAEb,mBAAmB;MACvBc,CAAC,EAAEd,mBAAmB;MACtBe,EAAE,EAAEf,mBAAmB;MACvBgB,CAAC,EAAEhB,mBAAmB;MACtBiB,EAAE,EAAEjB,mBAAmB;MACvBkB,CAAC,EAAElB,mBAAmB;MACtBmB,EAAE,EAAEnB;IACR,CAAC;IACD0D,sBAAsB,EAAE,oBAAoB;IAC5CC,OAAO,EAAE,SAATA,OAAOA,CAAY1D,GAAG,EAAE2D,MAAM,EAAE;MAC5B,IAAIC,CAAC,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;MAC5B,IAAID,CAAC,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,CAAC,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO9D,GAAG,GAAG,GAAG;MAExD,OAAOA,GAAG,GAAGmB,cAAc,CAACnB,GAAG,CAAC;IACpC,CAAC;IACD+D,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOzC,KAAK;AAEhB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}