{"ast": null, "code": "export var regionData = [{\n  name: '北京市',\n  children: [{\n    name: '北京市',\n    children: [{\n      name: '东城区'\n    }, {\n      name: '西城区'\n    }, {\n      name: '朝阳区'\n    }, {\n      name: '丰台区'\n    }, {\n      name: '石景山区'\n    }, {\n      name: '海淀区'\n    }, {\n      name: '门头沟区'\n    }, {\n      name: '房山区'\n    }, {\n      name: '通州区'\n    }, {\n      name: '顺义区'\n    }, {\n      name: '昌平区'\n    }, {\n      name: '大兴区'\n    }, {\n      name: '怀柔区'\n    }, {\n      name: '平谷区'\n    }, {\n      name: '密云区'\n    }, {\n      name: '延庆区'\n    }]\n  }]\n}, {\n  name: '天津市',\n  children: [{\n    name: '天津市',\n    children: [{\n      name: '和平区'\n    }, {\n      name: '河东区'\n    }, {\n      name: '河西区'\n    }, {\n      name: '南开区'\n    }, {\n      name: '河北区'\n    }, {\n      name: '红桥区'\n    }, {\n      name: '东丽区'\n    }, {\n      name: '西青区'\n    }, {\n      name: '津南区'\n    }, {\n      name: '北辰区'\n    }, {\n      name: '武清区'\n    }, {\n      name: '宝坻区'\n    }, {\n      name: '滨海新区'\n    }, {\n      name: '宁河区'\n    }, {\n      name: '静海区'\n    }, {\n      name: '蓟州区'\n    }]\n  }]\n}, {\n  name: '河北省',\n  children: [{\n    name: '石家庄市',\n    children: [{\n      name: '长安区'\n    }, {\n      name: '桥西区'\n    }, {\n      name: '新华区'\n    }, {\n      name: '井陉矿区'\n    }, {\n      name: '裕华区'\n    }, {\n      name: '藁城区'\n    }, {\n      name: '鹿泉区'\n    }, {\n      name: '栾城区'\n    }]\n  }, {\n    name: '唐山市',\n    children: [{\n      name: '路南区'\n    }, {\n      name: '路北区'\n    }, {\n      name: '古冶区'\n    }, {\n      name: '开平区'\n    }, {\n      name: '丰南区'\n    }, {\n      name: '丰润区'\n    }, {\n      name: '曹妃甸区'\n    }]\n  }, {\n    name: '秦皇岛市',\n    children: [{\n      name: '海港区'\n    }, {\n      name: '山海关区'\n    }, {\n      name: '北戴河区'\n    }, {\n      name: '抚宁区'\n    }]\n  }]\n}, {\n  name: '山西省',\n  children: [{\n    name: '太原市',\n    children: [{\n      name: '小店区'\n    }, {\n      name: '迎泽区'\n    }, {\n      name: '杏花岭区'\n    }, {\n      name: '尖草坪区'\n    }, {\n      name: '万柏林区'\n    }, {\n      name: '晋源区'\n    }]\n  }, {\n    name: '大同市',\n    children: [{\n      name: '平城区'\n    }, {\n      name: '云冈区'\n    }, {\n      name: '新荣区'\n    }, {\n      name: '云州区'\n    }]\n  }]\n}, {\n  name: '内蒙古自治区',\n  children: [{\n    name: '呼和浩特市',\n    children: [{\n      name: '新城区'\n    }, {\n      name: '回民区'\n    }, {\n      name: '玉泉区'\n    }, {\n      name: '赛罕区'\n    }, {\n      name: '土默特左旗'\n    }, {\n      name: '托克托县'\n    }]\n  }, {\n    name: '包头市',\n    children: [{\n      name: '东河区'\n    }, {\n      name: '昆都仑区'\n    }, {\n      name: '青山区'\n    }, {\n      name: '石拐区'\n    }, {\n      name: '白云鄂博矿区'\n    }, {\n      name: '九原区'\n    }]\n  }]\n}, {\n  name: '辽宁省',\n  children: [{\n    name: '沈阳市',\n    children: [{\n      name: '和平区'\n    }, {\n      name: '沈河区'\n    }, {\n      name: '大东区'\n    }, {\n      name: '皇姑区'\n    }, {\n      name: '铁西区'\n    }, {\n      name: '苏家屯区'\n    }, {\n      name: '浑南区'\n    }, {\n      name: '于洪区'\n    }]\n  }, {\n    name: '大连市',\n    children: [{\n      name: '中山区'\n    }, {\n      name: '西岗区'\n    }, {\n      name: '沙河口区'\n    }, {\n      name: '甘井子区'\n    }, {\n      name: '旅顺口区'\n    }, {\n      name: '金州区'\n    }, {\n      name: '普兰店区'\n    }]\n  }]\n}, {\n  name: '吉林省',\n  children: [{\n    name: '长春市',\n    children: [{\n      name: '南关区'\n    }, {\n      name: '宽城区'\n    }, {\n      name: '朝阳区'\n    }, {\n      name: '二道区'\n    }, {\n      name: '绿园区'\n    }, {\n      name: '双阳区'\n    }, {\n      name: '九台区'\n    }]\n  }, {\n    name: '吉林市',\n    children: [{\n      name: '昌邑区'\n    }, {\n      name: '龙潭区'\n    }, {\n      name: '船营区'\n    }, {\n      name: '丰满区'\n    }]\n  }]\n}, {\n  name: '黑龙江省',\n  children: [{\n    name: '哈尔滨市',\n    children: [{\n      name: '道里区'\n    }, {\n      name: '南岗区'\n    }, {\n      name: '道外区'\n    }, {\n      name: '平房区'\n    }, {\n      name: '松北区'\n    }, {\n      name: '香坊区'\n    }, {\n      name: '呼兰区'\n    }, {\n      name: '阿城区'\n    }]\n  }, {\n    name: '齐齐哈尔市',\n    children: [{\n      name: '龙沙区'\n    }, {\n      name: '建华区'\n    }, {\n      name: '铁锋区'\n    }, {\n      name: '昂昂溪区'\n    }, {\n      name: '富拉尔基区'\n    }]\n  }]\n}, {\n  name: '上海市',\n  children: [{\n    name: '上海市',\n    children: [{\n      name: '黄浦区'\n    }, {\n      name: '徐汇区'\n    }, {\n      name: '长宁区'\n    }, {\n      name: '静安区'\n    }, {\n      name: '普陀区'\n    }, {\n      name: '虹口区'\n    }, {\n      name: '杨浦区'\n    }, {\n      name: '闵行区'\n    }, {\n      name: '宝山区'\n    }, {\n      name: '嘉定区'\n    }, {\n      name: '浦东新区'\n    }, {\n      name: '金山区'\n    }, {\n      name: '松江区'\n    }, {\n      name: '青浦区'\n    }, {\n      name: '奉贤区'\n    }, {\n      name: '崇明区'\n    }]\n  }]\n}, {\n  name: '江苏省',\n  children: [{\n    name: '南京市',\n    children: [{\n      name: '鼓楼区'\n    }, {\n      name: '玄武区'\n    }, {\n      name: '秦淮区'\n    }, {\n      name: '建邺区'\n    }, {\n      name: '鼓楼区'\n    }, {\n      name: '浦口区'\n    }, {\n      name: '栖霞区'\n    }, {\n      name: '雨花台区'\n    }, {\n      name: '江宁区'\n    }, {\n      name: '六合区'\n    }, {\n      name: '溧水区'\n    }, {\n      name: '高淳区'\n    }]\n  }, {\n    name: '苏州市',\n    children: [{\n      name: '姑苏区'\n    }, {\n      name: '吴中区'\n    }, {\n      name: '相城区'\n    }, {\n      name: '吴江区'\n    }, {\n      name: '苏州工业园区'\n    }, {\n      name: '常熟市'\n    }, {\n      name: '张家港市'\n    }, {\n      name: '昆山市'\n    }, {\n      name: '太仓市'\n    }]\n  }, {\n    name: '无锡市',\n    children: [{\n      name: '梁溪区'\n    }, {\n      name: '惠山区'\n    }, {\n      name: '滨湖区'\n    }, {\n      name: '新吴区'\n    }, {\n      name: '锡山区'\n    }, {\n      name: '宜兴市'\n    }]\n  }, {\n    name: '徐州市',\n    children: [{\n      name: '鼓楼区'\n    }, {\n      name: '云龙区'\n    }, {\n      name: '贾汪区'\n    }, {\n      name: '泉山区'\n    }, {\n      name: '铜山区'\n    }, {\n      name: '丰县'\n    }, {\n      name: '沛县'\n    }, {\n      name: '睢宁县'\n    }, {\n      name: '新沂市'\n    }, {\n      name: '邳州市'\n    }]\n  }, {\n    name: '常州市',\n    children: [{\n      name: '天宁区'\n    }, {\n      name: '钟楼区'\n    }, {\n      name: '新北区'\n    }, {\n      name: '武进区'\n    }, {\n      name: '金坛区'\n    }, {\n      name: '溧阳市'\n    }]\n  }, {\n    name: '南通市',\n    children: [{\n      name: '崇川区'\n    }, {\n      name: '港闸区'\n    }, {\n      name: '通州区'\n    }, {\n      name: '如东县'\n    }, {\n      name: '启东市'\n    }, {\n      name: '如皋市'\n    }, {\n      name: '海门区'\n    }, {\n      name: '海安市'\n    }]\n  }, {\n    name: '扬州市',\n    children: [{\n      name: '广陵区'\n    }, {\n      name: '邗江区'\n    }, {\n      name: '江都区'\n    }, {\n      name: '宝应县'\n    }, {\n      name: '仪征市'\n    }, {\n      name: '高邮市'\n    }]\n  }, {\n    name: '镇江市',\n    children: [{\n      name: '京口区'\n    }, {\n      name: '润州区'\n    }, {\n      name: '丹徒区'\n    }, {\n      name: '丹阳市'\n    }, {\n      name: '扬中市'\n    }, {\n      name: '句容市'\n    }]\n  }, {\n    name: '泰州市',\n    children: [{\n      name: '海陵区'\n    }, {\n      name: '高港区'\n    }, {\n      name: '姜堰区'\n    }, {\n      name: '兴化市'\n    }, {\n      name: '靖江市'\n    }, {\n      name: '泰兴市'\n    }]\n  }]\n}, {\n  name: '浙江省',\n  children: [{\n    name: '杭州市',\n    children: [{\n      name: '上城区'\n    }, {\n      name: '下城区'\n    }, {\n      name: '江干区'\n    }, {\n      name: '拱墅区'\n    }, {\n      name: '西湖区'\n    }, {\n      name: '滨江区'\n    }, {\n      name: '萧山区'\n    }, {\n      name: '余杭区'\n    }, {\n      name: '富阳区'\n    }, {\n      name: '临安区'\n    }, {\n      name: '钱塘区'\n    }, {\n      name: '临平区'\n    }]\n  }, {\n    name: '宁波市',\n    children: [{\n      name: '海曙区'\n    }, {\n      name: '江北区'\n    }, {\n      name: '北仑区'\n    }, {\n      name: '镇海区'\n    }, {\n      name: '鄞州区'\n    }, {\n      name: '奉化区'\n    }, {\n      name: '余姚市'\n    }, {\n      name: '慈溪市'\n    }, {\n      name: '象山县'\n    }, {\n      name: '宁海县'\n    }]\n  }, {\n    name: '温州市',\n    children: [{\n      name: '鹿城区'\n    }, {\n      name: '龙湾区'\n    }, {\n      name: '瓯海区'\n    }, {\n      name: '洞头区'\n    }, {\n      name: '永嘉县'\n    }, {\n      name: '平阳县'\n    }, {\n      name: '苍南县'\n    }, {\n      name: '文成县'\n    }, {\n      name: '泰顺县'\n    }, {\n      name: '瑞安市'\n    }, {\n      name: '乐清市'\n    }]\n  }, {\n    name: '嘉兴市',\n    children: [{\n      name: '南湖区'\n    }, {\n      name: '秀洲区'\n    }, {\n      name: '嘉善县'\n    }, {\n      name: '海盐县'\n    }, {\n      name: '海宁市'\n    }, {\n      name: '平湖市'\n    }, {\n      name: '桐乡市'\n    }]\n  }, {\n    name: '湖州市',\n    children: [{\n      name: '吴兴区'\n    }, {\n      name: '南浔区'\n    }, {\n      name: '德清县'\n    }, {\n      name: '长兴县'\n    }, {\n      name: '安吉县'\n    }]\n  }, {\n    name: '绍兴市',\n    children: [{\n      name: '越城区'\n    }, {\n      name: '柯桥区'\n    }, {\n      name: '上虞区'\n    }, {\n      name: '新昌县'\n    }, {\n      name: '诸暨市'\n    }, {\n      name: '嵊州市'\n    }]\n  }, {\n    name: '金华市',\n    children: [{\n      name: '婺城区'\n    }, {\n      name: '金东区'\n    }, {\n      name: '武义县'\n    }, {\n      name: '浦江县'\n    }, {\n      name: '磐安县'\n    }, {\n      name: '兰溪市'\n    }, {\n      name: '义乌市'\n    }, {\n      name: '东阳市'\n    }, {\n      name: '永康市'\n    }]\n  }, {\n    name: '台州市',\n    children: [{\n      name: '椒江区'\n    }, {\n      name: '黄岩区'\n    }, {\n      name: '路桥区'\n    }, {\n      name: '三门县'\n    }, {\n      name: '天台县'\n    }, {\n      name: '仙居县'\n    }, {\n      name: '温岭市'\n    }, {\n      name: '临海市'\n    }, {\n      name: '玉环市'\n    }]\n  }, {\n    name: '衢州市',\n    children: [{\n      name: '柯城区'\n    }, {\n      name: '衢江区'\n    }, {\n      name: '常山县'\n    }, {\n      name: '开化县'\n    }, {\n      name: '龙游县'\n    }, {\n      name: '江山市'\n    }]\n  }]\n}, {\n  name: '福建省',\n  children: [{\n    name: '福州市',\n    children: [{\n      name: '鼓楼区'\n    }, {\n      name: '台江区'\n    }, {\n      name: '仓山区'\n    }, {\n      name: '马尾区'\n    }, {\n      name: '晋安区'\n    }, {\n      name: '长乐区'\n    }]\n  }, {\n    name: '厦门市',\n    children: [{\n      name: '思明区'\n    }, {\n      name: '海沧区'\n    }, {\n      name: '湖里区'\n    }, {\n      name: '集美区'\n    }, {\n      name: '同安区'\n    }, {\n      name: '翔安区'\n    }]\n  }]\n}, {\n  name: '江西省',\n  children: [{\n    name: '南昌市',\n    children: [{\n      name: '东湖区'\n    }, {\n      name: '西湖区'\n    }, {\n      name: '青云谱区'\n    }, {\n      name: '湾里区'\n    }, {\n      name: '青山湖区'\n    }, {\n      name: '新建区'\n    }]\n  }, {\n    name: '赣州市',\n    children: [{\n      name: '章贡区'\n    }, {\n      name: '南康区'\n    }, {\n      name: '赣县区'\n    }]\n  }]\n}, {\n  name: '山东省',\n  children: [{\n    name: '济南市',\n    children: [{\n      name: '历下区'\n    }, {\n      name: '市中区'\n    }, {\n      name: '槐荫区'\n    }, {\n      name: '天桥区'\n    }, {\n      name: '历城区'\n    }, {\n      name: '长清区'\n    }, {\n      name: '章丘区'\n    }]\n  }, {\n    name: '青岛市',\n    children: [{\n      name: '市南区'\n    }, {\n      name: '市北区'\n    }, {\n      name: '黄岛区'\n    }, {\n      name: '崂山区'\n    }, {\n      name: '李沧区'\n    }, {\n      name: '城阳区'\n    }]\n  }, {\n    name: '烟台市',\n    children: [{\n      name: '芝罘区'\n    }, {\n      name: '福山区'\n    }, {\n      name: '牟平区'\n    }, {\n      name: '莱山区'\n    }, {\n      name: '蓬莱区'\n    }, {\n      name: '龙口市'\n    }, {\n      name: '莱阳市'\n    }, {\n      name: '莱州市'\n    }, {\n      name: '招远市'\n    }, {\n      name: '栖霞市'\n    }, {\n      name: '海阳市'\n    }]\n  }, {\n    name: '潍坊市',\n    children: [{\n      name: '潍城区'\n    }, {\n      name: '寒亭区'\n    }, {\n      name: '坊子区'\n    }, {\n      name: '奎文区'\n    }, {\n      name: '临朐县'\n    }, {\n      name: '昌乐县'\n    }, {\n      name: '青州市'\n    }, {\n      name: '诸城市'\n    }, {\n      name: '寿光市'\n    }, {\n      name: '安丘市'\n    }, {\n      name: '高密市'\n    }, {\n      name: '昌邑市'\n    }]\n  }, {\n    name: '济宁市',\n    children: [{\n      name: '任城区'\n    }, {\n      name: '兖州区'\n    }, {\n      name: '微山县'\n    }, {\n      name: '鱼台县'\n    }, {\n      name: '金乡县'\n    }, {\n      name: '嘉祥县'\n    }, {\n      name: '汶上县'\n    }, {\n      name: '泗水县'\n    }, {\n      name: '梁山县'\n    }, {\n      name: '曲阜市'\n    }, {\n      name: '邹城市'\n    }]\n  }, {\n    name: '泰安市',\n    children: [{\n      name: '泰山区'\n    }, {\n      name: '岱岳区'\n    }, {\n      name: '宁阳县'\n    }, {\n      name: '东平县'\n    }, {\n      name: '新泰市'\n    }, {\n      name: '肥城市'\n    }]\n  }, {\n    name: '威海市',\n    children: [{\n      name: '环翠区'\n    }, {\n      name: '文登区'\n    }, {\n      name: '荣成市'\n    }, {\n      name: '乳山市'\n    }]\n  }, {\n    name: '淄博市',\n    children: [{\n      name: '淄川区'\n    }, {\n      name: '张店区'\n    }, {\n      name: '博山区'\n    }, {\n      name: '临淄区'\n    }, {\n      name: '周村区'\n    }, {\n      name: '桓台县'\n    }, {\n      name: '高青县'\n    }, {\n      name: '沂源县'\n    }]\n  }]\n}, {\n  name: '河南省',\n  children: [{\n    name: '郑州市',\n    children: [{\n      name: '中原区'\n    }, {\n      name: '二七区'\n    }, {\n      name: '管城回族区'\n    }, {\n      name: '金水区'\n    }, {\n      name: '上街区'\n    }, {\n      name: '惠济区'\n    }, {\n      name: '中牟县'\n    }]\n  }, {\n    name: '洛阳市',\n    children: [{\n      name: '老城区'\n    }, {\n      name: '西工区'\n    }, {\n      name: '瀍河回族区'\n    }, {\n      name: '涧西区'\n    }, {\n      name: '吉利区'\n    }, {\n      name: '洛龙区'\n    }]\n  }, {\n    name: '开封市',\n    children: [{\n      name: '龙亭区'\n    }, {\n      name: '顺河回族区'\n    }, {\n      name: '鼓楼区'\n    }, {\n      name: '禹王台区'\n    }, {\n      name: '祥符区'\n    }, {\n      name: '杞县'\n    }, {\n      name: '通许县'\n    }, {\n      name: '尉氏县'\n    }, {\n      name: '兰考县'\n    }]\n  }, {\n    name: '新乡市',\n    children: [{\n      name: '红旗区'\n    }, {\n      name: '卫滨区'\n    }, {\n      name: '凤泉区'\n    }, {\n      name: '牧野区'\n    }, {\n      name: '新乡县'\n    }, {\n      name: '获嘉县'\n    }, {\n      name: '原阳县'\n    }, {\n      name: '延津县'\n    }, {\n      name: '封丘县'\n    }, {\n      name: '长垣市'\n    }, {\n      name: '卫辉市'\n    }, {\n      name: '辉县市'\n    }]\n  }, {\n    name: '许昌市',\n    children: [{\n      name: '魏都区'\n    }, {\n      name: '建安区'\n    }, {\n      name: '鄢陵县'\n    }, {\n      name: '襄城县'\n    }, {\n      name: '禹州市'\n    }, {\n      name: '长葛市'\n    }]\n  }]\n}, {\n  name: '湖北省',\n  children: [{\n    name: '武汉市',\n    children: [{\n      name: '江岸区'\n    }, {\n      name: '江汉区'\n    }, {\n      name: '硚口区'\n    }, {\n      name: '汉阳区'\n    }, {\n      name: '武昌区'\n    }, {\n      name: '青山区'\n    }, {\n      name: '洪山区'\n    }, {\n      name: '东西湖区'\n    }, {\n      name: '汉南区'\n    }, {\n      name: '蔡甸区'\n    }, {\n      name: '江夏区'\n    }, {\n      name: '黄陂区'\n    }, {\n      name: '新洲区'\n    }]\n  }, {\n    name: '宜昌市',\n    children: [{\n      name: '西陵区'\n    }, {\n      name: '伍家岗区'\n    }, {\n      name: '点军区'\n    }, {\n      name: '猇亭区'\n    }, {\n      name: '夷陵区'\n    }]\n  }, {\n    name: '襄阳市',\n    children: [{\n      name: '襄城区'\n    }, {\n      name: '樊城区'\n    }, {\n      name: '襄州区'\n    }, {\n      name: '南漳县'\n    }, {\n      name: '谷城县'\n    }, {\n      name: '保康县'\n    }, {\n      name: '老河口市'\n    }, {\n      name: '枣阳市'\n    }, {\n      name: '宜城市'\n    }]\n  }, {\n    name: '荆州市',\n    children: [{\n      name: '沙市区'\n    }, {\n      name: '荆州区'\n    }, {\n      name: '公安县'\n    }, {\n      name: '监利市'\n    }, {\n      name: '江陵县'\n    }, {\n      name: '石首市'\n    }, {\n      name: '洪湖市'\n    }, {\n      name: '松滋市'\n    }]\n  }, {\n    name: '黄石市',\n    children: [{\n      name: '黄石港区'\n    }, {\n      name: '西塞山区'\n    }, {\n      name: '下陆区'\n    }, {\n      name: '铁山区'\n    }, {\n      name: '阳新县'\n    }, {\n      name: '大冶市'\n    }]\n  }, {\n    name: '十堰市',\n    children: [{\n      name: '茅箭区'\n    }, {\n      name: '张湾区'\n    }, {\n      name: '郧阳区'\n    }, {\n      name: '郧西县'\n    }, {\n      name: '竹山县'\n    }, {\n      name: '竹溪县'\n    }, {\n      name: '房县'\n    }, {\n      name: '丹江口市'\n    }]\n  }]\n}, {\n  name: '湖南省',\n  children: [{\n    name: '长沙市',\n    children: [{\n      name: '芙蓉区'\n    }, {\n      name: '天心区'\n    }, {\n      name: '岳麓区'\n    }, {\n      name: '开福区'\n    }, {\n      name: '雨花区'\n    }, {\n      name: '望城区'\n    }, {\n      name: '长沙县'\n    }]\n  }, {\n    name: '株洲市',\n    children: [{\n      name: '荷塘区'\n    }, {\n      name: '芦淞区'\n    }, {\n      name: '石峰区'\n    }, {\n      name: '天元区'\n    }, {\n      name: '渌口区'\n    }]\n  }]\n}, {\n  name: '广东省',\n  children: [{\n    name: '广州市',\n    children: [{\n      name: '荔湾区'\n    }, {\n      name: '越秀区'\n    }, {\n      name: '海珠区'\n    }, {\n      name: '天河区'\n    }, {\n      name: '白云区'\n    }, {\n      name: '黄埔区'\n    }, {\n      name: '番禺区'\n    }, {\n      name: '花都区'\n    }, {\n      name: '南沙区'\n    }, {\n      name: '从化区'\n    }, {\n      name: '增城区'\n    }]\n  }, {\n    name: '深圳市',\n    children: [{\n      name: '福田区'\n    }, {\n      name: '罗湖区'\n    }, {\n      name: '南山区'\n    }, {\n      name: '宝安区'\n    }, {\n      name: '龙岗区'\n    }, {\n      name: '盐田区'\n    }, {\n      name: '龙华区'\n    }, {\n      name: '坪山区'\n    }, {\n      name: '光明区'\n    }]\n  }, {\n    name: '珠海市',\n    children: [{\n      name: '香洲区'\n    }, {\n      name: '斗门区'\n    }, {\n      name: '金湾区'\n    }]\n  }, {\n    name: '佛山市',\n    children: [{\n      name: '禅城区'\n    }, {\n      name: '南海区'\n    }, {\n      name: '顺德区'\n    }, {\n      name: '三水区'\n    }, {\n      name: '高明区'\n    }]\n  }, {\n    name: '东莞市',\n    children: [{\n      name: '东城街道'\n    }, {\n      name: '南城街道'\n    }, {\n      name: '万江街道'\n    }, {\n      name: '莞城街道'\n    }, {\n      name: '虎门镇'\n    }, {\n      name: '长安镇'\n    }, {\n      name: '厚街镇'\n    }, {\n      name: '塘厦镇'\n    }, {\n      name: '常平镇'\n    }, {\n      name: '寮步镇'\n    }]\n  }, {\n    name: '中山市',\n    children: [{\n      name: '石岐区街道'\n    }, {\n      name: '东区街道'\n    }, {\n      name: '西区街道'\n    }, {\n      name: '南区街道'\n    }, {\n      name: '火炬开发区'\n    }, {\n      name: '小榄镇'\n    }, {\n      name: '古镇镇'\n    }]\n  }, {\n    name: '惠州市',\n    children: [{\n      name: '惠城区'\n    }, {\n      name: '惠阳区'\n    }, {\n      name: '博罗县'\n    }, {\n      name: '惠东县'\n    }, {\n      name: '龙门县'\n    }]\n  }, {\n    name: '汕头市',\n    children: [{\n      name: '龙湖区'\n    }, {\n      name: '金平区'\n    }, {\n      name: '濠江区'\n    }, {\n      name: '潮阳区'\n    }, {\n      name: '潮南区'\n    }, {\n      name: '澄海区'\n    }, {\n      name: '南澳县'\n    }]\n  }]\n}, {\n  name: '广西壮族自治区',\n  children: [{\n    name: '南宁市',\n    children: [{\n      name: '兴宁区'\n    }, {\n      name: '青秀区'\n    }, {\n      name: '江南区'\n    }, {\n      name: '西乡塘区'\n    }, {\n      name: '良庆区'\n    }, {\n      name: '邕宁区'\n    }, {\n      name: '武鸣区'\n    }]\n  }, {\n    name: '桂林市',\n    children: [{\n      name: '秀峰区'\n    }, {\n      name: '叠彩区'\n    }, {\n      name: '象山区'\n    }, {\n      name: '七星区'\n    }, {\n      name: '雁山区'\n    }]\n  }]\n}, {\n  name: '海南省',\n  children: [{\n    name: '海口市',\n    children: [{\n      name: '秀英区'\n    }, {\n      name: '龙华区'\n    }, {\n      name: '琼山区'\n    }, {\n      name: '美兰区'\n    }]\n  }, {\n    name: '三亚市',\n    children: [{\n      name: '海棠区'\n    }, {\n      name: '吉阳区'\n    }, {\n      name: '天涯区'\n    }, {\n      name: '崖州区'\n    }]\n  }]\n}, {\n  name: '重庆市',\n  children: [{\n    name: '重庆市',\n    children: [{\n      name: '渝中区'\n    }, {\n      name: '江北区'\n    }, {\n      name: '南岸区'\n    }, {\n      name: '沙坪坝区'\n    }, {\n      name: '九龙坡区'\n    }, {\n      name: '大渡口区'\n    }, {\n      name: '渝北区'\n    }, {\n      name: '巴南区'\n    }, {\n      name: '北碚区'\n    }, {\n      name: '綦江区'\n    }, {\n      name: '长寿区'\n    }, {\n      name: '江津区'\n    }, {\n      name: '合川区'\n    }, {\n      name: '永川区'\n    }, {\n      name: '南川区'\n    }]\n  }]\n}, {\n  name: '四川省',\n  children: [{\n    name: '成都市',\n    children: [{\n      name: '锦江区'\n    }, {\n      name: '青羊区'\n    }, {\n      name: '金牛区'\n    }, {\n      name: '武侯区'\n    }, {\n      name: '成华区'\n    }, {\n      name: '龙泉驿区'\n    }, {\n      name: '青白江区'\n    }, {\n      name: '新都区'\n    }, {\n      name: '温江区'\n    }, {\n      name: '双流区'\n    }, {\n      name: '郫都区'\n    }]\n  }, {\n    name: '绵阳市',\n    children: [{\n      name: '涪城区'\n    }, {\n      name: '游仙区'\n    }, {\n      name: '安州区'\n    }]\n  }, {\n    name: '乐山市',\n    children: [{\n      name: '市中区'\n    }, {\n      name: '沙湾区'\n    }, {\n      name: '五通桥区'\n    }, {\n      name: '金口河区'\n    }]\n  }, {\n    name: '泸州市',\n    children: [{\n      name: '江阳区'\n    }, {\n      name: '纳溪区'\n    }, {\n      name: '龙马潭区'\n    }, {\n      name: '泸县'\n    }, {\n      name: '合江县'\n    }, {\n      name: '叙永县'\n    }, {\n      name: '古蔺县'\n    }]\n  }, {\n    name: '德阳市',\n    children: [{\n      name: '旌阳区'\n    }, {\n      name: '罗江区'\n    }, {\n      name: '中江县'\n    }, {\n      name: '广汉市'\n    }, {\n      name: '什邡市'\n    }, {\n      name: '绵竹市'\n    }]\n  }, {\n    name: '内江市',\n    children: [{\n      name: '市中区'\n    }, {\n      name: '东兴区'\n    }]\n  }]\n}, {\n  name: '贵州省',\n  children: [{\n    name: '贵阳市',\n    children: [{\n      name: '南明区'\n    }, {\n      name: '云岩区'\n    }, {\n      name: '花溪区'\n    }, {\n      name: '乌当区'\n    }, {\n      name: '白云区'\n    }, {\n      name: '观山湖区'\n    }]\n  }, {\n    name: '遵义市',\n    children: [{\n      name: '红花岗区'\n    }, {\n      name: '汇川区'\n    }, {\n      name: '播州区'\n    }]\n  }, {\n    name: '六盘水市',\n    children: [{\n      name: '钟山区'\n    }, {\n      name: '六枝特区'\n    }, {\n      name: '水城区'\n    }, {\n      name: '盘州市'\n    }]\n  }, {\n    name: '安顺市',\n    children: [{\n      name: '西秀区'\n    }, {\n      name: '平坝区'\n    }, {\n      name: '普定县'\n    }, {\n      name: '镇宁布依族苗族自治县'\n    }, {\n      name: '关岭布依族苗族自治县'\n    }, {\n      name: '紫云苗族布依族自治县'\n    }]\n  }, {\n    name: '毕节市',\n    children: [{\n      name: '七星关区'\n    }, {\n      name: '大方县'\n    }, {\n      name: '黔西市'\n    }, {\n      name: '金沙县'\n    }, {\n      name: '织金县'\n    }, {\n      name: '纳雍县'\n    }, {\n      name: '威宁彝族回族苗族自治县'\n    }, {\n      name: '赫章县'\n    }]\n  }]\n}, {\n  name: '云南省',\n  children: [{\n    name: '昆明市',\n    children: [{\n      name: '五华区'\n    }, {\n      name: '盘龙区'\n    }, {\n      name: '官渡区'\n    }, {\n      name: '西山区'\n    }, {\n      name: '东川区'\n    }, {\n      name: '呈贡区'\n    }, {\n      name: '晋宁区'\n    }]\n  }, {\n    name: '大理白族自治州',\n    children: [{\n      name: '大理市'\n    }, {\n      name: '漾濞县'\n    }, {\n      name: '祥云县'\n    }, {\n      name: '宾川县'\n    }]\n  }, {\n    name: '曲靖市',\n    children: [{\n      name: '麒麟区'\n    }, {\n      name: '沾益区'\n    }, {\n      name: '马龙区'\n    }, {\n      name: '陆良县'\n    }, {\n      name: '师宗县'\n    }, {\n      name: '罗平县'\n    }, {\n      name: '富源县'\n    }, {\n      name: '会泽县'\n    }, {\n      name: '宣威市'\n    }]\n  }, {\n    name: '玉溪市',\n    children: [{\n      name: '红塔区'\n    }, {\n      name: '江川区'\n    }, {\n      name: '通海县'\n    }, {\n      name: '华宁县'\n    }, {\n      name: '易门县'\n    }, {\n      name: '峨山彝族自治县'\n    }, {\n      name: '新平彝族傣族自治县'\n    }, {\n      name: '元江哈尼族彝族傣族自治县'\n    }]\n  }, {\n    name: '保山市',\n    children: [{\n      name: '隆阳区'\n    }, {\n      name: '施甸县'\n    }, {\n      name: '龙陵县'\n    }, {\n      name: '昌宁县'\n    }, {\n      name: '腾冲市'\n    }]\n  }]\n}, {\n  name: '西藏自治区',\n  children: [{\n    name: '拉萨市',\n    children: [{\n      name: '城关区'\n    }, {\n      name: '堆龙德庆区'\n    }, {\n      name: '达孜区'\n    }, {\n      name: '林周县'\n    }]\n  }, {\n    name: '日喀则市',\n    children: [{\n      name: '桑珠孜区'\n    }, {\n      name: '南木林县'\n    }, {\n      name: '江孜县'\n    }]\n  }]\n}, {\n  name: '陕西省',\n  children: [{\n    name: '西安市',\n    children: [{\n      name: '新城区'\n    }, {\n      name: '碑林区'\n    }, {\n      name: '莲湖区'\n    }, {\n      name: '灞桥区'\n    }, {\n      name: '未央区'\n    }, {\n      name: '雁塔区'\n    }, {\n      name: '阎良区'\n    }, {\n      name: '临潼区'\n    }, {\n      name: '长安区'\n    }, {\n      name: '高陵区'\n    }, {\n      name: '鄠邑区'\n    }, {\n      name: '蓝田县'\n    }, {\n      name: '周至县'\n    }]\n  }, {\n    name: '咸阳市',\n    children: [{\n      name: '秦都区'\n    }, {\n      name: '杨陵区'\n    }, {\n      name: '渭城区'\n    }, {\n      name: '三原县'\n    }, {\n      name: '泾阳县'\n    }, {\n      name: '乾县'\n    }, {\n      name: '礼泉县'\n    }, {\n      name: '永寿县'\n    }, {\n      name: '彬州市'\n    }, {\n      name: '长武县'\n    }, {\n      name: '旬邑县'\n    }, {\n      name: '淳化县'\n    }, {\n      name: '武功县'\n    }, {\n      name: '兴平市'\n    }]\n  }, {\n    name: '宝鸡市',\n    children: [{\n      name: '渭滨区'\n    }, {\n      name: '金台区'\n    }, {\n      name: '陈仓区'\n    }, {\n      name: '凤翔区'\n    }, {\n      name: '岐山县'\n    }, {\n      name: '扶风县'\n    }, {\n      name: '眉县'\n    }, {\n      name: '陇县'\n    }, {\n      name: '千阳县'\n    }, {\n      name: '麟游县'\n    }, {\n      name: '凤县'\n    }, {\n      name: '太白县'\n    }]\n  }, {\n    name: '渭南市',\n    children: [{\n      name: '临渭区'\n    }, {\n      name: '华州区'\n    }, {\n      name: '潼关县'\n    }, {\n      name: '大荔县'\n    }, {\n      name: '合阳县'\n    }, {\n      name: '澄城县'\n    }, {\n      name: '蒲城县'\n    }, {\n      name: '白水县'\n    }, {\n      name: '富平县'\n    }, {\n      name: '韩城市'\n    }, {\n      name: '华阴市'\n    }]\n  }, {\n    name: '延安市',\n    children: [{\n      name: '宝塔区'\n    }, {\n      name: '安塞区'\n    }, {\n      name: '延长县'\n    }, {\n      name: '延川县'\n    }, {\n      name: '子长市'\n    }, {\n      name: '志丹县'\n    }, {\n      name: '吴起县'\n    }, {\n      name: '甘泉县'\n    }, {\n      name: '富县'\n    }, {\n      name: '洛川县'\n    }, {\n      name: '宜川县'\n    }, {\n      name: '黄龙县'\n    }, {\n      name: '黄陵县'\n    }]\n  }, {\n    name: '汉中市',\n    children: [{\n      name: '汉台区'\n    }, {\n      name: '南郑区'\n    }, {\n      name: '城固县'\n    }, {\n      name: '洋县'\n    }, {\n      name: '西乡县'\n    }, {\n      name: '勉县'\n    }, {\n      name: '宁强县'\n    }, {\n      name: '略阳县'\n    }, {\n      name: '镇巴县'\n    }, {\n      name: '留坝县'\n    }, {\n      name: '佛坪县'\n    }]\n  }, {\n    name: '榆林市',\n    children: [{\n      name: '榆阳区'\n    }, {\n      name: '横山区'\n    }, {\n      name: '神木市'\n    }, {\n      name: '府谷县'\n    }, {\n      name: '靖边县'\n    }, {\n      name: '定边县'\n    }, {\n      name: '绥德县'\n    }, {\n      name: '米脂县'\n    }, {\n      name: '佳县'\n    }, {\n      name: '吴堡县'\n    }, {\n      name: '清涧县'\n    }, {\n      name: '子洲县'\n    }]\n  }, {\n    name: '安康市',\n    children: [{\n      name: '汉滨区'\n    }, {\n      name: '汉阴县'\n    }, {\n      name: '石泉县'\n    }, {\n      name: '宁陕县'\n    }, {\n      name: '紫阳县'\n    }, {\n      name: '岚皋县'\n    }, {\n      name: '平利县'\n    }, {\n      name: '镇坪县'\n    }, {\n      name: '旬阳市'\n    }, {\n      name: '白河县'\n    }]\n  }, {\n    name: '商洛市',\n    children: [{\n      name: '商州区'\n    }, {\n      name: '洛南县'\n    }, {\n      name: '丹凤县'\n    }, {\n      name: '商南县'\n    }, {\n      name: '山阳县'\n    }, {\n      name: '镇安县'\n    }, {\n      name: '柞水县'\n    }]\n  }]\n}, {\n  name: '甘肃省',\n  children: [{\n    name: '兰州市',\n    children: [{\n      name: '城关区'\n    }, {\n      name: '七里河区'\n    }, {\n      name: '西固区'\n    }, {\n      name: '安宁区'\n    }, {\n      name: '红古区'\n    }]\n  }, {\n    name: '天水市',\n    children: [{\n      name: '秦州区'\n    }, {\n      name: '麦积区'\n    }]\n  }, {\n    name: '金昌市',\n    children: [{\n      name: '金川区'\n    }, {\n      name: '永昌县'\n    }]\n  }, {\n    name: '白银市',\n    children: [{\n      name: '白银区'\n    }, {\n      name: '平川区'\n    }, {\n      name: '靖远县'\n    }, {\n      name: '会宁县'\n    }, {\n      name: '景泰县'\n    }]\n  }, {\n    name: '张掖市',\n    children: [{\n      name: '甘州区'\n    }, {\n      name: '肃南裕固族自治县'\n    }, {\n      name: '民乐县'\n    }, {\n      name: '临泽县'\n    }, {\n      name: '高台县'\n    }, {\n      name: '山丹县'\n    }]\n  }, {\n    name: '酒泉市',\n    children: [{\n      name: '肃州区'\n    }, {\n      name: '金塔县'\n    }, {\n      name: '瓜州县'\n    }, {\n      name: '肃北蒙古族自治县'\n    }, {\n      name: '阿克塞哈萨克族自治县'\n    }, {\n      name: '玉门市'\n    }, {\n      name: '敦煌市'\n    }]\n  }]\n}, {\n  name: '青海省',\n  children: [{\n    name: '西宁市',\n    children: [{\n      name: '城东区'\n    }, {\n      name: '城中区'\n    }, {\n      name: '城西区'\n    }, {\n      name: '城北区'\n    }, {\n      name: '大通回族土族自治县'\n    }]\n  }, {\n    name: '海东市',\n    children: [{\n      name: '乐都区'\n    }, {\n      name: '平安区'\n    }]\n  }]\n}, {\n  name: '宁夏回族自治区',\n  children: [{\n    name: '银川市',\n    children: [{\n      name: '兴庆区'\n    }, {\n      name: '西夏区'\n    }, {\n      name: '金凤区'\n    }, {\n      name: '永宁县'\n    }, {\n      name: '贺兰县'\n    }]\n  }, {\n    name: '石嘴山市',\n    children: [{\n      name: '大武口区'\n    }, {\n      name: '惠农区'\n    }, {\n      name: '平罗县'\n    }]\n  }]\n}, {\n  name: '新疆维吾尔自治区',\n  children: [{\n    name: '乌鲁木齐市',\n    children: [{\n      name: '天山区'\n    }, {\n      name: '沙依巴克区'\n    }, {\n      name: '新市区'\n    }, {\n      name: '水磨沟区'\n    }, {\n      name: '头屯河区'\n    }, {\n      name: '达坂城区'\n    }, {\n      name: '米东区'\n    }]\n  }, {\n    name: '克拉玛依市',\n    children: [{\n      name: '独山子区'\n    }, {\n      name: '克拉玛依区'\n    }, {\n      name: '白碱滩区'\n    }, {\n      name: '乌尔禾区'\n    }]\n  }]\n}];", "map": {"version": 3, "names": ["regionData", "name", "children"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/src/config/region-data.js"], "sourcesContent": ["export const regionData = [\n  {\n    name: '北京市',\n    children: [{\n      name: '北京市',\n      children: [\n        { name: '东城区' },\n        { name: '西城区' },\n        { name: '朝阳区' },\n        { name: '丰台区' },\n        { name: '石景山区' },\n        { name: '海淀区' },\n        { name: '门头沟区' },\n        { name: '房山区' },\n        { name: '通州区' },\n        { name: '顺义区' },\n        { name: '昌平区' },\n        { name: '大兴区' },\n        { name: '怀柔区' },\n        { name: '平谷区' },\n        { name: '密云区' },\n        { name: '延庆区' }\n      ]\n    }]\n  },\n  {\n    name: '天津市',\n    children: [{\n      name: '天津市',\n      children: [\n        { name: '和平区' },\n        { name: '河东区' },\n        { name: '河西区' },\n        { name: '南开区' },\n        { name: '河北区' },\n        { name: '红桥区' },\n        { name: '东丽区' },\n        { name: '西青区' },\n        { name: '津南区' },\n        { name: '北辰区' },\n        { name: '武清区' },\n        { name: '宝坻区' },\n        { name: '滨海新区' },\n        { name: '宁河区' },\n        { name: '静海区' },\n        { name: '蓟州区' }\n      ]\n    }]\n  },\n  {\n    name: '河北省',\n    children: [\n      {\n        name: '石家庄市',\n        children: [\n          { name: '长安区' },\n          { name: '桥西区' },\n          { name: '新华区' },\n          { name: '井陉矿区' },\n          { name: '裕华区' },\n          { name: '藁城区' },\n          { name: '鹿泉区' },\n          { name: '栾城区' }\n        ]\n      },\n      {\n        name: '唐山市',\n        children: [\n          { name: '路南区' },\n          { name: '路北区' },\n          { name: '古冶区' },\n          { name: '开平区' },\n          { name: '丰南区' },\n          { name: '丰润区' },\n          { name: '曹妃甸区' }\n        ]\n      },\n      {\n        name: '秦皇岛市',\n        children: [\n          { name: '海港区' },\n          { name: '山海关区' },\n          { name: '北戴河区' },\n          { name: '抚宁区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '山西省',\n    children: [\n      {\n        name: '太原市',\n        children: [\n          { name: '小店区' },\n          { name: '迎泽区' },\n          { name: '杏花岭区' },\n          { name: '尖草坪区' },\n          { name: '万柏林区' },\n          { name: '晋源区' }\n        ]\n      },\n      {\n        name: '大同市',\n        children: [\n          { name: '平城区' },\n          { name: '云冈区' },\n          { name: '新荣区' },\n          { name: '云州区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '内蒙古自治区',\n    children: [\n      {\n        name: '呼和浩特市',\n        children: [\n          { name: '新城区' },\n          { name: '回民区' },\n          { name: '玉泉区' },\n          { name: '赛罕区' },\n          { name: '土默特左旗' },\n          { name: '托克托县' }\n        ]\n      },\n      {\n        name: '包头市',\n        children: [\n          { name: '东河区' },\n          { name: '昆都仑区' },\n          { name: '青山区' },\n          { name: '石拐区' },\n          { name: '白云鄂博矿区' },\n          { name: '九原区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '辽宁省',\n    children: [\n      {\n        name: '沈阳市',\n        children: [\n          { name: '和平区' },\n          { name: '沈河区' },\n          { name: '大东区' },\n          { name: '皇姑区' },\n          { name: '铁西区' },\n          { name: '苏家屯区' },\n          { name: '浑南区' },\n          { name: '于洪区' }\n        ]\n      },\n      {\n        name: '大连市',\n        children: [\n          { name: '中山区' },\n          { name: '西岗区' },\n          { name: '沙河口区' },\n          { name: '甘井子区' },\n          { name: '旅顺口区' },\n          { name: '金州区' },\n          { name: '普兰店区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '吉林省',\n    children: [\n      {\n        name: '长春市',\n        children: [\n          { name: '南关区' },\n          { name: '宽城区' },\n          { name: '朝阳区' },\n          { name: '二道区' },\n          { name: '绿园区' },\n          { name: '双阳区' },\n          { name: '九台区' }\n        ]\n      },\n      {\n        name: '吉林市',\n        children: [\n          { name: '昌邑区' },\n          { name: '龙潭区' },\n          { name: '船营区' },\n          { name: '丰满区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '黑龙江省',\n    children: [\n      {\n        name: '哈尔滨市',\n        children: [\n          { name: '道里区' },\n          { name: '南岗区' },\n          { name: '道外区' },\n          { name: '平房区' },\n          { name: '松北区' },\n          { name: '香坊区' },\n          { name: '呼兰区' },\n          { name: '阿城区' }\n        ]\n      },\n      {\n        name: '齐齐哈尔市',\n        children: [\n          { name: '龙沙区' },\n          { name: '建华区' },\n          { name: '铁锋区' },\n          { name: '昂昂溪区' },\n          { name: '富拉尔基区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '上海市',\n    children: [{\n      name: '上海市',\n      children: [\n        { name: '黄浦区' },\n        { name: '徐汇区' },\n        { name: '长宁区' },\n        { name: '静安区' },\n        { name: '普陀区' },\n        { name: '虹口区' },\n        { name: '杨浦区' },\n        { name: '闵行区' },\n        { name: '宝山区' },\n        { name: '嘉定区' },\n        { name: '浦东新区' },\n        { name: '金山区' },\n        { name: '松江区' },\n        { name: '青浦区' },\n        { name: '奉贤区' },\n        { name: '崇明区' }\n      ]\n    }]\n  },\n  {\n    name: '江苏省',\n    children: [\n      {\n        name: '南京市',\n        children: [\n          { name: '鼓楼区' },\n          { name: '玄武区' },\n          { name: '秦淮区' },\n          { name: '建邺区' },\n          { name: '鼓楼区' },\n          { name: '浦口区' },\n          { name: '栖霞区' },\n          { name: '雨花台区' },\n          { name: '江宁区' },\n          { name: '六合区' },\n          { name: '溧水区' },\n          { name: '高淳区' }\n        ]\n      },\n      {\n        name: '苏州市',\n        children: [\n          { name: '姑苏区' },\n          { name: '吴中区' },\n          { name: '相城区' },\n          { name: '吴江区' },\n          { name: '苏州工业园区' },\n          { name: '常熟市' },\n          { name: '张家港市' },\n          { name: '昆山市' },\n          { name: '太仓市' }\n        ]\n      },\n      {\n        name: '无锡市',\n        children: [\n          { name: '梁溪区' },\n          { name: '惠山区' },\n          { name: '滨湖区' },\n          { name: '新吴区' },\n          { name: '锡山区' },\n          { name: '宜兴市' }\n        ]\n      },\n      {\n        name: '徐州市',\n        children: [\n          { name: '鼓楼区' },\n          { name: '云龙区' },\n          { name: '贾汪区' },\n          { name: '泉山区' },\n          { name: '铜山区' },\n          { name: '丰县' },\n          { name: '沛县' },\n          { name: '睢宁县' },\n          { name: '新沂市' },\n          { name: '邳州市' }\n        ]\n      },\n      {\n        name: '常州市',\n        children: [\n          { name: '天宁区' },\n          { name: '钟楼区' },\n          { name: '新北区' },\n          { name: '武进区' },\n          { name: '金坛区' },\n          { name: '溧阳市' }\n        ]\n      },\n      {\n        name: '南通市',\n        children: [\n          { name: '崇川区' },\n          { name: '港闸区' },\n          { name: '通州区' },\n          { name: '如东县' },\n          { name: '启东市' },\n          { name: '如皋市' },\n          { name: '海门区' },\n          { name: '海安市' }\n        ]\n      },\n      {\n        name: '扬州市',\n        children: [\n          { name: '广陵区' },\n          { name: '邗江区' },\n          { name: '江都区' },\n          { name: '宝应县' },\n          { name: '仪征市' },\n          { name: '高邮市' }\n        ]\n      },\n      {\n        name: '镇江市',\n        children: [\n          { name: '京口区' },\n          { name: '润州区' },\n          { name: '丹徒区' },\n          { name: '丹阳市' },\n          { name: '扬中市' },\n          { name: '句容市' }\n        ]\n      },\n      {\n        name: '泰州市',\n        children: [\n          { name: '海陵区' },\n          { name: '高港区' },\n          { name: '姜堰区' },\n          { name: '兴化市' },\n          { name: '靖江市' },\n          { name: '泰兴市' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '浙江省',\n    children: [\n      {\n        name: '杭州市',\n        children: [\n          { name: '上城区' },\n          { name: '下城区' },\n          { name: '江干区' },\n          { name: '拱墅区' },\n          { name: '西湖区' },\n          { name: '滨江区' },\n          { name: '萧山区' },\n          { name: '余杭区' },\n          { name: '富阳区' },\n          { name: '临安区' },\n          { name: '钱塘区' },\n          { name: '临平区' }\n        ]\n      },\n      {\n        name: '宁波市',\n        children: [\n          { name: '海曙区' },\n          { name: '江北区' },\n          { name: '北仑区' },\n          { name: '镇海区' },\n          { name: '鄞州区' },\n          { name: '奉化区' },\n          { name: '余姚市' },\n          { name: '慈溪市' },\n          { name: '象山县' },\n          { name: '宁海县' }\n        ]\n      },\n      {\n        name: '温州市',\n        children: [\n          { name: '鹿城区' },\n          { name: '龙湾区' },\n          { name: '瓯海区' },\n          { name: '洞头区' },\n          { name: '永嘉县' },\n          { name: '平阳县' },\n          { name: '苍南县' },\n          { name: '文成县' },\n          { name: '泰顺县' },\n          { name: '瑞安市' },\n          { name: '乐清市' }\n        ]\n      },\n      {\n        name: '嘉兴市',\n        children: [\n          { name: '南湖区' },\n          { name: '秀洲区' },\n          { name: '嘉善县' },\n          { name: '海盐县' },\n          { name: '海宁市' },\n          { name: '平湖市' },\n          { name: '桐乡市' }\n        ]\n      },\n      {\n        name: '湖州市',\n        children: [\n          { name: '吴兴区' },\n          { name: '南浔区' },\n          { name: '德清县' },\n          { name: '长兴县' },\n          { name: '安吉县' }\n        ]\n      },\n      {\n        name: '绍兴市',\n        children: [\n          { name: '越城区' },\n          { name: '柯桥区' },\n          { name: '上虞区' },\n          { name: '新昌县' },\n          { name: '诸暨市' },\n          { name: '嵊州市' }\n        ]\n      },\n      {\n        name: '金华市',\n        children: [\n          { name: '婺城区' },\n          { name: '金东区' },\n          { name: '武义县' },\n          { name: '浦江县' },\n          { name: '磐安县' },\n          { name: '兰溪市' },\n          { name: '义乌市' },\n          { name: '东阳市' },\n          { name: '永康市' }\n        ]\n      },\n      {\n        name: '台州市',\n        children: [\n          { name: '椒江区' },\n          { name: '黄岩区' },\n          { name: '路桥区' },\n          { name: '三门县' },\n          { name: '天台县' },\n          { name: '仙居县' },\n          { name: '温岭市' },\n          { name: '临海市' },\n          { name: '玉环市' }\n        ]\n      },\n      {\n        name: '衢州市',\n        children: [\n          { name: '柯城区' },\n          { name: '衢江区' },\n          { name: '常山县' },\n          { name: '开化县' },\n          { name: '龙游县' },\n          { name: '江山市' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '福建省',\n    children: [\n      {\n        name: '福州市',\n        children: [\n          { name: '鼓楼区' },\n          { name: '台江区' },\n          { name: '仓山区' },\n          { name: '马尾区' },\n          { name: '晋安区' },\n          { name: '长乐区' }\n        ]\n      },\n      {\n        name: '厦门市',\n        children: [\n          { name: '思明区' },\n          { name: '海沧区' },\n          { name: '湖里区' },\n          { name: '集美区' },\n          { name: '同安区' },\n          { name: '翔安区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '江西省',\n    children: [\n      {\n        name: '南昌市',\n        children: [\n          { name: '东湖区' },\n          { name: '西湖区' },\n          { name: '青云谱区' },\n          { name: '湾里区' },\n          { name: '青山湖区' },\n          { name: '新建区' }\n        ]\n      },\n      {\n        name: '赣州市',\n        children: [\n          { name: '章贡区' },\n          { name: '南康区' },\n          { name: '赣县区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '山东省',\n    children: [\n      {\n        name: '济南市',\n        children: [\n          { name: '历下区' },\n          { name: '市中区' },\n          { name: '槐荫区' },\n          { name: '天桥区' },\n          { name: '历城区' },\n          { name: '长清区' },\n          { name: '章丘区' }\n        ]\n      },\n      {\n        name: '青岛市',\n        children: [\n          { name: '市南区' },\n          { name: '市北区' },\n          { name: '黄岛区' },\n          { name: '崂山区' },\n          { name: '李沧区' },\n          { name: '城阳区' }\n        ]\n      },\n      {\n        name: '烟台市',\n        children: [\n          { name: '芝罘区' },\n          { name: '福山区' },\n          { name: '牟平区' },\n          { name: '莱山区' },\n          { name: '蓬莱区' },\n          { name: '龙口市' },\n          { name: '莱阳市' },\n          { name: '莱州市' },\n          { name: '招远市' },\n          { name: '栖霞市' },\n          { name: '海阳市' }\n        ]\n      },\n      {\n        name: '潍坊市',\n        children: [\n          { name: '潍城区' },\n          { name: '寒亭区' },\n          { name: '坊子区' },\n          { name: '奎文区' },\n          { name: '临朐县' },\n          { name: '昌乐县' },\n          { name: '青州市' },\n          { name: '诸城市' },\n          { name: '寿光市' },\n          { name: '安丘市' },\n          { name: '高密市' },\n          { name: '昌邑市' }\n        ]\n      },\n      {\n        name: '济宁市',\n        children: [\n          { name: '任城区' },\n          { name: '兖州区' },\n          { name: '微山县' },\n          { name: '鱼台县' },\n          { name: '金乡县' },\n          { name: '嘉祥县' },\n          { name: '汶上县' },\n          { name: '泗水县' },\n          { name: '梁山县' },\n          { name: '曲阜市' },\n          { name: '邹城市' }\n        ]\n      },\n      {\n        name: '泰安市',\n        children: [\n          { name: '泰山区' },\n          { name: '岱岳区' },\n          { name: '宁阳县' },\n          { name: '东平县' },\n          { name: '新泰市' },\n          { name: '肥城市' }\n        ]\n      },\n      {\n        name: '威海市',\n        children: [\n          { name: '环翠区' },\n          { name: '文登区' },\n          { name: '荣成市' },\n          { name: '乳山市' }\n        ]\n      },\n      {\n        name: '淄博市',\n        children: [\n          { name: '淄川区' },\n          { name: '张店区' },\n          { name: '博山区' },\n          { name: '临淄区' },\n          { name: '周村区' },\n          { name: '桓台县' },\n          { name: '高青县' },\n          { name: '沂源县' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '河南省',\n    children: [\n      {\n        name: '郑州市',\n        children: [\n          { name: '中原区' },\n          { name: '二七区' },\n          { name: '管城回族区' },\n          { name: '金水区' },\n          { name: '上街区' },\n          { name: '惠济区' },\n          { name: '中牟县' }\n        ]\n      },\n      {\n        name: '洛阳市',\n        children: [\n          { name: '老城区' },\n          { name: '西工区' },\n          { name: '瀍河回族区' },\n          { name: '涧西区' },\n          { name: '吉利区' },\n          { name: '洛龙区' }\n        ]\n      },\n      {\n        name: '开封市',\n        children: [\n          { name: '龙亭区' },\n          { name: '顺河回族区' },\n          { name: '鼓楼区' },\n          { name: '禹王台区' },\n          { name: '祥符区' },\n          { name: '杞县' },\n          { name: '通许县' },\n          { name: '尉氏县' },\n          { name: '兰考县' }\n        ]\n      },\n      {\n        name: '新乡市',\n        children: [\n          { name: '红旗区' },\n          { name: '卫滨区' },\n          { name: '凤泉区' },\n          { name: '牧野区' },\n          { name: '新乡县' },\n          { name: '获嘉县' },\n          { name: '原阳县' },\n          { name: '延津县' },\n          { name: '封丘县' },\n          { name: '长垣市' },\n          { name: '卫辉市' },\n          { name: '辉县市' }\n        ]\n      },\n      {\n        name: '许昌市',\n        children: [\n          { name: '魏都区' },\n          { name: '建安区' },\n          { name: '鄢陵县' },\n          { name: '襄城县' },\n          { name: '禹州市' },\n          { name: '长葛市' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '湖北省',\n    children: [\n      {\n        name: '武汉市',\n        children: [\n          { name: '江岸区' },\n          { name: '江汉区' },\n          { name: '硚口区' },\n          { name: '汉阳区' },\n          { name: '武昌区' },\n          { name: '青山区' },\n          { name: '洪山区' },\n          { name: '东西湖区' },\n          { name: '汉南区' },\n          { name: '蔡甸区' },\n          { name: '江夏区' },\n          { name: '黄陂区' },\n          { name: '新洲区' }\n        ]\n      },\n      {\n        name: '宜昌市',\n        children: [\n          { name: '西陵区' },\n          { name: '伍家岗区' },\n          { name: '点军区' },\n          { name: '猇亭区' },\n          { name: '夷陵区' }\n        ]\n      },\n      {\n        name: '襄阳市',\n        children: [\n          { name: '襄城区' },\n          { name: '樊城区' },\n          { name: '襄州区' },\n          { name: '南漳县' },\n          { name: '谷城县' },\n          { name: '保康县' },\n          { name: '老河口市' },\n          { name: '枣阳市' },\n          { name: '宜城市' }\n        ]\n      },\n      {\n        name: '荆州市',\n        children: [\n          { name: '沙市区' },\n          { name: '荆州区' },\n          { name: '公安县' },\n          { name: '监利市' },\n          { name: '江陵县' },\n          { name: '石首市' },\n          { name: '洪湖市' },\n          { name: '松滋市' }\n        ]\n      },\n      {\n        name: '黄石市',\n        children: [\n          { name: '黄石港区' },\n          { name: '西塞山区' },\n          { name: '下陆区' },\n          { name: '铁山区' },\n          { name: '阳新县' },\n          { name: '大冶市' }\n        ]\n      },\n      {\n        name: '十堰市',\n        children: [\n          { name: '茅箭区' },\n          { name: '张湾区' },\n          { name: '郧阳区' },\n          { name: '郧西县' },\n          { name: '竹山县' },\n          { name: '竹溪县' },\n          { name: '房县' },\n          { name: '丹江口市' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '湖南省',\n    children: [\n      {\n        name: '长沙市',\n        children: [\n          { name: '芙蓉区' },\n          { name: '天心区' },\n          { name: '岳麓区' },\n          { name: '开福区' },\n          { name: '雨花区' },\n          { name: '望城区' },\n          { name: '长沙县' }\n        ]\n      },\n      {\n        name: '株洲市',\n        children: [\n          { name: '荷塘区' },\n          { name: '芦淞区' },\n          { name: '石峰区' },\n          { name: '天元区' },\n          { name: '渌口区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '广东省',\n    children: [\n      {\n        name: '广州市',\n        children: [\n          { name: '荔湾区' },\n          { name: '越秀区' },\n          { name: '海珠区' },\n          { name: '天河区' },\n          { name: '白云区' },\n          { name: '黄埔区' },\n          { name: '番禺区' },\n          { name: '花都区' },\n          { name: '南沙区' },\n          { name: '从化区' },\n          { name: '增城区' }\n        ]\n      },\n      {\n        name: '深圳市',\n        children: [\n          { name: '福田区' },\n          { name: '罗湖区' },\n          { name: '南山区' },\n          { name: '宝安区' },\n          { name: '龙岗区' },\n          { name: '盐田区' },\n          { name: '龙华区' },\n          { name: '坪山区' },\n          { name: '光明区' }\n        ]\n      },\n      {\n        name: '珠海市',\n        children: [\n          { name: '香洲区' },\n          { name: '斗门区' },\n          { name: '金湾区' }\n        ]\n      },\n      {\n        name: '佛山市',\n        children: [\n          { name: '禅城区' },\n          { name: '南海区' },\n          { name: '顺德区' },\n          { name: '三水区' },\n          { name: '高明区' }\n        ]\n      },\n      {\n        name: '东莞市',\n        children: [\n          { name: '东城街道' },\n          { name: '南城街道' },\n          { name: '万江街道' },\n          { name: '莞城街道' },\n          { name: '虎门镇' },\n          { name: '长安镇' },\n          { name: '厚街镇' },\n          { name: '塘厦镇' },\n          { name: '常平镇' },\n          { name: '寮步镇' }\n        ]\n      },\n      {\n        name: '中山市',\n        children: [\n          { name: '石岐区街道' },\n          { name: '东区街道' },\n          { name: '西区街道' },\n          { name: '南区街道' },\n          { name: '火炬开发区' },\n          { name: '小榄镇' },\n          { name: '古镇镇' }\n        ]\n      },\n      {\n        name: '惠州市',\n        children: [\n          { name: '惠城区' },\n          { name: '惠阳区' },\n          { name: '博罗县' },\n          { name: '惠东县' },\n          { name: '龙门县' }\n        ]\n      },\n      {\n        name: '汕头市',\n        children: [\n          { name: '龙湖区' },\n          { name: '金平区' },\n          { name: '濠江区' },\n          { name: '潮阳区' },\n          { name: '潮南区' },\n          { name: '澄海区' },\n          { name: '南澳县' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '广西壮族自治区',\n    children: [\n      {\n        name: '南宁市',\n        children: [\n          { name: '兴宁区' },\n          { name: '青秀区' },\n          { name: '江南区' },\n          { name: '西乡塘区' },\n          { name: '良庆区' },\n          { name: '邕宁区' },\n          { name: '武鸣区' }\n        ]\n      },\n      {\n        name: '桂林市',\n        children: [\n          { name: '秀峰区' },\n          { name: '叠彩区' },\n          { name: '象山区' },\n          { name: '七星区' },\n          { name: '雁山区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '海南省',\n    children: [\n      {\n        name: '海口市',\n        children: [\n          { name: '秀英区' },\n          { name: '龙华区' },\n          { name: '琼山区' },\n          { name: '美兰区' }\n        ]\n      },\n      {\n        name: '三亚市',\n        children: [\n          { name: '海棠区' },\n          { name: '吉阳区' },\n          { name: '天涯区' },\n          { name: '崖州区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '重庆市',\n    children: [{\n      name: '重庆市',\n      children: [\n        { name: '渝中区' },\n        { name: '江北区' },\n        { name: '南岸区' },\n        { name: '沙坪坝区' },\n        { name: '九龙坡区' },\n        { name: '大渡口区' },\n        { name: '渝北区' },\n        { name: '巴南区' },\n        { name: '北碚区' },\n        { name: '綦江区' },\n        { name: '长寿区' },\n        { name: '江津区' },\n        { name: '合川区' },\n        { name: '永川区' },\n        { name: '南川区' }\n      ]\n    }]\n  },\n  {\n    name: '四川省',\n    children: [\n      {\n        name: '成都市',\n        children: [\n          { name: '锦江区' },\n          { name: '青羊区' },\n          { name: '金牛区' },\n          { name: '武侯区' },\n          { name: '成华区' },\n          { name: '龙泉驿区' },\n          { name: '青白江区' },\n          { name: '新都区' },\n          { name: '温江区' },\n          { name: '双流区' },\n          { name: '郫都区' }\n        ]\n      },\n      {\n        name: '绵阳市',\n        children: [\n          { name: '涪城区' },\n          { name: '游仙区' },\n          { name: '安州区' }\n        ]\n      },\n      {\n        name: '乐山市',\n        children: [\n          { name: '市中区' },\n          { name: '沙湾区' },\n          { name: '五通桥区' },\n          { name: '金口河区' }\n        ]\n      },\n      {\n        name: '泸州市',\n        children: [\n          { name: '江阳区' },\n          { name: '纳溪区' },\n          { name: '龙马潭区' },\n          { name: '泸县' },\n          { name: '合江县' },\n          { name: '叙永县' },\n          { name: '古蔺县' }\n        ]\n      },\n      {\n        name: '德阳市',\n        children: [\n          { name: '旌阳区' },\n          { name: '罗江区' },\n          { name: '中江县' },\n          { name: '广汉市' },\n          { name: '什邡市' },\n          { name: '绵竹市' }\n        ]\n      },\n      {\n        name: '内江市',\n        children: [\n          { name: '市中区' },\n          { name: '东兴区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '贵州省',\n    children: [\n      {\n        name: '贵阳市',\n        children: [\n          { name: '南明区' },\n          { name: '云岩区' },\n          { name: '花溪区' },\n          { name: '乌当区' },\n          { name: '白云区' },\n          { name: '观山湖区' }\n        ]\n      },\n      {\n        name: '遵义市',\n        children: [\n          { name: '红花岗区' },\n          { name: '汇川区' },\n          { name: '播州区' }\n        ]\n      },\n      {\n        name: '六盘水市',\n        children: [\n          { name: '钟山区' },\n          { name: '六枝特区' },\n          { name: '水城区' },\n          { name: '盘州市' }\n        ]\n      },\n      {\n        name: '安顺市',\n        children: [\n          { name: '西秀区' },\n          { name: '平坝区' },\n          { name: '普定县' },\n          { name: '镇宁布依族苗族自治县' },\n          { name: '关岭布依族苗族自治县' },\n          { name: '紫云苗族布依族自治县' }\n        ]\n      },\n      {\n        name: '毕节市',\n        children: [\n          { name: '七星关区' },\n          { name: '大方县' },\n          { name: '黔西市' },\n          { name: '金沙县' },\n          { name: '织金县' },\n          { name: '纳雍县' },\n          { name: '威宁彝族回族苗族自治县' },\n          { name: '赫章县' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '云南省',\n    children: [\n      {\n        name: '昆明市',\n        children: [\n          { name: '五华区' },\n          { name: '盘龙区' },\n          { name: '官渡区' },\n          { name: '西山区' },\n          { name: '东川区' },\n          { name: '呈贡区' },\n          { name: '晋宁区' }\n        ]\n      },\n      {\n        name: '大理白族自治州',\n        children: [\n          { name: '大理市' },\n          { name: '漾濞县' },\n          { name: '祥云县' },\n          { name: '宾川县' }\n        ]\n      },\n      {\n        name: '曲靖市',\n        children: [\n          { name: '麒麟区' },\n          { name: '沾益区' },\n          { name: '马龙区' },\n          { name: '陆良县' },\n          { name: '师宗县' },\n          { name: '罗平县' },\n          { name: '富源县' },\n          { name: '会泽县' },\n          { name: '宣威市' }\n        ]\n      },\n      {\n        name: '玉溪市',\n        children: [\n          { name: '红塔区' },\n          { name: '江川区' },\n          { name: '通海县' },\n          { name: '华宁县' },\n          { name: '易门县' },\n          { name: '峨山彝族自治县' },\n          { name: '新平彝族傣族自治县' },\n          { name: '元江哈尼族彝族傣族自治县' }\n        ]\n      },\n      {\n        name: '保山市',\n        children: [\n          { name: '隆阳区' },\n          { name: '施甸县' },\n          { name: '龙陵县' },\n          { name: '昌宁县' },\n          { name: '腾冲市' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '西藏自治区',\n    children: [\n      {\n        name: '拉萨市',\n        children: [\n          { name: '城关区' },\n          { name: '堆龙德庆区' },\n          { name: '达孜区' },\n          { name: '林周县' }\n        ]\n      },\n      {\n        name: '日喀则市',\n        children: [\n          { name: '桑珠孜区' },\n          { name: '南木林县' },\n          { name: '江孜县' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '陕西省',\n    children: [\n      {\n        name: '西安市',\n        children: [\n          { name: '新城区' },\n          { name: '碑林区' },\n          { name: '莲湖区' },\n          { name: '灞桥区' },\n          { name: '未央区' },\n          { name: '雁塔区' },\n          { name: '阎良区' },\n          { name: '临潼区' },\n          { name: '长安区' },\n          { name: '高陵区' },\n          { name: '鄠邑区' },\n          { name: '蓝田县' },\n          { name: '周至县' }\n        ]\n      },\n      {\n        name: '咸阳市',\n        children: [\n          { name: '秦都区' },\n          { name: '杨陵区' },\n          { name: '渭城区' },\n          { name: '三原县' },\n          { name: '泾阳县' },\n          { name: '乾县' },\n          { name: '礼泉县' },\n          { name: '永寿县' },\n          { name: '彬州市' },\n          { name: '长武县' },\n          { name: '旬邑县' },\n          { name: '淳化县' },\n          { name: '武功县' },\n          { name: '兴平市' }\n        ]\n      },\n      {\n        name: '宝鸡市',\n        children: [\n          { name: '渭滨区' },\n          { name: '金台区' },\n          { name: '陈仓区' },\n          { name: '凤翔区' },\n          { name: '岐山县' },\n          { name: '扶风县' },\n          { name: '眉县' },\n          { name: '陇县' },\n          { name: '千阳县' },\n          { name: '麟游县' },\n          { name: '凤县' },\n          { name: '太白县' }\n        ]\n      },\n      {\n        name: '渭南市',\n        children: [\n          { name: '临渭区' },\n          { name: '华州区' },\n          { name: '潼关县' },\n          { name: '大荔县' },\n          { name: '合阳县' },\n          { name: '澄城县' },\n          { name: '蒲城县' },\n          { name: '白水县' },\n          { name: '富平县' },\n          { name: '韩城市' },\n          { name: '华阴市' }\n        ]\n      },\n      {\n        name: '延安市',\n        children: [\n          { name: '宝塔区' },\n          { name: '安塞区' },\n          { name: '延长县' },\n          { name: '延川县' },\n          { name: '子长市' },\n          { name: '志丹县' },\n          { name: '吴起县' },\n          { name: '甘泉县' },\n          { name: '富县' },\n          { name: '洛川县' },\n          { name: '宜川县' },\n          { name: '黄龙县' },\n          { name: '黄陵县' }\n        ]\n      },\n      {\n        name: '汉中市',\n        children: [\n          { name: '汉台区' },\n          { name: '南郑区' },\n          { name: '城固县' },\n          { name: '洋县' },\n          { name: '西乡县' },\n          { name: '勉县' },\n          { name: '宁强县' },\n          { name: '略阳县' },\n          { name: '镇巴县' },\n          { name: '留坝县' },\n          { name: '佛坪县' }\n        ]\n      },\n      {\n        name: '榆林市',\n        children: [\n          { name: '榆阳区' },\n          { name: '横山区' },\n          { name: '神木市' },\n          { name: '府谷县' },\n          { name: '靖边县' },\n          { name: '定边县' },\n          { name: '绥德县' },\n          { name: '米脂县' },\n          { name: '佳县' },\n          { name: '吴堡县' },\n          { name: '清涧县' },\n          { name: '子洲县' }\n        ]\n      },\n      {\n        name: '安康市',\n        children: [\n          { name: '汉滨区' },\n          { name: '汉阴县' },\n          { name: '石泉县' },\n          { name: '宁陕县' },\n          { name: '紫阳县' },\n          { name: '岚皋县' },\n          { name: '平利县' },\n          { name: '镇坪县' },\n          { name: '旬阳市' },\n          { name: '白河县' }\n        ]\n      },\n      {\n        name: '商洛市',\n        children: [\n          { name: '商州区' },\n          { name: '洛南县' },\n          { name: '丹凤县' },\n          { name: '商南县' },\n          { name: '山阳县' },\n          { name: '镇安县' },\n          { name: '柞水县' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '甘肃省',\n    children: [\n      {\n        name: '兰州市',\n        children: [\n          { name: '城关区' },\n          { name: '七里河区' },\n          { name: '西固区' },\n          { name: '安宁区' },\n          { name: '红古区' }\n        ]\n      },\n      {\n        name: '天水市',\n        children: [\n          { name: '秦州区' },\n          { name: '麦积区' }\n        ]\n      },\n      {\n        name: '金昌市',\n        children: [\n          { name: '金川区' },\n          { name: '永昌县' }\n        ]\n      },\n      {\n        name: '白银市',\n        children: [\n          { name: '白银区' },\n          { name: '平川区' },\n          { name: '靖远县' },\n          { name: '会宁县' },\n          { name: '景泰县' }\n        ]\n      },\n      {\n        name: '张掖市',\n        children: [\n          { name: '甘州区' },\n          { name: '肃南裕固族自治县' },\n          { name: '民乐县' },\n          { name: '临泽县' },\n          { name: '高台县' },\n          { name: '山丹县' }\n        ]\n      },\n      {\n        name: '酒泉市',\n        children: [\n          { name: '肃州区' },\n          { name: '金塔县' },\n          { name: '瓜州县' },\n          { name: '肃北蒙古族自治县' },\n          { name: '阿克塞哈萨克族自治县' },\n          { name: '玉门市' },\n          { name: '敦煌市' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '青海省',\n    children: [\n      {\n        name: '西宁市',\n        children: [\n          { name: '城东区' },\n          { name: '城中区' },\n          { name: '城西区' },\n          { name: '城北区' },\n          { name: '大通回族土族自治县' }\n        ]\n      },\n      {\n        name: '海东市',\n        children: [\n          { name: '乐都区' },\n          { name: '平安区' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '宁夏回族自治区',\n    children: [\n      {\n        name: '银川市',\n        children: [\n          { name: '兴庆区' },\n          { name: '西夏区' },\n          { name: '金凤区' },\n          { name: '永宁县' },\n          { name: '贺兰县' }\n        ]\n      },\n      {\n        name: '石嘴山市',\n        children: [\n          { name: '大武口区' },\n          { name: '惠农区' },\n          { name: '平罗县' }\n        ]\n      }\n    ]\n  },\n  {\n    name: '新疆维吾尔自治区',\n    children: [\n      {\n        name: '乌鲁木齐市',\n        children: [\n          { name: '天山区' },\n          { name: '沙依巴克区' },\n          { name: '新市区' },\n          { name: '水磨沟区' },\n          { name: '头屯河区' },\n          { name: '达坂城区' },\n          { name: '米东区' }\n        ]\n      },\n      {\n        name: '克拉玛依市',\n        children: [\n          { name: '独山子区' },\n          { name: '克拉玛依区' },\n          { name: '白碱滩区' },\n          { name: '乌尔禾区' }\n        ]\n      }\n    ]\n  }\n]; "], "mappings": "AAAA,OAAO,IAAMA,UAAU,GAAG,CACxB;EACEC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC,EACD;IACEA,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAS,CAAC,EAClB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAQ,CAAC;EAErB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAS,CAAC,EAClB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;IACTD,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AACH,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAa,CAAC,EACtB;MAAEA,IAAI,EAAE;IAAa,CAAC,EACtB;MAAEA,IAAI,EAAE;IAAa,CAAC;EAE1B,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAc,CAAC,EACvB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAU,CAAC,EACnB;MAAEA,IAAI,EAAE;IAAY,CAAC,EACrB;MAAEA,IAAI,EAAE;IAAe,CAAC;EAE5B,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAK,CAAC,EACd;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAW,CAAC,EACpB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAW,CAAC,EACpB;MAAEA,IAAI,EAAE;IAAa,CAAC,EACtB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAY,CAAC;EAEzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC;AAEL,CAAC,EACD;EACEA,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAM,CAAC,EACf;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAM,CAAC;EAEnB,CAAC,EACD;IACEA,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MAAED,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAQ,CAAC,EACjB;MAAEA,IAAI,EAAE;IAAO,CAAC,EAChB;MAAEA,IAAI,EAAE;IAAO,CAAC;EAEpB,CAAC;AAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}